// 分销企业数据帮助函数
package services

import (
	"eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	scrmsalesperson "eShop/services/distribution-service/enum/scrm-salesperson"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"sync"

	"xorm.io/xorm"
)

func GetStatsEnterpriseDaily(db *xorm.Engine, where map[string]interface{}) (totalData, curData, lastData distribution_po.StatsEnterpriseDaily, err error) {
	logPrefix := fmt.Sprintf("获取企业统计数据====，入参：%s", utils.InterfaceToJSON(where))
	log.Info(logPrefix)
	var waitGroup sync.WaitGroup
	results := make(chan string, 3)
	lastS, lastE, err := utils.GetLastCycleDate(where["startDate"].(string), where["endDate"].(string))
	if err != nil {
		return
	}
	w1 := make(map[string]interface{})
	w2 := map[string]interface{}{"statDateStart": where["startDate"], "statDateEnd": where["endDate"]}
	w3 := map[string]interface{}{"statDateStart": lastS.Format(utils.DateLayout), "statDateEnd": lastE.Format(utils.DateLayout)}
	str := fmt.Sprintf("获取分销企业和业务员数据上一周期时间:%s-%s||%s-%s", where["startDate"].(string), where["endDate"].(string), lastS.Format(utils.DateLayout), lastE.Format(utils.DateLayout))
	log.Info(str)
	waitGroup.Add(3)
	go func() {
		if totalData, err = distribution_po.GetEnterpriseData(db, w1); err != nil {
			results <- "获取累计数据失败" + err.Error()

		}
		waitGroup.Done()
	}()

	go func() {
		if curData, err = distribution_po.GetEnterpriseData(db, w2); err != nil {
			results <- "获取新增数据失败" + err.Error()

		}
		waitGroup.Done()
	}()

	go func() {
		if lastData, err = distribution_po.GetEnterpriseData(db, w3); err != nil {
			results <- "获取上一周期数据失败" + err.Error()

		}
		waitGroup.Done()
	}()

	waitGroup.Wait()
	close(results)

	for r := range results {
		if len(r) > 0 {
			err = errors.New(r)
			return
		}

	}
	return

}

// 获取业务员启用和停用数据
func GetSalespersonOpenStop(db *xorm.Engine, where map[string]interface{}) (totalOpenCnt, totalStopCnt, OpenCnt, stopCnt int, err error) {

	// 注意： 由于 停用和启用数据是直接读原表（scrm_salesperson）,然后累计业务员读的是统计表stats_enterprise_daily， 这个表的数据是定时任务每天凌晨跑前一天的数据， 从而会导致停用+启用会大于累计业务员总数
	// 查询累计停用和启用数据时， 不要包含今天的数据

	w1 := map[string]interface{}{"groupBy": where["groupBy"]}
	w2 := map[string]interface{}{"groupBy": where["groupBy"], "createTimeStart": where["startDate"], "createTimeEnd": where["endDate"].(string) + " 23:59:59"}

	out1, err := distribution_po.CntSalesperson(db, w1)
	if err != nil {
		return
	}
	for _, v := range out1 {
		if v.Status == scrmsalesperson.StatusOpen {
			totalOpenCnt = v.Cnt
		} else {
			totalStopCnt = v.Cnt
		}
	}

	out2, err := distribution_po.CntSalesperson(db, w2)
	if err != nil {
		return
	}
	for _, v := range out2 {
		if v.Status == scrmsalesperson.StatusOpen {
			OpenCnt = v.Cnt
		} else {
			stopCnt = v.Cnt
		}
	}
	return

}

// 获取 分销员启用和停用数据
func GetDistributorOpenStop(db *xorm.Engine, where map[string]interface{}) (totalOpenCnt, totalStopCnt, OpenCnt, stopCnt int, err error) {
	w1 := map[string]interface{}{"groupBy": where["groupBy"], "orgId": where["orgId"]}
	w2 := map[string]interface{}{"groupBy": where["groupBy"], "orgId": where["orgId"], "createTimeStart": where["startDate"], "createTimeEnd": where["endDate"].(string) + " 23:59:59"}

	out1, err := distribution_po.CntDistributor(db, w1)
	if err != nil {
		return
	}
	for _, v := range out1 {
		if v.Status == disdistributor.StatusValid {
			totalOpenCnt = v.Cnt
		} else if v.Status == disdistributor.StatusInvalid {
			totalStopCnt = v.Cnt
		}
	}

	out2, err := distribution_po.CntDistributor(db, w2)
	if err != nil {
		return
	}
	for _, v := range out2 {
		if v.Status == disdistributor.StatusValid {
			OpenCnt = v.Cnt
		} else if v.Status == disdistributor.StatusInvalid {
			stopCnt = v.Cnt
		}
	}
	return

}

// 数据总览-看板 返回结构体数据
func OrgStatsEntView(totalData, curData, lastData distribution_po.StatsEnterpriseDaily) (out vo.StatsEntView) {
	out = vo.StatsEntView{
		// // 累计业务员
		// TotalSalesman: totalData.TotalSalesman,
		// 新增业务员
		TotalSalesmanNew: curData.TotalSalesman,
		// 新增业务员  较上一周期百分比
		TotalSalesmanNewPercent: utils.CalProportion(curData.TotalSalesman, lastData.TotalSalesman),

		// 累计分销企业
		TotalEnterprise: totalData.TotalEnterprise,
		// 累计分销店铺
		TotalShop: totalData.TotalShop,
		// // 累计分销员
		// TotalDistributor: totalData.TotalDistributor,

		// 新增分销企业
		TotalEnterpriseNew: curData.TotalEnterprise,
		// 新增分销店铺
		TotalShopNew: curData.TotalShop,
		// 新增分销员
		TotalDistributorNew: curData.TotalDistributor,

		// 新增分销企业 较上一周期百分比
		TotalEnterpriseNewPercent: utils.CalProportion(curData.TotalEnterprise, lastData.TotalEnterprise),
		// 新增分销店铺 较上一周期百分比
		TotalShopNewPercent: utils.CalProportion(curData.TotalShop, lastData.TotalShop),
		// 新增分销员 较上一周期百分比
		TotalDistributorNewPercent: utils.CalProportion(curData.TotalDistributor, lastData.TotalDistributor),
	}
	return
}
