package services

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"testing"
	"time"
)

func TestInsureOrderStatService_InsureOrderStat(t *testing.T) {

	date1, _ := time.Parse(utils.DateLayout, "2024-07-12")
	date2, _ := time.Parse(utils.DateLayout, "2024-07-12")
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		startDate time.Time
		endDate   time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				BaseService: common.BaseService{},
			},

			args: args{
				startDate: date1,
				endDate:   date2,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := InsureOrderStatService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.InsureOrderStat(tt.args.startDate, tt.args.endDate); (err != nil) != tt.wantErr {
				t.Errorf("InsureOrderStat() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestInsureOrderStatService_InsureOrderStatRun(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		startTime string
		endTime   string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				startTime: "2024-07-16",
				endTime:   "2024-07-16",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := InsureOrderStatService{
				BaseService: tt.fields.BaseService,
			}
			s.InsureOrderStatRun(tt.args.startTime, tt.args.endTime)
		})
	}
}
