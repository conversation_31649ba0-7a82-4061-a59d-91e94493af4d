package blky_po

import "time"

// XlogisticsDetail 扫码记录表-深圳利都
type XlogisticsDetail struct {
	Id         int       `xorm:"pk autoincr 'id'" json:"id"`                                            // 主键ID
	Swlm       string    `xorm:"varchar(50) notnull unique 'swlm'" json:"swlm"`                         // 商品物流码
	State      uint8     `xorm:"tinyint unsigned notnull default 0 'state'" json:"state"`               // 0-未使用 1已使用
	CreateTime time.Time `xorm:"datetime default 'CURRENT_TIMESTAMP' 'create_time'" json:"create_time"` // 创建时间
	UpdateTime time.Time `xorm:"datetime default 'CURRENT_TIMESTAMP' 'update_time'" json:"update_time"` // 更新时间
}

// TableName 表名
func (x *XlogisticsDetail) TableName() string {
	return "blky.xlogistics_detail"
}
