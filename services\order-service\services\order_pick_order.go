package services

// B端小程序 订单拣货任务
import (
	"context"
	"eShop/domain/inventory-po/inventory"
	order_po "eShop/domain/order-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/proto/dac"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	"eShop/services/external-service/services"
	omnibus_service "eShop/services/omnibus-service/services"
	"eShop/view-model/external-vo/dto"
	order_vo "eShop/view-model/order-vo"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
)

type OrderPickService struct {
	common.BaseService
}

func NewOrderPickService() *OrderPickService {
	return &OrderPickService{}
}

// 获取拣货任务列表
func (s *OrderPickService) GetPickingTaskList(in order_vo.GetOrderListReq) (out []order_vo.GetOrderListRes, total int64, err error) {
	logPrefix := fmt.Sprintf("GetPickingTaskList获取拣货任务列表====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	out = make([]order_vo.GetOrderListRes, 0)
	// 获取拣货任务列表（待领取，待拣货，已完成）
	if in.ShopId == "" {
		return out, 0, fmt.Errorf("门店id不能为空")
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	s1 := `a.channel_id,a.order_sn,a.old_order_sn,c.order_sn as child_order_sn,a.order_status,a.order_status_child,a.warehouse_id,a.warehouse_code,a.warehouse_name,a.create_time,
	a.receiver_name,a.receiver_state,a.receiver_city,a.receiver_district,a.receiver_address,a.receiver_phone,a.en_receiver_phone,a.receiver_mobile,a.en_receiver_mobile`
	s2 := `b.buyer_memo,b.seller_memo,b.pickup_code,b.picking_time,b.is_picking,b.pick_user_id,b.pick_user_name`

	session = session.Table("dc_order.order_main").Alias("a").Join("inner", "dc_order.order_detail b", "a.order_sn = b.order_sn").
		Join("left", "dc_order.order_main c", "a.order_sn = c.parent_order_sn").
		Select(fmt.Sprintf("%s,%s", s1, s2)).
		Where("a.org_id = ?", enum.SaasOrgId).
		Where("a.parent_order_sn = ''").
		Where("a.channel_id in (1,2,3)").
		Where("a.shop_id = ?", in.ShopId).
		Where("a.is_virtual=0").
		Where("a.delivery_type != 5 and a.delivery_type != 6")
	switch in.Type {
	case 1: //待领取
		session = session.Where("a.order_status >= 20").Where("b.is_picking = 0").Where("b.pick_user_id = 0").Where("a.order_status_child=20102 or a.channel_id=3").OrderBy("a.create_time asc")
	case 2: //待拣货
		session = session.Where("a.order_status >= 20").Where("b.is_picking = 0").Where("b.pick_user_id = ?", in.PickupUserId).Where("a.order_status_child=20102 or a.channel_id=3").OrderBy("a.create_time asc")
	case 3: //已完成
		session = session.Where("b.is_picking = 1").Where("b.pick_user_id = ?", in.PickupUserId).OrderBy("a.create_time desc")
	}
	if in.PickupCode != "" {
		session = session.Where("b.pickup_code = ?", in.PickupCode)
	}
	if in.QueryKey != "" {
		session = session.Where("a.order_sn = ? or c.order_sn = ? or a.old_order_sn = ? or a.en_member_tel = ?", in.QueryKey, in.QueryKey, in.QueryKey, utils.MobileEncrypt(in.QueryKey))
	}
	if in.PickingTimeStart != "" {
		session = session.Where("b.picking_time >= ?", in.PickingTimeStart)
	}
	if in.PickingTimeEnd != "" {
		session = session.Where("b.picking_time <= ?", in.PickingTimeEnd)
	}
	if in.OrderSn != "" {
		session = session.Where("a.order_sn = ?", in.OrderSn)
	}

	session = session.Limit(in.PageSize, (in.PageIndex-1)*in.PageSize)
	if total, err = session.FindAndCount(&out); err != nil {
		return out, 0, err
	}
	if in.IsStats {
		return
	}

	orderSnList := make([]string, 0)
	for _, v := range out {
		orderSnList = append(orderSnList, v.OrderSn)
	}

	// 查询订单商品信息
	if len(orderSnList) == 0 {
		return
	}
	orderProduct := &order_po.OrderProduct{}
	orderProductReq := order_po.OrderProductRequest{
		OrderSns: orderSnList,
	}
	orderProductSli, orderProductMap, err := orderProduct.GetOrderProduct(session, orderProductReq)
	if err != nil {
		log.Error(logPrefix, "查询订单商品失败", err.Error())
		err = errors.New("查询订单商品失败: " + err.Error())
		return
	}
	inventoryMap := make(map[string]*inventory.Inventory)
	if in.ShowAvailableStock == 1 {
		skuIds := make([]int, 0)
		for _, v := range orderProductSli {
			skuIds = append(skuIds, cast.ToInt(v.SkuId))
		}
		if len(skuIds) > 0 {
			// 查询可用库存
			inventoryMap, _, _, _, err = inventory.Inventory{}.GetSkuInventoryByStore(context.Background(), session, inventory.GetSkuInventoryByStoreReq{StoreId: in.ShopId, SkuIds: skuIds})
			if err != nil {
				log.Error(logPrefix, "查询商品可用库存失败", err.Error())
				err = errors.New("查询商品可用库存失败: " + err.Error())
				return
			}
		}

	}

	// 组装订单商品信息
	for i, v := range out {
		if products, ok := orderProductMap[v.OrderSn]; ok {
			out[i].OrderProduct = make([]order_vo.OrderProduct, 0)
			for _, p := range products {
				// 可用库存
				availableNum := 0
				if inventoryInfo, ok := inventoryMap[fmt.Sprintf("%s_%d_%s", v.ShopId, v.WarehouseId, p.SkuId)]; ok {
					availableNum = inventoryInfo.AvailableNum
				}
				out[i].OrderProduct = append(out[i].OrderProduct, order_vo.OrderProduct{
					Id:           p.Id,
					ProductID:    p.ProductId,
					SkuID:        p.SkuId,
					ProductName:  p.ProductName,
					Image:        p.Image,
					ProductType:  p.ProductType,
					Specs:        p.Specs,
					BarCode:      p.BarCode,
					LocationCode: p.LocationCode,
					MarkingPrice: p.MarkingPrice,
					PayPrice:     p.PayPrice,
					Number:       p.Number,
					PaymentTotal: p.PaymentTotal,
					EmployeeName: p.EmployeeName,
					BuyType:      p.BuyType,
					PickedNumber: p.PickedNumber,
					AvailableNum: availableNum,
					IsPicking:    p.IsPicking,
				})
			}
		}
	}

	return

}

// // 获取拣货任务列表
// func (s *OrderPickService) PickingTaskStatistics(in order_vo.PickingTaskStatReq) (out order_vo.PickingTaskStatRes, err error) {
// 	logPrefix := fmt.Sprintf("PickingTaskStatistics拣货任务统计====入参:%s", utils.JsonEncode(in))
// 	log.Info(logPrefix)
// 	s.Begin()
// 	defer s.Close()
// 	session := s.Engine.NewSession()
// 	defer session.Close()

// 	if in.ShopId == "" {
// 		return out, fmt.Errorf("门店id不能为空")
// 	}

// 	return

// }

// ReceivePickingTask 领取拣货任务
func (s *OrderPickService) ReceivePickingTask(in order_vo.ReceivePickingTaskReq) (err error) {
	logPrefix := fmt.Sprintf("ReceivePickingTask领取拣货任务====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 开启事务
	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	// 获取父订单和子订单号
	orderSns := make([]string, 0)
	if err = session.Table("dc_order.order_main").Alias("a").Select("a.order_sn").
		Where("a.order_sn = ? or a.parent_order_sn = ?", in.OrderSn, in.OrderSn).
		Find(&orderSns); err != nil {
		log.Error(logPrefix, "获取父订单和子订单号失败", err.Error())
		return fmt.Errorf("获取父订单和子订单号失败: %v", err)
	}

	// 检查订单是否存在且状态是否正确
	orderDetail := new(order_po.OrderDetail)
	exists, err := session.Where("order_sn = ?", in.OrderSn).
		Where("is_picking = 0").   // 未拣货
		Where("pick_user_id = 0"). // 未被领取
		Get(orderDetail)

	if err != nil {
		session.Rollback()
		return fmt.Errorf("查询订单详情失败: %v", err)
	}

	if !exists {
		session.Rollback()
		return fmt.Errorf("订单不存在或已被领取")
	}

	// 更新订单详情,设置拣货人
	_, err = session.Table(orderDetail.TableName()).
		In("order_sn", orderSns).
		Update(map[string]interface{}{
			"pick_user_id":   in.PickupUserId,
			"pick_user_name": in.PickupUserName,
		})

	if err != nil {
		session.Rollback()
		return fmt.Errorf("更新订单拣货信息失败: %v", err)
	}

	// 提交事务
	if err := session.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// CompletePickingTask 完成拣货任务
func (s *OrderPickService) CompletePickingTask(in order_vo.CompletePickingTaskReq) (err error) {
	logPrefix := fmt.Sprintf("CompletePickingTask完成拣货任务====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 获取父订单和子订单号
	orderList := make([]order_po.Order, 0)
	if err = session.Table("dc_order.order_main").Alias("a").
		Select("a.*,b.is_picking,b.pick_user_id,b.pick_user_name").
		Join("inner", "dc_order.order_detail b", "a.order_sn = b.order_sn").
		Where("a.order_sn = ? or a.parent_order_sn = ?", in.OrderSn, in.OrderSn).
		Find(&orderList); err != nil {
		log.Error(logPrefix, "获取父订单和子订单号失败", err.Error())
		return fmt.Errorf("获取父订单和子订单号失败: %v", err)
	}
	var parentOrder, childOrder order_po.Order
	orderSns := make([]string, 0)
	for _, v := range orderList {
		orderSns = append(orderSns, v.OrderSn)
		if v.OrderStatus == 0 {
			log.Error(logPrefix, "订单未支付")
			return fmt.Errorf("订单未支付")
		}
		if v.ParentOrderSn == "" {
			parentOrder = v
		} else {
			childOrder = v
		}
	}
	if parentOrder.PickUserId != in.PickupUserId {
		log.Error(logPrefix, "不是自己的拣货单")
		return fmt.Errorf("不是自己的拣货单")
	}

	if parentOrder.OrderStatus == 0 {
		log.Error(logPrefix, "订单未支付或已取消")
		return fmt.Errorf("订单未支付或已取消")
	}
	if parentOrder.IsPicking == 1 {
		log.Error(logPrefix, "已拣货")
		return fmt.Errorf("已拣货")
	}
	// 美团、小程序渠道需要是已接单状态，才能完成拣货。 饿了么不接单拣货， 所以不要校验子状态
	if parentOrder.ChannelId != 3 && parentOrder.OrderStatusChild != 20102 {
		log.Error(logPrefix, "订单状态不正确")
		return fmt.Errorf("订单状态不正确")
	}

	// 开启事务
	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	// 更新子订单号的子订单状态
	if _, err = session.Table("dc_order.order_main").Alias("a").
		Where("a.order_sn = ?", childOrder.OrderSn).
		Update(map[string]interface{}{"order_status_child": parentOrder.OrderStatusChild}); err != nil {
		log.Error(logPrefix, "更新子订单状态失败", err.Error())
		session.Rollback()
		return fmt.Errorf("更新子订单状态失败: %v", err)
	}

	orderSnStr := ""
	for _, v := range orderSns {
		orderSnStr += fmt.Sprintf("%q,", v)
	}
	skuIdsStr := ""
	for _, v := range in.SkuIds {
		skuIdsStr += fmt.Sprintf("%d,", v)
	}
	// 更新订单商品拣货状态
	sql := fmt.Sprintf(`UPDATE dc_order.order_product 
		SET is_picking = 1, 
		    picked_number = number,
		    update_time = ?
		WHERE order_sn IN (%s) AND sku_id IN (%s)`, strings.Trim(orderSnStr, ","), strings.Trim(skuIdsStr, ","))
	result, err := session.Exec(sql, time.Now().Format("2006-01-02 15:04:05"))
	if err != nil {
		return err
	}
	affected, err := result.RowsAffected()
	if err != nil {
		log.Error(logPrefix, "更新商品拣货状态失败", err.Error())
		session.Rollback()
		return fmt.Errorf("更新商品拣货状态失败: %v", err)
	}
	if affected == 0 {
		session.Rollback()
		log.Error(logPrefix, "商品不存在或已完成拣货", in.OrderSn)
		return fmt.Errorf("商品不存在或已完成拣货")
	}

	// 检查是否所有商品都已拣货完成
	checkAllProductSQL := `SELECT COUNT(1) FROM dc_order.order_product 
		WHERE order_sn = ? AND is_picking = 0`

	var unfinishedCount int64
	_, err = session.SQL(checkAllProductSQL, in.OrderSn).Get(&unfinishedCount)
	if err != nil {
		log.Error(logPrefix, "检查商品拣货状态失败", err.Error())
		session.Rollback()
		return fmt.Errorf("检查商品拣货状态失败: %v", err)
	}

	// 如果所有商品都已拣货完成,更新订单详情的拣货状态
	if unfinishedCount == 0 {
		affected, err = session.Table("dc_order.order_detail").
			In("order_sn", orderSns).
			Update(map[string]interface{}{
				"is_picking":   1,
				"picking_time": time.Now().Format("2006-01-02 15:04:05"),
			})
		if err != nil {
			log.Error(logPrefix, "更新订单拣货状态失败", err.Error())
			session.Rollback()
			return fmt.Errorf("更新订单拣货状态失败: %v", err)
		}

		if affected == 0 {
			log.Error(logPrefix, "更新订单拣货状态失败", "订单不存在或已完成拣货")
			session.Rollback()
			return fmt.Errorf("更新订单拣货状态失败")
		}

		// 添加脚印
		orderLogs := []*order_po.OrderLog{
			{
				OrderSn: in.OrderSn,
				LogType: order_po.OrderLogPickedOrder,
			},
		}
		if err := new(order_po.OrderLog).BatchCreate(session, orderLogs); err != nil {
			session.Rollback()
			log.Error(logPrefix, "添加脚印失败", err.Error())
			return fmt.Errorf("添加脚印失败: %v", err)
		}
		orderPickService := new(services.OrderPick)

		// 手动拣货通知饿了么 或 美团
		if err = orderPickService.PushOrderPick(dto.OrderPick{
			ChannelId:  orderList[0].ChannelId,
			OldOrderSn: orderList[0].OldOrderSn,
			AppChannel: orderList[0].AppChannel,
		}); err != nil {
			log.Error(logPrefix, "推送拣货完成失败", err.Error())
		}

		//数据中心，后台通知状态修改
		omnibusService := new(omnibus_service.DacService)
		_, err = omnibusService.MessageUpdate(&dac.MessageUpdateRequest{
			OrderId: in.OrderSn,
		})
		if err != nil {
			log.Error(logPrefix, "更新消息状态失败", err.Error())
		}

	}

	// 提交事务
	if err := session.Commit(); err != nil {
		log.Error(logPrefix, "提交事务失败", err.Error())
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}
