package marketing_vo

import (
	viewmodel "eShop/view-model"
	order_vo "eShop/view-model/order-vo"
)

// SaveCouponReq 保存优惠券请求
type SaveCouponReq struct {
	//ID,新增时为0
	Id int `json:"id"`
	//连锁ID
	ChainId int64 `json:"chain_id" validate:"required"`
	//店铺ID
	StoreId string `json:"store_id" validate:"required"`
	//优惠券类型：1-满减 2-折扣 3-兑换
	Type int `json:"type" validate:"required,oneof=1 2 3"`
	//优惠券名称
	Name string `json:"name" validate:"required,max=10"`
	//优惠券总数
	TotalCount int `json:"total_count" validate:"required,min=1"`
	//适用商品：1-全部 2-指定可用 3-指定不可用
	ApplyProduct int `json:"apply_product"`
	//使用门槛 0则无门槛
	Threshold float64 `json:"threshold"`
	//折扣
	Discount float64 `json:"discount"`
	//最多优惠多少元
	BestOffer float64 `json:"best_offer"`
	//用券开始类型：1-当日起 2-次日起 3-指定时
	WithinDay int `json:"within_day"`
	//开始时间
	StartTime string `json:"start_time" validate:"omitempty,datetime=2006-01-02 15:04:05"`
	//结束时间
	EndTime string `json:"end_time" validate:"omitempty,datetime=2006-01-02 15:04:05"`
	//每人限领次数
	PersonLimit int `json:"person_limit"`
	//使用说明
	Remark string `json:"remark"`
	//指定商品列表
	SkuIds string `json:"sku_ids,omitempty"`
	//用券开始类型：1-当日起 2-次日起 3-指定时
	UseStartType int `json:"use_start_type"`
}

// SaveCouponRes 保存优惠券响应
type SaveCouponRes struct {
	//响应码
	Code int `json:"code"`
	//响应信息
	Message string `json:"message"`
}

type CouponListReq struct {
	viewmodel.BasePageHttpRequest
	//优惠券名称
	Name string `form:"name"`
	//类型：折扣/抵扣
	Type string `form:"type"`
	//状态 1-未开始 2-进行中 3-已结束
	Status int `form:"status"`
	//店铺ID
	StoreId string `json:"store_id"`
}
type UserCouponListReq struct {
	//优惠券名称
	Name string `form:"name"`
	//类型：折扣/抵扣
	Type string `form:"type"`
	//状态
	Status string `form:"status"`
	//店铺ID
	StoreId string `json:"store_id"`
}

type CouponListRes struct {
	viewmodel.BasePageHttpResponse
	//列表数据
	Data []MarketingCouponListRes `json:"data"`
}

type MarketingCouponListRes struct {
	//唯一数据ID
	Id int `xorm:"pk autoincr 'id'" json:"id"`
	//店铺ID
	StoreId string `xorm:"'store_id'" json:"store_id"`
	//优惠券类型：1-满减 2-折扣 3-兑换
	Type int `xorm:"'type'" json:"type"`
	//优惠券名称
	Name string `xorm:"'name'" json:"name"`
	//优惠券内容
	Content string `xorm:"'content'" json:"content"`
	//优惠券总数
	TotalCount int `xorm:"'total_count'" json:"total_count"`
	//剩余数量
	RemainCount int `xorm:"'remain_count'" json:"remain_count"`
	//领取数量
	ReceivedCount int `xorm:"'received_count'" json:"received_count"`
	//已使用数量
	UsedCount int `xorm:"'used_count'" json:"used_count"`
	//适用商品：1-全部 2-指定可用 3-指定不可用
	ApplyProduct int `xorm:"'apply_product'" json:"apply_product"`
	//使用门槛 0则无门槛
	Threshold float64 `xorm:"'threshold'" json:"threshold"`
	//折扣
	Discount float64 `xorm:"'discount'" json:"discount"`
	//最多优惠多少元
	BestOffer float64 `xorm:"'best_offer'" json:"best_offer"`
	//用券开始类型：1-当日起 2-次日起 3-指定时间
	UseStartType int `xorm:"'use_start_type'" json:"use_start_type"`
	//领券多少天内可以用
	WithinDay int `xorm:"'within_day'" json:"within_day"`
	//券开始日期
	StartTime string `xorm:"'start_time'" json:"start_time"`
	//券结束日期
	EndTime string `xorm:"'end_time'" json:"end_time"`
	//每人限领次数
	PersonLimit int `xorm:"'person_limit'" json:"person_limit"`
	//使用说明
	Remark string `xorm:"'remark'" json:"remark"`
	//数据状态：1-未开始 2-进行中 3-已结束
	Status int `xorm:"'status'" json:"status"`
	//删除标识：0-未删除 1-已删除
	IsDeleted int `xorm:"'is_deleted'" json:"is_deleted"`
}

// 优惠券详情请求
type CouponDetailReq struct {
	Id      int    `json:"id" validate:"required"`       // 优惠券ID
	StoreId string `json:"store_id" validate:"required"` // 店铺ID
}

// 优惠券详情请求
type UserCouponDetailReq struct {
	StoreId string `json:"store_id" validate:"required"` // 店铺ID
	//优惠券ID
	CouponID string `json:"coupon_id"`
	//优惠券COde
	Code string `json:"code"`
	//客户ID
	CustomerId string `json:"customer_id"`
}

// 优惠券详情响应
type CouponDetailResp struct {
	//优惠券ID
	Id int `json:"id"`
	//连锁ID
	ChainId int64 `json:"chain_id"`
	//店铺ID
	StoreId string `json:"store_id"`
	//优惠券COde
	Code string `json:"code"`
	//优惠券类型
	Type int `json:"type"`
	//优惠券名称
	Content string `json:"content"` // 优惠券内容
	//总数量
	TotalCount int `json:"total_count"`
	//剩余数量
	RemainCount int `json:"remain_count"`
	//适用商品：1-全部商品 2-指定商品 3-指定不可用商品
	Threshold float64 `json:"threshold"` // 使用门槛
	//优惠金额/折扣
	Discount float64 `json:"discount"`
	//最高优惠
	BestOffer float64 `json:"best_offer"`
	//使用开始类型：1-领取后立即生效 2-次日生效
	WithinDay int `json:"within_day"` // 有效期天数
	//开始时间
	StartTime string `json:"start_time"`
	//结束时间
	EndTime string `json:"end_time"`
	//每人限领
	PersonLimit int `json:"person_limit"`
	Status      int `json:"status"` // 状态：1-未开始 2-进行中 3-已结束 4-已禁用
	//备注
	Remark string `json:"remark"`
	//关联商品列表（当 apply_product 为 2 时返回）
	Products []struct {
		//商品ID
		ProductId int `json:"product_id"`
		//SKU ID
		SkuId int `json:"sku_id"`
		//商品名称
		ProductName string `json:"product_name"`
		//零售价
		RetailPrice int `json:"retail_price"`
		//库存
		Stock int `json:"stock"`
	} `json:"products,omitempty"` // 关联商品列表（当 apply_product 为 2 时返回）
	//优惠券名称
	Name string `json:"name"`
	//适用商品
	ApplyProduct int `json:"apply_product"`
	//使用开始类型
	UseStartType int `json:"use_start_type"`
}

// 1. 查询优惠券领取列表请求
type CouponReceiverListReq struct {
	viewmodel.BasePageHttpRequest
	//优惠券ID
	CouponId int `json:"coupon_id"`
	//客户名称
	CustomerName string `json:"customer_name"`
	//使用状态 -1 全部 1-未使用 2-已使用 3-已过期
	Status int `json:"status" validate:"omitempty,oneof= -1 1 2 3"`
	//店铺ID
	StoreId string `json:"store_id"`
	//客户ID
	CustomerId string `json:"customer_id"`
	//商品SKU IDs，多个用逗号分隔
	SkuIds string `json:"sku_ids"`
}

// 2. 停止用券请求
type StopCouponReq struct {
	Id int64 `json:"id"`
	//优惠券ID
	CouponId int64 `json:"coupon_id"`
	//客户ID
	CustomerId string `json:"customer_id"`
	//店铺ID
	StoreId string `json:"store_id"`
}

// 3. 领取记录返回
type CouponReceiverListRes struct {
	viewmodel.BasePageHttpResponse
	Data []CouponReceiverInfo `json:"data"`
}

type CouponReceiverInfo struct {
	//连锁ID
	ChainId int64 `json:"chain_id"`
	//店铺ID
	StoreId string `json:"store_id"`
	//优惠券ID
	CouponId int `json:"coupon_id"`
	//优惠券码
	Code string `json:"code"`
	//记录ID
	Id int64 `json:"id"`
	//客户ID
	CustomerId string `json:"customer_id"`
	//客户名称
	CustomerName string `json:"customer_name"`
	//客户手机号
	CustomerMobile string `json:"customer_mobile"`
	//加密客户手机号
	EncryCustomerMobile string `json:"encry_customer_mobile"`
	//领取时间
	CreatedTime string `json:"created_time"`
	//使用状态 0-全部 1-未使用 2-已使用 3-已过期
	Status int `json:"status"`
	//领取来源 1-商家赠送 2-用户领取
	Source int `json:"source"`
	//生效时间
	EnableTime string `json:"enable_time"`
	//失效时间
	ExpireTime string `json:"expire_time"`
	//订单编号(已使用时)
	OrderNo string `json:"order_no"`
	//优惠券名称
	CouponName string `json:"coupon_name"`
	//优惠券类型 1-满减 2-折扣
	CouponType int `json:"coupon_type"`
	//使用门槛 0则无门槛
	Threshold float64 `json:"threshold"`
	//折扣金额
	Discount float64 `json:"discount"`
	//最高优惠
	BestOffer float64 `json:"best_offer"`
	//适用商品 1-全部 2-指定可用 3-指定不可用
	ApplyProduct int `json:"apply_product"`
	//开始时间
	StartTime string `json:"start_time"`
	//结束时间
	EndTime string `json:"end_time"`
	//使用开始类型 1-当日起 2-次日起 3-指定时
	UseStartType int `json:"use_start_type"`
	// 优惠券内容
	Content string `json:"content"`
	//使用说明
	Remark string `json:"remark"`
}

// 发放优惠券请求
type IssueCouponReq struct {
	//优惠券ID,多个用逗号分隔
	CouponIds string `json:"coupon_ids" validate:"required"` // 优惠券ID,多个用逗号分隔
	//客户ID
	CustomerId string `json:"customer_id" validate:"required"` // 客户ID
	//发放来源
	Source int `json:"source" validate:"required"` // 1-商家赠送 2-用户领取
	//店铺ID
	StoreId string `json:"store_id"` // 店铺ID
}

// 发放优惠券响应
type IssueCouponRes struct {
	viewmodel.BaseHttpResponse
}

// GetSourceName 获取来源名称
func GetSourceName(source int) string {
	switch source {
	case 1:
		return "商家赠送"
	case 2:
		return "用户领取"
	default:
		return "未知来源"
	}
}

// UserCouponDetail 用户优惠券详情
type UserCouponDetail struct {
	Id           int     `json:"id"`            // 优惠券ID
	Name         string  `json:"name"`          // 优惠券名称
	Code         string  `json:"code"`          // 优惠券编码
	Type         int     `json:"type"`          // 类型：1满减券 2折扣券
	Discount     float64 `json:"discount"`      // 优惠金额/折扣率
	Threshold    float64 `json:"threshold"`     // 使用门槛
	BestOffer    float64 `json:"best_offer"`    // 最高优惠
	ApplyProduct int     `json:"apply_product"` // 适用商品：1全部商品 2指定商品 3指定商品不可用
	Products     []struct {
		SkuId int `json:"sku_id"` // 商品SKU ID
	} `json:"products"` // 适用商品列表
}

// CouponUserListReq 用户优惠券列表请求
type CouponUserListReq struct {
	viewmodel.BasePageHttpRequest
	StoreId    string `form:"store_id"`    // 店铺ID
	CustomerId string `form:"customer_id"` // 用户ID
	SkuIds     string `form:"sku_ids"`     // 商品ID,多个用逗号分隔
	PageIndex  int    `form:"page_index"`  // 页码
	PageSize   int    `form:"page_size"`   // 每页数量
}

// UserCouponListItem 用户优惠券列表项
type UserCouponListItem struct {
	Id           int     `json:"id"`             // 优惠券ID
	Name         string  `json:"name"`           // 优惠券名称
	Type         int     `json:"type"`           // 优惠券类型：1-满减 2-折扣 3-兑换
	Content      string  `json:"content"`        // 优惠券内容
	TotalCount   int     `json:"total_count"`    // 总数量
	RemainCount  int     `json:"remain_count"`   // 剩余数量
	ApplyProduct int     `json:"apply_product"`  // 适用商品：1-全部 2-指定可用 3-指定不可用
	Threshold    float64 `json:"threshold"`      // 使用门槛
	Discount     float64 `json:"discount"`       // 优惠金额/折扣
	BestOffer    float64 `json:"best_offer"`     // 最高优惠
	UseStartType int     `json:"use_start_type"` // 用券开始类型：1-当日起 2-次日起 3-指定时间
	WithinDay    int     `json:"within_day"`     // 领券多少天内可用
	StartTime    string  `json:"start_time"`     // 开始时间
	EndTime      string  `json:"end_time"`       // 结束时间
	Status       int     `json:"status"`         // 状态：1-未开始 2-进行中 3-已结束
	IsReceived   int     `json:"is_received"`    // 是否已领取：0-未领取 1-已领取
	Code         string  `json:"code"`           // 券码(已领取时返回)
}

type CouponUserListRes struct {
	viewmodel.BasePageHttpResponse
	Data []UserCouponListItem `json:"data"`
}
type GetRunningCouponProductsReq struct {
}

// CheckCouponAvailabilityReq 检查优惠券可用性请求
type CheckCouponAvailabilityReq struct {
	StoreId    string                 `json:"store_id"`    // 店铺ID
	CustomerId string                 `json:"customer_id"` // 用户ID
	Details    []order_vo.OrderDetail `json:"details"`     // 商品明细
}

// CheckCouponAvailabilityRes 检查优惠券可用性响应
type CheckCouponAvailabilityRes struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    CouponAvailabilityData `json:"data"`
}

// CouponAvailabilityData 优惠券可用性数据
type CouponAvailabilityData struct {
	AvailableCoupons   []CouponAvailabilityInfo `json:"available_coupons"`
	UnavailableCoupons []CouponAvailabilityInfo `json:"unavailable_coupons"`
}

// CouponAvailabilityInfo 优惠券可用性信息
type CouponAvailabilityInfo struct {
	// 优惠券ID
	Id int64 `json:"id"`
	// 优惠券名称
	Name string `json:"name"`
	// 优惠券类型：1-满减 2-折扣
	Type int `json:"type"`
	// 使用门槛,0则无门槛，1则有门槛
	Threshold float64 `json:"threshold"`
	// 优惠金额/折扣
	Discount float64 `json:"discount"`
	// 最高优惠
	BestOffer float64 `json:"best_offer"`
	// 开始时间
	StartTime string `json:"start_time"`
	// 结束时间
	EndTime string `json:"end_time"`
	// 券码
	Code string `json:"code"`
	// 优惠金额(分)
	DiscountAmount int `json:"discount_amount"`
	// 不可用原因
	UnavailableReason string `json:"unavailable_reason"`
	//优惠券ID
	CouponId int `json:"coupon_id"`
	//备注
	Remark string `json:"remark"`
}
