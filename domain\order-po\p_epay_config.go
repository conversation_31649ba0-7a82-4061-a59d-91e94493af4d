package order_po

import (
	"fmt"
	"time"

	"xorm.io/xorm"
)

type EpayConfig struct {
	ID            int64     `xorm:"id" json:"id" comment:"主键id"`
	TenantID      int64     `xorm:"tenant_id" json:"tenant_id" comment:"租户id"`
	PayID         int64     `xorm:"pay_id" json:"pay_id" default:"0" comment:"配置id"`
	PayType       string    `xorm:"pay_type" json:"pay_type" comment:"支付类型"`
	AppID         string    `xorm:"app_id" json:"app_id" comment:"appId"`
	AppType       string    `xorm:"app_type" json:"app_type" default:"NORMAL" comment:"app类型: NORMAL-基础支付信息; WX_MINI-微信小程序"`
	AppSecret     string    `xorm:"app_secret" json:"app_secret" comment:"支付秘钥"`
	AppPubKey     string    `xorm:"app_pub_key" json:"app_pub_key" comment:"支付公钥"`
	AppMerchantID string    `xorm:"app_merchant_id" json:"app_merchant_id" comment:"支付商户号 电银商户号"`
	MerchantID    string    `xorm:"merchant_id" json:"merchant_id" comment:"系统商户号"`
	TrmSN         string    `xorm:"trm_sn" json:"trm_sn" comment:"机具编号（tsn）或终端编号"`
	TrmID         string    `xorm:"trm_id" json:"trm_id" comment:"终端号"`
	IsDeleted     bool      `xorm:"is_deleted" json:"is_deleted" comment:"删除标识"`
	CreatedBy     int64     `xorm:"created_by" json:"created_by" default:"0" comment:"创建人"`
	CreatedTime   time.Time `xorm:"created_time" json:"created_time" comment:"创建时间"`
	UpdatedBy     int64     `xorm:"updated_by" json:"updated_by" default:"0" comment:"更新人"`
	UpdatedTime   time.Time `xorm:"updated_time" json:"updated_time" comment:"更新时间"`
}

func (op *EpayConfig) TableName() string {
	return "eshop_saas.p_epay_config"
}

// GetEpayConfig 获取支付配置信息
func (op *EpayConfig) GetEpayConfig(session *xorm.Session, tenantId int64, appType string) (*EpayConfig, error) {
	if tenantId == 0 {
		return nil, fmt.Errorf("租户ID不能为空")
	}
	if appType == "" {
		return nil, fmt.Errorf("应用类型不能为空")
	}

	config := new(EpayConfig)
	exists, err := session.Where("tenant_id = ?", tenantId).
		Where("app_type = ?", appType).
		Where("is_deleted = ?", false).
		Get(config)

	if err != nil {
		return nil, fmt.Errorf("查询支付配置失败: %v", err)
	}

	if !exists {
		return nil, fmt.Errorf("支付配置不存在")
	}

	return config, nil
}
