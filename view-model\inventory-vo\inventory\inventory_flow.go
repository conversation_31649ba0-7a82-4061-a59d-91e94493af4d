package inventory

import baseVO "eShop/view-model/inventory-vo"

// InventoryFlowPageRequest 库存流水查询参数
type InventoryFlowPageRequest struct {
	ChainId            int64  `json:"chain_id"`             // 连锁ID
	TenantId           string `json:"tenant_id"`            // 门店ID
	WarehouseId        int    `json:"warehouse_id"`         // 仓库id
	ProductId          int    `json:"product_id"`           // 商品ID
	SkuId              int    `json:"sku_id"`               // SKU ID
	BoundType          int    `json:"bound_type"`           // 出库/入库类型：1-入库；2-出库
	ItemType           int    `json:"item_type"`            // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12. 自用出库
	ItemRefId          int    `json:"item_ref_id"`          // 出入库关联的单据ID
	ItemRefNo          string `json:"item_ref_no"`          // 出入库关联的单据编号
	ItemRefType        int    `json:"item_ref_type"`        // 出入库关联的单据类型
	ChangeNum          int    `json:"change_num"`           // 变更数量
	CurrentCostPrice   int    `json:"current_cost_price"`   // 当前变更时商品平均成本价(分)
	ChangeAmount       int    `json:"change_amount"`        // 当前变更金额(分)
	TotalNumBefore     int    `json:"total_num_before"`     // 变更前总库存
	FreezeNumBefore    int    `json:"freeze_num_before"`    // 变更前锁定库存
	AvailableNumBefore int    `json:"available_num_before"` // 变更前可用库存
	TotalAmountBefore  int    `json:"total_amount_before"`  // 变更前总成本(分)
	TotalNumAfter      int    `json:"total_num_after"`      // 变更后总库存
	FreezeNumAfter     int    `json:"freeze_num_after"`     // 变更后锁定库存
	AvailableNumAfter  int    `json:"available_num_after"`  // 变更后可用库存
	TotalAmountAfter   int    `json:"total_amount_after"`   // 变更后总成本(分)
	IsDeleted          int    `json:"is_deleted"`           // 删除标识:0未删除,1已删除
	Operator           string `json:"operator"`             // 操作人
	baseVO.PageRequest
	baseVO.TimeQueryRequest
	baseVO.SortRequest
}

// InventoryFlowResponse 库存流水查询结果
type InventoryFlowResponse struct {
	Id                 int    `json:"id"`                   // 主键
	ChainId            int64  `json:"chain_id"`             // 连锁id
	TenantId           int    `json:"tenant_id"`            // 门店id
	ProductId          int    `json:"product_id"`           // 商品id
	SkuId              int    `json:"sku_id"`               // sku id
	BoundType          int    `json:"bound_type"`           // 出库/入库类型
	ItemType           int    `json:"item_type"`            // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12. 自用出库
	ItemRefId          int    `json:"item_ref_id"`          // 出入库关联的单据id
	ItemRefNo          string `json:"item_ref_no"`          // 出入库关联的单据编号
	ItemRefType        int    `json:"item_ref_type"`        // 出入库关联的单据类型
	ChangeNum          int    `json:"change_num"`           // 变更数量
	CurrentCostPrice   int    `json:"current_cost_price"`   // 当前变更时商品平均成本价(分)
	ChangeAmount       int    `json:"change_amount"`        // 当前变更金额(分)
	TotalNumBefore     int    `json:"total_num_before"`     // 变更前总库存
	FreezeNumBefore    int    `json:"freeze_num_before"`    // 变更前锁定库存
	AvailableNumBefore int    `json:"available_num_before"` // 变更前可用库存
	TotalAmountBefore  int    `json:"total_amount_before"`  // 变更前总成本(分)
	TotalNumAfter      int    `json:"total_num_after"`      // 变更后总库存
	FreezeNumAfter     int    `json:"freeze_num_after"`     // 变更后锁定库存
	AvailableNumAfter  int    `json:"available_num_after"`  // 变更后可用库存
	TotalAmountAfter   int    `json:"total_amount_after"`   // 变更后总成本(分)
	IsDeleted          int    `json:"is_deleted"`           // 删除标识:0未删除,1已删除
	Operator           string `json:"operator"`             // 操作人
	Remark             string `json:"remark"`               // 备注
	CreatedTime        string `json:"created_time"`         // 创建时间
	UpdatedTime        string `json:"updated_time"`         // 更新时间
}
