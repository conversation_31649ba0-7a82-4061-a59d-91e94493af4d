package services

import (
	po "eShop/domain/marketing-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	cache2 "eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"strings"

	marketing_vo "eShop/view-model/marketing-vo"
	"errors"
	"fmt"

	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type PetActivityService struct {
	common.BaseService
}

// ListPetContestants 获取参赛用户列表（分页+多条件）
func (s *PetActivityService) ListPetContestants(param *marketing_vo.PetContestantListReq) (out []marketing_vo.PetContestantListItem, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	tableName := po.PetContestant{}.TableName()
	query := session.Table(tableName)

	// 构建查询条件
	if param.Mobile != "" {
		//手机要加密才能查询
		query = query.Where("en_mobile = ?", utils.MobileEncrypt(param.Mobile))
	}
	if param.Nickname != "" {
		query = query.Where("nick_name LIKE ?", "%"+param.Nickname+"%")
	}
	if param.IsPk > 0 {
		query = query.Where("is_pk = ?", param.IsPk)
	}
	if param.PkWorkId != "" {
		query = query.Where("pk_work_id = ?", param.PkWorkId)
	}
	if param.RegisterStart != "" {
		query = query.Where("register_time >= ?", param.RegisterStart)
	}
	if param.RegisterEnd != "" {
		query = query.Where("register_time <= ?", param.RegisterEnd)
	}
	if param.PkStart != "" {
		query = query.Where("pk_time >= ?", param.PkStart)
	}
	if param.PkEnd != "" {
		query = query.Where("pk_time <= ?", param.PkEnd)
	}

	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}

	// 分页查询
	total64, err := query.Limit(param.PageSize, param.PageSize*(param.PageIndex-1)).
		Desc("id").
		FindAndCount(&out)
	if err != nil {
		log.Error("查询参赛用户列表失败，err=", err.Error())
		return
	}
	return out, int(total64), nil
}

// GetMyRank 获取用户作品的全国排名信息
func (s *PetActivityService) GetMyRank(req *marketing_vo.MyRankReq) (*marketing_vo.MyRankData, error) {
	s.Begin()
	defer s.Close()

	if req.UserId == "" {
		return nil, errors.New("用户ID不能为空")
	}

	// 使用窗口函数一次性查询所有排名、同票、与上一名/第一名票数差等信息
	sql := `SELECT 
		noble_pet_img AS pet_photo,
		work_code,
		vote_count,
		DENSE_RANK() OVER (ORDER BY vote_count DESC, create_time ASC) AS vote_count_rank,
		LAG(vote_count, 1) OVER (ORDER BY vote_count DESC, create_time ASC) - vote_count + 1 AS vote_diff_with_prev,
		COUNT(*) OVER (PARTITION BY vote_count) AS same_vote_count,
		MAX(vote_count) OVER () - vote_count AS diff_from_top,
		create_time
	FROM eshop.pet_artwork
	WHERE pk_status = 1 and scrm_user_id=?`

	type rankRow struct {
		PetPhoto         string `xorm:"pet_photo"`
		WorkCode         string `xorm:"work_code"`
		VoteCount        int    `xorm:"vote_count"`
		VoteCountRank    int    `xorm:"vote_count_rank"`
		VoteDiffWithPrev *int   `xorm:"vote_diff_with_prev"`
		SameVoteCount    int    `xorm:"same_vote_count"`
		DiffFromTop      int    `xorm:"diff_from_top"`
		CreateTime       string `xorm:"create_time"`
	}

	var myRow rankRow
	exist, err := s.Engine.SQL(sql, req.UserId).Get(&myRow)
	if err != nil {
		return nil, err
	}
	if !exist {
		ret := marketing_vo.MyRankData{}
		ret.IsHave = 0
		return &ret, err
	}

	// 组装返回数据
	return &marketing_vo.MyRankData{
		IsHave:        1,
		PetPhoto:      myRow.PetPhoto,
		WorkCode:      myRow.WorkCode,
		VoteCount:     myRow.VoteCount,
		Rank:          myRow.VoteCountRank,
		DiffFirst:     myRow.DiffFromTop,
		SameVoteCount: myRow.SameVoteCount,
		DiffPrev: func() int {
			if myRow.VoteDiffWithPrev != nil {
				return *myRow.VoteDiffWithPrev
			} else {
				return 0
			}
		}(),
		DiffToFirst: -myRow.DiffFromTop,
	}, nil
}

// GetMyVoteDetail 获取用户作品的投票明细（带分页）
func (s *PetActivityService) GetMyVoteDetail(req *marketing_vo.MyVoteDetailReq) ([]marketing_vo.MyRankVoteDetail, int, error) {
	s.Begin()
	defer s.Close()
	type VoteDetailWithAvatar struct {
		InviterNickname string    `xorm:"inviter_nickname"`
		InviterMobile   string    `xorm:"inviter_mobile"`
		VoteTime        time.Time `xorm:"vote_time"`
		VoteCount       int       `xorm:"vote_count"`
		UserAvatar      string    `xorm:"user_avatar"`
	}
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	session := s.Engine.Table("eshop.pet_invite").Alias("pi").
		Join("left", "scrm_organization_db.t_scrm_user_info u", "u.user_id = pi.invitee_id").
		Select("pi.inviter_nickname,pi.inviter_mobile,pi.vote_time,pi.vote_count,u.user_avatar").
		Where("pi.work_code = ? AND pi.inviter_id = ? AND pi.vote_count > 0", req.WorkCode, req.UserId)

	var voteDetails []VoteDetailWithAvatar
	total, err := session.OrderBy("vote_time desc").
		Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).
		FindAndCount(&voteDetails)
	if err != nil {
		return nil, 0, err
	}
	var voteDetailList []marketing_vo.MyRankVoteDetail
	for _, v := range voteDetails {
		voteDetailList = append(voteDetailList, marketing_vo.MyRankVoteDetail{
			Nickname:      v.InviterNickname,
			Mobile:        v.InviterMobile,
			VoteTime:      v.VoteTime.Format("2006-01-02 15:04:05"),
			VoteCount:     v.VoteCount,
			InviteeAvatar: v.UserAvatar,
		})
	}
	return voteDetailList, int(total), nil
}

// GetQrCode 获取二维码URL
func (s *PetActivityService) GetQrCode(req *marketing_vo.GetQrCodeReq) (*marketing_vo.GetQrCodeData, error) {
	s.Begin()
	defer s.Close()

	if req.Path == "" {
		return nil, errors.New("路径不能为空")
	}
	if req.Sid == "" {
		return nil, errors.New("会话ID不能为空")
	}
	if req.OrgId <= 0 {
		return nil, errors.New("组织ID无效")
	}

	// 根据typeUrl类型生成不同的二维码URL
	url, err := utils.GetQrImage(req.Path, req.Sid, req.OrgId, req.TypeUrl, 0)
	if err != nil {
		log.Error("生成二维码失败：err=", err.Error())
		return nil, err
	}

	return &marketing_vo.GetQrCodeData{
		URL: url,
	}, nil
}

// ListPetStats 获取数据监测统计列表（按日期分组，每日一条，包含所有指标）
func (s *PetActivityService) ListPetStats(param *marketing_vo.PetStatListReq) (out []marketing_vo.PetStatListItem, total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 构建查询条件
	where := "WHERE 1=1"
	args := []interface{}{}
	if param.DateStart != "" {
		where += " AND daliy_date >= ?"
		args = append(args, param.DateStart)
	}
	if param.DateEnd != "" {
		where += " AND daliy_date <= ?"
		args = append(args, param.DateEnd)
	}

	// 查询总天数
	countSql := fmt.Sprintf("SELECT COUNT(DISTINCT daliy_date) FROM eshop.pet_stat %s", where)
	_, err = session.SQL(countSql, args...).Get(&total)
	if err != nil {
		return nil, 0, err
	}

	// 分页参数
	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}
	limit := param.PageSize
	offset := (param.PageIndex - 1) * param.PageSize

	// 查询数据 - 使用原生SQL实现横表
	sql := fmt.Sprintf(`
		SELECT
		  daliy_date,
		  MAX(CASE WHEN metric_type = 1 THEN num ELSE 0 END) AS metric1,
		  MAX(CASE WHEN metric_type = 2 THEN num ELSE 0 END) AS metric2,
		  MAX(CASE WHEN metric_type = 3 THEN num ELSE 0 END) AS metric3,
		  MAX(CASE WHEN metric_type = 4 THEN num ELSE 0 END) AS metric4,
		  MAX(CASE WHEN metric_type = 5 THEN num ELSE 0 END) AS metric5,
		  MAX(CASE WHEN metric_type = 6 THEN num ELSE 0 END) AS metric6,
		  MAX(CASE WHEN metric_type = 7 THEN num ELSE 0 END) AS metric7,
		  MAX(CASE WHEN metric_type = 8 THEN num ELSE 0 END) AS metric8,
		  MAX(CASE WHEN metric_type = 9 THEN num ELSE 0 END) AS metric9,
		  MAX(CASE WHEN metric_type = 10 THEN num ELSE 0 END) AS metric10,
		  MAX(CASE WHEN metric_type = 11 THEN num ELSE 0 END) AS metric11,
		  MAX(CASE WHEN metric_type = 12 THEN num ELSE 0 END) AS metric12,
		  MAX(CASE WHEN metric_type = 13 THEN num ELSE 0 END) AS metric13,
		  MAX(CASE WHEN metric_type = 14 THEN num ELSE 0 END) AS metric14,
		  MAX(CASE WHEN metric_type = 15 THEN num ELSE 0 END) AS metric15,
		  MAX(CASE WHEN metric_type = 16 THEN num ELSE 0 END) AS metric16
		FROM eshop.pet_stat
		%s
		GROUP BY daliy_date
		ORDER BY daliy_date DESC
		LIMIT ? OFFSET ?`, where)
	argsWithPage := append(args, limit, offset)

	// 定义结果行结构
	type row struct {
		DaliyDate string `xorm:"daliy_date"`
		Metric1   int    `xorm:"metric1"`
		Metric2   int    `xorm:"metric2"`
		Metric3   int    `xorm:"metric3"`
		Metric4   int    `xorm:"metric4"`
		Metric5   int    `xorm:"metric5"`
		Metric6   int    `xorm:"metric6"`
		Metric7   int    `xorm:"metric7"`
		Metric8   int    `xorm:"metric8"`
		Metric9   int    `xorm:"metric9"`
		Metric10  int    `xorm:"metric10"`
		Metric11  int    `xorm:"metric11"`
		Metric12  int    `xorm:"metric12"`
		Metric13  int    `xorm:"metric13"`
		Metric14  int    `xorm:"metric14"`
		Metric15  int    `xorm:"metric15"`
		Metric16  int    `xorm:"metric16"`
	}
	var rows []row
	err = session.SQL(sql, argsWithPage...).Find(&rows)
	if err != nil {
		return nil, 0, err
	}

	// 转换为VO
	for _, r := range rows {
		out = append(out, marketing_vo.PetStatListItem{
			DaliyDate: r.DaliyDate,
			Metric1:   r.Metric1,
			Metric2:   r.Metric2,
			Metric3:   r.Metric3,
			Metric4:   r.Metric4,
			Metric5:   r.Metric5,
			Metric6:   r.Metric6,
			Metric7:   r.Metric7,
			Metric8:   r.Metric8,
			Metric9:   r.Metric9,
			Metric10:  r.Metric10,
			Metric11:  r.Metric11,
			Metric12:  r.Metric12,
			Metric13:  r.Metric13,
			Metric14:  r.Metric14,
			Metric15:  r.Metric15,
			Metric16:  r.Metric16,
		})
	}
	return out, total, nil
}

func (s *PetActivityService) GetActivityInfo(session *xorm.Session) (marketing_vo.PetActivityInfo, error) {
	var info marketing_vo.PetActivityInfo
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	data := mCache.Get(string(cache_source.EShop), "marketing:pet:prizetime", "marketing:pet:prize-end-time")
	endTimeStr := data[0].(string)      // 活动结束时间
	prizeEndTimeStr := data[1].(string) // 奖品领取结束时间
	if len(endTimeStr) == 0 {
		endTimeStr = "2025-08-31 23:59:59"      // 默认结束时间
		prizeEndTimeStr = "2025-09-08 23:59:59" // 默认结束时间
	}
	info.EndTime = endTimeStr
	info.PrizeEndTime = prizeEndTimeStr

	now := time.Now()
	endTime, _ := utils.ParseTime(info.EndTime)
	prizeEndTime, _ := utils.ParseTime(info.PrizeEndTime)
	info.PrizeDiffSec = int(prizeEndTime.Sub(now).Seconds())
	if now.After(endTime) {
		info.State = 2 // 已结束
	} else {
		info.State = 1 // 进行中
		info.DiffSec = int(endTime.Sub(now).Seconds())
	}

	return info, nil
}

// SharePetArtwork 分享作品
func (s *PetActivityService) SharePetArtwork(req *marketing_vo.SharePetArtworkReq) (*marketing_vo.SharePetArtworkData, error) {
	s.Begin()
	defer s.Close()

	if req.UserId == "" {
		return nil, errors.New("用户ID不能为空")
	}
	if req.InviterNickname == "" {
		return nil, errors.New("邀请人昵称不能为空")
	}
	if req.InviterMobile == "" {
		return nil, errors.New("邀请人手机号不能为空")
	}
	if req.WorkCode == "" {
		return nil, errors.New("作品编号不能为空")
	}
	if req.InviteeOpenId == "" {
		return nil, errors.New("被邀请人open_id不能为空")
	}

	// 验证作品是否存在
	var artwork po.PetArtwork
	exists, err := s.Engine.Table(po.PetArtwork{}.TableName()).
		Where("work_code = ? AND pk_status = 1", req.WorkCode).
		Get(&artwork)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.New("作品不存在")
	}

	// 检查是否已经分享过
	var existingInvite po.PetInvite
	exists, err = s.Engine.Table(po.PetInvite{}.TableName()).
		Where("inviter_id = ? AND work_code = ? AND invitee_open_id = ?", req.UserId, req.WorkCode, req.InviteeOpenId).
		Get(&existingInvite)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("已经分享过该作品给该用户")
	}

	// 处理手机号加密和加星
	inviterEnMobile := utils.MobileEncrypt(req.InviterMobile)
	inviterMobileStar := utils.AddStar(req.InviterMobile)

	// 创建邀请记录
	invite := po.PetInvite{
		InviterId:             req.UserId,
		InviterNickname:       req.InviterNickname,
		InviterMobile:         inviterMobileStar,
		InviterEnMobile:       inviterEnMobile,
		InviterType:           0, // 默认新客
		InviteeId:             "",
		InviteeNickname:       "",
		InviteeMobile:         "",
		InviteeEnMobile:       "",
		InviteeRegisterStatus: 0,
		InviteeRegisterTime:   time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
		InviteeType:           0,
		VoteStatus:            0, // 未投票
		WorkCode:              req.WorkCode,
		VoteTime:              time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
		VoteCount:             0,
		CreateTime:            time.Now(),
		UpdateTime:            time.Now(),
		InviteeOpenId:         req.InviteeOpenId,
	}

	_, err = s.Engine.Table(po.PetInvite{}.TableName()).Insert(&invite)
	if err != nil {
		return nil, err
	}

	return &marketing_vo.SharePetArtworkData{
		Success: true,
	}, nil
}

// ReceiveVoucherByType 根据用户ID和优惠券类型领取优惠券
// 参数:
//   - userId: 用户ID
//   - voucherType: 优惠券金额 ("5", "15", "30")
//   - voucherType: 优惠券金额 ("5", "15", "30")
//   - num: 领取数量，默认为1（不用传）
//
// 返回:
//   - 领取结果和错误信息
func (s *PetActivityService) ReceiveVoucherByType(userId string, voucherType, prizeType int) (*utils.ExchangeVoucherResponse, error) {
	if userId == "" {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	redisConn := cache2.GetRedisConn()
	lockKey := fmt.Sprintf("lock:receive_voucher_%s_%d", userId, voucherType)
	if lock, err := redisConn.SetNX(lockKey, 1, 10*time.Second).Result(); err != nil {
		return nil, err
	} else if !lock {
		return nil, errors.New("系统繁忙，请稍后再试")
	}

	// 根据优惠券类型获取对应的模板ID
	tid, err := s.getVoucherTemplateId(voucherType, prizeType)
	if err != nil {
		return nil, err
	}

	// 调用优惠券领取接口
	result, err := utils.ExchangeVoucherByUserId(tid, userId, 1)
	if err != nil {
		log.Error("优惠券领取失败:", "userId=", userId, "voucherType=", voucherType, "tid=", tid, "err=", err.Error())
		return nil, fmt.Errorf("优惠券领取失败: %v", err)
	}

	// 优惠券领取成功，添加到PetPrize表
	if len(result) > 0 && result[0].VoucherId != "" {
		// 获取用户信息
		var userInfo struct {
			MemberName   string `xorm:"member_name"`
			MemberMobile string `xorm:"member_mobile"`
		}

		s.Begin()
		defer s.Close()
		session := s.Engine.NewSession()
		defer session.Close()

		has, err := session.Table("upetmart.upet_member").
			Select("member_name, member_mobile").
			Where("scrm_user_id = ?", userId).
			Get(&userInfo)

		if err != nil {
			log.Error("获取用户信息失败:", "userId=", userId, "err=", err.Error())
		} else if has {
			// 构建奖品内容
			var prizeContent string
			switch voucherType {
			case 5:
				prizeContent = "5元无门槛券"
			case 15:
				prizeContent = "15元无门槛券"
			case 30:
				prizeContent = "30元无门槛券"
			default:
				prizeContent = fmt.Sprintf("%d元无门槛券", voucherType)
			}

			// 插入PetPrize记录
			prize := po.PetPrize{
				UserId:        userId,
				NickName:      userInfo.MemberName,
				Mobile:        utils.AddStar(userInfo.MemberMobile),
				EnMobile:      utils.MobileEncrypt(userInfo.MemberMobile),
				PrizeType:     prizeType,
				WorkCode:      "", // 根据业务需要设置
				PrizeCount:    0,
				ReceiveStatus: 3, // 已核销（优惠券已发放）
				PrizeContent:  prizeContent,
				CouponCode:    result[0].GetVoucherIdString(), // 保存优惠券ID
				CreateTime:    time.Now(),
				UpdateTime:    time.Now(),
			}

			if _, err := session.Table(po.PetPrize{}.TableName()).Insert(&prize); err != nil {
				log.Error("插入PetPrize记录失败:", "userId=", userId, "voucherType=", voucherType, "err=", err.Error())
			} else {
				log.Info("成功插入PetPrize记录:", "userId=", userId, "voucherType=", voucherType, "voucherId=", result[0].GetVoucherIdString())
			}
		}

		// 成功把返回的tid放到redis的键列表里eShop:marketing:pet-prize-pop: + userId
		redisKey := fmt.Sprintf("eShop:marketing:pet-prize-pop:%s", userId)
		if err := redisConn.Set(redisKey, result[0].VoucherId, 30*24*60*60*time.Second).Err(); err != nil {
			log.Error("写入领取优惠券tid到redis失败:", "userId=", userId, "tid=", tid, "err=", err.Error())
		}
	} else {
		log.Error("领取优惠券返回结果异常: result 为空或VoucherId为空", "userId=", userId, "tid=", tid, "result=", result)
	}

	log.Info("优惠券领取成功:", "userId=", userId, "voucherType=", voucherType, "tid=", tid, "result=", result)

	return &utils.ExchangeVoucherResponse{
		Code:  200,
		Datas: result,
	}, nil
}

// getVoucherTemplateId 根据优惠券类型获取模板ID
// 参数:
//   - voucherType: 优惠券类型 ("5", "15", "30")
//
// 返回:
//   - 模板ID和错误信息
func (s *PetActivityService) getVoucherTemplateId(voucherType, prizeType int) (int, error) {
	s.Begin()
	defer s.Close()
	var tidStr string
	if _, err := s.Engine.Table("upetmart.upet_setting").Select("value").Where("name=?", "voucher_template_id").Get(&tidStr); err != nil {
		log.Error("获取优惠券模板配置异常:" + err.Error())
		return 0, err
	}
	if len(tidStr) == 0 {
		return 0, errors.New("获取不到优惠券模板配置")
	}

	// 解析逗号分隔的模板ID
	tidList := strings.Split(tidStr, ",")
	if len(tidList) != 3 {
		return 0, fmt.Errorf("优惠券模板ID配置格式错误，应为3个ID用逗号分隔: %s", tidStr)
	}

	var prizes po.PetPrize
	db := s.Engine.Table("eshop.pet_prize").Where("prize_type=?", prizeType)

	// 根据优惠券类型选择对应的模板ID
	var selectedTid string
	switch voucherType {
	case 5:
		selectedTid = strings.TrimSpace(tidList[0])
		db.Where("prize_content=?", "5元无门槛券")
	case 15:
		selectedTid = strings.TrimSpace(tidList[1])
		db.Where("prize_content=?", "15元无门槛券")
	case 30:
		selectedTid = strings.TrimSpace(tidList[2])
		db.Where("prize_content=?", "30元无门槛券")
	default:
		return 0, fmt.Errorf("不支持的优惠券类型: %d", voucherType)
	}

	if _, err := db.Get(&prizes); err != nil {
		log.Error("获取优惠券模板配置异常:" + err.Error())
		return 0, err
	}
	if prizes.Id > 0 {
		return 0, errors.New("优惠券已发放，请勿重复操作")
	}

	tid := cast.ToInt(selectedTid)
	if tid <= 0 {
		return 0, fmt.Errorf("无效的优惠券模板ID: %s", selectedTid)
	}

	return tid, nil
}

// GetPetPrizeByVoucherType 根据优惠券类型查询对应的奖品记录
// 参数:
//   - voucherType: 优惠券类型 (5, 15, 30)
//
// 返回:
//   - 奖品记录和错误信息
func (s *PetActivityService) GetPetPrizeByVoucherType(voucherType int) (*po.PetPrize, error) {
	s.Begin()
	defer s.Close()

	var prize po.PetPrize
	exists, err := s.Engine.Table("pet_prize").
		Where("prize_type = ? AND status = 1", voucherType).
		Get(&prize)
	if err != nil {
		log.Error("查询奖品记录失败:", "voucherType=", voucherType, "err=", err.Error())
		return nil, fmt.Errorf("查询奖品记录失败: %v", err)
	}

	if !exists {
		return nil, fmt.Errorf("未找到对应的奖品记录，voucherType: %d", voucherType)
	}

	return &prize, nil
}

// GetPetPrizeByUserIdAndVoucherType 根据用户ID和优惠券类型查询奖品记录
// 参数:
//   - userId: 用户ID
//   - voucherType: 优惠券类型 (5, 15, 30)
//
// 返回:
//   - 奖品记录和错误信息
func (s *PetActivityService) GetPetPrizeByUserIdAndVoucherType(userId string, voucherType int) (*po.PetPrize, error) {
	s.Begin()
	defer s.Close()

	var prize po.PetPrize
	exists, err := s.Engine.Table("pet_prize").
		Where("scrm_user_id = ? AND prize_type = ? AND status = 1", userId, voucherType).
		Get(&prize)
	if err != nil {
		log.Error("查询用户奖品记录失败:", "userId=", userId, "voucherType=", voucherType, "err=", err.Error())
		return nil, fmt.Errorf("查询用户奖品记录失败: %v", err)
	}

	if !exists {
		return nil, fmt.Errorf("未找到用户的奖品记录，userId: %s, voucherType: %d", userId, voucherType)
	}

	return &prize, nil
}

// SaveVoucherTemplateIdToRedis 将模板ID保存到Redis
// 参数:
//   - userId: 用户ID
//   - voucherType: 优惠券类型
//   - tid: 模板ID
//
// 返回:
//   - 错误信息
func (s *PetActivityService) SaveVoucherTemplateIdToRedis(userId string, voucherType int, tid int) error {
	redisConn := cache2.GetRedisConn()
	key := fmt.Sprintf("eShop:marketing:pet-prize-pop:%s", userId)

	// 将模板ID添加到Redis列表中
	err := redisConn.RPush(key, fmt.Sprintf("%d:%d", voucherType, tid)).Err()
	if err != nil {
		log.Error("保存模板ID到Redis失败:", "userId=", userId, "voucherType=", voucherType, "tid=", tid, "err=", err.Error())
		return fmt.Errorf("保存模板ID到Redis失败: %v", err)
	}

	// 设置过期时间（例如24小时）
	err = redisConn.Expire(key, 24*time.Hour).Err()
	if err != nil {
		log.Error("设置Redis过期时间失败:", "key=", key, "err=", err.Error())
	}

	log.Info("模板ID已保存到Redis:", "userId=", userId, "voucherType=", voucherType, "tid=", tid, "key=", key)
	return nil
}
