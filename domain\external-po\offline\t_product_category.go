package offline

import (
	"xorm.io/xorm"
)

type TProductCategory struct {
	Id           int64  `json:"id" xorm:"pk autoincr not null INT 'id'"`
	ChainId      int64  `json:"chain_id"`      //连锁id
	CategoryName string `json:"category_name"` //分类名称
	ParentId     int64  `json:"parent_id"`     //上级目录ID
}

func (t TProductCategory) TableName() string {
	return "eshop_saas.t_product_category"
}

// func CategoryFindByParentId(db *xorm.Engine, parentId int64) (out []int64, err error) {

// 	//取出所有的分类
// 	if err = db.Table("eshop_saas.t_product_category").Select("id").Where("parent_id = ?", parentId).Find(&out); err != nil {
// 		err = errors.New("查询分类列表失败")
// 		return
// 	}

// 	return
// }

func GetCategoryInfoMap(db *xorm.Engine, where map[string]interface{}) (out map[string]TProductCategory, err error) {
	session := db.NewSession()
	defer session.Close()

	chainId, ok := where["chainId"]
	if ok {
		session = session.Where("chain_id=?", chainId)
	}

	categoryNames, ok := where["categoryNames"]
	if ok {
		session = session.In("category_name", categoryNames)
	}

	out = make(map[string]TProductCategory, 0)
	data := make([]TProductCategory, 0)
	if err = session.Table("eshop_saas.t_product_category").Alias("a").Select("id,category_name,chain_id,parent_id").Where("a.is_deleted=0").Find(&data); err != nil {
		return
	}
	for _, v := range data {
		out[v.CategoryName] = v
	}

	return

}
