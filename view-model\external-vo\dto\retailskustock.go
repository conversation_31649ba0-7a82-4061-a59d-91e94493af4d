package dto

type RetailSkuStockRequest struct {
	// APP方门店id
	App_poi_code string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	// 商品sku库存集合的json数据
	Food_data []FoodData `json:"food_data" form:"food_data" query:"food_data"`
	// 店铺主体Id
	StoreMasterId int32 `json:"store_master_id" form:"store_master_id" query:"store_master_id"`
}
type FoodData struct {
	// app_food_code(必填项),APP方商品id
	App_food_code string `json:"app_food_code" form:"app_food_code" query:"app_food_code"`
	// skus(必填项)，商品sku信息集合的json数组
	Skus []Skus `json:"skus" form:"skus" query:"skus"`
}

type Skus struct {
	// sku_id(必填项)，是sku唯一标识码
	Sku_id string `json:"sku_id" form:"sku_id" query:"sku_id"`
	// stock(必填项)，为sku的库存，传非负整数，若传"*"表示库存无限
	Stock string `json:"stock" form:"stock" query:"stock"`
}
