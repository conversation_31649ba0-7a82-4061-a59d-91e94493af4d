package services

import (
	"context"
	"encoding/json"
	"time"

	"github.com/spf13/cast"

	"eShop/infra/log"
	"eShop/infra/pkg/code"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto/dac"
	"eShop/services/common"
)

var (
	storeExternalRedisKey = "datacenter:store:external"
	orderSnRedisKey       = "datacenter:store:ordersn:"
)

// GetAppChannelByOrderSn
// 返回1.阿闻自有,2.TP代运营,0查不到门店
func GetAppChannelByOrderSn(orderSn string) (storeMasterId int32, retCode int) {
	if len(orderSn) <= 0 {
		retCode = code.ErrorCommon
		return
	}
	redisConn := cache.GetRedisConn()
	strAppChannel := redisConn.Get(orderSnRedisKey + orderSn).Val()
	if len(strAppChannel) <= 0 {
		//todo 调用order-center 查询接口
		appChannel := getOrderAppChannel(orderSn)
		if appChannel > 0 {
			redisConn.Set(orderSnRedisKey+orderSn, appChannel, time.Hour*24)
		}
		retCode = code.Success
		storeMasterId = appChannel
		return
	}
	retCode = code.Success
	storeMasterId = cast.ToInt32(strAppChannel)
	return
}

// GetAppChannelByStoreId
// 返回1.阿闻自有,2.TP代运营,0查不到门店
func GetAppChannelByStoreId(channelStoreId string) (storeMasterId int32, retCode int) {
	if len(channelStoreId) <= 0 {
		retCode = code.ErrorCommon
		return
	}
	redisConn := cache.GetRedisConn()
	data := redisConn.HGetAll(storeExternalRedisKey).Val()
	if len(data) <= 0 {
		setRedisData()
	}
	str := redisConn.HGet(storeExternalRedisKey, channelStoreId).Val()
	if len(str) <= 0 {
		storeMasterId, retCode = setRedisOne(channelStoreId)
		return
	}
	store := dac.StoreChannelExternalData{}
	err := json.Unmarshal([]byte(str), &store)
	if err != nil {
		log.Error("解析data数据错误:StoreChannelExternalData", err)
		retCode = code.ErrorCommon
		return
	}
	return store.AppChannel, code.Success
}

func setRedisOne(channelStoreId string) (storeMasterId int32, retCode int) {
	data := getStoreChannelExternalData(channelStoreId)
	if len(data) > 0 {
		redisConn := cache.GetRedisConn()
		isOk := redisConn.HSet("datacenter:store:external", data[0].ChannelStoreId, utils.JsonEncode(data[0])).Val()
		if !isOk {
			log.Error("set redis,datacenter:store:external:err", utils.JsonEncode(data))
			retCode = code.ErrorCommon
			return
		}
		retCode = code.Success
		storeMasterId = data[0].AppChannel
		return
	}
	retCode = code.ErrorCommon
	return
}

func setRedisData() {
	data := getStoreChannelExternalData("")
	if len(data) > 0 {
		redisValue := make(map[string]interface{}, 0)
		for _, v := range data {
			if len(v.ChannelStoreId) > 0 {
				redisValue[v.ChannelStoreId] = utils.JsonEncode(v)
			}
		}
		redisConn := cache.GetRedisConn()
		val := redisConn.HMSet("datacenter:store:external", redisValue)
		if val.Val() != "Ok" {
			log.Error(val)
		}
	}
}

func getOrderAppChannel(orderSn string) int32 {
	grpcRes, err := GetOrderAppChannel(context.Background(), &GetOrderAppChannelReq{OrderSn: orderSn})
	if err != nil {
		log.Error("调用GetOrderAppChannel失败，", err, "，参数："+orderSn, err)
		return 0
	}
	if grpcRes.Code != 200 {
		log.Error("调用GetOrderAppChannel失败，", err, "，参数：2"+orderSn, utils.JsonEncode(grpcRes))
		return 0
	}
	return grpcRes.Data.AppChannel
}

func getStoreChannelExternalData(channelStoreId string) []*dac.StoreChannelExternalData {
	data := make([]*dac.StoreChannelExternalData, 0)
	grpcRes, err := GetStoreChannelExternal(context.Background(), &dac.GetStoreChannelExternalReq{ChannelId: 2, ChannelStoreId: channelStoreId})
	if err != nil {
		log.Error("调用GetStoreChannelExternal失败，", err, "，参数：2,"+channelStoreId, err)
		return data
	}
	if grpcRes.Code != 200 {
		log.Error("调用GetStoreChannelExternal失败，", err, "，参数：2,"+channelStoreId, utils.JsonEncode(grpcRes))
		return data
	}
	return grpcRes.Data
}

func GetStoreChannelExternal(ctx context.Context, req *dac.GetStoreChannelExternalReq) (*dac.GetStoreChannelExternalRes, error) {
	conn := common.BaseService{}
	conn.Begin()
	defer conn.Close()
	db := conn.Session

	out := new(dac.GetStoreChannelExternalRes)
	out.Code = 200
	data := make([]*dac.StoreChannelExternalData, 0)
	strSql := "SELECT  a.`finance_code`,a.`channel_store_id`,b.`app_channel`  FROM datacenter.`store_relation` a  JOIN  datacenter.store b  ON b.`finance_code`=a.`finance_code`WHERE a.`channel_id`=? "
	args := make([]interface{}, 0)
	args = append(args, req.ChannelId)
	if len(req.ChannelStoreId) > 0 {
		strSql = strSql + " AND a.channel_store_id=?"
		args = append(args, req.ChannelStoreId)
	}
	err := db.SQL(strSql, args...).Find(&data)
	if err != nil {
		log.Error("GetStoreChannelExternal:"+cast.ToString(req.ChannelId), err)
		out.Code = 500
		out.Message = "查询数据库出错"
		out.Error = err.Error()
		return out, err
	}
	out.Data = data
	//if len(data) > 0 {
	//	out.Data = make([]*dac.StoreChannelExternalData, 0)
	//	for _, v := range data {
	//		out.Data = append(out.Data, v.ToStoreChannelExternalData())
	//	}
	//}
	return out, nil
}

type GetOrderAppChannelReq struct {
	//美团订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
}

type GetOrderAppChannelRes struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//订单信息
	Data *OrderAppChannelData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
}

type OrderAppChannelData struct {
	//1.阿闻自有,2.TP代运营,0查不到门店
	AppChannel int32 `protobuf:"varint,1,opt,name=app_channel,json=appChannel,proto3" json:"app_channel"`
}

// 根据订单号获取appChannel
func GetOrderAppChannel(ctx context.Context, req *GetOrderAppChannelReq) (*GetOrderAppChannelRes, error) {
	conn := common.BaseService{}
	conn.Begin()
	defer conn.Close()
	db := conn.Session

	out := new(GetOrderAppChannelRes)
	out.Code = 200
	out.Message = "Success"
	appChannel := 0
	_, err := db.SQL("SELECT  app_channel  FROM `dc_order`.`order_main`  WHERE old_order_sn=?", req.OrderSn).Get(&appChannel)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		log.Error("GetOrderAppChannel", utils.JsonEncode(req), err)
		return nil, err
	}
	out.Data = &OrderAppChannelData{AppChannel: int32(appChannel)}
	return out, nil
}
