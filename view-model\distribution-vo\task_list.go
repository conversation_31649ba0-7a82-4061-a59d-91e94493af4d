package distribution_vo

import viewmodel "eShop/view-model"

type TaskList struct {
	//任务id
	Id int `json:"id" xorm:"pk autoincr not null comment('任务id') INT 'id'"`
	//任务内容:任务内容:1:业务员导入 2:业务员导出 3:分销订单导出 4:分销订单佣金结算导出 5-提现导出 6-导入打款记录(即提现导出) 7分销员导出
	//8分销商品导入 9分销商品导出 10保险分销结算 11线下企业导出 12百林康源物流码导出 13宠商云店铺数据 14宠商云业务员统计数据导出 15商品数据导出
	//16商品区域数据导出 17门店商品导出 18 -saas连锁商品导出， 19-saas连锁商品导入 20批量上架 21批量下架 22批量删除 23 批量铺品 24批量调价 28库存库位导入 100百林康源物流码导入
	//102-防伪码查询导出 103-极宠家客户列表导出 104-员工业绩导出
	TaskContent int8 `json:"task_content" xorm:"not null default 0 comment('任务内容:1:业务员导入 2:业务员导出') TINYINT(1) 'task_content'"`
	//任务状态:1:未开始;2:进行中;3:已完成 4:失败
	TaskStatus int8 `json:"task_status" xorm:"not null default 1 comment('任务状态:1:未开始;2:进行中;3:已完成 4:失败') TINYINT(1) 'task_status'"`
	//任务详情
	TaskDetail string `json:"task_detail" xorm:"not null default '' comment('任务详情') VARCHAR(10000) 'task_detail'"`
	//操作文件路径或者参数
	OperationFileUrl string `json:"operation_file_url" xorm:"not null comment('操作文件路径或者参数') TEXT 'operation_file_url'"`
	RequestHeader    string `json:"request_header" xorm:"default 'null' comment('操作请求的token值，类似userinfo') VARCHAR(255) 'request_header'"`
	//操作结果文件路径
	ResulteFileUrl string `json:"resulte_file_url" xorm:"not null default '' comment('操作结果文件路径') VARCHAR(255) 'resulte_file_url'"`
	//创建人id
	CreateId string `json:"create_id" xorm:"not null default 0 comment('创建人id') VARCHAR(100) 'create_id'"`
	//创建人姓名
	CreateName string `json:"create_name" xorm:"default '' comment('创建人姓名') VARCHAR(50) 'create_name'"`
	//创建人手机号
	CreateMobile string `json:"create_mobile" xorm:"default '' comment('创建人手机号') VARCHAR(50) 'create_mobile'"`
	//创建人ip
	CreateIp string `json:"create_ip" xorm:"default '' comment('创建人ip') VARCHAR(100) 'create_ip'"`
	//ip所属位置
	IpLocation string `json:"ip_location" xorm:"default '' comment('ip所属位置') VARCHAR(50) 'ip_location'"`
	//成功数量
	SuccessNum int `json:"success_num" xorm:"default 0 comment('成功数量') INT 'success_num'"`
	//失败数量
	FailNum int `json:"fail_num" xorm:"default 0 comment('失败数量') INT 'fail_num'"`
	//任务名称扩展字段
	ExtendedData string `json:"extended_data" xorm:"comment('任务名称扩展字段') TEXT 'extended_data'"`
	ContextData  string `json:"context_data" xorm:"comment('上下文数据') LONGTEXT 'context_data'"`
	OrgId        int    `json:"org_id" xorm:"default 'null' comment('主体ID') INT 'org_id'"`
	DoCount      int    `json:"do_count" xorm:"default 'null' comment('执行失败次数') INT 'do_count'"`
	ErrMes       string `json:"err_mes" xorm:"default 'null' comment('上次执行失败原因') VARCHAR(500) 'err_mes'"`
	//创建时间
	CreateTime string `json:"create_time" xorm:"default 'null' comment('创建时间') DATETIME 'create_time'"`
	//修改时间
	UpdateTime string `json:"update_time" xorm:"default 'null' comment('修改时间') DATETIME 'update_time'"`
	//操作类型 0:导出 1:导入
	OperationType int `json:"operation_type" xorm:"default 0 comment('操作类型 0:导出 1:导入') INT 'operation_type'"`
	//归属企业 1-北京百林康源 2-深圳利都
	OrgType int `json:"org_type" xorm:"default 0 comment('归属企业') INT 'org_type'"`
}

// 任务列表返回参数
type TaskListRes struct {
	viewmodel.BasePageHttpResponse
	//业务员列表数据
	Data []TaskList `json:"data"`
}

// 获取请求列表参数
type GetTaskListRequest struct {
	viewmodel.BasePageHttpRequest
	//任务id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//任务内容:1:批量新建;2:批量删除;3:批量更新
	TaskContent int32 `protobuf:"varint,2,opt,name=task_content,json=taskContent,proto3" json:"task_content"`
	//任务状态:1:调度中;2:进行中;3:已完成；4：失败
	TaskStatus int32 `protobuf:"varint,3,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	//创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	//排序类型：createTimeDesc:根据创建时间倒序
	Sort string `protobuf:"bytes,6,opt,name=sort,proto3" json:"sort"`
	//创建时间
	CreateTime string `protobuf:"bytes,7,opt,name=createtime,proto3" json:"create_time"`
	//主体
	OrgId int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//发起人 0:自己;1:全部;2:其他;
	Promoter int32 `protobuf:"varint,11,opt,name=promoter,proto3" json:"promoter"`
	//任务内容:1:业务员导入 2:业务员导出 3:分销订单导出 4:分销订单佣金结算导出 5-提现导出 6-导入打款记录(即提现导出) 7分销员导出 8分销商品导入 9分销商品导出 10保险分销结算导出 11线下企业导出
	//12百林康源物流码导出 13润合云店店铺数据-报表导出 14润合云店业务员统计数据导出 15商品数据导出 16商品区域数据导出 17-saas门店商品导出 18-saas连锁商品导出 19-saas连锁商品导入 20-门店商品操作批量上架导入
	//21-门店商品操作批量下架导入 22-门店商品操作批量删除导入 23-门店商品操作批量铺品导入 24-门店商品操作批量调价导入 25-分销订单列表导出订单数据（含商品明细） 26-门店服务导入 27-门店活体导入 100-百林康源物流码导入
	//101-百林康源防伪码查询导出 102-防伪码查询明细导出 103-极宠家客户列表导出 104-员工业绩导出列表
	ContentStr string `protobuf:"varint,11,opt,name=content_str,proto3" json:"content_str"`
}
