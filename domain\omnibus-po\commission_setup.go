package omnibus_po

import (
	"errors"
	"time"

	"eShop/infra/log"

	"github.com/shopspring/decimal" // 引入decimal包

	"xorm.io/xorm"
)

// CommissionSetup 提成活动表
type CommissionSetup struct {
	Id          int       `xorm:"pk autoincr 'id'" json:"id"`
	ChainId     int64     `xorm:"'chain_id'" json:"chainId"`
	StoreId     string    `xorm:"'store_id'" json:"storeId"`
	SetupName   string    `xorm:"'setup_name'" json:"setupName"`
	Status      int       `xorm:"'status'" json:"status"` // 1-启用，0-禁用
	Operator    string    `xorm:"'operator'" json:"operator"`
	OperatorId  int64     `xorm:"'operator_id'" json:"operatorId"`
	CreatedTime time.Time `xorm:"created 'created_time'" json:"createdTime"`
	UpdatedTime time.Time `xorm:"updated 'updated_time'" json:"updatedTime"`
}

// TableName 设置表名
func (c *CommissionSetup) TableName() string {
	return "eshop.commission_setup"
}

// CommissionDetail 提成明细表
type CommissionDetail struct {
	Id             int       `xorm:"pk autoincr 'id'" json:"id"`
	SetupId        int       `xorm:"'setup_id'" json:"setupId"`
	CommissionType int       `xorm:"'commission_type'" json:"commissionType"` // 1-商品提成，2-服务提成，3-寄养提成，4-活动提成
	CalcType       int       `xorm:"'calc_type'" json:"calcType"`             // 1-按售价，2-按实收金额
	CommissionRate float64   `xorm:"'commission_rate'" json:"commissionRate"` // 提成比例
	ScopeType      int       `xorm:"'scope_type'" json:"scopeType"`           // 1-全部，2-指定
	CreatedTime    time.Time `xorm:"created 'created_time'" json:"createdTime"`
	UpdatedTime    time.Time `xorm:"updated 'updated_time'" json:"updatedTime"`
}

// TableName 设置表名
func (c *CommissionDetail) TableName() string {
	return "eshop.commission_detail"
}

// CommissionEmployee 提成员工表
type CommissionEmployee struct {
	Id          int       `xorm:"pk autoincr 'id'" json:"id"`
	SetupId     int       `xorm:"'setup_id'" json:"setupId"`       // 关联提成设置表ID
	EmployeeId  int64     `xorm:"'employee_id'" json:"employeeId"` // 员工ID
	Operator    string    `xorm:"'operator'" json:"operator"`      // 操作人
	OperatorId  int64     `xorm:"'operator_id'" json:"operatorId"` // 操作人ID
	CreatedTime time.Time `xorm:"created 'created_time'" json:"createdTime"`
	UpdatedTime time.Time `xorm:"updated 'updated_time'" json:"updatedTime"`
}

// TableName 设置表名
func (c *CommissionEmployee) TableName() string {
	return "eshop.commission_employee"
}

// CommissionPerformance 员工业绩表
type CommissionPerformance struct {
	Id               int       `xorm:"pk autoincr 'id'" json:"id"`
	OrderId          int       `xorm:"'order_id'" json:"orderId"`
	OrderNo          string    `xorm:"'order_no'" json:"orderNo"`
	EmployeeId       int64     `xorm:"'employee_id'" json:"employeeId"`
	RealName         string    `xorm:"'real_name'" json:"realName"`
	StoreId          int64     `xorm:"'store_id'" json:"storeId"`
	ProductType      int       `xorm:"'product_type'" json:"productType"`
	SkuId            int64     `xorm:"'sku_id'" json:"skuId"`
	ProductName      string    `xorm:"'product_name'" json:"productName"`
	SalesAmount      int64     `xorm:"'sales_amount'" json:"salesAmount"`
	CommissionRate   float64   `xorm:"'commission_rate'" json:"commissionRate"` // 提成比例(百分比，如5.5表示5.5%)
	CommissionAmount int64     `xorm:"'commission_amount'" json:"commissionAmount"`
	SetupId          int       `xorm:"'setup_id'" json:"setupId"`
	SetupName        string    `xorm:"'setup_name'" json:"setupName"`
	OrderTime        time.Time `xorm:"'order_time'" json:"orderTime"`
	UnitPrice        int64     `xorm:"'unit_price'" json:"unitPrice"`
	Quantity         int       `xorm:"'quantity'" json:"quantity"`
	CustomerName     string    `xorm:"'customer_name'" json:"customer_name"`
	Operator         string    `xorm:"'operator'" json:"operator"`
	OperatorId       int64     `xorm:"'operator_id'" json:"operatorId"`
	CreatedTime      time.Time `xorm:"created 'created_time'" json:"createdTime"`
	UpdatedTime      time.Time `xorm:"updated 'updated_time'" json:"updatedTime"`
}

// TableName 设置表名
func (c *CommissionPerformance) TableName() string {
	return "eshop.commission_performance"
}

// OrderItemCommission 订单项目提成信息
type OrderItemCommission struct {
	OrderId       int       // 订单ID
	OrderNo       string    // 订单编号
	SkuId         int64     // 商品SKU ID
	ProductName   string    // 商品名称
	ProductType   int       // 商品类型
	SalesAmount   int64     // 销售金额(分)
	ActualAmount  int64     // 实收金额(分)
	OrderTime     time.Time // 订单时间
	StoreId       int64     // 店铺ID
	Channel       int       // 订单来源: 1-小程序 2-美团 3-饿了么 100-线下门店
	AssignedEmpId int64     // 商品指定的员工ID，仅用于线下门店
	// 单价
	UnitPrice int64 `json:"unit_price"`
	//数量
	Quantity int `json:"quantity"`
	// 客户姓名
	CustomerName string `json:"customer_name"`
}

// EmployeeWithSetupInfo 员工信息与提成设置信息
type EmployeeWithSetupInfo struct {
	// 员工ID
	Id int64 `xorm:"id"`
	// 所属店铺
	TenantId int64 `xorm:"tenant_id"`
	// 员工姓名
	RealName string `xorm:"real_name"`
	// 手机号码
	Mobile string `xorm:"mobile"`
	// 角色名称
	RoleName string `xorm:"role_name"`
	// 是否已选择
	IsSelected bool `xorm:"is_selected"`
	// 所属提成设置名称
	SetupName string `xorm:"setup_name"`
	// 操作人
	Operator string `xorm:"operator"`
	//员工添加时间
	CreatedTime time.Time `xorm:"created_time"`
}

// 创建提成设置
func (c *CommissionSetup) Create(session *xorm.Session) error {
	_, err := session.Insert(c)
	return err
}

// 更新提成设置
func (c *CommissionSetup) Update(session *xorm.Session) error {
	_, err := session.ID(c.Id).Update(c)
	return err
}

// 删除提成设置
func (c *CommissionSetup) Delete(session *xorm.Session) error {
	_, err := session.ID(c.Id).Delete(c)
	return err
}

// 根据ID获取提成设置
func (c *CommissionSetup) GetById(session *xorm.Session, id int) (*CommissionSetup, error) {
	setup := &CommissionSetup{}
	has, err := session.ID(id).Get(setup)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return setup, nil
}

// 获取店铺的提成设置列表
func (c *CommissionSetup) GetList(session *xorm.Session, storeId string, chainId int64, setupName string, status int, offset, limit int) ([]*CommissionSetup, int64, error) {
	query := session.Where("1=1")

	if storeId != "" {
		query = query.And("store_id = ?", storeId)
	}

	if chainId > 0 {
		query = query.And("chain_id = ?", chainId)
	}

	if setupName != "" {
		query = query.And("setup_name like ?", "%"+setupName+"%")
	}

	if status > 0 {
		query = query.And("status = ?", status)
	}

	// 获取数据
	var results []*CommissionSetup
	total, err := query.Limit(limit, offset).OrderBy("created_time desc").FindAndCount(&results)
	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

// 创建提成明细
func (c *CommissionDetail) Create(session *xorm.Session) error {
	_, err := session.Insert(c)
	return err
}

// 批量创建提成明细
func (c *CommissionDetail) BatchCreate(session *xorm.Session, details []*CommissionDetail) error {
	_, err := session.Insert(details)
	return err
}

// 删除提成设置的所有明细
func (c *CommissionDetail) DeleteBySetupId(session *xorm.Session, setupId int64) error {
	_, err := session.Where("setup_id = ?", setupId).Delete(new(CommissionDetail))
	return err
}

// 根据设置ID获取提成明细列表
func (c *CommissionDetail) GetBySetupId(session *xorm.Session, setupId int) ([]*CommissionDetail, error) {
	var details []*CommissionDetail
	err := session.Where("setup_id = ?", setupId).Find(&details)
	return details, err
}

// 更新提成明细
func (c *CommissionDetail) Update(session *xorm.Session) error {
	_, err := session.ID(c.Id).Update(c)
	return err
}

// 删除提成明细
func (c *CommissionDetail) Delete(session *xorm.Session) error {
	_, err := session.ID(c.Id).Delete(c)
	return err
}

// GetEmployeeListWithSetup 获取员工列表及其选中状态和提成活动名称
func (c *CommissionEmployee) GetEmployeeListWithSetup(
	session *xorm.Session,
	setupId int,
	tenantId int64,
	realName string,
	mobile string,
	roleId int64,
	sourceChainId int64,
	pageIndex int,
	pageSize int) ([]EmployeeWithSetupInfo, int64, error) {

	var items []EmployeeWithSetupInfo

	// 构建基础查询SQL
	query := session.Table("eshop_saas.t_employee").
		Alias("e").
		Join("LEFT", "eshop_saas.t_role r", "e.role_id = r.id").
		// 左连接查询员工是否在当前活动中(用于确定is_selected)
		Join("LEFT", "eshop.commission_employee ce_current", "e.id = ce_current.employee_id AND ce_current.setup_id = ?", setupId).
		// 左连接查询员工参与的任何活动
		Join("LEFT", "(SELECT ce.employee_id, cs.setup_name, cs.operator FROM eshop.commission_employee ce JOIN eshop.commission_setup cs ON ce.setup_id = cs.id WHERE cs.status = 1) AS emp_setup", "e.id = emp_setup.employee_id").
		Where("e.is_deleted = 0 AND e.state = 1")

	// 应用筛选条件
	if tenantId > 0 {
		query = query.And("e.tenant_id = ?", tenantId)
	}

	if sourceChainId > 0 {
		query = query.And("e.source_chain_id = ?", sourceChainId)
	}

	if realName != "" {
		query = query.And("e.real_name LIKE ?", "%"+realName+"%")
	}

	if mobile != "" {
		query = query.And("e.account LIKE ?", "%"+mobile+"%")
	}

	if roleId > 0 {
		query = query.And("e.role_id = ?", roleId)
	}

	// 设置需要查询的字段
	query = query.Select(`
		e.id,
		e.tenant_id,
		e.real_name,
		emp_setup.operator,
		e.account as mobile,
		r.name as role_name,
		e.created_time,
		CASE WHEN ce_current.id IS NULL THEN 0 ELSE 1 END AS is_selected,
		IFNULL(emp_setup.setup_name, '') AS setup_name
	`)

	// 设置排序和分页
	query = query.OrderBy("e.created_time DESC")

	// 计算偏移量
	offset := (pageIndex - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// 使用FindAndCount查询总数和结果
	count, err := query.Limit(pageSize, offset).FindAndCount(&items)

	return items, count, err
}

// 检查员工是否已参与其他提成活动
func (c *CommissionEmployee) CheckEmployeeInOtherSetup(session *xorm.Session, setupId int, employeeId int64, storeId string) (bool, string, error) {
	var result struct {
		SetupName string `xorm:"setup_name"`
	}
	has, err := session.Table("eshop.commission_employee").
		Join("LEFT", "eshop.commission_setup", "commission_employee.setup_id = commission_setup.id").
		Where("commission_employee.employee_id = ? AND commission_employee.setup_id != ? AND commission_setup.status = 1 AND commission_setup.store_id = ?",
			employeeId, setupId, storeId).
		Cols("commission_setup.setup_name").
		Get(&result)

	return has, result.SetupName, err
}

// 删除提成设置的所有员工
func (c *CommissionEmployee) DeleteBySetupId(session *xorm.Session, setupId int) error {
	_, err := session.Where("setup_id = ?", setupId).Delete(new(CommissionEmployee))
	return err
}

// 批量创建提成员工
func (c *CommissionEmployee) BatchCreate(session *xorm.Session, employees []*CommissionEmployee) error {
	if len(employees) == 0 {
		return nil
	}
	_, err := session.Insert(employees)
	return err
}

// GetEmployeeCountBySetupId 统计提成设置关联的员工数量
func (c *CommissionEmployee) GetEmployeeCountBySetupId(session *xorm.Session, setupId int) (int, error) {
	count, err := session.Where("setup_id = ?", setupId).Count(new(CommissionEmployee))
	return int(count), err
}

// AssignOrderCommission 分配订单业绩给员工
// 根据订单信息和商品信息，匹配员工提成设置和规则，计算提成金额并保存业绩记录
// 参数:
//   - session: 数据库会话
//   - orderItems: 订单项目信息列表
//   - operator: 操作人
//   - operatorId: 操作人ID
//
// 返回:
//   - 成功分配的业绩记录数量
//   - 错误信息
func (c *CommissionPerformance) AssignOrderCommission(
	session *xorm.Session,
	orderItems []OrderItemCommission,
	operator string,
	operatorId int64) (int, error) {

	if len(orderItems) == 0 {
		return 0, nil
	}

	// 获取订单中第一个商品信息，用于获取店铺ID等公共信息
	firstItem := orderItems[0]
	storeId := firstItem.StoreId
	orderNo := firstItem.OrderNo

	log.Info("开始分配订单业绩，订单号: ", orderNo, " 店铺ID:", storeId)

	// 收集需要创建的业绩记录
	var performances []*CommissionPerformance

	// 收集有指定员工的商品的员工ID集合
	employeeIds := make([]int64, 0)
	for _, item := range orderItems {
		if item.AssignedEmpId > 0 {
			// 检查员工ID是否已在列表中
			exists := false
			for _, id := range employeeIds {
				if id == item.AssignedEmpId {
					exists = true
					break
				}
			}
			if !exists {
				employeeIds = append(employeeIds, item.AssignedEmpId)
			}
		}
	}

	// 如果没有指定员工，直接返回
	if len(employeeIds) == 0 {
		log.Infof("订单号 %s 没有指定员工，跳过分配提成", orderNo)
		return 0, nil
	}

	// 查询这些员工的提成设置信息
	var employeeSetups []struct {
		EmployeeId int64  `xorm:"employee_id"`
		SetupId    int    `xorm:"setup_id"`
		RealName   string `xorm:"real_name"`
		SetupName  string `xorm:"setup_name"`
	}

	// 修改后的代码 - 使用In方法代替IN条件
	if err := session.Table("eshop.commission_employee").
		Alias("ce").
		Join("LEFT", "eshop.commission_setup cs", "ce.setup_id = cs.id").
		Join("LEFT", "eshop_saas.t_employee e", "ce.employee_id = e.id").
		Where("cs.store_id = ? AND cs.status = 1", storeId).
		In("ce.employee_id", employeeIds). // 使用In方法
		Select("ce.employee_id, ce.setup_id, e.real_name, cs.setup_name").
		Find(&employeeSetups); err != nil {
		log.Errorf("查询员工提成设置失败，订单号: %s, 错误: %v", orderNo, err)
		return 0, err
	}

	// 如果没有员工参与提成设置，直接返回
	if len(employeeSetups) == 0 {
		log.Infof("订单号 %s 没有员工参与提成设置，跳过分配提成", orderNo)
		return 0, nil
	}

	// 构建员工提成设置映射，方便查询
	empSetupMap := make(map[int64]struct {
		SetupId   int
		SetupName string
		RealName  string
	})

	setupIds := make([]int, 0)
	for _, es := range employeeSetups {
		empSetupMap[es.EmployeeId] = struct {
			SetupId   int
			SetupName string
			RealName  string
		}{
			SetupId:   es.SetupId,
			SetupName: es.SetupName,
			RealName:  es.RealName,
		}

		// 检查设置ID是否已在列表中
		exists := false
		for _, id := range setupIds {
			if id == es.SetupId {
				exists = true
				break
			}
		}
		if !exists {
			setupIds = append(setupIds, es.SetupId)
		}
	}

	log.Infof("订单号 %s 涉及 %d 个提成设置", orderNo, len(setupIds))

	// 查询所有相关提成设置的明细规则
	var allDetails []*CommissionDetail
	if err := session.In("setup_id", setupIds).Find(&allDetails); err != nil {
		log.Errorf("查询提成明细规则失败，订单号: %s, 错误: %v", orderNo, err)
		return 0, err
	}

	if len(allDetails) == 0 {
		log.Infof("订单号 %s 没有提成明细规则，跳过分配提成", orderNo)
		return 0, nil
	}

	// 按设置ID分组整理提成明细
	setupDetailsMap := make(map[int][]*CommissionDetail)
	var allSpecifiedDetailIds []int

	for _, detail := range allDetails {
		setupDetailsMap[detail.SetupId] = append(setupDetailsMap[detail.SetupId], detail)
		if detail.ScopeType == 2 { // 2-指定
			allSpecifiedDetailIds = append(allSpecifiedDetailIds, detail.Id)
		}
	}

	// 查询所有指定范围的商品信息
	var specifiedProducts []*CommissionProduct
	if len(allSpecifiedDetailIds) > 0 {
		if err := session.Table("eshop.commission_product").
			In("detail_id", allSpecifiedDetailIds).
			Find(&specifiedProducts); err != nil {
			log.Errorf("查询指定范围商品信息失败，订单号: %s, 错误: %v", orderNo, err)
			return 0, err
		}
	}

	// 按明细ID分组整理商品
	detailProductsMap := make(map[int][]*CommissionProduct)
	for _, product := range specifiedProducts {
		detailProductsMap[product.DetailId] = append(detailProductsMap[product.DetailId], product)
	}

	// 直接遍历商品，为每个商品查找匹配的员工和提成规则
	for _, item := range orderItems {
		// 只处理指定了员工的商品
		if item.AssignedEmpId <= 0 {
			continue
		}

		// 获取员工提成设置
		empSetup, exists := empSetupMap[item.AssignedEmpId]
		if !exists {
			// 该员工没有提成设置，跳过
			log.Infof("订单号 %s, 商品 %s, 员工ID %d 没有提成设置，跳过分配提成",
				item.OrderNo, item.ProductName, item.AssignedEmpId)
			continue
		}

		// 获取该设置的明细规则
		details, exists := setupDetailsMap[empSetup.SetupId]
		if !exists || len(details) == 0 {
			// 没有提成明细规则，跳过
			log.Infof("订单号 %s, 商品 %s, 员工 %s 的提成设置没有明细规则，跳过分配提成",
				item.OrderNo, item.ProductName, empSetup.RealName)
			continue
		}

		// 查找匹配的提成明细
		var matchedDetail *CommissionDetail

		// 1. 先查找全部范围的提成规则
		for _, detail := range details {
			// ProductType - 1-商品，4-服务，5-活体，6-寄养 commissionType - 1-商品提成，2-服务提成，3-寄养提成，4-活动提成
			if detail.ScopeType == 1 && detail.CommissionType == getCommissionTypeByProductType(item.ProductType) {
				matchedDetail = detail
				break
			}
		}

		// 2. 如果没找到全部范围的规则，再查找指定商品范围的规则
		if matchedDetail == nil {
			for _, detail := range details {
				if detail.ScopeType == 2 && detail.CommissionType == getCommissionTypeByProductType(item.ProductType) {
					// 检查该明细下是否有匹配的商品
					products := detailProductsMap[detail.Id]
					for _, product := range products {
						if product.ProductType == item.ProductType && product.SkuId == item.SkuId {
							matchedDetail = detail
							break
						}
					}
					if matchedDetail != nil {
						break
					}
				}
			}
		}

		// 如果找到了匹配的提成明细，计算并记录业绩
		if matchedDetail != nil {
			// 检查该订单项是否已给该员工分配过业绩
			exists, err := session.Where(
				"order_id = ? AND sku_id = ? AND employee_id = ?",
				item.OrderId, item.SkuId, item.AssignedEmpId).
				Exist(&CommissionPerformance{})
			if err != nil {
				log.Errorf("检查订单项业绩记录失败，订单号: %s, 商品ID: %d, 员工ID: %d, 错误: %v",
					item.OrderNo, item.SkuId, item.AssignedEmpId, err)
				return 0, err
			}

			if exists {
				log.Infof("订单号 %s, 商品 %s 已存在员工 %s 的业绩记录，跳过分配提成",
					item.OrderNo, item.ProductName, empSetup.RealName)
				continue // 已存在记录，跳过
			}

			// 计算提成金额
			var baseAmount int64
			if matchedDetail.CalcType == 1 { // 1-按售价
				baseAmount = item.SalesAmount
			} else { // 2-按实收金额
				baseAmount = item.ActualAmount
			}

			// 使用decimal精确计算提成金额
			baseDecimal := decimal.NewFromInt(baseAmount)
			rateDecimal := decimal.NewFromFloat(matchedDetail.CommissionRate)
			hundredDecimal := decimal.NewFromInt(100)

			// 计算提成金额 (分)：baseAmount * commissionRate / 100
			commissionDecimal := baseDecimal.Mul(rateDecimal).Div(hundredDecimal)
			commissionAmount := commissionDecimal.IntPart()

			// 创建业绩记录
			performance := &CommissionPerformance{
				OrderId:          item.OrderId,
				OrderNo:          item.OrderNo,
				EmployeeId:       item.AssignedEmpId,
				RealName:         empSetup.RealName,
				StoreId:          storeId,
				ProductType:      item.ProductType,
				SkuId:            item.SkuId,
				ProductName:      item.ProductName,
				SalesAmount:      item.SalesAmount,
				CommissionRate:   matchedDetail.CommissionRate,
				CommissionAmount: commissionAmount,
				SetupId:          empSetup.SetupId,
				SetupName:        empSetup.SetupName,
				OrderTime:        item.OrderTime,
				Operator:         operator,
				OperatorId:       operatorId,
				UnitPrice:        item.UnitPrice,
				Quantity:         item.Quantity,
				CustomerName:     item.CustomerName,
			}

			performances = append(performances, performance)
			log.Infof("订单号 %s, 商品 %s 分配给员工 %s 提成金额 %d 分", item.OrderNo, item.ProductName, empSetup.RealName, commissionAmount)
		} else {
			log.Infof("订单号 %s, 商品 %s 未找到匹配的提成规则，跳过分配提成", item.OrderNo, item.ProductName)
		}
	}

	// 批量保存业绩记录
	if len(performances) > 0 {
		if _, err := session.Insert(performances); err != nil {
			log.Errorf("保存业绩记录失败，订单号: %s, 错误: %v", orderNo, err)
			return 0, err
		}
		log.Infof("订单号 %s 成功分配 %d 条业绩记录", orderNo, len(performances))

		// 获取第一个员工的姓名作为业绩分配员工名称
		staffName := ""
		if len(performances) > 0 {
			staffName = performances[0].RealName
		}

		// 更新订单详情表，将业绩分配信息冗余保存
		_, err := session.Exec(`
			update dc_order.order_detail d
			inner join dc_order.order_main o on o.order_sn=d.order_sn 
			set d.performance_staff_name=?,d.performance_operator_name=?,d.performance_operator_time=?
			where o.order_sn=? or o.parent_order_sn=?
		`, staffName, operator, time.Now().Format("2006-01-02 15:04:05"), orderNo, orderNo)

		if err != nil {
			log.Errorf("更新订单详情业绩分配信息失败，订单号: %s, 错误: %v", orderNo, err)
			return 0, errors.New("更新订单详情业绩分配信息失败，" + err.Error())
		}
		log.Infof("订单号 %s 成功更新订单详情业绩分配信息", orderNo)
	} else {
		log.Infof("订单号 %s 没有匹配的业绩记录需要保存", orderNo)
	}

	return len(performances), nil
}

// getCommissionTypeByProductType 根据商品类型获取对应的提成类型
func getCommissionTypeByProductType(productType int) int {
	// - 1-商品，4-服务，5-活体，6-寄养 commissionType - 1-商品提成，2-服务提成，3-寄养提成，4-活体提成
	switch productType {
	case 1: // 商品
		return 1 
	case 4: // 服务
		return 2 
	case 5: //活体
		return 4
	case 6: // 寄养
		return 3 
	default:
		return 1 
	}
}
