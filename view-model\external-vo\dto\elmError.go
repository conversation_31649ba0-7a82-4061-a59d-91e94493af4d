package dto

import "time"

type ElmError struct {
	Id         int       `json:"id" xorm:"pk autoincr not null comment('主键id') BIGINT 'id'"`
	ShopId     string    `json:"shop_id" xorm:"default 'null' comment('用来表示店铺的唯一标识') VARCHAR(100) 'shop_id'"`
	SkuId      string    `json:"sku_id" xorm:"default 'null' comment('用来表示商品的唯一标识') VARCHAR(100) 'sku_id'"`
	ErrMes     string    `json:"err_mes" xorm:"default 'null' comment('错误描述') VARCHAR(2000) 'err_mes'"`
	ErrorCode  string    `json:"error_code" xorm:"default 'null' comment('饿了么的接口') VARCHAR(100) 'error_code'"`
	ElmCmd     string    `json:"elm_cmd" xorm:"default 'null' comment('饿了么的接口') VARCHAR(100) 'elm_cmd'"`
	CreateTime time.Time `xorm:"default 'current_timestamp()' comment('创建时间') DATETIME created"`
}

func (c ElmError) TableName() string {
	return "dc_product.elm_error"
}

type ELmReturn struct {
	FailedList []struct {
		CustomSkuID string `json:"custom_sku_id"`
		ErrorMsg    string `json:"error_msg"`
		ErrorNo     int    `json:"error_no"`
		Upc         string `json:"upc"`
	} `json:"failed_list"`
	SuccessList []struct {
		CustomSkuID string `json:"custom_sku_id"`
		SkuID       int64  `json:"sku_id"`
		Stock       int    `json:"stock"`
		Upc         string `json:"upc"`
	} `json:"success_list"`
}
