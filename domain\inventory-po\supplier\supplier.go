package supplier

import (
	"context"
	"errors"
	"time"

	vo "eShop/view-model/inventory-vo/supplier"

	"xorm.io/xorm"
)

// Supplier 供应商实体
type Supplier struct {
	Id               int       `json:"id" xorm:"id pk autoincr comment('供应商ID')"`
	ChainId          int64     `json:"chainId" xorm:"chain_id notnull comment('连锁ID')"`
	Code             string    `json:"code" xorm:"code varchar(50) notnull comment('供应商编码')"`
	Name             string    `json:"name" xorm:"name varchar(100) notnull comment('供应商名称')"`
	Address          string    `json:"address" xorm:"address varchar(255) comment('供应商地址')"`
	Type             vo.SupplierType    `json:"type" xorm:"type varchar(50) comment('供应商类型')"`
	UnifiedSocialId  string    `json:"unifiedSocialId" xorm:"unified_social_id varchar(50) comment('统一社会信用编码')"`
	PurchasePeriod   int       `json:"purchasePeriod" xorm:"purchase_period comment('采购周期(天)')"`
	DueDays          int       `json:"dueDays" xorm:"due_days comment('到货天数')"`
	ContactPerson    string    `json:"contactPerson" xorm:"contact_person varchar(50) comment('联系人')"`
	ContactMethod    string    `json:"contactMethod" xorm:"contact_method varchar(50) comment('联系方式')"`
	BankAccount      string    `json:"bankAccount" xorm:"bank_account varchar(50) comment('银行账号')"`
	BankName         string    `json:"bankName" xorm:"bank_name varchar(100) comment('开户银行')"`
	AccountName      string    `json:"accountName" xorm:"account_name varchar(100) comment('开户名称')"`
	WarehouseId      int       `json:"warehouseId" xorm:"warehouse_id comment('仓库ID')"`
	Source           int       `json:"source" xorm:"source comment('1.店铺自建；2.连锁创建')"`
	Url              string    `json:"url" xorm:"url varchar(255) comment('供应商官网')"`
	Status           int       `json:"status" xorm:"status notnull comment('状态')"`
	CreatedOperator  string    `json:"createdOperator" xorm:"created_operator varchar(255) notnull comment('创建人')"`
	CreatedTime      time.Time `json:"createdTime" xorm:"created_time datetime notnull default(CURRENT_TIMESTAMP) comment('创建时间')"`
	UpdatedOperator  string    `json:"updatedOperator" xorm:"updated_operator varchar(255) notnull comment('更新人')"`
	UpdatedTime      time.Time `json:"updatedTime" xorm:"updated_time datetime notnull default(CURRENT_TIMESTAMP) comment('更新时间')"`
	Version          int       `json:"version" xorm:"version int notnull default(1) comment('版本号')"`
}

// TableName 表名
func (s *Supplier) TableName() string {
	return "inventory_suppliers"
}

// Create 创建供应商
func (s *Supplier) Create(ctx context.Context, session *xorm.Session) error {
	s.Status =1
	s.Version = 1
	_, err := session.Context(ctx).Insert(s)
	return err
}

// Update 更新供应商
func (s *Supplier) Update(ctx context.Context, session *xorm.Session) error {
	rows, err := session.Context(ctx).ID(s.Id).
		Where("chain_id = ?", s.ChainId).
		Update(s)
	if err != nil {
		return err
	}
	if rows == 0 {
		return errors.New("supplier not found or version conflict")
	}
	return nil
}

// Delete 删除供应商
func (s *Supplier) Delete(ctx context.Context, session *xorm.Session) error {
	rows, err := session.Context(ctx).ID(s.Id).
		Where("chain_id = ? AND version = ?", s.ChainId, s.Version).
		Delete(s)
	if err != nil {
		return err
	}
	if rows == 0 {
		return errors.New("supplier not found or version conflict")
	}
	return nil
}

// GetByID 根据ID获取供应商
func (s *Supplier) GetByID(ctx context.Context, session *xorm.Session, id int) error {
	has, err := session.Context(ctx).ID(id).
		Where("chain_id = ?", s.ChainId).
		Get(s)
	if err != nil {
		return err
	}
	if !has {
		return errors.New("supplier not found")
	}
	return nil
}

// List 查询供应商列表
func (s *Supplier) List(ctx context.Context, session *xorm.Session, offset, limit int) ([]Supplier, int64, error) {
	var suppliers []Supplier

	// 构建查询条件
	query := session.Context(ctx).Where("chain_id = ?", s.ChainId)

	// 获取总数
	total, err := query.Count(&Supplier{})
	if err != nil {
		return nil, 0, err
	}

	// 查询数据
	err = query.Limit(limit, offset).Desc("id").Find(&suppliers)
	if err != nil {
		return nil, 0, err
	}

	return suppliers, total, nil
}
// Event constants
const (
	EventTypeSupplierCreated = "supplier.created"
	EventTypeSupplierUpdated = "supplier.updated"
)
