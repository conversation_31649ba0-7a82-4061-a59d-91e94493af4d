package petai_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

const (

	// 发送这条消息的实体:user-代表该条消息内容是用户发送的;assistant-代表该条消息内容是 Bot 发送的
	MessageRoleUser      = "user"
	MessageRoleAssistant = "assistant"

	// 消息类型
	MessageTypeQuestion     = "question"      // 消息类型:question-用户输入内容;
	MessageTypeAnswer       = "answer"        // answer-Bot返回给用户的消息内容，支持增量返回;
	MessageTypeFuncCall     = "function_call" // function_call-Bot 对话过程中调用函数（function call）的中间结果;
	MessageTypeToolOutput   = "tool_output"
	MessageTypeToolResponse = "tool_response" // tool_response-调用工具(function call)后返回的结果;
	MessageTypeFollowUp     = "follow_up"     // follow_up-如果在 Bot上配置打开了用户问题建议开关，则会返回推荐问题相关的回复内容;
	MessageTypeVerbose      = "verbose"       // verbose-多 answer 场景下，服务端会返回一个 verbose 包，对应的 content 为 JSON 格式，content.msg_type =generate_answer_finish 代表全部 answer 回复完成。不支持在请求中作为入参。

	// 消息内容的类型:text-文本；object_string-多模态内容，即文本和文件的组合、文本和图片的组合;card-卡片。此枚举值仅在接口响应中出现，不支持作为入参
	MessageContentTypeText         = "text"
	MessageContentTypeObjectString = "object_string"

	MessageObjectStringTypeText  string = "text"
	MessageObjectStringTypeFile  string = "file"
	MessageObjectStringTypeImage string = "image"
	MessageObjectStringTypeAudio string = "audio"

	IntentMedical     int    = 1 //"医疗问题"
	IntentBaiKe       int    = 2 //"百科问题"
	IntentMedicalDesc string = "医疗问题"
	IntentBaiKeDesc   string = "百科问题"

	DataSourceCoze    = 1 // 消息来源:1-coze;2-自研医疗模型
	DataSourcePetaiAI = 2 // 消息来源:1-coze;2-自研医疗模型

	DataFromAI         = 1 // 数据来源：1-ai 2-好兽医互联网医院
	DataFromPetMedical = 2 // 数据来源：1-ai 2-好兽医互联网医院

	MessageEvaluateInit = 0 // 消息评价:0-初始
	MessageEvaluateLike = 1 // 消息评价:1-赞
	MessageEvaluateHate = 2 // 消息评价:2-踩

	FeedbackTypeNoHelp      = 1 // 没有帮助
	FeedbackTypeInfoWrong   = 2 // 信息有误
	FeedbackTypeUnderstand  = 3 // 理解错误
	FeedbackTypeViolate     = 4 // 违法有害
	FeedbackTypeIncomplete  = 5 // 内容不完整
	FeedbackTypeNotProfess  = 6 // 内容不专业
	FeedbackTypeFormatError = 7 // 格式错误
)

// 小闻养宠助消息表 (注意： 只有消息类型为answer时，DataSource这个字段才会有值， 只有消息类型为question时，Intent这个字段才会有值 )
type PetaiMessage struct {
	Id         int    `json:"id"  xorm:"pk autoincr 'id' comment('主键')"` //自增id
	Uuid       string `json:"uuid"`                                      // 消息uuid,唯一标识该条消息
	Intent     int    `json:"intent"`                                    // 前置模型预测问题类型：1-医疗问题,2-养宠百科
	DataSource int    `json:"data_source"`                               // 内容来源:1-coze;2-自研医疗模型
	DataFrom   int    `json:"data_from"`                                 //数据来源：1-ai 2-好兽医互联网医院

	UserInfoId               string    `json:"user_info_id"`                        //eshop.user_info.user_info_id用户ID作为唯一标示
	BotId                    string    `json:"botId"`                               // coze智能体id
	BotType                  int       `json:"bot_type" xorm:"not null TINYINT(4)"` // 智能体类型：1-养宠助手火山 2-小闻模型 3-宠物自诊 4-宠物识别；5-健康建议  6-互联网医院
	CozeConversationId       string    `json:"coze_conversation_id"`                //coze中的会话id
	CozeConversationCreateAt int       `json:"coze_conversation_create_at"`         //coze会话创建时间
	ConversationId           int       `json:"conversation_id"`                     //会话id即petai_conversation.id
	SectionId                string    `json:"section_id"`                          // coze上下文片段 ID
	ChatId                   string    `json:"chat_id"`                             //coze对话id
	CozeMessageId            string    `json:"coze_message_id"`                     // coze消息id
	ReasoningContent         string    `json:"reasoning_content"`
	Role                     string    `json:"role"`                       //发送这条消息的实体:user-代表该条消息内容是用户发送的;assistant-代表该条消息内容是 Bot 发送的
	Type                     string    `json:"type"`                       //消息类型:question-用户输入内容;answer-Bot返回给用户的消息内容，支持增量返回;function_call-Bot 对话过程中调用函数（function call）的中间结果;tool_response-调用工具(function call)后返回的结果;follow_up-如果在 Bot上配置打开了用户问题建议开关，则会返回推荐问题相关的回复内容;verbose-多 answer 场景下，服务端会返回一个 verbose 包，对应的 content 为 JSON 格式，content.msg_type =generate_answer_finish 代表全部 answer 回复完成。不支持在请求中作为入参。
	ContentType              string    `json:"content_type"`               //消息内容的类型:text-文本；object_string-多模态内容，即文本和文件的组合、文本和图片的组合;card-卡片。此枚举值仅在接口响应中出现，不支持作为入参
	Content                  string    `json:"content"`                    //消息内容
	MetaData                 string    `json:"meta_data"`                  //创建消息时的附加消息，获取消息时也会返回此附加消息
	Evaluate                 int       `json:"evaluate"`                   //消息评价：0-初始，1-赞，2-踩
	EvaluateTime             time.Time `json:"evaluate_time"`              //消息评价时间
	Feedback                 string    `json:"feedback"`                   //用户反馈
	FeedbackType             int       `json:"feedback_type"`              //反馈类别:1-没有帮助，2-信息有误，3-理解错误，4-违法有害，5-内容不完整 6-内容不专业，7-格式错误
	MessageTime              time.Time `json:"message_time"`               //消息时间
	CreateTime               time.Time `json:"create_time" xorm:"created"` //创建时间
	UpdateTime               time.Time `json:"update_time" xorm:"updated"` //更新时间
	PmOrderSn                int64     `json:"pm_order_sn"`                //互联网医院问诊订单号
	ImRecordId               int64     `json:"im_record_id"`               // 互联网医院环信信息表iDpet_medical.pm_im_record.id
	IsImSystemMsg            int       `json:"is_im_system_msg"`           // 是否是环信系统发送的消息(1-是，2不是)
	CloudFileUrl             string    `json:"cloud_file_url"`             //云上数据URL,多个用英文逗号隔开',
	ImExt                    string    `json:"im_ext"`                     // 扩展字段

}

// d
type MessageContentDetail struct {
	// The content type of the multimodal message.
	Type string `json:"type"` // text or file or image or audio

	// Text content. Required when type is text.
	Text string `json:"text,omitempty"`

	// The ID of the file or image content.
	FileID string `json:"file_id,omitempty"`
	// File name. Required when type is file.
	FileName string `json:"file_name,omitempty"`

	CreatedAt int `json:"created_at"`
	// The online address of the file or image content. Must be a valid address that is publicly
	// accessible. file_id or file_url must be specified when type is file or image.
	FileURL string `json:"file_url,omitempty"`
}
type PetaiMessageExd struct {
	PetaiConversation `xorm:"extends"`
	PetaiMessage      `xorm:"extends"`
}

func (m *PetaiMessage) TableName() string {
	return "eshop.petai_message"
}

type GetMessagesReq struct {
	UserInfoId     string   `json:"eshop_user_id"`
	ConversationId int      `json:"conversation_id"`
	Type           string   `json:"type"`
	Types          []string `json:"types"`
	OrderBy        string   `json:"order_by"`
	DataSource     int      `json:"data_source"` // 数据来源：1-coze,2自研问诊模型
	Limit          int      `json:"limit"`       //限制条数
	Evaluate       int      `json:"evaluate"`    // 消息评价：0-初始，1-赞，2-踩
	DataFrom       int      `json:"data_from"`   // 数据来源：1-ai 2-好兽医互联网医院

}

func (m *PetaiMessage) GetConversationMessages(session *xorm.Session, in GetMessagesReq) (out []PetaiMessageExd, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if in.ConversationId == 0 {
		err = errors.New("会话id为空")
		return
	}

	session.Table("eshop.petai_conversation").Alias("a").
		Join("inner", "eshop.petai_message b", "a.conversation_id=b.conversation_id and a.scrm_user_id").
		Where("a.conversation_id=?", in.ConversationId)
	if len(in.UserInfoId) > 0 {
		session.Where("a.user_info_id=?", in.UserInfoId)
	}
	if in.Types != nil {
		session.In("b.type", in.Types)
	}
	if in.DataSource > 0 {
		session.Where("a.data_source=?", in.DataSource)
	}
	if in.Evaluate > 0 {
		session.Where("b.evaluate=?", in.Evaluate)
	}
	if in.DataFrom > 0 {
		session.Where("a.data_from=?", in.DataFrom)
	}

	orderBy := "a.create_time asc,a.id asc"
	if len(in.OrderBy) > 0 {
		orderBy = in.OrderBy
	}

	out = make([]PetaiMessageExd, 0)
	err = session.OrderBy(orderBy).Find(&out)
	return
}

// 根据GetMessagesReq入参获取消息列表
func (m *PetaiMessage) GetMessages(session *xorm.Session, in GetMessagesReq) (out []PetaiMessage, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if in.ConversationId == 0 {
		err = errors.New("会话id为空")
		return
	}
	session.Where("conversation_id=?", in.ConversationId)
	if len(in.UserInfoId) > 0 {
		session.Where("user_info_id=?", in.UserInfoId)
	}
	if len(in.Types) > 0 {
		session.In("type", in.Types)
	}
	if in.DataSource > 0 {
		session.Where("data_source=?", in.DataSource)
	}

	orderBy := "id asc"
	if len(in.OrderBy) > 0 {
		orderBy = in.OrderBy
	}
	limit := 10
	if in.Limit > 0 {
		limit = in.Limit
	}
	out = make([]PetaiMessage, 0)
	err = session.OrderBy(orderBy).Limit(limit).Find(&out)
	return
}
