package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// StatsSalespersonPage
// @Summary 业务员数据列表查询
// @Tags 后台接口-业务员数据
// @Accept  plain
// @Produce  json
// @Param StatsShopPageReq query distribution_vo.StatsSalespersonPageReq true " "
// @Success 200 {object} distribution_vo.StatsSalespersonPageResp
// @Failure 400 {object} distribution_vo.StatsSalespersonPageResp
// @Router /manager/stats/salesperson/page-list [GET]
func StatsSalespersonPage(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.StatsSalespersonPageResp{}
	out.Code = 400
	param, err := utils.Bind[distribution_vo.StatsSalespersonPageReq](request)
	if err != nil {
		log.Error("获取业务员统计数据-报表解析参数失败-错误为:", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	service := services.StatsSalespersonService{}
	if out.Data, out.Total, err = service.StatsSalespersonPage(param); err != nil {
		log.Errorf("获取业务员统计数据-报表失败-错误为:", err.Error())
		out.Message = "获取业务员统计数据-报表概览失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}

// StatsSalespersonExport
// @Summary 业务员数据导出
// @Tags 后台接口-业务员数据
// @Accept  plain
// @Produce  json
// @Param StatsShopPageReq query distribution_vo.StatsSalespersonPageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/stats/salesperson/export [GET]
func StatsSalespersonExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.StatsSalespersonPageReq](r)
	if err != nil {
		log.Error("导出业务员统计数据，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("导出业务员统计数据，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 14
		err := s.CreatTask(r, task)
		if err != nil {
			log.Error("导出业务员统计数据：err=" + err.Error())
			resp.Message = fmt.Sprintf("导出业务员统计数据：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
