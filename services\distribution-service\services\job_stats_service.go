package services

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"sync"
	"time"

	"xorm.io/xorm"
)

type JobStatsService struct {
	common.BaseService
}

type JobStatsServiceData struct {
	Engine         *xorm.Engine
	Data           *po.StatsShopDistributorDaily
	StartTimestamp int64
	EndTimestamp   int64
}

// 跑历史数据，生成起始每天的统计记录
func (s JobStatsService) StatsOrderDaliyDataRun(startTime, endTime string) {
	startDay, err := time.Parse(utils.DateLayout, startTime)
	if err != nil {
		return
	}
	endDay, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return
	}
	for i := startDay; i.Before(endDay) || i.Equal(endDay); i = i.AddDate(0, 0, 1) {
		// 跑一天的数据
		fmt.Println("处理日期范围-商品订单-周:", i.Format("2006-01-02"), "-", i.Format("2006-01-02"))
		s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
			StartDate: i.Format(time.DateOnly),
			EndDate:   i.Format(time.DateOnly),
		})

		// 检测是不是周末，跑一周的数据
		weekday := i.Weekday()
		if weekday == time.Sunday {
			startDate := i.AddDate(0, 0, -6)
			fmt.Println("处理日期范围-商品订单-周:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是月末，跑一月的数据
		tomorrow := i.AddDate(0, 0, 1)
		if tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(0, -1, 0)
			fmt.Println("处理日期范围-商品订单-月:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是年末，跑一年的数据
		if tomorrow.Month() == 1 && tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(-1, 0, 0)
			s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}
	}
}

func (s JobStatsDisService) StatsOrderDaliyDataRun(startTime, endTime string) {
	startDay, err := time.Parse(utils.DateLayout, startTime)
	if err != nil {
		return
	}
	endDay, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return
	}
	for i := startDay; i.Before(endDay) || i.Equal(endDay); i = i.AddDate(0, 0, 1) {
		// 跑一天的数据
		s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
			StartDate: i.Format(time.DateOnly),
			EndDate:   i.Format(time.DateOnly),
		})

		// 检测是不是周末，跑一周的数据
		weekday := i.Weekday()
		if weekday == time.Sunday {
			startDate := i.AddDate(0, 0, -6)
			s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是月末，跑一月的数据
		tomorrow := i.AddDate(0, 0, 1)
		if tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(0, -1, 0)
			s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是年末，跑一年的数据
		if tomorrow.Month() == 1 && tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(-1, 0, 0)
			s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}
	}
}

func (s *JobStatsService) StatsOrderDaliyData() {
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsOrderDataLock, "daily")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := now.AddDate(0, 0, -1)
	s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   startDate.Format("2006-01-02"),
	})
}

func (s *JobStatsService) StatsOrderWeeklyData() {
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsOrderDataLock, "weekly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := now.AddDate(0, 0, -int(now.Weekday())-6) // 减去今天是星期几，再减去6天
	endDate := startDate.AddDate(0, 0, 6)
	s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s *JobStatsService) StatsOrderMonthlyData() {
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsOrderDataLock, "monthly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	now := time.Now()
	// 获取上个月最后一天的日期
	startDate := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, -1) // 加一个月，再减去一天
	s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s *JobStatsService) StatsOrderYearlyData() {
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsOrderDataLock, "yearly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	now := time.Now()

	startDate := time.Date(now.Year()-1, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := time.Date(now.Year()-1, 12, 31, 0, 0, 0, 0, time.Local)
	s.StatsOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

// 商品订单概览
// 1 累计下单指标
// 2 累计成交指标
// 3 累计取消指标
// 4 累计退款指标
// 5 更新到StatsShopDistributorDaily表
// StatsOrderDailyData 定时任务-统计商品订单每天的数据
func (s *JobStatsService) StatsOrderDailyData(req ...vo.StatsShopDistributorDailyReq) {
	log.Infof("StatsOrderDailyData cron begin:%v", req)
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("StatsOrderDailyData panic error:%v", err)
		}
	}()
	s.Begin()
	defer s.Close()
	//日期字符串StartDate 2024-06-01 转成时间戳
	var StartDate, EndDate string
	if len(req) > 0 {
		StartDate = req[0].StartDate
		EndDate = req[0].EndDate
	}
	startTimestamp := utils.Date2Timestamp(StartDate)
	//EndDate 字符串转时间 加一天
	endTimestamp := utils.AddDate2Timestamp(EndDate)

	serv := &JobStatsServiceData{
		Engine:         s.Engine,
		Data:           new(po.StatsShopDistributorDaily),
		StartTimestamp: startTimestamp,
		EndTimestamp:   endTimestamp,
	}
	//判断表已存在
	_, err := serv.Engine.Table("eshop.stats_shop_distributor_daily").
		Where("stat_date = ? and end_date=? and type = 1 and shop_id = 0", StartDate, EndDate).Get(serv.Data)
	if err != nil {
		log.Errorf("StatsOrderDailyData 查数数据失败 error:%v", err)
	}
	wg := sync.WaitGroup{}
	wg.Add(4)
	// 累计下单指标
	go func() {
		defer wg.Done()
		serv.StatsOrderDailyData1()
	}()

	// 累计成交指标
	go func() {
		defer wg.Done()
		serv.StatsOrderDailyData2()
	}()

	// 累计取消数据
	go func() {
		defer wg.Done()
		serv.StatsOrderDailyData3()
	}()

	// 累计退款数据
	go func() {
		defer wg.Done()
		serv.StatsOrderDailyData4()
	}()

	wg.Wait()

	//公共数据
	serv.Data.StatDate = StartDate
	serv.Data.EndDate = EndDate
	serv.Data.ShopId = 0
	serv.Data.Type = 1

	if serv.Data.Id > 0 {
		serv.Data.Type = 1
		if _, err = serv.Engine.Table("eshop.stats_shop_distributor_daily").ID(serv.Data.Id).Update(serv.Data); err != nil {
			log.Errorf("StatsOrderDailyData 更新数据失败 error:%s", err.Error())
		}
	} else {
		_, err = serv.Engine.Table("eshop.stats_shop_distributor_daily").Insert(&serv.Data)
		if err != nil {
			log.Errorf("StatsOrderDailyData 添加数据失败 error:%v", err)
		}
	}
}

// StatsOrderDailyData1 累计下单指标
func (s *JobStatsServiceData) StatsOrderDailyData1() {
	var (
		OrderCustomerCount int
		OrderCount         int
		OrderAmount        float64
		OrderProductCount  int
		OrderCustomerPrice int
	)
	session := s.Engine
	_, err := session.SQL(`
SELECT COUNT(DISTINCT o.buyer_id) AS 'order_customer_count',
       COUNT(DISTINCT og.order_id) AS 'order_count',
       IFNULL((SELECT SUM(order_amount)
               FROM (SELECT DISTINCT order_id, order_amount
                     FROM upetmart.upet_orders
                     WHERE order_father = 0
                       AND store_id = 3
                       AND add_time >= ?
                       AND add_time < ?
                     AND is_dis = 0
                     ) as unique_orders), 0) AS 'order_amount',
       IFNULL(SUM(og.goods_num),0) AS 'order_product_count'
FROM upetmart.upet_order_goods og
         LEFT JOIN upetmart.upet_orders o ON og.order_id = o.order_id
WHERE o.store_id = 3
  AND og.store_id = 3
  AND o.add_time >= ?
  AND o.add_time < ?
  AND o.order_father = 0
  and o.is_dis = 0;
`, s.StartTimestamp, s.EndTimestamp, s.StartTimestamp, s.EndTimestamp).
		Get(&OrderCustomerCount, &OrderCount, &OrderAmount, &OrderProductCount)
	if err != nil {
		log.Errorf("StatsOrderDailyData 累计下单指标 error:%v", err)
	}
	//计算累计下单客单价
	if OrderCustomerCount > 0 {
		OrderCustomerPrice = int(OrderAmount / float64(OrderCustomerCount) * 100)
	}
	s.Data.OrderCustomerCount = OrderCustomerCount
	s.Data.OrderCount = OrderCount
	s.Data.OrderAmount = int(OrderAmount * 100)
	s.Data.OrderProductCount = OrderProductCount
	s.Data.OrderCustomerPrice = OrderCustomerPrice
}

// StatsOrderDailyData2 累计成交指标
func (s *JobStatsServiceData) StatsOrderDailyData2() {
	var (
		TransCustomerCount int
		TransCount         int
		TransAmount        float64
		TransProductCount  int
		TransCustomerPrice int
	)
	session := s.Engine
	_, err := session.SQL(`
select COUNT(DISTINCT o.buyer_id) AS 'trans_customer_count',
       COUNT(DISTINCT og.order_id) AS 'trans_count',
       IFNULL((SELECT SUM(order_amount)
               FROM (SELECT DISTINCT order_id, order_amount
                     FROM upetmart.upet_orders
                     WHERE order_father = 0
                       AND store_id = 3
                       AND payment_time > 0
                       AND add_time >= ?
                       AND add_time < ?
                     AND is_dis = 0
                     ) AS unique_orders), 0) AS 'trans_amount',
       IFNULL(SUM(og.goods_num),0) AS 'trans_product_count'
FROM upetmart.upet_order_goods og
         LEFT JOIN upetmart.upet_orders o ON og.order_id = o.order_id
WHERE o.store_id = 3
  AND og.store_id = 3
  AND o.add_time >= ?
  AND o.add_time < ?
  AND o.payment_time > 0
  AND o.order_father = 0
  and o.is_dis = 0;

`, s.StartTimestamp, s.EndTimestamp, s.StartTimestamp, s.EndTimestamp).
		Get(&TransCustomerCount, &TransCount, &TransAmount, &TransProductCount)
	if err != nil {
		log.Errorf("StatsOrderDailyData 累计成交指标 error:%v", err)
	}
	//计算累计下单客单价
	if TransCustomerCount > 0 {
		TransCustomerPrice = int(TransAmount / float64(TransCustomerCount) * 100)
	}
	s.Data.TransCustomerCount = TransCustomerCount
	s.Data.TransCount = TransCount
	s.Data.TransAmount = int(TransAmount * 100)
	s.Data.TransProductCount = TransProductCount
	s.Data.TransCustomerPrice = TransCustomerPrice
}

// StatsOrderDailyData3 累计取消数据
func (s *JobStatsServiceData) StatsOrderDailyData3() {
	var (
		CancelCount        int
		CancelAmount       float64
		CancelProductCount int
	)
	session := s.Engine
	_, err := session.SQL(`
select COUNT(DISTINCT o.order_id) AS cancel_count,
       IFNULL((SELECT SUM(order_amount)
               FROM (SELECT DISTINCT order_id, order_amount
                     FROM upetmart.upet_orders
                     WHERE store_id = 3
                       AND payment_time = 0
                       and order_father = 0
                       and order_state = 0
                       AND add_time >= ?
                       AND add_time < ?
                       ) AS unique_orders), 0) AS 'cancel_amount',
       IFNULL(SUM(og.goods_num),0) AS cancel_product_count
from upetmart.upet_orders o
         left join upetmart.upet_order_goods og on o.order_id = og.order_id
where o.store_id = 3
  and o.add_time >= ?
  and o.add_time < ?
  and o.order_state = 0
  and o.payment_time = 0
  and o.order_father = 0;
`, s.StartTimestamp, s.EndTimestamp, s.StartTimestamp, s.EndTimestamp).
		Get(&CancelCount, &CancelAmount, &CancelProductCount)
	if err != nil {
		log.Errorf("StatsOrderDailyData 累计取消数据 error:%v", err)
	}
	s.Data.CancelCount = CancelCount
	s.Data.CancelAmount = int(CancelAmount * 100)
	s.Data.CancelProductCount = CancelProductCount
}

// StatsOrderDailyData4 累计退款数据
func (s *JobStatsServiceData) StatsOrderDailyData4() {
	var (
		RefundCount        int
		RefundAmount       float64
		RefundProductCount int
	)
	session := s.Engine
	_, err := session.SQL(`
SELECT count(DISTINCT r.order_id) AS refund_count,
       ifnull(SUM(r.refund_amount),0) AS refund_product_count
FROM upetmart.upet_refund_return r
WHERE r.store_id = 3
  AND r.refund_state = 3
  AND r.seller_state = 2
  AND r.admin_time >= ?
  AND r.admin_time < ?
`, s.StartTimestamp, s.EndTimestamp).
		Get(&RefundCount, &RefundAmount)
	if err != nil {
		log.Errorf("StatsOrderDailyData 累计退款数据 error:%v", err)
	}

	// 退款商品数
	_, err = session.SQL(`
SELECT 
    ifnull(SUM(refund_product_count),0) AS refund_product_count
FROM (SELECT SUM(r.goods_num) AS refund_product_count
      FROM upetmart.upet_refund_return r
      WHERE r.store_id = 3
        AND r.refund_state = 3
        AND r.seller_state = 2
        AND r.order_goods_id > 0
        AND r.admin_time >= ?
        AND r.admin_time < ?
      UNION ALL
      SELECT 
          ifnull(SUM(goods_num),0) AS refund_product_count
      FROM upetmart.upet_order_goods
      WHERE order_id IN (SELECT DISTINCT r.order_id
         FROM upetmart.upet_refund_return r
              LEFT JOIN upetmart.upet_order_goods og ON r.order_id = og.order_id
         WHERE r.store_id = 3
           AND r.order_goods_id = 0
           AND r.refund_state = 3
           AND r.seller_state = 2
           AND r.admin_time >= ?
           AND r.admin_time < ?
         )
) AS combined_refund_counts;
`, s.StartTimestamp, s.EndTimestamp, s.StartTimestamp, s.EndTimestamp).
		Get(&RefundProductCount)
	if err != nil {
		log.Errorf("StatsOrderDailyData 累计退款商品数据 error:%v", err)
	}
	s.Data.RefundCount = RefundCount
	s.Data.RefundAmount = int(RefundAmount * 100)
	s.Data.RefundProductCount = RefundProductCount
}
