package services

import (
	po "eShop/domain/points-po"
	"eShop/infra/errors"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"
	"time"

	"xorm.io/xorm"
	// "eShop/infra/log"
	// "xorm.io/xorm"
	// "time"
)

// ClsPointsDailyStatsService 提供了积分每日统计相关的服务
type ClsPointsDailyStatsService struct {
	SuperService[int, po.ClsPointsDailyStats, vo.ClsPointsDailyStatsSaveVO, vo.ClsPointsDailyStatsUpdateVO, vo.ClsPointsDailyStatsQueryVO, vo.ClsPointsDailyStatsResultVO]
	repo repo.ClsPointsDailyStatsRepo
}

// NewClsPointsDailyStatsService 创建一个新的 ClsPointsDailyStatsService 实例
func NewClsPointsDailyStatsService() ClsPointsDailyStatsService {
	return ClsPointsDailyStatsService{
		NewSuperService(
			&ClsPointsDailyStatsHooks{
				NewServiceHooks[int, po.ClsPointsDailyStats, vo.ClsPointsDailyStatsSaveVO, vo.ClsPointsDailyStatsUpdateVO, vo.ClsPointsDailyStatsQueryVO, vo.ClsPointsDailyStatsResultVO](),
			},
		),
		repo.NewClsPointsDailyStatsRepo(),
	}
}

// GetStats 获取积分统计数据
func (s ClsPointsDailyStatsService) GetStats(session *xorm.Session) (vo.ClsPointsStatVO, error) {
	var result vo.ClsPointsStatVO

	// 获取今天的日期
	today := time.Now().Format("2006-01-02")

	// 获取今日统计数据
	var todayStats po.ClsPointsDailyStats

	// 获取累计数据
	s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		result, err = s.repo.StatAll(tx)
		if err != nil {
			return errors.NewBadRequest("查询累计统计数据失败: " + err.Error())
		}

		// 查询今日数据
		todayStats, err = s.repo.QueryOne(tx, vo.ClsPointsDailyStatsQueryVO{
			StatDate: today,
		})
		if err != nil {
			return errors.NewBadRequest("查询今日统计数据失败: " + err.Error())
		}
		return nil
	})

	// 构建返回数据
	result.TodayIssuePoints = todayStats.IssueTotal
	result.TodayConsumePoints = todayStats.ConsumeTotal

	return result, nil
}

func (s ClsPointsDailyStatsService) StatSalesIn(session *xorm.Session, statDate string, flows []po.ClsPointsFlow) error {
	// 初始化统计数据
	var totalIssuePoints, totalIssueBlkyPoints, totalIssueSzldPoints int
	var totalConsumePoints, totalConsumeBlkyPoints, totalConsumeSzldPoints int

	// 计算总积分
	for _, flow := range flows {
		if flow.Type == 1 && flow.BizType != 6 {
			totalIssuePoints += flow.Points
			totalIssueBlkyPoints += flow.PointsBlky
			totalIssueSzldPoints += flow.PointsSzld
		}

		if flow.BizType == 3 {
			totalConsumePoints += flow.Points
			totalConsumeBlkyPoints += flow.PointsBlky
			totalConsumeSzldPoints += flow.PointsSzld
		}
		if flow.BizType == 4 {
			totalConsumePoints += flow.Points
			totalConsumeBlkyPoints -= flow.PointsBlky
			totalConsumeSzldPoints -= flow.PointsSzld
		}
	}

	statTime, err := time.ParseInLocation("2006-01-02", statDate, time.Local)
	if err != nil {
		return errors.NewBadRequest("统计日期格式错误：" + err.Error())
	}

	// 调用 repo 层方法更新或插入统计数据
	return s.repo.SaveOrUpdate(session, po.ClsPointsDailyStats{
		StatDate:     statTime,
		IssueTotal:   totalIssuePoints,
		IssueBlky:    totalIssueBlkyPoints,
		IssueSzld:    totalIssueSzldPoints,
		ConsumeTotal: totalConsumePoints,
		ConsumeBlky:  totalConsumeBlkyPoints,
		ConsumeSzld:  totalConsumeSzldPoints,
	})
}

// ClsPointsDailyStatsHooks 实现了针对 ClsPointsDailyStats 的特定钩子
type ClsPointsDailyStatsHooks struct {
	ServiceHooks[int, po.ClsPointsDailyStats, vo.ClsPointsDailyStatsSaveVO, vo.ClsPointsDailyStatsUpdateVO, vo.ClsPointsDailyStatsQueryVO, vo.ClsPointsDailyStatsResultVO]
}

// func (h ClsPointsDailyStatsHooks) BeforeCreate(session xorm.Session, saveVO vo.ClsPointsDailyStatsSaveVO) (vo.ClsPointsDailyStatsSaveVO, error) {
// 	log.Info("ClsPointsDailyStatsHooks: BeforeCreate hook triggered")
// 	return saveVO, nil
// }

// func (h ClsPointsDailyStatsHooks) BeforeUpdate(session xorm.Session, updateVO vo.ClsPointsDailyStatsUpdateVO) (vo.ClsPointsDailyStatsUpdateVO, error) {
// 	log.Info("ClsPointsDailyStatsHooks: BeforeUpdate hook triggered for ID:", updateVO.Id)
// 	return updateVO, nil
// }
