package async

import (
	"context"
	"eShop/domain/omnibus-po"
	"eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/proto"
	"eShop/services/common"
	"eShop/services/external-service/services"
	pro_category "eShop/services/product-service/enum/pro-category"
	"eShop/view-model"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

type CategoryAsync struct {
	common.BaseService
}

// parJson  异步任务的参数JSON数据
func (c CategoryAsync) OperationFunc(parJson string, org_id int) (*viewmodel.ImportResult, error) {
	logPrefix := fmt.Sprintf("异步任务-同步分类数据：%s ", utils.InterfaceToJSON(parJson))
	log.Info(logPrefix)
	out := new(viewmodel.ImportResult)
	//200 表示处理正常，会确认MQ，
	out.Code = 200
	//错误数量 有需要就写，比如批量操作100个商品，有成功有失败，就需要设置这个
	out.FailNum = 0
	out.SuccessNum = 0

	var errorList []vo.SyncCategoryToThirdParamsSesult
	var params []vo.CategorySync
	err := json.Unmarshal([]byte(parJson), &params)
	if err != nil {
		return out, errors.New(logPrefix + "解析json参数错误")
	}

	if len(params) > 0 {
		c.Begin()
		defer c.Close()

		session := c.Session

		var delResCode int
		var storeChannelList []vo.StoreChannel
		var categoryAsyncNum = map[string]int{
			"elmSuccessNum": 0,
			"elmErrorNum":   0,
			"mtSuccessNum":  0,
			"mtErrorNum":    0,
		}

		for _, v := range params {
			if v.SyncType == pro_category.SyncCategoryAll { //单个门店同步所有分类
				//查询单个门店下的门店信息
				if err = session.Table("datacenter.store").Alias("s").
					Join("inner", "datacenter.store_relation sr", "sr.finance_code = s.finance_code and sr.channel_store_id <> ''").
					Select("s.app_channel,sr.channel_store_id,sr.channel_id,s.name store_name,s.finance_code,s.chain_id").
					Where("s.finance_code = ? and sr.channel_id in (2,3)", v.StoreId).Find(&storeChannelList); err != nil {
					log.Error(logPrefix + "查询连锁下的门店失败,错误信息：" + err.Error())
					return out, errors.New(logPrefix + "查询连锁下的门店失败,错误信息：" + err.Error())
				}

				for _, storeChanne := range storeChannelList {
					log.Info(logPrefix+"美团饿了么帐号信息：", storeChanne)
					//取出所有没有同步的分类， 再同步
					var proCategoryList []*product_po.ProCategory
					if err = session.Table("eshop.pro_category").Alias("pc").
						Join("left", "eshop.pro_store_front_category psfc",
							"psfc.cate_name = pc.name AND psfc.store_id = ? AND psfc.channel_id = ?",
							storeChanne.FinanceCode, storeChanne.ChannelId).
						Where("pc.type = 1 AND pc.chain_id = ? AND (psfc.id IS NULL OR psfc.status != 1)",
							v.ChainId).
						OrderBy("pc.id").
						Find(&proCategoryList); err != nil {
						log.Error(logPrefix + "查询未同步的分类失败,错误信息：" + err.Error())
						return out, errors.New(logPrefix + "查询未同步的分类失败,错误信息：" + err.Error())
					}

					for _, proCategory := range proCategoryList {
						categorySyncData := vo.CategorySync{
							SyncType:     pro_category.SyncCategoryAdd,
							ChainId:      cast.ToInt64(storeChanne.ChannelId),
							StoreId:      v.StoreId,
							CategoryId:   cast.ToString(proCategory.Id),
							CategoryName: proCategory.Name,
							ParentId:     proCategory.ParentId,
							Sort:         proCategory.Sort,
						}
						if storeChanne.ChannelId == common.ChannelIdELM { //饿了么
							if delResCode, err = c.SyncChannelCategoryToELM(storeChanne, categorySyncData); err != nil {
								log.Error(logPrefix + "同步到饿了么失败,错误信息：" + err.Error())
							}
						} else if storeChanne.ChannelId == common.ChannelIdMT { //美团
							if delResCode, err = c.SyncChannelCategoryToMt(storeChanne, categorySyncData); err != nil {
								log.Error(logPrefix + "同步到美团失败,错误信息：" + err.Error())
							}
						}

						if err != nil {
							//将错误信息写入到errorList，再导出
							errorList = c.ChannelCategoryErrorList(errorList, storeChanne, categorySyncData, err.Error())
						}
						//计算成功和失败数量
						categoryAsyncNum = c.CountCategoryAsyncNum(categoryAsyncNum, delResCode, storeChanne.ChannelId)
					}
				}

			} else { //单个分类同步到所有门店
				storeIds := []string{v.StoreId}
				//判断是否有代运营的门店
				if v.RoleType > 0 {
					storeIds, err = new(omnibus_po.Store).GetStoreIdsByUserId(context.Background(), c.Engine, v.UserId)
					if err != nil {
						log.Error(logPrefix + "获取门店列表失败,错误信息：" + err.Error())
						return out, errors.New(logPrefix + "获取门店列表失败,错误信息：" + err.Error())
					}
				}

				//查询连锁下面的所有门店
				if err = session.Table("datacenter.store").Alias("s").
					Join("inner", "datacenter.store_relation sr", "sr.finance_code = s.finance_code and sr.channel_store_id <> ''").
					Select("s.app_channel,sr.channel_store_id,sr.channel_id,s.name store_name,s.finance_code").
					In("s.finance_code", storeIds).
					Where("sr.channel_id in (2,3)").Find(&storeChannelList); err != nil {
					log.Error(logPrefix + "查询连锁下的门店失败,错误信息：" + err.Error())
					return out, errors.New(logPrefix + "查询连锁下的门店失败,错误信息：" + err.Error())
				}

				for _, storeChanne := range storeChannelList {
					if storeChanne.ChannelId == common.ChannelIdELM { //饿了么
						if delResCode, err = c.SyncChannelCategoryToELM(storeChanne, v); err != nil {
							log.Error(logPrefix + "同步到饿了么失败,错误信息：" + err.Error())
						}
					} else if storeChanne.ChannelId == common.ChannelIdMT { //美团
						if delResCode, err = c.SyncChannelCategoryToMt(storeChanne, v); err != nil {
							log.Error(logPrefix + "同步到美团失败,错误信息：" + err.Error())
						}
					}

					if err != nil {
						//将错误信息写入到errorList，再导出
						errorList = c.ChannelCategoryErrorList(errorList, storeChanne, v, err.Error())
					}
					//计算成功和失败数量
					categoryAsyncNum = c.CountCategoryAsyncNum(categoryAsyncNum, delResCode, storeChanne.ChannelId)
				}
			}

		}
		out.Message = fmt.Sprintf("饿了么渠道,成功数量：%d,失败数量：%d,美团渠道，成功数量：%d,失败数量：%d", categoryAsyncNum["elmSuccessNum"], categoryAsyncNum["elmErrorNum"], categoryAsyncNum["mtSuccessNum"], categoryAsyncNum["mtErrorNum"])
	}

	//如果有需上传的文件，自己处理上传后，把URL写入到下面这个参数
	if len(errorList) > 0 {
		out.QiniuUrl, _ = c.ExportToExcel(errorList)
		log.Error(logPrefix + "同步分类失败,错误信息：" + utils.JsonEncode(errorList))
	} else {
		log.Error(logPrefix + "同步分类成功")
	}
	//错误的时候返回错误信息，成功的时候可以返回成功的一些内容，比如 （美团 成功100个商品 失败5个商品，饿了么成功80个 失败100个）  ，内容不超过1000个字符串

	out.Code = 200
	return out, nil
}

// 错误列表,导出
func (c CategoryAsync) ChannelCategoryErrorList(errorList []vo.SyncCategoryToThirdParamsSesult, storeChanne vo.StoreChannel, categorySyncData vo.CategorySync, errStr string) []vo.SyncCategoryToThirdParamsSesult {
	errorList = append(errorList, vo.SyncCategoryToThirdParamsSesult{
		CategoryId:     cast.ToInt(categorySyncData.CategoryId),
		CategoryName:   categorySyncData.CategoryName,
		FinanceCode:    storeChanne.FinanceCode,
		ShopName:       storeChanne.StoreName,
		IsSuccess:      false,
		Message:        errStr,
		ChannelId:      storeChanne.ChannelId,
		SyncType:       categorySyncData.SyncType,
		ChannelStoreId: storeChanne.ChannelStoreId,
		AppChannel:     int32(storeChanne.AppChannel),
	})

	return errorList
}

// 计算成功和失败数量
func (c CategoryAsync) CountCategoryAsyncNum(categoryAsyncNum map[string]int, delResCode int, channelId int) map[string]int {
	if channelId == common.ChannelIdELM { //饿了么
		//计算成功和失败数量
		if delResCode == 200 {
			categoryAsyncNum["elmSuccessNum"]++
		} else {
			categoryAsyncNum["elmErrorNum"]++
		}
	} else if channelId == common.ChannelIdMT { //美团
		if delResCode == 200 {
			categoryAsyncNum["mtSuccessNum"]++
		} else {
			categoryAsyncNum["mtErrorNum"]++
		}
	}

	return categoryAsyncNum
}

// 导出到线上
// fileUrl 线上url
// desc 说明
func (c CategoryAsync) ExportToExcel(errorList []vo.SyncCategoryToThirdParamsSesult) (fileUrl, desc string) {
	var excelRow [][]string
	// excel文件头
	excelRow = append(excelRow, []string{"任务类型", "渠道", "操作分类", "财务编码", "门店名称", "错误信息"})
	var successCount int
	for _, v := range errorList {
		if v.IsSuccess == false {

			syncTypeValue := ""
			if v.SyncType == pro_category.SyncCategoryAdd {
				syncTypeValue = "新增分类"
			} else if v.SyncType == pro_category.SyncCategoryUpdate {
				syncTypeValue = "编辑分类"
			} else if v.SyncType == pro_category.SyncCategoryDelete {
				syncTypeValue = "删除分类"
			}
			channelValue := ""
			if v.ChannelId == common.ChannelIdWeChatApp {
				channelValue = "微信小程序"
			} else if v.ChannelId == common.ChannelIdMT {
				channelValue = "美团渠道"
			} else if v.ChannelId == common.ChannelIdELM {
				channelValue = "饿了么渠道"
			} else if v.ChannelId == common.ChannelIdJD {
				channelValue = "京东到家渠道"
			}

			excelRow = append(excelRow, []string{syncTypeValue, channelValue, v.CategoryName, v.FinanceCode, v.ShopName, v.Message})
		} else {
			successCount++
		}
	}
	if len(errorList) == 0 {
		return
	}
	if successCount == len(errorList) {
		desc = "成功"
	} else {

		url, err := utils.ExportProductErr(excelRow)
		if err != nil {
			log.Error("params-CategorySync-Export,err:", err, utils.JsonEncode(excelRow))
		} else {
			fileUrl = url
		}

		if successCount == 0 {
			desc = "失败"
		} else {
			desc = fmt.Sprintf("部分成功(共:%d,成功:%d,失败:%d)", len(errorList), successCount, len(errorList)-successCount)
		}
	}

	return
}

// 同步到美团
func (c *CategoryAsync) SyncChannelCategoryToMt(storeChanne vo.StoreChannel, v vo.CategorySync) (code int, err error) {
	code = 400
	frontCategory := &product_po.ProStoreFrontCategory{
		StoreId:   storeChanne.FinanceCode,
		CateName:  v.CategoryName,
		ChannelId: common.ChannelIdMT,
		Status:    0,
		SyncError: "",
	}
	//删除分类
	if v.SyncType == pro_category.SyncCategoryDelete {
		request := &proto.MtRetailCatDeleteRequest{
			AppPoiCode: storeChanne.ChannelStoreId,
			//AppPoiCode:          "4889_21626516",
			CategoryCode:        "",
			CategoryName:        v.CategoryName,
			StoreMasterId:       int32(storeChanne.AppChannel),
			MoveProductToUncate: 0,
		}

		delRes, _ := new(services.MtProductService).MtRetailCatDelete(request)
		if delRes.Code == 200 {
			//调第三方接口成功，删除本地数据库记录
			if err = c.DelCategoryThird(common.ChannelIdMT, storeChanne.ChannelStoreId, cast.ToInt(v.CategoryId)); err != nil {
				return code, errors.New("删除分类与第三方分类id关联关系失败,错误信息：" + err.Error())
			}
			frontCategory.Status = 1 // 同步成功
			_, err = c.Session.Insert(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
		} else {
			frontCategory.SyncError = "删除分类失败:" + delRes.Message
			_, err = c.Session.Insert(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
			return code, errors.New("删除分类失败,错误信息：" + delRes.Message)
		}

	} else if v.SyncType == pro_category.SyncCategoryAdd || v.SyncType == pro_category.SyncCategoryUpdate { //添加、修改分类
		request := &proto.RetailCatUpdateRequest{
			AppPoiCode:            storeChanne.ChannelStoreId,
			CategoryCodeOrigin:    "",
			CategoryNameOrigin:    "",
			CategoryCode:          "",
			CategoryName:          "",
			SecondaryCategoryCode: "",
			SecondaryCategoryName: "",
			Sequence:              0,
			TargetLevel:           0,
			TargetParentName:      "",
			TopFlag:               0,
			WeeksTime:             "",
			Period:                "",
			StoreMasterId:         int32(storeChanne.AppChannel),
		}

		if v.SyncType == pro_category.SyncCategoryAdd {
			if v.ParentId == 0 { //一级分类
				request.CategoryName = v.CategoryName
				request.CategoryCode = v.CategoryId // 将code也同步过去
				request.Sequence = int32(v.Sort)
			} else { // 二级分类
				var parentCategory product_po.ProCategory
				if _, err = c.Session.Table("eshop.pro_category").Where("id = ?", v.ParentId).Get(&parentCategory); err != nil {
					return code, errors.New("查询父级分类失败,错误信息：" + err.Error())
				}
				request.CategoryName = parentCategory.Name
				request.CategoryCode = cast.ToString(parentCategory.Id)
				request.CategoryCodeOrigin = cast.ToString(parentCategory.Id)
				request.SecondaryCategoryName = v.CategoryName
				request.SecondaryCategoryCode = v.CategoryId
				request.Sequence = int32(v.Sort)

			}
		} else if v.SyncType == pro_category.SyncCategoryUpdate {
			request.CategoryName = v.CategoryName
			request.CategoryCode = cast.ToString(v.CategoryId)
			request.Sequence = cast.ToInt32(v.Sort)
			request.CategoryCodeOrigin = cast.ToString(v.CategoryId) // 修改的原始分类的id
		}

		delRes, _ := new(services.MtProductService).RetailCatUpdate(request)

		if delRes.Code == 200 && v.SyncType == pro_category.SyncCategoryAdd {
			//成功则将第三方分类id写入到数据库
			if err = c.insertCategoryThird(storeChanne.ChannelId, storeChanne.ChannelStoreId, v.CategoryId, cast.ToInt(v.CategoryId)); err != nil {
				return code, errors.New("保存分类与第三方分类id关联关系失败,错误信息：" + err.Error())
			}
			frontCategory.Status = 1 // 同步成功
			_, err = c.Session.Insert(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
		} else if delRes.Code != 200 {
			msg := ""
			if delRes.Error != nil {
				msg = delRes.Error.Msg
			} else {
				msg = delRes.Message
			}
			frontCategory.SyncError = "添加/编辑分类失败:" + msg
			_, err = c.Session.Where("store_id = ? and cate_name = ? and channel_id = ?", storeChanne.FinanceCode, v.CategoryName, common.ChannelIdMT).Update(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
			return code, errors.New("添加、编辑分类失败,错误信息：" + msg)
		}
	}

	return 200, nil
}

// 同步到饿了么
func (c *CategoryAsync) SyncChannelCategoryToELM(storeChanne vo.StoreChannel, v vo.CategorySync) (code int, err error) {
	code = 400
	frontCategory := &product_po.ProStoreFrontCategory{
		StoreId:   storeChanne.FinanceCode,
		CateName:  v.CategoryName,
		ChannelId: common.ChannelIdELM,
		Status:    0,
		SyncError: "",
	}
	//删除分类
	if v.SyncType == pro_category.SyncCategoryDelete {
		request := &vo.DelElmShopCategoryRequest{
			ShopId:       storeChanne.ChannelStoreId,
			CategoryId:   "",
			CategoryName: v.CategoryName,
			AppChannel:   int32(storeChanne.AppChannel),
		}

		delRes, _ := new(services.ElmProductService).DelElmShopCategory(request)
		if delRes.Code == 200 {
			//调第三方接口成功，删除本地数据库记录
			if err = c.DelCategoryThird(common.ChannelIdELM, storeChanne.ChannelStoreId, cast.ToInt(v.CategoryId)); err != nil {
				return code, errors.New("删除分类与第三方分类id关联关系失败,错误信息：" + err.Error())
			}
			frontCategory.Status = 1 // 同步成功
			_, err = c.Session.Where("store_id = ? and cate_name = ? and channel_id = ?", storeChanne.FinanceCode, v.CategoryName, common.ChannelIdELM).Update(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
		} else {
			frontCategory.SyncError = "删除分类失败:" + delRes.Error
			_, err = c.Session.Where("store_id = ? and cate_name = ? and channel_id = ?", storeChanne.FinanceCode, v.CategoryName, common.ChannelIdELM).Update(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
			return code, errors.New("删除分类失败,错误信息：" + delRes.Error)
		}

	} else if v.SyncType == pro_category.SyncCategoryAdd || v.SyncType == pro_category.SyncCategoryUpdate { //添加、修改分类
		request := &vo.NewElmShopCategoryRequest{
			CategoryId:         "",
			ShopCustomId:       v.CategoryId,
			ShopCustomParentId: cast.ToString(v.ParentId),
			ShopId:             storeChanne.ChannelStoreId,
			ParentCategoryId:   "",
			Name:               v.CategoryName,
			Rank:               cast.ToString(v.Sort),
			AppChannel:         int32(storeChanne.AppChannel),
		}

		delRes, _ := new(services.ElmProductService).NewElmShopCategory(request, v.SyncType)
		if delRes.Code == 200 && v.SyncType == pro_category.SyncCategoryAdd {
			//成功则将第三方分类id写入到数据库
			if err = c.insertCategoryThird(storeChanne.ChannelId, storeChanne.ChannelStoreId, v.CategoryId, cast.ToInt(v.CategoryId)); err != nil {
				return code, errors.New("保存分类与第三方分类id关联关系失败,错误信息：" + err.Error())
			}
			frontCategory.Status = 1 // 同步成功
			_, err = c.Session.Insert(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
		} else if delRes.Code != 200 {
			frontCategory.SyncError = "添加、编辑分类失败:" + delRes.Error
			_, err = c.Session.Where("store_id = ? and cate_name = ? and channel_id = ?", storeChanne.FinanceCode, v.CategoryName, common.ChannelIdELM).Update(frontCategory)
			if err != nil {
				return code, errors.New("保存同步状态失败,错误信息：" + err.Error())
			}
			return code, errors.New("添加、编辑分类失败,错误信息：" + delRes.Error)
		}
	}

	return 200, nil
}

// 保存到分类与第三方分类id关联关系
func (c *CategoryAsync) DelCategoryThird(channelId int, channelStoreId string, originalCategoryId int) error {
	_, err := c.Session.Table("pro_category_store_thirdid").Where("channel_id = ? and channel_store_id = ? and original_category_id = ?", channelId, channelStoreId, originalCategoryId).Delete(&product_po.ProCategoryStoreThirdid{})
	return err
}

// 保存到分类与第三方分类id关联关系
func (c *CategoryAsync) insertCategoryThird(channelId int, channelStoreId string, categoryId string, originalCategoryId int) error {
	_, err := c.Session.Insert(&product_po.ProCategoryStoreThirdid{
		ChannelId:          channelId,
		ChannelStoreId:     channelStoreId,
		CategoryId:         categoryId,
		OriginalCategoryId: originalCategoryId,
		CreateDate:         time.Time{},
		UpdateDate:         time.Time{},
	})

	return err
}

// 获取第三方分类id
func (c CategoryAsync) GetCategoryThirdid(channelId int, channelStoreId string, originalCategoryId string) (categoryId string, err error) {
	category := &product_po.ProCategoryStoreThirdid{}
	_, err = c.Session.Where("channel_id = ?", channelId).And("channel_store_id = ?", channelStoreId).And("original_category_id = ?", originalCategoryId).Get(category)
	if err != nil {
		return "", err
	}

	if category.CategoryId == "" {
		return "", errors.New("数据错误，查询不到对应的第三方分类id,渠道ID：" + channelStoreId + ",分类ID：" + cast.ToString(category.OriginalCategoryId))
	}

	return category.CategoryId, nil
}
