package main

import (
	"context"
	"eShop/applications/omnibus-app/controllers/api"
	"eShop/applications/omnibus-app/controllers/manager"
	_ "eShop/docs"
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	emiddware "eShop/infra/middleware"
	"eShop/infra/security"
	"eShop/infra/tracing"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/services/omnibus-service/services"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/BurntSushi/toml"

	"github.com/go-chi/chi/v5"
	httpSwagger "github.com/swaggo/http-swagger"
)

func service() http.Handler {
	//接下来才是正题
	r := chi.NewRouter()

	r.Use(emiddware.WithLogger)  //用中间件去处理日志问题。未完待续
	r.Use(emiddware.WithTracing) //基于jaeger的open tracing

	// jwtAuth
	jwtauth.JwtInit()
	r.Use(jwtauth.OfflineVerify(jwtauth.OfflineTokenFromHeader))
	r.Use(emiddware.SetOrgId) //给所有后台请求，添加主体标识org_id
	api.InitAllApi(r)
	manager.InitAllManager(r)

	// 短信配置
	service := services.SmsService{}
	smsHandle := manager.NewSmsController(service)
	smsHandle.RegisterRoutes(r)

	// 提成配置
	commissionService := services.CommissionService{}
	commissionHandle := manager.NewCommissionController(commissionService)
	commissionHandle.RegisterRoutes(r)

	r.Mount("/swagger", httpSwagger.WrapHandler)
	return r
}

func main() {
	toml.DecodeFile("appsetting.toml", &config.LocalSetting)
	config.LocalSetting.LocalIP = config.GetCurrentIP() //获取本机地址
	security.InitPEM("rsa_1024_priv.pem", "rsa_1024_pub.pem", "rsa_1024_pub.pem")
	// 设置时区
	time.Local, _ = time.LoadLocation("Asia/Shanghai")
	//临时注释掉
	//config.InitConfig(fmt.Sprintf("http://%s", config.LocalSetting.AppService.Address), config.LocalSetting.AppService.Appid, env.GetEnv())

	cache.CacheSources[cache_source.EShop] = cache.Address(config.Get("redis.PassAddr"))

	log.Init()

	fmt.Println("omnibus-app 启动成功! 端口 8151")

	_, closer, err := tracing.InitJaeger()
	if err != nil {
		panic(err)
	}

	//下面的代码主要是平滑关闭，
	//也就是 监听类似于 ctrl + c 等中断信号再关闭程序的处理。

	// The HTTP Server
	server := &http.Server{Addr: "0.0.0.0:8151", Handler: service()}

	// Server run context
	serverCtx, serverStopCtx := context.WithCancel(context.Background())

	// Listen for syscall signals for process to interrupt/quit
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	go func() {
		<-sig

		// Shutdown signal with grace period of 30 seconds
		shutdownCtx, _ := context.WithTimeout(serverCtx, 30*time.Second)

		go func() {
			<-shutdownCtx.Done()
			closer.Close()

			if shutdownCtx.Err() == context.DeadlineExceeded {
				log.Fatal("graceful shutdown timed out.. forcing exit.")
			}
		}()

		// Trigger graceful shutdown
		err := server.Shutdown(shutdownCtx)
		if err != nil {
			log.Fatal(err)
		}
		serverStopCtx()
	}()

	//初始化
	utils.InitClient()

	// Run the server
	err = server.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		log.Fatal(err)
	}

	// Wait for server context to be stopped
	<-serverCtx.Done()
}
