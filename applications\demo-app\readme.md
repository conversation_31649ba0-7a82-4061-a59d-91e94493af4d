# Demo App 示例项目规范

本文档用于指导开发团队在开发 Demo App 微服务时遵循统一的规范和最佳实践。

## 架构模式

### 1. CQRS模式
Demo App 采用简化版的 CQRS 模式：
- Command 模型：CreateCommand、TransferCommand、WithdrawCommand、DepositCommand
- Query 模型：QueryParams、QueryResult
- 虽然命令和查询在同一个服务中实现，但模型被清晰地分离

### 2. 事件溯源 (Event Sourcing)
- 所有状态变更都通过事件记录
- 使用 EventStore 持久化事件
- 通过 RabbitMQ 发布/订阅事件
- 支持基于事件的状态重建

## 项目架构

项目采用领域驱动设计(DDD)的分层架构：

```
/eshop
├── domain/                # 领域层：存放DDD的领域实体和持久化代码
│   └── demo-po/          # Demo服务的持久化对象
│       └── account/      # 账户领域模型
├── infra/                # 基础设施层：与业务无关的基础代码
│   ├── cache/           # 缓存
│   ├── config/          # 配置
│   ├── events/          # 事件存储和发布
│   ├── jwtauth/         # JWT认证
│   ├── middleware/      # 中间件
│   └── utils/          # 工具类
├── services/            # 服务层：存放业务逻辑实现
│   └── demo-service/    # Demo服务的业务逻辑
│       └── account/     # 账户服务实现（CQRS）
└── applications/        # 应用层：微服务入口
    └── demo-app/        # Demo应用
        ├── controllers/ # 控制器
        └── main.go      # 主程序
```

## 技术栈规范

### 后端框架和组件
- Go 1.21
- Chi - Web框架
- XORM - ORM框架和事件存储
- MySQL - 数据库
- RabbitMQ - 事件发布订阅
- Redis - 缓存
- Jaeger - 分布式追踪

## 设计模式实现

### 1. CQRS 实现
```go
// Command 模型
type CreateCommand struct {
    ID      string
    Balance float64
}

// Query 模型
type QueryParams struct {
    Status     string
    StartTime  int64
    PageNumber int
    PageSize   int
}
```

### 2. 事件溯源实现
```go
// 事件定义
type Event interface {
    GetEventType() string
    GetAggregateID() string
    GetTimestamp() time.Time
    GetVersion() int
}

// 事件存储
type EventStore interface {
    SaveEvent(event Event) error
    GetEvents(aggregateID string) ([]Event, error)
}
```

### 3. 领域事件
- AccountCreatedEvent：账户创建事件
- AccountTransferredEvent：转账事件
- AccountWithdrawnEvent：提现事件
- AccountDepositedEvent：存款事件

## API 设计规范

### Command API
- POST /demo-app/api/create - 创建账户
- POST /demo-app/api/withdraw - 提现
- POST /demo-app/api/deposit - 存款
- POST /demo-app/api/transfer - 转账

### Query API
- GET /demo-app/manager/query - 查询账户列表

## 事务处理

### 事务一致性
- 使用 XORM Session 管理事务
- 事务中保存领域事件
- 提交事务后发布事件
- 失败时自动回滚

### 并发控制
- 使用事件版本控制
- 乐观锁防止并发冲突
- 分布式锁保护关键操作

## 开发规范

### 代码组织
1. 按领域模块组织代码
2. 分离命令和查询模型
3. 保持层次间的清晰边界
4. 通过事件处理状态变更

### 命名规范
- Command 类型以 Command 结尾
- Event 类型以 Event 结尾
- Query 相关类型表明查询意图

### 测试规范
- 命令处理器的单元测试
- 查询处理器的单元测试
- 事件处理器的集成测试
- 端到端的 API 测试

## 部署规范

### 配置管理
- 使用 appsetting.toml 配置
- 环境变量覆盖
- 敏感信息加密

### 事件存储
- 使用 MySQL 存储事件
- 定期归档历史事件
- 支持事件重放功能

### 监控规范
- 使用 Jaeger 进行链路追踪
- 监控事件处理延迟
- 记录命令处理时间
- 异常监控和告警

## 安全规范

### 认证授权
- JWT token 认证
- 基于角色的访问控制
- 请求签名验证

### 数据安全
- 敏感数据加密
- SQL 注入防护
- XSS 防护

## 性能优化

### 查询优化
- 独立的查询模型
- 合理使用索引
- 结果集分页

### 事件处理
- 异步事件处理
- 批量事件处理
- 事件重放优化

### 缓存策略
- 查询结果缓存
- 缓存更新机制
- 防止缓存穿透