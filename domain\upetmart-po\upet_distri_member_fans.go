package upetmart_po

type UpetDistriMemberFans struct {
	DisFansId   int `xorm:"not null pk autoincr INT(10)"`
	DisMemberId int `xorm:"default 0 comment('分销员id') INT(10)"`
	MemberId    int `xorm:"default 0 comment('member') INT(10)"`
	State       int `xorm:"default 1 comment('1正常绑定粉丝，0已失效粉丝,2待确认') TINYINT(3)"`
	CreateTime  int `xorm:"default 0 comment('创建时间') INT(11)"`
	UpdateTime  int `xorm:"default 0 comment('跟新时间') INT(11)"`
	DisType     int `xorm:"default 0 comment('1分享连接2，扫码') TINYINT(2)"`
}

// DisMemberInfo
// getDisMemberInfo 方法查询结果
type DisMemberInfo struct {
	DisMemberId     int32 //分销员id
	OutsideMemberId int32 //外部分销员id
	ChainId         int32
	DisType         int32  //1 内部分销员 2外部分销员
	MemberMobile    string //分销员手机号码
	ScrmUserId      string //分销员
}

func (f *UpetDistriMemberFans) TableName() string {
	return "upetmart.upet_distri_member_fans"
}
