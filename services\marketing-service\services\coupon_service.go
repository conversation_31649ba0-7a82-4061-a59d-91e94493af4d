package services

import (
	marketing_po "eShop/domain/marketing-po"
	omnibus_po "eShop/domain/omnibus-po"
	product_po "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	omnibus_services "eShop/services/omnibus-service/services"
	marketing_vo "eShop/view-model/marketing-vo"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"errors"
	"fmt"

	"strings"
	"time"

	"xorm.io/xorm"

	"github.com/spf13/cast"
)

// 2. 注入接口
type CouponService struct {
	common.BaseService
}

func (s *CouponService) GetCouponList(param *marketing_vo.CouponListReq) (out []marketing_vo.MarketingCouponListRes, total int64, err error) {
	s.<PERSON>()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	// 构建查询条件
	tableName := marketing_po.MarketingCoupon{}.TableName()
	query := session.Table(tableName)
	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Type != "" {
		query = query.Where("type = ?", param.Type)
	}
	if param.Status >= 1 {
		currentTime := time.Now().Format("2006-01-02 15:04:05")
		switch param.Status {
		case 1: // 未开始
			query = query.Where("start_time > ? AND end_time > ? and status <> 3", currentTime, currentTime)
		case 2: // 进行中
			query = query.Where("(start_time <= ? AND end_time >= ? and status <>3) or (within_day > 0 and status <>3)", currentTime, currentTime)
		case 3: // 已结束
			query = query.Where("end_time < ? or status = 3", currentTime)
		}
	}
	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}
	// 分页查询
	total, err = query.Where("is_deleted = 0 AND store_id = ?", param.StoreId).
		Limit(param.PageSize, param.PageSize*(param.PageIndex-1)).
		Desc("created_time").
		FindAndCount(&out)
	if err != nil {
		log.Error("查询优惠券列表失败，err=", err.Error())
		return
	}

	// 计算每个优惠券的实时状态
	for i := range out {
		startTime, _ := time.ParseInLocation(
			"2006-01-02 15:04:05",
			out[i].StartTime,
			utils.ChinaLocation,
		)
		endTime, _ := time.ParseInLocation(
			"2006-01-02 15:04:05",
			out[i].EndTime,
			utils.ChinaLocation,
		)
		out[i].Status = s.calculateCouponStatus(&marketing_po.MarketingCoupon{
			IsDeleted:    out[i].IsDeleted,
			StartTime:    startTime,
			EndTime:      endTime,
			Status:       out[i].Status,
			UseStartType: out[i].UseStartType,
		})
	}
	return
}

// SaveCoupon 保存优惠券
func (s *CouponService) SaveCoupon(param *marketing_vo.SaveCouponReq) error {
	logPrefix := fmt.Sprintf("保存优惠券====，入参：%+v", param)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	// 1. 组装优惠券数据
	coupon := &marketing_po.MarketingCoupon{
		Id:           param.Id,
		ChainId:      param.ChainId,
		StoreId:      param.StoreId,
		Type:         param.Type,
		Name:         param.Name,
		TotalCount:   param.TotalCount,
		RemainCount:  param.TotalCount,
		ApplyProduct: param.ApplyProduct,
		Threshold:    param.Threshold,
		Discount:     param.Discount,
		BestOffer:    param.BestOffer,
		UseStartType: param.UseStartType,
		WithinDay:    param.WithinDay,
		Remark:       param.Remark,
		PersonLimit:  param.PersonLimit,
	}
	if param.UseStartType == 3 {
		var err error
		if coupon.StartTime, err = utils.ParseTime(param.StartTime); err != nil {
			return fmt.Errorf("开始时间格式错误: %v", err)
		}
		if coupon.EndTime, err = utils.ParseTime(param.EndTime); err != nil {
			return fmt.Errorf("结束时间格式错误: %v", err)
		}
	}

	// 2. 保存优惠券
	if err := coupon.Save(session); err != nil {
		session.Rollback()
		return err
	}

	// 3. 处理商品关联
	if err := s.handleProductRelation(session, coupon, param); err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

// handleProductRelation 处理优惠券商品关联
func (s *CouponService) handleProductRelation(session *xorm.Session, coupon *marketing_po.MarketingCoupon, param *marketing_vo.SaveCouponReq) error {
	if param.ApplyProduct == 1 {
		return nil
	}
	if param.SkuIds == "" {
		return errors.New("请选择商品")
	}

	// 删除旧关联
	if _, err := session.Where("ref_id = ? AND type = 1", coupon.Id).Delete(&marketing_po.MarketingProduct{}); err != nil {
		return fmt.Errorf("删除商品关联失败: %v", err)
	}

	// 添加新关联

	products := make([]marketing_po.MarketingProduct, 0)
	// 处理 SkuIds
	skuIds := strings.Split(param.SkuIds, ",")
	var skus []product_po.ProProductStoreInfo
	if err := session.Table("pro_product_store_info psi").
		Join("LEFT", "pro_sku ps", "psi.sku_id = ps.id").
		Where("psi.store_id = ? and psi.channel_id = ? and ps.is_del = 0",
			param.StoreId, common.ChannelIdOfflineShop).
		In("psi.sku_id", skuIds).
		Find(&skus); err != nil {
		session.Rollback()
		return fmt.Errorf("查询SKU信息失败: %v", err)
	}
	for _, v := range skus {
		products = append(products, marketing_po.MarketingProduct{
			ChainId:      param.ChainId,
			StoreId:      param.StoreId,
			RefId:        coupon.Id,
			Type:         1, // 优惠券
			ApplyType:    1, // 商品
			ProductType:  1, // 实物
			ProductRefId: v.ProductId,
			SkuId:        v.SkuId,
		})
	}

	if _, err := session.Table("marketing_product").Insert(&products); err != nil {
		session.Rollback()
		log.Error("保存优惠券商品关联失败，err=", err.Error())
		return errors.New("保存优惠券商品关联失败")
	}

	return nil
}

// DeleteCoupon 删除优惠券（更新删除标识）
func (s *CouponService) DeleteCoupon(id int64, storeId string) error {
	logPrefix := fmt.Sprintf("删除优惠券====，id：%d", id)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	// 1. 获取优惠券
	coupon, err := new(marketing_po.MarketingCoupon).GetByID(session, id, storeId)
	if err != nil {
		return err
	}

	// 2. 删除优惠券
	if err = coupon.Delete(session); err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

// EndCoupon 结束优惠券活动
func (s *CouponService) EndCoupon(id int64, storeId string) error {
	logPrefix := fmt.Sprintf("结束优惠券活动====，id：%d", id)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()

	redisConn := cache.GetRedisConn()
	lockKey := fmt.Sprintf("lock:end_coupon_%s_%d", storeId, id)
	lock, err := redisConn.SetNX(lockKey, 1, 10*time.Second).Result()
	if err != nil {
		return err
	}
	if !lock {
		return errors.New("系统繁忙，请稍后再试")
	}
	defer redisConn.Del(lockKey)

	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	// 1. 获取优惠券
	coupon, err := new(marketing_po.MarketingCoupon).GetByID(session, id, storeId)
	if err != nil {
		return err
	}

	// 2. 结束优惠券
	if err = coupon.End(session); err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

// 获取某个人是否有有效的优惠券
func (s *CouponService) GetUserCouponDetail(param *marketing_vo.UserCouponDetailReq) (*marketing_vo.CouponDetailResp, error) {
	logPrefix := fmt.Sprintf("获取个人优惠券详情====，入参：%+v", param)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 查询优惠券基本信息和券码
	var result struct {
		marketing_po.MarketingCoupon `xorm:"extends"`
		Code                         string `json:"code"`
	}

	currentTime := time.Now().Format("2006-01-02 15:04:05")
	exists, err := session.Table("marketing_coupon").
		Join("LEFT", "marketing_coupon_receiver", "marketing_coupon.id = marketing_coupon_receiver.coupon_id").
		Select("marketing_coupon.*, marketing_coupon_receiver.code").
		Where("marketing_coupon.id = ? AND marketing_coupon.store_id = ?  "+
			"AND marketing_coupon_receiver.customer_id = ? AND marketing_coupon_receiver.status = 1 "+
			"AND marketing_coupon_receiver.is_deleted = 0 AND marketing_coupon_receiver.code = ? "+
			"AND marketing_coupon_receiver.enable_time <= ? "+
			"AND marketing_coupon_receiver.expire_time >= ?",
			cast.ToInt(param.CouponID), param.StoreId, cast.ToInt64(param.CustomerId), param.Code,
			currentTime, currentTime).Get(&result)

	if err != nil {
		return nil, fmt.Errorf("查询优惠券信息失败: %v", err)
	}
	if !exists {
		return nil, errors.New("优惠券不存在或已过期")
	}

	// 2. 组装返回数据
	resp := &marketing_vo.CouponDetailResp{
		Id:           result.Id,
		ChainId:      result.ChainId,
		StoreId:      result.StoreId,
		Type:         result.Type,
		Name:         result.Name,
		Content:      result.Content,
		Code:         result.Code, // 添加券码
		TotalCount:   result.TotalCount,
		RemainCount:  result.RemainCount,
		ApplyProduct: result.ApplyProduct,
		Threshold:    result.Threshold,
		Discount:     result.Discount,
		BestOffer:    result.BestOffer,
		UseStartType: result.UseStartType,
		WithinDay:    result.WithinDay,
		StartTime:    result.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:      result.EndTime.Format("2006-01-02 15:04:05"),
		PersonLimit:  result.PersonLimit,
		Status:       s.calculateCouponStatus(&result.MarketingCoupon),
		Remark:       result.Remark,
	}

	// 3. 如果是指定商品，查询商品信息
	if result.ApplyProduct == 2 || result.ApplyProduct == 3 {
		type ProductInfo struct {
			ProductId   int    `json:"product_id"`
			SkuId       int    `json:"sku_id"`
			ProductName string `json:"product_name"`
			RetailPrice int    `json:"retail_price"`
			Stock       int    `json:"stock"`
		}
		var products []ProductInfo

		err = session.Table("marketing_product mp").
			Where("mp.ref_id = ? and type=1", cast.ToInt(param.CouponID)).
			Select("sku_id").
			Find(&products)

		if err != nil {
			return nil, fmt.Errorf("查询关联商品信息失败: %v", err)
		}

		// 添加商品信息到响应中
		for _, p := range products {
			resp.Products = append(resp.Products, struct {
				ProductId   int    `json:"product_id"`
				SkuId       int    `json:"sku_id"`
				ProductName string `json:"product_name"`
				RetailPrice int    `json:"retail_price"`
				Stock       int    `json:"stock"`
			}{
				ProductId:   p.ProductId,
				SkuId:       p.SkuId,
				ProductName: p.ProductName,
				RetailPrice: p.RetailPrice,
				Stock:       0,
			})
		}
	}

	return resp, nil
}

// GetCouponDetail 获取优惠券详情
func (s *CouponService) GetCouponDetail(param *marketing_vo.CouponDetailReq) (*marketing_vo.CouponDetailResp, error) {
	logPrefix := fmt.Sprintf("获取优惠券详情====，入参：%+v", param)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 查询优惠券基本信息
	var coupon marketing_po.MarketingCoupon
	exists, err := session.Where("id = ? AND store_id = ? AND is_deleted = 0",
		param.Id, param.StoreId).Get(&coupon)
	if err != nil {
		return nil, fmt.Errorf("查询优惠券信息失败: %v", err)
	}
	if !exists {
		return nil, errors.New("优惠券不存在")
	}

	// 2. 组装返回数据
	resp := &marketing_vo.CouponDetailResp{
		Id:           coupon.Id,
		ChainId:      coupon.ChainId,
		StoreId:      coupon.StoreId,
		Type:         coupon.Type,
		Name:         coupon.Name,
		Content:      coupon.Content,
		TotalCount:   coupon.TotalCount,
		RemainCount:  coupon.RemainCount,
		ApplyProduct: coupon.ApplyProduct,
		Threshold:    coupon.Threshold,
		Discount:     coupon.Discount,
		BestOffer:    coupon.BestOffer,
		UseStartType: coupon.UseStartType,
		WithinDay:    coupon.WithinDay,
		StartTime:    coupon.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:      coupon.EndTime.Format("2006-01-02 15:04:05"),
		PersonLimit:  coupon.PersonLimit,
		Status:       s.calculateCouponStatus(&coupon),
		Remark:       coupon.Remark,
	}

	// 3. 如果是商品，查询商品信息
	if coupon.ApplyProduct == 2 || coupon.ApplyProduct == 3 {
		type ProductInfo struct {
			ProductId   int    `json:"product_id"`
			SkuId       int    `json:"sku_id"`
			ProductName string `json:"product_name"`
			RetailPrice int    `json:"retail_price"`
			Stock       int    `json:"stock"`
		}
		var products []ProductInfo

		err = session.Table("marketing_product mp").
			Join("LEFT", "pro_product_store_info psi",
				"mp.sku_id = psi.sku_id").
			Join("LEFT", "pro_product pp",
				"mp.product_ref_id = pp.id").
			Where("mp.ref_id = ? AND mp.type = 1 AND mp.store_id = ? and psi.channel_id=?", param.Id, param.StoreId, common.ChannelIdOfflineShop).
			Select("mp.product_ref_id as product_id, " +
				"mp.sku_id, " +
				"pp.name as product_name, " +
				"psi.retail_price").
			Find(&products)

		if err != nil {
			return nil, fmt.Errorf("查询关联商品信息失败: %v", err)
		}

		// 添加商品信息到响应中
		for _, p := range products {
			resp.Products = append(resp.Products, struct {
				ProductId   int    `json:"product_id"`
				SkuId       int    `json:"sku_id"`
				ProductName string `json:"product_name"`
				RetailPrice int    `json:"retail_price"`
				Stock       int    `json:"stock"`
			}{
				ProductId:   p.ProductId,
				SkuId:       p.SkuId,
				ProductName: p.ProductName,
				RetailPrice: p.RetailPrice,
				Stock:       0,
			})
		}
	}

	return resp, nil
}

// GetCouponReceiverList 获取优惠券领取列表
// 选购商品时，获取优惠券列表及营销活动列表共用此接口
func (s *CouponService) GetCouponReceiverList(param *marketing_vo.CouponReceiverListReq) ([]marketing_vo.CouponReceiverInfo, int, error) {
	s.Begin()
	defer s.Close()

	// 1. 构建基础查询，明确指定表别名
	query := s.Engine.Table("marketing_coupon_receiver mcr").
		Join("LEFT", "marketing_coupon mc", "mcr.coupon_id = mc.id").
		Select("mcr.*, mc.name as coupon_name, mc.type as coupon_type,mc.content, " +
			"mc.threshold, mc.discount, mc.best_offer, mc.apply_product,mc.use_start_type," +
			"mc.start_time, mc.end_time,mc.remark")

	// 2. 基础条件 - 明确指定使用 mc 表的 store_id
	query = query.And("mc.store_id = ?", param.StoreId).
		And("mcr.is_deleted = 0")

	if param.CouponId > 0 {
		query = query.And("mcr.coupon_id = ?", param.CouponId)
	}
	// 搜索条件
	if param.CustomerName != "" {
		query = query.And("mcr.customer_name LIKE ?", "%"+param.CustomerName+"%")
	}
	//用户id
	if param.CustomerId != "" {
		query = query.And("mcr.customer_id = ?", cast.ToInt64(param.CustomerId))
	}
	//状态
	if param.Status > 0 {
		currentTime := time.Now().Format("2006-01-02 15:04:05")
		switch param.Status {
		case 1: // 未使用且在有效期内
			query = query.And("mcr.status = 1 AND enable_time < ? AND mcr.expire_time >= ?", currentTime, currentTime)
		case 2: // 已使用
			query = query.And("mcr.status = 2")
		case 3: // 已过期（包括时间过期和手动停止）
			query = query.And("mcr.status = 3 or (mcr.expire_time < ? and mcr.status != 2)", currentTime)
		}
	}

	// 如果有指定商品筛选
	if param.SkuIds != "" && param.SkuIds != "0" {
		// 验证并清理输入的 SkuIds
		skuIdList := strings.Split(param.SkuIds, ",")
		if len(skuIdList) == 0 {
			return nil, 0, errors.New("无效的商品ID")
		}

		// 构建 IN 查询的条件字符串
		inStr := "(" + strings.TrimRight(strings.TrimLeft(param.SkuIds, ","), ",") + ")"

		// 子查询：先找出所有符合条件的优惠券ID
		sql := `SELECT DISTINCT id FROM marketing_coupon 
				WHERE store_id = ? AND is_deleted = 0 
				AND (
					apply_product = 1 
					OR (apply_product = 2 AND id IN (
						SELECT ref_id FROM marketing_product 
						WHERE type = 1 AND store_id = ? 
						AND sku_id IN ` + inStr + `
					)) 
					OR (apply_product = 3 AND id NOT IN (
						SELECT ref_id FROM marketing_product 
						WHERE type = 1 AND store_id = ? 
						AND sku_id IN ` + inStr + `
					))
				)`

		couponIds := make([]int64, 0)
		err := s.Engine.SQL(sql, param.StoreId, param.StoreId, param.StoreId).Find(&couponIds)

		if err != nil {
			log.Errorf("查询商品相关优惠券失败: %v", err.Error())
			return nil, 0, err
		}

		// 如果没有找到符合条件的优惠券，添加一个不可能的ID确保查询结果为空
		if len(couponIds) == 0 {
			couponIds = append(couponIds, -1)
		}

		// 将找到的优惠券ID添加到主查询条件中
		query = query.In("mc.id", couponIds)
	}

	// 3. 分页
	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}

	// 4. 查询数据
	var list []marketing_vo.CouponReceiverInfo
	total, err := query.Limit(param.PageSize, (param.PageIndex-1)*param.PageSize).Desc("mcr.created_time").
		FindAndCount(&list)
	if err != nil {
		log.Errorf("获取优惠券领取列表失败: %v", err.Error())
		return nil, 0, err
	}

	// 5. 处理每条记录的状态和有效期
	for i := range list {
		startTime, _ := time.ParseInLocation(
			"2006-01-02 15:04:05",
			list[i].EnableTime,
			utils.ChinaLocation,
		)
		endTime, _ := time.ParseInLocation(
			"2006-01-02 15:04:05",
			list[i].ExpireTime,
			utils.ChinaLocation,
		)
		// 计算优惠券状态
		list[i].Status = s.calculateCouponReceiverStatus(&marketing_po.MarketingCoupon{
			IsDeleted:    0,
			StartTime:    startTime,
			EndTime:      endTime,
			Status:       list[i].Status,
			UseStartType: list[i].UseStartType,
		})
	}

	return list, int(total), err
}

// 停止用券
func (s *CouponService) StopCoupon(param *marketing_vo.StopCouponReq) error {
	s.Begin()
	defer s.Close()

	redisConn := cache.GetRedisConn()
	lockKey := fmt.Sprintf("lock:stop_coupon_%d_%s", param.CouponId, param.StoreId)
	lock, err := redisConn.SetNX(lockKey, 1, 10*time.Second).Result()
	if err != nil {
		return err
	}
	if !lock {
		return errors.New("系统繁忙，请稍后再试")
	}

	defer redisConn.Del(lockKey)

	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	// 1. 获取优惠券
	_, err = new(marketing_po.MarketingCoupon).GetByID(session, param.CouponId, param.StoreId)
	if err != nil {
		return err
	}

	// 2. 停止优惠券
	receiver := &marketing_po.MarketingCouponReceiver{}
	if err = receiver.Stop(session, param.Id, param.CouponId, param.StoreId, param.CustomerId); err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

// IssueCoupon 发放优惠券
func (s *CouponService) IssueCoupon(param *marketing_vo.IssueCouponReq) error {
	s.Begin()
	defer s.Close()

	redisConn := cache.GetRedisConn()
	lockKey := fmt.Sprintf("lock:issue_coupon_%s_%s", param.StoreId, param.CustomerId)
	lock, err := redisConn.SetNX(lockKey, 1, 10*time.Second).Result()
	if err != nil {
		return err
	}
	if !lock {
		return errors.New("系统繁忙，请稍后再试")
	}
	defer redisConn.Del(lockKey)

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 校验来源类型
	if param.Source != 1 && param.Source != 2 {
		return errors.New("无效的来源类型")
	}

	// 2. 查询客户信息
	customer, err := marketing_po.GetCustomerInfo(session, param.CustomerId, param.StoreId)
	if err != nil {
		return err
	}
	if customer == nil || customer.Phone == "" {
		// 如果没有客户信息或手机号，无法发送短信，可以选择回滚或仅记录日志后继续
		log.Infof("无法为客户 %s 发送优惠券短信：未找到客户信息或手机号", param.CustomerId)
		return errors.New("未找到客户信息")
	}

	// 解析优惠券ID
	couponIds := strings.Split(param.CouponIds, ",")
	if len(couponIds) == 0 {
		return errors.New("优惠券ID不能为空")
	}

	// 3. 查询店铺名称 (只需查询一次)
	storeName := "您的店铺" // 默认店铺名称
	storeMap, storeErr := new(omnibus_po.Store).GetStoreInfoByFinanceCode(session, []string{param.StoreId})
	if storeErr == nil && len(storeMap) > 0 {
		if store, exists := storeMap[param.StoreId]; exists {
			storeName = store.Name
		}
	} else {
		log.Infof("发放优惠券 %s 时未能获取店铺名称 (StoreId: %s): %v", param.CouponIds, param.StoreId, storeErr)
	}

	if err = session.Begin(); err != nil {
		return err
	}
	// 遍历处理每个优惠券
	for _, couponIdStr := range couponIds {
		couponId := cast.ToInt64(couponIdStr)
		if couponId <= 0 {
			log.Infof("无效的优惠券ID: %s", couponIdStr)
			continue // 跳过无效的ID
		}

		// 获取优惠券
		coupon, err := new(marketing_po.MarketingCoupon).GetByID(session, couponId, param.StoreId)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("获取优惠券 %d 失败: %v", couponId, err)
		}

		// 发放优惠券
		if err = coupon.Issue(session, cast.ToInt64(param.CustomerId)); err != nil {
			session.Rollback()
			return fmt.Errorf("发放优惠券 %d 失败: %v", couponId, err)
		}

		// 创建领取记录
		if err := marketing_po.CreateReceiveRecord(session, coupon, customer, param.CustomerId, param.Source); err != nil {
			session.Rollback()
			return fmt.Errorf("创建优惠券 %d 领取记录失败: %v", couponId, err)
		}

		// 发送短信通知
		s.SendIssueCouponNotification(coupon, customer, storeName)
	}

	return session.Commit()
}

// SendIssueCouponNotification 发送优惠券发放短信通知
func (s *CouponService) SendIssueCouponNotification(coupon *marketing_po.MarketingCoupon, customer *marketing_po.Customer, storeName string) {
	var templateCode string
	var couponTypeStr string

	// 根据优惠券类型选择短信模板
	if coupon.Type == 1 { // 1 代表满减券
		templateCode = omnibus_po.SmsTemplateCouponFullReduction
		couponTypeStr = cast.ToString(coupon.Threshold)
	} else if coupon.Type == 2 { //2 代表折扣券
		templateCode = omnibus_po.SmsTemplateCouponDiscount
		couponTypeStr = cast.ToString(coupon.Threshold)
	} else {
		log.Infof("优惠券 %d 类型 %d 不支持短信通知", coupon.Id, coupon.Type)
		return // 跳过不支持类型的短信发送
	}

	// 准备短信参数
	smsParams := omnibus_services.SmsCouponGiftParams{
		TenantName: storeName, // 使用前面查询到的店铺名称
		Threshold:  couponTypeStr,
		Discount:   cast.ToString(coupon.Discount),
	}
	paramsJson, jsonErr := json.Marshal(smsParams)
	if jsonErr != nil {
		log.Infof("序列化优惠券 %d 的短信参数失败: %v", coupon.Id, jsonErr)
		return // 序列化失败，无法发送
	}

	smsService := &omnibus_services.SmsService{}
	// 发送短信
	smsErr := smsService.SendMessage(omnibus_vo.SmsSendMessageRequest{
		StoreId:       coupon.StoreId,
		Mobile:        customer.Phone, // 使用客户手机号
		TemplateCode:  templateCode,
		TemplateParam: string(paramsJson),
	})
	if smsErr != nil {
		log.Infof("为客户 %s 发送优惠券 %d (%s) 的短信通知失败: %v", customer.Phone, coupon.Id, coupon.Name, smsErr)
	} else {
		log.Infof("成功为客户 %s 发送优惠券 %d (%s) 的短信通知", customer.Phone, coupon.Id, coupon.Name)
	}
}

// calculateCouponStatus 计算优惠券活动状态
func (s *CouponService) calculateCouponStatus(coupon *marketing_po.MarketingCoupon) int {
	if coupon.Status == 3 || coupon.IsDeleted == 1 {
		return marketing_po.StatusEnded
	}

	// 当日起/次日起的优惠券直接返回进行中状态
	if coupon.UseStartType == marketing_po.UseStartTypeToday || coupon.UseStartType == marketing_po.UseStartTypeTomorrow {
		return marketing_po.StatusRunning
	}

	// 获取当前时区的时间

	now := time.Now()
	if now.Before(coupon.StartTime) {
		return marketing_po.StatusPending
	} else if now.After(coupon.StartTime) && now.Before(coupon.EndTime) {
		return marketing_po.StatusRunning
	} else if now.After(coupon.EndTime) {
		return marketing_po.StatusEnded
	}
	return marketing_po.StatusRunning
}

// calculateCouponReceiverStatus 计算用户领取的优惠券状态
func (s *CouponService) calculateCouponReceiverStatus(coupon *marketing_po.MarketingCoupon) int {
	// 如果优惠券已被删除或已手动结束，直接返回过期状态
	if coupon.IsDeleted == 1 || coupon.Status == 3 {
		return marketing_po.StatusExpired
	} else if coupon.Status == 2 {
		return marketing_po.StatusUsed
	}

	now := time.Now()

	// 根据时间判断优惠券状态
	switch {
	case now.Before(coupon.StartTime):
		// 未到使用开始时间
		return marketing_po.StatusUnused
	case now.After(coupon.EndTime):
		// 已过使用结束时间
		return marketing_po.StatusExpired
	default:
		// 未使用且在有效期内
		return marketing_po.StatusUnused
	}
}

// GetUserCouponList 获取用户可见的优惠券列表
func (s *CouponService) GetUserCouponList(param *marketing_vo.CouponUserListReq) ([]marketing_vo.UserCouponListItem, int64, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 转换查询参数
	skuIds := make([]string, 0)
	if param.SkuIds != "" {
		skuIds = strings.Split(param.SkuIds, ",")
	}
	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}
	query := &marketing_po.CouponUserListQuery{
		StoreId:    param.StoreId,
		CustomerId: param.CustomerId,
		Status:     marketing_po.StatusRunning,
		SkuIds:     skuIds,
		PageIndex:  param.PageIndex,
		PageSize:   param.PageSize,
	}

	// 调用领域层查询
	list, total, err := new(marketing_po.MarketingCoupon).GetUserCouponList(session, query)
	if err != nil {
		return nil, 0, err
	}

	// 转换为VO对象
	result := make([]marketing_vo.UserCouponListItem, 0, len(list))
	for _, item := range list {
		result = append(result, marketing_vo.UserCouponListItem{
			Id:           item.Id,
			Name:         item.Name,
			Type:         item.Type,
			Content:      item.Content,
			TotalCount:   item.TotalCount,
			RemainCount:  item.RemainCount,
			ApplyProduct: item.ApplyProduct,
			Threshold:    item.Threshold,
			Discount:     item.Discount,
			BestOffer:    item.BestOffer,
			UseStartType: item.UseStartType,
			WithinDay:    item.WithinDay,
			StartTime:    item.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:      item.EndTime.Format("2006-01-02 15:04:05"),
			Status:       item.Status,
			IsReceived:   item.IsReceived,
			Code:         item.Code,
		})
	}

	return result, total, nil
}
