package cachekey

const (
	// 分销关系-粉丝关系保护期 设置
	ProtectDay = "eshop:distribution:protect:day:orgid:%d" //有效期多少天后自动解绑，如果值为""或者未设置，则代表无保护期 %d代表是 主体id

	// 更新分销员统计表提现金额 redis锁  可提现金额、提现中金额、已提现金额， 所设置的redis锁 dis_distributor_total.wait_withdraw、withdraw_apply、withdraw_success）
	WithdrawAmountUpdate = "distribution:withdraw:orgid:%d:shopid:%d:disid:%d:" // 占位符分别代表： 主体id、店铺id、分销员id

	// 更新分销员统计表提现金额 redis锁  可提现金额、提现中金额、已提现金额， 所设置的redis锁 dis_distributor_total.wait_withdraw、withdraw_apply、withdraw_success）
	// 目前仅支持老板提现，加锁只需加到企业上， 防止企业更换老板的情况
	ShopWithdrawAmountUpdate = "distribution:withdraw:orgid:%d:shopid:%d" // 占位符分别代表： 主体id、店铺id

	// 记录最新宠利扫提现全部订单数据
	CLSShopWithdrawOrder = "cls:shop:withdraw:order:orgid:%d:shopid:%d:disid:%d" // 占位符分别代表： 主体id、店铺id、分销员id
	// 记录最新宠利扫提现全部退款订单数据
	CLSShopWithdrawRefundOrder = "cls:shop:withdraw:refund:order:orgid:%d:shopid:%d:disid:%d" // 占位符分别代表： 主体id、店铺id、分销员id

	//提现审核 锁key
	WithdrawCheck = "distribution:withdraw:check:%d" //占换符代表：提现id
	// 写入待结算 redis锁
	InsertSettlementLock = "distribution:settlement:insert"

	// 待结算改为已结算 redis锁：
	ChangeSettlemenStatustLock = "distribution:settlement:change:status"

	// DefaultCommisRate 分销商品-默认分销佣金比例（示例： 如果商品分销佣金比例是：%11，则这里直接填11）
	DefaultCommisRate = "distribution:goods:defaultCommis:%d"

	// CustomerChannelCommisRate 客户渠道佣金比例 （示例： 如果商品分销佣金比例是：%11，则这里直接填11）
	CustomerChannelCommisRate = "distribution:goods:customerChannelCommis:%d"

	// 插入业务员补仓数据和二维码的锁
	SetSaleManBarCodeLock = "distribution:salesperson:add"

	// 跑业务员补充数据，服务 企业数量，分销员数量
	SetSaleManDataLock = "distribution:salesperson:up"
	//商品订单概览
	StatsOrderDataLock = "JobStatsService:StatsOrderData"
	//商品分销订单概览
	StatsDisOrderDataLock = "JobStatsService:StatsDisOrderDailyData"
	//业务员分销订单统计
	StatsSalespersonOrderDataLock = "JobStatsService:StatsSalespersonOrderDailyData"
	// 保险订单统计
	StatsInsureOrderDataLock = "JobStatsService:InsureOrderStat"

	// 业务员数据和分销企业数据统计
	StatsEnterpriseDailyDataLock = "JobStatsService:StatsEnterpriseDaily"

	// 店铺分销员佣金数据统计
	StatsShopDistributorDailyDataLock = "JobStatsService:StatsShopDistributorDaily"

	// 添加连锁商品锁
	AddChainProductLock = "distribution:add:chain:product:%s:%s" // 占位符分别代表： 连锁id、商品名称
	PetPrizeLock        = "marketing:PetPrizeLock"
	PetPrize5Or25Lock   = "marketing:PetPrize5Or25Lock"
	PetPrizePop = "marketing:pet-prize-pop:%s" // 占位符代表：用户ID
)
