// Package marketing_po 数据监测统计表领域模型
package marketing_po

import "time"

// PetStat 数据监测统计表
// 对应表：eshop.pet_stat
// 记录每日各类指标的统计数据
// 包含日期、指标类型、数值等
type PetStat struct {
	// 主键
	Id int `xorm:"pk autoincr 'id'"`
	// 日期
	DaliyDate string `xorm:"daliy_date"`
	// 指标编码
	MetricType int `xorm:"metric_type"`
	// 事件数值
	Num int `xorm:"num"`
	// 指标名称
	MetricName string `xorm:"metric_name"`
	// 作品编号
	WorkCode string `xorm:"work_code"`
	// 创建时间
	CreateTime time.Time `xorm:"create_time"`
	// 更新时间
	UpdateTime time.Time `xorm:"update_time"`
}

// TableName 返回表名
func (PetStat) TableName() string {
	return "eshop.pet_stat"
}
