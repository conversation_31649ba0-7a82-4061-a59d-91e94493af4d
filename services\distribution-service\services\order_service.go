package services

import (
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	dissettlement "eShop/services/distribution-service/enum/dis-settlement"
	diswithdrawrecord "eShop/services/distribution-service/enum/dis-withdraw-record"
	upetorders "eShop/services/distribution-service/enum/upet-orders"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/leeqvip/gophp/serialize"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

type DisOrderService struct {
	common.BaseService
}

func (h *DisOrderService) GetDisOrderList(in vo.GetDisOrderListReq) (out []vo.DisOrderListView, total int, err error) {
	log.Infof("获取分销订单列表-入参为%s", utils.InterfaceToJSON(in))
	h.Begin()
	defer h.Close()
	session := h.Session
	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	selectStr := `
	a.customer_channel_id,
	dcc.channel_path as customer_channel_path,
	a.org_type,
	a.store_id,
	a.order_id,
	a.order_sn,
	a.add_time as order_add_time,
	a.finnshed_time as order_finish_time,
	a.order_state,
	a.order_amount,
	a.refund_amount,
	a.shipping_fee,
	a.dis_type,
	(SELECT SUM(goods_pay_price) FROM upetmart.upet_order_goods og WHERE og.order_id = a.order_id AND og.is_dis = 1) as order_dis_amount,	
	a.buyer_id,
	a.buyer_name,
	a.buyer_phone,
	a.encrypt_mobile,
	a.logistics_code,
	a.swlm_status,
	IF(c.org_id = 4, c.real_name, c.name) as distributor_name,
	c.id as distributor_id,
	c.dis_role,
	CONCAT(a.tuoke_salesperson_id, '/', ss1.name, '/', ss1.org_name) as tuoke_salesperson_id_name,
	f.enterprise_name,
	de.enterprise_name as dis_enterprise_name,
	f.id as enterprise_id,
	a.in_system,
	cps.dis_commis_amount`

	session.Table("upetmart.upet_orders").Alias("a").
		Select(selectStr).
		Join("inner", "upetmart.upet_order_goods b", "a.order_id = b.order_id and b.is_dis = 1").
		Join("left", "eshop.dis_distributor c", "b.dis_member_id = c.member_id and a.store_id = c.org_id").
		Join("left", "eshop.shop e", "e.id = b.shop_id").Where("a.is_dis=1").
		Join("left", "eshop.scrm_enterprise f", "e.enterprise_id=f.id").
		Join("left", "eshop.dis_enterprise de", "de.scrm_enterprise_id = e.enterprise_id and de.org_id = ?", in.OrgId).
		Join("LEFT", "upetmart.upet_orders_salesperson uos", "uos.order_id=a.order_id").
		Join("LEFT", "eshop.scrm_salesperson ss", "ss.id=uos.salesperson_id").
		Join("LEFT", "eshop.scrm_salesperson ss1", "ss1.id=a.tuoke_salesperson_id").
		Join("LEFT", "eshop.dis_distributor_swlm_cps cps", "cps.order_sn = a.order_sn").
		Join("left", "eshop.dis_customer_channel dcc", "dcc.id = a.customer_channel_id").
		And("a.delete_state=0 and a.hide_state=0").
		And("a.store_id=?", in.OrgId)

	if in.OrgType != 0 {
		session.And("a.org_type = ?", in.OrgType)
	}
	if in.DeductCommission == 1 {
		session.And("cps.dis_commis_amount > 0")
	}
	if in.DeductCommission == 2 {
		session.And("cps.dis_commis_amount = 0 or cps.dis_commis_amount is null")
	}
	if in.OrderState != -1 {
		//如果是已发货， 需要把部分发货的包括进来
		if in.OrderState == upetorders.StateSend {
			session.And("(a.order_state = ? or a.order_state=?)", upetorders.StateSend, upetorders.StatePartSend)
		} else {
			session.And("a.order_state = ?", in.OrderState)
		}
	}
	if len(in.OrgName) > 0 {
		session.And("ss.org_name LIKE ?", "%"+in.OrgName+"%")
	}
	if in.WhereType != "" && in.Where != "" {
		whereTypeMap := map[string]string{
			"order_sn":       "a.order_sn = ?",
			"distributor_id": "c.id = ?",
			//"distributor_name": "c.name like ?",
			"buyer_mobile":    "a.encrypt_mobile = ?",
			"buyer_name":      "a.buyer_name like ?",
			"salesman_id":     "g.salesperson_id = ?",
			"salesman_name":   "h.name like ?",
			"enterprise_name": "f.enterprise_name like ?",
		}
		if in.OrgId == enum.BLKYOrgId {
			whereTypeMap["enterprise_name"] = "de.enterprise_name like ?"
		}

		if v, ok := whereTypeMap[in.WhereType]; ok {
			if in.WhereType == "buyer_name" || in.WhereType == "salesman_name" || in.WhereType == "enterprise_name" {
				session.And(v, "%"+in.Where+"%")
			} else if in.WhereType == "buyer_mobile" {
				session.And(v, utils.MobileEncrypt(in.Where))
			} else if in.WhereType == "salesman_id" || in.WhereType == "order_sn" {
				session.And(v, cast.ToInt64(in.Where))
			} else {
				session.And(v, in.Where)
			}

			if in.WhereType == "salesman_name" || in.WhereType == "salesman_id" {
				session.Join("left", "upetmart.upet_orders_salesperson g", "g.order_id=a.order_id").
					Join("left", "eshop.scrm_salesperson h", "h.id=g.salesperson_id")
			}
		}
		if in.WhereType == "distributor_name" {
			if in.OrgId == 4 {
				session.And("c.real_name like ?", "%"+in.Where+"%")
			} else {
				session.And("c.name like ?", "%"+in.Where+"%")
			}
		}
	}
	if in.LogisticsCode != "" {
		session.And("a.logistics_code = ?", in.LogisticsCode)
	}

	if in.WhereType2 != "" && (in.WhereStart != "" || in.WhereEnd != "") {
		if in.WhereType2 == "add_time" || in.WhereType2 == "finish_time" {
			if in.WhereType2 == "finish_time" {
				in.WhereType2 = "finnshed_time"
			}
			if in.WhereStart != "" {
				if start, err := time.ParseInLocation(utils.DateTimeLayout, in.WhereStart, time.Local); err != nil {
					log.Errorf("获取分销订单列表-解析时间错误%s", err.Error())
					return nil, 0, errors.New("解析开始时间错误")
				} else {
					session.And("a."+in.WhereType2+">=?", start.Unix())
				}

			}
			if in.WhereEnd != "" {
				if end, err := time.ParseInLocation(utils.DateTimeLayout, in.WhereEnd, time.Local); err != nil {
					log.Errorf("获取分销订单列表-解析时间错误%s", err.Error())
					return nil, 0, errors.New("解析开始时间错误")
				} else {
					session.And("a."+in.WhereType2+"<=?", end.Unix())
				}

			}
		}
	}
	data := make([]vo.DisOrderList, 0)
	count, err := session.OrderBy("a.add_time desc").Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).GroupBy("a.order_id").FindAndCount(&data)
	if err != nil {
		log.Errorf("获取分销订单列表-错误为%s", err.Error())
		return nil, 0, errors.New("获取分销订单列表" + err.Error())
	}

	type SalespersonStru struct {
		OrderId           int    `json:"order_id"`
		SalespersonId     int64  `json:"salesperson_id"`
		SalespersonName   string `json:"salesperson_name"`
		SalespersonIdName string `json:"salesperson_id_name"`
	}
	OrderIdMapSalesperson := make(map[int]SalespersonStru)
	salespersonData := make([]SalespersonStru, 0)
	if len(data) > 0 && in.OrgId == enum.OrgId {

		orderIdSli := make([]int, len(data))

		for k, v := range data {
			orderIdSli[k] = v.OrderId
		}
		newSession := h.Engine.NewSession()
		defer newSession.Close()

		sql2 := `a.order_id,
		a.salesperson_id,
		GROUP_CONCAT(CONCAT(b.NAME, "/", b.org_name) separator ', ') as salesperson_name,
		GROUP_CONCAT(CONCAT(b.id, "/", b.name, "/", b.org_name) separator ', ') as salesperson_id_name`
		if err := newSession.Table("upetmart.upet_orders_salesperson a").
			Select(sql2).Join("left", "eshop.scrm_salesperson b", "a.salesperson_id=b.id").
			In("a.order_id", orderIdSli).GroupBy("a.order_id").Find(&salespersonData); err != nil {
			log.Errorf("获取分销订单的业务员信息失败-错误为%s", err.Error())
			return nil, 0, errors.New("获取分销订单的业务员信息失败" + err.Error())
		}
		for _, v := range salespersonData {
			OrderIdMapSalesperson[v.OrderId] = SalespersonStru{
				OrderId:           v.OrderId,
				SalespersonId:     v.SalespersonId,
				SalespersonName:   v.SalespersonName,
				SalespersonIdName: v.SalespersonIdName,
			}
		}

	}
	out = make([]vo.DisOrderListView, len(data))
	for k, v := range data {
		OrderFinishTime := ""
		if v.OrderFinishTime > 0 {
			OrderFinishTime = time.Unix(int64(v.OrderFinishTime), 0).Format(utils.DateTimeLayout)
		}
		out[k].OrgType = v.OrgType
		out[k].CustomerChannelId = v.CustomerChannelId
		out[k].CustomerChannelPath = v.CustomerChannelPath
		out[k].StoreId = v.StoreId
		out[k].BuyerId = v.BuyerId
		out[k].BuyerName = v.BuyerName
		out[k].BuyerPhone = v.BuyerPhone
		out[k].EncryptMobile = v.EncryptMobile
		out[k].DistributorId = v.DistributorId
		out[k].DistributorName = v.DistributorName
		out[k].OrderAddTime = time.Unix(int64(v.OrderAddTime), 0).Format(utils.DateTimeLayout)
		out[k].OrderAmount = utils.Yuan2Fen(v.OrderAmount)
		out[k].OrderDisAmount = utils.Yuan2Fen(v.OrderDisAmount)
		out[k].OrderFinishTime = OrderFinishTime
		out[k].OrderSn = v.OrderSn
		out[k].OrderState = v.OrderState
		out[k].RefundAmount = utils.Yuan2Fen(v.RefundAmount)
		out[k].ShippingFee = utils.Yuan2Fen(v.ShippingFee)
		out[k].ShopName = enum.OrgMap[v.StoreId]
		if in.OrgId == enum.BLKYOrgId && (v.DisRole == disdistributor.DisRoleBoss || v.DisRole == disdistributor.DisRoleWorker) {
			out[k].EnterpriseName = v.DisEnterpriseName
		} else {
			out[k].EnterpriseName = v.EnterpriseName
		}
		out[k].EnterpriseId = v.EnterpriseId
		out[k].LogisticsCode = v.LogisticsCode
		out[k].SwlmStatus = v.SwlmStatus
		if salespersion, ok := OrderIdMapSalesperson[v.OrderId]; ok {
			out[k].SalespersonName = salespersion.SalespersonName
			out[k].SalespersonIdName = salespersion.SalespersonIdName
		}
		out[k].TuokeSalespersonIdName = v.TuokeSalespersonIdName
		if in.OrgId == enum.BLKYOrgId {
			out[k].ShopName = "百林康源"
		}
		out[k].DisType = v.DisType
		out[k].InSystem = v.InSystem
		out[k].DisCommisAmount = v.DisCommisAmount
	}

	total = int(count)
	return
}

func (h *DisOrderService) ExportDisOrderList(in vo.GetDisOrderListReq) (out []vo.DisOrderListView, total int, err error) {
	log.Infof("获取分销订单列表-入参为%s", utils.InterfaceToJSON(in))
	h.Begin()
	defer h.Close()
	session := h.Session
	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	selectStr := `
	a.org_type,
	a.customer_channel_id,
	dcc.channel_path as customer_channel_path,
	de.enterprise_name as dis_enterprise_name,
	a.store_id,
	a.order_id,
	a.order_sn,
	a.logistics_code,
	g.sdxtm as xiang_ma,
	a.add_time as order_add_time,
	a.finnshed_time as order_finish_time,
	a.order_state,
	a.order_amount,
	a.refund_amount,
	a.shipping_fee,
	a.dis_type,
	a.swlm_status,
	(SELECT SUM(goods_pay_price) FROM upetmart.upet_order_goods og WHERE og.order_id = a.order_id AND og.is_dis = 1) as order_dis_amount,	
	a.buyer_id,
	a.buyer_name,
	a.buyer_phone,
	a.encrypt_mobile,
	IF(c.org_id = 4, c.real_name, c.name) as distributor_name,
	c.id as distributor_id,
	CONCAT(a.tuoke_salesperson_id, '/', ss1.name, '/', ss1.org_name) as tuoke_salesperson_id_name,
	f.enterprise_name,
	f.id as enterprise_id,
	b.goods_id,
	b.goods_name,
	b.goods_num,
	b.dis_commis_rate,
	b.goods_pay_price,
	cps.dis_commis_amount,
	ROUND(goods_pay_price * dis_commis_rate / 100, 2) AS commis_amount`

	session.Table("upetmart.upet_orders").Alias("a").
		Select(selectStr).
		Join("inner", "upetmart.upet_order_goods b", "a.order_id = b.order_id and b.is_dis = 1").
		Join("left", "eshop.dis_distributor c", "b.dis_member_id = c.member_id and a.store_id = c.org_id").
		Join("left", "eshop.shop e", "e.id = b.shop_id").Where("a.is_dis=1").
		Join("left", "eshop.scrm_enterprise f", "e.enterprise_id=f.id").
		Join("left", "eshop.dis_enterprise de", "de.scrm_enterprise_id = e.enterprise_id").
		Join("LEFT", "upetmart.upet_orders_salesperson uos", "uos.order_id=a.order_id").
		Join("LEFT", "eshop.scrm_salesperson ss", "ss.id=uos.salesperson_id").
		Join("LEFT", "eshop.scrm_salesperson ss1", "ss1.id=a.tuoke_salesperson_id").
		Join("left", "blky.xkucun g", "g.swlm=a.logistics_code").
		Join("LEFT", "eshop.dis_distributor_swlm_cps cps", "cps.order_sn = a.order_sn").
		Join("left", "eshop.dis_customer_channel dcc", "dcc.id = a.customer_channel_id").
		And("a.delete_state=0 and a.hide_state=0").
		And("a.store_id=?", in.OrgId)

	if in.OrderState != -1 {
		//如果是已发货， 需要把部分发货的包括进来
		if in.OrderState == upetorders.StateSend {
			session.And("(a.order_state = ? or a.order_state=?)", upetorders.StateSend, upetorders.StatePartSend)
		} else {
			session.And("a.order_state = ?", in.OrderState)
		}
	}
	if in.DeductCommission == 1 {
		session.And("cps.dis_commis_amount > 0")
	}
	if in.DeductCommission == 2 {
		session.And("cps.dis_commis_amount = 0 or cps.dis_commis_amount is null")
	}

	if in.OrgType != 0 {
		session.And("a.org_type = ?", in.OrgType)
	}
	if len(in.OrgName) > 0 {
		session.And("ss.org_name LIKE ?", "%"+in.OrgName+"%")
	}
	if in.LogisticsCode != "" {
		session.And("a.logistics_code = ?", in.LogisticsCode)
	}
	if in.WhereType != "" && in.Where != "" {
		whereTypeMap := map[string]string{
			"order_sn":       "a.order_sn = ?",
			"distributor_id": "c.id = ?",
			//"distributor_name": "c.name like ? ",
			"buyer_mobile":    "a.encrypt_mobile = ?",
			"buyer_name":      "a.buyer_name like ?",
			"salesman_id":     "g.salesperson_id = ?",
			"salesman_name":   "h.name like ?",
			"enterprise_name": "f.enterprise_name like ?",
		}

		if v, ok := whereTypeMap[in.WhereType]; ok {
			if in.WhereType == "buyer_name" || in.WhereType == "salesman_name" || in.WhereType == "enterprise_name" {
				session.And(v, "%"+in.Where+"%")
			} else if in.WhereType == "buyer_mobile" {
				session.And(v, utils.MobileEncrypt(in.Where))
			} else if in.WhereType == "salesman_id" || in.WhereType == "order_sn" {
				session.And(v, cast.ToInt64(in.Where))
			} else {
				session.And(v, in.Where)
			}

			if in.WhereType == "salesman_name" || in.WhereType == "salesman_id" {
				session.Join("left", "upetmart.upet_orders_salesperson g", "g.order_id=a.order_id").
					Join("left", "eshop.scrm_salesperson h", "h.id=g.salesperson_id")
			}
		}
		if in.WhereType == "distributor_name" {
			if in.OrgId == 4 {
				session.And("c.real_name like ?", "%"+in.Where+"%")
			} else {
				session.And("c.name like ?", "%"+in.Where+"%")
			}
		}
	}

	if in.WhereType2 != "" && (in.WhereStart != "" || in.WhereEnd != "") {
		if in.WhereType2 == "add_time" || in.WhereType2 == "finish_time" {
			if in.WhereType2 == "finish_time" {
				in.WhereType2 = "finnshed_time"
			}
			if in.WhereStart != "" {
				if start, err := time.ParseInLocation(utils.DateTimeLayout, in.WhereStart, time.Local); err != nil {
					log.Errorf("获取分销订单列表-解析时间错误%s", err.Error())
					return nil, 0, errors.New("解析开始时间错误")
				} else {
					session.And("a."+in.WhereType2+">=?", start.Unix())
				}

			}
			if in.WhereEnd != "" {
				if end, err := time.ParseInLocation(utils.DateTimeLayout, in.WhereEnd, time.Local); err != nil {
					log.Errorf("获取分销订单列表-解析时间错误%s", err.Error())
					return nil, 0, errors.New("解析开始时间错误")
				} else {
					session.And("a."+in.WhereType2+"<=?", end.Unix())
				}

			}
		}
	}
	data := make([]vo.ExportDisOrderList, 0)
	count, err := session.OrderBy("a.add_time desc").Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).GroupBy("b.rec_id").FindAndCount(&data)
	if err != nil {
		log.Errorf("获取分销订单列表-错误为%s", err.Error())
		return nil, 0, errors.New("获取分销订单列表" + err.Error())
	}

	type SalespersonStru struct {
		OrderId           int    `json:"order_id"`
		SalespersonId     int64  `json:"salesperson_id"`
		SalespersonName   string `json:"salesperson_name"`
		SalespersonIdName string `json:"salesperson_id_name"`
	}
	OrderIdMapSalesperson := make(map[int]SalespersonStru)
	salespersonData := make([]SalespersonStru, 0)
	if len(data) > 0 && in.OrgId == enum.OrgId {

		orderIdSli := make([]int, len(data))

		for k, v := range data {
			orderIdSli[k] = v.OrderId
		}
		newSession := h.Engine.NewSession()
		defer newSession.Close()

		sql2 := `a.order_id,
		a.salesperson_id,
		GROUP_CONCAT(CONCAT(b.NAME, "/", b.org_name) separator ', ') as salesperson_name,
		GROUP_CONCAT(CONCAT(b.id, "/", b.name, "/", b.org_name) separator ', ') as salesperson_id_name`
		if err := newSession.Table("upetmart.upet_orders_salesperson a").
			Select(sql2).Join("left", "eshop.scrm_salesperson b", "a.salesperson_id=b.id").
			In("a.order_id", orderIdSli).GroupBy("a.order_id").Find(&salespersonData); err != nil {
			log.Errorf("获取分销订单的业务员信息失败-错误为%s", err.Error())
			return nil, 0, errors.New("获取分销订单的业务员信息失败" + err.Error())
		}
		for _, v := range salespersonData {
			OrderIdMapSalesperson[v.OrderId] = SalespersonStru{
				OrderId:           v.OrderId,
				SalespersonId:     v.SalespersonId,
				SalespersonName:   v.SalespersonName,
				SalespersonIdName: v.SalespersonIdName,
			}
		}

	}
	out = make([]vo.DisOrderListView, len(data))
	for k, v := range data {
		OrderFinishTime := ""
		if v.OrderFinishTime > 0 {
			OrderFinishTime = time.Unix(int64(v.OrderFinishTime), 0).Format(utils.DateTimeLayout)
		}
		out[k].StoreId = v.StoreId
		out[k].BuyerId = v.BuyerId
		out[k].BuyerName = v.BuyerName
		out[k].BuyerPhone = v.BuyerPhone
		out[k].EncryptMobile = v.EncryptMobile
		out[k].DistributorId = v.DistributorId
		out[k].DistributorName = v.DistributorName
		out[k].OrderAddTime = time.Unix(int64(v.OrderAddTime), 0).Format(utils.DateTimeLayout)
		out[k].OrderAmount = utils.Yuan2Fen(v.OrderAmount)
		out[k].OrderDisAmount = utils.Yuan2Fen(v.OrderDisAmount)
		out[k].OrderFinishTime = OrderFinishTime
		out[k].OrderSn = v.OrderSn
		out[k].OrderState = v.OrderState
		out[k].RefundAmount = utils.Yuan2Fen(v.RefundAmount)
		out[k].ShippingFee = utils.Yuan2Fen(v.ShippingFee)
		out[k].ShopName = enum.OrgMap[v.StoreId]
		out[k].EnterpriseName = v.EnterpriseName
		out[k].EnterpriseId = v.EnterpriseId
		out[k].SwlmStatus = v.SwlmStatus
		if salespersion, ok := OrderIdMapSalesperson[v.OrderId]; ok {
			out[k].SalespersonName = salespersion.SalespersonName
			out[k].SalespersonIdName = salespersion.SalespersonIdName
		}
		out[k].TuokeSalespersonIdName = v.TuokeSalespersonIdName
		if in.OrgId == enum.BLKYOrgId {
			out[k].ShopName = "百林康源"
		}
		out[k].DisType = v.DisType
		out[k].GoodsId = v.GoodsId
		out[k].GoodsName = v.GoodsName
		out[k].GoodsNum = v.GoodsNum
		out[k].DisCommisRate = v.DisCommisRate
		out[k].GoodsPayPrice = v.GoodsPayPrice
		out[k].CommisAmount = v.CommisAmount
		out[k].LogisticsCode = v.LogisticsCode
		out[k].XiangMa = v.XiangMa
		out[k].DisCommisAmount = v.DisCommisAmount
		out[k].CustomerChannelPath = v.CustomerChannelPath
		out[k].DisEnterpriseName = v.DisEnterpriseName
		out[k].OrgType = v.OrgType
	}

	total = int(count)
	return
}

func (h *DisOrderService) GetDisOrderListApi(in vo.GetDisOrderListApiReq) (out []vo.DisOrderData, total int, total_sale int, RealCommission int, err error) {
	logPrefix := fmt.Sprintf("小程序端获取分销订单列表-分销员会员id为%d", in.MemberId)
	log.Infof("%s - 入参为%s", logPrefix, utils.InterfaceToJSON(in))
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()
	// 根据分销员会员id获取分销员信息
	// 获取分销员信息
	var d distribution_po.DisDistributor
	if exists, err := h.Session.Table("dis_distributor").Where("member_id=?", in.MemberId).Where("org_id=?", in.OrgId).Get(&d); err != nil {
		log.Errorf("%s 获取分销员信息失败-错误为%s", logPrefix, err.Error())
		return nil, 0, 0, 0, errors.New("获取分销员信息失败")
	} else if !exists {
		log.Errorf("%s 未找到分销员信息", logPrefix)
		return nil, 0, 0, 0, errors.New("未找到分销员信息")
	}
	// 如果前端传入的is_show_blky_doctor_order为true，则将shop_id设置为0，表示展示百林康源医生角色时分销订单数据
	if in.IsShowBLKYDoctorOrder {
		d.ShopId = 0
	}

	selectStr := `
	cps.dis_commis_amount as deduct_commission,
	cps.create_time as deduct_commission_time,
	a.logistics_code,
	a.order_id,
	a.order_sn,
	a.order_state,
	a.refund_state,
	CAST(a.order_amount * 100 AS SIGNED) AS order_amount,
	-- 分销商品总金额
	CAST((
		SELECT SUM(og.goods_pay_price * 100)
		FROM upetmart.upet_order_goods og 
		WHERE og.order_id = a.order_id 
		AND og.is_dis = 1
	) AS SIGNED) AS dis_order_amount,
	-- 分销商品总佣金
	CAST((
        SELECT SUM(
                   CASE
                       WHEN r.refund_amount IS NOT NULL
                           THEN (og.goods_pay_price - r.refund_amount) * og.dis_commis_rate / 100 * 100
                       ELSE og.goods_pay_price * og.dis_commis_rate / 100 * 100
                       END
               )
        FROM upetmart.upet_order_goods og
             LEFT JOIN upetmart.upet_refund_return r ON og.rec_id = r.order_goods_id
            AND r.seller_state = 2
            AND r.refund_state = 3
        WHERE og.order_id = a.order_id
          AND og.is_dis = 1
    ) AS SIGNED) AS commission,
 -- 分销商品实际支付金额（减去退款后）
    CAST((
        SELECT SUM(og.goods_pay_price * 100) - IFNULL(SUM(r.refund_amount * 100), 0)
        FROM upetmart.upet_order_goods og 
        LEFT JOIN upetmart.upet_refund_return r ON og.rec_id = r.order_goods_id 
            AND r.seller_state = 2 
            AND r.refund_state = 3
        WHERE og.order_id = a.order_id 
        AND og.is_dis = 1
    ) AS SIGNED) AS pay_amount,
	-- 分销商品退款金额
	CAST((
		SELECT SUM(r.refund_amount * 100)
		FROM upetmart.upet_refund_return r
		INNER JOIN upetmart.upet_order_goods og ON r.order_goods_id = og.rec_id
		WHERE r.order_id = a.order_id 
		AND og.is_dis = 1
		AND r.seller_state = 2 
		AND r.refund_state = 3
	) AS SIGNED) AS refund_amount,
	a.add_time AS order_add_time,
	a.finnshed_time AS order_finish_time,
	c.shop_name,
	dd.name AS dis_name,
	(CASE
		WHEN MIN(d.status) = 1 THEN '待结算'
		WHEN MIN(d.status) = 2 THEN '已结算'
		ELSE ''
	END) AS sett_status_text`
	session.Table("upetmart.upet_orders").Alias("a").Select(selectStr).
		Join("inner", "upetmart.upet_order_goods b", "a.order_id=b.order_id and b.is_dis=1").
		Join("left", "eshop.shop as c", "b.shop_id=c.id").
		Join("left", "eshop.dis_settlement d", "a.order_sn=d.order_no and b.goods_id=d.goods_id").
		Join("left", "eshop.dis_distributor dd", "dd.member_id = b.dis_member_id and dd.org_id = a.store_id").
		Join("left", "eshop.dis_distributor_swlm_cps cps", "a.order_sn=cps.order_sn and a.logistics_code=cps.swlm").
		Where("a.is_dis=1").
		Where("a.store_id=?", in.OrgId).
		Where("a.order_demolition=0")

	if in.Flag == 0 { //自己的分销订单
		session.Where("b.dis_member_id=?", in.MemberId)
	} else if in.Flag == 1 { //店铺分销订单
		if d.DisRole != 1 {
			err = errors.New("只有老板才可以查看店铺分销订单")
			return
		}

		if in.DyMemberId > 0 {
			session.Where("b.dis_member_id=?", in.DyMemberId)
		}

	} else {
		err = errors.New("入参错误")
		return
	}
	if in.SaasShopId != "" {
		session.Where("c.saas_shop_id=?", in.SaasShopId)
	} else {
		session.Where("b.shop_id=?", d.ShopId)
	}

	if in.SearchValue != "" {
		session.Where("a.order_sn=? or a.encrypt_mobile =? or b.goods_name like ?", cast.ToInt64(in.SearchValue), utils.MobileEncrypt(in.SearchValue), "%"+in.SearchValue+"%")
	}

	if len(in.Where) > 0 {
		//查询条件的类型（订单号order_sn，name=分销员姓名，belong_salesman_name=业务员姓名，member_mobile=买家手机号，dis_id=分销员id,
		//belong_salesman_id=业务员ID，member_name=买家账号，线下企业
		switch in.WhereType {
		case "":
		case "id":
			session.And("dd.id = ?", in.Where)
		case "name":
			if in.OrgId == 4 {
				session.And("dd.real_name LIKE '%" + in.Where + "%'")
			} else {
				session.And("dd.name LIKE '%" + in.Where + "%'")
			}
		case "mobile":
			session.And("dd.encrypt_mobile = ?", utils.MobileEncrypt(in.Where))
		}
	}
	if in.OrderState > 0 {
		switch in.OrderState {
		case 1:
			session.In("a.order_state", []int{upetorders.StateNew, upetorders.StatePay, upetorders.StateSend, upetorders.StatePartSend})
		case 2:
			session.Where("a.order_state=?", upetorders.StateCancel)
		case 3:
			session.Where("a.order_state=?", upetorders.StateSuccess)
		}
	}

	//结算状态
	if in.SettStatus > 0 {
		session.Where("d.status=?", in.SettStatus)
	}
	// 下单时间
	if in.AddTimeStart != "" {
		if AddTimeStart, err := time.ParseInLocation(utils.DateTimeLayout2, in.AddTimeStart, time.Local); err != nil {
			return nil, 0, 0, 0, err
		} else {
			session.Where("a.add_time >=?", AddTimeStart.Unix())
		}
	}
	if in.AddTimeEnd != "" {
		if AddTimeEnd, err := time.ParseInLocation(utils.DateTimeLayout2, in.AddTimeEnd, time.Local); err != nil {
			return nil, 0, 0, 0, err
		} else {
			session.Where("a.add_time <=?", AddTimeEnd.Unix())
		}
	}

	switch in.StatsType {
	case 1: // 统计总销售额
		type SumStruct struct {
			GoodsPayPrice float64
		}
		ss := new(SumStruct)
		totalSale, err := session.Sum(ss, "goods_pay_price")
		if err != nil {
			log.Errorf("%s 获取总销售额失败-错误是%s", logPrefix, err.Error())
			err = errors.New("获取总销售额失败")
			return nil, 0, 0, 0, err
		}
		total_sale = cast.ToInt(totalSale * 100)
		return out, total, total_sale, 0, nil
	case 2: // 宠利扫统计总登记佣金(总佣金-扣除佣金)
		var realCommissionfloat float64
		_, err = session.Select("(sum(b.goods_pay_price * b.dis_commis_rate / 100 * 100) - sum(ifnull(cps.dis_commis_amount,0))) as deduct_commission").Get(&realCommissionfloat)
		if err != nil {
			log.Errorf("%s 获取总登记佣金失败-错误是%s", logPrefix, err.Error())
			err = errors.New("获取总登记佣金失败")
			return nil, 0, 0, 0, err
		}
		RealCommission = cast.ToInt(realCommissionfloat)
		return out, total, total_sale, RealCommission, nil
	}
	out = make([]vo.DisOrderData, 0)
	count, err := session.OrderBy("a.add_time desc").Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).GroupBy("a.order_id").FindAndCount(&out)
	if err != nil {
		log.Errorf("%s 获取订单失败-错误是%s", logPrefix, err.Error())
		err = errors.New("获取订单失败")
		return
	}
	total = int(count)
	orderIdSli := make([]int, len(out))
	for k, v := range out {
		orderIdSli[k] = v.OrderId
	}
	goodsList := make([]*vo.GoodsInfoExt, 0)
	if err = session.Table("upetmart.upet_order_goods").
		Alias("og").
		Select("og.order_id, og.goods_name,og.goods_image,og.goods_spec,og.goods_pay_price as goods_price,og.goods_num,og.dis_commis_rate,og.is_dis, CAST(IFNULL(r.refund_amount*100, 0) AS SIGNED) as refund_amount, r.order_goods_id").
		Join("LEFT", "upetmart.upet_refund_return r", "og.order_id = r.order_id and (((og.rec_id = r.order_goods_id OR r.order_goods_id = 0) AND r.seller_state = 2 AND r.refund_state = 3) OR r.refund_id IS NULL)").
		Where("og.is_dis = ?", 1).
		Where("og.store_id = ?", in.OrgId).
		In("og.order_id", orderIdSli).
		Find(&goodsList); err != nil {
		log.Errorf("%s -获取订单商品信息失败-错误是%s", logPrefix, err.Error())
		err = errors.New("获取订单商品信息失败")
		return
	}

	orderMapGoods := make(map[int][]*vo.GoodsInfoExt)
	imgDomain := fmt.Sprintf(enum.BBCImgPath, in.OrgId)
	for _, v := range goodsList {
		if _, ok := orderMapGoods[v.OrderId]; !ok {
			orderMapGoods[v.OrderId] = make([]*vo.GoodsInfoExt, 0)
		}
		if !strings.HasPrefix(v.GoodsImage, "https://") && !strings.HasPrefix(v.GoodsImage, "http://") {
			v.GoodsImage = imgDomain + v.GoodsImage
		}
		orderMapGoods[v.OrderId] = append(orderMapGoods[v.OrderId], v)
		// 佣金金额计算（浮点型计算，避免精度丢失）
		decimalValue := (decimal.NewFromFloat(v.GoodsPrice)).Mul(decimal.NewFromFloat(v.DisCommisRate / 100))
		v.DisComm, _ = decimalValue.Round(2).Float64()
		//判断是否整单退款
		if v.OrderGoodsId == 0 && v.RefundAmount > 0 {
			v.RefundAmount = utils.Yuan2Fen(v.GoodsPrice)
		}

		// 泛解析商品规格
		specValue, err := serialize.UnMarshal([]byte(v.GoodsSpec)) // 使用 Unserialize 反序列化
		if err != nil || specValue == nil {
			continue
		}

		for _, vv := range specValue.(map[string]interface{}) {
			spec := vv.(string)
			v.GoodsSpec = spec
		}

	}

	for k, v := range out {
		out[k].GoodsList = orderMapGoods[v.OrderId]
		out[k].OrderStateText = upetorders.StateMap[v.OrderState]

	}

	// 计算总销售额
	// type SumStruct struct {
	// 	GoodsPayPrice float64
	// }
	// ss := new(SumStruct)
	// session2 := h.Engine.NewSession()
	// defer session2.Close()
	// session2.Table("upetmart.upet_orders").Alias("a").Select("sum(b.goods_pay_price) as goods_pay_price").
	// 	Join("inner", "upetmart.upet_order_goods b", "a.order_id=b.order_id and b.is_dis=1").
	// 	Join("left", "eshop.shop as c", "b.shop_id=c.id").
	// 	Join("left", "eshop.dis_settlement d", "a.order_sn=d.order_no and b.goods_id=d.goods_id").
	// 	Where("a.is_dis=1").
	// 	Where("a.store_id=?", in.OrgId).
	// 	Where("a.order_demolition=0")
	// if in.Flag == 0 { //自己的分销订单
	// 	session2.Where("b.dis_member_id=?", in.MemberId)
	// } else if in.Flag == 1 { //店铺分销订单
	// 	if d.DisRole != 1 {
	// 		err = errors.New("只有老板才可以查看店铺分销订单")
	// 		return
	// 	}
	// 	session2.Where("b.shop_id=?", d.ShopId)
	// 	if in.DyMemberId > 0 {
	// 		session2.Where("b.dis_member_id=?", in.DyMemberId)
	// 	}
	// } else {
	// 	err = errors.New("入参错误")
	// 	return
	// }
	// if in.SearchValue != "" {
	// 	session2.Where("a.order_sn=? or a.encrypt_mobile =? or b.goods_name like ?", cast.ToInt64(in.SearchValue), utils.MobileEncrypt(in.SearchValue), "%"+in.SearchValue+"%")
	// }
	// if in.OrderState > 0 {
	// 	switch in.OrderState {
	// 	case 1:
	// 		session2.In("a.order_state", []int{upetorders.StateNew, upetorders.StatePay, upetorders.StateSend, upetorders.StatePartSend})
	// 	case 2:
	// 		session2.Where("a.order_state=?", upetorders.StateCancel)
	// 	case 3:
	// 		session2.Where("a.order_state=?", upetorders.StateSuccess)
	// 	}
	// }
	// //结算状态
	// if in.SettStatus > 0 {
	// 	session2.Where("d.status=?", in.SettStatus)
	// }
	// // 下单时间
	// if in.AddTimeStart != "" {
	// 	if AddTimeStart, err := time.ParseInLocation(utils.DateTimeLayout2, in.AddTimeStart, time.Local); err != nil {
	// 		return nil, 0, 0, err
	// 	} else {
	// 		session2.Where("a.add_time >=?", AddTimeStart.Unix())
	// 	}
	// }
	// if in.AddTimeEnd != "" {
	// 	if AddTimeEnd, err := time.ParseInLocation(utils.DateTimeLayout2, in.AddTimeEnd, time.Local); err != nil {
	// 		return nil, 0, 0, err
	// 	} else {
	// 		session2.Where("a.add_time <=?", AddTimeEnd.Unix())
	// 	}
	// }
	// totalSale, err := session2.Sum(ss, "goods_pay_price")
	// total_sale = cast.ToInt(totalSale * 100)
	return

}

// 百林康源 医生分销订单 直接插入已结算的数据
// 直接插入 已结算的数据
func (h *DisOrderService) OrderSetted(in vo.OrderSettedReq) error {
	if len(in.Data) == 0 {
		return errors.New("入参不能为空")
	}
	logPrefix := fmt.Sprintf("医生分销订单后，直接插入一条已结算的数据，主体=%d,电商会员id=%d|order_no=%s", in.Data[0].OrgId, in.Data[0].MemberId, in.Data[0].OrderNo)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	h.Begin()
	defer h.Close()
	var disData distribution_po.DisDistributor
	if exists, err := h.Session.Table("dis_distributor").Where("org_id=?", in.Data[0].OrgId).Where("member_id=?", in.Data[0].MemberId).Get(&disData); err != nil {
		log.Error(logPrefix, "查询分销员信息失败：err=", err.Error())
		return errors.New("查询分销员信息失败")
	} else if !exists {
		log.Error(logPrefix, "分销员信息不存在")
		return errors.New("分销员信息不存在")
	}

	// redis锁 防止并发修改已结算数据
	// cacheK := fmt.Sprintf(cachekey.WithdrawAmountUpdate, in.Data[0].OrgId, 0, disData.Id)
	// var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	// setNxReslt := mCache.TryLock(string(cache_source.EShop), cacheK, time.Minute*10)
	// if !setNxReslt {
	// 	log.Errorf("%s-设置redis锁(%s)失败", logPrefix, cacheK)
	// 	return errors.New("数据处理失败， 请稍后再试")
	// }
	// defer mCache.Delete(string(cache_source.EShop), cacheK)
	session := h.Engine.NewSession()
	defer session.Close()

	for _, in := range in.Data {
		session.Begin()
		// 目前只有百林康源 医生分销订单 直接就是已结算了
		if in.OrgId != enum.BLKYOrgId {
			return errors.New("目前仅支持百林康源调用")
		}
		if strings.Trim(in.OrderNo, " ") == "" || in.GoodsId == 0 {
			return errors.New("订单编号为空或商品skuid")
		}

		// 判断该订单该商品是否已经存在了结算表数据 如果存在 ， 则丢弃
		var settData distribution_po.DisSettlement
		if exists, err := session.Table("dis_settlement").Where("org_id=?", in.OrgId).Where("order_no=?", in.OrderNo).Where("goods_id=?", in.GoodsId).Get(&settData); err != nil {
			log.Error(logPrefix, "查询数据失败：err=", err.Error())
			return errors.New("查询数据失败")
		} else if exists {
			log.Error(logPrefix, "结算数据已经存在，数据如下：", utils.InterfaceToJSON(settData))
			return errors.New("结算数据已经存在")
		}

		OrderTime, _ := time.ParseInLocation(utils.DateTimeLayout, in.OrderTime, time.Local)
		OrderFinishTime, _ := time.ParseInLocation(utils.DateTimeLayout, in.OrderFinishTime, time.Local)

		decimalValue := decimal.NewFromFloat(cast.ToFloat64(in.PayAmount)).Mul(decimal.NewFromFloat(in.CommissionRate)).Mul(decimal.NewFromFloat(0.01))
		comm := decimalValue.Round(0).IntPart()
		//插入一条已结算的记录 dis_dis_settlement
		settData = distribution_po.DisSettlement{
			OrgId:           in.OrgId,
			ShopId:          in.ShopId,
			Status:          dissettlement.StatusSetted,
			SettlementNo:    utils.GenerateNo(enum.SettlementNoPrefix),
			SettlementTime:  time.Now(),
			OrderNo:         in.OrderNo,
			OrderTime:       OrderTime,
			OrderFinishTime: OrderFinishTime,
			GoodsId:         in.GoodsId,
			GoodsName:       in.GoodsName,
			PayAmount:       in.PayAmount,
			RefundAmount:    0,
			CommissionRate:  in.CommissionRate,
			DistributorId:   disData.Id,
			Commission:      cast.ToInt(comm),
		}

		// 第一步： 插入结算数据
		if _, err := session.Insert(&settData); err != nil {
			session.Rollback()
			log.Error(logPrefix, "插入结算数据失败，err=", err.Error())
			return errors.New("插入结算数据失败")
		}

		// 第二步： 更新 dis_distributor.settled_commission
		sql := `update eshop.dis_distributor set settled_commission = settled_commission + ?  where id=? and  org_id =? and member_id =?`
		if _, err := session.Exec(sql, comm, disData.Id, in.OrgId, in.MemberId); err != nil {
			session.Rollback()
			log.Error(logPrefix, "更新分销员的已结算佣金失败,err=", err.Error())
			return errors.New("更新分销员的已结算佣金失败")
		}

		// 第三步： 更新dis_distributor_total.settled_commission和 dis_distributor_total.wait_withdraw

		sql = `update eshop.dis_distributor_total set settled_commission = settled_commission +?,wait_withdraw=wait_withdraw+? where dis_id=? and org_id =? and member_id =? and shop_id =?;`
		if _, err := session.Exec(sql, comm, comm, disData.Id, in.OrgId, in.MemberId, in.ShopId); err != nil {
			session.Rollback()
			log.Error(logPrefix, "更新分销统计数据失败，err=", err.Error())
			return errors.New("更新分销统计数据失败")
		}

		sql = `update eshop.shop set settled_commission = settled_commission +?,wait_withdraw=wait_withdraw+? where  org_id =? and id =?;`
		if _, err := session.Exec(sql, comm, comm, in.OrgId, in.ShopId); err != nil {
			session.Rollback()
			log.Error(logPrefix, "更新店铺分销统计数据失败，err=", err.Error())
			return errors.New("更新店铺分销统计数据失败")
		}

		// 第四步： 插入 dis_withdraw_record记录
		var insertRecord = distribution_po.DisWithdrawRecord{
			OrgId:           in.OrgId,
			ShopId:          in.ShopId,
			DisId:           disData.Id,
			WithdrawDisId:   0,
			ThirdId:         cast.ToInt64(settData.Id),
			CreateTime:      time.Now(),
			UpdateTime:      time.Now(),
			Type:            diswithdrawrecord.TypeSetted,
			WaitWithdraw:    cast.ToInt(comm),
			WithdrawApply:   0,
			WithdrawSuccess: 0,
		}

		if _, err := session.Table("eshop.dis_withdraw_record").Insert(&insertRecord); err != nil {
			log.Error(logPrefix, "插入结算记录失败：err=", err.Error())
			session.Rollback()
			return errors.New("更新结算记录失败" + err.Error())
		}
		session.Commit()

	}

	return nil

}
