package services

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

// UpetUserService 用户服务
type UpetUserService struct {
	common.BaseService
}

// NewUpetUserService 创建用户服务实例
func NewUpetUserService() *UpetUserService {
	return &UpetUserService{}
}

// GetExtremeJoyUserList 获取极宠家(org_id=2)用户列表
func (s *UpetUserService) GetUserList(req vo.UserPageReq) ([]vo.VoUserMemberView, int64, error) {
	s.<PERSON>gin()
	defer s.Close()

	// 参数验证
	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	var users []vo.VoUserMemberView
	session := s.Engine.Table("eshop.users").Alias("u").
		Select("um.member_id, um.scrm_user_id, um.member_name, su.user_sex as member_sex, um.member_mobile, u.create_time, um.member_login_time,um.user_level_id,u.org_id,su.first_raises_pet").
		Join("LEFT", "upetmart.upet_member um", "um.member_id = u.member_id").
		Join("LEFT", "scrm_organization_db.t_scrm_user_info su", "su.user_id = um.scrm_user_id")

	// 添加查询条件
	session.Where("u.org_id = ?", req.OrgId)

	if req.Mobile != "" {
		session.Where("um.member_mobile = ?", req.Mobile)
	}
	if req.MemberName != "" {
		session.Where("um.member_name = ?", req.MemberName)
	}
	if req.MemberId != 0 {
		session.Where("um.member_id = ?", req.MemberId)
	}
	if req.RegisterStartTime != "" {
		session.Where("u.create_time >= ?", utils.Date2Timestamp(req.RegisterStartTime))
	}
	if req.RegisterEndTime != "" {
		session.Where("u.create_time <= ?", utils.Date2Timestamp(req.RegisterEndTime))
	}

	// 查询用户基本信息
	total, err := session.Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&users)

	if err != nil {
		log.Errorf("客户列表查询失败: %v", err)
		return nil, 0, fmt.Errorf("客户列表查询失败: %v", err)
	}

	// 如果有用户数据，查询订单统计信息
	if len(users) > 0 {
		// 收集所有scrm_user_id
		scrmUserIds := make([]string, 0, len(users))
		for _, user := range users {
			scrmUserIds = append(scrmUserIds, user.ScrmUserId)
		}

		// 查询订单统计信息
		var orderStats []struct {
			MemberId      string    `xorm:"member_id"`
			OrderCount    int       `xorm:"count"`
			LastOrderTime time.Time `xorm:"max_time"`
		}

		err = s.Engine.Table("dc_order.order_main").
			Select("member_id, COUNT(*) as count, MAX(create_time) as max_time").
			In("member_id", scrmUserIds).
			Where("org_id = ? and parent_order_sn != '' and is_pay = 1", req.OrgId). // 有效订单
			GroupBy("member_id").
			Find(&orderStats)

		if err != nil {
			log.Errorf("查询用户订单统计失败: %v", err)
			return nil, 0, fmt.Errorf("查询订单统计失败: %v", err)
		}

		// 将订单统计信息填充到用户信息中
		statsMap := make(map[string]vo.OrderStats)
		for _, stat := range orderStats {
			statsMap[stat.MemberId] = vo.OrderStats{
				Count:    stat.OrderCount,
				LastTime: stat.LastOrderTime,
			}
		}

		// 更新用户信息
		for i := range users {
			//手机号加星
			users[i].MemberMobile = utils.AddStar(users[i].MemberMobile)
			//时间戳转时间
			users[i].MemberLoginTime = time.Unix(cast.ToInt64(users[i].MemberLoginTime), 0).Format("2006-01-02 15:04:05")
			if stat, ok := statsMap[users[i].ScrmUserId]; ok {
				users[i].OrderCount = stat.Count
				users[i].LastPurchaseTime = stat.LastTime.Format("2006-01-02 15:04:05")
			}
		}
	}

	return users, total, nil
}

// GetUserDetail 获取用户详情
func (s *UpetUserService) GetUserDetail(req vo.UserDetailReq) (*vo.UserDetail, error) {
	s.Begin()
	defer s.Close()

	// 查询用户基本信息
	var detail vo.UserDetail
	if _, err := s.Engine.Table("eshop.users").Alias("u").
		Select("um.scrm_user_id,um.member_id as user_id, um.member_name as user_name, um.member_mobile as mobile, su.user_avatar,su.user_sex as sex,su.user_birthday as birthday,CONCAT(COALESCE(su.province,''), COALESCE(su.city,''), COALESCE(su.area,'')) as address, um.member_time as register_time, um.member_login_time as last_login_time,su.first_raises_pet").
		Join("LEFT", "upetmart.upet_member um", "um.scrm_user_id = u.scrm_user_id").
		Join("LEFT", "scrm_organization_db.t_scrm_user_info su", "su.user_id = um.scrm_user_id").
		Where("u.org_id = ? AND um.member_id = ?", req.OrgId, req.MemberId).
		Get(&detail); err != nil {
		return nil, fmt.Errorf("查询用户基本信息失败: %v", err)
	}
	if detail.UserId == 0 {
		return nil, fmt.Errorf("用户不存在")
	}

	// 查询扫码验真伪渠道统计
	var channelStats struct {
		HomeCount     int `xorm:"home_count"`
		WechatCount   int `xorm:"wechat_count"`
		PersonalCount int `xorm:"personal_count"`
	}

	if _, err := s.Engine.Table("eshop.verify_record").
		Select("COUNT(CASE WHEN source = 1 THEN 1 END) as home_count, "+
			"COUNT(CASE WHEN source = 2 THEN 1 END) as wechat_count, "+
			"COUNT(CASE WHEN source = 3 THEN 1 END) as personal_count").
		Where("user_id = ?", detail.ScrmUserId).
		Get(&channelStats); err != nil {
		return nil, fmt.Errorf("查询扫码统计失败: %v", err)
	}

	detail.WechatCount = channelStats.WechatCount
	detail.HomeCount = channelStats.HomeCount
	detail.PersonalCount = channelStats.PersonalCount
	detail.Count = channelStats.HomeCount + channelStats.WechatCount + channelStats.PersonalCount

	// 手机号加密处理
	detail.Mobile = utils.AddStar(detail.Mobile)

	return &detail, nil
}

// GetUserPhone 根据会员ID获取用户手机号
func (s *UpetUserService) GetUserPhone(req vo.UserPhoneReq) (vo.UserPhoneData, error) {
	s.Begin()
	defer s.Close()
	var result vo.UserPhoneData
	// 查询用户信息
	var userInfo struct {
		MemberId     string `xorm:"member_id"`
		MemberMobile string `xorm:"member_mobile"`
	}
	has, err := s.Engine.Table("upetmart.upet_member").Alias("um").
		Select("um.member_id, um.member_mobile").
		Where("um.member_id = ?", req.MemberId).
		Get(&userInfo)
	if err != nil {
		return result, fmt.Errorf("查询用户信息失败: %v", err)
	}

	if !has {
		return result, fmt.Errorf("用户不存在")
	}

	if userInfo.MemberId == "" {
		return result, fmt.Errorf("未找到该会员信息")
	}

	result.Mobile = userInfo.MemberMobile

	return result, nil
}
