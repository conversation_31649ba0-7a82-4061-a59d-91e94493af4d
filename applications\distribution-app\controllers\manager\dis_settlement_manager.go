package manager

import (
	tasklist "eShop/infra/enum"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

/**
管理后台 - 分销员结算管理
*/

// @Summary 获取结算列表接口 @fuma-v1.3
// @Tags 后台接口-结算管理
// @Accept  plain
// @Produce  json
// @Param GetDisSettlementListReq query distribution_vo.GetDisSettlementListReq true " "
// @Success 200 {object} distribution_vo.GetDisSettlementListRes
// @Failure 400 {object} distribution_vo.GetDisSettlementListRes
// @Router /manager/dis/settlement/list [GET]
func GetDisSettlementList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.GetDisSettlementListRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.GetDisSettlementListReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("获取结算列表失败-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	server := services.DisSettlementService{}
	if out.Data, out.Total, err = server.GetDisSettlementList(param); err != nil {
		log.Errorf("获取结算列表失败-错误为%s", err.Error())
		out.Message = "获取结算列表失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// @Summary 导出结算列表
// @Tags 后台接口-结算管理
// @Accept  json
// @Produce  json
// @Param GetDisSettlementListReq body distribution_vo.GetDisSettlementListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/settlement/export [POST]
func DisSettlementExport(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400

	param, err := utils.Bind[distribution_vo.GetDisSettlementListReq](request)
	param.OrgId = cast.ToInt(request.Header.Get("org_id"))
	if err != nil {
		log.Errorf("导出结算列表-参数解析失败-err=%s", err.Error())
		out.Message = fmt.Sprintf("导出结算列表列表，参数解析失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	s := common.TaskListService{}
	var task distribution_vo.TaskList
	par, _ := json.Marshal(param)
	task.OperationFileUrl = string(par)
	task.OrgId = cast.ToInt(request.Header.Get("org_id"))
	task.TaskContent = tasklist.TaskContentDisSettlementExport
	err = s.CreatTask(request, task)
	if err != nil {
		log.Errorf("导出结算列表失败：err=%s", err.Error())
		out.Message = fmt.Sprintf("导出结算列表失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200

	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}
