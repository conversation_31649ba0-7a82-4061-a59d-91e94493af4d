package omnibus_po

import (
	"context"
	"errors"
	"time"

	"eShop/infra/log"
	"eShop/infra/utils"

	"xorm.io/xorm"
)

type Store struct {
	Id                int       `json:"id" xorm:"pk autoincr not null comment('自增id') INT 'id'"`
	Name              string    `json:"name" xorm:"default '' comment('门店名称') VARCHAR(50) 'name'"`
	Shortname         string    `json:"shortname" xorm:"default 'null' comment('门店简称') VARCHAR(56) 'shortname'"`
	StoreCode         string    `json:"storeCode" xorm:"default 'null' comment('店铺编码') VARCHAR(56) 'storeCode'"`
	FinanceCode       string    `json:"finance_code" xorm:"not null comment('财务编码') VARCHAR(50) 'finance_code'"`
	ZilongId          string    `json:"zilong_id" xorm:"default '' comment('子龙门店id，systemid') VARCHAR(50) 'zilong_id'"`
	PointX            string    `json:"pointX" xorm:"default '' comment('定位信息经度') VARCHAR(56) 'pointX'"`
	PointY            string    `json:"pointY" xorm:"default '' comment('定位信息纬度') VARCHAR(56) 'pointY'"`
	Bigregion         string    `json:"bigregion" xorm:"default '' comment('大区') VARCHAR(56) 'bigregion'"`
	Provinceid        int       `json:"provinceid" xorm:"default 'null' comment('省id') INT 'provinceid'"`
	Province          string    `json:"province" xorm:"default '' comment('省份') VARCHAR(56) 'province'"`
	Cityid            int       `json:"cityid" xorm:"default 'null' comment('城市id') INT 'cityid'"`
	City              string    `json:"city" xorm:"default '' comment('市') VARCHAR(56) 'city'"`
	Countyid          int       `json:"countyid" xorm:"default 'null' comment('县区id') INT 'countyid'"`
	County            string    `json:"county" xorm:"default '' comment('县区') VARCHAR(56) 'county'"`
	Address           string    `json:"address" xorm:"default '' comment('店铺地址') VARCHAR(128) 'address'"`
	Desc              string    `json:"desc" xorm:"default '' comment('店铺简介') VARCHAR(256) 'desc'"`
	CustomCode        string    `json:"custom_code" xorm:"default 'null' comment('全渠道往来单位(A8)') VARCHAR(255) 'custom_code'"`
	ElmDelivery       int       `json:"elm_delivery" xorm:"default 11 comment('饿了么商户配送方式 9：蜂鸟快送，11：星火众包') INT 'elm_delivery'"`
	CreateTime        time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime        time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	AppChannel        int       `json:"app_channel" xorm:"default 0 comment('1.阿闻自有,2.TP代运营') INT 'app_channel'"`
	DeliveryMethod    int8      `json:"delivery_method" xorm:"default 1 comment('配送方式 1发外卖配送 2 发物流配送 3不发配送(商家自配)') TINYINT 'delivery_method'"`
	SellDrugs         int8      `json:"sell_drugs" xorm:"default 0 comment('是否有资质售药') TINYINT 'sell_drugs'"`
	DrugsChannelIds   string    `json:"drugs_channel_ids" xorm:"default '' comment('药品上架渠道,多个用英文逗号隔开') VARCHAR(50) 'drugs_channel_ids'"`
	ChainId           int64     `json:"chain_id" xorm:"default 0 comment('连锁ID') BIGINT 'chain_id'"`
	OrgId             int       `json:"org_id" xorm:"not null default 1 INT 'org_id'"`
	SourceChainId     int64     `json:"source_chain_id" xorm:"-"`     // 来源连锁ID（这个是账号所属连锁）
	AuthorizedChainId int64     `json:"authorized_chain_id" xorm:"-"` // 授权连锁ID （这个代表店铺交给哪个连锁运营）
}

type StoreChannel struct {
	FinanceCode    string `json:"finance_code" xorm:"not null comment('财务编码') VARCHAR(50) 'finance_code'"`
	AppChannel     int    `json:"app_channel" xorm:"default 0 comment('1.阿闻自有,2.TP代运营') INT 'app_channel'"`
	ChannelStoreId string `json:"channel_store_id" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
	ChannelId      int    `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') INT 'channel_id'"`
}

func (t Store) TableName() string {
	return "datacenter.store"
}

// NewStore 创建Store结构体实例
// 返回值:
//   - *Store: Store结构体指针
func NewStore() *Store {
	return &Store{}
}

// GetStores 根据用户ID获取门店信息
// 参数说明:
//   - db: xorm数据库引擎实例
//   - where: SQL查询条件，必须包含 user_id，可选包含 chain_id
//
// 返回值:
//   - stores: 门店信息列表
//   - err: 错误信息
func (s Store) GetStores(ctx context.Context, db *xorm.Engine, where map[string]interface{}) (stores []Store, storeMap map[string]Store, err error) {
	session := db.NewSession()
	defer session.Close()
	log.Info("GetStores根据用户ID获取门店信息", "入参where-", utils.JsonEncode(where))

	session = session.Table("eshop_saas.t_employee").Alias("e").
		Join("INNER", "eshop_saas.t_tenant t", "e.tenant_id = t.id AND t.is_deleted = 0 AND t.state = 1").
		Join("INNER", "eshop_saas.t_tenant_retail_cfg cfg", "t.id = cfg.tenant_id").
		Join("INNER", "datacenter.store s", "s.finance_code = CONVERT(t.id,CHAR(50))").
		Where("e.is_deleted=0")

	session = session.Select("s.*, e.source_chain_id, cfg.authorized_chain_id")

	// 检查必要的查询条件
	if userId, ok := where["userId"]; ok {
		session = session.Where("e.user_id = ?", userId)

	}

	if storeId, ok := where["storeId"]; ok {
		session = session.Where("t.id = ?", storeId)
	}

	stores = make([]Store, 0)
	storeMap = make(map[string]Store)
	err = session.Find(&stores)
	if err != nil {
		return
	}
	outType := 0
	if v, ok := where["outType"]; ok {
		outType = v.(int)
	}
	for _, store := range stores {
		switch outType {
		case 1:
			storeMap[store.FinanceCode] = store
		}
	}

	return
}

// 根据userId获取门店列表
func (s Store) GetStoreIdsByUserId(ctx context.Context, engine *xorm.Engine, userId string) (storeIds []string, err error) {
	logPrefix := "获取门店列表:"

	// 获取门店列表
	storeData, _, err := s.GetStores(ctx, engine, map[string]interface{}{
		"userId": userId,
	})
	if err != nil {
		log.Error(logPrefix, "获取门店列表失败，err=", err.Error())
		return nil, errors.New("获取门店列表失败")
	}
	// 提取门店ID
	for _, store := range storeData {
		storeIds = append(storeIds, store.FinanceCode)
	}
	return storeIds, nil
}

// 获取店铺信息
func (s *Store) GetStoreInfoByFinanceCode(session *xorm.Session, financeCodes []string) (storeMap map[string]Store, err error) {
	if len(financeCodes) == 0 {
		err = errors.New("financeCodes is empty")
		return
	}
	data := make([]Store, 0)
	storeMap = make(map[string]Store)
	if err = session.Table("datacenter.store").In("finance_code", financeCodes).Find(&data); err != nil {
		return
	}

	storeMap = make(map[string]Store)
	for _, v := range data {
		storeMap[v.FinanceCode] = v
	}
	return
}
