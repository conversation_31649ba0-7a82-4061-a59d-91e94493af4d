package points_vo

import (
	po "eShop/domain/points-po"
)

type ClsPointsRuleSaveVO struct {
	SuperSaveVO[po.ClsPointsRule]
	GoodsCode string `json:"goods_code" validate:"required"`
	Type      int    `json:"type" validate:"required"`
	StartTime string `json:"start_time" validate:"required"`
	EndTime   string `json:"end_time" validate:"required"`
}

type ClsPointsRuleUpdateVO struct {
	SuperUpdateVO[po.ClsPointsRule]
	GoodsCode    string         `json:"goods_code" validate:"required"`
	Type         int            `json:"type" validate:"required"`
	RuleTimeItem []RuleTimeItem `json:"rules"`
	Operator     string         `json:"operator"`
}

type ClsPointsRuleQueryVO struct {
	SuperQueryVO[po.ClsPointsRule]
	GoodsName     string `json:"goods_name" query:"upet_goods.goods_name:like"`
	GoodsCode     string `json:"goods_code" query:"cls_points_rule.goods_code:eq"`
	SkuId         string `json:"sku_id" query:"upet_goods.goods_id:eq"`
	EffectiveTime string `json:"effective_time" query:"cls_points_rule.start_time:lte;cls_points_rule.end_time:gte"`
	Type          int    `json:"type" query:"cls_points_rule.type:eq" validate:"required,oneof=1 2"`
	StartTime     string `json:"start_time" query:"cls_points_rule.end_time:gte"`
	EndTime       string `json:"end_time" query:"cls_points_rule.start_time:lte"`
	Status        []int  `json:"status" query:"cls_points_rule.status:in"`
}

type ClsPointsRuleResultVO struct {
	SuperResultVO[ClsPointsRuleResultVO] `xorm:"extends"`
	Id                                   int    `json:"id"` // 主键ID
	GoodsCode                            string `json:"goods_code"`
	Type                                 int    `json:"type"` // 规则类型：1-体系内，2-体系外
	StartTime                            string `json:"start_time"`
	EndTime                              string `json:"end_time"`
	Region                               string `json:"region"`
	Points                               int    `json:"points"`
	Status                               int    `json:"status"`
}

type ClsPointsRuleBatchSaveVO struct {
	GoodsCode []string     `json:"goods_code" validate:"required"`
	Type      int          `json:"type" validate:"required"`
	StartTime string       `json:"start_time" validate:"required"`
	EndTime   string       `json:"end_time" validate:"required"`
	RuleItem  []RegionItem `json:"rule_item"`
	Status    int          `json:"status" validate:"required"`
}

type MultiRuleResultVO struct {
	Id          string  `json:"id"`           // 主键ID，多个id英文逗号分隔
	SpuId       string  `json:"spu_id"`       // SPU ID(upet_goods.goods_commonid)
	SkuId       string  `json:"sku_id"`       // SKU ID(upet_goods.goods_id)
	GoodsCode   string  `json:"goods_code"`   // A8编号
	GoodsName   string  `json:"goods_name"`   // 商品名称(upet_goods.goods_name)
	MarketPrice float64 `json:"market_price"` // 市场价(upet_goods.goods_marketprice)
	Price       float64 `json:"price"`        // 价格(upet_goods.goods_price)
	Status      int     `json:"status"`       // 规则状态：1-启用，2-禁用
	Points      string  `json:"points"`       // 积分
	Region      string  `json:"region"`       // 地区
}

type ClsPointsRuleDetailVO struct {
	GoodsCode    string         `json:"goods_code"` // 商品编码
	Type         int            `json:"type"`       // 规则类型：1-体系内，2-体系外
	RuleTimeItem []RuleTimeItem `json:"rules"`
}

type RuleTimeItem struct {
	StartTime string       `json:"start_time" validate:"required"`
	EndTime   string       `json:"end_time" validate:"required"`
	RuleItem  []RegionItem `json:"rule_item" validate:"required"`
	Status    int          `json:"status" validate:"required"`
}

type RegionItem struct {
	Id     int    `json:"id"` // 主键ID数组
	Region string `json:"region" validate:"required"`
	Points int    `json:"points" validate:"required"`
}
