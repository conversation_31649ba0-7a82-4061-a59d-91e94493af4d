package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	base_service "eShop/services/base-service"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// DistributorPage 分页查询
// @Summary 分页查询
// @Description 分销员列表查询接口
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DistributorPageReq body distribution_vo.DistributorPageReq true " "
// @Success 200 {object} distribution_vo.DistributorPageResp
// @Failure 400 {object} distribution_vo.DistributorPageResp
// @Router /manager/distributor/page-list [GET]
func DistributorPage(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.DistributorPageResp{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.DistributorPageReq](request)
	orgId := request.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("分销员列表查询，参数解析失败：err=", err.Error())
		resp.BasePageHttpResponse.Message = err.Error()
	} else {
		service := services.DistributorManageService{}
		list, total, err := service.DistributorPage(req)
		if err != nil {
			log.Error("分销员列表查询失败：err=", err.Error())
			resp.BasePageHttpResponse.Message = err.Error()
		} else {
			resp.BasePageHttpResponse.Code = 200
			resp.BasePageHttpResponse.Total = total
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// DistributorExport 导出分销员
// @Summary 导出分销员
// @Description 导出分销员接口
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DistributorPageReq body distribution_vo.DistributorPageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/distributor/export [POST]
func DistributorExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DistributorPageReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("导出分销员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("导出分销员，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 7
		err := s.CreatTask(r, task)
		if err != nil {
			log.Error("导出分销员：err=" + err.Error())
			resp.Message = fmt.Sprintf("导出分销员：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// DistributorUnbind 清退操作
// @Summary 清退操作
// @Description 分销清退接口
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DisIdReq body distribution_vo.DisIdReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/distributor/unbind [POST]
func DistributorUnbind(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DisIdReq](r)
	if err != nil {
		log.Error("分销员清退操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分销员清退操作，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("分销员清退操作参数校验异常：%s", vErr)
		} else {
			service := services.DistributorManageService{}
			err = service.DistributorUnbind(req)
			if err != nil {
				log.Error("分销员清退失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("分销员清退异常：%s", err.Error())
			} else {
				resp.Code = 200
			}
		}
	}

	//添加清退的操作日志
	go func(r *http.Request, id int) {
		operateLogService := base_service.OperateLogService{}
		operateLogService.Add(r, distribution_vo.OperateLogReq{
			ModuleType:  base_service.ModuleDistributor,
			Type:        base_service.DistributorUnbind,
			FromId:      cast.ToString(id),
			Description: "清退分销员",
		})
	}(r, req.Id)

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// DistributorChange 更换企业
// @Summary 更换企业
// @Description 分销员更换企业
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DisChangeReq body distribution_vo.DisChangeReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/distributor/change [POST]
func DistributorChange(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DisChangeReq](r)
	if err != nil {
		log.Error("更换企业操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("更换企业操作，参数解析失败：%s", err.Error())
	} else {

		req.OrgId = cast.ToInt(r.Header.Get("org_id"))
		service := services.DistributorManageService{}
		err := service.DistributorChange(req)
		if err != nil {
			log.Error("更换企业失败：err=", err.Error())
			resp.Message = fmt.Sprintf("更换企业异常：%s", err.Error())
		} else {
			resp.Code = 200

			//添加变更业务员的操作日志
			go func(r *http.Request, id int) {
				operateLogService := base_service.OperateLogService{}
				operateLogService.Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDistributor,
					Type:        base_service.DistributorChange,
					FromId:      cast.ToString(id),
					Description: fmt.Sprintf("分销员id:“%d，企业从 %s|%s”变更为“%s|%s”", req.Id, req.OldEnterpriseName, req.OldEnterpriseId, req.EnterpriseName, req.EnterpriseId),
				})
			}(r, req.Id)
		}

	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}


// DistributorAble 启用分销员
// @Summary 启用分销员
// @Description 启用分销员
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DisIdReq body distribution_vo.DisIdReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/distributor/able [POST]
func DistributorAble(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DisIdReq](r)
	if err != nil {
		log.Error("启用分销员操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("启用分销员操作，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("启用分销员操作参数校验异常：%s", vErr)
		} else {
			service := services.DistributorManageService{}
			err = service.DistributorAble(req)
			if err != nil {
				log.Error("启用分销员失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("启用分销员异常：%s", err.Error())
			} else {
				resp.Code = 200
			}
		}
	}

	//添加清退的操作日志
	go func(r *http.Request, id int) {
		operateLogService := base_service.OperateLogService{}
		operateLogService.Add(r, distribution_vo.OperateLogReq{
			ModuleType:  base_service.ModuleDistributor,
			Type:        base_service.DistributorAble,
			FromId:      cast.ToString(id),
			Description: "启用分销员",
		})
	}(r, req.Id)

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// DistributorAbleDetail 分销员详情
// @Summary 分销员详情
// @Description 分销员详情
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DisIdReq body distribution_vo.DisIdReq true " "
// @Success 200 {object} distribution_vo.DistributorDetailRes
// @Failure 400 {object} distribution_vo.DistributorDetailRes
// @Router /manager/distributor/detail [POST]
func DistributorAbleDetail(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.DistributorDetailRes{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.DisIdReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("分销员详情，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分销员详情，参数解析失败：%s", err.Error())
	} else {
		service := services.DistributorManageService{}
		data, err := service.DistributorDetail(req)
		if err != nil {
			log.Error("分销员列表查询失败：err=", err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// ChangeHospital 更换绑定的医院
// @Summary 更换绑定的医院
// @Description 分销员（医生）更换绑定的医院
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DisChangeReq body distribution_vo.DisChangeHospitalReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/distributor/change-hospital [POST]
func ChangeHospital(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DisChangeHospitalReq](r)
	if err != nil {
		log.Error("更换企业操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("更换企业操作，参数解析失败：%s", err.Error())
	} else {
		service := services.DistributorManageService{}
		logMsg, err := service.ChangeHospital(req)
		if err != nil {
			log.Error("更换企业失败：err=", err.Error())
			resp.Message = fmt.Sprintf("更换企业异常：%s", err.Error())
		} else {
			resp.Code = 200

			//添加更换绑定的医院的操作日志
			go func(r *http.Request, id int) {
				operateLogService := base_service.OperateLogService{}
				operateLogService.Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDistributor,
					Type:        base_service.ChangeHospital,
					FromId:      cast.ToString(id),
					Description: logMsg,
				})
			}(r, req.Id)
		}

	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// Approve 分销员（医生）审核
// @Summary 分销员（医生）审核
// @Description 分销员（医生）审核
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param DisChangeReq body distribution_vo.DisApproveReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/distributor/approve [POST]
func Approve(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DisApproveReq](r)
	if err != nil {
		log.Error("审核操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("审核操作，参数解析失败：%s", err.Error())
	} else {
		service := services.DistributorManageService{}
		err := service.Approve(req)
		if err != nil {
			log.Error("审核失败：err=", err.Error())
			resp.Message = fmt.Sprintf("审核异常：%s", err.Error())
		} else {
			resp.Code = 200

			//添加分销员（医生）审核的操作日志
			go func(r *http.Request, id int) {
				desc := "分销员审核通过"
				if req.ApproveState == 3 {
					desc = "分销员审核失败"
				}
				operateLogService := base_service.OperateLogService{}
				operateLogService.Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDistributor,
					Type:        base_service.Approve,
					FromId:      cast.ToString(id),
					Description: desc,
				})
			}(r, req.Id)
		}

	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// ChangeSalesperson 变更注册业务员
// @Summary 变更注册业务员
// @Description 分销员变更注册业务员
// @Tags 后台接口-分销员
// @Accept  json
// @Produce  json
// @Param ChangeSalespersonReq body distribution_vo.ChangeSalespersonReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/distributor/change-salesperson [POST]
func ChangeSalesperson(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.ChangeSalespersonReq](r)
	if err != nil {
		log.Error("更换注册业务员操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("更换注册业务员操作，参数解析失败：%s", err.Error())
	} else {
		req.OrgId = cast.ToInt(r.Header.Get("org_id"))
		service := services.DistributorManageService{}
		err = service.ChangeSalesperson(req)
		if err != nil {
			log.Error("更换注册业务员失败：err=", err.Error())
			resp.Message = fmt.Sprintf("更换注册业务员异常：%s", err.Error())
		} else {
			resp.Code = 200

			//添加变更业务员的操作日志
			go func(r *http.Request, id int) {
				// 更新dis_salesperson表的tuoke_dis_num，更新业务员的分销员数量
				disSalesmanService := services.DisSalesmanService{}
				disSalesmanService.SyncTuokeDisNum(req.OldSalespersonId, req.SalespersonId)

				operateLogService := base_service.OperateLogService{}
				operateLogService.Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleDistributor,
					Type:        base_service.ChangeSalesperson,
					FromId:      cast.ToString(id),
					Description: fmt.Sprintf("分销员id:“%d，注册业务员从 %s|%s”变更为“%s|%s”", req.Id, req.OldSalespersonName, req.OldSalespersonId, req.SalespersonName, req.SalespersonId),
				})
			}(r, req.Id)
		}

	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
