package omnibus_po

import (
	"errors"

	"xorm.io/xorm"
)

type TScrmPetHealthRecord struct {
	Id                 int    `xorm:"not null pk autoincr comment('主键') INT(11)"`
	PetId              string `xorm:"not null comment('宠物id') INT(11)"`
	Type               string `xorm:"not null comment('类别类别（免疫：200000084，驱虫：200000085，绝育：200000086，洗牙：200000087）') VARCHAR(16)"`
	Category           string `xorm:"not null comment('类型（核心疫苗：200000088，狂犬疫苗：200000089，抗体检测：200000090）') VARCHAR(16)"`
	CYear              string `xorm:"not null comment('记录年份：yyyy') VARCHAR(4)"`
	CDate              string `xorm:"not null comment('记录日期：yyyy-mm-dd') DATE"`
	Number             int    `xorm:"not null default -1 comment('第几针') INT(11)"`
	Source             int    `xorm:"not null comment('记录来源（子龙系统记录:1、子龙医生记录:2、手动填写:3）') TINYINT(4)"`
	StructCode         string `xorm:"not null comment('医院编码') VARCHAR(10)"`
	RegisterStructCode string `xorm:"not null default '' comment('登记医院编码') VARCHAR(10)"`
	StructName         string `xorm:"not null default '' comment('医院名称') VARCHAR(32)"`
	ProductCode        string `xorm:"not null default '' comment('产品编码') VARCHAR(20)"`
	ProductName        string `xorm:"not null default '' comment('产品名称') VARCHAR(50)"`
	IsDelete           int    `xorm:"not null default 0 comment('是否删除：0否，1是') TINYINT(4)"`
	CreateTime         string `xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME"`
	UpdateTime         string `xorm:"not null default 'CURRENT_TIMESTAMP' comment('最后修改时间') DATETIME"`
	CreateUser         int    `xorm:"not null comment('创建人id') INT(11)"`
	UpdateUser         int    `xorm:"not null comment('修改人id') INT(11)"`
	NumberOf           int    `xorm:"not null default 0 comment('0：未知，1：首次免疫，2：二次免疫，3：尾次免疫，4:年度免疫') TINYINT(4)"`
}

func (s *TScrmPetHealthRecord) TableName() string {
	return "scrm_organization_db.t_scrm_pet_health_record"

}

// 根据宠物id获取健康记录
func (s *TScrmPetHealthRecord) GetHealthRecordByPetId(session *xorm.Session, scrmPetIdSli []string) (list []TScrmPetHealthRecord, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	list = make([]TScrmPetHealthRecord, 0)
	if err = session.Table("scrm_organization_db.t_scrm_pet_health_record").In("pet_id", scrmPetIdSli).
		Find(&list); err != nil {
		return
	}
	return
}
