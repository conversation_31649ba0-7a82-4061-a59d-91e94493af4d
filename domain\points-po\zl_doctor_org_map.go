package points_po

type ZlDoctorOrgMap struct {
	SimpleEntity[int] `xorm:"extends"`
	UserId            string `json:"user_id" xorm:"'user_id'"`                           // 员工id
	UserName          string `json:"user_name" xorm:"'user_name'"`                       // 医生姓名
	CoSocialReditCode string `json:"co_social_redit_code" xorm:"'co_social_redit_code'"` // 统一社会信用代码
	CoName            string `json:"co_name" xorm:"'co_name'"`                           // 公司名称
	Province          string `json:"province" xorm:"'province'"`                         // 省份
	City              string `json:"city" xorm:"'city'"`                                 // 城市
	District          string `json:"district" xorm:"'district'"`                         // 行政区
	Address           string `json:"address" xorm:"'address'"`                           // 详细地址
}

func (e ZlDoctorOrgMap) TableName() string {
	return "big_data.zl_doctor_org_map"
}

func (e ZlDoctorOrgMap) AsPointer() any {
	return &e
}
