package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	"eShop/view-model"
	"eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"net/http"
)

// @Summary 获取店铺信息
// @Description
// @Tags 小程序接口-店铺
// @Accept json
// @Produce json
// @Param DisShareReq body distribution_vo.DisShopReq true " "
// @Success 200 {object} distribution_vo.DisShopRes
// @Failure 400 {object} distribution_vo.DisShopRes
// @Router /api/shop/get [POST]
func GetShopDetail(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.DisShopRes{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.DisShopReq](r)
	if err != nil {
		log.Error("获取店铺，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取店铺，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	} else {

		server := services.DisShopService{}
		data, err := server.GetShopDetail(req)
		if err != nil {
			resp.Message = err.Error()
		} else {
			resp.Data = data
		}
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 获取店铺信息
// @Description
// @Tags 小程序接口-店铺
// @Accept json
// @Produce json
// @Param DisShareReq body distribution_vo.DisSaasShopReq true " "
// @Success 200 {object} distribution_vo.SaasShopRes
// @Failure 400 {object} distribution_vo.SaasShopRes
// @Router /api/shop/saas/get [GET]
func GetSaasShopDetail(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.SaasShopRes{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.DisSaasShopReq](r)
	if err != nil {
		log.Error("获取店铺，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取店铺，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	} else {
		jwtInfo, err := jwtauth.GetJwtInfo(r)
		if err != nil {
			log.Errorf("获取店铺信息-鉴权失败-错误为%s", err.Error())
			resp.Message = "鉴权失败"
			bytes, _ := json.Marshal(resp)
			w.Write(bytes)
			return
		}
		server := services.DisShopService{}
		req.Mobile = jwtInfo.Mobile
		data, err := server.GetSaasShopDetail(req)
		if err != nil {
			resp.Message = err.Error()
		} else {
			resp.Data = data
		}
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 添加或修改店铺信息
// @Description
// @Tags 小程序接口-店铺
// @Accept json
// @Produce json
// @Param DisShareReq body distribution_vo.DisShopUpdateReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/shop/add [Post]
func GetShopAdd(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.DisShopUpdateReq](r)
	if err != nil {
		log.Error("修改店铺，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("修改店铺，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	} else {
		server := services.DisShopService{}
		err = server.GetShopAdd(req)
		if err != nil {
			resp.Message = err.Error()
			return
		}

	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary scrm-企业查询接口
// @Description
// @Tags 小程序接口-企业管理
// @Accept json
// @Produce json
// @Param DisShopReq body distribution_vo.EnterpriseReq true " "
// @Success 200 {object} distribution_vo.EnterpriseRes
// @Failure 400 {object} distribution_vo.EnterpriseRes
// @Router /api/shop/get [GET]
func GetShop(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.EnterpriseRes{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.EnterpriseReq](r)
	if err != nil {
		log.Error("企业查询失败，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("企业查询失败，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	} else {
		server := services.DisShopService{}
		data, total, err := server.GetDisShop(req)
		if err != nil {
			resp.Message = err.Error()
		} else {
			resp.Data = data
			resp.Total = total
		}
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary scrm-企业列表查询
// @Tags  小程序接口-企业管理
// @Accept  plain
// @Produce  json
// @Param DisShopListReq query distribution_vo.DisShopListReq true " "
// @Success 200 {object} distribution_vo.DisShopListRes
// @Failure 400 {object} distribution_vo.DisShopListRes
// @Router /api/shop/list [GET]
func GetShopList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.DisShopListRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.DisShopListReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("获取线下企业管理列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.DisShopService{}
	if out.Data, out.Total, err = server.DisShopList(param); err != nil {
		log.Errorf("获取线下企业管理列表失败-错误为%s", err.Error())
		out.Message = "获取线下企业管理列表失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
