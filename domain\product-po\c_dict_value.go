package product_po

import (
	"time"
	"xorm.io/xorm"
)

type CDictValue struct {
	Id         int    `json:"id" xorm:"pk autoincr not null BIGINT 'id'"`
	DictValue  string `json:"dict_value" xorm:"not null default '' comment('字典code') VARCHAR(32) 'dict_value'"`
	DictName   string `json:"dict_name" xorm:"not null default '' comment('字典名') VARCHAR(128) 'dict_name'"`
	ParentCode string `json:"parent_code" xorm:"not null default '' comment('父类code') VARCHAR(32) 'parent_code'"`
	DictType   int    `json:"dict_type" xorm:"not null default 0 comment('字典类型 1客户来源 2宠物种类 3宠物花色 4宠物品种') INT 'dict_type'"`
	Path       string `json:"path" xorm:"not null default '' comment('路径') VARCHAR(256) 'path'"`
	//IsHot       byte      `json:"is_hot" xorm:"not null default 0 comment('是否热门') BIT(1) 'is_hot'"`
	EnglishName string `json:"english_name" xorm:"not null default '' comment('英文名') VARCHAR(512) 'english_name'"`
	PinyinName  string `json:"pinyin_name" xorm:"not null default '' comment('拼音') VARCHAR(64) 'pinyin_name'"`
	FirstLetter string `json:"first_letter" xorm:"not null default '' comment('首字母') VARCHAR(10) 'first_letter'"`
	DictComment string `json:"dict_comment" xorm:"not null default '' comment('备注') VARCHAR(256) 'dict_comment'"`
	DictSort    int    `json:"dict_sort" xorm:"not null default 0 comment('排序') INT 'dict_sort'"`
	//IsDel       byte      `json:"is_del" xorm:"not null default 0 comment('状态 0正常 1删除') BIT(1) 'is_del'"`
	CreatedBy   int       `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime time.Time `json:"created_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy   int       `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime time.Time `json:"updated_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time'"`
}

// 根据名称查询种类是否存在，存在的话返回
func (model CDictValue) GetCDictValueByName(db *xorm.Engine) (data CDictValue, err error) {
	_, err = db.Table("eshop_saas.c_dict_value").Where("dict_type=4 and dict_name!='其他' and dict_name=?", model.DictName).Get(&data)
	return
}
