package distribution_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// 宠利扫企业来源

const (
	DisEnterpriseDataSourceSelf  int = iota + 1 // 自建
	DisEnterpriseDataSourceCloud                // 云订货
)

type DisEnterprise struct {
	// 主键id
	Id int64 `json:"id" xorm:"pk autoincr not null comment('id') BIGINT 'id'"`

	OrgId int `json:"org_id" xorm:"not null default 0 comment('所属主体id') INT 'org_id'"`
	// 润和云店是企业R1编码即scrm_enterprise.id
	ScrmEnterpriseId int64 `json:"scrm_enterprise_id" xorm:"not null default 0 comment('企业id') BIGINT 'scrm_enterprise_id'"`
	// 企业名称
	EnterpriseName string `json:"enterprise_name" xorm:"not null default '' comment('企业名称') VARCHAR(128) 'enterprise_name'"`
	// 省
	Province string `json:"province" xorm:"not null default '' comment('省') VARCHAR(255) 'province'"`
	// 市
	City string `json:"city" xorm:"not null default '' comment('市') VARCHAR(255) 'city'"`
	// 区
	District string `json:"district" xorm:"not null default '' comment('区') VARCHAR(255) 'district'"`
	// 详细地址
	Address string `json:"address" xorm:"not null default '' comment('详细地址') VARCHAR(255) 'address'"`
	// 手机号
	Phone string `json:"phone" xorm:"not null default '' comment('手机号') VARCHAR(20) 'phone'"`
	// 加密手机号
	EncryptPhone string `json:"encrypt_phone" xorm:"not null default '' comment('加密手机号') VARCHAR(20) 'encrypt_phone'"`
	// 统一社会信用代码
	SocialCreditCode string `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	// 来源：1、自建   2 云订货
	DataSource int `json:"data_source" xorm:"not null default 1 comment('来源：1、自建   2 云订货') TINYINT 'data_source'"`
	// 创建时间
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	// 更新时间
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
}

func (d *DisEnterprise) TableName() string {
	return "eshop.dis_enterprise"
}

// GetEnterpriseReq 获取企业信息请求参数
type GetEnterprisesReq struct {
	EnterpriseName   string // 企业名称
	SocialCreditCode string // 统一社会信用代码
}

// GetEnterprises 根据企业名称或社会信用代码获取企业信息
func (d *DisEnterprise) GetEnterprises(session *xorm.Session, req GetEnterprisesReq) (enterprise []DisEnterprise, err error) {
	enterprise = make([]DisEnterprise, 0)
	query := session.Table(d.TableName())
	if req.EnterpriseName != "" {
		query = query.Where("enterprise_name = ?", req.EnterpriseName)
	}
	if req.SocialCreditCode != "" {
		query = query.Where("social_credit_code = ?", req.SocialCreditCode)
	}

	err = query.Find(&enterprise)
	if err != nil {
		return nil, err
	}

	return enterprise, nil
}

type CheckEnterpriseExistReq struct {
	SocialCreditCode string // 统一社会信用代码
	DisId            int    // 分销员id
	OrgId            int    // 主体id
}

// 检查企业是否存在，如果存在 ， 是否已经绑定老板角色
func (d *DisEnterprise) CheckEnterpriseExist(session *xorm.Session, req CheckEnterpriseExistReq) (out DisEnterprise, shopInfo Shop, err error) {
	logPrefix := fmt.Sprintf("获取企业信息,入参: %s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	if req.SocialCreditCode == "" {
		return out, shopInfo, errors.New("统一社会信用代码不能为空")
	}

	enterprise, err := d.GetEnterprises(session, GetEnterprisesReq{
		SocialCreditCode: req.SocialCreditCode,
	})
	if err != nil {
		log.Error(logPrefix, "获取企业信息失败，err=", err.Error())
		err = errors.New("获取企业信息失败")
		return out, shopInfo, err
	}
	if len(enterprise) > 0 {
		out = enterprise[0]
	}
	if out.Id == 0 {
		return out, shopInfo, nil
	}

	shopList, err := new(Shop).GetShops(session, GetShopsReq{
		EnterpriseId: out.ScrmEnterpriseId,
		OrgId:        req.OrgId,
	})
	if err != nil {
		log.Error(logPrefix, "获取店铺信息失败，err=", err.Error())
		err = errors.New("获取企业信息失败")
		return out, shopInfo, err
	}
	if len(shopList) == 0 {
		log.Error(logPrefix, "企业不存在")
		return out, shopInfo, errors.New("企业不存在")
	}

	shopInfo = shopList[0]
	// 判断这个分销员是否能成为企业老板

	disDistributor := new(DisDistributor)
	has, err := session.Table("eshop.dis_distributor").
		Where("shop_id = ?", shopInfo.Id).
		Where("org_id = ?", req.OrgId).
		Where("dis_role = ?", DisRoleBoss).
		Where("social_credit_code = ?", req.SocialCreditCode).
		Get(disDistributor)
	if err != nil {
		log.Error(logPrefix, "查询企业老板信息失败，err=", err.Error())
		return out, shopInfo, errors.New("查询企业老板信息失败")
	}
	if has && disDistributor.Id != req.DisId {
		log.Error(logPrefix, "该企业已经有老板角色了")
		return out, shopInfo, errors.New("该企业已经有老板角色了")
	}

	return out, shopInfo, nil
}

func (d *DisEnterprise) UpdateOrCreate(session *xorm.Session) (err error) {
	enterprise, err := d.GetEnterprises(session, GetEnterprisesReq{
		SocialCreditCode: d.SocialCreditCode,
	})
	if err != nil {
		log.Error("UpdateOrCreate获取企业信息失败，err=", err.Error())
		return err
	}
	if len(enterprise) > 0 {
		d.Id = enterprise[0].Id
	}
	if d.Id == 0 {
		_, err = session.Table(d.TableName()).Insert(d)
		if err != nil {
			log.Error("UpdateOrCreate更新企业信息失败，err=", err.Error())
			return err
		}
		if d.ScrmEnterpriseId <= 0 {
			d.ScrmEnterpriseId = d.Id
			_, err = session.Table(d.TableName()).Where("id = ?", d.Id).Cols("scrm_enterprise_id").Update(d)

		}

	} else {
		if d.ScrmEnterpriseId <= 0 {
			d.ScrmEnterpriseId = d.Id
		}
		_, err = session.Table(d.TableName()).Where("id = ?", d.Id).Update(d)
	}
	if err != nil {
		log.Error("UpdateOrCreate更新企业信息失败，err=", err.Error())
		return err
	}
	return nil

}
