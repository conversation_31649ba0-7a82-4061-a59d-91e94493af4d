package services

import (
	"context"
	petai_po "eShop/domain/petai-po"
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	petai_vo "eShop/view-model/petai-vo"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/coze-dev/coze-go"
)

// 定义事件类型
const (
	// EventTypeChatCreated      string = "event:conversation.chat.created"      // 对话创建
	// EventTypeChatInprogress   string = "event:conversation.chat.in_progress"  // 对话chat中
	EventTypeMessageDelta     string = "conversation.message.delta"     // 消息增量
	EventTypeMessageCompleted string = "conversation.message.completed" // 消息完成
	EventTypeChatCompleted    string = "conversation.chat.completed"    // 对话完成
	EventTypeChatFailed       string = "conversation.chat.failed"       // 对话完成
	EventTypeEnd              string = "done"
)

type EventData struct {
	Id             string `json:"id"`
	ConversationId string `json:"conversation_id"`
	BotId          string `json:"bot_id"`
	Role           string `json:"role"`
	Type           string `json:"type"`
	Content        string `json:"content"`
	ContentType    string `json:"content_type"`
	ChatId         string `json:"chat_id"`
}

// 定义包级别的客户端池
var httpClientPool = &sync.Pool{
	New: func() interface{} {
		return &http.Client{
			Timeout: time.Minute * 15,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
				DisableKeepAlives:   false,
			},
		}
	},
}

// coze 相关配置
type CozeConfig struct {
	BotId       string `json:"bot_id"`        //智能体id
	CozeToken   string `json:"coze_token"`    //coze token字段
	CozeApiBase string `json:"coze_api_base"` //coze平台openai的api地址
}
type CozeAuthJwt struct {
	OauthJwtClientID           string `json:"oauth_jwt_client_id"`             // coze oauth应用的client_id
	OauthJwtPublicKeyID        string `json:"oauth_jwt_public_key_id"`         // coze oauth应用的public_key_id
	OauthJwtPrivateKeyFilePath string `json:"oauth_jwt_private_key_file_path"` // coze oauth应用的private_key文件路径
}
type CozeService struct {
	common.BaseService
	Request *http.Request
	JwtInfo *jwtauth.JwtInfo
}

// NewCozeService 创建coze服务
func NewCozeService() *CozeService {
	return &CozeService{}
}

// coze的消息在服务端的保存时长为 180 天，到期后自动删除.
// 这里根据配置参数， 如果为true， 则自动带上会话的所有历史消息
func (s *CozeService) IsTakeHistoryMsg() bool {
	isTake := config.Get("petai_coze_take_history_msg")
	if isTake == "true" {
		return true
	}
	return false
}

// 是否需要内容安全检查
func (s *CozeService) NeedSafeCheck() bool {
	isNeed := config.Get("petai_coze_question_safe_check")
	if isNeed == "true" {
		return true
	}
	return false
}
func (s *CozeService) GetBotId(botType ...int) (botId string) {
	if len(botType) == 0 {
		botId = config.Get("petai_coze_bot_id")
	} else {
		switch botType[0] {
		case petai_po.BotTypePetCareAssistant:
			botId = config.Get("petai_coze_bot_id")
		case petai_po.BotTypeXiaoWenModel:
			botId = "xiaowenmodel"
		case petai_po.BotTypePetDiagnose: // 宠物自诊
			botId = config.Get("petai_coze_bot_id_pet_diagnose")
		case petai_po.BotTypePetRecog: // 宠物识别
			botId = config.Get("petai_coze_bot_id_pet_recog")
		case petai_po.BotTypeHealthType: //健康建议
			botId = config.Get("petai_coze_bot_id_health_type")
		case petai_po.BotTypePetMedical: //互联网医院
			botId = "pet-medical"

		default:
			botId = ""
		}
	}

	return
}

func (s *CozeService) GetPersonalToken() (token string) {
	token = config.Get("petai_coze_token") // coze 个人访问令牌
	fmt.Printf("=====coze token为%s=====", token)
	//	token = "pat_utsHiRGfWyVRic8GH7KF5p84HaWzwuHA9wUSwV86HYPPAo7CZzwsSocUPcwlYIqM" // 翠的uat环境的coze token
	if len(token) == 0 {
		//token = "pat_dBKmy7IZFvBQQN83A0eBj3qv7CRvA36oGLWXN7b4AuC8TI7GRedRiQ7lcoM9RETq" // 炯哥 用于uat环境
		//token = "pat_tALMebcIBihKLPoq5fOnFijwiZ5vDDGjh6VmWHqfJapwWKLaloUhN82KTg8Zmqoj" //炯哥 uat环境的coze token（我本地用）
		//token = "pat_utsHiRGfWyVRic8GH7KF5p84HaWzwuHA9wUSwV86HYPPAo7CZzwsSocUPcwlYIqM" // 翠的uat环境的coze token
		//token = "pat_x3tLl93EQXLgmGq0U0TzdgJVwwfVNY6JaUugJGaXiZwuXut23qZtrm1yqZmrXpdD" //成鹏的uat环境的coze token
		//token = "pat_VYt6RkwLv0xlI2OWhdqsG3YF9NvMGYXeesYmzOLJe5zREKS2M8nHcjfN8tTc7lV6" //彪哥
	}
	return
}

func (s *CozeService) GetCozeTokenType() (tokenType string) {
	tokenType = config.Get("petai_coze_token_type") // 访问coze 接口使用的token类型：oauth_jwt 或者 personal_token
	if len(tokenType) == 0 {
		tokenType = "personal_token"
	}
	return
}

func (s *CozeService) GetCozeAuthJwtConfig() (c CozeAuthJwt) {
	c.OauthJwtClientID = config.Get("petai_coze_jwt_oauth_client_id")
	c.OauthJwtPublicKeyID = config.Get("petai_coze_jwt_oauth_public_key_id")
	c.OauthJwtPrivateKeyFilePath = "./coze_oauth_private_key.pem"

	if c.OauthJwtClientID == "" {
		//c.OauthJwtClientID = "1115971567908" // 炯哥 - 养宠助手火山
		//c.OauthJwtClientID = "1134245179881" //翠的OAuth应用的client_id
		//c.OauthJwtClientID = "1146322448650" //彪哥
	}
	if c.OauthJwtPublicKeyID == "" {
		//c.OauthJwtPublicKeyID = "IdddsbU5vJ8Yrus3cxC1yT014GOQY1pZDI4g3bnZ5e8" // 炯哥 - 养宠助手火山
		//c.OauthJwtPublicKeyID = "Xt8t27vWTZguRCW8FZ_dH_-rC-IUC4n2F3efwsnXfsM" //翠的OAuth应用的public_key_id
		//c.OauthJwtPublicKeyID = "GmSwUKxWCu36dl7xlc5Lj5v-5r68MmDrI05km7ePcpw" //彪哥
	}
	return
}

// 智能体类型：1-养宠助手火山；2-宠物自诊；3-宠物识别；4-健康建议
func (s *CozeService) InitCozeConfig(userInfoId string, botType ...int) (c CozeConfig) {
	c.CozeApiBase = coze.CnBaseURL
	tokenType := s.GetCozeTokenType()
	if len(botType) > 0 {
		c.BotId = s.GetBotId(botType[0]) //智能体id
	} else {
		c.BotId = s.GetBotId() //智能体id
	}

	//  访问coze 接口使用的token类型：oauth_jwt 或者 personal_token
	if tokenType == "personal_token" {
		// 个人访问令牌
		c.CozeToken = s.GetPersonalToken()
	} else {
		// jwt oauth 类型
		// 先从redis里获取 jwt oauth的token
		cacheK := fmt.Sprintf("petai_coze_token_%s", userInfoId)
		var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

		authJwtToken := mCache.Get(string(cache_source.EShop), cacheK)
		if authJwtToken != nil && len(authJwtToken) > 0 && len(authJwtToken[0].(string)) > 0 {
			c.CozeToken = authJwtToken[0].(string)
		} else {
			c.CozeToken, _ = s.GetCozeTokenByJwt(userInfoId)
		}

	}
	return

}

func (s *CozeService) NewCozeAPI(userInfoId string, botType ...int) (cozeCli coze.CozeAPI, c CozeConfig) {
	if len(botType) > 0 {
		c = s.InitCozeConfig(userInfoId, botType[0])
	} else {
		c = s.InitCozeConfig(userInfoId)
	}

	authCli := coze.NewTokenAuth(c.CozeToken)
	// 从池中获取客户端
	client := httpClientPool.Get().(*http.Client)
	defer httpClientPool.Put(client)
	cozeCli = coze.NewCozeAPI(authCli, coze.WithBaseURL(c.CozeApiBase), coze.WithHttpClient(client))
	return
}

// coze 通过jwt auth获取token
func (s *CozeService) GetCozeTokenByJwt(userInfoId string) (token string, err error) {
	c := s.GetCozeAuthJwtConfig()
	if len(c.OauthJwtClientID) == 0 || len(c.OauthJwtPublicKeyID) == 0 || len(c.OauthJwtPrivateKeyFilePath) == 0 {
		err = errors.New("coze jwt oauth 配置信息不全")
		return
	}
	logPrefix := fmt.Sprintf("====通过jwt auth获取coze token====入参:%s", utils.JsonEncode(userInfoId))
	log.Info(logPrefix)
	// Read private key from file
	privateKeyBytes, err := os.ReadFile(c.OauthJwtPrivateKeyFilePath)
	if err != nil {
		log.Error(logPrefix, "读取coze oauth应用的private_key文件失败,错误信息为", err.Error())
		fmt.Printf("%s Error reading private key file: %v\n", logPrefix, err)
		return
	}
	jwtOauthPrivateKey := string(privateKeyBytes)

	// 从池中获取客户端
	client := httpClientPool.Get().(*http.Client)
	defer httpClientPool.Put(client)
	ttl := 86400
	// The jwt oauth type requires using private to be able to issue a jwt token, and through the jwt token,
	// apply for an access_token from the coze service.The sdk encapsulates this procedure,
	// and only needs to use get_access_token to obtain the access_token under the jwt oauth process.
	// Generate the authorization token The default ttl is 900s, and developers can customize the expiration time,
	// which can be set up to 24 hours at most.
	oauth, err := coze.NewJWTOAuthClient(coze.NewJWTOAuthClientParam{
		ClientID: c.OauthJwtClientID, PublicKey: c.OauthJwtPublicKeyID, PrivateKeyPEM: jwtOauthPrivateKey, TTL: &ttl,
	}, coze.WithAuthBaseURL(coze.CnBaseURL), coze.WithAuthHttpClient(client))
	if err != nil {
		log.Error(logPrefix, "初始化jwt oauth client失败,错误信息为", err.Error())
		fmt.Printf("Error creating JWT OAuth client: %v\n", err)
		return
	}
	ctx := context.Background()

	resp, err := oauth.GetAccessToken(ctx, nil)
	log.Info(logPrefix, "获取access token返回结果为", utils.JsonEncode(resp))
	if err != nil {
		log.Error(logPrefix, "获取access token失败,错误信息为", err.Error())
		fmt.Printf("Error getting access token: %v\n", err)
		return
	}
	// 保存到redis
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	cacheK := fmt.Sprintf("petai_coze_token_%s", userInfoId)
	mCache.Save(string(cache_source.EShop), cacheK, resp.AccessToken, time.Hour*23)
	return resp.AccessToken, nil

}

// 组装创建会话所需的消息列表
func (s *CozeService) OrgChatMessages(in petai_vo.ChatReq) (out []*coze.Message, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	out = make([]*coze.Message, 0)
	var (
		isTake       = s.IsTakeHistoryMsg() // 是否配置了带上历史消息
		logPrefix    = fmt.Sprintf("====组装发起coze对话的附加消息数据,入参为:%s====", utils.JsonEncode(in))
		conversation petai_po.PetaiConversation         //会话信息
		message      = make([]petai_po.PetaiMessage, 0) //会话消息信息
	)
	log.Info(logPrefix)
	// 如果没有设置，则根据会话的创建时间来判断，是否需要带上历史消息
	if !isTake {
		if conversation, err = new(petai_po.PetaiConversation).GetConversationByConversationId(session, in.ConversationId); err != nil {
			log.Error(logPrefix, "获取会话信息失败,错误信息为", err.Error())
			err = errors.New("获取会话信息失败")
			return
		}
		if conversation.CreateTime.AddDate(0, 0, 179).Before(time.Now()) {
			isTake = true
		}
	}
	// 宠物自诊，宠物识别，健康建议， 不需要带上历史消息
	if isTake && in.BotType != petai_po.BotTypePetDiagnose && in.BotType != petai_po.BotTypePetRecog && in.BotType != petai_po.BotTypeHealthType {
		req := petai_po.GetMessagesReq{
			UserInfoId:     in.UserInfoId,
			ConversationId: in.ConversationId,
		}

		// if len(in.PetDesc) > 0 {
		// 	out = append(out, coze.BuildAssistantAnswer(in.PetDesc, nil))
		// }

		//message, err = new(petai_po.PetaiMessage).GetMessages(session, req)
		// 上下文 只取ai消息
		sql := `select * from (select * from eshop.petai_message where user_info_id = ? and conversation_id = ? and data_from=1  limit 10) t order by message_time asc,id asc`
		if err = session.SQL(sql, req.UserInfoId, req.ConversationId).Find(&message); err != nil {
			log.Error(logPrefix, "获取会话消息失败,错误信息为", err.Error())
			err = errors.New("获取会话消息失败")
			return
		}

		if len(message) > 0 {
			for _, v := range message {
				metaData := make(map[string]string)
				if len(v.MetaData) > 0 {
					if err = utils.JsonDecode(v.MetaData, &metaData); err != nil {
						log.Error(logPrefix, "解析消息元数据失败,错误信息为", err.Error())
						continue
					}
				}
				if coze.MessageType(v.Type) == petai_po.MessageTypeQuestion {
					switch v.ContentType {
					case petai_po.MessageContentTypeText:
						out = append(out, coze.BuildUserQuestionText(v.Content, metaData))
					case petai_po.MessageContentTypeObjectString:
						content := make([]*coze.MessageObjectString, 0)
						if err = json.Unmarshal([]byte(v.Content), &content); err != nil {
							log.Error(logPrefix, "解析消息内容失败,错误信息为", err.Error())
							continue
						}

						out = append(out, coze.BuildUserQuestionObjects(content, metaData))
					default:
						continue
					}

				} else if v.Type == petai_po.MessageTypeAnswer {
					switch v.ContentType {
					case petai_po.MessageContentTypeText:
						out = append(out, coze.BuildAssistantAnswer(v.Content, metaData))
					case petai_po.MessageContentTypeObjectString:
						// content := make([]*coze.MessageObjectString, 0)
						// if err = json.Unmarshal([]byte(v.Content), &content); err != nil {
						// 	log.Error(logPrefix, "解析消息内容失败,错误信息为", err.Error())
						// 	continue
						// }

						out = append(out, coze.BuildAssistantAnswer(v.Content, metaData))
					default:
						continue
					}
				} else {
					continue
				}
			}
		}

	}
	metadata := make(map[string]string)
	if err = utils.JsonDecode(in.Message.MetaData, &metadata); err != nil {
		log.Error(logPrefix, "解析消息元数据失败,错误信息为", err.Error())
		err = errors.New("解析消息元数据失败")
		return
	}
	if in.Message.ContentType == petai_po.MessageContentTypeText {
		if s.NeedSafeCheck() && !utils.CheckContent(utils.CheckContentReq{Content: in.Message.TextContent, Type: 1}) {
			err = errors.New("内容含违规信息，请修改后重新提交")
			return
		}
		// 将宠物信息添加到消息中

		out = append(out, coze.BuildUserQuestionText(in.Message.TextContent, metadata))
	} else if in.Message.ContentType == petai_po.MessageContentTypeObjectString {
		content := make([]*coze.MessageObjectString, 0)
		if err = json.Unmarshal([]byte(utils.JsonEncode(in.Message.ObjectContent)), &content); err != nil {
			log.Error(logPrefix, "解析消息内容失败,错误信息为", err.Error())
			err = errors.New("解析消息内容失败")
			return
		}
		// 症状自诊时， 需要将宠物信息添加到消息中
		if in.BotType == petai_po.BotTypePetDiagnose {
			content = append(content, &coze.MessageObjectString{
				Type: coze.MessageObjectStringTypeText,
				Text: in.PetDesc,
			})
		}

		for _, v := range content {
			if len(v.Text) > 0 && s.NeedSafeCheck() && coze.MessageObjectStringType(v.Type) == coze.MessageObjectStringTypeText && !utils.CheckContent(utils.CheckContentReq{Content: v.Text, Type: 1}) {
				log.Error(logPrefix, "内容含违规信息，请修改后重新提交|", v.Text)
				err = errors.New("内容含违规信息，请修改后重新提交")
				return
			}
			// 用户上传图片时， 前端会做安全审核检测， 审核通过后将图片上传至阿里云
			// if s.NeedSafeCheck() && coze.MessageObjectStringType(v.Type) == coze.MessageObjectStringTypeImage && !utils.CheckContent(utils.CheckContentReq{FileUrl: v.FileURL, Type: 2}) {
			// 	log.Error(logPrefix, "图片含违规信息，请重新提交|", v.Text)
			// 	err = errors.New("图片含违规信息，请重新提交")
			// 	return
			// }
			// if s.NeedSafeCheck() && coze.MessageObjectStringType(v.Type) == coze.MessageObjectStringTypeAudio && !utils.CheckContent(utils.CheckContentReq{FileUrl: v.FileURL, Type: 3}) {
			// 	log.Error(logPrefix, "图片含违规信息，请重新提交|", v.Text)
			// 	err = errors.New("图片含违规信息，请重新提交")
			// 	return
			// }

		}

		out = append(out, coze.BuildUserQuestionObjects(content, metadata))
	}
	fmt.Printf("%s,组装发送给coze的消息成功,返回结果为:%s\n", logPrefix, utils.JsonEncode(out))
	return

}

// 组装创建问诊会话所需的消息列表
func (s *CozeService) OrgConsultChatMessages(in *petai_vo.ConsultChatReq) (out []*coze.Message, question string, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	out = make([]*coze.Message, 0)
	logPrefix := fmt.Sprintf("====组装发起问诊会话对话的附加消息数据,订单号为:%d====", in.PmOrderSn)
	log.Info(logPrefix, ",入参为", utils.JsonEncode(in))

	if len(in.Message) == 0 {
		log.Error(logPrefix, "消息内容为空")
		err = errors.New("消息内容为空")
		return
	}

	for _, v := range in.Message {
		if v.Role == "" {
			log.Error(logPrefix, "消息角色为空")
			err = errors.New("消息角色为空")
			return
		}
		if v.Role != "user" && v.Role != "assistant" {
			log.Error(logPrefix, "消息角色为非法,角色为", v.Role)
			err = errors.New("消息角色为非法")
			return
		}
		if v.Role == "assistant" && v.ContentType != petai_po.MessageContentTypeText {
			log.Error(logPrefix, "assistant消息类型时， 消息内容类型必须为text")
			continue
		}
		metadata := make(map[string]string)
		if err = utils.JsonDecode(v.MetaData, &metadata); err != nil {
			log.Error(logPrefix, "解析消息元数据失败,错误信息为", err.Error())
			err = errors.New("解析消息元数据失败")
			return
		}
		if v.ContentType == petai_po.MessageContentTypeText {
			if s.NeedSafeCheck() && !utils.CheckContent(utils.CheckContentReq{Content: v.TextContent, Type: 1}) {
				err = errors.New("内容含违规信息，请修改后重新提交")
				return
			}
			// 将宠物信息添加到消息中
			if v.Role == "user" {
				out = append(out, coze.BuildUserQuestionText(strings.TrimSpace(v.TextContent), metadata))
			} else {
				out = append(out, coze.BuildAssistantAnswer(strings.TrimSpace(v.TextContent), metadata))
			}
		} else if v.ContentType == petai_po.MessageContentTypeObjectString {
			content := make([]*coze.MessageObjectString, 0)
			if err = json.Unmarshal([]byte(utils.JsonEncode(v.ObjectContent)), &content); err != nil {
				log.Error(logPrefix, "解析消息内容失败,错误信息为", err.Error())
				err = errors.New("解析消息内容失败")
				return
			}

			for _, vv := range content {
				if len(vv.Text) > 0 && s.NeedSafeCheck() && coze.MessageObjectStringType(vv.Type) == coze.MessageObjectStringTypeText && !utils.CheckContent(utils.CheckContentReq{Content: vv.Text, Type: 1}) {
					log.Error(logPrefix, "内容含违规信息，请修改后重新提交|", vv.Text)
					err = errors.New("内容含违规信息，请修改后重新提交")
					return
				}
			}
			out = append(out, coze.BuildUserQuestionObjects(content, metadata))

		}
	}
	if len(out) == 0 {
		log.Error(logPrefix, "消息内容为空")
		err = errors.New("消息内容为空")
		return
	}
	//问题描述
	lastMessage := out[len(out)-1]
	if lastMessage.ContentType == petai_po.MessageContentTypeText {
		question = lastMessage.Content
	} else if lastMessage.ContentType == petai_po.MessageContentTypeObjectString {
		content := make([]*coze.MessageObjectString, 0)
		if err = json.Unmarshal([]byte(utils.JsonEncode(lastMessage.Content)), &content); err != nil {
			log.Error(logPrefix, "解析消息内容失败,错误信息为", err.Error())
			err = errors.New("解析消息内容失败")
			return
		}

		for _, v := range content {
			if v.Type == coze.MessageObjectStringTypeText {
				question += v.Text + ","
			}
		}

	}
	question = strings.TrimRight(question, ",")
	question = strings.TrimSpace(question)

	return

}

// CancelChat 终止对话
func (s *CozeService) CancelChat(in petai_vo.CancelChatReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====终止coze对话====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	if in.ConversationId == 0 || in.CozeConversationId == "" {
		log.Error(logPrefix, "会话id和coze会话id均不能为空")
		err = errors.New("会话id和coze会话id均不能为空")
		return
	}
	// 获取会话信息
	conversation, err := new(petai_po.PetaiConversation).GetConversationByConversationId(session, in.ConversationId)
	if err != nil {
		log.Error(logPrefix, "获取会话信息失败,错误信息为", err.Error())
		err = errors.New("获取会话信息失败")
		return
	}
	if conversation.Id == 0 {
		log.Error(logPrefix, "会话信息不存在")
		err = errors.New("会话信息不存在")
		return
	}

	if in.CozeConversationId == "" {
		log.Error(logPrefix, "coze会话id为空")
		err = errors.New("coze会话id为空")
		return
	}
	ctx := context.Background()
	// Init the Coze client through the access_token.
	cozeCli, _ := s.NewCozeAPI(in.UserInfoId)

	// 获取对话详情， 对话状态为已完成，已失败，和对话中断,则不允许终止
	chat, err := cozeCli.Chat.Retrieve(ctx, &coze.RetrieveChatsReq{
		ConversationID: in.CozeConversationId,
		ChatID:         in.ChatId,
	})
	if err != nil {
		log.Error(logPrefix, "Error retrieving chat status:", err.Error())
		err = errors.New("获取对话状态失败")
		return
	}
	if chat.Chat.Status == coze.ChatStatusCompleted || chat.Chat.Status == coze.ChatStatusFailed || chat.Chat.Status == coze.ChatStatusCancelled || chat.Chat.Status == coze.ChatStatusRequiresAction {
		log.Error(logPrefix, "Chat is already completed or failed, current status:", chat.Chat.Status)
		err = errors.New("对话已完成或已失败，无法终止")
		return
	}
	// 终止对话
	cancelResp, err := cozeCli.Chat.Cancel(ctx, &coze.CancelChatsReq{
		ConversationID: in.CozeConversationId,
		ChatID:         in.ChatId,
	})
	log.Infof("%s resp:%s|err:%v", logPrefix, utils.JsonEncode(cancelResp), err)
	if err != nil {
		log.Error(logPrefix, "Error cancelling chat:", err.Error())
		err = errors.New("终止对话失败" + err.Error())
		return
	}

	return
}

func (s *CozeService) ConversationDataAcquisition(conversationId int) (dataAcquisitionShow bool, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====查询对话是否被数据采集====会话id:%d", conversationId)
	log.Info(logPrefix)

	data, err := new(petai_po.UserPetVaccinateRecord).GetByConversationId(session, conversationId)
	if err != nil {
		log.Error(logPrefix, "查询会话健康档案记录失败,错误信息为", err.Error())
		err = errors.New("查询会话健康档案记录失败")
		return
	}
	if len(data) > 0 {
		dataAcquisitionShow = true
	}

	// messageExtend := &petai_po.PetaiMessageExtend{}
	// exist, err := session.Table("eshop.petai_message_extend").Where("conversation_id=?", conversationId).Where("collected_type=?", petai_po.CollectedTypePetInfo).Get(messageExtend)
	// if err != nil {
	// 	log.Error(logPrefix, "查询消息扩展表失败:", err.Error())
	// 	return
	// }
	// if exist {
	// 	dataAcquisitionShow = true
	// }
	return

}
