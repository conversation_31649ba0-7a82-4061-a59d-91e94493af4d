package manager

import (
	"eShop/infra/utils"
	"eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/services"
	marketsvs "eShop/services/marketing-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"

	"encoding/json"
	"net/http"
)

// TaskDo
// @Summary 定时任务调用
// @Tags 共用中心
// @Accept  plain
// @Produce  json
// @Param id query string true "1添加业务员拓展表和二维码 2跑服务企业数和分销员数 100待结算数据 101将订单已完成超过15天的待结算转为已结算 102重新统计待结算和已结算数据 103提现统计 104提现统计 110分销企业数据统计表 111分销店铺数据统计表 112保险订单报表 113-跑每日的分销【企业】数据统计表 114跑分销店铺每日【佣金】数据统计表  120商品订单统计 121商品分销订单统计 122 商品订单业务员分销订单统计 123-跑商品订单每日统计 124-跑商品分销订单统计 125-跑商品订单业务员分销订单每日统计"
// @Param org_code query string false "开始时间，格式：2019-01-02"
// @Param org_name query string false "结束时间，格式：2019-01-03"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/tool/task-do [POST]
func TaskDo(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	req, err := utils.Bind[distribution_vo.DisSalesmanView](request)
	if err != nil {
		out.Message = "解析参数错误,需要参数id， 1=添加业务员拓展表和二维码，2=跑服务企业数和分销员数"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	switch req.Id {

	case "1": //添加业务员拓展表和二维码
		server := services.DisSalesmanService{}
		server.SetSaleManBarCode()

	case "2": //跑服务企业数和分销员数
		server := services.DisSalesmanService{}
		server.SetSaleManData()
	case "3": //将数据库里scrm_leads_autonavi数据导入es
		services := services.OutService{}
		services.UpdateOutHospitalToEs()
	case "100": //执行一次 定时器， 将已完成的订单写入待结算数据
		server := services.DisSettlementService{}
		server.InsertSettlementData()

	case "101": //执行一次定时器， 将订单已完成超过15天的待结算转为已结算
		server := services.DisSettlementService{}
		server.ChangeSettlementStatus()

	case "102": //重新统计 待结算和已结算数据(包括：eshop.dis_distributor_total和eshop.shop)
		// todo
		server := services.DisSettlementService{}
		server.SettCommUpdate(map[string]string{}, req.OrgId)
	case "103": // 提现统计 统计eshop.dis_distributor_total和eshop.shop表中的：withdraw_success、withdraw_apply、wait_withdraw
		server := services.DiscommissionService{}
		server.WithdrawCommUpdate(map[string]string{}, enum.WithdrawFlagShop, req.OrgId)
	case "104": // 提现统计 统计eshop.dis_distributor_total和eshop.shop表中的：withdraw_success、withdraw_apply、wait_withdraw
		server := services.DiscommissionService{}
		server.WithdrawCommUpdate(map[string]string{}, enum.WithdrawFlagSelf, req.OrgId)
		out.Message = "没有找到对应的定时任务"
		out2, _ := json.Marshal(out)
		writer.Write(out2)

	case "112":
		insureOrderStatService := services.InsureOrderStatService{}
		insureOrderStatService.InsureOrderStatRun(req.OrgCode, req.OrgName)
	case "113": // 分销企业数据统计表stats_enterprise_daily
		statsEnterpriseService := services.StatsEnterpriseService{}
		statsEnterpriseService.StatsEnterpriseDailyRun(req.OrgCode, req.OrgName)
	case "114": // 分销店铺数据统计表 stats_shop_distributor_daily.commission, unsettled_commission,settled_commission
		statsShopDisService := services.StatsShopService{}
		statsShopDisService.StatsShopDistributorDailyRun(req.OrgCode, req.OrgName)

	case "120": //商品订单统计
		jobStatsService := services.JobStatsService{}
		jobStatsService.StatsOrderDailyData(distribution_vo.StatsShopDistributorDailyReq{StartDate: req.OrgCode, EndDate: req.OrgName})
	case "121": // 商品分销订单统计
		jobStatsDisService := services.JobStatsDisService{}
		jobStatsDisService.StatsDisOrderDailyData(distribution_vo.StatsShopDistributorDailyReq{StartDate: req.OrgCode, EndDate: req.OrgName})
	case "122": // 业务员分销订单统计
		jobStatsSalespersonService := services.JobStatsSalespersonService{}
		jobStatsSalespersonService.StatsSalespersonOrderDailyData(distribution_vo.StatsShopDistributorDailyReq{StartDate: req.OrgCode, EndDate: req.OrgName})

	case "123": //初始化商品订单每日统计数据
		jobStatsService := services.JobStatsService{}
		jobStatsService.StatsOrderDaliyDataRun(req.OrgCode, req.OrgName)
	case "124": //初始化商品分销订单每日统计数据
		jobStatsService := services.JobStatsDisService{}
		jobStatsService.StatsDisOrderDaliyDataRun(req.OrgCode, req.OrgName)
	case "125": //初始化业务员分销订单每日统计数据
		jobStatsService := services.JobStatsSalespersonService{}
		jobStatsService.StatsSalespersonOrderDaliyDataRun(req.OrgCode, req.OrgName)
	case "126": //贵族活动结束
		prizeService := marketsvs.NewPetPrizeService()
		prizeService.GrantRankingPrizes()
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
