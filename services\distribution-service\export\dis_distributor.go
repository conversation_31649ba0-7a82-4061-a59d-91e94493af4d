package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	upetorders "eShop/services/distribution-service/enum/upet-orders"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type DisDistributorTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.DistributorPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销员导出
func (e DisDistributorTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.DistributorPageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	orgId := e.ExportParams.OrgId
	e.SetSheetName(orgId)
	client := services.DistributorManageService{}

	k := 0
	for {
		ret, _, err := client.DistributorPage(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++
			role := "店员"
			if ret[i].DisRole == 1 {
				role = "老板"
			} else if ret[i].DisRole == 3 {
				role = "医生"
			}
			BelongSalesman := ""
			for _, x := range ret[i].BelongSalesman {
				BelongSalesman += x.BelongSalesmanId + "/" + x.BelongSalesmanName + "；"
			}

			axis := fmt.Sprintf("A%d", k+1)
			// 百林康源
			if orgId == 4 {
				status := "启用"
				if ret[i].Status == 2 {
					status = "禁用"
				} else if ret[i].Status == 3 {
					status = "未启用"
				}

				approveState := "待审核"
				if ret[i].ApproveState == 2 {
					approveState = "审核通过"
				} else if ret[i].ApproveState == 3 {
					approveState = "审核失败"
				}

				//"分销员ID", "会员ID", "会员帐号", "分销员姓名", "分销员手机号", "电商店铺", "绑定医院", "企业角色", "分销单数", "分销销售额(元)", "分销佣金总额(元)", "提现成功(元)", "提现申请(元)", "待提现（元）", "分销状态", "认证状态", "审核原因", "加入时间", "更新时间",
				_ = e.writer.SetRow(axis, []interface{}{
					ret[i].Id,                               //分销员ID
					ret[i].MemberId,                         //会员ID
					ret[i].MemberName,                       //会员帐号
					ret[i].RealName,                         //分销员真实姓名
					ret[i].Mobile,                           //分销员手机号
					ret[i].AvailablePoints,                  //当前积分数
					upetorders.InSystemMap[ret[i].InSystem], //分销员类型
					ret[i].VeterinaryCode,                   //兽医资格证编号
					"百林康源",                                  //电商店铺
					ret[i].Province,
					ret[i].City,
					ret[i].HospitalName,                    //绑定医院
					ret[i].DisEnterpriseName,               //绑定的企业
					role,                                   //企业角色
					ret[i].OrderPayNum,                     //分销单数
					utils.Fen2Yuan(ret[i].TotalPaySales),   //分销销售额
					utils.Fen2Yuan(ret[i].TotalCommission), //分销佣金总额
					utils.Fen2Yuan(ret[i].WithdrawSuccess), //提现成功
					utils.Fen2Yuan(ret[i].WithdrawApply),   //提现申请
					utils.Fen2Yuan(ret[i].WaitWithdraw),    //待提现
					status,                                 //分销状态
					approveState,                           //认证状态
					ret[i].Reason,                          //审核原因
					ret[i].CreateTime,                      //加入时间
					ret[i].UpdateTime,                      //更新时间
					ret[i].TuokeSalesmanName,               //邀请人
				})
			} else {
				//"分销员ID","会员ID","会员名称","分销员姓名","分销员手机号","所属业务员", "分销店铺","绑定线下企业","企业R1编码","企业角色","注册业务员","注册业务员Id","分销单数", "分销销售额(元)", "分销佣金总额(元)", "已结佣金(元)", "未结佣金(元)",  "累计客户数", "有效客户数",
				_ = e.writer.SetRow(axis, []interface{}{
					ret[i].Id,                              //分销员ID
					ret[i].MemberId,                        //会员ID
					ret[i].MemberName,                      //会员名称
					ret[i].Name,                            //分销员姓名
					ret[i].Mobile,                          //分销员手机号
					BelongSalesman,                         //所属业务员
					"润合云店",                                 //分销店铺
					ret[i].EnterpriseName,                  //绑定线下企业
					ret[i].EnterpriseCode,                  //企业R1编码
					role,                                   //企业角色
					ret[i].TuokeSalesmanName,               //注册业务员
					ret[i].TuokeSalespersonId,              //注册业务员ID
					ret[i].OrderPayNum,                     //分销单数
					utils.Fen2Yuan(ret[i].TotalPaySales),   //分销销售额
					utils.Fen2Yuan(ret[i].TotalCommission), //分销佣金总额
					utils.Fen2Yuan(ret[i].SettledCommissionTotal),   //已结佣金
					utils.Fen2Yuan(ret[i].UnsettledCommissionTotal), //未结佣金
					ret[i].TotalCustomer,                            //累计客户数
					ret[i].ValidCustomer,                            //有效客户数
				})
			}
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return

}

// SetSheetName 分销员导出列表头
func (e DisDistributorTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"分销员ID", "会员ID", "会员名称", "分销员姓名", "分销员手机号", "所属业务员", "分销店铺", "绑定线下企业", "企业R1编码", "企业角色", "注册业务员", "注册业务员Id", "分销单数", "分销销售额(元)", "分销佣金总额(元)", "已结佣金(元)", "未结佣金(元)", "累计客户数", "有效客户数",
	}
	// 条件：orgId=4
	if args[0] == 4 {
		nameList = []interface{}{
			"分销员ID", "会员ID", "会员帐号", "分销员姓名", "分销员手机号", "当前积分数", "分销员类型", "兽医资格证编号", "电商店铺", "所在省", "所在市", "绑定医院", "绑定线下企业", "企业角色", "分销单数", "分销销售额(元)", "分销佣金总额(元)", "提现成功(元)", "提现申请(元)", "待提现（元）", "分销状态", "认证状态", "审核原因", "加入时间", "更新时间", "邀请人",
		}
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e DisDistributorTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("分销员导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e DisDistributorTask) OperationFunc(row []string, orgId int) string {
	return ""
}
