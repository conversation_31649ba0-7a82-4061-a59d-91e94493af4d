// // 分销企业数据

package services

import (
	"eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

/*
*
SELECT sum(total_salesman) as total_salesman,

	sum(total_enterprise) as total_enterprise,
	sum(total_shop) as total_shop,
	sum(total_distributor) as total_distributor,
	sum(total_trans_enterprise) as total_trans_enterprise,
	sum(ins_total_trans_enterprise) as ins_total_trans_enterprise,
	sum(total_trans_distributor) as total_trans_distributor,
	sum(ins_total_trans_distributor) as ins_total_trans_distributor FROM `eshop`.`stats_enterprise_daily` WHERE (stat_date>="2024-07-01") AND (stat_date<="2024-07-20") LIMIT 1;

-- --------------------------------------------------------

SELECT count(*) as cnt,status FROM `eshop`.`scrm_salesperson` GROUP BY status
SELECT count(*) as cnt,status FROM `eshop`.`scrm_salesperson` WHERE (create_time>="2024-07-01") AND (create_time<="2024-07-20 23:59:59") GROUP BY status;

SELECT count(*) as cnt,status FROM `eshop`.`dis_distributor` WHERE (org_id=3) GROUP BY status;
SELECT count(*) as cnt,status FROM `eshop`.`dis_distributor` WHERE (org_id=3) AND (create_time>="2024-07-01") AND (create_time<="2024-07-20 23:59:59") GROUP BY status
*/
func TestStatsEnterpriseService_GetStatsEntView(t *testing.T) {
	type args struct {
		in distribution_vo.GetKanbanOverviewReq
	}
	tests := []struct {
		name    string
		s       StatsEnterpriseService
		args    args
		wantOut distribution_vo.StatsEntView
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				in: distribution_vo.GetKanbanOverviewReq{
					StartDate: "2024-07-01",
					EndDate:   "2024-07-01",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.s.GetStatsEntView(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsEnterpriseService.GetStatsEntView() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsEnterpriseService.GetStatsEntView() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

/*
-- 新增分销企业
select count(id) from eshop.shop  where org_id=3 and create_time>="2024-07-09" and create_time<="2024-07-09 23:59:59";
-- 新增分销店铺
select count(id) from eshop.shop  where org_id=3 and  is_setted_shop=1 and is_setted_time>="2024-07-09" and is_setted_time<="2024-07-09 23:59:59";
-- 新增成交企业
select

		count(distinct b.shop_id)
	from
		upetmart.upet_orders a
	left join upetmart.upet_order_goods b on
		a.order_id = b.order_id
	where
		a.store_id = 3
		and a.is_dis = 1
		and a.payment_time>0
		and b.shop_id>0
		and a.add_time >="2024-07-09"
		and a.add_time <="2024-07-09 23:59:59";
	-- 新增分销员
	select count(id) from eshop.dis_distributor  where org_id=3 and  create_time>="2024-07-09" and create_time<="2024-07-09 23:59:59";

-- 新增成交分销员
select count(distinct b.dis_member_id) from

	upetmart.upet_orders a
	left join upetmart.upet_order_goods b on
	a.order_id = b.order_id
	where
	a.store_id = 3
	and a.is_dis = 1
	and b.dis_member_id >0
	and a.payment_time >0
	and a.add_time >="2024-07-09" and b.add_time <="2024-07-09 23:59:59";

-- 新增业务员
select count(*) from eshop.scrm_salesperson a where  a.create_time>="2024-06-29" and a.create_time<="2024-06-29 23:59:59";

SELECT * FROM `eshop`.`stats_enterprise_daily` WHERE (stat_date=?) LIMIT 1;
UPDATE `eshop`.`stats_enterprise_daily` SET `total_enterprise` = ?, `total_shop` = ?, `total_trans_enterprise` = ?, `total_distributor` = ?, `total_trans_distributor` = ?, `total_salesman` = ?, `update_time` = ? WHERE (stat_date=?);
*/
func TestStatsEnterpriseService_StatsEnterpriseDaily(t *testing.T) {
	type args struct {
		req []distribution_vo.StatsEnterpriseDailyReq
	}
	req := make([]distribution_vo.StatsEnterpriseDailyReq, 0)
	req = append(req, distribution_vo.StatsEnterpriseDailyReq{StartDate: "2024-07-09"})
	tests := []struct {
		name string
		s    StatsEnterpriseService
		args args
	}{
		// TODO: Add test cases.
		{
			name: "分销企业数据统计表stats_enterprise_daily写入数据",
			args: args{
				req: req,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.s.StatsEnterpriseDaily(tt.args.req...)
		})
	}
}

func TestStatsEnterpriseService_StatsEnterpriseDailyRun(t *testing.T) {
	s := StatsEnterpriseService{}
	s.StatsEnterpriseDailyRun("2023-12-30", "2024-07-14")
	//s.StatsEnterpriseWeeklyRun()
}
