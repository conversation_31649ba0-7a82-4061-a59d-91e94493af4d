package controllers

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/order-service/services"
	marketing_vo "eShop/view-model/marketing-vo"
	vo "eShop/view-model/order-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"

	"github.com/go-chi/chi/v5"
)

func NewShopOrderController(service services.ShopOrderService) *ShopOrderController {
	return &ShopOrderController{
		service: service,
	}
}

// ShopOrderController 门店订单控制器
type ShopOrderController struct {
	service services.ShopOrderService
}

func (w ShopOrderController) RegisterRoutes(r chi.Router) {
	r.Route("/order-app/manager/shop", func(r chi.Router) {
		r.Post("/calc", w.<PERSON><PERSON>)
		r.Post("/commit", w.Commit)
		r.Get("/order-detail", w.Detail)
		r.Post("/performance/save", w.<PERSON>sign<PERSON>om<PERSON>)
	})

	//小程序
	r.Route("/order-app/api", func(r chi.Router) {
		r.Post("/coupon-availability", w.CouponAvailability)
	})
}

// Calc 购物车计算
// @Summary 购物车计算
// @Description 购物车计算
// @Tags 门店订单管理
// @Accept json
// @Produce json
// @Param command body vo.CalcRequest true "请求购物车参数"
// @Success 200 {object} vo.CalcResponse "成功"
// @Failure 400 {object} vo.CalcResponse "请求错误"
// @Router /order-app/manager/shop/calc [post]
func (c *ShopOrderController) Calc(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.CalcRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}
	v := vo.CalcResponse{}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())

		response.BadRequest(w, err.Error())
	}
	cmd.Data.TenantID = jwtInfo.TenantId
	cmd.Data.ChainID = jwtInfo.ChainId

	v, err = c.service.Calc(cmd)

	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, v)

}

// Commit 提交订单
// @Summary 提交订单
// @Description 提交订单
// @Tags 门店订单管理
// @Accept json
// @Produce json
// @Param command body vo.CommitRequest true "提交订单参数"
// @Success 200 {object} vo.CalcResponse "成功"
// @Failure 400 {object} vo.CalcResponse "请求错误"
// @Router /order-app/manager/shop/commit [post]
func (c *ShopOrderController) Commit(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.CommitRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}
	//v := vo.CalcResponse{}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())

		response.BadRequest(w, err.Error())
	}
	if cmd.OrgId == 0 {
		cmd.OrgId = cast.ToInt(r.Header.Get("org_id"))
	}
	cmd.CalcInfo.TenantID = jwtInfo.TenantId
	cmd.CalcInfo.ChainID = jwtInfo.ChainId

	v, err := c.service.Commit(r.Context(), cmd)

	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, v)

}

// Detail 获取订单详情
// @Summary 获取订单详情
// @Description 获取订单详情
// @Tags 门店订单管理
// @Param order_sn query string true "子订单号"
// @Produce json
// @Success 200 {object} vo.DetailResponse
// @Failure 400 {object} vo.DetailResponse
// @Router /order-app/manager/shop/order-detail [get]
func (c *ShopOrderController) Detail(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.DetailRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}
	v, err := c.service.Detail(cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, v)
}

// CheckCouponAvailability 优惠券可用与不可用列表
// @Summary 检查优惠券可用性
// @Description 检查优惠券可用性
// @Tags 门店订单管理
// @Accept json
// @Produce json
// @Param command body marketing_vo.CheckCouponAvailabilityReq true "检查优惠券可用性参数"
// @Success 200 {object} marketing_vo.CheckCouponAvailabilityRes "成功"
// @Failure 400 {object} marketing_vo.CheckCouponAvailabilityRes "请求错误"
// @Router /order-app/api/coupon-availability [post]
func (c *ShopOrderController) CouponAvailability(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.CheckCouponAvailabilityRes{}
	out.Code = 400

	// 1. 参数验证
	param, err := utils.Bind[marketing_vo.CheckCouponAvailabilityReq](r)
	if err != nil {
		log.Errorf("检查优惠券可用性-解析参数失败: %s", err.Error())
		out.Message = "参数解析失败"
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	// 2. 获取store_id
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	param.StoreId = jwtInfo.TenantId
	param.CustomerId = jwtInfo.CustomerId
	// 3. 调用service
	result, err := c.service.CheckCouponAvailability(&param)
	if err != nil {
		log.Errorf("检查优惠券可用性失败: %s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "查询成功"
	out.Data = result.Data
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// AssignCommission 手动分配员工业绩
// @Summary 手动分配员工业绩
// @Description 根据订单号手动触发员工业绩计算
// @Tags 门店订单管理
// @Accept json
// @Produce json
// @Param command body vo.AssignCommissionRequest true "分配业绩请求参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /order-app/manager/shop/performance/save [post]
func (c *ShopOrderController) AssignCommission(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.AssignCommissionRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数: "+err.Error())
		return
	}

	err = c.service.AssignCommission(cmd.OrderSn, cmd.StaffName, cmd.StaffID, cmd.OperatorId, cmd.OperatorName)

	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}
