package distribution_po

// RefundMethod 退款方式常量
const (
	RefundMethodCash     = 1  // 现金
	RefundMethodBalance  = 2  // 余额
	RefundMethodDeposit  = 3  // 押金
	RefundMethodMark     = 4  // 标记收款
	RefundMethodWechat   = 5  // 微信
	RefundMethodAlipay   = 6  // 支付宝
	RefundMethodPos      = 7  // 自有POS
	RefundMethodCredit   = 8  // 挂账
	RefundMethodOther    = 9  // 其它
	RefundMethodCard     = 10 // 储值卡
	RefundMethodScanPay  = 11 // 扫码支付
	RefundMethodTimeCard = 12 // 次卡

)

// BuyType 购买类型
const (
	BuyTypeTimesCard = 1 // 次卡
)

// GetRefundMethodText 获取退款方式文案
func GetRefundMethodText(method int, buyType int) string {
	if buyType == BuyTypeTimesCard {
		return "次卡"
	}

	switch method {
	case RefundMethodCash:
		return "现金"
	case RefundMethodBalance:
		return "余额"
	case RefundMethodDeposit:
		return "押金"
	case RefundMethodMark:
		return "标记收款"
	case RefundMethodWechat:
		return "微信"
	case RefundMethodAlipay:
		return "支付宝"
	case RefundMethodPos:
		return "自有POS"
	case RefundMethodCredit:
		return "挂账"
	case RefundMethodOther:
		return "其它"
	case RefundMethodCard:
		return "储值卡"
	case RefundMethodScanPay:
		return "扫码支付"
	case RefundMethodTimeCard:
		return "次卡"
	default:
		return "未知支付方式"
	}
}
