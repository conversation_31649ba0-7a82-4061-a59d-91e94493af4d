package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/rc4"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"sync"
	"time"

	"github.com/spf13/cast"

	"eShop/infra/config"
	"eShop/infra/log"
)

type SecurityConfig struct {
	securityKey string
	once        sync.Once
}

var securityConfigInstance *SecurityConfig

type DecryptRequest struct {
	CiphertextsArray []string `json:"array"`
	AppId            int      `json:"app-id"`
	UserId           string   `json:"user-id"`
	Timestamp        int      `json:"timestamp"`
	Sign             string   `json:"sign"`
}

func GetSecurityConfig() *SecurityConfig {
	if securityConfigInstance == nil {
		securityConfigInstance = &SecurityConfig{}
		securityConfigInstance.once.Do(func() {
			url := config.Get("getMobileSecurityKeyUrl")

			req := DecryptRequest{
				AppId:     10,
				UserId:    "21",
				Timestamp: cast.ToInt(time.Now().Unix()),
			}

			params := make(map[string]string)
			params["app-id"] = "10"
			params["user-id"] = "21"
			params["timestamp"] = strconv.FormatInt(time.Now().Unix(), 10)
			securityCode := config.Get("security_code")
			req.Sign = sign(securityCode, params)

			var reqBody bytes.Buffer
			jbody, _ := json.Marshal(req)
			reqBody.Write(jbody)
			resp, err := http.Post(url, "application/json", &reqBody)
			if err != nil {
				log.Error("请求data-security获取key失败,error:", err.Error())
				return
			}
			defer resp.Body.Close()
			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				log.Error("请求data-security获取key解析失败,error:", err.Error())
				return
			}
			type Resp struct {
				Key string `json:"key"`
			}
			var res Resp
			if err = json.Unmarshal(body, &res); err != nil {
				log.Error("请求data-security获取key反序列失败,error:", err.Error())
				return
			}
			if res.Key == "" {
				log.Error("请求data-security获取key不能为空")
				return
			}
			securityConfigInstance.securityKey = res.Key
		})
	}
	return securityConfigInstance
}

func GetSecurityKey() string {
	return GetSecurityConfig().securityKey
}

func sign(secretCode string, biz map[string]string) string {
	if biz == nil {
		biz = make(map[string]string)
	}
	//取出map所有key进行正序排序
	keys := make([]string, 0, len(biz))
	for k := range biz {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	// 按顺序进行拼接字符串
	builder := new(bytes.Buffer)
	for _, k := range keys {
		builder.WriteString(k)
		builder.WriteString(biz[k])
	}

	// 最后拼接secretCode
	builder.WriteString(secretCode)
	// 生成md5
	h := md5.New()
	h.Write(builder.Bytes())
	signData := hex.EncodeToString(h.Sum(nil))
	return signData
}

// MobileEncrypt 手机号加密
func MobileEncrypt(mobile string) string {
	if len(mobile) == 0 {
		return ""
	}
	c, _ := rc4.NewCipher([]byte(GetSecurityKey()))
	src := []byte(mobile)
	dst := make([]byte, len(src))
	c.XORKeyStream(dst, src)
	return base64.StdEncoding.EncodeToString(dst)
}

// MobileDecrypt 手机号rc4解密
func MobileDecrypt(ciphertext string) string {
	if len(ciphertext) == 0 {
		return ""
	}
	// 是明文手机号不处理
	if ok, _ := regexp.MatchString(`^\d+$`, ciphertext); ok {
		return ciphertext
	}
	c, _ := rc4.NewCipher([]byte(GetSecurityKey()))
	src, _ := base64.StdEncoding.DecodeString(ciphertext)
	dst := make([]byte, len(src))
	c.XORKeyStream(dst, src)

	return string(dst)
}

// 实现手机号、身份证号、银行卡号的加*号处理
func AddStar(idCard string) string {
	if len(idCard) == 0 {
		return ""
	}
	if len(idCard) == 11 {
		return idCard[:3] + "****" + idCard[7:]
	} else if len(idCard) == 18 {
		return idCard[:6] + "********" + idCard[14:]
	} else if len(idCard) > 14 {
		return idCard[:6] + "******" + idCard[12:]
	}
	return idCard
}

// 校验手机号
func IsValidPhoneNumber(phoneNumber string) bool {
	// 匹配手机号的正则表达式
	pattern := `^1[3-9]\d{9}$`

	// 编译正则表达式
	reg, err := regexp.Compile(pattern)
	if err != nil {
		fmt.Println("正则表达式编译失败:", err)
		return false
	}

	// 使用正则表达式匹配手机号
	return reg.MatchString(phoneNumber)
}

// 校验身份证号码是否有效
func ValidateIDCard(idCard string) bool {
	// 1. 检查长度和格式
	if !regexp.MustCompile(`^\d{17}(\d|X|x)$`).MatchString(idCard) {
		return false
	}

	// 2. 获取出生日期并检查有效性
	birthYear, _ := strconv.Atoi(idCard[6:10])
	birthMonth, _ := strconv.Atoi(idCard[10:12])
	birthDay, _ := strconv.Atoi(idCard[12:14])
	if birthYear < 1900 || birthYear > 2023 || birthMonth < 1 || birthMonth > 12 || birthDay < 1 || birthDay > 31 {
		return false
	}

	// 3. 计算校验位
	weightFactors := []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
	sum := 0
	for i := 0; i < 17; i++ {
		digit, _ := strconv.Atoi(string(idCard[i]))
		sum += digit * weightFactors[i]
	}
	mod := sum % 11
	checkCodeMap := []string{"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"}
	expectedCheckCode := checkCodeMap[mod]

	// 4. 比较校验位
	return string(idCard[17]) == expectedCheckCode
}

// 校验银行卡号是否有效
func ValidateBankCard(cardNumber string) bool {
	return regexp.MustCompile(`^\d{16,19}$`).MatchString(cardNumber)
}

func ValidateUSCC(uscc string) bool {
	if len(uscc) != 18 {
		return false
	}

	// 正则表达式匹配18位数字或大写字母
	matched, _ := regexp.MatchString("^[0-9A-Z]{18}$", uscc)
	if !matched {
		return false
	}
	return true
}
