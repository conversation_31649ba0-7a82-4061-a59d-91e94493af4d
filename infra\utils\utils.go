package utils

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"eShop/infra/log"

	"eShop/infra/pkg/util/cache"
	"eShop/services/distribution-service/enum"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"io"
	"io/ioutil"
	"net/http"
	"regexp"
	"sort"
	"sync"

	config1 "eShop/infra/config"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/shopspring/decimal"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"

	"math/rand"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"
)

const (
	key             = "zej9Fw5oOmsThq5e"
	DATETIME_LAYOUT = "2006-01-02 15:04:05"
)

// 定义包级别的客户端池
var httpClientPool2 = &sync.Pool{
	New: func() interface{} {
		return &http.Client{
			Timeout: time.Minute * 15,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
				DisableKeepAlives:   false,
			},
		}
	},
}

// 返回hash int
func HashInt(s string) int {
	v := int(crc32.ChecksumIEEE([]byte(s)))
	if v >= 0 {
		return v
	}
	if -v >= 0 {
		return -v
	}
	// v == MinInt
	return 0
}

// 捕获异常
func CatchPanic(prefix ...string) {
	p := ""
	if len(prefix) > 0 {
		p = prefix[0]
	}
	if err := recover(); err != nil {
		stack := make([]byte, 4<<10) //4KB
		length := runtime.Stack(stack, false)
		log.Errorf("[PANIC RECOVER-%s] %v %s\n", p, err, stack[:length])
	}
}

// 判断是否是生产环境
func IsProEnv() bool {
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	isPro := true
	switch env {
	case "dev":
		isPro = false
	case "sit1":
		isPro = false
	case "uat":
		isPro = false
	}
	return isPro
}

// 判断是否是开发环境
func IsDevEnv() bool {
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	isDev := false
	if env == "dev" {
		isDev = true
	}
	return isDev
}

// InterfaceToJSON interface 转化成 json 字符串
func InterfaceToJSON(req interface{}) string {
	reqByte, _ := json.Marshal(req)
	return string(reqByte)
}

func IsBankCardValid(n string) bool {
	var sum int
	odd := len(n)&1 == 1
	for i, c := range n {
		if c < '0' || c > '9' {
			return false
		}
		d := int(c - '0')
		if (i&1 == 1) == odd {
			d *= 2
			if d > 9 {
				d -= 9
			}
		}
		sum += d
	}
	return sum%10 == 0
}

// 生成唯一编号
func GenerateNo(prefix string) string {
	// 生成时间戳
	timestamp := time.Now().Unix()

	// 生成随机数
	random := rand.Intn(10000)

	// 生成提现编号
	no := fmt.Sprintf("%s%d%d", prefix, timestamp, random)

	return no
}

// 生成32位md5字串
func GetMd5String(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// 生成json字符串
func JsonEncode(i interface{}) string {
	return string(JsonEncodeByte(i))
}

// JsonDecode 将JSON字符串解码到目标对象
func JsonDecode(data string, v interface{}) error {
	if len(data) == 0 {
		return nil
	}
	err := json.Unmarshal([]byte(data), v)
	if err != nil {
		return fmt.Errorf("JSON解析失败: %v", err)
	}
	return nil
}

// 生成美化后的json字符串
func JsonEncodeBeuty(i interface{}) string {
	bt, _ := json.MarshalIndent(i, "", "\t")
	return string(bt)
}

// 生成json byte切片
func JsonEncodeByte(i interface{}) []byte {
	bt, _ := json.Marshal(i)
	return bt
}

// 判断字串 是否在 主串里
func StringContains(str, subStr string) bool {
	strSli := strings.Split(str, ",")
	m := make(map[string]int)
	for _, v := range strSli {
		m[v] = 0
	}

	if _, ok := m[subStr]; ok {
		return true
	}
	return false
}

// 获取正在运行的函数名
// skip 函数调用层级，1：当前调用函数，2：当前函数上层调用函数，以此类推
func RunFuncName(skip ...int) string {
	if len(skip) == 0 {
		skip = []int{2}
	} else {
		skip[0] += 1
	}

	pc := make([]uintptr, 1)
	runtime.Callers(skip[0], pc)

	return runtime.FuncForPC(pc[0]).Name()
}

// 将处理失败的商品信息导入excel上传至七牛云
func ExportProductErr(errList [][]string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Error("ExportProductErr-panic", ",调用方法:", r, RunFuncName(2), ",请求参数：", errList)
		}
	}()
	f := excelize.NewFile()
	for i, list := range errList {
		var index string
		for j, e := range list {
			if j < 26 {
				index = string(rune(65 + j))
			} else {
				n := j - 26
				index = string(rune(65+n/26)) + string(rune(65+n))
			}
			f.SetCellValue("Sheet1", index+strconv.Itoa(i+1), e)
		}
	}

	f.Save()
	file, err := f.WriteToBuffer()
	if err != nil {
		return "", err
	}

	fileName := cast.ToString(time.Now().UnixNano()) + "_商品错误导出.xlsx"
	uf := &UploadFile{
		Name:   fileName,
		Reader: bytes.NewReader(file.Bytes()),
	}

	return uf.ToQiNiu()

	//bodyBuffer := &bytes.Buffer{}
	//bodyWriter := multipart.NewWriter(bodyBuffer)
	//
	//fileName := cast.ToString(time.Now().UnixNano()) + "_商品错误导出.xlsx"
	//fileWriter, _ := bodyWriter.CreateFormFile("file", fileName)
	//io.Copy(fileWriter, file)
	//path := config.GetString("file-upload-url") + "/fss/up"
	//contentType := bodyWriter.FormDataContentType()
	//bodyWriter.Close()
	//// 上传文件
	//resp, _ := http.Post(path, contentType, bodyBuffer)
	//defer resp.Body.Close()
	//
	//resp_body, err := ioutil.ReadAll(resp.Body)
	//if err != nil {
	//	return "", err
	//}
	//var result uploadResult
	//err = json.Unmarshal(resp_body, &result)
	//if err != nil {
	//	return "", err
	//}
	//if len(result.Url) == 0 {
	//	return "", errors.New(result.Err)
	//}
	//return result.Url, nil
}

func RemoveDuplicates(slice []int) []int {
	result := make([]int, 0)
	seen := make(map[int]struct{})
	for _, v := range slice {
		if _, ok := seen[v]; !ok {
			result = append(result, v)
			seen[v] = struct{}{}
		}
	}
	return result
}

// AesEncrypt
// /加密
func AesEncrypt(orig string) string {
	// 转成字节数组
	origData := []byte(orig)
	k := []byte(key)
	// 分组秘钥
	// NewCipher该函数限制了输入k的长度必须为16, 24或者32
	block, _ := aes.NewCipher(k)
	// 获取秘钥块的长度
	blockSize := block.BlockSize()
	// 补全码
	origData = PKCS7Padding(origData, blockSize)
	// 加密模式
	blockMode := cipher.NewCBCEncrypter(block, k[:blockSize])
	// 创建数组
	cryted := make([]byte, len(origData))
	// 加密
	blockMode.CryptBlocks(cryted, origData)
	return base64.StdEncoding.EncodeToString(cryted)
}

// PKCS7Padding
// 补码
// AES加密数据块分组长度必须为128bit(byte[16])，密钥长度可以是128bit(byte[16])、192bit(byte[24])、256bit(byte[32])中的任意一个。
func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padText...)
}

// 校验手机号
func IsMobile(mobile string) bool {
	if len(mobile) != 11 {
		return false
	}
	reg := regexp.MustCompile(`^1([3-9])\d{9}$`)
	return reg.MatchString(mobile)
}

// 限流，并且重试10次
func LimitRate(key string, limit int, duration time.Duration, nowCount int) bool {

	client := cache.GetRedisConn()
	// 使用 INCR 增加请求计数
	count, err := client.Incr(key).Result()
	if err != nil {
		return false
	}

	// 如果是第一次记录计数，设置过期时间
	if count == 1 {
		if err := client.Expire(key, duration).Err(); err != nil {
			return false
		}
	}

	// 检查是否超过限制
	if count > int64(limit) {
		if nowCount < 10 {
			// 睡眠 1 秒
			time.Sleep(1 * time.Second)
			return LimitRate(key, limit, duration, nowCount+1)
		} else {
			return false
		}
	}
	return true
}

// 设置上下文超时时间
func SetTimeoutCtx(ctx context.Context, timeout ...time.Duration) context.Context {
	if len(timeout) == 0 {
		timeout = []time.Duration{30 * time.Second}
	}
	ctx, _ = context.WithTimeout(ctx, timeout[0])
	return ctx
}

func Md5ToUUID(str string) string {
	md5str := strings.ToUpper(GetMd5(str))
	return fmt.Sprintf("%s-%s-%s-%s-%s", md5str[0:8], md5str[8:12], md5str[12:16], md5str[16:20], md5str[20:32])
}

// 生成md5字串
func GetMd5(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// 获取body中json参数
func GetBodyParams(ctx echo.Context) map[string]string {
	var params map[string]interface{}

	// 针对body数据多次读取，后面读取不到问题处理
	bodyBytes, _ := ioutil.ReadAll(ctx.Request().Body)
	err := json.Unmarshal(bodyBytes, &params)
	if err != nil {
		log.Error("GetBodyParams签名获取body参数错误", err)
	}
	ctx.Request().Body.Close() //  must close
	ctx.Request().Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))

	requestMap := make(map[string]string)
	for k, v := range params {
		requestMap[k] = cast.ToString(v)
	}
	return requestMap
}

func MakeSignForStandardPay(data map[string]string) (string, error) {
	// A、参数校验，app_id,timestamp,secret
	appId := data["app_id"]
	timestamp := data["timestamp"]
	if cast.ToInt(appId) < 1 || cast.ToInt(appId) > 6 { // 1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS 6：上海兽丘
		return "", errors.New("app_id错误")
	}
	if len(timestamp) != 13 {
		return "", errors.New("时间戳错误")
	}
	secret := ""
	switch appId {
	case "2":
		secret = config.GetString("standardPaySecret_zl")
	case "3":
		secret = config.GetString("standardPaySecret_r")
	case "4":
		secret = config.GetString("standardPaySecret_hos")
	case "5":
		secret = config.GetString("standardPaySecret_saas")
	case "6":
		secret = config.GetString("standardPaySecret_sq")
	default:
		secret = config.GetString("standardPaySecret")
	}
	if secret == "" {
		return "", errors.New("查询secret失败")
	}

	//B、字典排序处理，定义一个slice，用来对key进行排序
	s := make([]string, len(data))
	for k := range data {
		s = append(s, k)
	}
	sort.Strings(s)

	//C、参数拼成字符串
	str := ""
	for _, v := range s {
		if data[v] == "" || v == "sign" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + data[v]
	}
	if str == "" {
		return "", errors.New("签名失败")
	}

	// D、进行加密
	str += "&secret=" + secret
	sign := strings.ToUpper(MD5(str))
	return sign, nil
}

func MD5(s string) string {
	data := []byte(s)
	has := md5.Sum(data)
	md5str := fmt.Sprintf("%x", has) //将[]byte转成16进制

	return md5str
}

// 标准支付接口签名校验
func CheckSignForStandardPay(next echo.HandlerFunc) echo.HandlerFunc {

	return func(c echo.Context) error {
		var (
			err  error
			sign string
			list = make(map[string]string)
		)
		list = GetBodyParams(c)
		log.Info("请求参数:", list)

		// app支付签名规则不一样，只需要特定字段参与签名，新增支付宝小程序类型17，18-百度小程序
		if list["trans_type"] == "10" || list["trans_type"] == "11" || list["trans_type"] == "17" || list["trans_type"] == "18" {
			list = map[string]string{
				"app_id":     list["app_id"],
				"notify_url": list["notify_url"],
				"order_name": list["order_name"],
				"order_no":   list["order_no"],
				"pay_amount": list["pay_amount"],
				"pay_total":  list["pay_total"],
				"timestamp":  list["timestamp"],
				"sign":       list["sign"],
			}
		}

		// 签名
		if sign, err = MakeSignForStandardPay(list); err != nil {
			log.Error(err.Error(), "CheckSignForStandardPay请求参数:", list)
		}
		if list["sign"] == "" || sign != list["sign"] {
			log.Error("签名sign不正确", "CheckSignForStandardPay请求参数:", list, sign)
		}

		return next(c)
	}
}

type Pay struct {
	//回调地址
	NotifyURL string `json:"notify_url" validate:"required"`
	//订单名称
	OrderName string `json:"order_name" validate:"required"`
	ProductId string `json:"product_id"`
	//商品描述
	ProductDesc string `json:"product_desc"`
	//订单号
	OrderNo string `json:"order_no" validate:"required"`
	//实付金额,单位：分
	PayAmount int64 `json:"pay_amount" validate:"required"`
	//总金额,单位：分
	PayTotal int64 `json:"pay_total" validate:"required"`
	//签名，互联网医疗和阿闻只有部分字段参与签名，如下：app_id=2&notify_url=https://123&order_name=test&order_no=123&pay_amount=1&pay_total=1&timestamp=1234567891234&secret=5fBgKs5UYD2t11PUzLxQqrRIBDwAwggEKAoIBAQDL2qWFfEVHQ8BAf8EBAMCBs
	Sign string `json:"sign" validate:"required"`
	//1：阿闻，2：子龙，3：R1，4：互联网,，5：SAAS
	AppId int32 `json:"app_id" validate:"required"`
	//1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准） 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付
	TransType int32 `json:"trans_type" validate:"required"`
	//1、竖屏B扫C时：传标准终端绑定接口返回的dyTermNo
	//2、B扫C（标准时）：传电银内部终端号
	TrmId string `json:"trm_id"`
	//微信 JSAPI 支付时必传
	OpenId string `json:"open_id"`
	//子商户公众账号 ID
	SubAppId string `json:"sub_app_id"`
	//1、竖屏B扫C时：传机具编号（tsn）
	//2、B扫C（标准时）：智能 POS 终端的机具编号
	TrmSn string `json:"trm_sn"`
	////交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `json:"order_pay_type"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extend_info"`
	//优惠金额,单位：分
	Discount int64 `json:"discount"`
	//付款码,用户用银联支付宝、微信生成的付款码
	BarCode string `json:"bar_code"`
	//商户号 (trans_type 为12时必传)
	MerchantId string `json:"merchant_id"`
	ClientIP   string `json:"client_ip"`
	//订单有限时间（分）
	ValidTime int32 `json:"valid_time"`
	//时间戳，用于签名（毫秒）
	Timestamp int64 `json:"timestamp"  validate:"required"`
	//前台回调地址  网银支付参数
	FrontUrl string `json:"front_url"`
	//应用渠道标识  App-Android  App-iOS H5 web   网银支付参数
	ChannelType string `json:"channel_type"`
	//产品类型  ENTERPRISE_BANK(企业网银)  QUICK_PAY(快捷) PERSONAL_BANK(个人网银)  COMPLEX_BANK(综合收银)  网银支付参数
	ProdType string `json:"prod_type"`
	//接口类型  0：跳转网页支付 1：直接接口支付  网银支付参数
	InterfaceType string `json:"interface_type"`
	//卡类型  0：借记卡  1：贷记卡  接口类型为直接接口支付时必传   网银支付参数
	CardType string `json:"card_type"`
	//银行编码 接口类型为直接接口支付时必传   网银支付参数
	BankCode string `json:"bank_code"`
}

// MakeStandardPaySign 标准支付参数加签
func MakeStandardPaySign(jsonBytes []byte, appId int32) (string, error) {
	// 使用json.Marshal和Unmarshal构建signMap
	// jsonBytes, err := json.Marshal(params)
	// if err != nil {
	// 	return "", fmt.Errorf("序列化支付参数失败: %v", err)
	// }

	var signMap map[string]interface{}
	if err := json.Unmarshal(jsonBytes, &signMap); err != nil {
		return "", fmt.Errorf("反序列化支付参数失败: %v", err)
	}

	// 获取secret
	secret := ""
	switch appId {
	case 2:
		secret = config.GetString("standardPaySecret_zl")
	case 3:
		secret = config.GetString("standardPaySecret_r")
	case 4:
		secret = config.GetString("standardPaySecret_hos")
	case 5, 7:
		secret = config.GetString("standardPaySecret_saas")
	case 6:
		secret = config.GetString("standardPaySecret_sq")
	default:
		secret = config.GetString("standardPaySecret")
	}
	//secret = "tPZYNMaxfJP6Dp62G75FECb2Yh2wb6zsn9JMvE7xmDb9Fo443uIuEaenCZ59avOR"
	if secret == "" {
		return "", fmt.Errorf("获取支付密钥失败")
	}

	// 字典序排序
	var keys []string
	for k, v := range signMap {
		// 排除空值和sign字段
		if v != nil && v != "" && k != "sign" {
			// 对于数值类型,0也要参与签名
			switch val := v.(type) {
			case string:
				if val != "" {
					keys = append(keys, k)
				}
			default:
				keys = append(keys, k)
			}
		}
	}
	sort.Strings(keys)

	// 拼接待签名字符串
	var signStr string
	for i, k := range keys {
		if i > 0 {
			signStr += "&"
		}
		signStr += k + "=" + cast.ToString(signMap[k])
	}
	signStr += "&secret=" + secret

	// MD5加密并转大写
	sign := strings.ToUpper(MD5(signStr))
	return sign, nil
}

// GetClientIP 获取客户端IP
func GetClientIP(r *http.Request) string {
	ip := r.Header.Get("X-Real-IP")
	if ip == "" {
		ip = r.Header.Get("X-Forwarded-For")
	}
	if ip == "" {
		ip = r.RemoteAddr
	}
	return ip
}

// GetLocationByIP 根据IP地址获取地理位置
func GetLocationByIP(ip string) string {
	// 如果IP为空或为本地IP，返回空
	if ip == "" || ip == "127.0.0.1" || ip == "::1" || strings.HasPrefix(ip, "192.168.") {
		return ""
	}

	// 从配置中获取高德地图API密钥
	// apiKey := config.GetString("amap.key")
	apiKey := "dd26f6f344af5a5ebf7aabe29f5ea4a8"
	if apiKey == "" {
		log.Error("未配置高德地图API密钥，无法获取地理位置信息")
		return ""
	}

	// 构建请求URL
	url := fmt.Sprintf("https://restapi.amap.com/v3/ip?ip=%s&key=%s", ip, apiKey)

	// 发送请求
	client := &http.Client{Timeout: 3 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		log.Errorf("调用高德地图API失败: %v", err)
		return ""
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("读取高德地图API响应失败: %v", err)
		return ""
	}

	// 解析JSON
	var result struct {
		Status    string `json:"status"`
		Province  string `json:"province"`
		City      string `json:"city"`
		Adcode    string `json:"adcode"`
		Rectangle string `json:"rectangle"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		log.Errorf("解析高德地图API响应失败: %v", err)
		return ""
	}

	// 如果API返回失败
	if result.Status != "1" {
		log.Errorf("高德地图API返回错误状态: %s", string(body))
		return ""
	}

	// 组合省市区信息
	location := ""
	if result.Province != "" {
		location = result.Province
		if result.City != "" && result.City != result.Province {
			location += " " + result.City
		}
	}

	return location
}

// 获取税后金额
func GetAfterTaxAmount(amount int, rate string) (AfterTaxAmount int) {
	// 计算所需缴纳税金额（浮点型计算，避免精度丢失）
	// 税后金额：税前金额-税前金额*（1-1/1.08），直接取小数点后两位，不进行四舍五入
	decimalValue := decimal.NewFromFloat(cast.ToFloat64(amount)) // 税前金额
	one := decimal.NewFromFloat(1)
	rateDecimal := decimal.NewFromFloat(cast.ToFloat64(rate))
	taxRate := one.Sub(one.Div(one.Add(rateDecimal))) // 计算1-1/(1+rate)
	taxAmount := decimalValue.Mul(taxRate)            // 税额，直接截取2位小数
	AfterTaxAmount64 := decimalValue.Sub(taxAmount).Truncate(2).IntPart()
	return int(AfterTaxAmount64)
}

// 获取 提现税率
func WithdrawTaxRate(orgId int) string {
	taxRate := config1.Get(fmt.Sprintf(enum.WithdrawTaxRate, orgId))
	log.Info(fmt.Sprintf("提现税率-orgId=%d,taxRate=%s", orgId, taxRate))
	if taxRate == "" {
		taxRate = enum.CommissionTaxRate
	}

	return taxRate
}

// Post
// 发送POST请求
// url：         请求地址
// data：        POST请求提交的数据
// contentType： 请求体格式，如：application/json
// reqbody： 请求提提交的数据，是reader类型
// content：     请求放回的内容
func Post(url string, data []byte, contentType string, headers map[string]string, reqbody ...io.Reader) (string, error) {
	// 超时时间：30秒
	req := new(http.Request)
	var err error
	if len(reqbody) > 0 {
		req, err = http.NewRequest("POST", url, reqbody[0])
	} else {
		req, err = http.NewRequest("POST", url, bytes.NewBuffer(data))
	}

	if err != nil {
		log.Error("post ", url, " error.", err)
		return "", err
	}
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	} else {
		req.Header.Set("Content-Type", "application/json")
	}
	//循环处理请求头信息
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	// 从池中获取客户端
	client := httpClientPool2.Get().(*http.Client)
	defer httpClientPool2.Put(client)
	res, err := client.Do(req)
	if err != nil {
		log.Error(err)
		return "", err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	return string(body), err

}

type CheckContentReq struct {
	// 1: 文本 2: 图片 3: 音频 4: 视频
	Type        int    `json:"type"`
	Content     string `json:"content"`
	FileUrl     string `json:"file_url"`
	CallbackUrl string `json:"callback_url"`
	BizType     string `json:"biz_type"`
}
type CheckContentRes struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Error   string `json:"error"`
	Details struct {
		Suggestion string `json:"suggestion"`
		Label      string `json:"label"`
		SubLabel   string `json:"sub_label"`
		DataId     string `json:"data_id"`
		TaskId     string `json:"task_id"`
	} `json:"details"`
	TotalCount int `json:"total_count"`
}

// CheckContent 内容安全审核接口
func CheckContent(checkContentReq CheckContentReq) bool {
	if len(checkContentReq.Content) == 0 {
		return true
	}
	checkContentReqByte, _ := json.Marshal(checkContentReq)
	method := "/edu/external/digitization/main-moderation"
	baseUrl := config.GetString("sh_edu_url")
	methodUrl := fmt.Sprintf("%s%s", baseUrl, method)
	log.Info("调用教育的内容审核接口(", methodUrl, ")参数：", string(checkContentReqByte))
	if res, err := Post(methodUrl, checkContentReqByte, "", nil); err != nil {
		log.Error("调用教育的内容审核接口(", methodUrl, ")参数：", string(checkContentReqByte))
		return false
	} else {
		log.Info("调用教育的内容审核接口(", methodUrl, ")参数：", string(checkContentReqByte), ";返回结果：", res)
		checkContentRes := CheckContentRes{}
		if err := json.Unmarshal([]byte(res), &checkContentRes); err != nil {
			log.Error("返回结果进行数据解析失败，错误：", err, ";返回结果：", res)
			return false
		} else {
			if checkContentRes.Code == 200 {
				if strings.ToLower(checkContentRes.Details.Suggestion) == "pass" {
					return true
				} else if strings.ToLower(checkContentRes.Details.Label) == "ad" {
					//内容安全审核优化：放开营销、广告等内容拦截，可直接发送-服务端
					return true
				}
			}
		}
	}
	return false
}

func GenerateUUID() string {
	uuidV4 := uuid.New()                        // 生成符合RFC4122的UUIDv4
	uuidStr := uuidV4.String()                  // 格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
	return strings.ReplaceAll(uuidStr, "-", "") // 移除连字符，生成32位小写字符串
}

// GetBJZLRequestHeader 北京处理签名头信息
// 1.拼装参数
// Str1： apiSecret={#apiSecret#}&apiStr={#apiStr#}&apiId={#apiId#}&timestamp={#timestamp#}&apiSecret={#apiSecret#}
//
// 参与签名的参数说明，其中如{#apiSecret#}为参数信息，
// apiSecret 加密秘钥（由业务方提供）
// apiId 加密apiID（由业务方提供）
// apiStr 随机字符串（16位）
// timestamp 当前时间时间戳
//
// 2. 进行MD5加密
// Str2:  md5(Str1)
//
// 3. 将加密后的字符串转换为大写
// Str3： strtoupper(Str2);
//
// 生成Str3就是接口签名Sign
//
// 将以下参数通过header传递
// Sign  对应生成的签名字符串，即Str3的值
// Appid  对应 apiId 加密apiID的值
// Apistr 对应 apiStr的值
// Timestamp 对应 timestamp 的值
func GetBJZLRequestHeader() map[string]string {
	apiId := config1.Get("bj_zl_apiId")
	apiSecret := config1.Get("bj_zl_apiSecret")
	apiStr := GenerateRandomStr(16)
	timeStamp := int32(time.Now().Unix())
	signStr := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%d&apiSecret=%s", apiSecret, apiStr, apiId, timeStamp, apiSecret)
	h := md5.New()
	h.Write([]byte(signStr))
	sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	bjHeaders := make(map[string]string, 0)
	bjHeaders["BrandId"] = "13"
	bjHeaders["Netorgid"] = "99999"
	bjHeaders["Appid"] = apiId
	bjHeaders["Apistr"] = apiStr
	bjHeaders["Timestamp"] = cast.ToString(timeStamp)
	bjHeaders["Sign"] = sign
	return bjHeaders
}

// 随机字符串
func GenerateRandomStr(size int) string {
	iKind, kinds, result := 3, [][]int{{10, 48}, {26, 97}, {26, 65}}, make([]byte, size)
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < size; i++ {
		iKind = rand.Intn(3)
		scope, base := kinds[iKind][0], kinds[iKind][1]
		result[i] = uint8(base + rand.Intn(scope))
	}
	return string(result)
}

// 宠物自诊- 截取疑似疾病
func PetDiagnoseDiseaseName(text string) (result string) {
	// 定位目标段落
	startMarker := "疑似疾病/症状"
	endMarker := "症状原因分析"

	startIdx := strings.Index(text, startMarker)
	if startIdx == -1 {
		fmt.Println("未找到目标段落")
		return
	}

	// 提取目标段落内容
	targetSection := text[startIdx:]
	endIdx := strings.Index(targetSection, endMarker)
	if endIdx != -1 {
		targetSection = targetSection[:endIdx]
	}

	// 分割为行处理
	lines := strings.Split(targetSection, "\n")
	var resultLines []string

	// 标志位用于识别内容范围
	inTarget := false

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		// 检测到 1. 开头的行开始记录
		if strings.HasPrefix(trimmed, "1.") {
			inTarget = true
		}

		// 检测到 2. 开头的行结束记录
		if strings.HasPrefix(trimmed, "2.") {
			break
		}

		if inTarget && len(trimmed) > 0 {
			resultLines = append(resultLines, trimmed)
		}
	}

	// 输出结果
	result = strings.Join(resultLines, "\n")
	fmt.Println("提取结果：")

	// 查找第一个 ** 的位置
	startIdx2 := strings.Index(result, "**")
	if startIdx2 == -1 {
		return "" // 没有找到第一个 **
	}

	// 从第一个 ** 之后开始查找第二个 **
	remaining := result[startIdx2+2:]
	endIdx2 := strings.Index(remaining, "**")
	if endIdx2 == -1 {
		return "" // 没有找到配对的第二个 **
	}

	// 提取两个 ** 之间的内容
	return remaining[:endIdx2]

}

// 记录函数执行时长
func RunningDuration(msg string, ctx ...*context.Context) func() {
	start := time.Now()
	var cc interface{}
	if len(ctx) > 0 {
		cc = ctx[0]
	}
	log.Info(cc, msg, " start at =========================================================", start.Format("2006-01-02 15:04:05"))
	return func() {
		log.Info(cc, msg, " end ===========================================================,经历时长：", time.Since(start).Seconds())
	}
}

// 提取两个标记之间的值
func ExtractBetween(text, start, end string) string {
	startIdx := strings.Index(text, start)
	if startIdx == -1 {
		return ""
	}
	startIdx += len(start)
	endIdx := strings.Index(text[startIdx:], end)
	if endIdx == -1 {
		return text[startIdx:]
	}
	return strings.TrimSpace(text[startIdx : startIdx+endIdx])
}

// 提取指定前缀后的值
func ExtractValue(text, prefix, suffix string) string {
	startIdx := strings.Index(text, prefix)
	if startIdx == -1 {
		return ""
	}
	startIdx += len(prefix)
	endIdx := strings.Index(text[startIdx:], suffix)
	if endIdx == -1 {
		return strings.TrimSpace(text[startIdx:])
	}
	return strings.TrimSpace(text[startIdx : startIdx+endIdx])
}

// func UpImageToOss(imageUrl string) (ossUrl string, err error) {
// 	logPrefix := fmt.Sprintf("将网络图片上传到oss====%s", imageUrl)
// 	req, err := http.NewRequest("GET", imageUrl, nil)
// 	if err != nil {
// 		log.Errorf(logPrefix, "下载环信资源失败，地址：", imageUrl, "。err: ", err)
// 		return
// 	}
// 	resp, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		log.Error("下载环信资源失败，地址：", easemobImg.Url, "。err: ", err)
// 		continue
// 	}
// 	//filenameSuffixArray := strings.Split(easemobImg.Filename, "/")
// 	//filenameSuffix := filenameSuffixArray[len(filenameSuffixArray)-1]
// 	// 在原有代码中添加以下内容
// 	headerBuffer := make([]byte, 512)
// 	n, err := io.ReadFull(resp.Body, headerBuffer)
// 	if err != nil && err != io.ErrUnexpectedEOF {
// 		log.Error("读取文件头失败：", easemobImg.Url, "。err: ", err)
// 		continue
// 	}

// 	// 检测真实文件类型
// 	contentType := http.DetectContentType(headerBuffer[:n])
// 	ext := utils.GetExtensionFromMIME(contentType) // 根据类型获取扩展名

// 	// 组合完整读取器（文件头 + 剩余内容）
// 	fullReader := io.MultiReader(bytes.NewReader(headerBuffer[:n]), resp.Body)

// 	ossUrl, err := utils.UploadFileToOss(fullReader, ext)
// 	if err != nil {
// 		log.Error("上传阿里云失败：", err)
// 		continue
// 	}
// }
