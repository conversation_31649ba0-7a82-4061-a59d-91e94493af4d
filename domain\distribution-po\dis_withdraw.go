package distribution_po

import (
	"eShop/infra/log"
	"time"

	"xorm.io/xorm"
)

type DisWithdraw struct {
	Id                 int       `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`
	OrgId              int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	ShopId             int       `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'"`
	WithdrawNo         string    `json:"withdraw_no" xorm:"default '' comment('提现编号') VARCHAR(50) 'withdraw_no'"`
	Status             int       `json:"status" xorm:"default 0 comment('提现状态：0-默认, 1-未打款，2-已打款，3-已拒绝') INT 'status'"`
	DistributorId      int       `json:"distributor_id" xorm:"default 0 comment('分销员id') INT 'distributor_id'"`
	DisRole            int       `json:"dis_role" xorm:"not null default 0 comment('分销员角色 0-初始值 1-老板 2-店员') INT 'dis_role'"`
	PreTaxAmount       int       `json:"pre_tax_amount" xorm:"default 0 comment('税前提现金额(分)') INT 'pre_tax_amount'"`
	AfterTaxAmount     int       `json:"after_tax_amount" xorm:"default 0 comment('税后提现金额(分)') INT 'after_tax_amount'"`
	BankName           string    `json:"bank_name" xorm:"default '' comment('收款银行') VARCHAR(255) 'bank_name'"`
	BankAccount        string    `json:"bank_account" xorm:"default '' comment('收款账号') VARCHAR(255) 'bank_account'"`
	EncryptBankAccount string    `json:"encrypt_bank_account" xorm:"default '' comment('收款账号') VARCHAR(255) 'encrypt_bank_account'"`
	AccountName        string    `json:"account_name" xorm:"default '' comment('开户姓名') VARCHAR(50) 'account_name'"`
	IdCard             string    `json:"id_card" xorm:"not null default '' comment('身份证号码') VARCHAR(100) 'id_card'"`
	EncryptIdCard      string    `json:"encrypt_id_card"` //身份证号码加密
	BankBranch         string    `json:"bank_branch" xorm:"default '' comment('收款支行') VARCHAR(255) 'bank_branch'"`
	PayTime            time.Time `json:"pay_time" xorm:"default 'null' comment('打款时间') DATETIME 'pay_time'"`
	RejectReason       string    `json:"reject_reason" xorm:"not null default '' comment('拒绝原因') VARCHAR(100) 'reject_reason'"`
	CreateTime         time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime         time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	BankMobile         string    `json:"bank_mobile" xorm:"default '' comment('收款人手机号加*') VARCHAR(50) 'bank_mobile'"`
	BankEncryptMobile  string    `json:"bank_encrypt_mobile" xorm:"default '' comment('收款人加密手机号') VARCHAR(20) 'bank_encrypt_mobile'"`
}

func GetDisWithdrawData(db *xorm.Engine, where map[string]interface{}) (amount int, err error) {
	log.Infof("GetDisWithdrawData开始执行，查询条件: %+v", where)

	session := db.NewSession()
	defer session.Close()

	orgId, ok := where["orgId"]
	if ok {
		session = session.Where("org_id=?", orgId)
		log.Infof("添加筛选条件: orgId=%v", orgId)
	}

	payTimeStartS, ok := where["payTimeStart"]
	if ok {
		session = session.Where("pay_time>=?", payTimeStartS)
		log.Infof("添加筛选条件: payTimeStart=%v", payTimeStartS)
	}
	payTimeE, ok := where["payTimeEnd"]
	if ok {
		session = session.Where("pay_time<=?", payTimeE)
		log.Infof("添加筛选条件: payTimeEnd=%v", payTimeE)
	}
	createTimeS, ok := where["createTimeStart"]
	if ok {
		session = session.Where("create_time>=?", createTimeS)
		log.Infof("添加筛选条件: createTimeStart=%v", createTimeS)
	}
	createTimeE, ok := where["createTimeEnd"]
	if ok {
		session = session.Where("create_time<=?", createTimeE)
		log.Infof("添加筛选条件: createTimeEnd=%v", createTimeE)
	}

	status, ok := where["status"]
	if ok {
		session = session.Where("status=?", status)
		log.Infof("添加筛选条件: status=%v", status)
	}

	groupBy, ok := where["groupBy"]
	if ok {
		groupByStr := groupBy.(string)
		session = session.GroupBy(groupByStr)
		log.Infof("添加分组条件: groupBy=%s", groupByStr)
	}

	// 获取最终SQL语句
	sql, params := session.Table("eshop.dis_withdraw").Select("sum(pre_tax_amount) as pre_tax_amount").LastSQL()
	log.Infof("执行SQL: %s, 参数: %+v", sql, params)

	_, err = session.Table("eshop.dis_withdraw").Select("sum(pre_tax_amount) as pre_tax_amount").Get(&amount)
	if err != nil {
		log.Errorf("GetDisWithdrawData查询失败: %v", err)
		return
	}

	log.Infof("GetDisWithdrawData查询成功，返回金额: %d", amount)
	return
}
