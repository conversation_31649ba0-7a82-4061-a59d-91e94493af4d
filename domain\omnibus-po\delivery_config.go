package omnibus_po

type DeliveryConfig struct {
	ID             int    `xorm:"'id' pk autoincr comment('主键')" json:"id"`
	DeliveryName   string `xorm:"'delivery_name' varchar(100) notnull comment('配送类型名称')" json:"delivery_name"`
	AppKey         string `xorm:"'app_key' varchar(100) comment('app_key')" json:"app_key"`
	AppSecret      string `xorm:"'app_secret' varchar(100) comment('app_secret')" json:"app_secret"`
	SourceID       string `xorm:"'source_id' varchar(100) comment('source_id')" json:"source_id"`
	DeliveryType   int    `xorm:"'delivery_type' int comment('//配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风')" json:"delivery_type"`
	Code           string `xorm:"'code' varchar(500) comment('用于获取token的code')" json:"code"`
	RefreshToken   string `xorm:"'refresh_token' varchar(1000) comment('用于刷新的token')" json:"refresh_token"`
	OrgID          int    `xorm:"'org_id' int notnull default(1)" json:"org_id"`
	StoreID        string `xorm:"'store_id' varchar(500) comment('第三方门店ID')" json:"store_id"`
	ChannelID      int    `xorm:"'channel_id' int notnull default(1) comment('SAAS的才需要这个值')" json:"channel_id"`
	FinanceCode    string `xorm:"'finance_code' varchar(50) comment('SAAS的才需要这个值')" json:"finance_code"`
	DeliveryMethod int    `xorm:"'delivery_method' int default(0) comment('1:外卖平台配送 2:三方配送 3：聚合配送 4：自行配送')" json:"delivery_method"`
	ThirdType      int    `xorm:"'third_type' int default(0) comment('1.麦芽田 2花集通 3聚好送 4青云聚信 5美团配送 6猎豹AI')" json:"third_type"`
	ServiceCode    int    `xorm:"'service_code' int null comment('服务包ID')" json:"service_code"`
	Category       string `xorm:"varchar(50) comment('订单分类(麦芽田枚举)')" json:"category"`
	MytType        string `xorm:"varchar(50) comment('店铺类型 [\"waimai\",\"shop\",\"other\"]')" json:"myt_type"`
	Location       string `xorm:"varchar(100) comment('经纬度')" json:"location"`
	ShopName       string `xorm:"varchar(100) comment('店铺名称')" json:"shop_name"`
}

// TableName sets the table name for the DeliveryConfig struct
func (DeliveryConfig) TableName() string {
	return "datacenter.delivery_config"
}
