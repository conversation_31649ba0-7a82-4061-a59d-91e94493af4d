package points_repo

import (
	po "eShop/domain/points-po"
	"eShop/infra/utils"
	vo "eShop/view-model/points-vo"

	"xorm.io/xorm"
)

type ClsPointsFlowRepo struct {
	SuperRepo[po.ClsPointsFlow]
}

func NewClsPointsFlowRepo() ClsPointsFlowRepo {
	return ClsPointsFlowRepo{
		NewSuperRepo[po.ClsPointsFlow](),
	}
}

// ExpireFlow 过期积分流水
func (r ClsPointsFlowRepo) ExpireFlow(session *xorm.Session, ids []int) error {
	_, err := session.In("id", ids).Update(&po.ClsPointsFlow{Status: 4})
	return err
}

// Page 分页查询积分流水记录
func (r ClsPointsFlowRepo) Page(session *xorm.Session, queryVO vo.ClsPointsFlowQueryVO) ([]vo.ClsPointsFlowResultVO, int64, error) {
	// 1. 构建基础查询
	query := session.Table("cls_points_flow").
		Select(`
			cls_points_flow.id,
			cls_points_flow.dis_id,
			cls_points_flow.enterprise_id,
			cls_points_flow.type,
			cls_points_flow.biz_type,
			cls_points_flow.status,
			cls_points_flow.points,
			cls_points_flow.expire_time,
			cls_points_flow.bill_order_no,
			cls_points_flow.bill_goods_code,
			cls_points_flow.points_order_id,
			cls_points_flow.points_blky,
			cls_points_flow.points_szld,
			cls_points_flow.operator,
			cls_points_flow.occur_time,
			cls_points_flow.region,
			cls_points_flow.remark,
			cls_points_order.order_no,
			cls_points_order.goods_name as order_goods_name,
			cls_points_goods.cost_price,
			upet_goods.goods_name as bill_goods_name,
			upet_goods.brand_id,
			cls_points_source.brand_name,
			dis_distributor.real_name as dis_name,
			dis_distributor.in_system,
			dis_distributor.dis_role,
			dis_distributor.mobile,
			dis_distributor.encrypt_mobile,
			dis_enterprise.enterprise_name,
			dis_distributor_detail.hospital_name`).
		// 积分订单关联（积分兑换）
		Join("LEFT", "cls_points_order", "cls_points_order.id=cls_points_flow.points_order_id AND cls_points_flow.biz_type=3").
		// 积分兑换商品（积分兑换）
		Join("LEFT", "cls_points_goods", "cls_points_goods.id=cls_points_order.goods_id").
		// 商品信息关联（开单商品和退货）
		Join("LEFT", "upetmart.upet_goods", "upet_goods.goods_serial=cls_points_flow.bill_goods_code AND upet_goods.store_id=4 AND cls_points_flow.biz_type IN (1,4)").
		// 品牌来源关联
		Join("LEFT", "cls_points_source", "cls_points_source.brand_id=upet_goods.brand_id").
		// 分销商信息关联
		Join("LEFT", "dis_distributor", "dis_distributor.id=cls_points_flow.dis_id").
		// 店铺信息关联
		Join("LEFT", "shop", "shop.id=dis_distributor.shop_id").
		// 企业信息关联
		Join("LEFT", "dis_enterprise", "dis_enterprise.scrm_enterprise_id=shop.enterprise_id").
		// 分销商详情关联
		Join("LEFT", "dis_distributor_detail", "dis_distributor_detail.dis_id=cls_points_flow.dis_id")

	// 2. 添加查询条件
	conditions := utils.GetQueryCondition(queryVO)
	if conditions != "" {
		query = query.Where(conditions)
	}
	if queryVO.InSystem > -1 {
		query.And("dis_distributor.in_system=?", queryVO.InSystem)
	}
	if len(queryVO.EnterpriseName) > 0 {
		query.And("dis_enterprise.enterprise_name = ? OR dis_enterprise.social_credit_code = ?", queryVO.EnterpriseName, queryVO.EnterpriseName)
	}

	// 3. 处理分页和排序
	pageSize := queryVO.GetSize()
	offset := pageSize * (queryVO.GetCurrent() - 1)

	orderBy := queryVO.GetOrderBy()
	if orderBy != "" {
		query.OrderBy(orderBy)
	} else {
		query.Desc("cls_points_flow.created_at") // 默认按创建时间倒序
	}

	// 4. 执行查询
	var results []vo.ClsPointsFlowResultVO
	total, err := query.Limit(pageSize, offset).FindAndCount(&results)
	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}
