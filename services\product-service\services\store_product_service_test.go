package services

import (
	product_po "eShop/domain/product-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/services/common"
	product_vo "eShop/view-model/product-vo"
	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"xorm.io/xorm"
)

func TestStoreProductService_BatchStoreProduct(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
		JwtInfo     *jwtauth.XCShopPayload
	}
	type args struct {
		in product_vo.BatchStoreProductReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				BaseService: common.BaseService{},
				JwtInfo: &jwtauth.XCShopPayload{
					Token:    "eyJ0eXAiOiJKc29uV2ViVG9rZW4iLCJhbGciOiJIUzI1NiJ9.eyJUZW5hbnRJZCI6IjU3NjUzNDE1NzU5MDE1Mzk3NSIsIlV1aWQiOiJhMTA5OTdmYTcwNGY0N2Q1YmM2NjAwNWI3MjU4YWY5MCIsIlVzZXJJZCI6IjU3MjExNjAxMDYzMjA0MzU4OSIsIlNvdXJjZUNoYWluSWQiOiIwIiwiQ3VzdG9tZXJJZCI6IjAiLCJUZW5hbnRJZHMiOiIiLCJFbXBsb3llZUlkIjoiNTc2NTM0MTU3NTkwMTUzOTc4IiwiQ2hhaW5JZCI6IjU3NjUzNDE1NzU5MDE1Mzg0OSIsImlhdCI6MTc0MDQwMDc4OSwibmJmIjoxNzQwNDAwNzg5LCJleHAiOjE3NDI5OTI3ODl9.z1rE8b2TiUkgorGVSYnHen80FeAPZnoQaAUgPiXp-qA",
					TenantId: "576534157590153975",
					ChainId:  "530185752454150978",
					UserId:   "test_user_id",
					UserName: "test_user",
					RoleType: 2,
				},
			},
			args: args{
				// 类型 1-铺品 2-上架 3-下架 4库存同步  5-商品调价 6-删除 7-编辑
				// 铺品
				//in: product_vo.BatchStoreProductReq{
				//	ProductIds: "111007",
				//	//ProductIds: "111008",
				//	//ProductIds: "111008",
				//	ChannelIds: "3",
				//	Type:       1,
				//	//PriceType:       2,
				//	//ChannlePriceStr: "1-500|2-501",
				//	TenantId: "530094312600386773",
				//},

				//编辑
				//in: product_vo.BatchStoreProductReq{
				//	ProductIds: "111009",
				//	ChannelIds: "3",
				//	Type:       7,
				//	TenantId:   "576534157590153975",
				//},

				//删除
				in: product_vo.BatchStoreProductReq{
					ProductIds: "105502",
					Type:       6,
					TenantId:   "576534157590153975",
					SyncType:   1,
				},

				//in: product_vo.BatchStoreProductReq{
				//	//ProductIds: "100138",
				//	ProductIds: "111008",
				//	// SkuIds:     "100138001",
				//	// ChannelIds: "2",
				//	Type:     6,
				//	TenantId: "530094312600386773",
				//},

				// 上下架
				//in: product_vo.BatchStoreProductReq{
				//	ProductIds: "101336,101348",
				//	ChannelIds: "2",
				//	Type:       2, //2-上架 3-下架
				//	TenantId:   "576534157590153975",
				//},

				// 同步库存
				// in: product_vo.BatchStoreProductReq{
				// 	SkuIds: "107975001",
				// 	//ProductIds: "107186",
				// 	ChannelIds: "3",
				// 	Type:       4,
				// 	TenantId:   "576534157590153975",
				// },

				// 调价
				//in: product_vo.BatchStoreProductReq{
				//	SkuIds:    "105709001",
				//	Type:      5,
				//	PriceType: 1,
				//	//ChannlePriceStr: "2-880",
				//	TenantId: "576534157590153975",
				//},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreProductService{
				BaseService: tt.fields.BaseService,
				JwtInfo:     tt.fields.JwtInfo,
			}
			log.Init()
			if _, err := s.BatchStoreProduct(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("BatchStoreProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreProductServiceData_GetProduct(t *testing.T) {
	type fields struct {
		Engine        *xorm.Engine
		SkuIds        []int
		ChannelIds    []int
		Params        product_vo.BatchStoreProductReq
		FinanceCodes  []string
		StoreProducts []*product_po.ProProductStoreAppChannel
		UpResult      []*ChannelProductUp_Result
		UnknownError  error
		TaskId        int
	}
	type args struct {
		ChannelIds []int
		SkuIds     []int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ChannelIds: []int{1, 2, 3},
				SkuIds:     []int{3, 11},
			},
		},
	}
	ss := common.BaseService{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &StoreProductServiceData{
				Engine:        ss.Engine,
				SkuIds:        tt.fields.SkuIds,
				ChannelIds:    tt.fields.ChannelIds,
				Params:        tt.fields.Params,
				FinanceCodes:  tt.fields.FinanceCodes,
				StoreProducts: tt.fields.StoreProducts,
				UpResult:      tt.fields.UpResult,
				UnknownError:  tt.fields.UnknownError,
				TaskId:        tt.fields.TaskId,
			}
			if err := s.GetProduct(tt.args.ChannelIds, tt.args.SkuIds); (err != nil) != tt.wantErr {
				t.Errorf("GetProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreProductService_FindStoreProductList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in product_vo.FindStoreProductListReq
	}
	log.Init()
	tests := []struct {
		name      string
		fields    fields
		args      args
		wantOut   []product_vo.FindStoreProductList
		wantTotal int64
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				//{"page_index":1,"page_size":5,"chain_id":"530185752454150978","store_id":"530185752454150979","channel_id":0,"status":0,"query":"","type":0,"category_id":0,"category_id_offline":"","category_level":0,"is_stats":false,"exprot":0}
				in: product_vo.FindStoreProductListReq{
					StoreId: "530185752454150979",
					ChainId: "530185752454150978",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreProductService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, gotTotal, err := s.FindStoreProductList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindStoreProductList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("FindStoreProductList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("FindStoreProductList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestStoreProductService_BatchStoreProduct1(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in product_vo.BatchStoreProductReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "tst",
			args: args{
				in: product_vo.BatchStoreProductReq{
					//SkuIds: "100322001",
					ProductIds: "100138",
					Type:       1,
					ChannelIds: "3",
					TenantId:   "530094312600386773",
				},

				//调价
				//in: product_vo.BatchStoreProductReq{
				//	SkuIds:          "100844002",
				//	Type:            6,
				//	ChannlePriceStr: "2-210",
				//	TenantId:        "530219708465609002",
				//	PriceType:       2,
				//},
				//{"sku_ids":"81","type":1,"channel_ids":"3","price_type":0,"channle_price_str":"","tenant_id":"530219708465609002","UserId":"","UserName":""}
				//in: product_vo.BatchStoreProductReq{
				//	SkuIds:     "60",
				//	Type:       2,
				//	ChannelIds: "2,3",
				//	TenantId:   "530219708465609002",
				//},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreProductService{
				BaseService: tt.fields.BaseService,
			}
			if _, err := s.BatchStoreProduct(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("BatchStoreProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreProductService_EditStoreProduct(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in product_vo.EditStoreProductReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				//{"product_id":66,"channel_category_id":"20020","channel_category_name":"猫的牵引绳","store_id":"530219708465609002"}
				in: product_vo.EditStoreProductReq{
					ProductId:           111007,
					ChannelCategoryId:   "13",
					ChannelCategoryName: "猫馋嘴零售",
					StoreId:             "530094312600386773",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := StoreProductService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.EditStoreProduct(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("EditStoreProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStoreProductService_FindStoreSkuList(t *testing.T) {
	type args struct {
		in product_vo.FindStoreSkuListReq
	}
	tests := []struct {
		name      string
		s         *StoreProductService
		args      args
		wantOut   []product_vo.FindStoreProductList
		wantTotal int64
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				in: product_vo.FindStoreSkuListReq{
					StoreId:   "530219708465609002",
					ChannelId: 3,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.s.FindStoreSkuList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StoreProductService.FindStoreSkuList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StoreProductService.FindStoreSkuList() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("StoreProductService.FindStoreSkuList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}
