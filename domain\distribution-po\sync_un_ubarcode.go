package distribution_po

import (
	"time"

	"xorm.io/xorm"
)

// SyncUnUbarcode 不可用箱码记录表
type SyncUnUbarcode struct {
	Id         int       `xorm:"pk autoincr 'id'" json:"id"`                      // 自增主键
	Ubarcode   string    `xorm:"varchar(100) notnull 'ubarcode'" json:"ubarcode"` // 箱码
	Type       int       `xorm:"int notnull 'type'" json:"type"`                  // 类型 1-整箱扫码 2-拆包装 3-退货
	CreateTime time.Time `xorm:"created 'create_time'" json:"create_time"`        // 创建时间
	UpdateTime time.Time `xorm:"updated 'update_time'" json:"update_time"`        // 更新时间
}

// TableName 表名
func (s SyncUnUbarcode) TableName() string {
	return "blky.sync_un_ubarcode"
}

// AddUnUbarcode 添加不可用箱码记录
func (s *SyncUnUbarcode) AddUnUbarcode(session *xorm.Session, ubarcode string, unUbarcodeType int) error {
	s.Ubarcode = ubarcode
	s.Type = unUbarcodeType
	_, err := session.Insert(s)
	return err
}

// IsUnUbarcode 检查箱码是否已经是不可用状态
func (s *SyncUnUbarcode) IsUnUbarcode(session *xorm.Session, ubarcode string) (bool, error) {
	return session.Where("ubarcode = ?", ubarcode).Exist(s)
}

// DeleteUnUbarcode 删除不可用箱码记录
func (s *SyncUnUbarcode) DeleteUnUbarcode(session *xorm.Session, ubarcode string) (bool, error) {
	result, err := session.Where("ubarcode = ?", ubarcode).Delete(s)
	if err != nil {
		return false, err
	}
	return result > 0, nil
}

// GetUnUbarcodeByType 根据类型查询不可用箱码记录
func (s *SyncUnUbarcode) GetUnUbarcodeByType(session *xorm.Session, ubarcode string, ubarcodeType int) (bool, error) {
	return session.Where("type = ? and ubarcode = ?", ubarcodeType, ubarcode).Get(s)
}
