package services

import (
	po "eShop/domain/points-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	repo "eShop/repo/points-repo"
	vo "eShop/view-model/points-vo"
	"math"
	"slices"
	"strconv"
	"time"

	"eShop/infra/errors"

	"fmt"
	"strings"

	"xorm.io/xorm"
)

// ClsPointsFlowService 提供了积分流水相关的服务
type ClsPointsFlowService struct {
	SuperService[int, po.ClsPointsFlow, vo.ClsPointsFlowSaveVO, vo.ClsPointsFlowUpdateVO, vo.ClsPointsFlowQueryVO, vo.ClsPointsFlowResultVO]
	repo repo.ClsPointsFlowRepo
}

// NewClsPointsFlowService 创建一个新的 ClsPointsFlowService 实例
func NewClsPointsFlowService() ClsPointsFlowService {
	return ClsPointsFlowService{
		NewSuperService(
			&ClsPointsFlowHooks{
				NewServiceHooks[int, po.ClsPointsFlow, vo.ClsPointsFlowSaveVO, vo.ClsPointsFlowUpdateVO, vo.ClsPointsFlowQueryVO, vo.ClsPointsFlowResultVO](),
			},
		),
		repo.NewClsPointsFlowRepo(),
	}
}

// SalesIn 处理销售积分入账
// curDate: 销售日期，格式："2025-06-08"，为空时默认为昨天
func (s ClsPointsFlowService) SalesIn(session *xorm.Session, curDate string) error {
	if curDate == "" {
		curDate = time.Now().Format("2006-01-02")
	}
	// 1. 准备销售数据
	salesMap, err := s.prepareSalesData(session, curDate)
	if err != nil {
		return err
	}

	// 2. 准备商品相关数据
	goodsData, err := s.prepareGoodsData(session, curDate, salesMap)
	if err != nil {
		return err
	}

	// 3. 准备操作员相关数据
	operateUserIds := make([]string, 0, len(salesMap))
	for operateUserId := range salesMap {
		operateUserIds = append(operateUserIds, operateUserId)
	}
	operatorMap, err := NewClsPointsAccountService().GetOperatorMap(session, operateUserIds)
	if err != nil {
		return err
	}

	// 4. 处理积分流水
	allFlows, errMsg := s.processFlows(curDate, salesMap, operatorMap, goodsData)

	// 5. 批量保存数据
	if err := s.batchSaveFlows(session, curDate, allFlows); err != nil {
		return errors.NewBadRequest("批量创建积分流水失败：" + err.Error())
	}

	if len(errMsg) > 0 {
		return errors.NewBadRequest("部分处理失败：\n" + strings.Join(errMsg, "\n"))
	}

	return nil
}

// prepareSalesData 准备销售数据
func (s ClsPointsFlowService) prepareSalesData(session *xorm.Session, curDate string) (map[string][]po.ZlDoctorSaleGoods, error) {
	var sales []po.ZlDoctorSaleGoods

	// curDate减一天
	curTime, err := time.Parse("2006-01-02", curDate)
	if err != nil {
		return nil, errors.NewBadRequest("日期格式错误：" + err.Error())
	}
	preDate := curTime.AddDate(0, 0, -1).Format("2006-01-02")

	err = s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		sales, err = repo.NewZlDoctorSaleGoodsRepo().List(tx, vo.ZlDoctorSaleGoodsQueryVO{
			Dt: preDate,
		})
		return err
	})
	if err != nil {
		return nil, errors.NewBadRequest("查询开单记录失败：" + err.Error())
	}
	if len(sales) == 0 {
		return nil, errors.NewBadRequest("日期：" + preDate + "，没有开单记录")
	}

	// 按操作员ID分组
	salesMap := make(map[string][]po.ZlDoctorSaleGoods)
	for _, sale := range sales {
		if _, ok := salesMap[sale.OperateUserId]; !ok {
			salesMap[sale.OperateUserId] = make([]po.ZlDoctorSaleGoods, 0)
		}
		salesMap[sale.OperateUserId] = append(salesMap[sale.OperateUserId], sale)
	}

	return salesMap, nil
}

// GoodsData 商品相关数据
type GoodsData struct {
	SourceMap map[string]int                // 商品编码到sourceId的映射
	RuleMap   map[string][]po.ClsPointsRule // 商品编码到规则的映射
}

// prepareGoodsData 准备商品相关数据
func (s ClsPointsFlowService) prepareGoodsData(session *xorm.Session, curDate string, salesMap map[string][]po.ZlDoctorSaleGoods) (GoodsData, error) {
	// 收集所有商品编码
	goodsCodes := make([]string, 0)
	goodsRegionMap := make(map[string][]string)
	for _, sales := range salesMap {
		for _, sale := range sales {
			if !slices.Contains(goodsCodes, sale.R1Code) {
				goodsCodes = append(goodsCodes, sale.R1Code)
				goodsRegionMap[sale.R1Code] = []string{sale.Area}
			} else {
				goodsRegionMap[sale.R1Code] = append(goodsRegionMap[sale.R1Code], sale.Area)
			}
		}
	}

	// 查询商品品牌信息
	sourceMap, err := s.queryGoodsSource(session, goodsCodes)
	if err != nil {
		return GoodsData{}, err
	}

	// 查询积分规则
	ruleMap, err := s.queryPointsRules(session, curDate, goodsCodes, goodsRegionMap)
	if err != nil {
		return GoodsData{}, err
	}

	return GoodsData{
		SourceMap: sourceMap,
		RuleMap:   ruleMap,
	}, nil
}

// queryGoodsSource 查询商品品牌信息
func (s ClsPointsFlowService) queryGoodsSource(session *xorm.Session, goodsCodes []string) (map[string]int, error) {
	type GoodsSourceInfo struct {
		GoodsSerial string `xorm:"goods_serial"`
		SourceId    int    `xorm:"source_id"`
	}
	var goodsSourceList []GoodsSourceInfo
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		return tx.Table("upetmart.upet_goods").Alias("ug").
			Join("LEFT", "cls_points_source cps", "cps.brand_id = ug.brand_id").
			Cols("ug.goods_serial", "cps.source_id").
			In("ug.goods_serial", goodsCodes).And("ug.store_id=4").
			Find(&goodsSourceList)
	})
	if err != nil {
		return nil, errors.NewBadRequest("批量查询商品品牌信息失败：" + err.Error())
	}

	sourceMap := make(map[string]int)
	for _, gs := range goodsSourceList {
		sourceMap[gs.GoodsSerial] = gs.SourceId
	}
	return sourceMap, nil
}

// queryPointsRules 查询积分规则
func (s ClsPointsFlowService) queryPointsRules(session *xorm.Session, curDate string, goodsCodes []string, regionMap map[string][]string) (map[string][]po.ClsPointsRule, error) {
	// curDate减一天
	curTime, err := time.Parse("2006-01-02", curDate)
	if err != nil {
		return nil, errors.NewBadRequest("日期格式错误：" + err.Error())
	}
	preDate := curTime.AddDate(0, 0, -1).Format("2006-01-02")

	var rules []po.ClsPointsRule
	err = s.tm.NotSupported(session, func(tx *xorm.Session) error {
		return tx.Table("cls_points_rule").
			In("goods_code", goodsCodes).
			And("type = ?", 1).
			And("status = ?", 1).
			And("start_time <= ?", preDate).
			And("end_time >= ?", preDate).
			Find(&rules)
	})
	if err != nil {
		return nil, errors.NewBadRequest("批量查询积分规则失败：" + err.Error())
	}

	ruleMap := make(map[string][]po.ClsPointsRule)
	for _, rule := range rules {
		if regions, ok := regionMap[rule.GoodsCode]; ok && slices.Contains(regions, rule.Region) {
			ruleMap[rule.GoodsCode] = append(ruleMap[rule.GoodsCode], rule)
		}
	}
	return ruleMap, nil
}

// processFlows 处理积分流水
func (s ClsPointsFlowService) processFlows(
	curDate string,
	salesMap map[string][]po.ZlDoctorSaleGoods,
	operatorMap map[string]vo.SalesOperatorVO,
	goodsData GoodsData,
) ([]po.ClsPointsFlow, []string) {
	var allFlows []po.ClsPointsFlow
	var errMsg []string

	// curDate转time.Time
	occurTime, err := time.ParseInLocation("2006-01-02", curDate, time.Local)
	if err != nil {
		errMsg = append(errMsg, "日期格式错误")
	}

	// 计算失效时间 次年同月+3个月后最后一天过期
	expireTime := time.Date(occurTime.Year()+1, occurTime.Month(), 1, 23, 59, 59, 59, time.Local).AddDate(0, 4, -1)

	// 处理每个操作员的销售数据
	for operateUserId, sales := range salesMap {
		task := vo.SalesTask{
			OperateUserId:  operateUserId,
			Sales:          sales,
			Operator:       operatorMap[operateUserId],
			GoodsSourceMap: goodsData.SourceMap,
			RuleMap:        goodsData.RuleMap,
			OccurTime:      occurTime,
			ExpireTime:     expireTime,
		}

		// 处理单个任务
		flows, err := s.packFlows(task)
		if err != nil {
			errMsg = append(errMsg, fmt.Sprintf("处理用户 %s 失败: %v", operateUserId, err))
		} else {
			allFlows = append(allFlows, flows...)
		}
	}

	return allFlows, errMsg
}

// packFlows 在内存中处理销售数据并生成积分流水记录
func (s ClsPointsFlowService) packFlows(task vo.SalesTask) ([]po.ClsPointsFlow, error) {
	if len(task.Sales) == 0 {
		return nil, errors.NewBadRequest("没有开单记录")
	}

	// 验证操作员信息
	if len(task.Operator.StaffNo) == 0 {
		return nil, errors.NewBadRequest("未找到分销商记录，StaffNo=" + task.Operator.StaffNo)
	}

	flows := make([]po.ClsPointsFlow, 0, len(task.Sales))
	for _, sale := range task.Sales {
		flow, err := s.createFlowFromSale(sale, task)
		if err != nil {
			continue
		}
		flows = append(flows, flow)
	}

	return flows, nil
}

// createFlowFromSale 从销售记录创建积分流水
func (s ClsPointsFlowService) createFlowFromSale(sale po.ZlDoctorSaleGoods, task vo.SalesTask) (po.ClsPointsFlow, error) {
	// 获取积分规则
	rules, ok := task.RuleMap[sale.R1Code]
	if !ok || len(rules) == 0 {
		return po.ClsPointsFlow{}, errors.NewBadRequest("未找到商品积分规则：" + sale.R1Code)
	}

	// 获取商品来源
	sourceId := task.GoodsSourceMap[sale.R1Code]
	if sourceId == 0 {
		return po.ClsPointsFlow{}, errors.NewBadRequest("未找到商品品牌信息：" + sale.R1Code)
	}

	// 计算积分
	points := 0
	for _, rule := range rules {
		if sale.Area == rule.Region {
			points = int(math.Abs(float64(rule.Points) * sale.ItemNum))
			break
		}
	}
	if points == 0 {
		return po.ClsPointsFlow{}, errors.NewBadRequest("积分值为0：" + sale.R1Code)
	}
	bizType := 1
	flowType := 1
	remainingPoints := int(points)

	// 根据来源分配积分
	var pointsBlky, pointsSzld int
	if sourceId == 1 {
		pointsBlky = int(points)
	} else if sourceId == 2 {
		pointsSzld = int(points)
	}

	if sale.ItemNum < 0 {
		bizType = 4  // 商品退货积分扣减
		flowType = 2 // 积分出账
		remainingPoints = -remainingPoints
		pointsBlky = -pointsBlky
		pointsSzld = -pointsSzld
	}

	// 创建积分流水记录
	return po.ClsPointsFlow{
		DisId:           task.Operator.DisId,
		EnterpriseId:    task.Operator.EnterpriseId,
		Type:            flowType, // 积分进账
		BizType:         bizType,  // 开单商品
		Status:          1,        // 待使用
		Points:          int(points),
		RemainingPoints: remainingPoints, // 初始剩余积分等于积分值
		BillId:          sale.Id,
		BillOrderNo:     sale.OrderNumber,
		BillGoodsCode:   sale.R1Code,
		PointsBlky:      pointsBlky,
		PointsSzld:      pointsSzld,
		ExpireTime:      task.ExpireTime,
		Operator:        task.Operator.RealName,
		StaffNo:         task.Operator.StaffNo,
		Region:          sale.Area,
		OccurTime:       task.OccurTime,
	}, nil
}

// batchSaveFlows 批量保存积分流水
func (s ClsPointsFlowService) batchSaveFlows(session *xorm.Session, curDate string, flows []po.ClsPointsFlow) error {
	return s.tm.Required(session, func(tx *xorm.Session) error {
		// 批量插入积分流水
		_, err := s.repo.BatchCreate(tx, flows)
		if err != nil {
			return err
		}

		// 收集需要更新的账户
		accountUpdates := make(map[int]int) // disId -> enterpriseId
		for _, flow := range flows {
			if flow.DisId > 0 {
				accountUpdates[flow.DisId] = flow.EnterpriseId
			}
		}

		// 批量更新账户
		accountService := NewClsPointsAccountService()
		if err := accountService.BatchUpdateByDisIds(tx, accountUpdates, curDate, 1); err != nil {
			// 批量更新内部已经处理了日志，这里可以选择是否要中断整个事务
			log.Errorf("批量更新积分账户过程中发生错误: %v", err)
			// return err // 如果希望单个用户的失败导致整体回滚，则取消此行注释
		}

		// 更新或创建每日统计数据
		dailyStatsService := NewClsPointsDailyStatsService()
		if err := dailyStatsService.StatSalesIn(tx, curDate, flows); err != nil {
			log.Error("积分流水，更新积分账户失败：error=", err)
		}

		return nil
	})
}

// OrderPointOut 处理积分兑换订单的积分流水记录
func (s ClsPointsFlowService) OrderPointOut(session *xorm.Session, order po.ClsPointsOrder) error {
	// 1. 根据order.DisId，查询积分账户
	disId := order.DisId
	pointsCost := order.PointsCost
	log.Info("OrderPointOut result" + utils.JsonEncode(order))

	// 根据disId查出enterpirseId
	accountService := NewClsPointsAccountService()
	distributor, err := accountService.GetDistributor(session, disId)
	if err != nil {
		return errors.NewBadRequest("分销员信息失败: " + err.Error())
	}
	enterpiseId := distributor.EnterpriseId

	account, err := accountService.Query(session, vo.ClsPointsAccountQueryVO{
		DisId: disId,
	})
	if err != nil {
		log.Error("积分兑换，查询积分帐号失败：dis_id=", disId, "enterprise_id=", enterpiseId, "error=", err)
		return err
	}

	if account.AvailablePoints < pointsCost {
		log.Error("积分兑换，检查到可用积分不够")
		return errors.New("可用积分不够")
	}

	// 2. 根据需要消耗的积分数和disId，查询需要扣减的积分流水列表
	flow, err := s.CostFlows(session, enterpiseId, disId, pointsCost)
	if err != nil {
		return errors.NewBadRequest("积分兑换，检查到可用积分不够")
	}

	// 更新积分账户
	updAccount := make(map[int]int)
	updAccount[disId] = enterpiseId
	if err = accountService.BatchUpdateByDisIds(session, updAccount, "", 2); err != nil {
		return err
	}

	// 3. 创建积分流水记录并更新相关数据
	// 3.1 创建积分流水记录
	_, err = s.repo.Create(session, po.ClsPointsFlow{
		DisId:         disId,
		EnterpriseId:  enterpiseId,
		Type:          2, // 积分出账
		BizType:       3, // 积分兑换
		Points:        pointsCost,
		PointsOrderId: order.Id,
		PointsBlky:    flow.PointsBlky,
		PointsSzld:    flow.PointsSzld,
		Operator:      distributor.RealName,
		Region:        flow.Region,
		OccurTime:     order.OrderTime,
		StaffNo:       flow.StaffNo,
	})
	if err != nil {
		return errors.NewBadRequest("创建积分流水记录失败：" + err.Error())
	}

	// 3.3 更新每日统计
	statsRepo := repo.NewClsPointsDailyStatsRepo()
	if err = statsRepo.SaveOrUpdate(session, po.ClsPointsDailyStats{
		StatDate:     order.OrderTime,
		ConsumeTotal: pointsCost,
		ConsumeBlky:  flow.PointsBlky,
		ConsumeSzld:  flow.PointsSzld,
	}); err != nil {
		return err
	}

	return nil
}

func (s ClsPointsFlowService) CostFlows(session *xorm.Session, enterpriseId, disId, pointsCost int) (po.ClsPointsFlow, error) {
	var empty po.ClsPointsFlow
	var pointsBlky int
	var pointsSzld int
	var flows []vo.CostFlow
	var err error
	flows, err = s.ListCostFlows(session, enterpriseId, disId, pointsCost)
	if err != nil {
		return empty, err
	}

	// 遍历flows
	for _, flow := range flows {
		var clsPointsFlow po.ClsPointsFlow
		var costPoints int
		// 优化后的逻辑，简化分支结构
		if flow.TotalSum > pointsCost {
			// 当前流水部分使用
			clsPointsFlow.Status = 2
			clsPointsFlow.RemainingPoints = flow.TotalSum - pointsCost
			costPoints = flow.RemainingPoints - clsPointsFlow.RemainingPoints
		} else {
			// 当前流水全部用完
			clsPointsFlow.Status = 3
			clsPointsFlow.RemainingPoints = 0
			costPoints = flow.RemainingPoints
		}

		remainingBlky := flow.RemainingPoints - flow.PointsSzld
		if costPoints != flow.RemainingPoints {
			// 部分使用
			if remainingBlky > 0 {
				if remainingBlky > costPoints {
					remainingBlky = costPoints
				}
			} else {
				remainingBlky = 0
			}
		} else {
			// 全部使用
			if remainingBlky < 0 {
				if flow.PointsBlky < 0 {
					remainingBlky = flow.PointsBlky
				} else {
					remainingBlky = 0
				}
			}
		}
		pointsBlky += remainingBlky
		pointsSzld += (costPoints - remainingBlky)

		_, err = session.Cols("status", "remaining_points").ID(flow.Id).Update(clsPointsFlow)
		if err != nil {
			return empty, errors.New("更新积分流水记录失败：" + err.Error())
		}
	}

	return po.ClsPointsFlow{
		EnterpriseId: enterpriseId,
		PointsBlky:   pointsBlky,
		PointsSzld:   pointsSzld,
		Region:       flows[len(flows)-1].Region,
		Operator:     flows[0].Operator,
		StaffNo:      flows[0].StaffNo,
	}, nil
}

// ListCostFlows 查询需要扣减的积分流水列表
func (s ClsPointsFlowService) ListCostFlows(session *xorm.Session, enterpriseId, disId, pointsCost int) ([]vo.CostFlow, error) {
	baseSQL := "SELECT id, dis_id, enterprise_id, points, remaining_points, operator, region, staff_no, points_blky, points_szld, total_sum " +
		"FROM ( " +
		"SELECT id, dis_id, enterprise_id, points, remaining_points, operator, region, staff_no, points_blky, points_szld, " +
		"SUM(remaining_points) OVER (ORDER BY created_at ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS total_sum " +
		"FROM cls_points_flow WHERE "
	var whereSQL string
	var param int

	whereSQL = "enterprise_id = ? AND status IN (1, 2) "
	param = enterpriseId
	// 后续补充体系外的逻辑，这里if就需要加条件
	if true {
		whereSQL = "dis_id = ? AND status IN (1, 2) "
		param = disId
	}

	orderSQL := "ORDER BY occur_time,id) t WHERE total_sum < ? + remaining_points"
	sql := baseSQL + whereSQL + orderSQL

	var flows []vo.CostFlow
	err := session.SQL(sql, param, pointsCost).Find(&flows)
	if err != nil {
		return nil, errors.NewBadRequest("查询积分流水失败：" + err.Error())
	}

	if len(flows) == 0 {
		return nil, errors.NewBadRequest("未找到可用的积分流水记录")
	}

	return flows, nil
}

// PointsExpire 处理积分过期
// curDate: 指定日期，为空时使用当前日期
func (s ClsPointsFlowService) PointsExpire(session *xorm.Session, curDate string) error {
	return s.tm.Required(session, func(tx *xorm.Session) error {
		// 1. 获取过期时间范围
		startTime, endTime, err := s.getExpireTimeRange(curDate)
		if err != nil {
			return err
		}

		// 2. 查询需要过期的积分流水
		flows, err := s.queryExpireFlows(tx, startTime, endTime)
		if err != nil {
			return err
		}

		// 3. 处理过期流水并生成新的扣减流水
		expireResult, err := s.processExpireFlows(flows, endTime)
		if err != nil {
			return err
		}

		// 4. 保存过期处理结果
		return s.saveExpireResult(tx, expireResult, curDate)
	})
}

// getExpireTimeRange 获取过期时间范围
func (s ClsPointsFlowService) getExpireTimeRange(curDate string) (startTime, endTime time.Time, err error) {
	endTime = time.Now()
	if len(curDate) > 0 {
		endTime, err = time.ParseInLocation("2006-01-02", curDate, time.Local)
		if err != nil {
			return time.Time{}, time.Time{}, errors.New("积分过期，日期格式错误：" + err.Error())
		}
	}
	startTime = endTime.AddDate(0, 0, -1)
	return
}

// queryExpireFlows 查询需要过期的积分流水
func (s ClsPointsFlowService) queryExpireFlows(session *xorm.Session, startTime, endTime time.Time) ([]po.ClsPointsFlow, error) {
	flows, err := s.repo.List(session, vo.ClsPointsFlowQueryVO{
		ExpireTimeStart: startTime.Format("2006-01-02"),
		ExpireTimeEnd:   endTime.Format("2006-01-02"),
		Status:          []int{1, 2},
	})
	if err != nil {
		return nil, errors.New("查询需要过期的积分流水失败：" + err.Error())
	}
	if len(flows) == 0 {
		return nil, errors.New("PointsExpire 没有找到需要过期的记录：curDate=" + endTime.Format("2006-01-02"))
	}
	return flows, nil
}

// ExpireResult 过期处理结果
type ExpireResult struct {
	ExpiredFlowIds []int                  // 需要标记为过期的流水ID
	NewFlows       []po.ClsPointsFlow     // 新生成的扣减流水
	TotalStats     po.ClsPointsDailyStats // 统计数据
}

// processExpireFlows 处理过期流水并生成新的扣减流水
func (s ClsPointsFlowService) processExpireFlows(flows []po.ClsPointsFlow, occurTime time.Time) (ExpireResult, error) {
	var result ExpireResult
	pointsMap := make(map[string]int)
	pointsBlkyMap := make(map[string]int)
	pointsSzldMap := make(map[string]int)
	staffNoMap := make(map[string]po.ClsPointsFlow)

	// 1. 统计各个员工的过期积分
	for _, flow := range flows {
		result.ExpiredFlowIds = append(result.ExpiredFlowIds, flow.Id)

		key := flow.StaffNo
		if flow.BizType == 6 {
			key += "_8" // 获赠积分到期
		} else {
			key += "_5" // 积分到期自动扣减
		}

		pointsMap[key] += flow.RemainingPoints
		remainingBlky := flow.RemainingPoints - flow.PointsSzld
		pointsBlkyMap[key] += remainingBlky
		pointsSzldMap[key] += flow.RemainingPoints - remainingBlky

		// 记录员工信息用于生成新流水
		if _, exists := staffNoMap[key]; !exists {
			staffNoMap[key] = flow
		}
	}

	// 2. 生成新的扣减流水
	result.NewFlows = s.generateExpireFlows(staffNoMap, pointsMap, pointsBlkyMap, pointsSzldMap, occurTime)

	// 3. 汇总统计数据
	result.TotalStats = s.sumExpireStats(result.NewFlows, occurTime)

	return result, nil
}

// generateExpireFlows 生成过期扣减流水
func (s ClsPointsFlowService) generateExpireFlows(staffNoMap map[string]po.ClsPointsFlow,
	pointsMap, pointsBlkyMap, pointsSzldMap map[string]int, occurTime time.Time) []po.ClsPointsFlow {
	var newFlows []po.ClsPointsFlow

	for key, baseFlow := range staffNoMap {
		parts := strings.Split(key, "_")
		staffNo := parts[0]
		bizTypeStr := parts[1]
		bizType, _ := strconv.Atoi(bizTypeStr)

		newFlows = append(newFlows, po.ClsPointsFlow{
			DisId:        baseFlow.DisId,
			EnterpriseId: baseFlow.EnterpriseId,
			Type:         2,       // 积分出账
			BizType:      bizType, // 积分到期自动扣减
			Points:       pointsMap[key],
			PointsBlky:   pointsBlkyMap[key],
			PointsSzld:   pointsSzldMap[key],
			Region:       baseFlow.Region,
			Operator:     "系统自动",
			StaffNo:      staffNo,
			OccurTime:    occurTime,
		})
	}

	return newFlows
}

// sumExpireStats 汇总过期统计数据
func (s ClsPointsFlowService) sumExpireStats(flows []po.ClsPointsFlow, statDate time.Time) po.ClsPointsDailyStats {
	var stats po.ClsPointsDailyStats
	stats.StatDate = statDate

	for _, flow := range flows {
		stats.ExpiredTotal += flow.Points
		stats.ExpiredBlky += flow.PointsBlky
		stats.ExpiredSzld += flow.PointsSzld
	}

	return stats
}

// saveExpireResult 保存过期处理结果
func (s ClsPointsFlowService) saveExpireResult(session *xorm.Session, result ExpireResult, curDate string) error {
	// 1. 将流水记录标记为过期
	if err := s.repo.ExpireFlow(session, result.ExpiredFlowIds); err != nil {
		return errors.New("过期积分流水失败：" + err.Error())
	}

	// 2. 批量新增积分到期自动扣减流水记录
	if _, err := s.repo.BatchCreate(session, result.NewFlows); err != nil {
		return errors.New("新增积分到期自动扣减流水记录失败：" + err.Error())
	}

	// 3. 更新积分账户
	if err := s.updateExpiredAccounts(session, result.NewFlows, curDate); err != nil {
		return err
	}

	// 4. 更新每日统计
	statsRepo := repo.NewClsPointsDailyStatsRepo()
	if err := statsRepo.SaveOrUpdate(session, result.TotalStats); err != nil {
		return err
	}

	return nil
}

// updateExpiredAccounts 更新过期积分账户
func (s ClsPointsFlowService) updateExpiredAccounts(session *xorm.Session, flows []po.ClsPointsFlow, curDate string) error {
	accountService := NewClsPointsAccountService()
	updAccount := make(map[int]int)
	for _, flow := range flows {
		if flow.DisId > 0 {
			updAccount[flow.DisId] = flow.EnterpriseId
		}
	}

	if err := accountService.BatchUpdateByDisIds(session, updAccount, curDate, 3); err != nil {
		return err
	}
	return nil
}

// Page 处理积分流水分页查询
func (s ClsPointsFlowService) Page(session *xorm.Session, queryVO vo.ClsPointsFlowQueryVO) ([]vo.ClsPointsFlowResultVO, int64, error) {
	var result []vo.ClsPointsFlowResultVO
	var total int64
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		accountService := NewClsPointsAccountService()
		if queryVO.DisId > 0 {
			var distributor vo.DistributorVO
			distributor, err = accountService.GetDistributor(tx, queryVO.DisId)
			if err != nil {
				return err
			}

			if queryVO.EnterpriseId == 0 {
				queryVO.EnterpriseId = distributor.EnterpriseId
			}
		}

		if len(queryVO.Mobile) > 0 {
			queryVO.Mobile = utils.MobileEncrypt(queryVO.Mobile)
		}

		result, total, err = s.repo.Page(tx, queryVO)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, 0, err
	}

	// 只有医生展示医院名称
	if len(result) > 0 {
		for i, flow := range result {
			if flow.DisRole != 3 {
				result[i].HospitalName = ""
			}
		}
	}

	return result, total, nil
}

func (s ClsPointsFlowService) GetStat(session *xorm.Session, queryVO vo.ClsPointsFlowQueryVO) (vo.ClsPointsFlowStatVO, error) {
	var flows []po.ClsPointsFlow
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		accountService := NewClsPointsAccountService()
		var distributor vo.DistributorVO
		if queryVO.DisId > 0 && queryVO.EnterpriseId == 0 {
			distributor, err = accountService.GetDistributor(tx, queryVO.DisId)
			if err != nil {
				return err
			}
			queryVO.EnterpriseId = distributor.EnterpriseId
		}
		flows, err = s.repo.List(tx, queryVO)
		return err
	})
	if err != nil {
		return vo.ClsPointsFlowStatVO{}, err
	}

	var result vo.ClsPointsFlowStatVO

	now := time.Now()
	loc := now.Location() // 获取本地时区
	// 设置为当天的 00:00:00（本地时间）
	startOfDay := time.Date(now.Year(), now.Month(), now.Day()-1, 23, 59, 59, 0, loc)
	if len(flows) > 0 {
		for _, flow := range flows {
			// 积分进账，但不包括积分赠送的收入
			if flow.Type == 1 && flow.BizType != 6 {
				result.TotalGet += flow.Points
				if flow.OccurTime.After(startOfDay) {
					result.TodayGet += flow.Points
				}
			}

			if flow.Status == 1 || flow.Status == 2 {
				// 部分使用
				result.Current += flow.RemainingPoints
			}

			if flow.BizType == 3 || flow.BizType == 4 {
				result.TotalUse += flow.Points
				if flow.OccurTime.After(startOfDay) {
					result.TodayUse += flow.Points
				}
			} else if flow.BizType == 5 || flow.BizType == 8 {
				result.TotalExpire += flow.Points
			}
		}
	}

	return result, nil
}

func (s ClsPointsFlowService) UpdatebyStaffNo(session *xorm.Session, disMobileMap map[int]string) error {
	log.Infof("UpdatebyStaffNo disMobileMap=%s", utils.JsonEncode(disMobileMap))

	dmTx := s.tm.DbDmMdm()
	return s.tm.Required(session, func(tx *xorm.Session) error {
		// 根据手机号查询shr,获取到staffNo
		updAccount := make(map[int]int)
		for disId, mobile := range disMobileMap {
			var staffNo string
			_, err := dmTx.Table("dm_mdm.shr_t_staff_info").Cols("shr_staff_no").Where("mobile=?", mobile).Limit(1).Get(&staffNo)
			if err != nil {
				log.Error("根据手机号查询员工编号失败：" + err.Error())
				continue
			}

			var operator vo.SalesOperatorVO
			_, err = tx.SQL(`SELECT dd.id AS dis_id, de.id AS enterprise_id, dd.real_name AS real_name
				FROM dis_distributor dd
				LEFT JOIN shop s ON s.id = dd.shop_id
				LEFT JOIN dis_enterprise de ON de.scrm_enterprise_id = s.enterprise_id
				WHERE dd.id = ?
			`, disId).Get(&operator)
			if err != nil {
				log.Error("查询企业ID失败: " + err.Error())
				continue
			}

			_, err = tx.Table("cls_points_flow").Where("staff_no=?", staffNo).Update(map[string]interface{}{
				"dis_id":        disId,
				"enterprise_id": operator.EnterpriseId,
				"operator":      operator.RealName,
			})
			if err != nil {
				log.Error("更新积分流水表失败: " + err.Error())
				continue
			}

			updAccount[disId] = operator.EnterpriseId
		}

		if len(updAccount) > 0 {
			pointsAccountSrv := NewClsPointsAccountService()
			if err := pointsAccountSrv.BatchUpdateByDisIds(tx, updAccount, "", 0); err != nil {
				return err
			}
		}

		return nil
	})
}

// ClsPointsFlowHooks 实现了针对 ClsPointsFlow 的特定钩子
type ClsPointsFlowHooks struct {
	ServiceHooks[int, po.ClsPointsFlow, vo.ClsPointsFlowSaveVO, vo.ClsPointsFlowUpdateVO, vo.ClsPointsFlowQueryVO, vo.ClsPointsFlowResultVO]
}
