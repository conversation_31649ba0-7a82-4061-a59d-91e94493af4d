package services

import (
	"bytes"
	logger "eShop/infra/log"
	"eShop/infra/pkg/app"
	"eShop/infra/pkg/code"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	dto2 "eShop/view-model/external-vo/dto"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/cast"
)

// 饿了么API请求通用方法
func HttpPostToElm(url string, bytesData []byte) ([]byte, error) {
	//跳过证书验证
	//tr := &http.Transport{
	//	TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	//	DialContext: (&net.Dialer{
	//		Timeout: 10 * time.Second,
	//	}).DialContext,
	//}

	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		logger.Error(err.Error())
		return nil, err
	}
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")

	//client := &http.Client{Transport: tr}
	resp, err := utils.HttpTransportClientTimeout.Do(request)
	if err != nil {
		logger.Error(err.Error())
		return nil, err
	}
	if resp.StatusCode != 200 {
		logger.Error(resp.Status)
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err.Error())
		return nil, err
	} else {
		logger.Info(fmt.Sprintf("饿了么接口:%s,返回:%s", url, string(respBytes)))
	}
	return respBytes, nil
}

// 饿了么API签名
func ElmSign(arr map[string]string, elmSecret string) string {
	arr["secret"] = elmSecret
	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	//进行排序
	sort.Strings(s)
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + arr[v]
	}
	return strings.ToUpper(utils.GetMd5String(str))
}

// 饿了么API请求参数组装
func BuildCmd(arr map[string]string) string {
	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		if str != "" {
			str += "&"
		}
		if v == "body" {
			str += v + "=" + url.QueryEscape(arr[v])
		} else {
			str += v + "=" + arr[v]
		}
	}
	return str
}

// 饿了么API请求参数ticket
func Md5ToUUID(str string) string {
	md5str := strings.ToUpper(utils.GetMd5String(str))
	return fmt.Sprintf("%s-%s-%s-%s-%s", md5str[0:8], md5str[8:12], md5str[12:16], md5str[16:20], md5str[20:32])
}

func ReturnFormt(cmd string, elmError dto2.ElmError) ([]byte, error) {

	ret := dto2.ResponseBoolData{}
	ret.Body.Errno = 10086
	ret.Body.Error = "未匹配到对应的cmd"
	mesTity := " 可在“商品管理-商品操作日志-饿了么拦截记录”操作删除记录恢复接口调用"
	switch cmd {
	case "sku.offline.one", "sku.stock.update.batch", "sku.price.update.one":
		ret.Body.Errno = cast.ToInt32(elmError.ErrorCode)

		ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + " sku_id:" + elmError.SkuId + mesTity
		if strings.Contains(elmError.ErrMes, "通过入参未查询到商户信息") {
			ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + mesTity
		}
		reByte, _ := json.Marshal(ret)
		return reByte, nil
	case "sku.create":
		ret := dto2.ResponseData{}
		ret.Body.Errno = cast.ToInt32(elmError.ErrorCode)
		ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + " sku_id:" + elmError.SkuId + mesTity
		if strings.Contains(elmError.ErrMes, "通过入参未查询到商户信息") {
			ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + mesTity
		}
		reByte, _ := json.Marshal(ret)
		return reByte, nil
	case "sku.list":
		ret := dto2.ResponseSkuListData{}
		ret.Body.Errno = cast.ToInt32(elmError.ErrorCode)
		ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + " sku_id:" + elmError.SkuId + mesTity
		if strings.Contains(elmError.ErrMes, "通过入参未查询到商户信息") {
			ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + mesTity
		}
		reByte, _ := json.Marshal(ret)
		return reByte, nil
	case "sku.online.one":
		ret := dto2.ResponseInterfaceData{}
		ret.Body.Errno = cast.ToInt32(elmError.ErrorCode)
		ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + " sku_id:" + elmError.SkuId + mesTity
		if strings.Contains(elmError.ErrMes, "通过入参未查询到商户信息") {
			ret.Body.Error = "接口自动过滤：" + elmError.ErrMes + " 店铺ID：" + elmError.ShopId + mesTity
		}
		reByte, _ := json.Marshal(ret)
		return reByte, nil
	}
	reByte, _ := json.Marshal(ret)
	return reByte, nil
}

// ElmAPI
// 统一封装接口
// appChannel 1.阿闻自有,2.TP代运营 不传 则默认为阿闻自有
func ElmAPI(cmd string, body interface{}, appChannel int32) ([]byte, error) {
	isFiter := false
	//判断要过滤的接口
	if cmd == "sku.offline.one" || cmd == "sku.price.update.one" || cmd == "sku.create" || cmd == "sku.list" ||
		cmd == "sku.online.one" || cmd == "sku.stock.update.batch" {
		isFiter = true
	}
	db := utils.NeWDatacenterDbConn()
	shopId := "-10086"
	skuId := "-10086"

	if cmd == "sku.create" || cmd == "sku.update" || cmd == "batch.sku.create" || cmd == "batch.sku.update" {
		bodys := body.(map[string]interface{})
		shopId = cast.ToString(bodys["shop_id"])
	}

	if cmd == "sku.update" {
		bodys := body.(map[string]interface{})
		skuId = cast.ToString(bodys["custom_sku_id"])
	}

	if isFiter {
		bodys := body.(map[string]interface{})
		shopId = cast.ToString(bodys["shop_id"])
		switch cmd {
		case "sku.offline.one":
			skuId = cast.ToString(bodys["custom_sku_id"])
		case "sku.price.update.one":
			ItemSkuId := strings.Split(cast.ToString(bodys["custom_sku_id"]), ":")
			if len(ItemSkuId) > 0 {
				skuId = ItemSkuId[0]
			}
		case "sku.create":
			skuId = cast.ToString(bodys["custom_sku_id"])
		case "sku.list":
			if len(cast.ToString(bodys["upc"])) > 0 {
				skuId = cast.ToString(bodys["upc"])
			}
			if len(cast.ToString(bodys["custom_sku_id"])) > 0 {
				skuId = cast.ToString(bodys["custom_sku_id"])
			}

		case "sku.online.one":
			skuId = cast.ToString(bodys["custom_sku_id"])
		}
		//先去查询对应的是否有报错，有的话直接返回对应错误出去

		elmError := dto2.ElmError{}
		//db.ShowSQL()
		session := db.NewSession()

		if shopId != "-10086" && skuId != "10086" { //如果是空的，说明没有解析出来数据
			session.Where(" (shop_id=? and error_code='59999') or (shop_id=? and sku_id=? )", shopId, shopId, skuId)

		} else if cmd == "sku.list" && shopId != "-10086" {
			session.Where("shop_id=? and error_code='59999'", shopId)
		}
		//如果是创建的话，不判断商品不存在的数据
		if cmd == "sku.create" {
			session.And("error_code!='500003' and err_mes!='商品不存在' ")
		}
		session.Get(&elmError)

		//说明有错误，直接返回错误不处理了
		if elmError.Id != 0 {
			// 是否只抓去某几种错误
			return ReturnFormt(cmd, elmError)
		}
	}

	appConfig, retCode := GetStoreMasterChannelAppConfig(appChannel, app.Channel_ELM)
	if retCode != code.Success {
		logger.Error("services.GetStoreMasterChannelAppConfig", appChannel, app.Channel_ELM, appConfig)
		return []byte(""), errors.New("GetStoreMasterChannelAppConfig failed")
	}

	source := appConfig.AppId
	secret := appConfig.AppSecret

	arr := make(map[string]string)
	jsonStr, _ := json.Marshal(body)
	arr["cmd"] = cmd
	arr["version"] = "3"
	timestamp := time.Now().Unix()
	arr["timestamp"] = strconv.FormatInt(timestamp, 10)

	newGUID := uuid.New()
	// 将 GUID 转换为字符串
	guidString := strings.ToUpper(newGUID.String())

	arr["ticket"] = guidString //Md5ToUUID(arr["timestamp"])

	arr["source"] = source

	arr["body"] = string(jsonStr)
	arr["encrypt"] = ""
	//签名
	sign := ElmSign(arr, secret)
	arr["sign"] = sign
	//参数组装
	param := BuildCmd(arr)

	logger.Info("饿了么接口,请求参数:", "cmd____", cmd, ",appChannel____", appChannel, ",请求参数____", param, ",请求参数arr____", arr)

	result, err := HttpPostToElm(utils.ElmUrl, []byte(param))
	if err != nil {
		logger.Error("饿了么接口,请求错误____", cmd, ",请求参数____", param, ",错误信息____", err)
		return result, err
	}
	var responseData dto2.ResponseBase
	var responseData1 dto2.ResponseBaseElm
	err = json.Unmarshal(result, &responseData1)
	if err != nil {
		logger.Error("饿了么接口,返回数据参数解码失败____", cmd, ",请求参数____", param, ",错误信息____", err)
		return result, err
	}
	responseData.Errno = cast.ToInt32(responseData1.Body.Errno)
	responseData.Error = responseData1.Body.Error
	if responseData.Errno != 0 {
		// jsonstr, _ = json.Marshal(responseData1)
		logger.Error("饿了么接口,请求错误____", cmd, ",请求参数____", param, ",返回信息____", string(result))

		//只过滤某写错误
		if isFiter && (responseData.Errno == 500003 || responseData.Errno == 59999 || responseData.Errno == 500001 || responseData.Errno == 100017 || strings.Contains(responseData.Error, "通过入参未查询到商户信息")) {
			//如果不等于0，需要保存错误信息
			elmError := dto2.ElmError{}
			elmError.ErrorCode = cast.ToString(responseData.Errno)
			elmError.ErrMes = responseData.Error
			elmError.ShopId = shopId
			elmError.SkuId = skuId
			elmError.ElmCmd = cmd
			if strings.Contains(elmError.ErrMes, "通过入参未查询到商户信息") {
				elmError.SkuId = ""
			}
			lockKey := "external:elm:error:" + shopId + ":" + skuId
			redisConn := cache.GetRedisConn()
			defer redisConn.Del(lockKey)
			//防止多条重复插入
			if redisConn.SetNX(lockKey, time.Now().Unix(), 5*time.Second).Val() {
				_, err1 := db.Insert(elmError)
				if err1 != nil {
					logger.Error("饿了么接口,返回报错存储失败", err)
				}
			}
		}

		var ElmBatchReturn dto2.ElmBatchReturn
		if cmd == "batch.sku.create" || cmd == "batch.sku.update" {
			err = json.Unmarshal(result, &ElmBatchReturn)
			if len(ElmBatchReturn.Body.Data.SuccessList) > 0 {
				var spuIds []string
				for _, v := range ElmBatchReturn.Body.Data.SuccessList {
					spuIds = append(spuIds, v.CustomSkuID)
				}
				elmError := dto2.ElmError{}
				_, err := db.Where("shop_id =?", shopId).In("sku_id", spuIds).Delete(&elmError)
				if err != nil {
					logger.Error("饿了么接口,自动过滤数据删除失败", err)
				}
			}
		}

	} else {
		//如果是编辑或者创建商品成功了。那么就需要删除过滤表里面的数据
		if cmd == "sku.create" || cmd == "sku.update" {

			elmError := dto2.ElmError{}
			_, err := db.Where("shop_id =? and sku_id=?", shopId, skuId).Delete(&elmError)
			if err != nil {
				logger.Error("饿了么接口,自动过滤数据删除失败", err)
			}
		}

		if cmd == "batch.sku.create" || cmd == "batch.sku.update" {
			var ElmBatchReturn dto2.ElmBatchReturn
			err = json.Unmarshal(result, &ElmBatchReturn)
			if len(ElmBatchReturn.Body.Data.SuccessList) > 0 {
				var spuIds []string
				for _, v := range ElmBatchReturn.Body.Data.SuccessList {
					spuIds = append(spuIds, v.CustomSkuID)
				}
				elmError := dto2.ElmError{}
				_, err := db.Where("shop_id =?", shopId).In("sku_id", spuIds).Delete(&elmError)
				if err != nil {
					logger.Error("饿了么接口,自动过滤数据删除失败", err)
				}

			}

		}

	}

	logger.Error("饿了么接口,请求返回参数日志____", cmd, ",请求参数____", param, ",返回信息____", string(result))
	return result, err
}

func StringToInt32(s string) int32 {
	str, _ := strconv.ParseInt(s, 10, 32)
	return int32(str)
}
