package controllers

import (
	"bytes"
	"context"
	petai_po "eShop/domain/petai-po"
	"eShop/infra/cache"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/services/petai-service/services"
	petai_vo "eShop/view-model/petai-vo"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/coze-dev/coze-go"
	"github.com/go-chi/chi/v5"
	"github.com/spf13/cast"
)

func NewCozeController(service services.CozeService) *CozeController {
	return &CozeController{
		service: service,
	}
}

// ShopOrderController 门店订单控制器
type CozeController struct {
	service services.CozeService
}

func (w *CozeController) RegisterRoutes(r chi.Router) {

	//小程序
	r.Route("/petai-app/api/conversation", func(r chi.Router) {
		r.Post("/chat", w.Chat)                              // 发起对话
		r.Post("/chat/cancel", w.CancelChat)                 // 中止对话
		r.Post("/create", w.ConversationCreate)              // 创建会话
		r.Post("/detail", w.ConversationDetail)              // 获取会话详情
		r.Post("/list", w.ConversationList)                  // 获取会话列表
		r.Post("/message/list", w.ConversationMessageList)   // 获取会话消息列表
		r.Post("/message/evaluate", w.EvaluateMessage)       // 评价消息
		r.Post("/file/upload", w.UploadFile)                 // 上传文件
		r.Post("/file/retrieve", w.RetrieveFile)             // 获取文件信息
		r.Post("/transfer-petmedical", w.TransferPetMedical) // 转接互联网医生人工服务

	})

	// 互联网医院管理端和医生端调用的接口
	r.Route("/petai-app/api/pet-medical", func(r chi.Router) {
		// 互联网医院管理端 - 以下接口是供互联网医院管理端使用的，前端带上互联网医院医生端的token,即可调用
		r.With(jwtauth.PetMedicalVerify()).Post("/conversation/list", w.ConversationList)             // 互联网医院管理端-获取会话列表
		r.With(jwtauth.PetMedicalVerify()).Post("/conversation/detail", w.ConversationDetailForAdmin) // 互联网医院管理端-获取会话消息列表

		// 互联网医院医生端 - 以下接口是供互联网医院医生端使用的，前端带上互联网医院医生端的token,即可调用
		r.With(jwtauth.PetMedicalVerify()).Post("/conversation/message/list", w.ConversationMessageList) // 互联网医院医生端-获取会话消息列表
		r.With(jwtauth.PetMedicalVerify()).Post("/conversation/evaluate", w.EvaluateConversation)        // 互联网医院医生端-医生评价AI会话

		// 互联网医院医生端  医生针对指定的互联网医院问诊单 来发起一个问诊会话， 以此让互联网医生来验证模型回答的准确性
		r.With(jwtauth.PetMedicalVerify()).Post("/consult/chat", w.ConsultChat)                                         // 互联网医院医生端 针对于一个问诊发起会话以及发起对话
		r.With(jwtauth.PetMedicalVerify()).Post("/consult/chat/reference-content", w.ConsultChatReferenceContent)       // 互联网医院医生端 医生引用模型回答
		r.With(jwtauth.PetMedicalVerify()).Post("/consult/conversation/message/list", w.ConsultConversationMessageList) // 互联网医院医生端-获取会话消息列表

	})

}

// ConversationCreate 创建会话
// @Summary 创建会话 @petai-v1.0.0 @petai-v1.1.0
// @Description 创建会话
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConversationCreateReq true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/conversation/create [post]
func (c *CozeController) ConversationCreate(w http.ResponseWriter, r *http.Request) {

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====创建coze会话====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====创建coze会话====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	userInfoId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.ConversationCreateReq](r)
	if err != nil {
		log.Error("====创建coze会话====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = userInfoId
	// 如果前端没有传botType，则需要调前置接口， 判断是养宠助手火山还是小闻模型

	resp, err := c.service.ConversationCreate(cmd)
	if err != nil {
		log.Error("====创建coze会话====创建会话失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithData(w, resp)
}

// ConversationDetail 获取会话详情
// @Summary 获取会话详情 @petai-v1.0.0
// @Description 获取会话详情
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConversationDetailReq true "请求参数"
// @Success 200 {object} petai_vo.ConversationDetailRes "成功"
// @Failure 400 {object} petai_vo.ConversationDetailRes "请求错误"
// @Router /petai-app/api/conversation/detail [post]
func (c *CozeController) ConversationDetail(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====获取会话详情====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====获取会话详情====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.ConversationDetailReq](r)
	if err != nil {
		log.Error("====获取会话详情====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = eshopUserId
	resp, err := c.service.ConversationDetail(cmd)
	if err != nil {
		log.Error("====获取会话详情====获取会话详情失败 err=", err.Error())
		response.BadRequest(w, "获取会话详情失败")
		return
	}
	response.SuccessWithData(w, resp)
}

// ConversationDetailForAdmin 管理后台获取会话详情
// @Summary 管理后台获取会话详情 @petai-v1.1.0
// @Description 管理后台获取会话详情
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConversationDetailForAdminReq true "请求参数"
// @Success 200 {object} petai_vo.ConversationDetailForAdminRes "成功"
// @Failure 400 {object} petai_vo.ConversationDetailForAdminRes "请求错误"
// @Router /petai-app/api/pet-medical/conversation/detail [post]
func (c *CozeController) ConversationDetailForAdmin(w http.ResponseWriter, r *http.Request) {
	resp := petai_vo.ConversationDetailForAdmin{}
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====管理后台获取会话详情====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if jwtInfo.AdminLoginInfo.Id <= 0 {
		log.Error("====管理后台获取会话详情====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}

	cmd, err := utils.Bind[petai_vo.ConversationDetailForAdminReq](r)
	if err != nil {
		log.Error("====管理后台获取会话详情====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	if cmd.ConversationId <= 0 {
		log.Error("====管理后台获取会话详情====会话id不能为空 err=", err.Error())
		response.BadRequest(w, "会话id不能为空")
		return
	}
	resp, total, err := c.service.ConversationDetailForAdmin(cmd)
	if err != nil {
		log.Error("====管理后台获取会话详情====获取会话详情失败 err=", err.Error())
		response.BadRequest(w, "获取会话详情失败")
		return
	}

	response.SuccessWithPage(w, resp, total)
}

// ConversationList 获取会话列表
// @Summary 获取会话列表 @petai-v1.0.0
// @Description 获取会话列表
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConversationListReq true "请求参数"
// @Success 200 {object} petai_vo.ConversationListRes "成功"
// @Failure 400 {object} petai_vo.ConversationListRes "请求错误"
// @Router /petai-app/api/conversation/list [post]
func (c *CozeController) ConversationList(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====获取会话列表====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}

	if jwtInfo.Id == "" && jwtInfo.AdminLoginInfo.Id <= 0 {
		log.Error("====获取会话列表====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id
	cmd, err := utils.Bind[petai_vo.ConversationListReq](r)
	if err != nil {
		log.Error("====获取会话列表====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = eshopUserId
	data, total, err := c.service.ConversationList(cmd)
	if err != nil {
		log.Error("====获取会话列表====获取会话列表失败 err=", err.Error())
		response.BadRequest(w, "获取会话列表失败")
		return
	}
	response.SuccessWithPage(w, data, total)
}

// ConversationMessageList 获取会话消息列表
// @Summary 获取会话消息列表 @petai-v1.1.0
// @Description 获取会话消息列表
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConversationMessageListReq true "请求参数"
// @Success 200 {object} petai_vo.ConversationMessageListExtend "成功"
// @Failure 400 {object} petai_vo.ConversationMessageListExtend "请求错误"
// @Router /petai-app/api/conversation/message/list [post]
func (c *CozeController) ConversationMessageList(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====获取会话列表====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 && len(jwtInfo.UserId) == 0 {
		log.Error("====获取会话列表====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.ConversationMessageListReq](r)
	if err != nil {
		log.Error("====获取会话消息列表====获取会话消息请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = eshopUserId
	data, total, err := c.service.ConversationMessageList(cmd)
	if err != nil {
		log.Error("====获取会话消息列表====获取会话消息列表失败 err=", err.Error())
		response.BadRequest(w, "获取会话消息列表失败")
		return
	}

	dataAcquisitionShow, _ := c.service.ConversationDataAcquisition(cmd.ConversationId)
	res := petai_vo.ConversationMessageListRes{
		Code:                200,
		Message:             "success",
		Data:                data,
		Total:               total,
		DataAcquisitionShow: dataAcquisitionShow,
	}

	// questionUuidSli := make([]string, 0)
	// for _, v := range data {
	// 	if v.Role == petai_po.MessageRoleUser {
	// 		questionUuidSli = append(questionUuidSli, v.Uuid)
	// 	}
	// }
	// DataAcquisitionMap, err := new(services.DataAcquisitionService).DataAcquisitionListByMessageUuid(petai_vo.DataAcquisitionListByMessageUuidReq{
	// 	QuestionUuid: questionUuidSli,
	// })
	// if err != nil {
	// 	log.Error("====获取会话消息列表====获取数据采集列表失败 err=", err.Error())
	// 	response.BadRequest(w, "获取会话消息列表失败")
	// 	return
	// }

	// res := make([]*petai_vo.ConversationMessageListExtend, 0)

	// for _, v := range data {

	// 	if DataAcquisition, ok := DataAcquisitionMap[v.Uuid]; ok {
	// 		tmp := &petai_vo.ConversationMessageListExtend{
	// 			PetaiMessage:    v,
	// 			DataAcquisition: DataAcquisition,
	// 		}
	// 		res = append(res, tmp)
	// 	} else {
	// 		tmp := &petai_vo.ConversationMessageListExtend{
	// 			PetaiMessage: v,
	// 		}
	// 		res = append(res, tmp)
	// 	}

	// }
	bytes, _ := json.Marshal(res)
	w.Write(bytes)

}

// ConsultConversationMessageList 获取会话消息列表
// @Summary 获取会话消息列表 @petai-v2.1.0
// @Description 获取会话消息列表
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConsultConversationMessageListReq true "请求参数"
// @Success 200 {object} petai_vo.ConsultConversationMessageListRes "成功"
// @Failure 400 {object} petai_vo.ConsultConversationMessageListRes "请求错误"
// @Router /petai-app/api/pet-medical/consult/conversation/message/list [post]
func (c *CozeController) ConsultConversationMessageList(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====获取会话列表====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 && len(jwtInfo.UserId) == 0 {
		log.Error("====获取会话列表====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}

	cmd, err := utils.Bind[petai_vo.ConsultConversationMessageListReq](r)
	if err != nil {
		log.Error("====获取会话消息列表====获取会话消息请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	res := petai_vo.ConsultConversationMessageListRes{}
	res.Data, res.Total, err = c.service.ConsultConversationMessageList(cmd)
	if err != nil {
		log.Error("====获取会话消息列表====获取会话消息列表失败 err=", err.Error())
		response.BadRequest(w, "获取会话消息列表失败")
		return
	}

	bytes, _ := json.Marshal(res)
	w.Write(bytes)

}

// EvaluateMessage 评价消息
// @Summary 评价消息 @petai-v1.1.0
// @Description 评价消息
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.EvaluateMessageReq true "请求参数"
// @Success 200 {object} response.BaseResp  "成功"
// @Failure 400 {object} response.BaseResp  "请求错误"
// @Router /petai-app/api/conversation/message/evaluate [post]
func (c *CozeController) EvaluateMessage(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====评价消息====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====评价消息====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.EvaluateMessageReq](r)
	if err != nil {
		log.Error("====评价消息====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	if len(cmd.MessageId) <= 0 {
		log.Error("====评价消息====消息id不能为空")
		response.BadRequest(w, "消息id不能为空")
		return
	}
	if cmd.Evaluate != petai_po.MessageEvaluateInit && cmd.Evaluate != petai_po.MessageEvaluateLike && cmd.Evaluate != petai_po.MessageEvaluateHate {
		log.Error("====评价消息====评价参数错误")
		response.BadRequest(w, "评价参数错误")
		return
	}
	cmd.UserInfoId = eshopUserId
	err = c.service.EvaluateMessage(cmd)
	if err != nil {
		log.Error("====评价消息===失败 err=", err.Error())
		response.BadRequest(w, "评价消息失败")
		return
	}
	response.Success(w)
}

// EvaluateConversation 互联网医生评价AI会话
// @Summary 互联网医生评价AI会话 @petai-v1.1.0
// @Description 互联网医生评价AI会话
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.EvaluateConversationReq true "请求参数"
// @Success 200 {object} response.BaseResp  "成功"
// @Failure 400 {object} response.BaseResp  "请求错误"
// @Router /petai-app/api/pet-medical/conversation/evaluate [post]
func (c *CozeController) EvaluateConversation(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====互联网医生评价AI会话====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.UserId) == 0 {
		log.Error("====互联网医生评价AI会话====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	doctorCode := jwtInfo.UserId

	cmd, err := utils.Bind[petai_vo.EvaluateConversationReq](r)
	if err != nil {
		log.Error("====互联网医生评价AI会话====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.FeedbackDoctorCode = doctorCode
	cmd.FeedbackDoctorName = jwtInfo.UserName
	if cmd.PmOrderSn <= 0 {
		log.Error("====互联网医生评价AI会话====问诊订单号不能为空")
		response.BadRequest(w, "问诊订单号不能为空")
		return
	}

	if len(cmd.Feedback) == 0 && cmd.FeedbackType == 0 {
		response.BadRequest(w, "反馈内容和补充内容不能都为空")
		return
	}
	cmd.Evaluate = petai_po.MessageEvaluateHate

	err = c.service.EvaluateConversation(cmd)
	if err != nil {
		log.Error("====互联网医生评价AI会话===失败 err=", err.Error())
		response.BadRequest(w, "评价消息失败")
		return
	}
	response.Success(w)
}

// Chat 发起对话
// @Summary 发起对话 @petai-v1.0.0
// @Description 发起对话
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ChatReq true "请求对话参数"
// @Success 200 {object} petai_vo.ChatRes "成功"
// @Failure 400 {object} petai_vo.ChatRes "请求错误"
// @Router /petai-app/api/conversation/chat [post]
func (c *CozeController) Chat(w http.ResponseWriter, r *http.Request) {
	// 1. 设置 SSE 响应头（关键！）
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")

	if utils.IsProEnv() {
		w.Header().Set("Access-Control-Allow-Origin", "www.xiaow.com") // 生产环境需严格配置
	} else {
		w.Header().Set("Access-Control-Allow-Origin", "*") // 生产环境需严格配置
	}
	// 2. 获取 Flusher 对象
	flusher, ok := w.(http.Flusher)
	if !ok {
		log.Error("====发起对话时获取Flusher对象失败====Streaming not supported")
		http.Error(w, "Streaming not supported", http.StatusBadRequest)
		return
	}

	// 3. 处理请求参数
	cmd, err := utils.Bind[petai_vo.ChatReq](r)
	if err != nil {
		// 返回 SSE 格式的错误信息
		log.Error("====发起对话时获取请求参数失败 err=", err.Error())
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", "获取请求参数失败")
		flusher.Flush()
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====发起对话时获取登录信息失败 err=", err.Error())
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", "获取登录信息失败")
		flusher.Flush()
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====发起对话时获取登录信息失败,用户id为0")
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", "用户未登录")
		flusher.Flush()
		return
	}

	cmd.UserInfoId = jwtInfo.Id
	// 根据会话id获取会话详情
	conversation, err := c.service.ConversationDetail(petai_vo.ConversationDetailReq{
		ConversationId: cmd.ConversationId,
		IsCozeData:     false,
		UserInfoId:     cmd.UserInfoId,
	})
	if err != nil {
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", "会话不存在")
		flusher.Flush()
		return
	}
	if conversation == nil || conversation.Id == 0 {
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", "会话不存在")
		flusher.Flush()
		return
	}
	cmd.PetInfoId = conversation.PetInfoId
	logPrefix := fmt.Sprintf("====用户发起对话,UserInfoId:%s,ConversationId:%d,会话详情:%s====", cmd.UserInfoId, cmd.ConversationId, utils.JsonEncode(conversation))
	log.Info(logPrefix, "请求参数:", utils.JsonEncode(cmd))

	// 根据会话id获取会话对应的coze会话详情
	conversationMapCoze, err := c.service.ConversationMapCoze(cmd.ConversationId)
	if err != nil {
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", "获取数据失败")
		flusher.Flush()
		return
	}
	MedicalPetInfo := &petai_vo.MedicalPetInfo{}
	if len(conversation.PetInfoId) > 0 {
		// 判断会话是否有宠物信息
		if MedicalPetInfo, err = new(services.PetService).PetInfoForMedical(conversation.PetInfoId); err != nil {
			log.Errorf("%s 获取宠物信息失败,err为%s", logPrefix, err.Error())
			fmt.Fprintf(w, "event: error\ndata: %s\n\n", "获取宠物信息失败")
			flusher.Flush()
			return
		}

	}
	cmd.PetDesc = MedicalPetInfo.FullText

	// 前端没有传智能体类型时， 通过自研前置模型判断，是调用小闻模型还是养宠助手火山智能体，且调用这两个需要带上上下文。 如果前端传来智能体类型（如疾病自诊，宠物识别，健康建议）， 则调用相应的coze智能体， 且不需要带上上下文。
	messages, err := c.service.OrgChatMessages(cmd)
	if err != nil {
		log.Error(logPrefix, "获取历史对话失败 err=", err.Error())
		if strings.Contains(err.Error(), "内容含违规信息，请修改后重新提交") {
			fmt.Fprintf(w, "event: error\ndata: %s\n\n", err.Error())
		} else {
			fmt.Fprintf(w, "event: error\ndata: %s\n\n", err.Error())
		}
		flusher.Flush()
		return
	}

	if cmd.BotType == 0 {
		// 调用前置模型
		medicalUrl := c.service.MedicalUrl()
		textStr := ""
		if cmd.Message.ContentType == string(petai_po.MessageContentTypeText) {
			textStr = cmd.Message.TextContent
		} else if cmd.Message.ContentType == string(petai_po.MessageContentTypeObjectString) {
			for _, v := range cmd.Message.ObjectContent {
				if v.Type == petai_po.MessageObjectStringTypeText {
					textStr += v.Text + ","
				}
			}

		}
		textStr = strings.TrimRight(textStr, ",")

		medicalReq := petai_vo.MedicalStreamPostReq{
			Text:           textStr,
			ConversationId: conversation.Id,
			UserInfoId:     cmd.UserInfoId,
			PetInfoId:      conversation.PetInfoId,
			PetDetail:      *MedicalPetInfo,
			Messages:       messages,
		}

		log.Infof("%s 调用前置模型开始====请求地址为%s,入参为%s", logPrefix, medicalUrl, utils.JsonEncode(medicalReq))

		singleRes, uuid := c.service.StreamPost(w, flusher, medicalUrl, medicalReq, "", nil)

		cmd.BotType = petai_po.BotTypePetCareAssistant
		if singleRes.Intent != petai_po.IntentBaiKeDesc {
			cmd.BotType = petai_po.BotTypeXiaoWenModel
			conversation.BotType = cmd.BotType
			conversation.BotId = c.service.GetBotId(cmd.BotType)
			// 更新会话的智能体类型和id
			err = c.service.EditConversation(conversation)
			log.Errorf("%s 编辑会话信息失败 err=%v", logPrefix, err)

			return
		}
		cmd.Uuid = uuid

	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// Init the Coze client through the access_token.
	cozeCli, _ := c.service.NewCozeAPI(cmd.UserInfoId, cmd.BotType)

	conversationCoze, ok := conversationMapCoze[cmd.BotType]
	if !ok { //创建会话
		// Create a new conversation
		resp, err := cozeCli.Conversations.Create(ctx, &coze.CreateConversationsReq{BotID: c.service.GetBotId(cmd.BotType)})
		log.Infof("%s resp:%s|err:%v", logPrefix, utils.JsonEncode(resp), err)
		if err != nil {
			log.Error(logPrefix, "Error creating conversation:", err.Error())
			fmt.Fprintf(w, "event: error\ndata: %s\n\n", "创建会话失败")
			flusher.Flush()
			return
		}
		if resp == nil || len(resp.Conversation.ID) == 0 {
			log.Error(logPrefix, "Error creating conversation: resp.Conversation is nil")
			fmt.Fprintf(w, "event: error\ndata: %s\n\n", "创建会话失败")
			flusher.Flush()
			return
		}
		conversationCoze = petai_po.PetaiConversationCoze{
			ConversationId:           cmd.ConversationId,
			BotId:                    c.service.GetBotId(cmd.BotType),
			BotType:                  cmd.BotType,
			CozeConversationId:       resp.Conversation.ID,
			CozeConversationCreateAt: resp.Conversation.CreatedAt,
		}
		err = c.service.CreateConversationCoze(&conversationCoze)
		if err != nil {
			log.Error(logPrefix, "Error creating conversation coze: ", err.Error())
		}
	}
	cmd.CozeConversationId = conversationCoze.CozeConversationId
	cmd.CozeConversationCreateAt = conversationCoze.CozeConversationCreateAt

	message2 := make([]*coze.Message, 0)
	// 宠物识别,和宠物自诊 无需发送宠物信息
	if len(cmd.PetDesc) > 0 && cmd.BotType != petai_po.BotTypePetRecog && cmd.BotType != petai_po.BotTypePetDiagnose {
		// &coze.Message{
		// 	Role:    coze.MessageRoleUser,
		// 	Type:    coze.MessageTypeQuestion,
		// 	Content: cmd.PetDesc,
		// }

		message2 = append(message2, coze.BuildUserQuestionText(cmd.PetDesc, nil))
		message2 = append(message2, coze.BuildAssistantAnswer(cmd.PetDesc, nil))
	}

	message2 = append(message2, messages...)

	req := &coze.CreateChatsReq{
		ConversationID: conversationCoze.CozeConversationId,
		BotID:          c.service.GetBotId(cmd.BotType),
		UserID:         cmd.UserInfoId,
		Messages:       message2,
	}
	log.Info(logPrefix, "组装发送给coze的消息为", utils.JsonEncode(message2))

	resp, err := cozeCli.Chat.Stream(ctx, req)
	if err != nil {
		fmt.Printf("%s ,Error starting chats: %s\n", logPrefix, err.Error())
		log.Error(logPrefix, fmt.Sprintf("Error starting chats: %s\n", err.Error()))
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", "发起对话失败")
		flusher.Flush()
		return
	}

	defer resp.Close()
	var reasoningContent bytes.Buffer
	for {
		event, err := resp.Recv()
		if errors.Is(err, io.EOF) {
			log.Info(logPrefix, "Stream finished")
			fmt.Println(logPrefix, "Stream finished")
			fmt.Fprint(w, "event: end\ndata: stream_completed\n\n")
			flusher.Flush()
			break
		}
		if err != nil {
			log.Error(logPrefix, "接收数据发生错误:", err.Error())
			fmt.Println(logPrefix, "接收数据发生错误:", err.Error())
			fmt.Fprintf(w, "event: error\ndata: %s\n\n", "接收数据发生错误")
			flusher.Flush()
			break
		}
		switch event.Event {
		case coze.ChatEventConversationChatCreated, coze.ChatEventConversationChatInProgress:
			//time.Sleep(10 * time.Second)
			fmt.Printf("event:%s\n", event.Event)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event.Chat)) // SSE 格式
			fmt.Println("aaaaaaaaaa", event.Chat.ID)
			flusher.Flush()

		case coze.ChatEventConversationMessageDelta:
			// 发送增量内容（核心逻辑）
			fmt.Print(event.Message.Content)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event.Message)) // SSE 格式
			if event.Message.Type == petai_po.MessageTypeAnswer {
				if len(event.Message.ReasoningContent) > 0 {
					reasoningContent.WriteString(event.Message.ReasoningContent)
				}
			}
			flusher.Flush()
		case coze.ChatEventConversationMessageCompleted:
			log.Info(logPrefix, "event:", event.Event, ",,message:", utils.JsonEncode(event.Message))
			fmt.Printf("event:%s\n", event.Event)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event.Message)) // SSE 格式
			flusher.Flush()
			if event.Message.Type == petai_po.MessageTypeAnswer {
				//检查该对话有没有被取消， 如果有被取消， 不用写入数据库表里
				chat, err := cozeCli.Chat.Retrieve(ctx, &coze.RetrieveChatsReq{
					ConversationID: event.Message.ConversationID,
					ChatID:         event.Message.ChatID,
				})
				if err != nil {
					log.Error(logPrefix, "Error retrieving chat status:", err.Error())
				}
				if chat.Chat.Status == coze.ChatStatusCancelled {
					log.Error(logPrefix, "Chat is already canceled, current status:", chat.Chat.Status)

				} else {
					cmd.ReasoningContent = reasoningContent.String()
					// 保存消息
					if cmd.Uuid == "" {
						cmd.Uuid = utils.GenerateUUID()
					}
					messageId, err := c.service.SaveMessage(cmd, event.Message)
					if err != nil {
						fmt.Fprintf(w, "event: %s\ndata: %s\n\n", services.EventTypeChatFailed, err.Error())
						flusher.Flush()
						return
					}

					conversation.BotType = cmd.BotType
					conversation.BotId = c.service.GetBotId(cmd.BotType)
					// 更新会话的智能体类型和id
					err = c.service.EditConversation(conversation)
					log.Infof("%s 编辑会话信息失败 err==%v", logPrefix, err)

					// tmp := struct {
					// 	Data string `json:"data"`
					// }{Data: cast.ToString(messageId)}
					fmt.Fprintf(w, "event: messageid\ndata: %d\n\n", messageId)
					flusher.Flush()
				}
			}

		case coze.ChatEventConversationChatCompleted:
			log.Info(logPrefix, "event:", event.Event)
			fmt.Printf("event:%s\n", event.Event)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event.Chat)) // SSE 格式
			flusher.Flush()
		case coze.ChatEventConversationChatFailed:
			log.Info(logPrefix, "event:", event.Event)
			fmt.Printf("event:%s\n", event.Event)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event)) // SSE 格式
			flusher.Flush()
		case coze.ChatEventConversationChatRequiresAction:
			log.Info(logPrefix, "event:", event.Event)
			fmt.Printf("event:%s\n", event.Event)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event)) // SSE 格式
			flusher.Flush()
		case coze.ChatEventConversationAudioDelta:
			log.Info(logPrefix, "event:", event.Event)
			fmt.Printf("event:%s\n", event.Event)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event)) // SSE 格式
			flusher.Flush()
		case coze.ChatEventError:
			log.Info(logPrefix, "event:", event.Event)
			fmt.Printf("event:%s\n", event.Event)
			fmt.Fprintf(w, "event: %s\ndata: %s\n\n", event.Event, utils.JsonEncode(event)) // SSE 格式
			flusher.Flush()
		case coze.ChatEventDone:
			log.Info(logPrefix, "event:", event.Event)
			fmt.Printf("event:%s\n", event.Event)
			// todo 获取该会话的采集数据列表
			dataAcquisitionShow, _ := c.service.ConversationDataAcquisition(cmd.ConversationId)
			// utils.JsonEncode(event)
			fmt.Fprintf(w, "event: %s\ndata: %v\n\n", event.Event, dataAcquisitionShow) // SSE 格式
			flusher.Flush()
		default:
			// 发送心跳维持连接
			fmt.Printf("event: heartbeat\n")
			fmt.Fprint(w, "event: heartbeat\ndata: 心跳\n\n")
			flusher.Flush()
		}

	}
	log.Info(logPrefix, "chat done,log:%s\n", resp.Response().LogID())
	fmt.Println(logPrefix, "chat done,log:", resp.Response().LogID())

}

// ConsultChat 互联网医院医生端智能问答测试版
// @Summary 互联网医院医生端智能问答测试版 @petai-v2.1.0
// @Description 互联网医院医生端智能问答测试版
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConsultChatReq true "请求对话参数"
// @Success 200 {object} petai_vo.ConsultChatRes "成功"
// @Failure 400 {object} petai_vo.ConsultChatRes "请求错误"
// @Router /petai-app/api/pet-medical/consult/chat [post]
func (c *CozeController) ConsultChat(w http.ResponseWriter, r *http.Request) {
	logPrefix := "====互联网医院医生端智能问答测试版ConsultChat===="
	// // 1. 设置 SSE 响应头（关键！）
	// w.Header().Set("Content-Type", "text/event-stream")
	// w.Header().Set("Cache-Control", "no-cache")
	// w.Header().Set("Connection", "keep-alive")
	// w.Header().Set("Access-Control-Allow-Origin", "*")

	// // 2. 获取 Flusher 对象
	// flusher, ok := w.(http.Flusher)
	// if !ok {
	// 	log.Error(logPrefix, "====发起对话时获取Flusher对象失败====Streaming not supported")
	// 	http.Error(w, "Streaming not supported", http.StatusBadRequest)
	// 	return
	// }

	// 3. 处理请求参数
	cmd, err := utils.Bind[petai_vo.ConsultChatReq](r)
	if err != nil {
		// 返回 SSE 格式的错误信息
		log.Error(logPrefix, "====发起对话时获取请求参数失败 err=", err.Error())
		// fmt.Fprintf(w, "event: error\ndata: %s\n\n", "获取请求参数失败")
		// flusher.Flush()
		response.BadRequest(w, "获取请求参数失败"+err.Error())
		return
	}
	defer utils.RunningDuration("互联网医院问诊单发起对话" + cast.ToString(cmd.PmOrderSn) + "ConsultChat")()
	logPrefix = fmt.Sprintf("====互联网医院医生端智能问答测试版ConsultChat订单号为%s====", cmd.PmOrderSn)
	log.Info(logPrefix, "请求参数:", utils.JsonEncode(cmd))

	// 同一个订单 30秒内只能发起一次对话
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	cacheK := fmt.Sprintf("consult_chat_order_sn_%s", cmd.PmOrderSn)
	setNxReslt := redisConn.TryLock(string(cache_source.EShop), cacheK, time.Second*10)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败,请求参数为%s", logPrefix, cacheK, utils.JsonEncode(cmd))

		response.BadRequest(w, "正在对话中，请稍后再试"+err.Error())
		return
	}
	defer redisConn.Delete(string(cache_source.EShop), cacheK)

	if cmd.PmOrderSn == "" {
		log.Error(logPrefix, "订单号不能为空")
		// fmt.Fprintf(w, "event: error\ndata: %s\n\n", "订单号不能为空")
		// flusher.Flush()
		response.BadRequest(w, "订单号不能为空")
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error(logPrefix, "====发起对话时获取登录信息失败 err=", err.Error())
		// fmt.Fprintf(w, "event: error\ndata: %s\n\n", "获取登录信息失败")
		// flusher.Flush()
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	// 既不是互联网医院医生端，也不是互联网医院用户端
	if len(jwtInfo.UserId) == 0 && len(jwtInfo.Scrmid) == 0 {
		log.Error(logPrefix, "用户未登录")
		// fmt.Fprintf(w, "event: error\ndata: %s\n\n", "用户未登录")
		// flusher.Flush()
		response.BadRequest(w, "用户未登录")
		return
	}
	consultConversation, petInfoDetail, isNew, err := c.service.ConsultConversationCreate(petai_vo.ConsultConversationCreateReq{
		PmOrderSn: cast.ToInt64(cmd.PmOrderSn),
	})
	if err != nil {
		log.Error(logPrefix, "创建会话失败 err=", err.Error())
		// fmt.Fprintf(w, "event: error\ndata: %s\n\n", err.Error())
		// flusher.Flush()
		response.BadRequest(w, err.Error())
		return
	}
	medicalUrl := c.service.MedicalUrl()
	textStr := consultConversation.Title
	messages2 := make([]*coze.Message, 0)
	if !isNew {
		messages, lastQuestion, err := c.service.OrgConsultChatMessages(&cmd)
		if err != nil {
			log.Error(logPrefix, "获取历史对话失败 err=", err.Error())
			// fmt.Fprintf(w, "event: error\ndata: %s\n\n", "获取历史对话失败")
			// flusher.Flush()
			response.BadRequest(w, "获取历史对话失败")
			return
		}
		textStr = lastQuestion
		// 将宠物信息和症状描述加入上下文中
		messages2 = append(messages2, coze.BuildUserQuestionText(strings.TrimSpace(consultConversation.Title), nil))
		messages2 = append(messages2, messages...)

	}

	medicalReq := petai_vo.MedicalChatReq{
		PetInfoId:            petInfoDetail.PetId,
		QuestionUuid:         utils.GenerateUUID(),
		Text:                 textStr,
		ThreadId:             fmt.Sprintf("%s_%d", "consultconversation", consultConversation.Id),
		PetName:              petInfoDetail.PetName,
		PetKindofStr:         petInfoDetail.PetKindofStr,    // 宠物种类
		PetVarietyStr:        petInfoDetail.PetVarietyStr,   // 宠物品种,
		PetSexStr:            petInfoDetail.PetSexStr,       // 宠物性别
		PetAge:               petInfoDetail.PetAge,          // 宠物年龄
		PetWeight:            petInfoDetail.PetWeight,       // 宠物体重(单位：kg)
		PetNeuteringStr:      petInfoDetail.PetNeuteringStr, // 宠物绝育
		LastestVaccinateTime: "",                            // 最近一次疫苗时间
		LastestVaccinateName: "",                            // 最近一次疫苗名称
		LastestDewormingTime: "",                            // 最近一次驱虫时间
		LastestDewormingName: "",                            // 最近一次驱虫名称
		Messages:             messages2,
		Intent:               petai_po.IntentMedicalDesc, //意图直接定义为"医疗问题"
		PmOrderSn:            cast.ToInt64(cmd.PmOrderSn),
	}

	//log.Infof("%s 调用基础模型开始====请求地址为%s,入参为%s", logPrefix, medicalUrl, utils.JsonEncode(medicalReq))
	//c.service.ConsultStreamPost(w, flusher, medicalUrl, medicalReq, "", nil)
	ConsultStreamPostRes, answer, answerUuid, err := c.service.ConsultStreamPost(medicalUrl, medicalReq, "", nil)
	if err != nil {
		log.Error(logPrefix, "调用基础模型失败 err=", err.Error())
		response.BadRequest(w, "调用基础模型失败"+err.Error())
		return
	}
	log.Infof("%s 调用基础模型结束====请求地址为%s,请求入参为%s,返回结果为%s", logPrefix, medicalUrl, utils.JsonEncode(medicalReq), utils.JsonEncode(ConsultStreamPostRes))

	response.SuccessWithData(w, struct {
		Answer     string `json:"answer"`
		AnswerUuid string `json:"answer_uuid"`
	}{
		Answer:     answer,
		AnswerUuid: answerUuid,
	})

}

// ConsultChatReferenceContent 互联网医院医生端智能问答测试版 医生引用模型回答
// @Summary 互联网医院医生端智能问答测试版 医生引用模型回答 @petai-v2.1.0
// @Description 互联网医院医生端智能问答测试版 医生引用模型回答
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.ConsultChatReferenceContentReq true "请求对话参数"
// @Success 200 {object} petai_vo.ConsultChatReferenceContentRes "成功"
// @Failure 400 {object} petai_vo.ConsultChatRes "请求错误"
// @Router /petai-app/api/pet-medical/consult/chat/reference-content [post]
func (c *CozeController) ConsultChatReferenceContent(w http.ResponseWriter, r *http.Request) {
	logPrefix := "====互联网医院医生端智能问答测试版ConsultChatReferenceContent===="
	// 3. 处理请求参数
	cmd, err := utils.Bind[petai_vo.ConsultChatReferenceContentReq](r)
	if err != nil {
		log.Error(logPrefix, "====医生引用模型回答====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "入参验证失败"+err.Error())
		return

	}
	logPrefix = fmt.Sprintf("%s 问诊会话id为%d, 引用的消息uuid为%s, 实际发送内容为%s", logPrefix, cmd.ConsultConversationId, cmd.MessageUuid, cmd.ReferenceContent)
	log.Info(logPrefix, "请求参数:", utils.JsonEncode(cmd))

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error(logPrefix, "====医生引用模型回答====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	// 既不是互联网医院医生端，也不是互联网医院用户端
	if len(jwtInfo.UserId) == 0 && len(jwtInfo.Scrmid) == 0 {
		log.Error(logPrefix, "用户未登录")
		response.BadRequest(w, "获取登录信息失败")
		return
	}

	if err := c.service.ConsultChatReferenceContent(cmd); err != nil {
		log.Error(logPrefix, "医生引用模型回答失败 err=", err.Error())
		response.BadRequest(w, "医生引用模型回答失败")
		return
	}
	response.Success(w)
}

// CancelChat 中止对话
// @Summary 中止对话 @petai-v1.0.0
// @Description 中止对话
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.CancelChatReq true "请求参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/conversation/chat/cancel [post]
func (c *CozeController) CancelChat(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====中止对话====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====中止对话====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.CancelChatReq](r)
	if err != nil {
		log.Error("====中止对话====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = eshopUserId
	err = c.service.CancelChat(cmd)
	if err != nil {
		log.Error("====中止对话====中止对话失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}
	response.Success(w)
}

// UploadFile 上传文件
// @Summary 上传文件 @petai-v1.0.0
// @Description 上传文件
// @Tags 小闻养宠助手
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "文件"
// @Success 200 {object} petai_vo.UploadFileRes "成功"
// @Failure 400 {object} petai_vo.UploadFileRes "请求错误"
// @Router /petai-app/api/conversation/file/upload [post]
func (c *CozeController) UploadFile(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====上传文件====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====上传文件====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id
	logPrefix := "====上传文件===="
	out := petai_vo.UploadFileRes{}
	// 解析文件
	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		log.Error(logPrefix, "解析文件失败 err=", err.Error())
		response.BadRequest(w, "解析文件失败")
		return
	}
	defer file.Close()

	// Init the Coze client through the access_token.
	cozeCli, _ := c.service.NewCozeAPI(eshopUserId)
	ctx := context.Background()

	uploadFile := coze.NewUploadFile(file, fileHeader.Filename)
	// 上传文件到Coze
	uploadResp, err := cozeCli.Files.Upload(ctx, &coze.UploadFilesReq{
		File: uploadFile,
	})
	if err != nil {
		log.Error(logPrefix, "上传文件失败 err=", err.Error())
		response.BadRequest(w, "上传文件失败")
		return
	}

	out.Data.FileId = uploadResp.FileInfo.ID
	out.Data.FileName = uploadResp.FileInfo.FileName
	out.Data.CreatedAt = uploadResp.FileInfo.CreatedAt
	out.Data.FileBytes = uploadResp.FileInfo.Bytes
	response.SuccessWithData(w, uploadResp.FileInfo)
}

// RetrieveFile 获取文件信息
// @Summary 获取文件信息 @petai-v1.0.0
// @Description 获取文件信息
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.RetrieveFileReq true "请求参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /petai-app/api/conversation/file/retrieve [post]
func (c *CozeController) RetrieveFile(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====获取文件信息====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====获取文件信息====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id
	logPrefix := "====获取文件信息===="

	// 解析请求参数
	cmd, err := utils.Bind[petai_vo.RetrieveFileReq](r)
	if err != nil {
		log.Error(logPrefix, "获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}

	// Init the Coze client through the access_token.
	cozeCli, _ := c.service.NewCozeAPI(eshopUserId)
	ctx := context.Background()

	// 获取文件信息
	retrieveResp, err := cozeCli.Files.Retrieve(ctx, &coze.RetrieveFilesReq{
		FileID: cmd.FileId,
	})
	if err != nil {
		log.Error(logPrefix, "获取文件信息失败 err=", err.Error())
		response.BadRequest(w, "获取文件信息失败")
		return
	}

	log.Info(logPrefix, "获取文件信息成功, LogID:", retrieveResp.LogID())
	response.SuccessWithData(w, retrieveResp.FileInfo)
}

// TransferPetMedical 转接互联网医生人工服务
// @Summary 转接互联网医生人工服务 @petai-v1.1.0
// @Description 转接互联网医生人工服务
// @Tags 小闻养宠助手
// @Accept json
// @Produce json
// @Param command body petai_vo.TransferPetMedicalReq true "请求参数"
// @Success 200 {object} response.BaseResp  "成功"
// @Failure 400 {object} response.BaseResp  "请求错误"
// @Router /petai-app/api/conversation/transfer-petmedical [post]
func (c *CozeController) TransferPetMedical(w http.ResponseWriter, r *http.Request) {
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("====转接互联网医生人工服务====获取登录信息失败 err=", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if len(jwtInfo.Id) == 0 {
		log.Error("====转接互联网医生人工服务====用户未登录")
		response.BadRequest(w, "用户未登录")
		return
	}
	eshopUserId := jwtInfo.Id

	cmd, err := utils.Bind[petai_vo.TransferPetMedicalReq](r)
	if err != nil {
		log.Error("====转接互联网医生人工服务====获取请求参数失败 err=", err.Error())
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	cmd.UserInfoId = eshopUserId
	if cmd.ConversationId <= 0 {
		log.Error("====转接互联网医生人工服务====会话id不能为空")
		response.BadRequest(w, "获取请求参数失败")
		return
	}
	isShow, err := c.service.TransferPetMedical(cmd)
	if err != nil {
		log.Error("====转接互联网医生人工服务发生错误===失败 err=", err.Error())
		response.BadRequest(w, "转接互联网医生人工服务发生错误")
		return
	}
	response.SuccessWithData(w, isShow)

}
