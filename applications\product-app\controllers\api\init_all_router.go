package api

import (
	"github.com/go-chi/chi/v5"
)

func InitAllApi(r chi.Router) {
	InitProductCategoryRouter(r)
}

func InitProductCategoryRouter(r chi.Router) {

	r.Route("/product-app/api", func(r chi.Router) {
		//分类列表
		r.Post("/category/list", CategoryListForApi)
		//小程序商品列表
		r.Post("/product/list", StoreProductList)
		// 小程序商品sku详情
		r.Post("/sku/detail", StoreSkuDetail)
		//商品库存
		r.Post("/product/stock", StoreProductStock)
		//查询商品上下架状态
		r.Post("/product/updownstatus", StoreProductUpdownstatus)

	})

}
