package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"fmt"

	"github.com/spf13/cast"
)

type BlkyProductService struct {
	common.BaseService
}

func (s BlkyProductService) BlkyProductPage(req vo.BlkyProductPageReq) ([]vo.BlkyProductData, int, error) {
	s.<PERSON>gin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	var list []vo.BlkyProductData

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	whereSql := "1=1"
	if len(req.StartLogisticsCode) > 0 {
		whereSql += fmt.Sprintf(" AND swlm >= '%s'", req.StartLogisticsCode)
	}
	if len(req.EndLogisticsCode) > 0 {
		whereSql += fmt.Sprintf(" AND swlm <= '%s'", req.EndLogisticsCode)
	}
	if len(req.StartTime) > 0 {
		whereSql += fmt.Sprintf(" AND dregtime >= '%s'", req.StartTime)
	}
	if len(req.EndTime) > 0 {
		whereSql += fmt.Sprintf(" AND dregtime <= '%s'", req.EndTime)
	}
	if len(req.ProductName) > 0 {
		whereSql += fmt.Sprintf(" AND sspmc like '%s'", "%"+req.ProductName+"%")
	}

	if req.StartSn != "" {
		whereSql += fmt.Sprintf(" AND sn >= '%s'", req.StartSn)
	}

	if req.EndSn != "" {
		whereSql += fmt.Sprintf(" AND sn <= '%s'", req.EndSn)
	}

	var total int64
	var total2 int64

	sql := ""
	if (req.DataType < 0 || req.DataType == 1) && (req.EndSn == "" && req.StartSn == "") {
		_, err := session.SQL(fmt.Sprintf("SELECT COUNT(1) FROM blky.xkucun WHERE %s", whereSql)).Get(&total)
		if err != nil {
			log.Error("查询百林康源商品列表总数失败:", err.Error())
			return nil, 0, err
		}

		sql = "(SELECT kc.iid AS id,kc.swlm,kc.sfwm,kc.dregtime,kc.sspmc,kcd.state,1 as data_type,'' sn,sspmc as product_name FROM blky.xkucun kc LEFT JOIN blky.xkucun_detail kcd ON kcd.swlm=kc.swlm)"
	}

	if req.DataType < 0 || req.DataType == 2 {
		_, err := session.SQL(fmt.Sprintf("SELECT COUNT(1) FROM blky.xlogistics_code WHERE %s", whereSql)).Get(&total2)
		if err != nil {
			log.Error("查询百林康源商品列表总数失败:", err.Error())
			return nil, 0, err
		}
		if len(sql) > 0 {
			sql += " UNION All"
		}
		sql += " (SELECT kc.iid AS id,kc.swlm,kc.sfwm,kc.dregtime,kc.sspmc,kcd.state,2 as data_type,kc.sn,t.pname as product_name FROM blky.xlogistics_code kc LEFT JOIN blky.xlogistics_detail kcd ON kcd.swlm=kc.swlm left join blky.sjan_packaging_details pd on pd.barcode = kc.swlm left join blky.sjan_packaging_tasks t on t.bill_no = pd.bill_no)"
	}

	sql = fmt.Sprintf("select * from (%s) aa", sql)

	limit := req.PageSize
	start := (req.PageIndex - 1) * req.PageSize
	err := session.SQL(fmt.Sprintf(sql+
		" WHERE %s"+
		" ORDER BY dregtime DESC,id desc"+
		" LIMIT %d, %d", whereSql, start, limit)).Find(&list)
	if err != nil {
		log.Error("查询百林康源商品列表失败:", err.Error())
		return nil, 0, err
	}

	return list, cast.ToInt(total + total2), nil
}
