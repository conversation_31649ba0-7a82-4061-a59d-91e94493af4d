package omnibus_vo

type SessionInfo struct {
	// 用户ID
	UserId string `json:"userId"`
	// 微信openId
	OpenId string `json:"openId"`
	// 微信unionId
	UnionId string `json:"unionId"`
	// 会话token
	Token string `json:"token"`
	// 用户姓名
	Username string `json:"userName"`
	// saas门店id
	TenantId  string `json:"TenantId"`
	TenantIds string `json:"TenantIds"`
	// 连锁ID
	ChainId string `json:"ChainId"`
	//代运营连锁ID
	//SourceChainId string `json:"sourceChainId"`
	//电商用户id
	MemberId int `json:"member_id"`
}
