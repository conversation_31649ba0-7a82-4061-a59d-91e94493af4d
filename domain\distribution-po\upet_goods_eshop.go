package distribution_po

import "time"

type UpetGoodsEshop struct {
	StoreId    int       `json:"store_id" xorm:"default 0 comment('店铺id') INT 'store_id'"`
	ShopId     int       `json:"shop_id" xorm:"default 0 comment('线上分销店铺id(eshop.shop.id)') INT 'shop_id'"`
	GoodsId    int       `json:"goods_id" xorm:"not null comment('商品skuid') INT 'goods_id'"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}
