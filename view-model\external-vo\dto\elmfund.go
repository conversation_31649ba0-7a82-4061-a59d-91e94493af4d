package dto

type ELMPartreFundProducts struct {
	//返回错误信息
	SubBizOrderId string `json:"sub_biz_order_id"`
	//商品的sku码,和upc,custom_sku_id三选一
	//SkuId string `json:"sku_id"`
	//商品的upc编码,和sku_id,custom_sku_id三选一
	//Upc string `json:"upc"`
	//商品的自定义编码ID,和sku_id,upc三选一
	//CustomSkuId string `json:"custom_sku_id"`
	//退款的该商品数量
	Number string `json:"number"`
	//平台商品ID
	PlatformSkuId string `json:"platform_sku_id"`
}

type ELMFundAgree struct {
	//订单编号
	OrderId string `json:"order_id"`
	//退款单号
	RefundOrderId string `json:"refund_order_id"`
	//拒绝原因,不超过100字,同意不需要填写
	RefuseReason string `json:"refuse_reason"`
	//幂等id,保持订单纬度每次请求唯一，重试不变，长度大小100个字符内
	IdempotentId string `json:"idempotent_id"`
	//逆向单ID（请通过逆向推送消息order.reverse.push获取）
	ReverseOrderId string `json:"reverse_order_id"`
	//当拒绝场景必填，即action_type = 2 or action_type =4 时 必填，枚举值： 7019-双方协商一致不再取消订单、 7020-商品已经备货完成、 7021-商品已送出、 7802-商品发出时完好、
	//7803-用户未举证/举证无效、 7804-商品影响二次销售、 7805-商品不符合7天无理由退款、 7302-未收到退货(仅退货单支持传入该code)、 7001-其他原因
	ReasonCode string `json:"reason_code"`
	//逆向单审批操作类型，枚举值：【1-同意全单/部分退款申请 、 2-拒绝全单/部分退款申请、 3-同意退货申请 、 4-拒绝退货申请】
	ActionType string `json:"action_type"`
	//原因备注说明信息（商家自定义），当reason_code为7001时 必填
	ReasonRemarks string `json:"reason_remarks"`
}

type ELMOrderCancel struct {
	//订单编号
	OrderId string `json:"order_id"`
	//取消原因分类，
	Type string `json:"type"`
	//取消原因描述
	Reason string `json:"reason"`
}

type ELMPartreFund struct {
	//订单编号
	OrderId string `json:"order_id"`
	//商品的sku码,和upc,custom_sku_id三选一
	RefundProductList []ELMPartreFundProducts `json:"refund_product_list"`
	//幂等id,保持订单纬度每次请求唯一，重试不变，长度大小100个字符内
	IdempotentId string `json:"idempotent_id"`
	//申请退款类型, 枚举值：1 全退 ， 2 部分退（按商品部分退），如 部分退（2） 则 refund_product_list 参数不能为空
	RefundType string `json:"refund_type"`
	//发起退款原因 code，枚举值：7015-商品已售完、 7017-无骑手接单/无运力、 7053-骑手无法配送、 7054-无法联系到用户（未接听、关机、号码错误）、 7018-商户暂时不营业、
	//7070-拣货人力不足、 7908-补差退款、 7001-其他原因、 7052-药师审核处方笺不通过（仅支持医药处方订单）
	ReasonCode string `json:"reason_code"`
	//详细原因信息，reason_code为7001时必填
	ReasonRemarks string `json:"reason_remarks"`
	//标品部分退资金类型，枚举值：0 按件数退，1 按照金额退 （部分退场景有效，不填 默认为 0 标品按件数退，称重品必须按金额退）
	//FundCalculateType string `json:"fund_calculate_type"`
	//非必填，reason_code为7015 缺货售中部分取消退款场景，是否需要平台外呼通知用户，仅refund_type=2时 传入有效；枚举值： 1:通知 ， 0 or null :不通知
	//NeedIvrUser int32 `json:"need_ivr_user"`
}

type ELMReturnMes struct {
	Body      BodyMessage `json:"body"`
	Cmd       string      `json:"cmd"`
	Sign      string      `json:"sign"`
	Source    string      `json:"source"`
	Ticket    string      `json:"ticket"`
	Timestamp int         `json:"timestamp"`
	Version   string      `json:"version"`
}

type BodyMessage struct {
	Errno int    `json:"errno"`
	Error string `json:"error"`
}

type ELMBase struct {
	Cmd       string `json:"cmd"`
	Sign      string `json:"sign"`
	Source    string `json:"source"`
	Ticket    string `json:"ticket"`
	Timestamp int    `json:"timestamp"`
	Version   string `json:"version"`
}

type ELMBody struct {
	Errno int    `json:"errno"`
	Error string `json:"error"`
}

// 2.0订单逆向
type ELMReturn struct {
	ELMOrderReverseQueryRes
	ELMBase
}

type ELMOrderReverseQueryRes struct {
	Body ELMOrderReverseQueryBody `json:"body"`
}

type ELMOrderReverseQueryBody struct {
	Data ELMOrderReverseQueryData `json:"data"`
	ELMBody
}

type ELMOrderReverseQuery struct {
	//订单编号
	OrderId string `json:"order_id"`
}

type ELMOrderReverseQueryData struct {
	//饿了么订单号
	OrderId string `json:"order_id"`
	//退款成功后订单当前剩余【订单总金额】，单位：分
	RemainTotalPrice int64 `json:"remain_total_price"`
	//退款成功后订单当前剩余【用户实付总金额】，单位：分
	RemainUserTotalAmount int64 `json:"remain_user_total_amount"`
	//退款成功后当前剩余【订单总优惠】金额，单位：分
	RemainDiscountTotalAmount int64 `json:"remain_discount_total_amount"`
	//退款成功后当前订单剩余【商户应收】金额，单位：分
	MerchantIncome int64 `json:"merchant_income"`
	//退款成功后当前订单剩余佣金总和【技术服务费（settled_merchant_commission_amount）+履约服务费（base_logistics_amount）+支付服务费（pay_channel_fee）】，单位：分
	CommissionAmount int64 `json:"commission_amount"`
	//退款成功退款后当前订单剩余【技术服务费】，单位：分
	SettledMerchantCommissionAmount int64 `json:"settled_merchant_commission_amount"`
	//退款成功后当前订单剩余【履约服务费】，单位：分
	BaseLogisticsAmount int64 `json:"base_logistics_amount"`
	//退款成功后当前订单剩余【支付服务费】，单位：分
	PayChannelFee int64 `json:"pay_channel_fee"`
	//主退单列表
	ReverseOrderList []ReverseOrderList `json:"reverse_order_list"`
}

type ReverseOrderList struct {
	//饿了么订单号
	OrderId string `json:"order_id"`
	//零售退款单ID
	RefundOrderId string `json:"refund_order_id"`
	//退单发起退款时传入的幂等Id
	IdempotentId string `json:"idempotent_id"`
	//退款发起角色:10用户,20商户,25API商家,30客服,40系统,50物流,60风控
	OperatorRole string `json:"operator_role"`
	//逆向单场景：100 售中, 200 售后
	Scene string `json:"scene"`
	//该退单的上一个退款状态：0初始化，10申请，20拒绝，30仲裁，40关闭，50成功，60失败
	LastRefundStatus int32 `json:"last_refund_status"`
	//该退单当前退款状态：0初始化，10申请，20拒绝，30仲裁，40关闭，50成功，60失败
	RefundStatus int32 `json:"refund_status"`
	//是否退货标志 0：否 1：是
	WhetherReturnGoods int32 `json:"whether_return_goods"`
	//该退单的上一个退货子状态:0无效状态，1001买家已申请退货等待审批处理，1002商家拒绝退货申请，1003退货仲裁已发起客服介入中，1004已同意退货等待发货，
	//1005已发货等待卖家确认收货，1006已收到货并同意退款，1007未收到货不同意退款，1008退货关闭
	LastReturnGoodsStatus int32 `json:"last_return_goods_status"`
	//该退单的当前退货子状态:0无效状态，1001买家已申请退货等待审批处理，1002商家拒绝退货申请，1003退货仲裁已发起客服介入中，1004已同意退货等待发货，
	//1005已发货等待卖家确认收货，1006已收到货并同意退款，1007未收到货不同意退款，1008退货关闭
	ReturnGoodsStatus int32 `json:"return_goods_status"`
	//该逆向单申请退用户的金额，单位分
	ApplyRefundUserAmount int64 `json:"apply_refund_user_amount"`
	//该逆向单实际退款给用户的金额，单位分
	RefundUserAmount int64 `json:"refund_user_amount"`
	//发起退单的原因code，具体原因枚举值详见文档 https://open-retail.ele.me/#/guide?topic=ntmt8f
	RefundReasonCode int32 `json:"refund_reason_code"`
	//发起退单的原因code描述
	RefundReasonCodeDesc string `json:"refund_reason_code_desc"`
	//发起退款的原因附加备注描述信息
	RefundReasonContent string `json:"refund_reason_content"`
	//退单凭证列表
	ImageList []string `json:"image_list"`
	//本次退单退款成功后，订单所有商品是否已全部退完，0|未退完 1|已退完
	IsRefundAll int32 `json:"is_refund_all"`
	//逆向单创建（申请）时间戳，单位毫秒
	ApplyTime int64 `json:"apply_time"`
	//退单补贴分摊金额
	DiscountDetail DiscountDetail `json:"discount_detail"`
	//退单购物金详情
	RefundShopCardDetail RefundShopCardDetail `json:"refund_shop_card_detail"`
	//退单中的退货信息
	ReturnGoodsInfo ReturnGoodsInfo `json:"return_goods_info"`
	//子退单列表
	SubReverseOrderList []SubReverseOrderList `json:"sub_reverse_order_list"`
}

type DiscountDetail struct {
	//商品原总价 = （用户实付 + 补贴总额 discount_total_amount ）
	TotalPrice int64 `json:"total_price"`
	//本次退款总金额中的补贴总额，单位：分
	DiscountTotalAmount int64 `json:"discount_total_amount"`
	//本次退款总金额中 包含的【商家】承担金额，单位：分
	MerchantDiscountAmount int64 `json:"merchant_discount_amount"`
	//本次退款总金额中 包含的【平台】承担金额，单位：分
	PlatformDiscountAmount int64 `json:"platform_discount_amount"`
	//本次退款总金额中 包含的【代理商】承担金额，单位：分
	AgentDiscountAmount int64 `json:"agent_discount_amount"`
	//本次退款总金额中 包含的【用户】承担金额，单位：分
	UserDiscountAmount int64 `json:"user_discount_amount"`
}

type RefundShopCardDetail struct {
	//退用户购物金金额，单位：分
	RefundShopCardPrice int32 `json:"refund_shop_card_price"`
	//本金金额，单位：分
	BaseShopCardPrice int32 `json:"base_shop_card_price"`
	//赠金金额：单位：分
	GiveShopCardPrice int32 `json:"give_shop_card_price"`
	//赠金中【平台承担】金额，单位：分
	PlatformGiveShopCardRate int32 `json:"platform_give_shop_card_rate"`
	//赠金中【商家承担】金额，单位：分
	ShopGiveShopCardRate int32 `json:"shop_give_shop_card_rate"`
}

type ReturnGoodsInfo struct {
	//退货方式，枚举值：0-商家上门取货；1-自行送回
	ReturnGoodsType int32 `json:"return_goods_type"`
	//自行退回方式，枚举值：1.快递退回；2. 跑腿退回；3.自行送回
	SelfReturnType int32 `json:"self_return_type"`
	//商家上门取货时间(开始)， returnGoodsType = 1 时有效，格式：时间戳，精确到毫秒
	ExpectPickUpStartTime int64 `json:"shopGiveShopCardRate"`
	//商家上门取货时间(结束) ，returnGoodsType = 1 时有效，格式：时间戳，精确到毫秒
	ExpectPickUpEndTime int64 `json:"expect_pick_up_end_time"`
	//商家上门取货地址，returnGoodsType = 1 时有效
	PickUpAddress string `json:"pick_up_address"`
	//商家上门取货 联系人名称，returnGoodsType = 1 时有效
	ContactName string `json:"contact_name"`
	//商家上门取货 用户真实手机号，returnGoodsType = 1 时有效
	ContactPhone string `json:"contact_phone"`
	//商家上门取货 用户隐私手机号，returnGoodsType = 1 时有效
	PrivacyContactPhone int32 `json:"privacy_contact_phone"`
	//用户在C端确认退回时间，格式：时间戳，精确到毫秒
	SendOffTime int64 `json:"send_off_time"`
	//用户自行送回的快递单号，当selfReturnType = 1 时 必填
	LogisticsOrderId string `json:"logistics_order_id"`
}

type SubReverseOrderList struct {
	//商品多规格自定义id
	CustomSkuSpecID string `json:"custom_sku_spec_id"`
	//商品子单本次退款状态，枚举值：10 申请；20 拒绝；30 仲裁；40 关闭；50 成功；60 失败
	RefundStatus int `json:"refund_status"`
	//饿了么商品ID
	PlatformSkuID int64 `json:"platform_sku_id"`
	//虚拟订单类型，commodityType=4 商品类型为虚拟商品是有效；0:普通实物商品 1:包装费子单 2:处方服务费子单、3:附加0.01费子单、4:配送费子单
	VirtualType string `json:"virtual_type"`
	//是否是赠品子单，true 是， false 否
	FreeGift bool `json:"free_gift"`
	//申请退款件数/金额，件数单位：整型，金额单位：分
	ApplyQuantity int64 `json:"apply_quantity"`
	//商品 upc 信息
	Upc string `json:"upc"`
	//商品子单类型, 1:称重品、2:标品、3:按件称重、4:虚拟商品子单
	CommodityType int `json:"commodity_type"`
	//赠品子单关联的主品子单Id列表 （free_gift = true 时，有效）
	GiftRelatedSubBizOrderIDList []string `json:"gift_related_sub_biz_order_id_list"`
	//商品子单 退款算价类型 ，枚举值：0按件数退，1按照金额退
	FundCalculateType int `json:"fund_calculate_type"`
	//商品子单唯一标识
	SubBizOrderID string `json:"sub_biz_order_id"`
	//商家同意件数/金额，件数单位：整型，金额单位：分
	RefundQuantity int64 `json:"refund_quantity"`
	//商品子单的退款分摊明细详情
	DiscountDetail DiscountDetail `json:"discount_detail"`
	//商品子单的上一次退款状态，枚举值：10 申请；20 拒绝；30 仲裁；40 关闭；50 成功；60 失败
	LastRefundStatus int `json:"last_refund_status"`
	//主退单ID，标识订单一次退款/退货
	RefundOrderID int64 `json:"refund_order_id"`
	//商品自定义id
	CustomSkuID string `json:"custom_sku_id"`
	//商品子单实际退用户的金额，单位：分
	RefundUserAmount int64 `json:"refund_user_amount"`
	//商品子单申请创建时间，单位：毫秒
	ApplyRefundTime int64 `json:"apply_refund_time"`
	//sku 商品名称
	SkuName string `json:"sku_name"`
	//商品子单用户申请退款金额，单位：分
	ApplyRefundUserAmount int64 `json:"apply_refund_user_amount"`
}

type ElmOrderReverseProcess struct {
	//逆向单ID（请通过逆向推送消息order.reverse.push获取）
	ReverseOrderId string `json:"reverse_order_id"`
	//订单号（平台饿了么单号）
	OrderId string `json:"order_id"`
	//幂等Id（商家自定义），请求唯一标识
	IdempotentId string `json:"idempotent_id"`
	//逆向单审批操作类型，枚举值：【1-同意全单/部分退款申请 、 2-拒绝全单/部分退款申请、 3-同意退货申请 、 4-拒绝退货申请】
	ActionType string `json:"action_type"`
	//当拒绝场景必填，即action_type = 2 or action_type =4 时 必填，枚举值： 7019-双方协商一致不再取消订单、 7020-商品已经备货完成、 7021-商品已送出、 7802-商品发出时完好、
	//7803-用户未举证/举证无效、 7804-商品影响二次销售、 7805-商品不符合7天无理由退款、 7302-未收到退货(仅退货单支持传入该code)、 7001-其他原因
	ReasonCode string `json:"reason_code"`
	//原因备注说明信息（商家自定义），当reason_code为7001时 必填
	ReasonRemarks string `json:"reason_remarks"`
	//商品的sku码,和upc,custom_sku_id三选一
	RefundProductList []RefundProductList `json:"refund_product_list"`
}

type RefundProductList struct {
	//商品子单ID
	SubBizOrderId string `json:"sub_biz_order_id"`
	//平台商品ID
	PlatformSkuId int64 `json:"platform_sku_id"`
	//申请退款件数（整型）
	Number string `json:"number"`
	//申请退款金额（单位：分，整型）
	RefundAmount string `json:"refund_amount"`
}

type ElmOrderReverseApply struct {
	//订单号（平台饿了么单号）
	OrderId string `json:"order_id"`
	//幂等Id（商家自定义），请求唯一标识
	IdempotentId string `json:"idempotent_id"`
	//申请退款类型, 枚举值：1 全退 ， 2 部分退（按商品部分退），如 部分退（2） 则 refund_product_list 参数不能为空
	RefundType string `json:"refund_type"`
	//发起退款原因 code，枚举值：7015-商品已售完、 7017-无骑手接单/无运力、 7053-骑手无法配送、 7054-无法联系到用户（未接听、关机、号码错误）、 7018-商户暂时不营业、
	//7070-拣货人力不足、 7908-补差退款、 7001-其他原因、 7052-药师审核处方笺不通过（仅支持医药处方订单）
	ReasonCode string `json:"reason_code"`
	//详细原因信息，reason_code为7001时必填
	ReasonRemarks string `json:"reason_remarks"`
	//标品部分退资金类型，枚举值：0 按件数退，1 按照金额退 （部分退场景有效，不填 默认为 0 标品按件数退，称重品必须按金额退）
	FundCalculateType string `json:"fund_calculate_type"`
	//非必填，reason_code为7015 缺货售中部分取消退款场景，是否需要平台外呼通知用户，仅refund_type=2时 传入有效；枚举值： 1:通知 ， 0 or null :不通知
	NeedIvrUser string `json:"need_ivr_user"`
	//申请退款商品列表信息（非必填），部分退（ refund_type = 2）时，refund_product_list 必填
	RefundProductList []RefundProductList `json:"refund_product_list"`
}

type ElmOrderReverseConsult struct {
	//饿了么订单号
	OrderId string `json:"order_id"`
	//查询订单剩余可退商品信息； true 查询订单剩余可退商品及分摊信息 ， false or null 按照入参传入商品子单列表（order_line_list）查询可退商品金额及分摊信息
	RefundAllSubOrder bool `json:"refund_all_sub_order"`
	//需退款预览的商品子单列表， refund_all_order_line = true , 传入的子单列表无效
	SubOrderList []SubOrderList `json:"sub_order_list"`
}

type SubOrderList struct {
	//商品子单ID（必填）
	SubBizOrderId string `json:"sub_order_list"`
	//平台商品ID
	PlatformSkuId int64 `json:"platform_sku_id"`
	//退款咨询算价类型，枚举值： 0:按件数退、 1:按金额退、2:按重量退（查询商品重量可退金额）
	RefundCalType int32 `json:"refund_cal_type"`
	//退款数量 （refund_cal_type = 0 时，必填）
	RefundQuantity int64 `json:"refund_quantity"`
	//退款金额，单位/分 (refund_cal_type = 1 时，必填)
	RefundAmount int64 `json:"refund_amount"`
	//实际要退的重量，单位/克 g （refund_cal_type = 2 时，必填）
	RefundWeight int64 `json:"refund_weight"`
}

type ELMConsultReturn struct {
	ELMConsultReturnRes
	ELMBase
}

type ELMConsultReturnRes struct {
	Body ELMConsultReturnBody `json:"body"`
}

type ELMConsultReturnBody struct {
	Data ELMConsultReturnData `json:"data"`
	ELMBody
}

type ELMConsultReturnData struct {
	//可退回用户的总金额，单位/分
	RefundUserTotalAmount int `json:"refund_user_total_amount"`
	//商品子单 算价结果明细(注：订单包装费、配送费等虚拟费用会以商品子单结构体现)
	SubOrderConsultResultList []SubOrderConsultResultList `json:"sub_order_consult_result_list"`
	//退回平台的总金额，单位/分
	MerchantRefundTotalAmount int `json:"merchant_refund_total_amount"`
	//饿了么订单号
	OrderID string `json:"order_id"`
	//是否已全部退完；枚举值：true 是、 false 否
	IsRefundAll bool `json:"is_refund_all"`
	//部分取消/退款 校验结果
	PartRefundCheckResult []PartRefundCheckResult `json:"part_refund_check_result"`
	//全单取消/退款 校验结果
	AllRefundCheckResult []PartRefundCheckResult `json:"all_refund_check_result"`
}

type SubOrderConsultResultList struct {
	//当前商品子单 应退商品数量，商品总数量 = cur_refund_quantity+ refunded_quantity + refundable_quantity
	CurRefundQuantity int `json:"cur_refund_quantity"`
	//当前商品子单 剩余可退的商品数量， 整型
	RefundableQuantity int `json:"refundable_quantity"`
	//当前商品子单 已退用户实付金额，单位/分
	CurRefundUserAmount int `json:"cur_refund_user_amount"`
	//虚拟商品子单类型, commodity_type =4 时有效；枚举值： 0:普通实物商品 1:包装费 2:处方服务费 3:附加0.01费 4:配送费
	VirtualType int `json:"virtual_type"`
	//当前商品子单 剩余可退 原价总额，单位/分
	RefundableTotalPrice int `json:"refundable_total_price"`
	//是否是赠品商品子单，枚举值：true 是， false 否
	FreeGift bool `json:"free_gift"`
	//商品 upc 信息
	Upc string `json:"upc"`
	//商品子单类型, 枚举值：1:称重品、2:标品、3:按件称重、4:虚拟费用商品
	CommodityType int `json:"commodity_type"`
	//商品子单ID
	SubBizOrderId int64 `json:"sub_biz_order_id"`
	//平台商品ID
	PlatformSkuId int64 `json:"platform_sku_id"`
	//商家商品自定义id
	CustomSkuId string `json:"custom_sku_id"`
	//商品多规格自定义id
	CustomSkuSpecId string `json:"custom_sku_spec_id"`
	//当前商品子单 退款分摊明细
	SubOrderDiscountDetail SubOrderDiscountDetail `json:"sub_order_discount_detail"`
	//当前商品子单 剩余可退的用户实付金额，单位/分
	RefundableUserAmount int `json:"refundable_user_amount"`
	//赠品商品子单关联的主品子单Id列表 （free_gift = true 时，有效）
	GiftRelatedOrderLineIDList []string `json:"gift_related_order_line_id_list"`
	//当前商品子单 已退的原价总额，单位/分
	RefundedTotalPrice int `json:"refunded_total_price"`
	//当前商品子单 已退商品数量（如：称重品：全部退完时为 1 ， 未退完时为 0
	RefundedQuantity int `json:"refunded_quantity"`
	//当前商品子单 退款原总价，单位/分，商品总原价 = cur_refund_total_price + refunded_total_price + refundable_total_price
	CurRefundTotalPrice int    `json:"cur_refund_total_price"`
	OrderLineID         string `json:"order_line_id"`
	//sku商品名称
	SkuName string `json:"sku_name"`
	//当前商品子单 已退用户实付金额，单位/分
	RefundedUserAmount int `json:"refunded_user_amount"`
	//按照当前传参商品预算价，该商品子单是否已退完，枚举值：true 已退完，false 未退完
	IsRefundAll bool `json:"is_refund_all"`
	//是否为组合品
	IsCombine bool `json:"is_combine"`
}

type SubOrderDiscountDetail struct {
	//原总价 = （用户实付 cur_refund_user_amount + 补贴总额 discount_total_amount ），单位：分
	TotalPrice int `json:"total_price"`
	//当前退款总金额中的补贴总额，单位：分
	PlatformDiscountAmount int `json:"platform_discount_amount"`
	//当前退款总金额中 包含的【商家】承担金额，单位：分
	DiscountTotalAmount int `json:"discount_total_amount"`
	//当前退款总金额中 包含的【平台】承担金额，单位：分
	UserDiscountAmount int `json:"user_discount_amount"`
	//当前退款总金额中 包含的【代理商】承担金额，单位：分
	MerchantDiscountAmount int `json:"merchant_discount_amount"`
	//当前退款总金额中 包含的【用户】承担金额，单位：分
	AgentDiscountAmount int `json:"agent_discount_amount"`
}

type PartRefundCheckResult struct {
	//校验结果 true: 支持 、 false:不支持
	CheckResult bool `json:"total_price"`
	//不支持原因code ， 不支持的原因code；check_result = false 有效
	ErrorCode string `json:"total_price"`
	//不支持原因描述，不支持的原因描述；check_result = false 有效
	ErrorMessage string `json:"total_price"`
}

type ELMOrderCancelData struct {
	//订单ID
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
	//退单ID
	RefundOrderId interface{} `json:"refund_order_id" form:"refund_order_id" query:"refund_order_id"`
	//消息类型。10:发起申请,20:客服介入,
	//30:客服拒绝,40:客服同意,
	//50:商户拒绝,60:商户同意,70:申请失效
	Type string `json:"type" form:"type" query:"type"`
	//申请取消原因
	CancelReason string `json:"cancel_reason" form:"cancel_reason" query:"cancel_reason"`
	//申请取消附加原因
	AdditionReason string `json:"addition_reason" form:"addition_reason" query:"addition_reason"`
	//拒绝原因
	RefuseReason string `json:"refuse_reason" form:"refuse_reason" query:"refuse_reason"`
	//区分订单完成前用户全单取消或订单完成后全单退款流程。
	//1表示订单完成前用户全单取消申请流程，
	//2表示订单完成后用户全单退款申请流程
	CancelType string `json:"cancel_type" form:"cancel_type" query:"cancel_type"`
	//用户上传申请退款图片信息，最多3张；
	Pictures []string `json:"pictures" form:"pictures" query:"pictures"`
}

type ELMOrderCreate struct {
	//订单ID
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
}
