package distribution_po

import (
	"time"

	"xorm.io/xorm"
)

type DisDistributorDetail struct {
	DisId        int       `json:"dis_id" xorm:"pk not null INT 'dis_id'"`
	HospitalName string    `json:"hospital_name" xorm:"not null default '' VARCHAR(32) 'hospital_name'"`
	Province     string    `json:"province" xorm:"not null default '' comment('省份') VARCHAR(20) 'province'"`
	City         string    `json:"city" xorm:"not null default '' comment('城市') VARCHAR(20) 'city'"`
	Professional string    `json:"professional" xorm:"default '' comment('身份、职称') VARCHAR(20) 'professional'"`
	Specialize   string    `json:"specialize" xorm:"default '' comment('擅长') VARCHAR(20) 'specialize'"`
	CreateTime   time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	UpdateTime   time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
}

func (d *DisDistributorDetail) TableName() string {
	return "eshop.dis_distributor_detail"
}

func (d *DisDistributorDetail) Insert(session *xorm.Session) (err error) {
	exist, err := session.Table(d.TableName()).Where("dis_id = ?", d.DisId).Get(d)
	if err != nil {
		return
	}
	if exist {
		return
	}

	_, err = session.Table(d.TableName()).Insert(d)
	return
}
