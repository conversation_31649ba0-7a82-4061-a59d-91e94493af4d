package services

import (
	"eShop/services/common"
	"eShop/view-model/distribution-vo"
	"testing"
)

func TestJobStatsSalespersonService_StatsSalespersonOrderDailyData(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req []distribution_vo.StatsShopDistributorDailyReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "tesst",
			args: args{
				req: []distribution_vo.StatsShopDistributorDailyReq{
					{StartDate: "2024-06-11", EndDate: "2024-06-13"},
				}},
			fields: fields{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &JobStatsSalespersonService{
				BaseService: tt.fields.BaseService,
			}
			s.StatsSalespersonOrderDailyData(tt.args.req...)
		})
	}
}

func TestJobStatsSalespersonService_StatsSalespersonOrderDaliyDataRun(t *testing.T) {
	s := JobStatsSalespersonService{}
	//s.StatsSalespersonOrderDaliyDataRun("2024-04-01", "2024-07-14")
	s.StatsSalespersonOrderWeeklyDataRun()
}
