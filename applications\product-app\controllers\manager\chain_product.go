package manager

import (
	"eShop/infra/enum"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/product-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// AddChainProduct
// @Summary 商品服务-连锁商品库-添加单个连锁商品
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param ChainProductReq body vo.ChainProduct true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/chain/add [POST]
func AddChainProduct(writer http.ResponseWriter, request *http.Request) {

	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.ChainProduct](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	req.Product.CreatedBy = jwtInfo.UserId
	req.Product.CreatedName = jwtInfo.UserName
	req.Product.UpdatedBy = jwtInfo.UserId
	req.Product.UpdatedName = jwtInfo.UserName
	req.Product.ChainId = jwtInfo.ChainId
	// cast.ToInt64(jwtInfo.SourceChainId) > 0代表店铺被代运营了
	// 如果代运营连锁的员工登录， jwtInfo.SourceChainId 代表代运营连锁
	// 如果被代运营连锁的员工登录， jwtInfo.SourceChainId 代表代运营连锁
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		req.Product.ChainId = jwtInfo.SourceChainId
	}
	server := services.ProductService{JwtInfo: jwtInfo}
	if err := server.AddChainProduct(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// GetChainProductInfo
// @Summary 商品服务-连锁商品详情-获取连锁商品详情
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param GetChainProductReq body vo.GetChainProductReq true " "
// @Success 200 {object} vo.GetChainProductRes
// @Router /product-app/manager/chain/info [POST]
func GetChainProductInfo(writer http.ResponseWriter, request *http.Request) {
	out := vo.GetChainProductRes{}
	out.Code = 400
	req, err := utils.Bind[vo.GetChainProductReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	req.ChainId = cast.ToInt(jwtInfo.ChainId)
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		req.ChainId = cast.ToInt(jwtInfo.SourceChainId)
	}
	server := services.ProductService{}
	out.Data, err = server.GetChainProductInfo(req)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// FindChainProductList
// @Summary 商品服务-连锁商品库-获取连锁商品列表(以spu为维度来查列表)
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param FindChainProductListReq body vo.FindChainProductListReq true " "
// @Success 200 {object} vo.FindChainProductListRes
// @Router /product-app/manager/chain/list [POST]
func FindChainProductList(writer http.ResponseWriter, request *http.Request) {
	out := vo.FindChainProductListRes{}
	out.Code = 400
	out.Data = make([]vo.FindChainProductList, 0)
	req, err := utils.Bind[vo.FindChainProductListReq](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	req.ChainId = jwtInfo.ChainId
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		req.ChainId = jwtInfo.SourceChainId
	}
	server := services.ProductService{JwtInfo: jwtInfo}
	var total int64
	out.Data, total, err = server.FindChainProductList(req)
	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Total = int(total)
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// ChainProductExport
// @Summary  商品服务-连锁商品库-连锁商品导出
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param FindChainProductListReq body vo.FindChainProductListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/chain/export [POST]
func ChainProductExport(w http.ResponseWriter, r *http.Request) {
	out := viewmodel.BaseHttpResponse{
		Code: 400,
	}
	req, err := utils.Bind[vo.FindChainProductListReq](r)
	if err != nil {
		out.Message = fmt.Sprintf("导出请求参数解析失败：%s" ,err.Error())
	} else {
		jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
		if err != nil {
			out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		} else if jwtInfo.TenantId == "" {
			out.Message = "门店ID不能为空"
		}
		req.ChainId = jwtInfo.ChainId

		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = enum.TaskContentChainProductExport
		task.CreateId = jwtInfo.UserId
		task.CreateName = jwtInfo.UserName
		task.RequestHeader = utils.InterfaceToJSON(jwtInfo)
		err = s.CreatTask(r, task)
		if err != nil {
			out.Message = fmt.Sprintf("导出商品数据：%s", err.Error())
		} else {
			out.Code = 200
		}
	}
	bytes, _ := json.Marshal(out)
	w.Write(bytes)
}

// BatchPushChainProduct
// @Summary 商品服务-连锁商品库-批量下发连锁商品到门店
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param BatchPushChainProductReqReq body vo.BatchPushChainProductReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/chain/batch-push [POST]
func BatchPushChainProduct(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.BatchPushChainProductReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	req.CreatedId = cast.ToInt64(jwtInfo.UserId)
	req.CreateName = jwtInfo.UserName
	req.ChainId = cast.ToInt(jwtInfo.ChainId)
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		req.ChainId = cast.ToInt(jwtInfo.SourceChainId)
	}
	server := services.ProductService{
		Request: request,
		JwtInfo: jwtInfo,
	}

	if err := server.BatchPushChainProduct(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// EditChainProduct
// @Summary 商品服务-连锁商品库-编辑单个连锁商品
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param ChainProductReq body vo.ChainProduct true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/chain/edit [POST]
func EditChainProduct(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.ChainProduct](request)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	server := services.ProductService{
		Request: request,
		JwtInfo: jwtInfo,
	}
	req.Product.UpdatedBy = jwtInfo.UserId
	req.Product.UpdatedName = jwtInfo.UserName
	req.Product.ChainId = jwtInfo.ChainId
	// 如果来源连锁id不为空，则使用来源连锁id
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		req.Product.ChainId = jwtInfo.SourceChainId
	}
	if err := server.EditChainProduct(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// DelChainProduct
// @Summary 商品服务-连锁商品库-删除单个连锁商品
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param DelChainProductReq body vo.DelChainProductReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/chain/del [POST]
func DelChainProduct(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.DelChainProductReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	req.UpdatedBy = cast.ToInt64(jwtInfo.UserId)
	req.UpdatedName = jwtInfo.UserName
	server := services.ProductService{
		Request: request,
	}
	req.ChainId = cast.ToInt(jwtInfo.ChainId)
	if cast.ToInt64(jwtInfo.SourceChainId) > 0 {
		req.ChainId = cast.ToInt(jwtInfo.SourceChainId)
	}
	if err := server.DelChainProduct(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// GenerateBarCode
// @Summary 商品服务-连锁商品库-生成商品条码
// @Tags 宠物连锁SAAS-管理后台
// @Accept  plain
// @Produce  json
// @Success 200 {object} vo.GenerateBarCodeRes
// @Router /product-app/manager/chain/gene-barcode [POST]
func GenerateBarCode(writer http.ResponseWriter, request *http.Request) {
	out := vo.GenerateBarCodeRes{}
	out.Code = 400
	var err error
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	server := services.ProductService{}
	if out.Data.BarCode, err = server.GenerateBarCode(cast.ToInt64(jwtInfo.ChainId)); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}
