package petai_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

// 互联网医院医生端PC发起的问诊会话表
type PetaiConsultConversation struct {
	Id         int       `json:"id"  xorm:"pk autoincr 'id' comment('主键')"` //自增id
	PmOrderSn  int64     `json:"pm_order_sn"`                               //互联网医院问诊订单号
	ScrmUserId string    `json:"scrm_user_id"`                              //用户scrmid即scrm_organization_db.t_scrm_user_info.user_id
	DoctorCode string    `json:"doctor_code"`                               //医生编号即pet_medical.pm_consult_info.doctor_code
	Title      string    `json:"title"`                                     //会话标题
	CreateTime time.Time `json:"create_time" xorm:"created"`                //创建时间
	UpdateTime time.Time `json:"update_time" xorm:"updated"`                //更新时间

}

type PetaiConsultConversationExd struct {
	PetaiConsultConversation `xorm:"extends"`
	PetId                    string `json:"pet_id" xorm:"<-"`
	PetName                  string `json:"pet_name" xorm:"<-"`
	PetSex                   int32
	PetKindof                string `json:"pet_kindof" xorm:"<-"`
	PetVariety               string `json:"pet_variety" xorm:"<-"`
	PetAge                   string

	ImmuneStatus  int32  `json:"immune_status" xorm:"<-"`
	PetNeutering  int32  `json:"pet_neutering" xorm:"<-"`
	BathFreq      int32  `json:"bath_freq" xorm:"<-"`
	FeedWay       int32  `json:"feed_way" xorm:"<-"`
	SymptomType   string `json:"symptom_type" xorm:"<-"`
	Symptom       string `json:"symptom" xorm:"<-"`
	SymptomRecent int32  `json:"symptom_recent" xorm:"<-"`
}

func (m *PetaiConsultConversation) TableName() string {
	return "eshop.petai_consult_conversation"
}

func (m *PetaiConsultConversation) GetConsultConversationByPmOrderSn(session *xorm.Session, pmOrderSn int64) (out *PetaiConsultConversation, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if pmOrderSn == 0 {
		err = errors.New("订单号不能为空")
		return
	}
	out = new(PetaiConsultConversation)
	session.Table("eshop.petai_consult_conversation").Alias("a").
		Where("a.pm_order_sn=?", pmOrderSn)

	exists, err := session.Get(out)
	if err != nil {
		return
	}
	if !exists {
		err = errors.New("问诊会话不存在")
		return
	}
	return
}

func (m *PetaiConsultConversation) GetConsultConversationById(session *xorm.Session, conversationId int64) (out *PetaiConsultConversation, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if conversationId < 0 {
		err = errors.New("会话id不能为空")
		return
	}
	out = new(PetaiConsultConversation)
	session.Table("eshop.petai_consult_conversation").Alias("a").
		Where("a.id=?", conversationId)

	exists, err := session.Get(out)
	if err != nil {
		return
	}
	if !exists {
		err = errors.New("问诊会话不存在")
		return
	}
	return
}
