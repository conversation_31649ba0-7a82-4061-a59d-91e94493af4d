package distribution_vo

type PiPolicyOrderDto struct {
	Id             int     `json:"id"`
	DelFlag        int     `json:"del_flag"`
	OrderNo        string  `json:"order_no"`
	DisId          int     `json:"dis_id"`
	DisRate        float64 `json:"dis_rate"`
	SalesmanId     int     `json:"salesman_id"`
	ShopId         int     `json:"shop_id"`
	RealPremiumAmt float64 `json:"real_premium_amt"`
	DisType        int     `json:"dis_type"`
	EnterpriseId   int64   `json:"enterprise_id"`
	SalespersonIds string  `json:"salesperson_ids"`
	UserId         string  `json:"user_id"`
}
