package product_po

import (
	"xorm.io/xorm"
)

// 原结构体

// 新结构体，添加注释 2025-01-22
type ProProductChannel struct {
	// 主键
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`
	// 商品ID
	ProductId int `json:"product_id" xorm:"default 'null' comment('商品ID') INT 'product_id'"`
	// 第三方类目ID
	CategoryThirdId int `json:"category_third_id" xorm:"default 'null' comment('第三方类目ID') INT 'category_third_id'"`
	// 第三方类目名称
	CategoryThirdName string `json:"category_third_name" xorm:"default 'null' comment('第三方类目名称') INT 'category_third_name'"`
	// 渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)
	ChannelId int `json:"channel_id" xorm:"default 'null' comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') INT 'channel_id'"`
	// 是否同步成功,0，未同步，1成功，2失败（这里是指同步到第三方渠道的总部商品库，例如：只有启动美团总部商品库 该字段才有用）
	SyncStatus int `json:"sync_status" xorm:"not null default 0 comment('是否同步成功,0，未同步，1成功，2失败（这里是指同步到第三方渠道的总部商品库，例如：只有启动美团总部商品库 该字段才有用）') INT 'sync_status'"`
	// 同步错误信息
	SyncError string `json:"sync_error" xorm:"default 'null' comment('同步错误信息') VARCHAR(500) 'sync_error'"`
	// 添加时间
	CreateDate string `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	// 修改时间
	UpdateDate string `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
}

// out0=map[商品id][]ProProductChannel
// out1 = map[商品id]map[渠道id]渠道类目信息
func GetProductChannelInfo(session *xorm.Session, where map[string]interface{}) (out0 map[int][]ProProductChannel, out1 map[int]map[int]ProProductChannel, err error) {
	out0 = make(map[int][]ProProductChannel)
	out1 = make(map[int]map[int]ProProductChannel)

	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}
	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	channelIdSli, ok := where["channelIdSli"]
	if ok {
		session = session.In("channel_id", channelIdSli)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}

	data := make([]ProProductChannel, 0)
	if err = session.Table("eshop.pro_product_channel").Find(&data); err != nil {
		return
	}

	outType := 0
	if v, ok := where["outType"]; ok {
		outType = v.(int)
	}

	for _, v := range data {
		switch outType {
		case 1:
			if _, ok := out1[v.ProductId]; !ok {
				out1[v.ProductId] = make(map[int]ProProductChannel)
			}
			out1[v.ProductId][v.ChannelId] = v
		default:
			if _, ok := out0[v.ProductId]; !ok {
				out0[v.ProductId] = make([]ProProductChannel, 0)
			}
			out0[v.ProductId] = append(out0[v.ProductId], v)
		}

	}

	return
}
