package points_vo

import (
	po "eShop/domain/points-po"
)

// ClsPointsGoodsSaveVO 创建积分商品时使用的VO
type ClsPointsGoodsSaveVO struct {
	SuperSaveVO[po.ClsPointsGoods]
	Name          string `json:"name" validate:"required,max=100"`
	Type          int    `json:"type" validate:"required,oneof=1 2"`    // 1-实物商品，2-虚拟商品
	SubType       int    `json:"sub_type" validate:"omitempty,oneof=1"` // 1-第三方商品 (如果Type是特定值才需要，或有默认值0)
	CostPrice     int    `json:"cost_price" validate:"required,gte=0"`
	MarketPrice   int    `json:"market_price" validate:"required,gte=0"`
	PointsPrice   int    `json:"points_price" validate:"required,gte=0"`
	Stock         int    `json:"stock" validate:"required,gte=0"`
	ImageUrl      string `json:"image_url" validate:"omitempty,url,max=255"`
	Description   string `json:"description"`
	ExchangeLimit int    `json:"exchange_limit"` // 0 代表没有限制
	StartTime     string `json:"start_time,omitempty"`
	EndTime       string `json:"end_time,omitempty"`
	Status        int    `json:"status" validate:"required,oneof=1 2"` // 1-上架，2-下架
}

// ClsPointsGoodsUpdateVO 更新积分商品时使用的VO
type ClsPointsGoodsUpdateVO struct {
	SuperUpdateVO[po.ClsPointsGoods]
	Id            int    `json:"id" validate:"required"`
	Name          string `json:"name" validate:"omitempty,max=100"`
	Type          int    `json:"type" validate:"omitempty,oneof=1 2"`
	SubType       int    `json:"sub_type" validate:"omitempty,oneof=1"`
	MarketPrice   int    `json:"market_price" validate:"omitempty,gte=0"`
	PointsPrice   int    `json:"points_price" validate:"omitempty,gte=0"`
	Stock         int    `json:"stock" validate:"omitempty,gte=0"`
	ImageUrl      string `json:"image_url" validate:"omitempty,url,max=255"`
	Description   string `json:"description"`
	ExchangeLimit int    `json:"exchange_limit" validate:"omitempty,gte=0"` // 0 代表没有限制
	StartTime     string `json:"start_time,omitempty"`
	EndTime       string `json:"end_time,omitempty"`
	Status        int    `json:"status" validate:"omitempty,oneof=1 2"`
}

// ClsPointsGoodsQueryVO 查询积分商品时使用的VO
type ClsPointsGoodsQueryVO struct {
	SuperQueryVO[po.ClsPointsGoods]
	Name      string `json:"name" query:"name:like"`
	Type      int    `json:"type" query:"type:eq"`
	Status    int    `json:"status" query:"status:eq"`
	Points    int    `json:"points" query:"points_price:lt"`
	OccurTime string `json:"occur_time" query:"start_time:lte;end_time:gte"`
}

// ClsPointsGoodsResultVO 返回积分商品结果时使用的VO
type ClsPointsGoodsResultVO struct {
	SuperResultVO[ClsPointsGoodsResultVO]
	Id            int    `json:"id"`
	Name          string `json:"name"`
	Type          int    `json:"type"`
	SubType       int    `json:"sub_type"`
	CostPrice     int    `json:"cost_price"`
	MarketPrice   int    `json:"market_price"`
	PointsPrice   int    `json:"points_price"`
	Stock         int    `json:"stock"`
	ImageUrl      string `json:"image_url"`
	Description   string `json:"description"`
	ExchangeLimit int    `json:"exchange_limit"` // 0 代表没有限制
	StartTime     string `json:"start_time"`
	EndTime       string `json:"end_time"`
	Status        int    `json:"status"`
	SaleCount     int    `json:"sale_count"` // 售出数
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"`
}
