package dto

// 普通属性列表
type ValueList struct {
	// 普通属性值Id
	ValueId int64 `json:"valueId,omitempty" form:"valueId" query:"valueId"`
	// 普通属性值名称
	Value string `json:"value" form:"value" query:"value"`
}

// 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
type CommonAttrValue struct {
	// 普通属性Id
	AttrId int64 `json:"attrId,omitempty,omitempty" form:"attrId" query:"attrId"`
	// 普通属性名称
	AttrName  string      `json:"attrName,omitempty" form:"attrName" query:"attrName"`
	ValueList []ValueList `json:"valueList,omitempty" form:"valueList" query:"valueList"`
}

// 表示sku可售时间 非必镇
type AvailableTimes struct {
	Monday    string `json:"monday,omitempty" form:"monday" query:"monday"`
	Tuesday   string `json:"tuesday,omitempty" form:"tuesday" query:"tuesday"`
	Wednesday string `json:"wednesday,omitempty" form:"wednesday" query:"wednesday"`
	Thursday  string `json:"thursday,omitempty" form:"thursday" query:"thursday"`
	Friday    string `json:"friday,omitempty" form:"friday" query:"friday"`
	Saturday  string `json:"saturday,omitempty" form:"saturday" query:"saturday"`
	Sunday    string `json:"sunday,omitempty" form:"sunday" query:"sunday"`
}

// 商品销售属性 非必镇
type OpenSaleAttrValue struct {
	//  销售属性id，不支持自定义
	AttrId int64 `json:"attrId,omitempty,omitempty" form:"attrId" query:"attrId"`
	// 销售属性值id。当属性值录入方式为文本时，该参数无需上传
	ValueId int64 `json:"valueId,omitempty,omitempty" form:"valueId" query:"valueId"`
	//  销售属性值，支持自定义
	Value string `json:"value,omitempty" form:"value" query:"value"`
}

// Sku信息 非必镇
type SkuParam struct {
	// 是sku唯一标识码 必镇
	Sku_id string `json:"sku_id" form:"sku_id" query:"sku_id"`
	// sku的规格名称  建时门店启用类目属性且skus中传递了销售属性则为非必填(会自动根据销售属性组合其规格)，其余情况参考字段描述里规则。 更新商品时，本参数非必填。
	Spec string `json:"spec" form:"spec" query:"spec"`
	// 为sku的商品包装上的条形码编号，UPC/EAN码；字符长度8位或者13位 非必镇
	Upc string `json:"upc" form:"upc" query:"upc"`
	// 为sku的价格  创建时必填
	Price string `json:"price" form:"price" query:"price"`
	// sku的库存量 创建时必填
	Stock string `json:"stock" form:"stock" query:"stock"`
	// 表示sku可售时间 非必镇
	Available_times AvailableTimes `json:"available_times" form:"available_times" query:"available_times"`
	// 表示sku的料位码 非必镇
	Location_code string `json:"location_code,omitempty" form:"location_code" query:"location_code"`
	// 包装费老计费规则，表示：商品sku单件需使用打包盒的数量 非必镇
	Box_num string `json:"box_num,omitempty" form:"box_num" query:"box_num"`
	//  包装费老计费规则，表示：商品sku单个打包盒的价格，单位是元，不能为负数 非必镇
	Box_price string `json:"box_price,omitempty" form:"box_price" query:"box_price"`
	// 包装费阶梯计价规则，表示：每M件商品收取N元包装费中的M  非必镇
	Ladder_box_num string `json:"ladder_box_num,omitempty" form:"ladder_box_num" query:"ladder_box_num"`
	// 包装费阶梯计价规则，表示：每M件商品收取N元包装费中的N。 非必镇
	Ladder_box_price string `json:"ladder_box_price,omitempty" form:"ladder_box_price" query:"ladder_box_price"`
	//  表示sku的重量   否，与weight_for_unit和weight_unit至多填写一个
	Weight int64 `json:"weight,omitempty" form:"weight" query:"weight"`
	// 表示sku的重量数值信息  创建时，如填写weight_unit，则weight_for_unit必填且与weight至多填写一个，否则非必填
	Weight_for_unit string `json:"weight_for_unit,omitempty" form:"weight_for_unit" query:"weight_for_unit"`
	// 表示sku的重量数值单位，枚举值如下： 1."克(g)" 2."千克(kg)" 3."毫升(ml)" 4."升(L)" 5."磅" 6."斤" 7."两"。
	// 创建时，如填写weight_for_unit，则weight_unit必填且与weight至多填写一个，否则非必填
	Weight_unit string `json:"weight_unit,omitempty" form:"weight_unit" query:"weight_unit"`
	// 商品销售属性 非必镇
	OpenSaleAttrValueList []OpenSaleAttrValue `json:"openSaleAttrValueList,omitempty" form:"openSaleAttrValueList" query:"openSaleAttrValueList"`
}

// 属性信息 非必镇
type Propertie struct {
	// 属性名称，字段信息限定长度不超过10个字符。最多支持传10组属性。不允许上传emoji等表情符  若有properties参数则必须填
	Property_name string `json:"property_name" form:"property_name" query:"property_name"`
	// 属性值 若有properties参数则必须填
	Values string `json:"values" form:"values" query:"values"`
}

// 限购信息  非必镇
type LimitSaleInfo struct {
	// 是否限制购买数量 必镇
	LimitSale bool `json:"limitSale" form:"limitSale" query:"limitSale"`
	// 限购规则： 1-限制下单顾客每X天限购数量，X为frequency，不传默认为1；2-限制整个周期内下单顾客限购数量 如限购开启则必填
	Type int32 `json:"type" form:"type" query:"type"`
	// 限购循环天数： 最大31，最小1。 非必镇
	Frequency int32 `json:"frequency,omitempty" form:"frequency" query:"frequency"`
	// 限购开始日期 如限购开启则必填
	Begin string `json:"begin" form:"begin" query:"begin"`
	// 限购数量  如限购开启则必填
	Count int32 `json:"count,omitempty" form:"count" query:"count"`
	// 限购结束日期 如限购开启则必填
	End string `json:"end" form:"end" query:"end"`
}

type RetailBatchinitdata struct {
	//APP方商品id，  必镇
	App_food_code string `json:"app_food_code" form:"app_food_code" query:"app_food_code"`
	// 单个商品需使用的打包盒数量  非必填
	Box_num float32 `json:"box_num,omitempty" form:"box_num" query:"box_num"`
	// 单个打包盒的价格，单位是元，需在0-100之间 非必填
	Box_price float32 `json:"box_price,omitempty" form:"box_price" query:"box_price"`
	//  分类名称    创建时：category_code与category_name、category_code_list、category_name_list字段必须且只能填写一个 更新时非必填
	Category_name string `json:"category_name" form:"category_name" query:"category_name"`
	// 商品上下架状态，字段取值范围：0-上架，1-下架。  非必镇
	Is_sold_out int32 `json:"is_sold_out" form:"is_sold_out" query:"is_sold_out"`
	// 一个订单中此商品的最小购买量   必填
	Min_order_count int32 `json:"min_order_count" form:"min_order_count" query:"min_order_count"`
	// 商品名称(总称：如电风机) 创建时必填
	Name string `json:"name" form:"name" query:"name"`
	// 商品描述  非必镇
	Description string `json:"description" form:"description" query:"description"`
	// 商品的售卖单位 非必镇
	Unit string `json:"unit" form:"unit" query:"unit"`
	// 商品图片  非必镇
	Picture string `json:"picture" form:"picture" query:"picture"`
	// 	 商品在当前分类下的排序  非必镇
	Sequence int32 `json:"sequence,omitempty" form:"sequence" query:"sequence"`
	// 商品价格 创建时，如不传skus信息，则本参数必填。
	Price float32 `json:"price,omitempty" form:"price" query:"price"`
	// 美团内部商品类目id 门店启用类目属性并且传递了销售或普通属性则  1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。 门店未启用类目属性或未传递销售属性/普通属性则非必传。
	Tag_id int64 `json:"tag_id,omitempty" form:"tag_id" query:"tag_id"`
	// 商品的品牌名称 非必镇
	Zh_name string `json:"zh_name" form:"zh_name" query:"zh_name"`
	// 完整产品名称（如：非力蒲电风机）
	Product_name string `json:"product_name" form:"product_name" query:"product_name"`
	// 商品的产地 非必镇
	Origin_name string `json:"origin_name" form:"origin_name" query:"origin_name"`
	//  功能 非必镇
	Flavour string `json:"flavour" form:"flavour" query:"flavour"`
	// 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
	Common_attr_value []CommonAttrValue `json:"common_attr_value" form:"common_attr_value" query:"common_attr_value"`
	// APP方商品的skus信息字符串，支持同时传多个sku信息  非必镇
	Skus []SkuParam `json:"skus" form:"skus" query:"skus"`
	// 商品的图片详情 非必镇
	Picture_contents string `json:"picture_contents" form:"picture_contents" query:"picture_contents"`
	// 商品属性 非必镇
	Properties []Propertie `json:"properties" form:"properties" query:"properties"`
	// 是否为“力荐”商品，字段取值范围：0-否， 1-是。  非必镇
	Is_specialty int32 `json:"is_specialty,omitempty" form:"is_specialty" query:"is_specialty"`
	// 视频ID 非必镇
	Video_id int64 `json:"video_id,omitempty" form:"video_id" query:"video_id"`
	// 商品限购详情  非必镇
	Limit_sale_info LimitSaleInfo `json:"limit_sale_info,omitempty" form:"limit_sale_info" query:"limit_sale_info"`
}

// retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑]    请求参数
type RetailBatchinitdataRequest struct {
	// APP方门店id，即商家中台系统里门店的编码 必镇
	App_poi_code string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	// 当次调用的操作类型 非必镇
	Operation int `json:"operation,omitempty" form:"operation" query:"operation"`
	// 多个商品数据集合的json格式数组  必镇
	Food_data []RetailBatchinitdata `json:"food_data" form:"food_data" query:"food_data"`
}
