package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type ClsPointsRuleTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.ClsPointsRuleQueryVO
	writer       *excelize.StreamWriter
	common.BaseService
}

func (e ClsPointsRuleTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	return
}

func (e ClsPointsRuleTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e ClsPointsRuleTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("宠利扫积分规则导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e ClsPointsRuleTask) OperationFunc(row []string, orgId int) string {
	// 错误信息
	var msg string

	if len(row) < 1 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}

	if len(row[0]) == 0 {
		msg = "商品Skuid不能为空"
		return msg
	}

	// 业务校验：1、重复商品导入 2、skuid不存在
	e.Begin()
	defer e.Close()

	// 将子上下文传入Session
	session := e.Session

	service := service.NewClsPointsRuleService()
	rule, err := service.Query(session, vo.ClsPointsRuleQueryVO{
		GoodsCode: row[0],
		Status:    []int{1},
	})
	if err != nil {
		msg = "商品积分规则获取失败"
		return msg
	}
	if rule.Id > 0 {
		msg = "商品积分规则已存在"
		return msg
	}

	// 获取商品信息
	var goodsSerial string
	_, err = session.Cols("goods_serial").Table("upetmart.upet_goods").Where("goods_id = ? AND store_id=4", row[0]).Get(&goodsSerial)
	if err != nil || len(goodsSerial) == 0 {
		msg = "skuid不存在"
		return msg
	}

	// 获取商品信息
	_, err = service.Create(session, vo.ClsPointsRuleSaveVO{
		GoodsCode: goodsSerial,
		Type:      1,
		StartTime: "0001-01-01 00:00:00",
		EndTime:   "9999-12-31 23:59:59",
	})
	if err != nil {
		msg = "商品积分规则创建失败"
		return msg
	}
	return ""
}
