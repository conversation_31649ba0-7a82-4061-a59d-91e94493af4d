package manager

import (
	"eShop/infra/utils"
	"eShop/services/omnibus-service/services"
	"eShop/view-model"
	"encoding/json"
	"net/http"
)

// 获取用户token
// @Summary 用户信息-获取token
// @Tags 宠物连锁SAAS-用户管理中心
// @Accept  json
// @Produce  json
// @Param EmptyReq query viewmodel.EmptyReq true " "
// @Success 200 {object} viewmodel.HttpResponse
// @Failure 400 {object} viewmodel.HttpResponse
// @Router /omnibus-app/user/token [Post]
func GetToken(writer http.ResponseWriter, r *http.Request) {
	out := viewmodel.HttpResponse{Code: 400}
	_, err := utils.Bind[viewmodel.EmptyReq](r)
	server := services.UserService{}
	if out.Data, err = server.GetToken(r); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
