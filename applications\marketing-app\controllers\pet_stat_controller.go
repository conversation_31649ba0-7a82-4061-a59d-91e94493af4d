package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/marketing-service/services"
	marketing_vo "eShop/view-model/marketing-vo"
	"net/http"

	"github.com/go-chi/chi/v5"
)

type PetStatController struct {
	service services.PetStatService
}

func NewPetStatController(service services.PetStatService) *PetStatController {
	return &PetStatController{service: service}
}

// @Summary 埋点上报
// @Tags 数据监测
// @Accept json
// @Produce json
// @Param command body marketing_vo.PetStatLogReq true "埋点请求"
// @Success 200 {object} marketing_vo.PetStatLogRes
// @Failure 400 {object} marketing_vo.PetStatLogRes
// @Router /marketing-app/awen/api/pet-stat/log [post]
func (c *PetStatController) AddPetStatLog(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[marketing_vo.PetStatLogReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}
	err = c.service.AddPetStatLog(&req)
	if err != nil {
		response.BadRequest(w, "埋点错误: "+err.Error())
		return
	}
	response.Success(w)
}

func (c *PetStatController) RegisterRoutes(r chi.Router) {
	r.Route("/marketing-app/awen/api/pet-stat", func(r chi.Router) {
		r.Post("/log", c.AddPetStatLog)
	})
	r.Route("/marketing-app/awen/manager/pet-stat", func(r chi.Router) {
		r.Post("/log", c.AddPetStatLog)
	})
}
