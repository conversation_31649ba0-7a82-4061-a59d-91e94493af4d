package external_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"errors"
	"time"

	"xorm.io/xorm"
)

// ShrTStaffInfo 员工基础信息表
type ShrTStaffInfo struct {
	Id                    int       `xorm:"not null pk autoincr 'id'" json:"id"`                                                  // 主键ID
	StaffNo               string    `xorm:"not null default '' varchar(64) 'staff_no'" json:"staff_no"`                           // (未维护)员工编号,值和shr_staff_no一致
	Name                  string    `xorm:"not null varchar(32) 'name'" json:"name"`                                              // 员工用户名
	RealName              string    `xorm:"not null default '' varchar(64) 'real_name'" json:"real_name"`                         // 真实姓名
	Gender                uint      `xorm:"not null default 0 'gender'" json:"gender"`                                            // 性别：0未知,1男,2女
	Email                 string    `xorm:"varchar(64) 'email'" json:"email"`                                                     // 员工邮箱
	Mobile                string    `xorm:"not null varchar(15) 'mobile'" json:"mobile"`                                          // 员工手机号
	Phone                 string    `xorm:"varchar(32) 'phone'" json:"phone"`                                                     // 员工座机号
	Wechat                string    `xorm:"varchar(32) 'wechat'" json:"wechat"`                                                   // 员工微信号
	Photo                 string    `xorm:"varchar(500) 'photo'" json:"photo"`                                                    // 员工照片
	Avatar                string    `xorm:"varchar(255) 'avatar'" json:"avatar"`                                                  // 用户头像
	Status                int       `xorm:"not null default 2 'status'" json:"status"`                                            // 员工状态：2在职 16离职
	EmplyeeStatus         int       `xorm:"not null default 3 'emplyee_status'" json:"emplyee_status"`                            // 员工状态详情：1实习,2试用期,3正式员工,4劳务,5劳务派遣,16辞职离职,17辞退离职,20其他
	EmplyeeStatusCn       string    `xorm:"varchar(100) 'emplyee_status_cn'" json:"emplyee_status_cn"`                            // 用工关系状态名称
	EmplyeeStatusCode     string    `xorm:"varchar(64) 'emplyee_status_code'" json:"emplyee_status_code"`                         // 用工关系状态编码
	IsChangePosition      int       `xorm:"not null default 0 'is_change_position'" json:"is_change_position"`                    // 是否调岗：1是,0否
	Comment               string    `xorm:"varchar(512) 'comment'" json:"comment"`                                                // 员工备注
	HavaVetNo             int       `xorm:"'hava_vet_no'" json:"hava_vet_no"`                                                     // 是否有执兽证：1是,0否
	VetIdNumber           string    `xorm:"varchar(255) 'vet_id_number'" json:"vet_id_number"`                                    // 兽医证编号
	ShrStaffStructId      string    `xorm:"varchar(64) 'shr_staff_struct_id'" json:"shr_staff_struct_id"`                         // SHR员工主任职组织id
	ShrStaffPositionId    string    `xorm:"varchar(64) 'shr_staff_position_id'" json:"shr_staff_position_id"`                     // SHR员工职位id
	EhrStaffId            string    `xorm:"varchar(64) 'ehr_staff_id'" json:"ehr_staff_id"`                                       // EHR用户查询编码
	ShrStaffId            string    `xorm:"not null default '' varchar(64) 'shr_staff_id'" json:"shr_staff_id"`                   // SHR用户查询ID
	ShrStaffNo            string    `xorm:"not null default '' varchar(64) 'shr_staff_no'" json:"shr_staff_no"`                   // SHR员工编码
	ShrStaffStructName    string    `xorm:"not null default '' varchar(64) 'shr_staff_struct_name'" json:"shr_staff_struct_name"` // SHR员工主任职组织名称
	ShrStaffStructCode    string    `xorm:"varchar(64) 'shr_staff_struct_code'" json:"shr_staff_struct_code"`                     // SHR员工主任职组织code
	ShrStaffPosition      string    `xorm:"not null default '' varchar(64) 'shr_staff_position'" json:"shr_staff_position"`       // SHR员工职位名称
	ShrStaffPositionCode  string    `xorm:"varchar(64) 'shr_staff_position_code'" json:"shr_staff_position_code"`                 // SHR员工职位编号
	ShrStaffGrade         string    `xorm:"varchar(64) 'shr_staff_grade'" json:"shr_staff_grade"`                                 // SHR员工职级名称
	ShrStaffGradeCode     string    `xorm:"varchar(64) 'shr_staff_grade_code'" json:"shr_staff_grade_code"`                       // SHR员工职级编号
	ShrStaffGradeRank     string    `xorm:"varchar(64) 'shr_staff_grade_rank'" json:"shr_staff_grade_rank"`                       // SHR员工职等名称
	ShrDutyCode           string    `xorm:"varchar(64) 'shr_duty_code'" json:"shr_duty_code"`                                     // SHR员工职务编号
	ShrDutyName           string    `xorm:"varchar(64) 'shr_duty_name'" json:"shr_duty_name"`                                     // SHR员工职务名称
	IsHide                int       `xorm:"not null default 0 'is_hide'" json:"is_hide"`                                          // (无用)员工多账号状态：1隐藏,0显示
	DirectLeader          string    `xorm:"varchar(64) 'direct_leader'" json:"direct_leader"`                                     // 直属领导SHRID
	DepartLeader          string    `xorm:"varchar(64) 'depart_leader'" json:"depart_leader"`                                     // 部门领导SHRID
	LineLeader            string    `xorm:"varchar(64) 'line_leader'" json:"line_leader"`                                         // 条线领导SHRID
	ShrLoginUser          string    `xorm:"varchar(64) 'shr_login_user'" json:"shr_login_user"`                                   // SHR系统登录用户名
	Birthday              time.Time `xorm:"date 'birthday'" json:"birthday"`                                                      // 出生日期
	IdCard                string    `xorm:"varchar(64) 'id_card'" json:"id_card"`                                                 // 身份证号
	EmployeeDay           time.Time `xorm:"date 'employee_day'" json:"employee_day"`                                              // 入职日期
	DimissionDay          time.Time `xorm:"date 'dimission_day'" json:"dimission_day"`                                            // 离职日期
	FormalEmployeeDay     time.Time `xorm:"date 'formal_employee_day'" json:"formal_employee_day"`                                // 转正日期
	StartWorkDay          time.Time `xorm:"date 'start_work_day'" json:"start_work_day"`                                          // 参加工作时间
	BankName              string    `xorm:"varchar(100) 'bank_name'" json:"bank_name"`                                            // 银行名称
	SubBankName           string    `xorm:"varchar(100) 'sub_bank_name'" json:"sub_bank_name"`                                    // 支行名称
	BankNo                string    `xorm:"varchar(64) 'bank_no'" json:"bank_no"`                                                 // 银行账号
	BankProvince          string    `xorm:"varchar(64) 'bank_province'" json:"bank_province"`                                     // 开户省
	BankCity              string    `xorm:"varchar(100) 'bank_city'" json:"bank_city"`                                            // 开户市
	GraduateSchool        string    `xorm:"varchar(100) 'graduate_school'" json:"graduate_school"`                                // 毕业学校
	GraduateDay           time.Time `xorm:"date 'graduate_day'" json:"graduate_day"`                                              // 毕业时间
	GraduateProfession    string    `xorm:"varchar(100) 'graduate_profession'" json:"graduate_profession"`                        // 毕业专业
	AttenceTypeCode       string    `xorm:"varchar(64) 'attence_type_code'" json:"attence_type_code"`                             // 办公地点编码
	AttenceTypeName       string    `xorm:"varchar(64) 'attence_type_name'" json:"attence_type_name"`                             // 办公地点名称
	IsSealManager         int       `xorm:"default 0 'is_seal_manager'" json:"is_seal_manager"`                                   // 是否管理印鉴：1是,0否
	IsCollectBakMoney     int       `xorm:"default 0 'is_collect_bak_money'" json:"is_collect_bak_money"`                         // 是否领用备用金：1是,0否
	IsCashier             int       `xorm:"default 0 'is_cashier'" json:"is_cashier"`                                             // 是否收银及每日对账人员：1是,0否
	HavaAccidentInsurance int       `xorm:"default 0 'hava_accident_insurance'" json:"hava_accident_insurance"`                   // 是否购买意外险：1是,0否
	CreateBy              string    `xorm:"varchar(64) 'create_by'" json:"create_by"`                                             // 创建人ID
	CreateTime            time.Time `xorm:"datetime 'create_time'" json:"create_time"`                                            // 创建时间
	UpdateBy              string    `xorm:"varchar(31) 'update_by'" json:"update_by"`                                             // 最后修改人ID
	UpdateTime            time.Time `xorm:"datetime 'update_time'" json:"update_time"`                                            // 最后更新时间
}

func (s *ShrTStaffInfo) TableName() string {
	return "dm_mdm.shr_t_staff_info"
}

// 根据电话号码判断 员工是否存在于SHR系统
func (s *ShrTStaffInfo) IsExistByMobile(session *xorm.Engine, mobile string) (bool, error) {
	starMobile := utils.AddStar(mobile)
	has, err := session.Table(s.TableName()).Where("(mobile = ? or mobile=?) and status = 2", starMobile, mobile).Get(s)
	if err != nil {
		return false, err
	}
	return has, nil
}

// 判断员工是否存在于SHR系统
func DisIsInSystem(session *xorm.Engine, mobile string, realName string, socialCreditCode string) (inSystem int8, err error) {
	if session == nil {
		err = errors.New("session不能为nil")
		return
	}
	if mobile == "" {
		err = errors.New("mobile不能为空")
		return
	}
	if realName == "" {
		err = errors.New("realName不能为空")
		return
	}
	// 如果传了社会信用代码， 则判断社会信用代码是否存在于SHR系统
	if len(socialCreditCode) > 0 {
		// 判断 社会信用代码
		inSystem, err = new(VCoBusiness).GetBySocialReditCode(session, socialCreditCode)
		if err != nil {
			log.Error("判断员工是否存在于SHR系统失败", "err", err.Error())
			err = errors.New("判断员工是否存在于SHR系统失败" + err.Error())
			return
		}
	} else {
		// 判断员工是否存在于SHR系统
		shrTStaffInfo := new(ShrTStaffInfo)
		if has, e := shrTStaffInfo.IsExistByMobile(session, mobile); e != nil {
			err = errors.New("判断员工是否存在于SHR系统失败" + e.Error())
			return
		} else if has {
			if shrTStaffInfo.RealName != realName {
				err = errors.New("您输入的姓名和体系内姓名不一致，请重新输入")
				return
			}
			inSystem = 1
		}
	}
	return
}
