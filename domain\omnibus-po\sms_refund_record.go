package omnibus_po

import (
	"time"

	"xorm.io/xorm"
)

// SmsRefundRecord 短信退款表
type SmsRefundRecord struct {
	// 主键ID
	Id int64 `json:"id" xorm:"pk autoincr 'id'"`
	// 连锁id
	ChainId int64 `json:"chain_id" xorm:"not null default 0 INT 'chain_id'"`
	// 店铺ID
	StoreId string `json:"store_id" xorm:"not null VARCHAR(50) 'store_id'"`
	// 店铺名称
	StoreName string `json:"store_name" xorm:"not null VARCHAR(100) 'store_name'"`
	// 订单编号
	OrderSn int64 `json:"order_sn" xorm:"not null BIGINT 'order_sn'"`
	// 退款条数
	RefundCount int `json:"refund_count" xorm:"not null default 0 INT 'refund_count'"`
	// 退款金额(分)
	RefundAmount int `json:"refund_amount" xorm:"not null default 0 INT 'refund_amount'"`
	// 申请时间
	ApplyTime time.Time `json:"apply_time" xorm:"not null DATETIME 'apply_time'"`
	// 退款时间
	RefundTime time.Time `json:"refund_time" xorm:"DATETIME 'refund_time'"`
	// 操作人
	Operator string `json:"operator" xorm:"VARCHAR(50) 'operator'"`
	// 退款状态(1-待审核,2-退款成功,3-退款拒绝,4-退款取消)
	RefundStatus int `json:"refund_status" xorm:"not null default 0 TINYINT 'refund_status'"`
	// 备注
	Remark string `json:"remark" xorm:"VARCHAR(255) 'remark'"`
	// 审核原因
	AuditReason string `json:"audit_reason" xorm:"VARCHAR(255) 'audit_reason'"`
	// 退款凭证图片(多张图片用逗号分隔)
	Images string `json:"images" xorm:"TEXT 'images'"`
	// 创建时间
	CreatedTime time.Time `json:"created_time" xorm:"created 'created_time'"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time" xorm:"updated 'updated_time'"`
}

func (r *SmsRefundRecord) TableName() string {
	return "eshop.sms_refund_record"
}

// Create 创建退款记录
func (r *SmsRefundRecord) Create(session *xorm.Session) error {
	_, err := session.Insert(r)
	return err
}

// Update 更新退款记录
func (r *SmsRefundRecord) Update(session *xorm.Session) error {
	_, err := session.ID(r.Id).Update(r)
	return err
}

// UpdateStatus 更新退款状态
func (r *SmsRefundRecord) UpdateStatus(session *xorm.Session, id int64, refundStatus int, operator string, remark string, auditReason string) error {

	updateFields := map[string]interface{}{
		"refund_status": refundStatus,
		"operator":      operator,
		"updated_time":  time.Now(),
	}

	if remark != "" {
		updateFields["remark"] = remark
	}

	if auditReason != "" {
		updateFields["audit_reason"] = auditReason
	}

	// 如果是退款成功状态，记录退款时间
	if refundStatus == 2 {
		updateFields["refund_time"] = time.Now()
	}

	_, err := session.Table(r.TableName()).Where("id = ?", id).Update(updateFields)
	return err
}

// GetByRefundSn 根据退款单号获取记录
func (r *SmsRefundRecord) GetByRefundSn(session *xorm.Session, refundSn string) (*SmsRefundRecord, error) {
	var record SmsRefundRecord
	has, err := session.Where("refund_sn = ?", refundSn).Get(&record)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &record, nil
}

// GetList 获取退款记录列表
func (r *SmsRefundRecord) GetList(session *xorm.Session, chainId int, storeId string, storeName string,
	refundSn string, orderSn string, refundStatus int, startApplyTime string, endApplyTime string,
	startRefundTime string, endRefundTime string, offset int, limit int) ([]SmsRefundRecord, int64, error) {

	query := session.Where("1=1")

	if chainId > 0 {
		query = query.And("chain_id = ?", chainId)
	}

	if storeName != "" {
		query = query.And("store_name LIKE ? or store_id = ?", "%"+storeName+"%", storeName)
	}

	if refundStatus > 0 {
		query = query.And("refund_status = ?", refundStatus)
	}
	if startApplyTime != "" && endApplyTime != "" {
		query = query.And("apply_time BETWEEN ? AND ?", startApplyTime, endApplyTime)
	}
	if startRefundTime != "" && endRefundTime != "" {
		query = query.And("refund_time BETWEEN ? AND ?", startRefundTime, endRefundTime)
	}

	var (
		total int64
		err   error
	)

	var records []SmsRefundRecord
	total, err = query.Limit(limit, offset).Desc("id").FindAndCount(&records)
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetById 根据id获取记录
func (r *SmsRefundRecord) GetById(session *xorm.Session, id int64) (*SmsRefundRecord, error) {
	var record SmsRefundRecord
	has, err := session.Where("id = ?", id).Get(&record)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &record, nil
}
