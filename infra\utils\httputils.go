package utils

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net"
	"net/http"
	"net/textproto"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"eShop/infra/config"
	"eShop/infra/log"

	"github.com/spf13/cast"
)

var HttpDefaultClient *http.Client
var HttpTransportClient *http.Client
var HttpTransportClientTimeout *http.Client

//var Client60Second *http.Client
//var Client30Second *http.Client

func init() {
	HttpDefaultClient = http.DefaultClient
	HttpTransportClient = &http.Client{Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	HttpTransportClientTimeout = &http.Client{Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		DialContext: (&net.Dialer{
			Timeout: 10 * time.Second,
		}).DialContext,
	}}

	//Client60Second = &http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	//Client30Second = &http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}

}

type HttpRequestParam struct {
	Data           string            //请求体数据
	HttpType       string            //请求类型 GET or POST
	Url            string            //请求url
	Headers        map[string]string //请求头设置
	TcpDailTimeOut time.Duration     //tcp链接超时
	RequestTimeOut time.Duration     //请求超时 包括建立链接 发送数据 服务器处理数据以及返回数据的整体时间 0表示不限制
}

// HttpCommonV2
// 通用http请求
func HttpCommonV2(param HttpRequestParam) ([]byte, error) {
	request := new(http.Request)
	var err error
	if param.HttpType == "POST" {
		byteData := bytes.NewReader([]byte(param.Data))
		request, err = http.NewRequest("POST", param.Url, byteData)
	} else {
		request, err = http.NewRequest("GET", param.Url, nil)
	}
	if err != nil {
		//此处返回err 不进行日志打印，日志的打印处理交给调用层
		return nil, err
	}
	if len(param.Headers) > 0 {
		for key, val := range param.Headers {
			request.Header.Set(key, val) //"application/json;charset=UTF-8"
		}
	}
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	//超时链接超时设置
	if param.TcpDailTimeOut > 0 {
		tr.DialContext = (&net.Dialer{
			Timeout: param.TcpDailTimeOut,
		}).DialContext
	}

	//client := http.Client{
	//	Transport: tr,
	//	Timeout:   param.RequestTimeOut, //请求超时时间
	//}
	HttpDefaultClient.Transport = tr
	HttpDefaultClient.Timeout = param.RequestTimeOut

	resp, err := HttpDefaultClient.Do(request)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		//关闭链接 避免泄露
		defer resp.Body.Close()
	}
	if resp.StatusCode != 200 {
		byteStr := `{"error":{"code":` + strconv.Itoa(resp.StatusCode) + `,"msg":"` + resp.Status + `"}}`
		return []byte(byteStr), errors.New("http请求失败")
	}
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return respBytes, nil
}

// post公共方法
func HttpPostCommon(url string, bytesData []byte, headers map[string]string) (respBytes []byte, err error) {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			log.Error("oms-order http post panic:", errPanic)
		}
	}()
	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		log.Info(err.Error())
		return nil, errors.New("创建请求出错:" + err.Error())
	}
	request.Header.Set("Content-Type", "application/json;charset=UTF-8")
	for k, v := range headers {
		request.Header.Set(k, v)
	}

	resp, err := HttpTransportClientTimeout.Do(request)

	if err != nil {
		log.Error("请求出错：" + url + ":" + err.Error())
		return nil, errors.New("请求出错:" + err.Error())
	}

	if resp != nil {
		defer resp.Body.Close()
	}
	respBytes, err = io.ReadAll(resp.Body)
	//状态码不对
	if resp.StatusCode != 200 {
		log.Error("请求出错："+url+"返回状态码："+cast.ToString(resp.StatusCode), ",body:", string(respBytes))
		return nil, errors.New("状态码不对,返回http状态码：" + cast.ToString(resp.StatusCode))
	}
	if err != nil {
		log.Error("读取返回数据出错" + url + ":" + err.Error())
		return nil, errors.New("读取返回数据出错:" + err.Error())
	}
	return respBytes, nil
}

// UploadFile 通用文件上传方法
func UploadOfflineFile(filePath string, headers map[string]string, formFields map[string]string) (string, error) {
	logPrefix := "上传文件===="

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		log.Error(logPrefix, "文件不存在:", filePath)
		return "", fmt.Errorf("文件不存在: %s", filePath)
	}

	// 读取文件内容
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		log.Error(logPrefix, "读取文件失败:", err.Error())
		return "", err
	}

	// 检查文件内容
	if len(fileContent) == 0 {
		log.Error(logPrefix, "文件内容为空")
		return "", fmt.Errorf("文件内容为空")
	}

	// 创建multipart form
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 创建文件表单字段
	h := make(textproto.MIMEHeader)
	h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="file"; filename="%s"`, filepath.Base(filePath)))
	h.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	part, err := writer.CreatePart(h)
	if err != nil {
		log.Error(logPrefix, "创建表单字段失败:", err.Error())
		return "", err
	}

	if _, err = part.Write(fileContent); err != nil {
		log.Error(logPrefix, "写入文件内容失败:", err.Error())
		return "", err
	}

	// 添加额外的表单字段
	for key, value := range formFields {
		if err = writer.WriteField(key, value); err != nil {
			log.Error(logPrefix, "添加表单字段失败:", err.Error())
			return "", err
		}
	}

	// 关闭writer
	if err = writer.Close(); err != nil {
		log.Error(logPrefix, "关闭writer失败:", err.Error())
		return "", err
	}
	uploadURL := config.Get("offline_url") + "/api/base/file/file-upload"
	// 创建请求
	req, err := http.NewRequest("POST", uploadURL, body)
	if err != nil {
		log.Error(logPrefix, "创建请求失败:", err.Error())
		return "", err
	}

	// 设置Content-Type
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 设置其他请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Error(logPrefix, "发送请求失败:", err.Error())
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Error(logPrefix, "读取响应失败:", err.Error())
		return "", err
	}

	// 解析响应
	var result struct {
		Code int    `json:"code"`
		Data string `json:"data"`
		Msg  string `json:"msg"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		log.Error(logPrefix, "解析响应失败:", err.Error())
		return "", err
	}

	if result.Code != 0 {
		log.Error(logPrefix, "上传失败:", result.Msg)
		return "", fmt.Errorf("上传失败: %s", result.Msg)
	}

	return result.Data, nil
}
