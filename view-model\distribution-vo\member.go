package distribution_vo

type Member struct {
	Id               int    `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`
	ScrmUserId       int    `json:"scrm_user_id" xorm:"not null comment('用户userid') INT 'scrm_user_id'"`
	OrgId            int    `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	MemberTruename   string `json:"member_truename" xorm:"default 'null' comment('真实姓名') VARCHAR(20) 'member_truename'"`
	WeixinUnionid    string `json:"weixin_unionid" xorm:"default 'null' comment('微信用户统一标识') VARCHAR(50) 'weixin_unionid'"`
	WeixinMiniOpenid string `json:"weixin_mini_openid" xorm:"default '' comment('小程序openid') VARCHAR(50) 'weixin_mini_openid'"`
}

//
//func (t *Member) Get() (*Member, error) {
//	ok, err := GlobalEngine.Get(t)
//	if err != nil {
//		err = fmt.Errorf("Member get Error:%v", err)
//		log.Error(err)
//		return nil, err
//	}
//	if ok {
//		return t, nil
//	}
//	return nil, errors.New("Member not exists")
//}
//
//func (t *Member) GetList() ([]*Member, error) {
//	var CodeInfo []*Member
//	var err error
//	err = GlobalEngine.Find(&CodeInfo, t)
//	if err != nil {
//		err = fmt.Errorf("Member get list Error:%v", err)
//		log.Error(err)
//		return nil, err
//	}
//	return CodeInfo, err
//}
//
//func (t *Member) Delete() (err error) {
//	affected, err := GlobalEngine.Delete(t)
//	if err != nil {
//		err = fmt.Errorf("Member delete Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	log.Infof("Member delete id:%v, affect:%v", t.Id, affected)
//	return nil
//}
//
//func (t *Member) DeleteWithSession(session *xorm.Session) (err error) {
//	affected, err := session.Delete(t)
//	if err != nil {
//		err = fmt.Errorf("Member delete Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	log.Infof("Member delete id:%v, affect:%v", t.Id, affected)
//	return nil
//}
//
//func (t *Member) Insert() (err error) {
//	_, err = GlobalEngine.Insert(t)
//	if err != nil {
//		err = fmt.Errorf("Member insert Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) InsertWithSession(session *xorm.Session) (err error) {
//	_, err = session.Insert(t)
//	if err != nil {
//		err = fmt.Errorf("Member insert Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) Modify() (err error) {
//	if t.Id == 0 {
//		err = errors.New("Member modify Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//	_, err = GlobalEngine.ID(t.Id).Update(t)
//	if err != nil {
//		err = fmt.Errorf("Member modify Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) ModifyWithSession(session *xorm.Session) (err error) {
//	if t.Id == 0 {
//		err = errors.New("Member modify with session Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//	_, err = session.ID(t.Id).Update(t)
//	if err != nil {
//		err = fmt.Errorf("Member modify with session Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) SetScrmUserId(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("Member set ScrmUserId Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(Member)
//	tp.ScrmUserId = val
//	_, err = GlobalEngine.ID(t.Id).Cols("scrm_user_id").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("Member set ScrmUserId Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) SetOrgId(val int) (err error) {
//	if t.Id == 0 {
//		err = errors.New("Member set OrgId Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(Member)
//	tp.OrgId = val
//	_, err = GlobalEngine.ID(t.Id).Cols("org_id").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("Member set OrgId Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) SetMemberTruename(val string) (err error) {
//	if t.Id == 0 {
//		err = errors.New("Member set MemberTruename Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(Member)
//	tp.MemberTruename = val
//	_, err = GlobalEngine.ID(t.Id).Cols("member_truename").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("Member set MemberTruename Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) SetWeixinUnionid(val string) (err error) {
//	if t.Id == 0 {
//		err = errors.New("Member set WeixinUnionid Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(Member)
//	tp.WeixinUnionid = val
//	_, err = GlobalEngine.ID(t.Id).Cols("weixin_unionid").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("Member set WeixinUnionid Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
//
//func (t *Member) SetWeixinMiniOpenid(val string) (err error) {
//	if t.Id == 0 {
//		err = errors.New("Member set WeixinMiniOpenid Error, pk is nil")
//		log.Error(err)
//		return err
//	}
//
//	tp := new(Member)
//	tp.WeixinMiniOpenid = val
//	_, err = GlobalEngine.ID(t.Id).Cols("weixin_mini_openid").Update(tp)
//	if err != nil {
//		err = fmt.Errorf("Member set WeixinMiniOpenid Error:%v", err)
//		log.Error(err)
//		return err
//	}
//	return nil
//}
