package enum

// 定义任务内容
const (
	SyncProductAddTaskContent = 1 //同步创建第三方商品任务（包括线下门店端、美团、饿了么、京东到家）

	SyncProductEditTaskContent = 2 //同步更新第三方商品任务（包括线下门店端、美团、饿了么、京东到家）

	SyncProductDelTaskContent = 3 //同步删除第三方商品任务（包括线下门店端、美团、饿了么、京东到家）

	SyncCategoryTaskContent = 4 //同步分类任务

	SyncUpProductTaskContent = 5 //批量上架第三方商品任务（包括线下门店端、美团、饿了么、京东到家）

	SyncDownProductTaskContent = 6 //批量下架第三方商品任务（包括线下门店端、美团、饿了么、京东到家）

	SyncPriceProductTaskContent = 7 //批量调价第三方商品任务（包括线下门店端、美团、饿了么、京东到家）

	SyncCreateProductTaskContent = 8 //批量铺品第三方商品任务（包括线下门店端、美团、饿了么、京东到家）

	SyncXkucunCodeDateTaskContent = 9 //批量写入物流码（深圳利都）

	SyncPetArtworkTaskContent = 10 //贵族裂变活动 - AI生成贵族宠物图任务(极宠家)

)

// 上面加的任务内容要写入到下面，不然不会有MQ监听
var (
	InitQueue = []int{
		SyncProductAddTaskContent,
		SyncProductEditTaskContent,
		SyncProductDelTaskContent,
		SyncCategoryTaskContent,
		SyncUpProductTaskContent,
		SyncDownProductTaskContent,
		SyncPriceProductTaskContent,
		SyncCreateProductTaskContent,
		SyncXkucunCodeDateTaskContent,
		SyncPetArtworkTaskContent,
	}
)

var SyncTaskContentMap = map[int]string{
	SyncProductAddTaskContent:     "同步创建第三方商品任务",
	SyncProductEditTaskContent:    "同步更新第三方商品任务",
	SyncProductDelTaskContent:     "同步删除第三方商品任务",
	SyncCategoryTaskContent:       "同步分类任务",
	SyncUpProductTaskContent:      "批量上架第三方商品任务",
	SyncDownProductTaskContent:    "批量下架第三方商品任务",
	SyncPriceProductTaskContent:   "批量调价第三方商品任务",
	SyncCreateProductTaskContent:  "批量铺品第三方商品任务",
	SyncXkucunCodeDateTaskContent: "批量写入物流码（深圳利都）",
	SyncPetArtworkTaskContent:     "贵族裂变活动 - AI生成贵族宠物图任务(极宠家)",
}
