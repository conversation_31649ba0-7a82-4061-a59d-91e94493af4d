package inventory

import (
	"context"
	po "eShop/domain/inventory-po/inventory"
	ioBoundPO "eShop/domain/inventory-po/iobound"
	"eShop/infra/errors"
	jwt "eShop/infra/jwtauth"
	"eShop/services/common"
	vo "eShop/view-model/inventory-vo/inventory"
	"fmt"
	"strings"

	"github.com/samber/lo"
	"xorm.io/xorm"
)

type InventoryService struct {
	common.BaseService
}

func NewInventoryService() *InventoryService {
	return &InventoryService{}
}

func (s InventoryService) InventoryManage(ctx context.Context, session *xorm.Session, inventoryChange *vo.InventoryChange) ([]po.InventoryFlow, error) {
	skuIds := lo.Uniq(lo.Filter(lo.Map(inventoryChange.SkuInventoryChanges, func(change vo.SkuInventoryChanges, _ int) int {
		return change.SkuId
	}), func(skuId int, _ int) bool {
		return skuId != 0
	}))

	if len(skuIds) == 0 {
		return nil, errors.NewBadRequest("变动商品不存在")
	}

	// 使用充血模型，创建库存模型，调用模型内查询方法
	inventories, err := new(po.Inventory).ListBySkuIds(ctx, session, inventoryChange.WarehouseId, skuIds)
	if err != nil || len(inventories) == 0 {
		return nil, errors.NewBadRequest("变动商品不存在")
	}

	switch 2 - (inventoryChange.ItemType & 1) {
	case 1:
		return inbound(ctx, session, inventories, inventoryChange)
	case 2:
		return outbound(ctx, session, inventories, inventoryChange)
	default:
		return nil, errors.NewBadRequest("未知出入库类型")
	}
}

func inbound(ctx context.Context, session *xorm.Session, inventories []po.Inventory, inventoryChange *vo.InventoryChange) ([]po.InventoryFlow, error) {
	result := make([]po.InventoryFlow, 0)
	inventoryMap := make(map[int]*po.Inventory, len(inventories))
	for i := range inventories {
		inventoryMap[inventories[i].SkuId] = &inventories[i]
	}
	// skuFlowMap, err := new(po.InventoryFlow).GetSkuHasFlow(ctx, session, lo.Keys(inventoryMap))
	// if err != nil {
	// 	return nil, err
	// }

	// 1. 先检查冻结库存是否小于解冻库存
	err := inBoundCheck(inventoryMap, inventoryChange)
	if err != nil {
		return nil, err
	}

	// 2. 入库计算 - 使用返回值
	result, err = ioBoundCalc(ctx, inventoryMap, inventoryChange, result)
	if err != nil {
		return nil, err
	}

	// 3. 保存结果
	err = new(po.Inventory).BatchUpdate(ctx, session, inventories)
	if err != nil {
		return nil, err
	}
	err = new(po.InventoryFlow).BatchInsert(ctx, session, result)
	if err != nil {
		return nil, err
	}

	// TODO 商品缓存中关于库存字段的更新操作

	// 4. 发送库存变更消息
	// sendChangeMessage(result)

	// TODO 5. 发送库存预警信息
	// sendInventoryWarningMessage(changeEventDTO, skuFlowMap, new(po.InventoryFlow).GetSkuFirstSalesOverMonth(lo.Keys(inventoryMap)), inventories, result)

	return result, nil
}

func inBoundCheck(inventoryMap map[int]*po.Inventory, inventoryChange *vo.InventoryChange) error {
	//单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 12.自用出库
	if inventoryChange.ItemType != 3 {
		priceEmpties := []string{}
		for _, change := range inventoryChange.SkuInventoryChanges {
			opInventory := inventoryMap[change.SkuId]
			if change.Price <= 0 {
				if len(opInventory.ProductName) > 0 {
					priceEmpties = append(priceEmpties, opInventory.ProductName)
				} else {
					priceEmpties = append(priceEmpties, change.ProductName)
				}
			}
		}

		if len(priceEmpties) > 0 && (inventoryChange.ItemType == 1 || inventoryChange.ItemType == 9 || inventoryChange.ItemType == 11) {
			return errors.NewBadRequest("商品: " + strings.Join(priceEmpties, ",") + " 价格为空或者小于0.01元")
		}
	} else {
		shortages := []string{}
		for _, change := range inventoryChange.SkuInventoryChanges {
			opInventory := inventoryMap[change.SkuId]
			if opInventory.FreezeNum < change.ChangeNum {
				if len(opInventory.ProductName) > 0 {
					shortages = append(shortages, opInventory.ProductName)
				} else {
					shortages = append(shortages, change.ProductName)
				}
			}
		}
		if len(shortages) > 0 {
			return errors.NewBadRequest("商品: " + strings.Join(shortages, ",") + " 冻结库存数小于解冻库存数")
		}
	}
	return nil
}

func ioBoundCalc(ctx context.Context, inventoryMap map[int]*po.Inventory, inventoryChange *vo.InventoryChange, result []po.InventoryFlow) ([]po.InventoryFlow, error) {
	// 1. 出入库
	for _, change := range inventoryChange.SkuInventoryChanges {
		inventory := inventoryMap[change.SkuId]
		// 1.1 计算
		calcResult, err := new(ioBoundPO.IoBoundDetail).CalcInventoryChange(inventoryChange.ItemType, *inventory, change)
		if err != nil {
			return nil, err
		}

		// 添加调试日志
		fmt.Printf("SkuId: %d, calcResult.FreezeNumAfter: %d\n", change.SkuId, calcResult.FreezeNumAfter)

		// 1.2 设置出库流水
		result = append(result, po.InventoryFlow{
			ChainId:            jwt.CtxGet[int64](ctx, "ChainId"),
			StoreId:            jwt.CtxGet[string](ctx, "TenantId"),
			WarehouseId:        inventoryChange.WarehouseId,
			ProductId:          inventory.ProductId,
			SkuId:              inventory.SkuId,
			BoundType:          2 - (inventoryChange.ItemType & 1),
			ItemType:           inventoryChange.ItemType,
			ItemRefId:          inventoryChange.ItemRefId,
			ItemRefNo:          inventoryChange.ItemRefNo,
			ItemRefType:        inventoryChange.ItemRefType,
			ChangeNum:          change.ChangeNum,
			CurrentCostPrice:   calcResult.CurrentCostPrice,
			ChangeAmount:       calcResult.ChangeAmount,
			TotalNumBefore:     calcResult.TotalNumBefore,
			FreezeNumBefore:    calcResult.FreezeNumBefore,
			AvailableNumBefore: calcResult.AvailableNumBefore,
			TotalAmountBefore:  calcResult.TotalAmountBefore,
			TotalNumAfter:      calcResult.TotalNumAfter,
			FreezeNumAfter:     calcResult.FreezeNumAfter,
			AvailableNumAfter:  calcResult.AvailableNumAfter,
			TotalAmountAfter:   calcResult.TotalAmountAfter,
			IsDeleted:          0,
			Operator:           inventoryChange.Operator,
			Remark:             inventoryChange.Remark,
		})

		// 1.3 计算出库后结果，直接修改指针指向的值
		inventory.TotalNum = calcResult.TotalNumAfter
		inventory.FreezeNum = calcResult.FreezeNumAfter
		inventory.AvailableNum = calcResult.AvailableNumAfter
		inventory.TotalAmount = calcResult.TotalAmountAfter
		inventory.AvgCostPrice = calcResult.AvgCostPriceAfter
	}

	return result, nil
}

func outbound(ctx context.Context, session *xorm.Session, inventories []po.Inventory, inventoryChange *vo.InventoryChange) ([]po.InventoryFlow, error) {
	result := make([]po.InventoryFlow, 0)
	inventoryMap := make(map[int]*po.Inventory, len(inventories))
	for i := range inventories {
		inventoryMap[inventories[i].SkuId] = &inventories[i]
	}

	// 1. 先检查可用库存是否足够
	err := outBoundCheck(inventoryMap, inventoryChange)
	if err != nil {
		return nil, err
	}

	// 2. 出库计算 - 使用返回值
	result, err = ioBoundCalc(ctx, inventoryMap, inventoryChange, result)
	if err != nil {
		return nil, err
	}

	// 3. 保存结果
	err = new(po.Inventory).BatchUpdate(ctx, session, inventories)
	if err != nil {
		return nil, err
	}
	err = new(po.InventoryFlow).BatchInsert(ctx, session, result)
	if err != nil {
		return nil, err
	}

	// TODO 商品缓存中关于库存字段的更新操作

	// 4. 发送库存变更消息
	// sendChangeMessage(result)

	// TODO 5. 发送库存预警
	// sendInventoryWarningMessage(inventoryChange, inventoryMap, new(po.InventoryFlow).GetSkuFirstSalesOverMonth(lo.Keys(inventoryMap)), inventories, result)

	return result, nil
}

func outBoundCheck(inventoryMap map[int]*po.Inventory, inventoryChange *vo.InventoryChange) error {
	if inventoryChange.ItemType == 6 {
		var numErrorProducts []string
		for _, change := range inventoryChange.SkuInventoryChanges {
			opInventory := inventoryMap[change.SkuId]
			if opInventory.FreezeNum < change.ChangeNum {
				numErrorProducts = append(numErrorProducts, change.ProductName)
			}
		}
		if len(numErrorProducts) > 0 {
			return errors.NewBadRequest("商品: " + strings.Join(numErrorProducts, ",") + " 冻结库存少于当前实际出库库存")
		}
	} else {
		var shortages []string
		for _, change := range inventoryChange.SkuInventoryChanges {
			opInventory := inventoryMap[change.SkuId]
			if opInventory.AvailableNum < change.ChangeNum {
				if len(opInventory.ProductName) > 0 {
					shortages = append(shortages, opInventory.ProductName)
				} else {
					shortages = append(shortages, change.ProductName)
				}
			}
		}
		if len(shortages) > 0 {
			return errors.NewBadRequest("商品: " + strings.Join(shortages, ",") + " 可用库存不足")
		}
	}
	return nil
}

func (s InventoryService) Page(ctx context.Context, cmd vo.InventoryPageRequest) ([]vo.InventoryResponse, int64, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	inventories, total, err := new(po.Inventory).Page(ctx, session, cmd)
	if err != nil {
		return nil, 0, err
	}

	if len(inventories) > 0 {
		storeId := jwt.CtxGet[string](ctx, "TenantId")
		for i, inventory := range inventories {
			inventories[i].ProductSku, err = new(po.Inventory).GetProductBySkuId(ctx, session, storeId, inventory.SkuId)
			if err != nil {
				return nil, 0, err
			}
		}
	}

	return inventories, total, nil
}

func (s InventoryService) BatchCreate(ctx context.Context, cmd vo.InventoryInitCommand) error {
	s.Begin()
	session := s.Session
	defer s.Close()

	// 创建库存记录
	_, err := new(po.Inventory).BatchCreate(ctx, session, cmd)
	if err != nil {
		return err
	}

	return nil
}

func (s InventoryService) FlowPage(ctx context.Context, cmd vo.InventoryFlowPageRequest) ([]vo.InventoryFlowResponse, int64, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	inventories, total, err := new(po.InventoryFlow).Page(ctx, session, cmd)
	if err != nil {
		return nil, 0, err
	}

	return inventories, total, nil
}

// 缺货预警分页
func (s InventoryService) WarningPage(ctx context.Context, req vo.InventoryWarningRequest) ([]vo.InventoryWarningResponse, int64, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	// 查询预警阈值
	var warningQty int
	_, err := session.SQL("SELECT parameter FROM eshop_saas.sys_switch_setting WHERE CODE='INVENTORY_WARNING_CONFIG' AND option_value='INVENTORY_QTY'").Get(&warningQty)
	if err != nil || warningQty == 0 {
		warningQty = 5 // 默认5
	}

	return new(po.Inventory).WarningPage(ctx, session, req, warningQty)
}
