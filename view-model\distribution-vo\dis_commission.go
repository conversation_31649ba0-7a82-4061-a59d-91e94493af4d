package distribution_vo

import viewmodel "eShop/view-model"

type GetDisCommissionInfoReq struct {
	viewmodel.BasePageHttpRequest
	OrgId    int `json:"org_id" `   //所属主体id
	ShopId   int `json:"shop_id"`   //所属店铺id
	MemberId int `json:"member_id"` //分销员在电商表的upet_member表的member_id
	// 是否显示百林康源医生订单
	IsShowBLKYDoctorOrder bool `json:"is_show_blky_doctor_order"`
}
type GetShopCommInfo struct {
	Id                     int64 `json:"id"`                                                                                  //分销店铺id
	SettledCommission      int   `json:"settled_commission" xorm:"default 0 comment('已结佣金(分)') INT 'settled_commission'"`     //已结佣金(分)
	UnsettledCommission    int   `json:"unsettled_commission" xorm:"default 0 comment('未结佣金(分)') INT 'unsettled_commission'"` //未结佣金(分)
	InsSettledCommission   int   `json:"ins_settled_commission"`                                                              //保险已结佣金(分)
	InsUnsettledCommission int   `json:"ins_unsettled_commission"`                                                            //保险未结佣金(分)

	WithdrawSuccess int `json:"withdraw_success" xorm:"default 0 comment('提现成功(分)') INT 'withdraw_success'"` //提现成功(分)
	WithdrawApply   int `json:"withdraw_apply" xorm:"default 0 comment('提现申请(分)') INT 'withdraw_apply'"`     //提现申请(分)
	WaitWithdraw    int `json:"wait_withdraw" xorm:"default 0 comment('待提现(分)') INT 'wait_withdraw'"`        //待提现(分)
}
type GetShopCommInfoResp struct {
	viewmodel.BasePageHttpResponse
	Data ShopCommInfo `json:"data"`
}
type GetDisCommissionInfoRes struct {
	viewmodel.BasePageHttpResponse
	Data GetDisCommissionList `json:"data"`
}

type ShopCommInfoResp struct {
	viewmodel.BasePageHttpResponse
	Data ShopCommInfo `json:"data"`
}
type ShopCommInfo struct {
	Dis             DisDistributor       `json:"dis"`               //分销员信息
	ShopComm        GetShopCommInfo      `json:"shop_comm"`         //润合云店店铺提现金额统计信息 或者是 宠利扫老板角色提现金额统计信息
	Detail          []GetShopCommListRes `json:"detail"`            //润合云店店铺佣金明细 或者是 宠利扫老板所在店铺佣金明细
	DisTotal        DisDistributorTotal  `json:"dis_total"`         // 分销员维度提现金额
	WithdrawList    []DisWithdrawView    `json:"withdraw_list"`     //提现记录
	WithdrawTaxRate string               `json:"withdraw_tax_rate"` //提现税率
}

type GetDisCommissionList struct {
	Dis      DisDistributor      `json:"dis"`
	DisTotal DisDistributorTotal `json:"dis_total"`
	Wid      []DisWithdrawView   `json:"wid"`
}

type DisCommissionApplyReq struct {
	OrgId          int    `json:"org_id"`           //所属主体id
	MemberId       int    `json:"member_id"`        //分销员在电商表的upet_member表的member_id
	AccountName    string `json:"account_name"`     //开户姓名
	BankName       string `json:"bank_name"`        //收款银行
	BankAccount    string `json:"bank_account"`     //收款账号
	WithdrawIdCard string `json:"withdraw_id_card"` //提现身份证号码
	BankBranch     string `json:"bank_branch"`      //收款支行
	Amount         int    `json:"amount"`           //提现金额（单位分）
	BankMobile     string `json:"bank_mobile"`      //收款人手机号
	// 是否提现曾经作为百林康源医生角色的可提现金额
	IsWithdrawBLKYDoctorMoney bool `json:"is_withdraw_blky_doctor_money"`
	// 可提现订单号(多个订单号用逗号隔开) - 宠利扫老板提现
	OrderNo string `json:"order_no"`
	// 申请全部可提现订单 - 宠利扫老板提现
	IsApplyAllOrder bool `json:"is_apply_all_order"`
	// 最大订单id - 宠利扫老板提现
	MaxOrderId int `json:"max_order_id"`
	// 全部全选 时订单总条数 - 宠利扫老板提现
	AllOrderCnt int `json:"all_order_cnt"`
	// 店铺id - 宠利扫老板提现
	ShopId int `json:"shop_id"`
	// 分销员id
	DisId int `json:"dis_id"`
}

type DisCommissionApplyRes struct {
	viewmodel.BaseHttpResponse
	OperateType int `json:"operate_type"`
}
type GetShopCommListReq struct {
	OrgId  int `json:"org_id"`  //主体id
	ShopId int `json:"shop_id"` //分销店铺id

}

type GetShopCommListRes struct {
	DisRole                int    `json:"dis_role"`                 //分销员角色( 0-初始值 1-老板 2-店员 3-医生)
	DistributorName        string `json:"distributor_name"`         //分销员名称"`
	SettledCommission      int    `json:"settled_commission"`       //已结算佣金(单位分)
	UnsettledCommission    int    `json:"unsettled_commission"`     //未结算佣金(单位分)
	InsSettledCommission   int    `json:"ins_settled_commission"`   //保险已结佣金(分)
	InsUnsettledCommission int    `json:"ins_unsettled_commission"` //保险未结佣金(分)
}
