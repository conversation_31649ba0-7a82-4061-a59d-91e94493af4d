package services

import (
	blky_po "eShop/domain/blky-po"
	"eShop/infra/log"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
)

type ClsVerifyService struct {
	common.BaseService
}

func (s *ClsVerifyService) GetClsVerifyPage(req vo.ClsVerifyPageReq) ([]vo.ClsVerifyPageData, int, error) {
	s.<PERSON>gin()
	defer s.Close()

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	verifyRecord := &blky_po.VerifyRecord{}
	var records []blky_po.VerifyRecordExt
	var total int64
	var err error

	// 根据查询类型调用不同的领域方法
	if req.DataType == 1 {
		records, total, err = verifyRecord.GetVerifyStats(
			s.Engine,
			req.CompanyName,
			req.Code,
			req.PageIndex,
			req.PageSize,
		)
	} else {
		records, total, err = verifyRecord.GetVerifyDetails(
			s.Engine,
			req.CompanyName,
			req.Code,
			req.PageIndex,
			req.PageSize,
		)
	}

	if err != nil {
		log.Error("查询防伪码记录失败:", err.Error())
		return nil, 0, err
	}

	// 转换为VO
	result := make([]vo.ClsVerifyPageData, 0, len(records))
	for _, record := range records {
		sourceName := ""
		if record.Source == 1 {
			sourceName = "首页-扫一扫"
		} else if record.Source == 2 {
			sourceName = "微信-扫一扫"
		} else if record.Source == 3 {
			sourceName = "个人中心-查验真伪"
		}
		data := vo.ClsVerifyPageData{
			Id:               record.Id,
			CompanyName:      record.CompanyName,
			Code:             record.Code,
			VerifyTime:       record.VerifyTime.Format("2006-01-02 15:04:05"),
			UserOpenid:       record.UserOpenid,
			UserId:           record.UserId,
			UserPhone:        record.UserPhone,
			VerifyType:       record.VerifyType,
			VerifyDesc:       sourceName,
			VerifyLocation:   record.VerifyLocation,
			VerifyEntry:      record.VerifyEntry,
			VerifyCount:      record.VerifyCount,
			EncryptUserPhone: record.EncryptUserPhone,
		}
		result = append(result, data)
	}

	return result, int(total), nil
}
