package utils

import (
	"context"
	"eShop/infra/config"
	"eShop/infra/log"
	"fmt"

	"github.com/olivere/elastic/v7"
)

var (
	IndexChannelStoreProduct = "channel_store_product" //商品
)

//哪个项目需要调用，需要在配置系统对应的项目，添加es相关的配置

func NewEsClient(url_name_pwd ...string) *elastic.Client {
	var esUrl, esUname, esPwd string
	if len(url_name_pwd) == 3 {
		esUrl = url_name_pwd[0]
		esUname = url_name_pwd[1]
		esPwd = url_name_pwd[2]
	} else {
		esUrl = config.Get("elasticsearch")
		esUname = config.Get("elasticsearch.LoginName")
		esPwd = config.Get("elasticsearch.Password")
	}

	//// 测试es uat环境的配置
	// esUrl = "http://es-cn-m7r1uawz10006espz.public.elasticsearch.aliyuncs.com:9200"
	// esUname = "elastic"
	// esPwd = "dk3aOf6U"

	// esUrl = "http://172.30.1.47:9200"
	// esUname = "elastic"
	// esPwd = "x1SiSj8KN4s8j6FK"

	if es, err := elastic.NewClient(
		elastic.SetSniff(false),
		elastic.SetHealthcheck(false),
		elastic.SetURL(esUrl),
		elastic.SetBasicAuth(esUname, esPwd),
	); err != nil {

		log.Error("es ============= client error: ", err)
		return nil
	} else {
		log.Info("es ============= client successful")
		return es
	}
}

func IndexExists(client *elastic.Client, indexName string) (bool, error) {
	ctx := context.Background()

	exists, err := client.IndexExists(indexName).Do(ctx)
	if err != nil {
		return false, err
	}

	return exists, nil
}

func CreateESIndex(orgId string) error {

	indexName := "channel_store_product" + orgId
	client := NewEsClient()

	exists, err := IndexExists(client, indexName)
	if err != nil {
		log.Fatalf("Error checking if index exists: %v", err)
	}

	//如果索引已经存在了。就不创建了
	if exists {
		return nil
	}

	settings := `{ "settings" : {
  "index" : {
	"search" : {
	  "slowlog" : {
		"level" : "info",
		"threshold" : {
		  "fetch" : {
			"warn" : "200ms",
			"trace" : "50ms",
			"debug" : "80ms",
			"info" : "100ms"
		  },
		  "query" : {
			"warn" : "500ms",
			"trace" : "50ms",
			"debug" : "100ms",
			"info" : "200ms"
		  }
		}
	  }
	},
	"refresh_interval" : "10s",
	"indexing" : {
	  "slowlog" : {
		"level" : "info",
		"threshold" : {
		  "index" : {
			"warn" : "200ms",
			"trace" : "20ms",
			"debug" : "50ms",
			"info" : "100ms"
		  }
		},
		"source" : "1000"
	  }
	},
	"number_of_shards" : "1",
	"unassigned" : {
	  "node_left" : {
		"delayed_timeout" : "5m"
	  }
	},
	"analysis" : {
	  "filter" : {
		"edge_ngram_filter" : {
		  "type" : "edge_ngram",
		  "min_gram" : "1",
		  "max_gram" : "30"
		},
		"my_pinyin" : {
		  "keep_joined_full_pinyin" : "false",
		  "keep_separate_first_letter" : "false",
		  "none_chinese_pinyin_tokenize" : "true",
		  "type" : "pinyin",
		  "keep_none_chinese" : "true",
		  "keep_none_chinese_together" : "true"
		}
	  },
	  "char_filter" : {
		"split_by_whitespace_filter" : {
		  "pattern" : "(.+?)",
		  "type" : "pattern_replace",
		  "replacement" : "$1 "
		}
	  },
	  "analyzer" : {
		"onlyone_analyzer" : {
		  "tokenizer" : "onlyone_pinyin"
		},
		"edge_ngra_analyzer" : {
		  "filter" : [
			"edge_ngram_filter",
			"lowercase"
		  ],
		  "type" : "custom",
		  "tokenizer" : "keyword"
		},
		"ik_max_word_analyzer" : {
		  "type" : "custom",
		  "tokenizer" : "ik_max_word"
		},
		"ik_pinyin_analyzer" : {
		  "filter" : [
			"my_pinyin"
		  ],
		  "type" : "custom",
		  "tokenizer" : "ik_smart"
		},
		"ik_smart_analyzer" : {
		  "type" : "custom",
		  "tokenizer" : "ik_smart"
		},
		"char_analyzer" : {
		  "char_filter" : [
			"split_by_whitespace_filter"
		  ],
		  "tokenizer" : "whitespace"
		}
	  },
	  "tokenizer" : {
		"onlyone_pinyin" : {
		  "type" : "pinyin",
		  "keep_separate_first_letter" : "false",
		  "keep_full_pinyin" : "false"
		}
	  }
	}
  }
}
, "mappings":{
	"properties" : {
	"cities" : {
	  "type" : "text",
	  "fields" : {
		"keyword" : {
		  "type" : "keyword",
		  "ignore_above" : 256
		}
	  }
	},
	"finance_code" : {
	  "type" : "text",
	  "fields" : {
		"keyword" : {
		  "type" : "keyword",
		  "ignore_above" : 256
		}
	  }
	},
	"has_stock_new" : {
	  "type" : "long"
	},
	"product" : {
	  "properties" : {
		"channel_category_id" : {
		  "type" : "long"
		},
		"channel_category_name" : {
		  "type": "text",
		  "analyzer": "ik_max_word",
		  "search_analyzer": "ik_smart"
		},
		"channel_id" : {
		  "type" : "text",
		  "fields" : {
			"keyword" : {
			  "type" : "keyword",
			  "ignore_above" : 256
			}
		  }
		},
		"id" : {
		  "type" : "long"
		},
                "is_vip":{"type":"long"},
		"is_virtual" : {
		  "type" : "long"
		},
		"name" : {
		  "type" : "text",
		  "fields" : {
                "EDGE" : {
                  "type" : "text",
                  "analyzer" : "edge_ngra_analyzer"
                },
                "IKM" : {
                  "type" : "text",
                  "analyzer" : "ik_max_word_analyzer"
                },
                "IKPY" : {
                  "type" : "text",
                  "analyzer" : "ik_pinyin_analyzer"
                },
                "IKS" : {
                  "type" : "text",
                  "analyzer" : "ik_smart_analyzer"
                }
              },
              "analyzer" : "char_analyzer"
		},
		"pic" : {
		  "type" : "text",
		  "fields" : {
			"keyword" : {
			  "type" : "keyword",
			  "ignore_above" : 256
			}
		  }
		},
		"product_type" : {
		  "type" : "long"
		},
		"selling_point" : {
		  "type": "text",
		  "analyzer": "ik_max_word",
		  "search_analyzer": "ik_smart"
		},
		"short_name" : {
		  "type" : "text",
		  "analyzer": "ik_max_word",
		  "search_analyzer": "ik_smart"
		},
		"term_type" : {
		  "type" : "long"
		},
		"term_value" : {
		  "type" : "long"
		},
		"virtual_invalid_refund" : {
		  "type" : "long"
		}
	  }
	},
	"region_id" : {
	  "type" : "long"
	},
	"sku_info" : {
	  "properties" : {
		"is_pre_sale" : {
		  "type" : "long"
		},
		"market_price" : {
		  "type" : "long"
		},
		"member_price" : {
		  "type" : "long"
		},
		"pre_sale_price" : {
		  "type" : "long"
		},
		"promotion_price" : {
		  "type" : "long"
		},
		"promotion_type" : {
		  "type" : "long"
		},
		"retail_price" : {
		  "type" : "long"
		},
		"sales_volume" : {
		  "type" : "long"
		},
		"sku_group" : {
		  "properties" : {
			"channel_id" : {
			  "type" : "long"
			},
			"count" : {
			  "type" : "long"
			},
			"discount_price" : {
			  "type" : "long"
			},
			"discount_type" : {
			  "type" : "long"
			},
			"group_product_id" : {
			  "type" : "long"
			},
			"group_sku_id" : {
			  "type" : "long"
			},
			"market_price" : {
			  "type" : "long"
			},
			"product_id" : {
			  "type" : "long"
			},
			"product_name" : {
			  "type" : "text",
			  "fields" : {
				"keyword" : {
				  "type" : "keyword",
				  "ignore_above" : 256
				}
			  }
			},
			"product_type" : {
			  "type" : "long"
			},
			"sku_id" : {
			  "type" : "long"
			}
		  }
		},
		"sku_id" : {
		  "type" : "long"
		},
		"skuv" : {
		  "properties" : {
			"spec_name" : {
			  "type" : "text",
			  "fields" : {
				"keyword" : {
				  "type" : "keyword",
				  "ignore_above" : 256
				}
			  }
			},
			"spec_value_value" : {
			  "type": "text",
			  "analyzer": "ik_max_word",
			  "search_analyzer": "ik_smart"
			}
		  }
		},
		"weight_for_unit" : {
		  "type" : "long"
		}
	  }
	},
	"tags" : {
	  "type": "text",
	  "analyzer": "ik_max_word",
	  "search_analyzer": "ik_smart"
	},
	"update_date" : {
	  "type" : "text",
	  "fields" : {
		"keyword" : {
		  "type" : "keyword",
		  "ignore_above" : 256
		}
	  }
	},
	"warehouse_category" : {
	  "type" : "long"
	},
	"warehouse_id" : {
	  "type" : "long"
	}
  }
	}
	}`

	err = CreateIndexWithMappingAndSettings(client, indexName, settings)
	if err != nil {
		log.Error("Error creating index with mapping and settings: ", err.Error())
		return err
	}

	return nil

}

func CreateESIndexByName(indexName string) error {

	//indexName := "channel_store_product" + orgId
	client := NewEsClient()

	exists, err := IndexExists(client, indexName)
	if err != nil {
		log.Fatalf("Error checking if index exists: %v", err)
	}

	//如果索引已经存在了。就不创建了
	if exists {
		return nil
	}

	settings := `{ "settings" : {
  "index" : {
	"search" : {
	  "slowlog" : {
		"level" : "info",
		"threshold" : {
		  "fetch" : {
			"warn" : "200ms",
			"trace" : "50ms",
			"debug" : "80ms",
			"info" : "100ms"
		  },
		  "query" : {
			"warn" : "500ms",
			"trace" : "50ms",
			"debug" : "100ms",
			"info" : "200ms"
		  }
		}
	  }
	},
	"refresh_interval" : "10s",
	"indexing" : {
	  "slowlog" : {
		"level" : "info",
		"threshold" : {
		  "index" : {
			"warn" : "200ms",
			"trace" : "20ms",
			"debug" : "50ms",
			"info" : "100ms"
		  }
		},
		"source" : "1000"
	  }
	},
	"number_of_shards" : "1",
	"unassigned" : {
	  "node_left" : {
		"delayed_timeout" : "5m"
	  }
	},
	"analysis" : {
	  "filter" : {
		"edge_ngram_filter" : {
		  "type" : "edge_ngram",
		  "min_gram" : "1",
		  "max_gram" : "30"
		},
		"my_pinyin" : {
		  "keep_joined_full_pinyin" : "false",
		  "keep_separate_first_letter" : "false",
		  "none_chinese_pinyin_tokenize" : "true",
		  "type" : "pinyin",
		  "keep_none_chinese" : "true",
		  "keep_none_chinese_together" : "true"
		}
	  },
	  "char_filter" : {
		"split_by_whitespace_filter" : {
		  "pattern" : "(.+?)",
		  "type" : "pattern_replace",
		  "replacement" : "$1 "
		}
	  },
	  "analyzer" : {
		"onlyone_analyzer" : {
		  "tokenizer" : "onlyone_pinyin"
		},
		"edge_ngra_analyzer" : {
		  "filter" : [
			"edge_ngram_filter",
			"lowercase"
		  ],
		  "type" : "custom",
		  "tokenizer" : "keyword"
		},
		"ik_max_word_analyzer" : {
		  "type" : "custom",
		  "tokenizer" : "ik_max_word"
		},
		"ik_pinyin_analyzer" : {
		  "filter" : [
			"my_pinyin"
		  ],
		  "type" : "custom",
		  "tokenizer" : "ik_smart"
		},
		"ik_smart_analyzer" : {
		  "type" : "custom",
		  "tokenizer" : "ik_smart"
		},
		"char_analyzer" : {
		  "char_filter" : [
			"split_by_whitespace_filter"
		  ],
		  "tokenizer" : "whitespace"
		}
	  },
	  "tokenizer" : {
		"onlyone_pinyin" : {
		  "type" : "pinyin",
		  "keep_separate_first_letter" : "false",
		  "keep_full_pinyin" : "false"
		}
	  }
	}
  }
}
,  "mappings": {
      "properties": {
        "address": {
          "type": "text",
          "analyzer": "ik_max_word",
          "search_analyzer": "ik_smart"

        },
        "bigregion": {
          "type": "keyword"
        },
        "chain_id": {
          "type": "long"
        },
        "city": {
          "type": "keyword"
        },
        "cityid": {
          "type": "long"
        },
        "county": {
          "type": "keyword"
        },
        "countyid": {
          "type": "long"
        },
        "create_time": {
          "type": "date"
        },
        "desc": {
          "type": "keyword"
        },
        "finance_code": {
          "type": "keyword"
        },
        "id": {
          "type": "long"
        },
        "location_point": {
         "type": "geo_point"
        },
        "name": {
         "type": "text",
          "analyzer": "ik_max_word",
          "search_analyzer": "ik_smart"
        },
        "org_id": {
          "type": "long"
        },
        "pointX": {
          "type": "keyword"
        },
        "pointY": {
          "type": "keyword"
        },
        "province": {
          "type": "keyword"
        },
        "provinceid": {
          "type": "long"
        },
        "shortname": {
          "type": "keyword"
        },
        "storeCode": {
          "type": "keyword"
        },
        "update_time": {
          "type": "date"
        },
        "zilong_id": {
          "type": "keyword"
        }
      }
    }
	}`

	err = CreateIndexWithMappingAndSettings(client, indexName, settings)
	if err != nil {
		log.Error("Error creating index with mapping and settings: ", err.Error())
		return err
	}

	return nil

}

func updateMapping(client *elastic.Client, indexName string, mapping string) error {
	ctx := context.Background()

	// 更新映射
	updateMapping, err := client.PutMapping().Index(indexName).BodyString(mapping).Do(ctx)
	if err != nil {
		return err
	}
	if updateMapping.Acknowledged {
		fmt.Printf("Mapping for index %s updated.\n", indexName)
	}

	return nil
}

func CreateIndexWithMappingAndSettings(client *elastic.Client, indexName string, settings string) error {
	ctx := context.Background()
	// 创建索引
	createIndex, err := client.CreateIndex(indexName).BodyString(settings).Do(ctx)
	if err != nil {
		return err
	}
	if createIndex.Acknowledged {
		fmt.Printf("Index %s created.\n", indexName)
	}

	return nil
}
