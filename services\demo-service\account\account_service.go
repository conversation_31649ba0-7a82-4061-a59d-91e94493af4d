package account

import (
	"context"
	"eShop/domain/demo-po/account"
	"eShop/services/common"
	"time"
)

type AccountService struct {
	common.BaseService
}

// NewService 创建账户服务
func NewAccountService() *AccountService {
	return &AccountService{}
}

// Create 创建账户
func (s AccountService) Create(ctx context.Context, cmd CreateCommand) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()

	acc := account.NewAccount(cmd.ID, cmd.Balance)
	if err := acc.Save(ctx, session); err != nil {
		session.Rollback()
		return err
	}

	// 创建事件
	// event := &account.AccountCreatedEvent{
	// 	BaseEvent: events.BaseEvent{
	// 		EventType:   account.EventTypeAccountCreated,
	// 		AggregateID: acc.ID,
	// 		Timestamp:   time.Now(),
	// 		Version:     acc.Version,
	// 	},
	// 	Balance: acc.Balance,
	// }

	// 保存事件
	// if err := s.EventStore.SaveEvent(event); err != nil {
	// 	session.Rollback()
	// 	return err
	// }
	return session.Commit()
}

// Transfer 转账
func (s *AccountService) Transfer(ctx context.Context, cmd TransferCommand) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()

	if err := session.Begin(); err != nil {
		return err
	}

	from, err := account.FindByID(ctx, cmd.FromID, session)
	if err != nil {
		session.Rollback()
		return err
	}

	to, err := account.FindByID(ctx, cmd.ToID, session)
	if err != nil {
		session.Rollback()
		return err
	}

	if err := from.Transfer(to, cmd.Amount); err != nil {
		session.Rollback()
		return err
	}

	if err := from.Update(ctx, session); err != nil {
		session.Rollback()
		return err
	}

	if err := to.Update(ctx, session); err != nil {
		session.Rollback()
		return err
	}

	// 创建转账事件
	// event := &account.AccountTransferredEvent{
	// 	BaseEvent: events.BaseEvent{
	// 		EventType:   account.EventTypeAccountTransferred,
	// 		AggregateID: from.ID,
	// 		Timestamp:   time.Now(),
	// 		Version:     from.Version,
	// 	},
	// 	FromID:  from.ID,
	// 	ToID:    to.ID,
	// 	Amount:  cmd.Amount,
	// 	Balance: from.Balance,
	// }

	// 保存事件
	// if err := s.EventStore.SaveEvent(event); err != nil {
	// 	session.Rollback()
	// 	return err
	// }
	return session.Commit()
}

// Withdraw 提现
func (s *AccountService) Withdraw(ctx context.Context, cmd WithdrawCommand) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()

	if err := session.Begin(); err != nil {
		return err
	}

	acc, err := account.FindByID(ctx, cmd.ID, session)
	if err != nil {
		session.Rollback()
		return err
	}

	if err := acc.Withdraw(cmd.Amount); err != nil {
		session.Rollback()
		return err
	}

	if err := acc.Update(ctx, session); err != nil {
		session.Rollback()
		return err
	}

	// 创建提现事件
	// event := &account.AccountWithdrawnEvent{
	// 	BaseEvent: events.BaseEvent{
	// 		EventType:   account.EventTypeAccountWithdrawn,
	// 		AggregateID: acc.ID,
	// 		Timestamp:   time.Now(),
	// 		Version:     acc.Version,
	// 	},
	// 	Amount:  cmd.Amount,
	// 	Balance: acc.Balance,
	// }

	// // 保存事件
	// if err := s.EventStore.SaveEvent(event); err != nil {
	// 	session.Rollback()
	// 	return err
	// }

	return session.Commit()
}

// Deposit 存款
func (s *AccountService) Deposit(ctx context.Context, cmd DepositCommand) error {
	s.Begin()
	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	acc, err := account.FindByID(ctx, cmd.ID, session)
	if err != nil {
		session.Rollback()
		return err
	}

	if err := acc.Deposit(cmd.Amount); err != nil {
		session.Rollback()
		return err
	}

	if err := acc.Update(ctx, session); err != nil {
		session.Rollback()
		return err
	}

	// 创建存款事件
	// event := &account.AccountDepositedEvent{
	// 	BaseEvent: events.BaseEvent{
	// 		EventType:   account.EventTypeAccountDeposited,
	// 		AggregateID: acc.ID,
	// 		Timestamp:   time.Now(),
	// 		Version:     acc.Version,
	// 	},
	// 	Amount:  cmd.Amount,
	// 	Balance: acc.Balance,
	// }

	// // 保存事件
	// if err := s.EventStore.SaveEvent(event); err != nil {
	// 	session.Rollback()
	// 	return err
	// }

	return session.Commit()
}

// Get 获取账户
func (s *AccountService) Get(ctx context.Context, id string) (*account.Account, error) {
	return account.FindByID(ctx, id, nil)
}

// Query 查询账户列表
func (s *AccountService) Query(ctx context.Context, params QueryParams) (*QueryResult, error) {
	s.Begin()
	session := s.Engine.NewSession()
	defer session.Close()

	if params.Status != "" {
		session.Where("status = ?", params.Status)
	}
	if params.StartTime > 0 {
		session.Where("created_at > ?", time.Unix(params.StartTime, 0))
	}

	// Count total before pagination
	total, err := session.Count(&account.Account{})
	if err != nil {
		return nil, err
	}

	// Apply pagination
	if params.PageNumber > 0 && params.PageSize > 0 {
		offset := (params.PageNumber - 1) * params.PageSize
		session.Limit(params.PageSize, offset)
	}

	var items []*account.Account
	err = session.Find(&items)
	if err != nil {
		return nil, err
	}

	return &QueryResult{
		Items: items,
		Total: total,
	}, nil
}
