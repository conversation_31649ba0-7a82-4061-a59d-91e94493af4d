package services

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"github.com/spf13/cast"
	"time"
)

type StatsSkuCityDailyService struct {
	common.BaseService
}

// GetStatsSkuCityDailyByDate 查询商品时间范围统计,分页查询
func (s StatsSkuCityDailyService) GetStatsSkuCityDailyByDate(req vo.StatsSkuCityDailyPageReq) ([]vo.StatsSkuCityDaily, int, error) {
	s.<PERSON>gin()
	defer s.Close()
	db := s.Engine
	if len(req.StartDate) == 0 || len(req.EndDate) == 0 { //默认30天
		req.StartDate, req.EndDate = utils.GetDate(30)
	}
	startTimestamp := utils.Date2Timestamp(req.StartDate)
	targetTime, err := time.Parse("2006-01-02", req.EndDate)
	targetTime = targetTime.AddDate(0, 0, 1)
	endTimestamp := utils.Date2Timestamp(targetTime.Format("2006-01-02"))

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	pageIndex := (req.PageIndex - 1) * req.PageSize
	pageSize := req.PageSize

	var stats = make([]vo.StatsSkuCityDaily, 0)
	session := db.Table("upetmart.upet_orders").Alias("o").
		Select(`
	og.goods_id AS skuid,
	og.order_id,
	og.goods_name AS product_name,
	COUNT(DISTINCT o.buyer_id) AS order_customer_count,
	SUM(og.goods_num) AS order_quantity,
	ROUND(SUM(og.goods_pay_price * 100)) AS order_amount,
	COUNT(DISTINCT CASE WHEN o.payment_time > 0 THEN o.buyer_id END) AS deal_customer_count,
	SUM(CASE WHEN o.payment_time > 0 THEN og.goods_num ELSE 0 END) AS deal_quantity,
	ROUND(SUM(CASE WHEN o.payment_time > 0 THEN og.goods_pay_price * 100 ELSE 0 END)) AS deal_amount,
	COUNT(DISTINCT CASE WHEN o.is_dis = 1 AND o.payment_time > 0 THEN o.buyer_id END) AS dist_deal_customer_count,
	SUM(CASE WHEN o.is_dis = 1 AND o.payment_time > 0 THEN og.goods_num ELSE 0 END) AS dist_deal_quantity,
	ROUND(SUM(CASE
		 WHEN o.is_dis = 1 AND o.payment_time > 0 THEN og.goods_pay_price * 100
		 ELSE 0 END)) AS dist_deal_amount
	`).
		Join("left", "upetmart.upet_order_goods og", "o.order_id = og.order_id").
		Where("o.store_id = 3").
		Where("o.add_time BETWEEN ? AND ?", startTimestamp, endTimestamp).
		Where("o.order_father = 0")

	if req.ProductName != "" {
		session.Where("og.goods_id = ? or og.goods_name like ?", req.ProductName, "%"+req.ProductName+"%")
	}
	total, err := session.GroupBy("og.goods_id").Limit(pageSize, pageIndex).FindAndCount(&stats)

	if err != nil {
		log.Errorf("GetStatsSkuCityDailyByDate error:%v", err)
		return stats, 0, err
	}

	return stats, cast.ToInt(total), nil
}

// GetStatsSkuCityDailyBySkuid 查询某个商品在某个时间范围每个城市的统计
func (s StatsSkuCityDailyService) GetStatsSkuCityDailyBySkuid(req vo.StatsSkuCityDailyPageReq) ([]vo.StatsSkuCityDaily, int, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine
	if req.Skuid == 0 {
		return nil, 0, errors.New("skuid is required")
	}
	if len(req.StartDate) == 0 || len(req.EndDate) == 0 { //默认30天
		req.StartDate, req.EndDate = utils.GetDate(30)
	}
	startTimestamp := utils.Date2Timestamp(req.StartDate)
	targetTime, err := time.Parse("2006-01-02", req.EndDate)
	targetTime = targetTime.AddDate(0, 0, 1)
	endTimestamp := utils.Date2Timestamp(targetTime.Format("2006-01-02"))
	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	pageIndex := (req.PageIndex - 1) * req.PageSize
	pageSize := req.PageSize
	stats := make([]vo.StatsSkuCityDaily, 0)
	total, err := session.Table("upetmart.upet_order_goods").Alias("og").
		Select(`
og.goods_id AS skuid,
og.goods_name AS product_name,
pa.area_name AS province,
ca.area_name AS city,
COUNT(DISTINCT o.buyer_id) AS order_customer_count,
SUM(og.goods_num) AS order_quantity,
ROUND(SUM(og.goods_pay_price * 100)) AS order_amount,
COUNT(DISTINCT CASE WHEN o.payment_time > 0 THEN o.buyer_id END) AS deal_customer_count,
SUM(CASE WHEN o.payment_time > 0 THEN og.goods_num ELSE 0 END) AS deal_quantity,
ROUND(SUM(CASE WHEN o.payment_time > 0 THEN og.goods_pay_price * 100 ELSE 0 END)) AS deal_amount,
COUNT(DISTINCT CASE WHEN o.is_dis = 1 AND o.payment_time > 0 THEN o.buyer_id END) AS dist_deal_customer_count,
SUM(CASE WHEN o.is_dis = 1 AND o.payment_time > 0 THEN og.goods_num ELSE 0 END) AS dist_deal_quantity,
ROUND(SUM(CASE WHEN o.is_dis = 1 AND o.payment_time > 0 THEN og.goods_pay_price * 100 ELSE 0 END)) AS dist_deal_amount
`).
		Join("left", "upetmart.upet_orders o", "og.order_id = o.order_id").
		Join("left", "upetmart.upet_order_common oc", "oc.order_id = o.order_id").
		Join("left", "upetmart.upet_area pa", "oc.reciver_province_id = pa.area_id").
		Join("left", "upetmart.upet_area ca", "oc.reciver_city_id = ca.area_id").
		Where("o.store_id = 3 and o.add_time BETWEEN ? AND ?", startTimestamp, endTimestamp).
		And("og.goods_id = ?", req.Skuid).And("o.order_father=0").
		GroupBy("oc.reciver_province_id, oc.reciver_city_id").
		Limit(pageSize, pageIndex).
		FindAndCount(&stats)
	if err != nil {
		log.Errorf("GetStatsSkuCityDailyByDateAndSkuid error:%v", err)
		return stats, 0, err
	}
	return stats, cast.ToInt(total), nil
}
