package distribution_vo

import (
	"eShop/view-model"
)

type GetKanbanOverviewReq struct {
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
}
type GetKanbanOverviewResp struct {
	viewmodel.BaseHttpResponse
	Data GetKanbanOverview `json:"data"`
}
type GetKanbanOrderResp struct {
	viewmodel.BaseHttpResponse
	Data GetKanbanOrder `json:"data"`
}
type GetKanbanOrder struct {
	// 商品订单数据概览
	OrderData struct {
		// 下单数据
		SubmitOrder SubmitOrder `json:"submit_order"`
		// 成交数据
		TransOrder TransOrder `json:"trans_order"`
		// 取消/退款数据
		CancelRefundOrder CancelRefundOrder `json:"cancel_refund_order"`
	} `json:"order_data" comment:"商品订单概览"`
	// 商品分销订单数据概览
	DisOrderData struct {
		// 下单数据
		SubmitOrder SubmitOrder `json:"submit_order"`
		// 成交数据
		TransOrder TransOrder `json:"trans_order"`
		//分销新增来源数据
		DisSource DisSource `json:"dis_source"`
		// 分销企业数据
		StatsEntTransView StatsEntTransView `json:"stats_ent_trans_view"`
		// 分销佣金
		DisCommission DisCommission `json:"dis_commission"`
	} `json:"dis_order_data" comment:"商品分销订单概览"`
}
type GetKanbanInsOrderResp struct {
	viewmodel.BaseHttpResponse
	Data GetKanbanInsOrder `json:"data"`
}
type GetKanbanInsOrder struct {
	// 商品订单数据概览
	OrderData struct {
		// 下单数据
		SubmitOrder SubmitOrder `json:"submit_order"`
		// 成交数据
		TransOrder TransOrder `json:"trans_order"`
		// 取消/退款数据
		CancelRefundOrder CancelRefundOrder `json:"cancel_refund_order"`
	} `json:"order_data" comment:"商品订单概览"`
	// 商品分销订单数据概览
	DisOrderData struct {
		// 下单数据
		SubmitOrder SubmitOrder `json:"submit_order"`
		// 成交数据
		TransOrder TransOrder `json:"trans_order"`
		//分销新增来源数据
		DisSource DisSource `json:"dis_source"`
		// 分销企业数据
		StatsEntInsTransView StatsEntInsTransView `json:"stats_ent_ins_trans_view"`
		// 分销佣金
		DisCommission DisCommission `json:"dis_commission"`
	} `json:"dis_order_data" comment:"商品分销订单概览"`
}
type GetKanbanOverview struct {
	// 数据总览-业务员数据、数据总览-分销企业数据
	SalesmanAndEnt StatsEntView `json:"salesman_and_ent"`
	// 数据总览-订单数据概览
	OrderData struct {
		// 下单数据
		SubmitOrder SubmitOrder `json:"submit_order"`
		// 成交数据
		TransOrder TransOrder `json:"trans_order"`
	} `json:"order_data" comment:"商品订单概览"`
	// 数据总览-分销订单数据概览
	DisOrderData struct {
		// 下单数据
		SubmitOrder SubmitOrder `json:"submit_order"`
		// 成交数据
		TransOrder TransOrder `json:"trans_order"`
	} `json:"dis_order_data" comment:"商品分销订单概览"`
	// 数据总览-佣金概览
	Commission Commission `json:"commission"`
}

type StatsDisCenterOverviewReq struct {
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
	//分销员用户id(前端不用传)
	MemberId int `json:"member_id"`
	// flag=0 代表的是分销员中心-店员， flag=1 代表的是分销员中心-老板
	Flag int `json:"flag"`
}
type StatsDisCenterOverview struct {
	// 商品数据和保险数据之和
	GoodsSumIns GoodsSumIns `json:"goods_sum_ins"`
	// 商品数据
	GoodsData StatsShopDistributorDailyForApi `json:"goods_data"`
	// 保险数据
	InsData StatsShopDistributorDailyForApi `json:"ins_data"`
}

type GoodsSumIns struct {
	// 新增成交单数
	TransCount int `json:"trans_count" comment:"新增成交单数"`
	// 新增成交金额
	TransAmount int `json:"trans_amount" comment:"新增成交金额"`
	// 新增成交客户数
	TransCustomerCount int `json:"trans_customer_count" comment:"新增成交客户数"`

	// 新增成交单数 较上一周期百分比
	TransCountPercent string `json:"trans_count_percent" comment:"新增成交单数 较上一周期百分比"`
	// 新增成交金额 较上一周期百分比
	TransAmountPercent string `json:"trans_amount_percent" comment:"新增成交金额 较上一周期百分比"`
	// 新增成交客户数 较上一周期百分比
	TransCustomerCountPercent string `json:"trans_customer_count_percent" comment:"新增成交客户数 较上一周期百分比"`

	// 新增分销佣金
	Commission int `json:"commission" comment:"新增分销佣金"`
	// 新增未结佣金
	UnsettledCommission int `json:"unsettled_commission" comment:"新增待结佣金"`
	// 新增已结佣金
	SettledCommission int `json:"settled_commission" comment:"新增已结佣金"`
	// 新增分销佣金 较上一周期百分比
	CommissionPercent string `json:"commission_percent" comment:"新增分销佣金"`
	// 新增未结佣金 较上一周期百分比
	UnsettledCommissionPercent string `json:"unsettled_commission_percent" comment:"新增待结佣金"`
	// 新增已结佣金 较上一周期百分比
	SettledCommissionPercent string `json:"settled_commission_percent" comment:"新增已结佣金"`

	// 分销海报成交单数
	PosterTransCount int `json:"poster_trans_count" comment:"分销海报成交单数"`
	// 分销海报成交金额(分)
	PosterTransAmount int `json:"poster_trans_amount" comment:"分销海报成交金额(分)"`
	// 分销海报成交单数   较上一周期百分比
	PosterTransCountPercent string `json:"poster_trans_count_percent" comment:"分销海报成交单数  较上一周期百分比"`
	// 分销海报成交金额(分)   较上一周期百分比
	PosterTransAmountPercent string `json:"poster_trans_amount_percent" comment:"分销海报成交金额 较上一周期百分比"`

	// 分销链接成交单数
	LinkTransCount int `json:"link_trans_count" comment:"分销链接成交单数"`
	// 分销链接成交金额
	LinkTransAmount int `json:"link_trans_amount" comment:"分销链接成交金额(分)"`
	// 分销链接成交单数 较上一周期百分比
	LinkTransCountPercent string `json:"link_trans_count_percent" comment:"分销链接成交单数 较上一周期百分比"`
	// 分销链接成交金额 较上一周期百分比
	LinkTransAmountPercent string `json:"link_trans_amount_percent" comment:"分销链接成交金额较上一周期百分比"`

	// 粉丝关系成交单数
	FanRelationTransCount int `json:"fan_relation_trans_count" comment:"粉丝关系成交单数"`
	// 粉丝关系成交金额
	FanRelationTransAmount int `json:"fan_relation_trans_amount" comment:"粉丝关系成交金额"`
	// 粉丝关系成交单数  较上一周期百分比
	FanRelationTransCountPercent string `json:"fan_relation_trans_count_percent" comment:"粉丝关系成交单数"`
	// 粉丝关系成交金额 较上一周期百分比
	FanRelationTransAmountPercent string `json:"fan_relation_trans_amount_percent" comment:"粉丝关系成交金额"`
}
type StatsShopDistributorDailyForApi struct {
	// 新增成交客户数
	TransCustomerCount int `json:"trans_customer_count" comment:"新增成交客户数"`
	// 新增成交单数
	TransCount int `json:"trans_count" comment:"新增成交单数"`
	// 新增成交金额
	TransAmount int `json:"trans_amount" comment:"新增成交金额"`
	// 新增成交商品数
	TransProductCount int `json:"trans_product_count" comment:"新增成交商品数"`
	// 新增成交客单价
	TransCustomerPrice int `json:"trans_customer_price" comment:"新增成交客单价"`
	// 新增成交客户数 较上一周期百分比
	TransCustomerCountPercent string `json:"trans_customer_count_percent" comment:"新增成交客户数 较上一周期百分比"`
	// 新增成交单数 较上一周期百分比
	TransCountPercent string `json:"trans_count_percent" comment:"新增成交单数 较上一周期百分比"`
	// 新增成交金额 较上一周期百分比
	TransAmountPercent string `json:"trans_amount_percent" comment:"新增成交金额 较上一周期百分比"`
	// 新增成交商品数 较上一周期百分比
	TransProductCountPercent string `json:"trans_product_count_percent" comment:"新增成交商品数 较上一周期百分比"`
	// 新增成交客单价 较上一周期百分比
	TransCustomerPricePercent string `json:"trans_customer_price_percent" comment:"新增成交客单价 较上一周期百分比"`

	// 新增分销佣金
	Commission int `json:"commission" comment:"新增分销佣金"`
	// 新增未结佣金
	UnsettledCommission int `json:"unsettled_commission" comment:"新增待结佣金"`
	// 新增已结佣金
	SettledCommission int `json:"settled_commission" comment:"新增已结佣金"`
	// 新增分销佣金 较上一周期百分比
	CommissionPercent string `json:"commission_percent" comment:"新增分销佣金"`
	// 新增未结佣金 较上一周期百分比
	UnsettledCommissionPercent string `json:"unsettled_commission_percent" comment:"新增待结佣金"`
	// 新增已结佣金 较上一周期百分比
	SettledCommissionPercent string `json:"settled_commission_percent" comment:"新增已结佣金"`

	// 分销海报扫码数
	PosterScanCount int `json:"poster_scan_count" comment:"分销海报扫码数"`
	// 分销海报成交单数
	PosterTransCount int `json:"poster_trans_count" comment:"分销海报成交单数"`
	// 分销海报成交金额(分)
	PosterTransAmount int `json:"poster_trans_amount" comment:"分销海报成交金额(分)"`
	// 分销海报扫码数   较上一周期百分比
	PosterScanCountPercent string `json:"poster_scan_count_percent" comment:"分销海报扫码数  较上一周期百分比"`
	// 分销海报成交单数   较上一周期百分比
	PosterTransCountPercent string `json:"poster_trans_count_percent" comment:"分销海报成交单数  较上一周期百分比"`
	// 分销海报成交金额(分)   较上一周期百分比
	PosterTransAmountPercent string `json:"poster_trans_amount_percent" comment:"分销海报成交金额 较上一周期百分比"`

	// 分销链接点击数
	LinkClickCount int `json:"link_click_count" comment:"分销链接点击数"`
	// 分销链接成交单数
	LinkTransCount int `json:"link_trans_count" comment:"分销链接成交单数"`
	// 分销链接成交金额
	LinkTransAmount int `json:"link_trans_amount" comment:"分销链接成交金额(分)"`
	// 分销链接点击数 较上一周期百分比
	LinkClickCountPercent string `json:"link_click_count_percent" comment:"分销链接点击数  较上一周期百分比"`
	// 分销链接成交单数 较上一周期百分比
	LinkTransCountPercent string `json:"link_trans_count_percent" comment:"分销链接成交单数 较上一周期百分比"`
	// 分销链接成交金额 较上一周期百分比
	LinkTransAmountPercent string `json:"link_trans_amount_percent" comment:"分销链接成交金额较上一周期百分比"`

	// 粉丝关系成交单数
	FanRelationTransCount int `json:"fan_relation_trans_count" comment:"粉丝关系成交单数"`
	// 粉丝关系成交金额
	FanRelationTransAmount int `json:"fan_relation_trans_amount" comment:"粉丝关系成交金额"`
	// 粉丝关系成交单数  较上一周期百分比
	FanRelationTransCountPercent string `json:"fan_relation_trans_count_percent" comment:"粉丝关系成交单数"`
	// 粉丝关系成交金额 较上一周期百分比
	FanRelationTransAmountPercent string `json:"fan_relation_trans_amount_percent" comment:"粉丝关系成交金额"`
}

type StatsDisCenterIndexReq struct {
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
	//分销员用户id(前端不用传)
	MemberId int `json:"member_id"`
	// flag=0 代表的是分销员中心-店员 ， flag=1 代表的是分销员中心-老板
	Flag int `json:"flag"`
}
type StatsDisCenterIndexResp struct {
	viewmodel.BaseHttpResponse
	Data StatsDisCenterIndex `json:"data"`
}
type StatsDisCenterIndex struct {
	// 企业数据
	Shop StatsDisCenterIndexShop `json:"shop"`
	// 我的数据
	Distributor StatsDisCenterIndexMy `json:"distributor"`
}
type StatsDisCenterIndexShop struct {
	// 本月佣金
	ThisMonthCommission int `json:"this_month_commission"`
	// 本月待结算佣金
	ThisMonthUnsettledCommission int `json:"this_month_unsettled_commission"`
	// 本月订单
	ThisMonthTransCount int `json:"this_month_trans_count"`

	// 累计分销员数
	DistributorCnt int `json:"distributor_cnt"`
	// 累计佣金(单位分)
	Commission int `json:"commission"`
	// 待结佣金
	UnsettledCommission int `json:"unsettled_commission"`
	// 累计订单
	TransCount int `json:"trans_count"`
	// 累计客户
	TotalCustomer int `json:"total_customer"`
	// 可提现金额
	WaitWithdraw int `json:"wait_withdraw"`
}
type StatsDisCenterIndexMy struct {
	// 本月佣金
	ThisMonthCommission int `json:"this_month_commission"`
	// 本月待结算佣金
	ThisMonthUnsettledCommission int `json:"this_month_unsettled_commission"`
	// 本月订单
	ThisMonthTransCount int `json:"this_month_trans_count"`
	// 本月消费客户
	ThisMonthTransCustomerCount int `json:"this_month_trans_customer_count"`

	// 累计佣金(单位分)
	Commission int `json:"commission"`
	// 待结佣金
	UnsettledCommission int `json:"unsettled_commission"`
	// 累计订单
	TransCount int `json:"trans_count"`
	// 累计客户
	TotalCustomer int `json:"total_customer"`
}

type StatsDisCenterOverviewResp struct {
	viewmodel.BaseHttpResponse
	Data StatsDisCenterOverview `json:"data"`
}

type StatsDisCenterGraphReq struct {
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
	// 曲线图类型：1-分销单数 2-分销金额 3-分销佣金 4-分销客户
	GraphType int `json:"graph_type"`
	//分销员用户id(前端不用传)
	MemberId int `json:"member_id"`
	// flag=0 代表的是分销员中心-店员 ， flag=1 代表的是分销员中心-老板
	Flag int `json:"flag"`
}

type StatsDisCenterGraphResp struct {
	viewmodel.BaseHttpResponse
	Data []StatsDisGraphData `json:"data"`
}
type StatsDisCenterTotalReq struct {
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
	//分销员用户id(前端不用传)
	MemberId int `json:"member_id"`
	// flag=0 代表的是分销员中心-店员 ， flag=1 代表的是分销员中心-老板
	Flag int `json:"flag"`
}
type StatsDisCenterTotalResp struct {
	viewmodel.BaseHttpResponse
	Data StatsDisCenterTotal `json:"data"`
}
type StatsDisCenterTotal struct {
	// 分销成交金额(单位分)
	TransAmount int `json:"trans_amount"`
	// 分销成交单数
	TransCount int `json:"trans_count"`
	// 分销佣金(单位分)
	Commission int `json:"commission"`
	// 已结佣金
	SettledCommission int `json:"settled_commission"`
	// 待结佣金
	UnsettledCommission int `json:"unsettled_commission"`
	// 分销员数
	DistributorCnt int `json:"distributor_cnt"`
	// 累计客户
	TotalCustomer int `json:"total_customer"`
	// 提现成功
	WithdrawSuccess int `json:"withdraw_success"`
	// 提现申请
	WithdrawApply int `json:"withdraw_apply"`
	// 待提现
	WaitWithdraw int `json:"wait_withdraw"`
}

type StatsDisCenterDistributorReq struct {
	viewmodel.BasePageHttpRequest
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
	//分销员用户id(前端不用传)
	MemberId int `json:"member_id"`
	// 分销员名称
	DisName string `json:"dis_name"`
	// 排序:1-分销单数升序 2-分销单数降序 3-分销金额升序 4-分销金额降序 5-分销佣金升序 6-分销佣金降序
	OrderBy int `json:"order_by"`
}
type DisCenterDistributor struct {
	// 分销员名称
	DisName string `json:"dis_name"`
	// 分销员member_id
	DisMemberId int `json:"dis_member_id"`
	// 分销员角色 0-初始值 1-老板 2-店员
	DisRole int `json:"dis_role"`
	// 分销员状态：  1-启用 2-禁用（已清退）
	Status int `json:"status"`
	// 累计客户数(dis_distributor.total_customer)
	TotalCustomer int `json:"total_customer"`

	// 分销店铺id
	ShopId int `json:"shop_id"`
	// 分销员ID
	DisId int `json:"dis_id"`
	//stats_shop_distributor_daily.type类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单)
	Type int `json:"type"`

	// 分销成交金额
	TransAmount int `json:"trans_amount"`
	// 分销成交单数
	TransCount int `json:"trans_count"`
	// 消费客户数
	TransCustomerCount int `json:"trans_customer_count"`
	// 分销佣金
	Commission int `json:"commission"`

	// 商品分销成交金额
	GoodsTransAmount int `json:"goods_trans_amount"`
	// 商品分销成交单数
	GoodsTransCount int `json:"goods_trans_count"`
	// 保险分销成交金额
	InsTransAmount int `json:"ins_trans_amount"`
	// 保险分销成交单数
	InsTransCount int `json:"ins_trans_count"`
}

type StatsDisCenterDistributorResp struct {
	viewmodel.BasePageHttpResponse
	Data []DisCenterDistributor `json:"data"`
}
type StatsDisCenterGoodsReq struct {
	viewmodel.BasePageHttpRequest
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
	// 主体(前端不用传)
	OrgId int64 `json:"org_id"`
	//分销员用户id(前端不用传)
	MemberId int `json:"member_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 排序:1-分销件数升序 2-分销件数降序 3-分销金额升序 4-分销金额降序 5-分销佣金升序 6-分销佣金降序
	OrderBy int `json:"order_by"`
	// flag=0 代表的是分销员中心-店员 ， flag=1 代表的是分销员中心-老板
	Flag int `json:"flag"`
}
type StatsDisCenterGoods struct {
	// 商品Id
	GoodsId int `json:"goods_id"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 商品图片
	GoodsImage string `json:"goods_image"`
	// 分销成交件数
	TransCount int `json:"trans_count"`
	// 分销成交金额(单位分)
	TransAmount int `json:"trans_amount"`
	// 分销佣金(单位分)
	Commission int `json:"commission"`
}
type StatsDisCenterGoodsResp struct {
	viewmodel.BasePageHttpResponse
	Data []StatsDisCenterGoods `json:"data"`
}

// 商品订单概览
type OrderData struct {
	// 下单数据
	SubmitOrder SubmitOrder `json:"submit_order"`
	// 成交数据
	TransOrder TransOrder `json:"trans_order"`
	//取消/退款数据
	CancelRefundOrder CancelRefundOrder `json:"cancel_refund_order"`
	//分销新增来源数据
	DisSource DisSource `json:"dis_source"`
	// 分销佣金数据
	DisCommission DisCommission `json:"dis_commission"`
}

type SubmitOrder struct {
	// 累计下单客户数
	OrderCustomerCountTotal int `json:"order_customer_count_total" comment:"累计下单客户数"`
	// 累计下单单数
	OrderCountTotal int `json:"order_count_total" comment:"累计下单单数"`
	// 累计下单金额
	OrderAmountTotal int `json:"order_amount_total" comment:"累计下单金额"`
	// 累计下单商品数
	OrderProductCountTotal int `json:"order_product_count_total" comment:"累计下单商品数"`
	// 累计下单客单价
	OrderCustomerPriceTotal int `json:"order_customer_price_total" comment:"累计下单客单价"`
	// 新增下单客户数
	OrderCustomerCount int `json:"order_customer_count" comment:"新增下单客户数"`
	// 新增下单单数
	OrderCount int `json:"order_count" comment:"新增下单单数"`
	// 新增下单金额
	OrderAmount int `json:"order_amount" comment:"新增下单金额"`
	// 新增下单商品数
	OrderProductCount int `json:"order_product_count" comment:"新增下单商品数"`
	// 新增下单客单价
	OrderCustomerPrice int `json:"order_customer_price" comment:"新增下单客单价"`
	// 新增下单客户数 较上一周期百分比
	OrderCustomerCountPercent string `json:"order_customer_count_percent" comment:"新增下单客户数"`
	// 新增下单单数 较上一周期百分比
	OrderCountPercent string `json:"order_count_percent" comment:"新增下单单数"`
	// 新增下单金额 较上一周期百分比
	OrderAmountPercent string `json:"order_amount_percent" comment:"新增下单金额"`
	// 新增下单商品数 较上一周期百分比
	OrderProductCountPercent string `json:"order_product_count_percent" comment:"新增下单商品数"`
	// 新增下单客单价 较上一周期百分比
	OrderCustomerPricePercent string `json:"order_customer_price_percent" comment:"新增下单客单价"`
}
type TransOrder struct {
	// 累计成交客户数
	TransCustomerCountTotal int `json:"trans_customer_count_total" comment:"累计成交客户数"`
	// 累计成交单数
	TransCountTotal int `json:"trans_count_total" comment:"累计成交单数"`
	// 累计成交金额
	TransAmountTotal int `json:"trans_amount_total" comment:"累计成交金额"`
	// 累计成交商品数
	TransProductCountTotal int `json:"trans_product_count_total" comment:"累计成交商品数"`
	// 累计成交客单价
	TransCustomerPriceTotal int `json:"trans_customer_price_total" comment:"累计成交客单价"`
	// 新增成交客户数
	TransCustomerCount int `json:"trans_customer_count" comment:"新增成交客户数"`
	// 新增成交单数
	TransCount int `json:"trans_count" comment:"新增成交单数"`
	// 新增成交金额
	TransAmount int `json:"trans_amount" comment:"新增成交金额"`
	// 新增成交商品数
	TransProductCount int `json:"trans_product_count" comment:"新增成交商品数"`
	// 新增成交客单价
	TransCustomerPrice int `json:"trans_customer_price" comment:"新增成交客单价"`
	// 新增成交客户数 较上一周期百分比
	TransCustomerCountPercent string `json:"trans_customer_count_percent" comment:"新增成交客户数 较上一周期百分比"`
	// 新增成交单数 较上一周期百分比
	TransCountPercent string `json:"trans_count_percent" comment:"新增成交单数 较上一周期百分比"`
	// 新增成交金额 较上一周期百分比
	TransAmountPercent string `json:"trans_amount_percent" comment:"新增成交金额 较上一周期百分比"`
	// 新增成交商品数 较上一周期百分比
	TransProductCountPercent string `json:"trans_product_count_percent" comment:"新增成交商品数 较上一周期百分比"`
	// 新增成交客单价 较上一周期百分比
	TransCustomerPricePercent string `json:"trans_customer_price_percent" comment:"新增成交客单价 较上一周期百分比"`
}

type CancelRefundOrder struct {
	// 累计取消单数
	CancelCountTotal int `json:"cancel_count_total" comment:"累计取消单数"`
	// 累计取消金额
	CancelAmountTotal int `json:"cancel_amount_total" comment:"累计取消金额"`
	// 累计取消商品数
	CancelProductCountTotal int `json:"cancel_product_count_total" comment:"累计取消商品数"`
	// 累计退款单数
	RefundCountTotal int `json:"refund_count_total" comment:"累计退款单数"`
	// 累计退款金额
	RefundAmountTotal int `json:"refund_amount_total" comment:"累计退款金额"`
	// 累计退款商品数
	RefundProductCountTotal int `json:"refund_product_count_total" comment:"累计退款商品数"`
	// 新增取消单数
	CancelCount int `json:"cancel_count" comment:"新增取消单数"`
	// 新增取消金额
	CancelAmount int `json:"cancel_amount" comment:"新增取消金额"`
	// 新增取消商品数
	CancelProductCount int `json:"cancel_product_count" comment:"新增取消商品数"`
	// 新增退款单数
	RefundCount int `json:"refund_count" comment:"新增退款单数"`
	// 新增退款金额
	RefundAmount int `json:"refund_amount" comment:"新增退款金额"`
	// 新增退款商品数
	RefundProductCount int `json:"refund_product_count" comment:"新增退款商品数"`
	// 新增取消单数  较上一周期百分比
	CancelCountPercent string `json:"cancel_count_percent" comment:"新增取消单数 较上一周期百分比"`
	// 新增取消金额  较上一周期百分比
	CancelAmountPercent string `json:"cancel_amount_percent" comment:"新增取消金额 较上一周期百分比"`
	// 新增取消商品数  较上一周期百分比
	CancelProductCountPercent string `json:"cancel_product_count_percent" comment:"新增取消商品数 较上一周期百分比"`
	// 新增退款单数  较上一周期百分比
	RefundCountPercent string `json:"refund_count_percent" comment:"新增退款单数 较上一周期百分比"`
	// 新增退款金额  较上一周期百分比
	RefundAmountPercent string `json:"refund_amount_percent" comment:"新增退款金额 较上一周期百分比"`
	// 新增退款商品数  较上一周期百分比
	RefundProductCountPercent string `json:"refund_product_count_percent" comment:"新增退款商品数 较上一周期百分比"`
}

type DisSource struct {
	// 分销海报扫码数
	PosterScanCount int `json:"poster_scan_count" comment:"分销海报扫码数"`
	// 分销海报成交单数
	PosterTransCount int `json:"poster_trans_count" comment:"分销海报成交单数"`
	// 分销海报成交金额(分)
	PosterTransAmount int `json:"poster_trans_amount" comment:"分销海报成交金额(分)"`
	// 分销海报扫码数   较上一周期百分比
	PosterScanCountPercent string `json:"poster_scan_count_percent" comment:"分销海报扫码数  较上一周期百分比"`
	// 分销海报成交单数   较上一周期百分比
	PosterTransCountPercent string `json:"poster_trans_count_percent" comment:"分销海报成交单数  较上一周期百分比"`
	// 分销海报成交金额(分)   较上一周期百分比
	PosterTransAmountPercent string `json:"poster_trans_amount_percent" comment:"分销海报成交金额 较上一周期百分比"`

	// 分销链接点击数
	LinkClickCount int `json:"link_click_count" comment:"分销链接点击数"`
	// 分销链接成交单数
	LinkTransCount int `json:"link_trans_count" comment:"分销链接成交单数"`
	// 分销链接成交金额
	LinkTransAmount int `json:"link_trans_amount" comment:"分销链接成交金额(分)"`
	// 分销链接点击数 较上一周期百分比
	LinkClickCountPercent string `json:"link_click_count_percent" comment:"分销链接点击数  较上一周期百分比"`
	// 分销链接成交单数 较上一周期百分比
	LinkTransCountPercent string `json:"link_trans_count_percent" comment:"分销链接成交单数 较上一周期百分比"`
	// 分销链接成交金额 较上一周期百分比
	LinkTransAmountPercent string `json:"link_trans_amount_percent" comment:"分销链接成交金额较上一周期百分比"`

	// 粉丝关系成交单数
	FanRelationTransCount int `json:"fan_relation_trans_count" comment:"粉丝关系成交单数"`
	// 粉丝关系成交金额
	FanRelationTransAmount int `json:"fan_relation_trans_amount" comment:"粉丝关系成交金额"`
	// 粉丝关系成交单数  较上一周期百分比
	FanRelationTransCountPercent string `json:"fan_relation_trans_count_percent" comment:"粉丝关系成交单数"`
	// 粉丝关系成交金额 较上一周期百分比
	FanRelationTransAmountPercent string `json:"fan_relation_trans_amount_percent" comment:"粉丝关系成交金额"`
}

type DisCommission struct {
	// 累计分销佣金
	CommissionTotal int `json:"commission_total" comment:"累计分销佣金"`
	// 累计未结佣金
	UnsettledCommissionTotal int `json:"unsettled_commission_total" comment:"累计待结佣金"`
	// 累计已结佣金
	SettledCommissionTotal int `json:"settled_commission_total" comment:"累计已结佣金"`
	// 新增分销佣金
	Commission int `json:"commission" comment:"新增分销佣金"`
	// 新增未结佣金
	UnsettledCommission int `json:"unsettled_commission" comment:"新增待结佣金"`
	// 新增已结佣金
	SettledCommission int `json:"settled_commission" comment:"新增已结佣金"`
	// 新增分销佣金 较上一周期百分比
	CommissionPercent string `json:"commission_percent" comment:"新增分销佣金"`
	// 新增未结佣金 较上一周期百分比
	UnsettledCommissionPercent string `json:"unsettled_commission_percent" comment:"新增待结佣金"`
	// 新增已结佣金 较上一周期百分比
	SettledCommissionPercent string `json:"settled_commission_percent" comment:"新增已结佣金"`
}
type StatsShopDistributorDailyReq struct {
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
}

// 商品订单概览响应
type StatsShopOrderOverviewResp struct {
	viewmodel.BaseHttpResponse
	Data StatsShopOrderOverview `json:"data" comment:"商品订单概览"`
}

// 商品订单概览响应
type StatsShopOrderOverview struct {
}

type StatsShopPageDto struct {
	ShopId  int    `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'"`
	StatIds string `json:"stat_ids" xorm:"VARCHAR(64) 'stat_ids'"`
}

type StatsShopPageReq struct {
	// 分销企业（企业名称/店铺名称）
	Name string `json:"name"`
	// 所属业务员
	SalesmanName string `json:"salesman_name"`
	// 时间范围-开始时间
	StartDate string `json:"start_date"`
	// 时间范围-结束时间
	EndDate string `json:"end_date"`

	viewmodel.BasePageHttpRequest
}

type StatsShopPageResp struct {
	viewmodel.BasePageHttpResponse
	Data []StatsShopPageData `json:"data"`
}

type StatsShopPageData struct {
	Simple StatsShopSimple `json:"simple"`
	// 商品+保险分销数据（时间范围内数据）
	TotalDistribute TotalStatDistribute `json:"total_distribute"`
	// 商品分销数据
	ProductDistribute StatDistribute `json:"product_distribute"`
	// 保险分销数据
	InsureDistribute StatDistribute `json:"insure_distribute"`
}

type StatsShopSimple struct {
	// 企业编码
	EnterpriseId string `json:"enterprise_id"`
	// 企业名称
	EnterpriseName string `json:"enterprise_name"`
	// 分销店铺名称
	ShopName string `json:"shop_name"`
	// 所属业务员
	SalesmanName string `json:"salesman_name"`
	// 电商店铺
	UpetShopName string `json:"upet_shop_name"`
	// 分销员数量
	DisCount int `json:"dis_count"`
	// 成交分销员
	TransDisCount int `json:"trans_dis_count"`
}

type TotalStatDistribute struct {
	// 分销数据
	StatDistribute
	// 税前提现金额
	PreTaxAmount int `json:"pre_tax_amount"`
}

type StatDistribute struct {
	// 分销成交客户数
	TransCustomerCount int `json:"trans_customer_count"`
	// 分销成交单数
	TransCount int `json:"trans_count"`
	// 分销成交金额
	TransAmount int `json:"trans_amount"`
	// 分销商品件数
	TransProductCount int `json:"trans_product_count"`
	// 分销成交客单价
	TransCustomerPrice int `json:"trans_customer_price"`
	// 分销海报成交单数
	PosterTransCount int `json:"poster_trans_count"`
	// 分销海报成交金额
	PosterTransAmount int `json:"poster_trans_amount"`
	// 分销链接成交单数
	LinkTransCount int `json:"link_trans_count"`
	// 分销链接成交金额
	LinkTransAmount int `json:"link_trans_amount"`
	// 粉丝关系成交单数
	FanRelationTransCount int `json:"fan_relation_trans_count"`
	// 粉丝关系成交金额
	FanRelationTransAmount int `json:"fan_relation_trans_amount"`
	// 分销佣金总额
	Commission int `json:"commission"`
	// 已结佣金
	SettledCommission int `json:"settled_commission"`
	// 未结佣金
	UnsettledCommission int `json:"unsettled_commission"`
}
