package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SjanProduct 北京世纪安诺产品信息表领域模型
type SjanProduct struct {
	Id         int       `xorm:"pk autoincr not null 'id'" json:"id"`               // 自增主键
	PNo        string    `xorm:"not null unique VARCHAR(50) 'p_no'" json:"p_no"`    // 产品编号
	Pname      string    `xorm:"not null VARCHAR(100) 'pname'" json:"pname"`        // 产品名称
	Ptype      string    `xorm:"not null VARCHAR(50) 'ptype'" json:"ptype"`         // 产品分类
	UnitNum    int       `xorm:"not null 'unit_num'" json:"unit_num"`               // 包装数量
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"` // 创建时间
	UpdateTime time.Time `xorm:"datetime updated 'update_time'" json:"update_time"` // 更新时间
}

// TableName 表名
func (s *SjanProduct) TableName() string {
	return "blky.sjan_products"
}

// GetByPNo 根据产品编号获取产品信息
func (s *SjanProduct) GetByPNo(session *xorm.Session, pNo string) (*SjanProduct, error) {
	var product SjanProduct
	has, err := session.Where("p_no = ?", pNo).Get(&product)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &product, nil
}
