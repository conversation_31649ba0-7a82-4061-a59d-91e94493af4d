// domain/omnibus-po/sms_config.go
package omnibus_po

import (
	"time"

	"xorm.io/xorm"
)

const (
	SmsSignName              = "深圳市瑞海联科技"
	RpMillionsVerifyCodeKey  = "Saas:SmsVerifyCode:"
	RpMillionsVerifyCountKey = "Saas:SmsVerifyCodeCount:"
	// 登录注册验证码(不消耗店铺短信次数)
	SmsTemplateVerifyCode = "SMS_486215142"
	// 储值卡支付验证码
	SmsTemplateStoreCardPayVerify = "SMS_486090123"
	// 储值卡消费通知
	SmsTemplateStoreCardConsume = "SMS_486185138"
	// 储值卡退费通知
	SmsTemplateStoreCardRefund = "SMS_486285123"
	// 储值卡充值通知
	SmsTemplateStoreCardRecharge = "SMS_486095127"
	// 次卡充值通知
	SmsTemplateTimeCardRecharge = "SMS_486270123"
	// 储值卡过期通知
	SmsTemplateStoreCardExpire = "SMS_486095126"
	// 次卡过期通知
	SmsTemplateTimeCardExpire = "SMS_486220132"
	// 优惠券-满减赠送通知
	SmsTemplateCouponFullReduction = "SMS_486145109"
	// 优惠券-折扣赠送通知
	SmsTemplateCouponDiscount = "SMS_486265113"
	// 优惠券过期通知
	SmsTemplateCouponExpire = "SMS_486100139" // 需要申请新模板
	// 预约通知
	SmsTemplateReservation = "SMS_486265119"
	// 寄养到期通知
	SmsTemplatePetHosting = "SMS_486250135"
	// 店铺短信剩余数量key前缀
	StoreSmsCountKey = "Store:Sms:Count:"
)

// 短信模板内容
const (
	// 验证码模板
	SmsTemplateVerifyCodeContent = "您的短信验证码是${code}。如非本人操作，请忽略本短信"
	// 储值卡支付验证码模板
	SmsTemplateStoreCardPayVerifyContent = "您的储值卡支付短信验证码是${code}。如非本人操作，请忽略本短信"
	// 储值卡消费通知模板
	SmsTemplateStoreCardConsumeContent = "尊敬的${tenantName}客户，您消费${payAmount}元，您的账户现有余额${balanceAmount}元。感谢您的支持和信任！"
	// 储值卡退费通知模板
	SmsTemplateStoreCardRefundContent = "尊敬的${tenantName}客户，您已成功退款${refundAmount}元，您的账户现有余额${balanceAmount}元。感谢您的支持和信任！"
	// 储值卡充值通知模板
	SmsTemplateStoreCardRechargeContent = "尊敬的${tenantName}客户，您已成功充值${changeAmount}元，您的账户现有余额${balanceAmount}元。感谢您的支持和信任！"
	// 次卡充值通知模板
	SmsTemplateTimeCardRechargeContent = "尊敬的${tenantName}客户，您已成功充值${changeAmount}元，您的账户现有余额${totalNum}次。感谢您的支持和信任！"
	// 储值卡过期提醒模板
	SmsTemplateStoreCardExpireContent = "尊敬的${tenantName}客户，您在${activateTime}办理的储值卡${storeCardName}将于${expireTime}过期，余额${balanceAmount}元，请及时到店消费使用。"
	// 次卡过期提醒模板
	SmsTemplateTimeCardExpireContent = "尊敬的${tenantName}客户，您在${activateTime}办理的次卡${timeCardName}将于${expireTime}过期，余额${totalNum}次，请及时到店消费使用。"
	// 优惠券-满减赠送通知模板
	SmsTemplateCouponFullReductionContent = "尊敬的${tenantName}客户，为感谢您的支持和信任，赠送您满减优惠券一张，满${threshold}减${discount}。可到店消费使用。拒收请回复R"
	// 优惠券-折扣赠送通知模板
	SmsTemplateCouponDiscountContent = "尊敬的${tenantName}客户，为感谢您的支持和信任，赠送您折扣优惠券一张，满${threshold}打${discount}折。可到店消费使用。拒收请回复R"
	// 优惠券过期提醒模板
	SmsTemplateCouponExpireContent = "尊敬的${tenantName}客户，您有一张优惠券，即将于${expireTime}过期。"
	// 预约服务通知模板
	SmsTemplateReservationContent = "尊敬的${tenantName}客户，您预约${reservationTime}的服务，请及时到店。"
	// 寄养到期通知模板
	SmsTemplatePetHostingContent = "尊敬的${tenantName}客户，您寄养的${petName}，即将于${endTime}完毕，希望Ta健康成长。"
)

// 更新模板内容映射
var TemplateContentMap = map[string]string{
	SmsTemplateVerifyCode:          SmsTemplateVerifyCodeContent,
	SmsTemplateStoreCardPayVerify:  SmsTemplateStoreCardPayVerifyContent,
	SmsTemplateStoreCardConsume:    SmsTemplateStoreCardConsumeContent,
	SmsTemplateStoreCardRefund:     SmsTemplateStoreCardRefundContent,
	SmsTemplateStoreCardRecharge:   SmsTemplateStoreCardRechargeContent,
	SmsTemplateTimeCardRecharge:    SmsTemplateTimeCardRechargeContent,
	SmsTemplateStoreCardExpire:     SmsTemplateStoreCardExpireContent,
	SmsTemplateTimeCardExpire:      SmsTemplateTimeCardExpireContent,
	SmsTemplateCouponFullReduction: SmsTemplateCouponFullReductionContent,
	SmsTemplateCouponDiscount:      SmsTemplateCouponDiscountContent,
	SmsTemplateCouponExpire:        SmsTemplateCouponExpireContent,
	SmsTemplateReservation:         SmsTemplateReservationContent,
	SmsTemplatePetHosting:          SmsTemplatePetHostingContent,
}

// SmsConfig 短信配置
type SmsConfig struct {
	Id           int       `json:"id" xorm:"pk autoincr 'id'"`
	ChainId      int64     `json:"chain_id" xorm:"not null INT 'chain_id'"`
	StoreId      string    `json:"store_id" xorm:"not null VARCHAR(50) 'store_id'"`
	ConfigType   string    `json:"config_type" xorm:"not null VARCHAR(50) 'config_type' comment('配置类型')"`
	ConfigName   string    `json:"config_name" xorm:"not null VARCHAR(100) 'config_name' comment('配置名称')"`
	IsEnabled    bool      `json:"is_enabled" xorm:"not null default false TINYINT 'is_enabled'"`
	ExpireDays   int       `json:"expire_days" xorm:"INT 'expire_days'"`
	TemplateCode string    `json:"template_code" xorm:"VARCHAR(50) 'template_code'"`
	CreatedTime  time.Time `json:"created_time" xorm:"created 'created_time'"`
	UpdatedTime  time.Time `json:"updated_time" xorm:"updated 'updated_time'"`
}

func (s SmsConfig) TableName() string {
	return "eshop.sms_config"
}

// GetByStoreId 获取店铺的短信配置
func (s *SmsConfig) GetByStoreId(session *xorm.Session, storeId string) ([]SmsConfig, error) {
	var configs []SmsConfig
	err := session.Where("store_id = ?", storeId).Find(&configs)
	return configs, err
}

// 查询短信模板是否开启
func (s *SmsConfig) IsSmsEnabled(session *xorm.Session, storeId string, templateCode string) (bool, error) {
	var config SmsConfig
	has, err := session.Where("store_id = ? AND template_code = ?", storeId, templateCode).Get(&config)
	return has && config.IsEnabled, err
}

// Save 保存短信配置
func (s *SmsConfig) Save(session *xorm.Session) error {
	var count int64
	var err error

	count, err = session.Table(s.TableName()).Where("store_id = ? AND config_type = ?", s.StoreId, s.ConfigType).Count(new(SmsConfig))
	if err != nil {
		return err
	}

	if count > 0 {
		// 时间要转时区
		_, err = session.Table(s.TableName()).Where("store_id = ? AND config_type = ?", s.StoreId, s.ConfigType).
			Update(map[string]interface{}{
				"is_enabled":    s.IsEnabled,
				"expire_days":   s.ExpireDays,
				"template_code": s.TemplateCode,
			})
	} else {
		_, err = session.Table(s.TableName()).Insert(s)
	}

	return err
}

// InitDefaultConfigs 初始化默认配置
func (s *SmsConfig) InitDefaultConfigs(session *xorm.Session, storeId string) error {
	defaultConfigs := []struct {
		ConfigType   string
		ConfigName   string
		TemplateCode string
	}{
		{"consumption", "消费提醒", SmsTemplateStoreCardConsume},                // 储值卡消费通知
		{"store_card_recharge", "储值卡充值", SmsTemplateStoreCardRecharge},      // 储值卡充值通知
		{"time_card_recharge", "次卡充值", SmsTemplateTimeCardRecharge},         // 次卡充值通知
		{"store_card_expire", "储值卡过期", SmsTemplateStoreCardExpire},          // 储值卡过期通知
		{"time_card_expire", "次卡过期", SmsTemplateTimeCardExpire},             // 次卡过期通知
		{"coupon_full_reduction", "满减赠送通知", SmsTemplateCouponFullReduction}, // 优惠券-满减赠送通知
		{"coupon_discount", "折扣赠送通知", SmsTemplateCouponDiscount},            // 优惠券-折扣赠送通知
		{"coupon_expire", "优惠券过期", SmsTemplateCouponExpire},                 // 优惠券过期通知
		{"refund_remind", "退单提醒", SmsTemplateStoreCardRefund},               // 储值卡退费通知
		{"reservation", "预约通知", SmsTemplateReservation},                     // 预约通知
		{"pet_hosting_expire", "寄养到期通知", SmsTemplatePetHosting},             // 寄养到期通知
	}

	for _, cfg := range defaultConfigs {
		config := &SmsConfig{
			StoreId:      storeId,
			ConfigType:   cfg.ConfigType,
			ConfigName:   cfg.ConfigName,
			IsEnabled:    false,
			TemplateCode: cfg.TemplateCode,
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		}

		err := config.Save(session)
		if err != nil {
			return err
		}
	}

	return nil
}
