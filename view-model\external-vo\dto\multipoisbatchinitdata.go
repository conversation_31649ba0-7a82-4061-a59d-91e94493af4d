package dto

//// 普通属性列表
//type ValueList struct {
//	// 普通属性值Id
//	ValueId int64 `json:"valueId" form:"valueId" query:"valueId"`
//	// 普通属性值名称
//	Value string `json:"value" form:"value" query:"value"`
//}
//// 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
//type CommonAttrValue struct {
//	// 普通属性Id
//	AttrId int64 `json:"attrId" form:"attrId" query:"attrId"`
//	// 普通属性名称
//	AttrName string `json:"attrName" form:"attrName" query:"attrName"`
//	ValueList []ValueList `json:"valueList" form:"valueList" query:"valueList"`
//}

//// 限购信息  非必镇
//type LimitSaleInfo struct {
//	// 是否限制购买数量 必镇
//	LimitSale bool `json:"limitSale" form:"limitSale" query:"limitSale"`
//	// 限购规则： 1-限制下单顾客每X天限购数量，X为frequency，不传默认为1；2-限制整个周期内下单顾客限购数量 如限购开启则必填
//	Type int32 `json:"type" form:"type" query:"type"`
//	// 限购循环天数： 最大31，最小1。 非必镇
//	frequency int32  `json:"frequency" form:"frequency" query:"frequency"`
//	// 限购开始日期 如限购开启则必填
//	Begin string `json:"begin" form:"begin" query:"begin"`
//	// 限购数量  如限购开启则必填
//	Count int32 `json:"count" form:"count" query:"count"`
//	// 限购结束日期 如限购开启则必填
//	End string `json:"end" form:"end" query:"end"`
//}

type RetailInfo struct {
	// APP方商品id，即商家中台系统里商品的编码 必镇
	App_food_code string `json:"app_food_code" form:"app_food_code" query:"app_food_code"`
	// 商品名称 创建时必镇
	Name string `json:"name" form:"name" query:"name"`
	// 商品描述  非必镇
	Description string `json:"description,omitempty" form:"description" query:"description"`
	// APP方商品的skus信息，支持同时传多个sku信息。
	Skus []SkuParam `json:"Skus,omitempty" form:"Skus" query:"Skus"`
	// 商品的最小购买量，创建商品时，min_order_count字段信息如不传则默认为1。 必镇
	Min_order_count int `json:"min_order_count,omitempty" form:"min_order_count" query:"min_order_count"`
	// 商品的售卖单位。创建商品时，unit字段信息如不传则默认为“份”。 非必镇
	Unit string `json:"unit,omitempty" form:"unit" query:"unit"`
	// 分类名称 创建时必填
	Category_name string `json:"category_name" form:"category_name" query:"category_name"`
	// 商品上下架状态，字段取值范围：0-上架，1-下架。 非必镇
	Is_sold_out int `json:"is_sold_out" form:"is_sold_out" query:"is_sold_out"`
	// 商品图片： 非必镇
	Picture string `json:"picture,omitempty" form:"picture" query:"picture"`
	// 商品在当前分类下的排序：非必镇
	Sequence int `json:"sequence,omitempty" form:"sequence" query:"sequence"`
	// 美团内部商品类目id
	// 门店启用结构化属性并且传递了销售或普通属性则
	// 1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。若门店未启用或未传递销售属性/普通属性则非必传
	Tag_id int64 `json:"tag_id,omitempty" form:"tag_id" query:"tag_id"`
	// 商品品牌 非必镇
	Zh_name string `json:"zh_name,omitempty" form:"zh_name" query:"zh_name"`
	// 商品的产地 非必镇
	Origin_name string `json:"origin_name,omitempty" form:"origin_name" query:"origin_name"`
	// 商品的图片详情 非必镇
	Picture_contents string `json:"picture_contents,omitempty" form:"picture_contents" query:"picture_contents"`
	// 是否为“力荐”商品，字段取值范围：0-否， 1-是。 非必镇
	Is_specialty int `json:"is_specialty,omitempty" form:"is_specialty" query:"is_specialty"`
	//  商品普通属性的json字符串 若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
	Common_attr_value []CommonAttrValue `json:"common_attr_value,omitempty" form:"common_attr_value" query:"common_attr_value"`
	// 商品限购详情 非必镇
	Limit_sale_info LimitSaleInfo `json:"limit_sale_info,omitempty" form:"limit_sale_info" query:"limit_sale_info"`
}

type InitData struct {
	// 当次调用的操作类型  1-创建，2-更新。   必镇
	Type int `json:"type" form:"type" query:"type"`
	// is_all_pois=1，代表同步商品信息至该应用app下全部门店；如此字段传其他值，则返回错误信息，本次调用全部失败。
	// is_all_pois与app_poi_codes字段必须且只能填写一个
	Is_all_pois int `json:"is_all_pois,omitempty" form:"is_all_pois" query:"is_all_pois"`
	// APP方门店id 即商家中台系统里门店的编码 is_all_pois与app_poi_codes字段必须且只能填写一个
	App_poi_codes string `json:"app_poi_codes,omitempty" form:"app_poi_codes" query:"app_poi_codes"`
	// 多个商品数据集合的json格式数组：(1)可传商品数据限定不能超过200组
	Retail_info []RetailInfo `json:"retail_info" form:"retail_info" query:"retail_info"`
}

// / retail/multipois/batchinitdata 批量创建/更新商品信息至多店 （1次/5分钟，200个商品，最多可同步至3000个门店）
// 应用级参数
type MultipoisBatchinitdataRequest struct {
	Init_data InitData `json:"init_data" form:"init_data" query:"init_data"`
}

type MultipoisBatchinitdataResult struct {
	Data int `json:"data" form:"data" query:"data"`
}
