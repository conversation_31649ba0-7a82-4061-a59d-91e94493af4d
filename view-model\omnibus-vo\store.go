package omnibus_vo

import (
	omnibus_po "eShop/domain/omnibus-po"
	product_po "eShop/domain/product-po"
	viewmodel "eShop/view-model"
	marketing_vo "eShop/view-model/marketing-vo"
	"time"
)

type AddStoreRequest struct {
	//门店id
	ShopId string `json:"shop_id"`
	//连锁id
	ChainId string `json:"chain_id"`
	//主体id
	OrgId int `json:"org_id"`
	//门店名称
	Name string `json:"name"`
	//店铺地址
	Address string `json:"address"`
	//定位信息经度
	PointX string `json:"point_x"`
	//定位信息纬度
	PointY string `json:"point_y"`
	//省份
	Province string `json:"province"`
	//市
	City string `json:"city"`
	//县区
	County string `json:"county"`
	//联系电话
	Mobile string `json:"mobile"`
	//店铺头像
	Image string `json:"image"`
	//店铺状态
	BusinessStatus int `json:"business_status"`
	//小程序商城appid
	ChannelStoreId string `json:"channel_store_id"`
}

type StoreGetReq struct {
	OrgId int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//门店ID
	TenantId string `json:"tenant_id"`
}

type StoreListReq struct {
	OrgId int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//连锁ID
	ChainId string `xorm:"default 'NULL' comment('连锁id') int(3)"`
	//定位信息经度
	PointX string `xorm:"default 'NULL' comment('定位信息经度') float 'PointX'"`
	//定位信息纬度
	PointY string `xorm:"default 'NULL' comment('定位信息纬度') float 'PointY'"`
}

type StoreListRes struct {
	viewmodel.BasePageHttpResponse
	Data []StoreListView `json:"data"`
}
type StoreListView struct {
	Id int `xorm:"not null pk autoincr comment('自增id') INT(10)" json:"id"`
	//门店名称
	Name   string `xorm:"default '''' comment('门店名称') VARCHAR(50)" json:"name"`
	Mobile string `json:"mobile"`
	//财务编码
	FinanceCode string `xorm:"default 'NULL' comment('财务编码') VARCHAR(50)" json:"finance_code"`
	//门店ID
	ZilongId string `xorm:"default '''' comment('子龙门店id，systemid') VARCHAR(50)" json:"zilong_id"`
	//门店简称
	Shortname string `xorm:"default '''' comment('门店简称') VARCHAR(56)" json:"short_name"`
	//店铺编码
	StoreCode string `xorm:"default '''' comment('店铺编码') VARCHAR(56) 'StoreCode'" json:"store_code"`
	//店铺头像
	Image string `json:"image" xorm:"default '' comment('店铺头像') VARCHAR(128) 'image'"`
	//定位信息经度
	PointX string `xorm:"default 'NULL' comment('定位信息经度') float 'PointX'" json:"point_x"`
	//定位信息纬度
	PointY string `xorm:"default 'NULL' comment('定位信息纬度') float 'PointY'" json:"point_y"`
	//省份
	Province string `xorm:"default 'NULL' comment('省份') VARCHAR(56)" json:"province"`
	//市
	City string `xorm:"default 'NULL' comment('市') VARCHAR(56)" json:"city"`
	//县区
	County string `xorm:"default 'NULL' comment('县区') VARCHAR(56)" json:"county"`
	//店铺地址
	Address string `xorm:"default 'NULL' comment('店铺地址') VARCHAR(128)" json:"address"`
	//店铺简介
	Desc    string `xorm:"default 'NULL' comment('店铺简介') VARCHAR(256)" json:"desc"`
	ChainId string `xorm:"default 'NULL' comment('连锁id') VARCHAR(128)" json:"chain_id"`
	OrgId   string `xorm:"default 'NULL' comment('主体id') VARCHAR(128)" json:"org_id"`
	//距离 km
	Distance string `xorm:"default 'NULL' comment('主体id') VARCHAR(128)" json:"distance"`
	// app类型: NORMAL-基础支付信息; WX_MINI-微信小程序（当app_type不等于'WX_MINI'时，代表的意思是:店铺未开通小程序微信支付，模块无数据不可用.  前端提示：当前店铺未开通线上商城。）
	AppType string `json:"app_type"`
}

type StorePromotionListReq struct {
	StoreId int `json:"store_id"` // 店铺id(前端无需传)
}
type StorePromotionListRes struct {
	viewmodel.BaseHttpResponse
	Data PromotionList `json:"data"`
}
type PromotionList struct {
	MarketingActivity []marketing_vo.ActivityListData       `json:"marketing_activity"`
	MarketingCoupon   []marketing_vo.MarketingCouponListRes `json:"marketing_coupon"`
}

type StoreEditReq struct {
	OrgId int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//门店ID
	TenantId string `json:"tenant_id"`
	//第三方对应关系
	StoreIds []StoreRelation `json:"store_ids"`
}

type StoreGetRes struct {
	viewmodel.BaseHttpResponse
	//业务员列表数据
	Data StoreGet `json:"data"`
}
type StoreGet struct {
	//门店ID
	TenantId string `json:"tenant_id"`
	//门店名称
	TenantName string `json:"tenant_name"`
	//第三方对应关系
	StoreIds []StoreRelation `json:"store_ids"`
	//仓库对应关系
	Warehouses []WarehouseRelationShop `json:"warehouses"`
	//是否设置支付   0未设置  1已设置
	IsSetPay int `json:"is_set_pay"`
}

// 第三方对应关系
type StoreRelation struct {
	//渠道ID  1-小程序商城，2-美团，3-饿了么，4-京东到家
	ChannelId int `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序商城，2-美团，3-饿了么，4-京东到家 )') INT 'channel_id'"`
	//第三方ID
	ChannelStoreId string `json:"channel_store_id" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
	WarehouseName  string `json:"warehouse_name" xorm:"default 'null' comment('仓库名称') VARCHAR(255) 'warehouse_name'"`
	WarehouseId    int    `json:"warehouse_id" xorm:"not null comment('仓库id') INT 'warehouse_id'"`
}

// 仓库和门店的对应关系表
type WarehouseRelationShop struct {
	Id            int    `json:"id" xorm:"pk autoincr not null comment('自增id') INT 'id'"`
	WarehouseName string `json:"warehouse_name" xorm:"default 'null' comment('仓库名称') VARCHAR(255) 'warehouse_name'"`
	WarehouseId   int    `json:"warehouse_id" xorm:"not null comment('仓库id') INT 'warehouse_id'"`
	ChannelId     int    `json:"channel_id" xorm:"not null comment('1、小程序，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提') INT 'channel_id'"`
}

// 对应ES数据结构
type SaasStore struct {
	Id            int       `json:"id" xorm:"pk autoincr not null comment('自增id') INT 'id'"`
	Name          string    `json:"name" xorm:"default '' comment('门店名称') VARCHAR(50) 'name'"`
	Shortname     string    `json:"shortname" xorm:"default 'null' comment('门店简称') VARCHAR(56) 'shortname'"`
	StoreCode     string    `json:"storeCode" xorm:"default 'null' comment('店铺编码') VARCHAR(56) 'storeCode'"`
	FinanceCode   string    `json:"finance_code" xorm:"not null comment('财务编码') VARCHAR(50) 'finance_code'"`
	ZilongId      string    `json:"zilong_id" xorm:"default '' comment('子龙门店id，systemid') VARCHAR(50) 'zilong_id'"`
	PointX        string    `json:"pointX" xorm:"default '' comment('定位信息经度') VARCHAR(56) 'pointX'"`
	PointY        string    `json:"pointY" xorm:"default '' comment('定位信息纬度') VARCHAR(56) 'pointY'"`
	Bigregion     string    `json:"bigregion" xorm:"default '' comment('大区') VARCHAR(56) 'bigregion'"`
	Provinceid    int       `json:"provinceid" xorm:"default 'null' comment('省id') INT 'provinceid'"`
	Province      string    `json:"province" xorm:"default '' comment('省份') VARCHAR(56) 'province'"`
	Cityid        int       `json:"cityid" xorm:"default 'null' comment('城市id') INT 'cityid'"`
	City          string    `json:"city" xorm:"default '' comment('市') VARCHAR(56) 'city'"`
	Countyid      int       `json:"countyid" xorm:"default 'null' comment('县区id') INT 'countyid'"`
	County        string    `json:"county" xorm:"default '' comment('县区') VARCHAR(56) 'county'"`
	Address       string    `json:"address" xorm:"default '' comment('店铺地址') VARCHAR(128) 'address'"`
	Desc          string    `json:"desc" xorm:"default '' comment('店铺简介') VARCHAR(256) 'desc'"`
	CreateTime    time.Time `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime    time.Time `json:"update_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('最后更新时间') DATETIME 'update_time'"`
	ChainId       int       `json:"chain_id" xorm:"default 0 comment('连锁ID') BIGINT 'chain_id'"`
	OrgId         int       `json:"org_id" xorm:"not null default 1 comment('主体id') INT 'org_id'"`
	LocationPoint string    `json:"location_point"` //经纬度
}

type GetStoreListReq struct {
	ChainId   int64 `json:"chain_id"`   //连锁id
	OrgId     int   `json:"org_id"`     //主体id
	UserId    int64 `json:"user_id"`    //线下门店端登录员工id
	ProductId int   `json:"product_id"` //商品id
	StoreId   int   `json:"store_id"`   //店铺id
}

type GetStoreList struct {
	StoreId      string `json:"store_id"`      //店铺id
	StoreName    string `json:"store_name"`    //店铺名称
	IsDistri     int    `json:"is_distri"`     //是否下发 ： 0否 1是
	IsAuthorized int    `json:"is_authorized"` //是否被授权代运营了 ： 0否 1是
}

type GetStoreListRes struct {
	viewmodel.BasePageHttpResponse
	Data []GetStoreList `json:"data"` //门店列表
}

type GetDistriStoreListReq struct {
	ProductId int `json:"product_id"` //商品id

}

type GetDistriStoreListRes struct {
	viewmodel.BaseHttpResponse
	Data []product_po.ProProductStore `json:"data"`
}

type AddOrEditWarehouseReq struct {
	StoreId string `json:"store_id"`                     //店铺id(前端不用传， 取token里的门店id)
	ShopNo  string `json:"shop_no"  validate:"required"` //配送的门店编码
}

type GetWarehouseConfigReq struct {
	StoreId string `json:"store_id"` //店铺id （前端不用传， 取token里的门店id）
}
type GetWarehouseConfigResp struct {
	viewmodel.BaseHttpResponse
	Data omnibus_po.WarehouseDeliveryRelation `json:"data"`
}
type AddOrEditWarehouseResp struct {
	viewmodel.BaseHttpResponse
}

// SyncStoreRegisterReq 同步店铺注册请求
type SyncStoreRegisterReq struct {
	// 主体ID
	OrgId int `json:"org_id" validate:"required"`
	// 店铺ID
	StoreId string `json:"store_id" validate:"required"`
	// 连锁ID
	ChainId string `json:"chain_id"`
	// 店铺名称
	StoreName string `json:"store_name" validate:"required"`
	// 店铺logo
	StoreLogo string `json:"store_logo"`
	// 手机号
	Mobile string `json:"mobile" validate:"required"`
	//身份证号码
	IdCard string `json:"id_card"`
	//分销员身份证正面
	IdcardFront string `json:"idcard_front"`
	//分销员身份证反面
	IdcardReverse string `json:"idcard_reverse"`
	//统一社会信用代码
	SocialCreditCode string `json:"social_credit_code" validate:"required"`
	//统一社会信用代码图片
	SocialCodeImage string `json:"social_code_image" validate:"required"`
	// 企业名称
	EnterpriseName string `json:"enterprise_name"`
	// 企业类型 1-个体工商户 0-企业
	Type int `json:"type"`
	//注册手机号，多个管理员时，校验是否老板手机号一致
	RegisterPhone string `json:"register_phone" validate:"required"`
}

// SyncStoreRegisterResp 同步店铺注册响应
type SyncStoreRegisterResp struct {
	viewmodel.BaseHttpResponse
}

type ShopStoreGetRequest struct {
	//财务编码
	Finance_Code string `protobuf:"bytes,1,opt,name=finance_Code,json=financeCode,proto3" json:"finance_Code"`
	//子龙门店id
	ZilongId string `protobuf:"bytes,2,opt,name=zilong_id,json=zilongId,proto3" json:"zilong_id"`
	//渠道id
	Channel_Id int32 `protobuf:"varint,3,opt,name=channel_Id,json=channelId,proto3" json:"channel_Id"`
	//用户编号
	User_No string `protobuf:"bytes,4,opt,name=user_No,json=userNo,proto3" json:"user_No"`
}

// DeliveryConfigReq 配送方式配置请求
type DeliveryConfigReq struct {
	ID             int    `json:"id"`              // 主键,0表示新增
	DeliveryName   string `json:"delivery_name"`   // 配送类型名称
	AppKey         string `json:"app_key"`         // app_key
	AppSecret      string `json:"app_secret"`      // app_secret
	SourceID       string `json:"source_id"`       // source_id
	DeliveryType   int    `json:"delivery_type"`   // 配送类型 0美配 1闪送 2自配 3达达 4蜂鸟 5顺风
	Code           string `json:"code"`            // 用于获取token的code
	RefreshToken   string `json:"refresh_token"`   // 用于刷新的token
	StoreId        string `json:"store_id"`        // 第三方门店ID
	ChannelID      int    `json:"channel_id"`      // SAAS渠道ID
	FinanceCode    string `json:"finance_code"`    // 财务编码
	DeliveryMethod int    `json:"delivery_method"` // 配送方式 1:外卖平台配送 2:三方配送 3：聚合配送 4：自行配送
	ThirdType      int    `json:"third_type"`      // 1.麦芽田 2花集通 3聚好送 4青云聚信 5美团配送 6猎豹AI
	OrgId          int    `json:"org_id"`          // 主体ID
	ServiceCode    int    `json:"service_code"`    // 服务包ID
	//订单分类(麦芽田枚举)
	Category string `xorm:"varchar(50) comment('订单分类(麦芽田枚举)')" json:"category"`
	//店铺类型 [\"waimai\",\"shop\",\"other\"]
	MytType string `xorm:"varchar(50) comment('店铺类型 [\"waimai\",\"shop\",\"other\"]')" json:"myt_type"`
	//经纬度 ,分割
	Location string `xorm:"varchar(100) comment('经纬度')" json:"location"`
	//店铺名称
	ShopName string `xorm:"varchar(100) comment('店铺名称')" json:"shop_name"`
	//是否需要刷新token
	Refresh int `json:"refresh"`
}

// DeliveryConfigRes 配送方式配置响应
type DeliveryConfigRes struct {
	viewmodel.BaseHttpResponse
}

// GetDeliveryConfigReq 获取配送方式配置请求
type GetDeliveryConfigReq struct {
	FinanceCode string `json:"finance_code" validate:"required"` // 财务编码
	ChannelID   int    `json:"channel_id" validate:"required"`   // SAAS渠道ID
	OrgId       int    `json:"org_id"`                           // 主体ID,从JWT获取
}

// GetDeliveryConfigRes 获取配送方式配置响应
type GetDeliveryConfigRes struct {
	viewmodel.BaseHttpResponse
	Data *omnibus_po.DeliveryConfig `json:"data"` // 配送方式配置信息
}

// SyncEmployeeReq 同步员工信息请求
type SyncEmployeeReq struct {
	// 店铺ID
	StoreId string `json:"store_id" validate:"required"`
	// 主体ID
	OrgId int `json:"org_id" validate:"required"`
	// 员工手机号
	Mobile string `json:"mobile" validate:"required"`
	// 员工姓名
	Name string `json:"name" validate:"required"`
}
