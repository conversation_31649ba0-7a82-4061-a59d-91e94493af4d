package controllers

import (
	po "eShop/domain/points-po"
	"eShop/infra/response"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"net/http"

	"github.com/go-chi/chi"
)

// ClsPointsDailyStatsController 提供了积分每日统计相关的API接口
type ClsPointsDailyStatsController struct {
	SuperController[int, po.ClsPointsDailyStats, vo.ClsPointsDailyStatsSaveVO, vo.ClsPointsDailyStatsUpdateVO, vo.ClsPointsDailyStatsQueryVO, vo.ClsPointsDailyStatsResultVO]
	ControllerHooks[int, po.ClsPointsDailyStats, vo.ClsPointsDailyStatsSaveVO, vo.ClsPointsDailyStatsUpdateVO, vo.ClsPointsDailyStatsQueryVO, vo.ClsPointsDailyStatsResultVO]
	service service.ClsPointsDailyStatsService
}

// NewClsPointsDailyStatsController 创建一个新的 ClsPointsDailyStatsController 实例
func NewClsPointsDailyStatsController() ClsPointsDailyStatsController {
	return ClsPointsDailyStatsController{
		NewSuperController(
			service.NewClsPointsDailyStatsService(),
			&ClsPointsDailyStatsController{},
		),
		NewControllerHooks[int, po.ClsPointsDailyStats, vo.ClsPointsDailyStatsSaveVO, vo.ClsPointsDailyStatsUpdateVO, vo.ClsPointsDailyStatsQueryVO, vo.ClsPointsDailyStatsResultVO](),
		service.NewClsPointsDailyStatsService(),
	}
}

// Routes 定义并注册与积分每日统计相关的路由
func (c ClsPointsDailyStatsController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
	r.Get("/stat", c.GetStats) // 添加新的统计路由
}

// @Summary 获取积分统计数据
// @Description 获取系统中的积分统计信息
// @Tags 积分统计
// @Accept json
// @Produce json
// @Success 200 {object} response.Response[vo.ClsPointsStatVO] "成功获取库存分页数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /points-app/cls-points-stats/stat [get]
func (c ClsPointsDailyStatsController) GetStats(w http.ResponseWriter, r *http.Request) {
	// 获取统计数据
	stats, err := c.service.GetStats(nil)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, stats)
}
