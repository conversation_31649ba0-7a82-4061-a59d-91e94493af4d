package points_po

import "time"

type ClsPointsRuleLog struct {
	SimpleEntity[int] `xorm:"extends"`
	GoodsCode         string    `json:"goods_code" xorm:"'goods_code'"`
	Type              int       `json:"type" xorm:"'type'"`
	BizType           int       `json:"biz_type" xorm:"'biz_type'"`
	Description       string    `json:"description" xorm:"'description'"`
	Operator          string    `json:"operator" xorm:"'operator'"`
	CreatedAt         time.Time `json:"created_at" xorm:"created 'created_at'"`
}

func (e ClsPointsRuleLog) TableName() string {
	return "cls_points_rule_log"
}

func (e ClsPointsRuleLog) AsPointer() any {
	return &e
}
