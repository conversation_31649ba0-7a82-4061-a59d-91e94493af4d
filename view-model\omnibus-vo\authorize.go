package omnibus_vo

type TokenRequest struct {
	//授权方式，此处response_type=token。
	ResponseType string `json:"response_type" form:"response_type" query:"response_type"`
	// APP方门店id 即商家中台系统里门店的编码 is_all_pois与app_poi_codes字段必须且只能填写一个
	AppPoiCode string `json:"app_poi_code,omitempty" form:"app_poi_code" query:"app_poi_code"`
}

type TokenRefreshRequest struct {
	//授权方式，此处response_type=token。
	GrantType string `json:"grant_type" form:"grant_type" query:"grant_type"`
	// APP方门店id 即商家中台系统里门店的编码 is_all_pois与app_poi_codes字段必须且只能填写一个
	AppPoiCode   string `json:"app_poi_code,omitempty" form:"app_poi_code" query:"app_poi_code"`
	RefreshToken string `json:"refresh_token"` // 用于刷新access token的refresh token
}

type TokenResponse struct {
	Status       int64  `json:"status"`        // 请求成功或失败，参考错误码
	AccessToken  string `json:"access_token"`  // 获取的access token
	ExpiresIn    int64  `json:"expires_in"`    // 过期时间，单位秒
	RefreshToken string `json:"refresh_token"` // 用于刷新access token的refresh token
	ReExpiresIn  int64  `json:"re_expires_in"` // 过期时间，单位秒
	State        string `json:"state"`         // 请求时传递了该参数，则会原样返回
	Message      string `json:"message"`       // 错误描述
}
