package points_vo

import (
	po "eShop/domain/points-po"
)

// ClsPointsAccountSaveVO 用于创建积分账户的视图对象
type ClsPointsAccountSaveVO struct {
	SuperSaveVO[po.ClsPointsAccount]
	DisId        int `json:"dis_id" validate:"required"`
	EnterpriseId int `json:"enterprise_id" validate:"required"`
}

// ClsPointsAccountUpdateVO 用于更新积分账户的视图对象
type ClsPointsAccountUpdateVO struct {
	SuperUpdateVO[po.ClsPointsAccount]
	Id              int `json:"id" validate:"required"`
	AvailablePoints int `json:"available_points"`
	TotalPoints     int `json:"total_points"`
	ConsumePoints   int `json:"consume_points"`
	ExpiredPoints   int `json:"expired_points"`
	ExpiringPoints  int `json:"expiring_points"`
}

// ClsPointsAccountQueryVO 用于查询积分账户的视图对象
type ClsPointsAccountQueryVO struct {
	SuperQueryVO[po.ClsPointsAccount]
	DisId        int `json:"dis_id" query:"eq" validate:"required"`
	EnterpriseId int `json:"enterprise_id" query:"eq"`
}

// ClsPointsAccountResultVO 用于返回积分账户结果的视图对象
type ClsPointsAccountResultVO struct {
	SuperResultVO[ClsPointsAccountResultVO]
	Id               int `json:"id"`
	DisId            int `json:"dis_id"`
	EnterpriseId     int `json:"enterprise_id"`
	AvailablePoints  int `json:"available_points"`
	TotalPoints      int `json:"total_points"`
	ConsumePoints    int `json:"consume_points"`
	ExpiredPoints    int `json:"expired_points"`
	ExpiringPoints   int `json:"expiring_points"`
	EnterprisePoints int `json:"enterprise_points"` // 企业积分
}

type DistributorVO struct {
	DisId         int    `json:"dis_id"`
	EnterpriseId  int    `json:"enterprise_id"`
	DisRole       int    `json:"dis_role"`
	RealName      string `json:"real_name"`
	EncryptMobile string `json:"encrypt_mobile"`
}

type ClsPointsAccountBatchUpdateVO struct {
	SaveVO []ClsPointsAccountSaveVO `json:"save_vo"`
}

type ClsPointsGiveVO struct {
	// 赠送人dis_id
	FromDisId int `json:"from_dis_id" validate:"required"`
	// 接受人dis_id
	ToDisId int `json:"to_dis_id" validate:"required"`
	// 赠送积分数
	Points int `json:"points" validate:"required"`
}

type RegisterClsWorkerVO struct {
	StaffNo string `json:"staff_no"` // 员工编号
}
