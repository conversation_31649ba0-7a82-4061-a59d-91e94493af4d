package controllers

import (
	"context"
	jwt "eShop/infra/jwtauth"
	"eShop/infra/response"
	"eShop/infra/utils"
	iobound "eShop/services/inventory-service/iobound"
	baseVO "eShop/view-model/inventory-vo"
	inventoryVO "eShop/view-model/inventory-vo/inventory"
	vo "eShop/view-model/inventory-vo/iobound"
	"net/http"

	"github.com/go-chi/chi/v5"
)

type IoBoundController struct {
	service iobound.IoBoundService
}

// NewIoBoundController 创建库存控制器
func NewIoBoundController(service iobound.IoBoundService) *IoBoundController {
	return &IoBoundController{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (c IoBoundController) RegisterRoutes(r chi.Router) {
	r.Route("/inventory-app/io-bound", func(r chi.Router) {
		r.Post("/init-inventory", c.InitInventory)
		r.Post("/create", c.Create)
		r.Post("/page", c.Page)
		r.Get("/detail", c.Detail)
		r.Post("/freeze", c.Freeze)
		r.Post("/unfreeze", c.UnFreeze)
		r.Post("/order-out", c.OrderOut)
		r.Post("/refund-in", c.RefundIn)
	})

	r.Route("/inventory-app/io-bound-detail", func(r chi.Router) {
		r.Post("/page", c.DetailPage)
		r.Post("/summary", c.DetailSummary)
	})
}

func (c IoBoundController) InitInventory(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[inventoryVO.InventoryInitCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	_, err = c.service.InitInventory(context.Background(), nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Create 创建库存
// @Summary 创建库存
// @Description 创建新的库存记录
// @Tags 出入库
// @Accept json
// @Produce json
// @Param cmd body vo.IoBoundCreateCommand true "出入库信息"
// @Success 200 {object} response.BaseResp "成功创建库存"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound/create [post]
func (c IoBoundController) Create(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.IoBoundCreateCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	cmd.ChainId = jwt.CtxGet[int64](r.Context(), "ChainId")
	cmd.StoreId = jwt.CtxGet[string](r.Context(), "TenantId")
	cmd.Operator = jwt.CtxGet[string](r.Context(), "UserName")
	_, err = c.service.Create(r.Context(), nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Page 分页列表
// @Summary 分页列表
// @Description 分页列表
// @Tags 出入库
// @Accept json
// @Produce json
// @Param cmd body vo.IoBoundPageRequest true "分页请求"
// @Success 200 {object} response.Response[[]vo.IoBoundResponse] "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound/page [post]
func (c IoBoundController) Page(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.IoBoundPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.service.Page(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// Detail 出入库详情
// @Summary 出入库详情
// @Description 详情
// @Tags 出入库
// @Accept json
// @Produce json
// @Param cmd body baseVO.IdRequest true "详情请求"
// @Success 200 {object} response.Response[vo.IoBoundResponse] "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound/detail [get]
func (c IoBoundController) Detail(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[baseVO.IdRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	detail, err := c.service.Detail(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, detail)
}

// Freeze 冻结库存
// @Summary 冻结库存
// @Description 冻结库存
// @Tags 库存
// @Accept json
// @Produce json
// @Param inventory body vo.OutBoundCommand true "冻结库存请求"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound/freeze [post]
func (c IoBoundController) Freeze(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.OutBoundCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	_, err = c.service.Freeze(r.Context(), nil, &cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// UnFreeze 解冻库存
// @Summary 解冻库存
// @Description 解冻库存
// @Tags 库存
// @Accept json
// @Produce json
// @Param inventory body vo.UnFreezeCommand true "解冻库存请求"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound/unfreeze [post]
func (c IoBoundController) UnFreeze(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.UnFreezeCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	_, err = c.service.UnFreeze(r.Context(), nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// OrderOut 订单出库
// @Summary 订单出库
// @Description 订单出库
// @Tags 出入库
// @Accept json
// @Produce json
// @Param cmd body vo.OutBoundCommand true "订单出库请求"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound/order-out [post]
func (c IoBoundController) OrderOut(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.OutBoundCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	_, err = c.service.OrderOut(r.Context(), nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// RefundIn 销售订单退货入库
// @Summary 销售订单退货入库
// @Description 销售订单退货入库
// @Tags 出入库
// @Accept json
// @Produce json
// @Param cmd body vo.InBoundCommand true "销售订单退货入库请求"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound/refund-in [post]
func (c IoBoundController) RefundIn(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.InBoundCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	_, err = c.service.RefundIn(r.Context(), nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// DetailPage 库存详情
// @Summary 库存详情
// @Description 库存详情
// @Tags 出入库详情
// @Accept json
// @Produce json
// @Param cmd body vo.IoBoundDetailPageRequest true "请求参数"
// @Success 200 {object} response.Response[[]vo.IoBoundDetailResponse] "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound-detail/page [post]
func (c IoBoundController) DetailPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.IoBoundDetailPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.service.DetailPage(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// DetailSummary 分页列表统计
// @Summary 分页列表统计
// @Description 分页列表统计
// @Tags 出入库详情
// @Accept json
// @Produce json
// @Param cmd body vo.IoBoundDetailPageRequest true "请求参数"
// @Success 200 {object} response.Response[int] "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/io-bound-detail/summary [post]
func (c IoBoundController) DetailSummary(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.IoBoundDetailPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	summary, err := c.service.DetailSummary(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, summary)
}
