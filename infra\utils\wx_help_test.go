package utils

import "testing"

func TestGetQrImage(t *testing.T) {
	type args struct {
		path string
		sid  string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "生成二维码"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetQrImage("pages/tabs/mall", "id=3", 3, 1, 0)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetQrImage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.<PERSON><PERSON>rf("GetQrImage() got = %v, want %v", got, tt.want)
			}
		})
	}
}
