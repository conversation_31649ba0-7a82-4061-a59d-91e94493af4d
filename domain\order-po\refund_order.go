package order_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// 订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款
const (
	RefundStatePending     = 1
	RefundStateClosed      = 2
	RefundStateSuccess     = 3
	RefundStateFirstAudit  = 6
	RefundStateSecondAudit = 7
	RefundStateAuditFail   = 8
	RefundStateCancel      = 9
)

type RefundOrder struct {
	Id                  int       `xorm:"not null pk autoincr comment('自增主键') INT(11)"`
	OldRefundSn         string    `xorm:"not null default '''' comment('渠道退款单号') VARCHAR(50)"`
	RefundSn            string    `xorm:"not null default '''' comment('退款单号') unique VARCHAR(50)"`
	OrderSn             string    `xorm:"not null default '''' comment('原始订单号') index VARCHAR(50)"`
	OldOrderSn          string    `xorm:"not null default '''' comment('美团订单号（原美团订单号）') index VARCHAR(50)"`
	FullRefund          int       `xorm:"not null default 1 comment('部分退还是整单退，1整单 2部分') INT(11)"`
	RefundTypeSn        string    `xorm:"default 'NULL' comment('售后单类型 JustRefund=仅退款 RefundAndGoods=退款退货') VARCHAR(50)"`
	ReasonCode          string    `xorm:"not null default ''01'' comment('前段没有判定，管易:007，全渠道:01') VARCHAR(50)"`
	RefundRemark        string    `xorm:"not null default '''' comment('售后单备注') VARCHAR(200)"`
	RefundType          int       `xorm:"not null default 1 comment('申请类型:1为仅退款,2为退款退货') INT(11)"`
	RefundReason        string    `xorm:"not null default '''' comment('退款原因') VARCHAR(500)"`
	DiscountAmount      string    `xorm:"not null default ''0'' comment('优惠金额，单位元') VARCHAR(20)"`
	Freight             string    `xorm:"not null default ''0'' comment('运费，单位元') VARCHAR(20)"`
	RefundAmount        float64   `xorm:"not null default ''0'' comment('退款金额，单位元') VARCHAR(20)"`
	WarehouseCode       string    `xorm:"not null default '''' comment('对应仓库id') VARCHAR(50)"`
	ShopId              string    `xorm:"not null default '''' comment('门店财务编码') VARCHAR(50)"`
	ExpressName         string    `xorm:"not null default '''' comment('退货快递名称') VARCHAR(100)"`
	ExpressNum          string    `xorm:"default 'NULL' comment('退货快递单号') VARCHAR(50)"`
	OrderSource         int       `xorm:"not null default 1 comment('仓库所属1:(a8 or 全渠道)  2:管易  3:门店') INT(11)"`
	TradeCode           string    `xorm:"not null default '''' comment('管易销售订单单据编号') VARCHAR(50)"`
	RefundState         int       `xorm:"default 1 comment('订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败,9:撤销退款') INT(11)"`
	ApplyOpUserType     string    `xorm:"not null default '''' comment('发起退款角色(仅适用于支持退货退款的商家),1-用户,2-商家,3-客服') VARCHAR(100)"`
	ChannelId           int       `xorm:"not null default 0 comment('渠道id（datacenter.platform_channel表）1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店') INT(11)"`
	IsCancelOrder       int       `xorm:"not null default 0 comment('京东特有字段,1取消订单，0售后订单') INT(11)"`
	ExpectRefundTime    int       `xorm:"not null default 0 comment('预计退款时间') index INT(10)"`
	IsVirtual           int       `xorm:"not null default 0 comment('是否是虚拟订单，0否1是') TINYINT(4)"`
	DelExpressInfo      string    `xorm:"not null default '''' comment('快递信息，JSON格式') VARCHAR(500)"`
	CreateTime          string    `xorm:" default 'current_timestamp()' comment('创建时间') DATETIME created"`
	UpdateTime          string    `xorm:" default 'current_timestamp()' comment('更新时间') DATETIME updated"`
	AppChannel          int       `xorm:"not null default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
	PayRefundId         string    `xorm:"not null default '''' comment('支付中心退款单号') VARCHAR(100)"`
	PayRefundTranNo     string    `xorm:"not null default '''' comment('支付中心退款交易流水号') VARCHAR(100)"`
	PushThird           int       `xorm:"not null default 0 comment('是否推送第三方0没推、1推了') TINYINT(4)"`
	PushThirdFailReason string    `xorm:"not null default '''' comment('推送失败原因') VARCHAR(256)"`
	RefundedTime        string    `xorm:" default 'NULL' comment('退款完成时间') DATETIME"`
	ActivityPtAmount    string    `xorm:" default 'NULL' comment('退款单平台优惠金额') VARCHAR(256)"`
	DeliveryPrice       string    `xorm:" default 'NULL' comment('本次运费退款金额') VARCHAR(256)"`
	ServiceType         string    `xorm:"default '''' comment('区分是否已开通退货退款售后业务。 未开通的场景： 0-退款流程或申诉流程 已开通场景： 1-仅退款流程 2-退款退货流程') VARCHAR(10)"`
	IsPicking           int       `json:"is_picking" xorm:"not null default 0 comment('是否处理,0否1是') TINYINT"`
	ProcessTime         time.Time `json:"process_time" xorm:"comment('处理时间') DATETIME"`
	Operator            string    `json:"operator" xorm:"not null default '' comment('操作人') VARCHAR(50)"`
}

func (r RefundOrder) TableName() string {
	return "dc_order.refund_order"
}

// 拓展方法
type RefundOrderExtend struct {
	RefundOrder `xorm:"extends"`
	// 自提点编码
	PickupCode string `json:"pickup_code"`
	// 仓库id
	WarehouseId int `json:"warehouse_id"`
	// 仓库名称
	WarehouseName string `json:"warehouse_name"`
	// 连锁id
	ChainId int `json:"chain_id"`
}

type RefundOrderRequest struct {
	OrderSn  string   `json:"order_sn"`  // 订单编号
	OrderSns []string `json:"order_sns"` // 订单编号列表
	Fields   string   `json:"fields"`    // 查询字段
}

// out1=map[orderSn][]*RefundOrder 订单退款单列表
// out2=map[refundSn]*RefundOrder 退款单
func (r *RefundOrder) GetRefundOrder(session *xorm.Session, req RefundOrderRequest) (out []*RefundOrder, out1 map[string][]*RefundOrder, out2 map[string]*RefundOrder, err error) {
	out = make([]*RefundOrder, 0)

	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.refund_order").Select(req.Fields)
	if req.OrderSn != "" {
		session = session.Where("order_sn = ?", req.OrderSn)
	}
	if len(req.OrderSns) > 0 {
		session = session.In("order_sn", req.OrderSns)
	}

	err = session.Find(&out)

	if err != nil {
		return nil, nil, nil, err
	}

	out1 = make(map[string][]*RefundOrder)
	out2 = make(map[string]*RefundOrder)

	for _, refund := range out {
		out1[refund.OrderSn] = append(out1[refund.OrderSn], refund)
		out2[refund.RefundSn] = refund
	}
	return out, out1, out2, nil
}

// Create 创建退款单
func (r *RefundOrder) Create(session *xorm.Session, data *RefundOrder) error {
	_, err := session.Insert(data)
	if err != nil {
		log.Errorf("创建退款单失败, err: %v", err)
		return err
	}
	return nil
}

type RefundOrderParams struct {
	// 退回方式 1:原路退回 2:指定退回
	RefundType int `json:"refund_type"`
	// 退款原因
	RefundReason string `json:"refund_reason"`
	// 退款备注
	Remark string `json:"remark"`
	// 退款金额
	RefundAmount int `json:"refund_amount"`
	// 类型 1:整单退款 2:部分退款
	Type int `json:"type"`
	// 退货类型 1-退货退款 2-仅退款
	ReType int `json:"re_type"`
	//操作人
	Operator string `json:"operator"`
}

type RefundProduct struct {
	OrderProductId int `json:"order_product_id"` // 订单商品ID
	RefundNum      int `json:"refund_num"`       // 退款数量
	RefundAmount   int `json:"refund_amount"`    // 退款金额(分)
}

// CreateRefundOrder 创建退款单
func (r *RefundOrder) CreateRefundOrder(session *xorm.Session, order OrderMainView, params RefundOrderParams, refundSn string) (*RefundOrder, error) {
	refundTypeSn := "仅退款"
	if params.ReType == 2 {
		refundTypeSn = "退货退款"
	}
	// 构建退款单
	refundOrder := &RefundOrder{
		RefundSn:        refundSn,
		OrderSn:         order.OrderSn,
		OldOrderSn:      order.OrderSn,
		RefundType:      params.ReType, // 使用请求中的退款类型
		FullRefund:      params.Type,   // 整单退
		RefundTypeSn:    refundTypeSn,
		RefundReason:    params.RefundReason,
		RefundRemark:    params.Remark,
		RefundAmount:    utils.Fen2Yuan(params.RefundAmount),
		ShopId:          order.ShopId,
		RefundState:     RefundStateSuccess, // 退款状态
		ChannelId:       order.ChannelId,
		ApplyOpUserType: "2", // 商家发起
		OrderSource:     order.Source,
		AppChannel:      order.AppChannel,
		IsVirtual:       order.IsVirtual,
		RefundedTime:    time.Now().Format("2006-01-02 15:04:05"),
		Operator:        params.Operator,
	}

	// 创建退款单
	if err := r.Create(session, refundOrder); err != nil {
		log.Errorf("创建退款单失败,退款单号: %s, err: %v", refundSn, err)
		return nil, fmt.Errorf("创建退款单失败: %v", err)
	}

	return refundOrder, nil
}

// 新增更新方法
func (r *RefundOrder) UpdatePickingStatus(session *xorm.Session, refundSn string, operator string) error {
	location, err := time.LoadLocation("Asia/Shanghai") // 根据需要设置时区
	if err != nil {
		log.Errorf("加载时区失败: %v", err)
		return err
	}
	if _, err := session.Table(r.TableName()).
		Where("refund_sn = ?", refundSn).
		Update(map[string]interface{}{
			"is_picking":   1,
			"process_time": time.Now().In(location), // 使用指定时区的当前时间
			"operator":     operator,
		}); err != nil {
		log.Errorf("更新退款单处理状态失败,退款单号: %s, err: %v", refundSn, err)
		return err
	}
	return nil
}
