package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	distribution_po "eShop/domain/distribution-po"
	"eShop/domain/external-po/offline"
	inventory_po "eShop/domain/inventory-po/inventory"
	"eShop/domain/inventory-po/location"
	warehouse_po "eShop/domain/inventory-po/warehouse"
	marketing_po "eShop/domain/marketing-po"
	omnibus_po "eShop/domain/omnibus-po"
	product_po "eShop/domain/product-po"
	"eShop/infra/enum"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	mq "eShop/infra/mq"
	"eShop/infra/utils"
	"eShop/services/common"
	omnibus_service "eShop/services/omnibus-service/services"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	omnibus_vo "eShop/view-model/omnibus-vo"
	product_vo "eShop/view-model/product-vo"

	"path/filepath"

	"golang.org/x/sync/errgroup"
	"xorm.io/xorm"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2" // 使用 excelize 库生成 Excel 文件
)

type StoreProductService struct {
	common.BaseService
	JwtInfo *jwtauth.XCShopPayload
}

type StoreProductServiceData struct {
	Engine     *xorm.Engine
	SkuIds     []int
	ChannelIds []int
	Params     product_vo.BatchStoreProductReq
	// 需要上架的门店财务代码-必须
	FinanceCodes []string
	// 商品与门店财务代码的关联关系
	StoreProducts []*product_po.ProProductStoreAppChannel
	// 上架处理结果
	UpResult []*ChannelProductUp_Result
	// 是否出现了未捕获的异常
	UnknownError error
	TaskId       int
	PriceArr     map[int]int
}

// 上架执行结果
type ChannelProductUp_Result struct {
	// 门店ID
	StoreId   string
	SkuId     int
	ChannelId int
	// 是否成功
	IsSuccess bool
	// 信息
	Message string
}

// 支持sku批量,以英文逗号拼接
// 操作类型 1-铺品 2-上架 3-下架 4库存同步  5-商品调价 6-删除
func (s StoreProductService) BatchStoreProduct(in product_vo.BatchStoreProductReq) (res *StoreProductServiceData, err error) {
	log.Infof("StoreProductService BatchStoreProduct 入参：%s", utils.JsonEncode(in))
	defer func() {
		if err != nil {
			log.Errorf("StoreProductService BatchStoreProduct 入参：%s ,返回:%s", utils.JsonEncode(in), err)
		}
	}()

	if in.RoleType == 1 && in.Type != 4 && (strings.Contains(in.ChannelIds, "2") || strings.Contains(in.ChannelIds, "3")) {
		return nil, errors.New("操作权限不足")
	}

	// 解析渠道 IDs
	var channelIdstr []string
	if in.ChannelIds != "" {
		channelIdstr = strings.Split(in.ChannelIds, ",")
	}
	var ChannelIds []int
	if len(channelIdstr) > 0 {
		for _, cid := range channelIdstr {
			ChannelIds = append(ChannelIds, cast.ToInt(cid))
		}
	}

	s.Begin()
	defer s.Close()
	// 解析 SKU IDs
	var SkuIds []int
	if in.SkuIds != "" {
		// 解析SKU IDs
		SkuIds = parseCommaSeparatedInts(in.SkuIds)
		var productIds []int
		if err = s.Engine.Table("eshop.pro_sku").
			In("id", SkuIds).
			Cols("product_id").
			Find(&productIds); err != nil {
			return nil, errors.New("查询商品SPU失败")
		}
		// 添加缺失的商品数据
		if in.Type != proproductstoreinfo.ProductTypeDel {
			s.addMissingProducts(productIds, in.TenantId)
		}
	} else if in.ProductIds != "" {
		// 解析Product IDs
		productIdInts := parseCommaSeparatedInts(in.ProductIds)
		if in.Type != proproductstoreinfo.ProductTypeDel {
			s.addMissingProducts(productIdInts, in.TenantId)
		}
		// 查询相关的SKU IDs
		if err = s.Engine.Table("eshop.pro_product_store_info").
			Where("store_id = ?", in.TenantId).
			In("product_id", productIdInts).
			Select("/*FORCE_MASTER*/ sku_id").
			GroupBy("sku_id").
			Find(&SkuIds); err != nil {
			return nil, errors.New("查询商品SKU失败")
		}
		// 如果 SKU 数量大于 2，使用异步任务处理
		if len(productIdInts) >= 1 && in.SyncType == 1 {
			return s.handleAsyncTask(productIdInts, ChannelIds, in)
		}
	} else {
		return nil, errors.New("no SKU IDs provided")
	}

	var channelPrices map[int]int
	if in.Type == 5 && in.PriceType == 2 {
		//获取渠道值：渠道1-价格1|渠道2-价格2（1-10|2-11）
		channelPrices, ChannelIds, err = parseChannelPrices(in.ChannlePriceStr)
		if err != nil {
			return nil, errors.New("调价输入有误")
		}
		if len(channelPrices) == 0 {
			return nil, errors.New("调价输入有误")
		}
	} else if in.PriceType == 1 {
		if in.RoleType == 2 {
			ChannelIds = []int{common.ChannelIdMT, common.ChannelIdELM}
		} else if in.RoleType == 1 {
			ChannelIds = []int{common.ChannelIdWeChatApp, common.ChannelIdOfflineShop}
		} else {
			ChannelIds = []int{common.ChannelIdWeChatApp, common.ChannelIdOfflineShop, common.ChannelIdMT, common.ChannelIdELM}
		}
	}

	serv := &StoreProductServiceData{
		Engine:     s.Engine,
		SkuIds:     SkuIds,
		ChannelIds: ChannelIds,
		Params:     in,
		PriceArr:   channelPrices,
	}
	if err = serv.GetProduct(ChannelIds, SkuIds); err != nil {
		return nil, errors.New(err.Error())
	}

	// 批量操作
	err = serv.syncToThird()

	//返回错误提示信息
	var ErrMsg []string
	for _, v := range serv.UpResult {
		if !v.IsSuccess {
			ErrMsg = append(ErrMsg, v.Message)
		}
	}
	if len(ErrMsg) > 0 {
		return serv, errors.New(strings.Join(ErrMsg, ";"))
	}

	return serv, err
}

// addMissingProducts 添加缺失的商品数据
func (s *StoreProductService) addMissingProducts(productIds []int, storeId string) {
	session := s.Engine.NewSession()
	defer session.Close()
	if err := new(ProductService).AddProductStoreData(product_vo.AddProductStoreDataReq{
		ProductIds: productIds,
		StoreIds:   []string{storeId},
		Session:    session,
	}); err != nil {
		log.Errorf("添加商品SPU数据失败, productId: %s, error: %v", utils.JsonEncode(productIds), err)
	}
}

// parseCommaSeparatedInts 解析逗号分隔的整数字符串
func parseCommaSeparatedInts(str string) []int {
	if str == "" {
		return nil
	}

	strIds := strings.Split(str, ",")
	ids := make([]int, 0, len(strIds))
	for _, id := range strIds {
		ids = append(ids, cast.ToInt(id))
	}
	return ids
}

// 获取商品信息
func (s *StoreProductServiceData) GetProduct(ChannelIds, SkuIds []int) error {
	session := s.Engine.NewSession()
	defer session.Close()
	var data = make([]*product_po.ProProductStoreAppChannel, 0)
	db := s.Engine.Table("eshop.pro_product_store_spu").Alias("b")
	db.Select(`b.*,p.name,cc.name as category_name,p.chain_id,p.new_sell,p.new_sell_str,p.selling_point,p.pic,p.content_pc,
        pc.category_third_id,pc.category_third_name`).
		Join("left", "eshop.pro_product p", "b.product_id = p.id").
		Join("left", "eshop.pro_product_channel pc", "b.product_id = pc.product_id and b.channel_id = pc.channel_id").
		Join("left", "eshop.pro_category cc", "cc.id = b.channel_category_id").
		In("b.store_id", s.Params.TenantId)

	// 2. 根据传入参数添加查询条件
	if s.Params.ProductIds != "" {
		productIds := parseCommaSeparatedInts(s.Params.ProductIds)
		db.In("b.product_id", productIds)
	} else if len(SkuIds) > 0 {
		// 如果只传入SkuIds，先查询对应的ProductIds
		var productIds []int
		if err := s.Engine.Table("eshop.pro_sku").
			Select("DISTINCT product_id").
			In("id", SkuIds).
			Find(&productIds); err != nil {
			return errors.New("获取商品ID失败")
		}
		db.In("b.product_id", productIds)
	}

	// 3. 根据不同场景添加查询条件
	if s.Params.Type == proproductstoreinfo.ProductTypeDel {
		//判断是代运营,只是查询美团、饿了么渠道 2,3
		//被代运营只能删除门店渠道跟小程序渠道 1,100
		if len(ChannelIds) > 0 {
			db.In("b.channel_id", ChannelIds)
		} else if s.Params.RoleType == 2 {
			db.In("b.channel_id", []int{common.ChannelIdMT, common.ChannelIdELM})
		} else if s.Params.RoleType == 1 {
			db.In("b.channel_id", []int{common.ChannelIdWeChatApp, common.ChannelIdOfflineShop})
		}
	} else {
		db.In("b.channel_id", ChannelIds)
	}

	if err := db.Find(&data); err != nil {
		log.Errorf("获取商品数据失败: %s", err.Error())
		return errors.New("获取商品数据失败")
	}

	if len(data) == 0 {
		return errors.New("商品未设置操作渠道")
	}

	// 6. 查询SKU信息
	var skuList []product_po.ProProductStoreInfoExt
	skuQuery := s.Engine.Table("eshop.pro_product_store_info").Alias("psi")
	skuQuery.Select(`psi.*,s.store_unit,s.bar_code as sku_upc,s.weight_for_unit,s.product_specs`).
		Join("LEFT", "eshop.pro_sku s", "s.id = psi.sku_id").
		Where("psi.store_id = ?", s.Params.TenantId)

	// 根据传入参数添加查询条件
	if len(SkuIds) > 0 {
		skuQuery.In("psi.sku_id", SkuIds)
	} else if s.Params.ProductIds != "" {
		productIds := parseCommaSeparatedInts(s.Params.ProductIds)
		skuQuery.In("psi.product_id", productIds)
	}

	// 添加渠道条件
	if s.Params.Type == proproductstoreinfo.ProductTypeDel {
		if len(ChannelIds) > 0 {
			db.In("psi.channel_id", ChannelIds)
		} else if s.Params.RoleType == 2 {
			skuQuery.In("psi.channel_id", []int{common.ChannelIdMT, common.ChannelIdELM})
		} else if s.Params.RoleType == 1 {
			skuQuery.In("psi.channel_id", []int{common.ChannelIdWeChatApp, common.ChannelIdOfflineShop})
		}
	} else {
		skuQuery.In("psi.channel_id", ChannelIds)
	}

	//// 操作只铺品的商品
	if s.Params.Type != proproductstoreinfo.ProductTypeDel && s.Params.Type > 1 && s.Params.Type < 7 {
		skuQuery.Where("psi.is_distribution = 1")
	} else if s.Params.Type == proproductstoreinfo.ProductTypeLaunch {
		skuQuery.Where("psi.is_distribution = 0")
	}

	if err := skuQuery.Find(&skuList); err != nil {
		return errors.New("获取商品sku信息失败")
	}

	// 7. 构建SKU映射
	skuMap := make(map[string][]product_po.ProProductStoreInfoExt) // key: productId_channelId
	for _, sku := range skuList {
		key := fmt.Sprintf("%d_%d", sku.ProductId, sku.ChannelId)
		skuMap[key] = append(skuMap[key], sku)
	}

	// 8. 获取所有商品ID并去重
	productIdMap := make(map[int]struct{})
	var productIds []int
	for _, product := range data {
		if _, exists := productIdMap[product.ProductId]; !exists {
			productIds = append(productIds, product.ProductId)
			productIdMap[product.ProductId] = struct{}{}
		}
	}

	// 9. 查询商品渠道属性信息
	var proProductChannelAttr product_po.ProProductChannelAttr
	_, ProductChannelAttrInfo, err := proProductChannelAttr.GetProductChannelAttrInfo(session, product_po.ProductChannelAttrReq{
		ProductIds: productIds,
		ChannelIds: ChannelIds,
		OutType:    1,
	})
	if err != nil {
		return errors.New("获取商品渠道属性信息失败")
	}
	for _, product := range data {
		key := fmt.Sprintf("%d_%d", product.ProductId, product.ChannelId)
		if sku, ok := skuMap[key]; ok {
			product.Skus = sku
		}
		if attr, has := ProductChannelAttrInfo[product.ProductId][product.ChannelId]; has {
			product.Attr = attr
		}
	}

	s.StoreProducts = data
	return nil
}

func (s *StoreProductServiceData) syncToThird() error {
	switch s.Params.Type {
	case proproductstoreinfo.ProductTypeLaunch, proproductstoreinfo.ProductTypeUpdate: //创建、编辑商品
		return s.syncShelveToAPI()
	case proproductstoreinfo.ProductTypeUp: //上架
		return s.syncUpToAPI()
	case proproductstoreinfo.ProductTypeDown: //下架
		return s.syncDownToAPI()
	case proproductstoreinfo.ProductTypeStock: //库存同步
		return s.syncInventoryToAPI()
	case proproductstoreinfo.ProductTypePrice: //调价
		return s.syncPriceAdjustmentToAPI()
	case proproductstoreinfo.ProductTypeDel:
		return s.syncDeleteToAPI()
	default:
		return errors.New("请求参数类型有误")
	}
}

func (s *StoreProductServiceData) syncShelveToAPI() error {

	for _, storeProduct := range s.StoreProducts {
		if len(storeProduct.Skus) == 0 {
			continue
		}
		var (
			data = &product_po.ProProductStoreInfo{}
		)
		var upDownResult = &ChannelProductUp_Result{
			SkuId:     storeProduct.ProductId,
			ChannelId: storeProduct.ChannelId,
			StoreId:   storeProduct.StoreId,
			IsSuccess: true,
		}
		s.UpResult = append(s.UpResult, upDownResult)

		if !s.checkNewSellStatus(storeProduct, upDownResult) {
			continue
		}

		// 当编辑商品时，检查是否有铺品成功的SKU
		if s.Params.Type == proproductstoreinfo.ProductTypeUpdate {
			hasPushedSku := false
			for _, sku := range storeProduct.Skus {
				if sku.IsDistribution == 1 {
					hasPushedSku = true
					break
				}
			}
			if !hasPushedSku {
				upDownResult.Message = fmt.Sprintf("商品product_id:%d没有铺品成功的SKU", storeProduct.ProductId)
				upDownResult.IsSuccess = false
				continue
			}
		}
		//判断是storeProduct.ChannelId 否在 storeProduct.NewSellStr里，有则走后面的逻辑
		if s.Params.RoleType == 2 && !strings.Contains(storeProduct.NewSellStr, strconv.Itoa(storeProduct.ChannelId)) {
			log.Infof("创建编辑,请求参数,params:%s,不可售", utils.JsonEncode(storeProduct))
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s不可售", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
			upDownResult.IsSuccess = false
			continue
		}

		switch storeProduct.ChannelId {
		case common.ChannelIdWeChatApp:
			_, _, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
			if !state {
				log.Infof("创建编辑,请求参数,params:%s,门店信息未设置", utils.JsonEncode(storeProduct))
				upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:门店信息未设置", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
				upDownResult.IsSuccess = false
				continue
			}
		case common.ChannelIdMT:
			if err := s.UpdateMtProduct(storeProduct); err != nil {
				data.SyncError = err.Error()
			}
		case common.ChannelIdELM: //饿了么
			if err := s.UpdateElmProduct(storeProduct); err != nil {
				data.SyncError = err.Error()
			}
		}

		//更新铺品状态
		if data.SyncError != "" {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:%s", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId], data.SyncError)
			upDownResult.IsSuccess = false
		}

		// 更新铺品状态
		if err := new(product_po.ProProductStoreInfo).UpdateShelveStatus(s.Engine, storeProduct, data.SyncError); err != nil {
			log.Errorf("更新铺品状态失败: %v", err)
		}
	}
	return nil
}

func (s *StoreProductServiceData) syncUpToAPI() error {
	var syncStockDatas = make([]omnibus_vo.SyncStockData, 0)

	for _, storeProduct := range s.StoreProducts {
		var data = &product_po.ProProductStoreInfo{}
		var upDownResult = &ChannelProductUp_Result{
			SkuId:     storeProduct.ProductId,
			ChannelId: storeProduct.ChannelId,
			StoreId:   storeProduct.StoreId,
			IsSuccess: true,
		}
		s.UpResult = append(s.UpResult, upDownResult)
		if len(storeProduct.Skus) == 0 {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s没有铺品成功的SKU", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
			upDownResult.IsSuccess = false
			continue
		}

		switch storeProduct.ChannelId {
		case common.ChannelIdWeChatApp:
			_, _, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
			if !state {
				log.Infof("上架商品,请求参数,params:%s,门店信息未设置", utils.JsonEncode(storeProduct))
				upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s门店信息未设置", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
				upDownResult.IsSuccess = false
				continue
			}
		case common.ChannelIdMT:
			if err := s.RetailSellStatus(storeProduct, 0); err != nil {
				data.SyncError = err.Error()
			} else {
				syncStockDatas = append(syncStockDatas, omnibus_vo.SyncStockData{
					StoreId:   storeProduct.StoreId,
					ChannelId: storeProduct.ChannelId,
					SkuId:     int64(storeProduct.Skus[0].SkuId),
					ProductId: storeProduct.ProductId,
				})
			}
		case common.ChannelIdELM:
			if err := s.OnlineElmShopSkuOne(storeProduct, 1); err != nil {
				data.SyncError = err.Error()
			} else {
				go func(storeProduct *product_po.ProProductStoreAppChannel) {
					err = s.UpdateElmSkuSpecStock(storeProduct)
				}(storeProduct)
			}

		case common.ChannelIdOfflineShop:
			continue
		default:
			log.Errorf("上架商品，渠道设置错误,params：%s", utils.JsonEncode(storeProduct))
			data.SyncError = "渠道设置错误"
		}
		// 更新商品上架状态
		if data.SyncError != "" {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:%s", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId], data.SyncError)
			upDownResult.IsSuccess = false
		}

		// 更新上架状态
		if err := new(product_po.ProProductStoreInfo).UpdateUpDownStatus(s.Engine, storeProduct, data.SyncError, proproductstoreinfo.UpDownStateUp); err != nil {
			log.Errorf("更新上架状态失败: %v", err)
		}
	}
	//上架成功，同步库存到mq
	if len(syncStockDatas) > 0 {
		log.Infof("上架商品，同步库存到mq,params：%s", utils.JsonEncode(syncStockDatas))
		go func() {
			new(omnibus_service.InventoryService).SyncStock(syncStockDatas)
		}()
	}

	return nil
}

func (s *StoreProductServiceData) syncDownToAPI() error {
	for _, storeProduct := range s.StoreProducts {
		var data = &product_po.ProProductStoreInfo{}
		var upDownResult = &ChannelProductUp_Result{
			SkuId:     storeProduct.ProductId,
			ChannelId: storeProduct.ChannelId,
			StoreId:   storeProduct.StoreId,
			IsSuccess: true,
		}
		s.UpResult = append(s.UpResult, upDownResult)
		if len(storeProduct.Skus) == 0 {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s没有铺品成功的SKU", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
			upDownResult.IsSuccess = false
			continue
		}

		switch storeProduct.ChannelId {
		case common.ChannelIdWeChatApp:
			_, _, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
			if !state {
				log.Infof("下架商品,请求参数,params:%s,门店信息未设置", utils.JsonEncode(storeProduct))
				upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:门店信息未设置", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
				upDownResult.IsSuccess = false
				continue
			}
		case common.ChannelIdMT:
			if err := s.RetailSellStatus(storeProduct, 1); err != nil {
				data.SyncError = err.Error()
			}
		case common.ChannelIdELM:
			if err := s.OnlineElmShopSkuOne(storeProduct, 2); err != nil {
				data.SyncError = err.Error()
			}
		case common.ChannelIdOfflineShop:
			continue
		default:
			log.Errorf("下架商品，渠道设置错误,params：%s", utils.JsonEncode(storeProduct))
			data.SyncError = "渠道设置错误"
		}
		//更新商品下架状态
		if data.SyncError != "" {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:%s",
				storeProduct.ProductId,
				common.ChannelIdMap[storeProduct.ChannelId],
				data.SyncError)
			upDownResult.IsSuccess = false
		}

		// 更新下架状态
		if err := new(product_po.ProProductStoreInfo).UpdateUpDownStatus(s.Engine, storeProduct, data.SyncError, proproductstoreinfo.UpDownStateDown); err != nil {
			log.Errorf("更新下架状态失败: %v", err)
		}
	}
	return nil
}

func (s *StoreProductServiceData) syncDeleteToAPI() error {
	session := s.Engine.NewSession()
	defer session.Close()
	for _, storeProduct := range s.StoreProducts {
		var data = &product_po.ProProductStoreInfo{}
		var upDownResult = &ChannelProductUp_Result{
			SkuId:     storeProduct.ProductId,
			ChannelId: storeProduct.ChannelId,
			StoreId:   storeProduct.StoreId,
			IsSuccess: true,
		}
		s.UpResult = append(s.UpResult, upDownResult)

		switch storeProduct.ChannelId {
		case common.ChannelIdWeChatApp:
		case common.ChannelIdMT:
			if len(storeProduct.Skus) > 0 {
				// 判断是否存在已铺品的SKU
				hasLaunchedSku := false
				for _, sku := range storeProduct.Skus {
					if sku.IsDistribution == 1 { // 假设 IsDistribution == 1 表示已铺品
						hasLaunchedSku = true
						break
					}
				}
				if hasLaunchedSku {
					if err := s.RetailSkuDelete(storeProduct); err != nil {
						data.SyncError = err.Error()
					}
				}
			}
		case common.ChannelIdELM:
			if len(storeProduct.Skus) > 0 {
				// 判断是否存在已铺品的SKU
				hasLaunchedSku := false
				for _, sku := range storeProduct.Skus {
					if sku.IsDistribution == 1 { // 假设 IsDistribution == 1 表示已铺品
						hasLaunchedSku = true
						break
					}
				}
				if hasLaunchedSku {
					if err := s.DeleteElmShopSku(storeProduct); err != nil {
						data.SyncError = err.Error()
					}
				}
			}
		}
		if data.SyncError != "" {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:%s", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId], data.SyncError)
			upDownResult.IsSuccess = false
		} else {
			session.Begin()
			defer session.Close()
			// 删除指定店铺和商品的SKU数据
			err := new(product_po.ProProductStoreInfo).DeleteProductStoreInfo(session, storeProduct)
			if err != nil {
				session.Rollback()
			}

			// 删除指定店铺和商品的SPU数据
			spu := &product_po.ProProductStoreSpu{
				ProductId: storeProduct.ProductId,
				StoreId:   storeProduct.StoreId,
				ChannelId: storeProduct.ChannelId,
			}
			if err = spu.DelSpuByStoreAndProduct(session); err != nil {
				log.Errorf("删除商品，pro_product_store_spu失败,params：%s,error:%s", utils.JsonEncode(storeProduct), err)
				session.Rollback()
			}
			session.Commit()
			// 最后删除下发商品表
			new(product_po.ProProductStore).DeleteProductStore(session, storeProduct.ProductId, storeProduct.StoreId)
		}
	}
	return nil
}

func (s *StoreProductServiceData) syncInventoryToAPI() error {
	var syncStockDatas = make([]omnibus_vo.SyncStockData, 0)
	for _, storeProduct := range s.StoreProducts {
		var data = &product_po.ProProductStoreInfo{}
		var upDownResult = &ChannelProductUp_Result{
			SkuId:     storeProduct.ProductId,
			ChannelId: storeProduct.ChannelId,
			StoreId:   storeProduct.StoreId,
			IsSuccess: true,
		}
		s.UpResult = append(s.UpResult, upDownResult)
		if len(storeProduct.Skus) == 0 {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s没有铺品成功的SKU", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
			upDownResult.IsSuccess = false
			continue
		}

		switch storeProduct.ChannelId {
		case common.ChannelIdMT:
			syncStockDatas = append(syncStockDatas, omnibus_vo.SyncStockData{
				StoreId:   storeProduct.StoreId,
				ChannelId: storeProduct.ChannelId,
				SkuId:     int64(storeProduct.Skus[0].SkuId),
				ProductId: storeProduct.ProductId,
			})
			if len(syncStockDatas) > 0 {
				log.Infof("库存同步，同步库存到mq,params：%s", utils.JsonEncode(syncStockDatas))
				//go func() {
				new(omnibus_service.InventoryService).SyncStock(syncStockDatas)
				//}()
			}
		case common.ChannelIdELM:
			if len(storeProduct.Attr) > 0 {
				if err := s.UpdateElmSkuSpecStock(storeProduct); err != nil {
					data.SyncError = err.Error()
				}
				if data.SyncError != "" {
					upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:%s", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId], data.SyncError)
					upDownResult.IsSuccess = false
				} else {
					upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:库存同步成功", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
				}
			}
		}
	}
	//同步库存到mq

	return nil
}

// 通用的 API 同步调用方法
func (s *StoreProductServiceData) syncPriceAdjustmentToAPI() error {
	channelPriceArr, err := s.channelPriceFun()
	if err != nil {
		return errors.New(err.Error())
	}

	for _, storeProduct := range s.StoreProducts {
		var upDownResult = &ChannelProductUp_Result{
			SkuId:     storeProduct.ProductId,
			ChannelId: storeProduct.ChannelId,
			StoreId:   storeProduct.StoreId,
			IsSuccess: true,
		}
		s.UpResult = append(s.UpResult, upDownResult)
		if len(storeProduct.Skus) == 0 {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s没有铺品成功的SKU", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
			upDownResult.IsSuccess = false
			continue
		}

		//判断是否channelPriceArr记录
		params := utils.JsonEncode(storeProduct)
		var data = &product_po.ProProductStoreInfo{}
		if _, ok := channelPriceArr[storeProduct.Skus[0].SkuId]; ok {
			channelPrice := channelPriceArr[storeProduct.Skus[0].SkuId]
			RetailPrice := channelPrice[storeProduct.ChannelId]
			storeProduct.Skus[0].RetailPrice = RetailPrice
			switch storeProduct.ChannelId {
			case common.ChannelIdWeChatApp:
			case common.ChannelIdMT:
				if err := s.MtRetailSkuPrice(storeProduct); err != nil {
					data.SyncError = err.Error()
				}
			case common.ChannelIdELM:
				// 判断单规格与多规格
				if len(storeProduct.Attr) > 0 {
					if err = s.UpdateElmSkuSpecPrice(storeProduct); err != nil {
						data.SyncError = err.Error()
					}
				} else {
					if err = s.SkuPriceUpdateOne(storeProduct); err != nil {
						data.SyncError = err.Error()
					}
				}
			default:
				continue
			}
			//更新商品价格
			data.RetailPrice = RetailPrice
			if data.SyncError == "" {
				data.RetailPrice = RetailPrice
				data.SyncError = ""
				data.Status = proproductstoreinfo.StatusNormal
			} else {
				data.Status = proproductstoreinfo.StatusUpFailed
				upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:%s", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId], data.SyncError)
				upDownResult.IsSuccess = false
			}
			if _, err := s.Engine.Table("eshop.pro_product_store_info").Cols("retail_price,sync_error,status").
				Where("store_id =? and channel_id=? and sku_id=?", storeProduct.StoreId, storeProduct.ChannelId, storeProduct.Skus[0].SkuId).
				Update(data); err != nil {
				log.Errorf("更新商品价格失败，params:%s,err=%s", params, err.Error())
				return err
			}
		}
	}
	return nil
}

func (s *StoreProductServiceData) channelPriceFun() (map[int]map[int]int, error) {
	channelPriceArr := make(map[int]map[int]int)
	//判断是否统一调价
	channelArr := []int{}
	if s.Params.PriceType == 1 {
		var data = make([]product_po.ProProductStoreInfo, 0)
		if err := s.Engine.Table("eshop.pro_product_store_info").
			Where("store_id = ?", s.Params.TenantId).
			In("sku_id", s.SkuIds).Find(&data); err != nil {
			return nil, errors.New("查询商品信息异常")
		}

		for _, product := range data {
			if _, ok := channelPriceArr[product.SkuId]; !ok {
				channelPriceArr[product.SkuId] = make(map[int]int)
			}
			channelPriceArr[product.SkuId][product.ChannelId] = cast.ToInt(s.Params.ChannlePriceStr)
			channelArr = append(channelArr, product.ChannelId)
		}
		//先更新本地数据
		_, err := s.Engine.Table("eshop.pro_product_store_info").
			Where("store_id=?", s.Params.TenantId).
			In("channel_id", channelArr).
			In("sku_id", s.SkuIds).
			Update(map[string]interface{}{"retail_price": cast.ToInt(s.Params.ChannlePriceStr)})
		if err != nil {
			return nil, errors.New("更新商品分类失败")
		}
	} else {
		for _, skuId := range s.SkuIds {
			if _, ok := channelPriceArr[skuId]; !ok {
				channelPriceArr[skuId] = make(map[int]int)
			}
			for channelId, price := range s.PriceArr {
				channelPriceArr[skuId][channelId] = price
				_, err := s.Engine.Table("eshop.pro_product_store_info").
					Where("store_id=? and sku_id = ?", s.Params.TenantId, skuId).
					In("channel_id", channelId).
					Update(map[string]interface{}{"retail_price": price})
				if err != nil {
					return nil, errors.New("更新商品分类失败")
				}
			}
		}
	}
	return channelPriceArr, nil
}

func parseChannelPrices(priceStr string) (map[int]int, []int, error) {
	channelPrices := make(map[int]int)
	channelId := []int{}
	for _, vv := range strings.Split(priceStr, "|") {
		channelPrice := strings.Split(vv, "-")
		if len(channelPrice) != 2 {
			return nil, nil, errors.New("invalid channel price format")
		}
		channelID, err := cast.ToIntE(channelPrice[0])
		if err != nil {
			return nil, nil, fmt.Errorf("invalid channel ID: %w", err)
		}
		price, err := cast.ToIntE(channelPrice[1])
		if err != nil {
			return nil, nil, fmt.Errorf("invalid price: %w", err)
		}
		channelPrices[channelID] = price
		channelId = append(channelId, channelID)
	}
	return channelPrices, channelId, nil
}

func (s *StoreProductServiceData) checkNewSellStatus(storeProduct *product_po.ProProductStoreAppChannel, upDownResult *ChannelProductUp_Result) bool {
	// 只在美团和饿了么渠道进行检查
	if storeProduct.ChannelId == common.ChannelIdMT || storeProduct.ChannelId == common.ChannelIdELM {
		if storeProduct.NewSell != 2 {
			upDownResult.Message = fmt.Sprintf("商品product_id:%d-%s:不可售", storeProduct.ProductId, common.ChannelIdMap[storeProduct.ChannelId])
			upDownResult.IsSuccess = false
			return false
		}
	}
	return true
}

// 库存中心-手动出入库商品列表
func (s *StoreProductService) FindStoreInOutList(in product_vo.FindStoreInOutListReq) (out []product_vo.FindStoreInOutList, total int64, err error) {
	logPrefix := fmt.Sprintf("库存中心-手动出入库商品列表====入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	if in.StoreId == "" {
		log.Error(logPrefix, "店铺id不能为空")
		return
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	out = make([]product_vo.FindStoreInOutList, 0)
	session = session.Table("eshop.pro_product_store_info").Alias("a").
		Select("a.sku_id,a.product_id,a.channel_id,c.name,c.pic,b.bar_code,b.product_specs,f.name as warehouse_name,d.warehouse_id,e.available_num,e.avg_cost_price,f.category as warehouse_category,g.code as location_code").
		Join("inner", "eshop.pro_sku b", "a.sku_id=b.id and b.is_del=0").
		Join("inner", "eshop.pro_product c", "a.product_id=c.id").
		Join("inner", "dc_dispatch.warehouse_relation_shop d", "a.store_id=d.shop_id and a.channel_id=d.channel_id").
		Join("inner", "dc_dispatch.warehouse f", "f.id=d.warehouse_id").
		Join("left", "eshop.inventory e", "e.store_id=a.store_id and e.sku_id=a.sku_id and e.warehouse_id=d.warehouse_id").
		Join("left", "eshop.inventory_location g", "g.store_id=a.store_id and g.warehouse_id=d.warehouse_id and g.sku_id=a.sku_id").
		Where("a.store_id=?", in.StoreId)
	if in.WarehouseId > 0 {
		session = session.Where("d.warehouse_id=?", in.WarehouseId)
	}

	if in.ChainId > 0 {
		session = session.Where("c.chain_id=?", in.ChainId)
	}
	if len(in.ProductName) > 0 {
		session = session.Where("c.name LIKE ?", "%"+in.ProductName+"%")
	}
	if len(in.BarCode) > 0 {
		session = session.Where("b.bar_code=?", in.BarCode)
	}

	if len(in.QueryKey) > 0 {
		session = session.Where("c.name LIKE ? or b.bar_code=? or c.id=? or b.id=? or g.code=?", "%"+in.QueryKey+"%", in.QueryKey, cast.ToInt(in.QueryKey), cast.ToInt(in.QueryKey), in.QueryKey)
	}
	if in.CategoryIdOffline > 0 {
		session = session.Where("c.category_id_offline=?", in.CategoryIdOffline)
	}
	if total, err = session.Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).GroupBy("a.sku_id,a.store_id").
		FindAndCount(&out); err != nil {
		log.Error(logPrefix, "获取商品列表失败,err=", err.Error())
		return
	}

	return
}

// 获取渠道门店商品、服务和活体列表（必须输入渠道id，店铺id）
func (s *StoreProductService) FindStoreSkuList(in product_vo.FindStoreSkuListReq) (out []product_vo.FindStoreProductList, total int64, err error) {
	logPrefix := "获取渠道门店商品、服务和活体列表===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	s.Begin()
	defer s.Close()
	storeId := in.StoreId
	if cast.ToInt(storeId) == 0 && s.JwtInfo != nil && cast.ToInt(s.JwtInfo.TenantId) > 0 {
		storeId = s.JwtInfo.TenantId
	}
	if cast.ToInt(storeId) == 0 {
		err = errors.New("店铺id不能为空")
		return
	}
	if in.ChannelId == 0 {
		in.ChannelId = common.ChannelIdOfflineShop
	}
	StoreProductSkuList := make([]product_vo.StoreProductSku, 0)

	// 查询条件
	session := s.Engine.Table("eshop.pro_product a").
		Select("a.id as product_id,a.product_type,a.name,a.pic,a.new_sell,a.category_nav,a.category_id,a.category_id_offline,a.content_pc,b.channel_id,b.product_third_id,b.channel_category_id,GROUP_CONCAT(c.sku_id) as sku_ids,c.bar_code").
		Join("left", "eshop.pro_sku e", "a.id=e.product_id and e.is_del=0").
		Join("INNER", "eshop.pro_product_store_spu b", "a.id=b.product_id").
		Join("INNER", "eshop.pro_product_store_info c", "a.id=c.product_id and b.channel_id=c.channel_id and b.store_id=c.store_id").
		Where("c.channel_id=?", in.ChannelId).Where("c.store_id=?", storeId).GroupBy("a.id")

	if in.HasStock {
		session.Join("INNER", "eshop.inventory f", "c.store_id=f.store_id and c.sku_id=f.sku_id and f.available_num>0")
	}
	if in.UpDownState > 0 {
		session.And("c.up_down_state=?", in.UpDownState)
	}

	if in.ChannelId == common.ChannelIdMT || in.ChannelId == common.ChannelIdELM {
		session.And("a.new_sell=?", product_po.NewSellOpen)
	}
	if in.ChannelId > 0 && in.ChannelId != common.ChannelIdOfflineShop {
		session.Join("INNER", "datacenter.store_relation d", "b.channel_id=d.channel_id and b.store_id=d.finance_code").
			And("d.finance_code=?", storeId).Where("d.channel_store_id!=?", " ")
	}

	if len(in.SkuIds) > 0 {
		session.In("c.sku_id", in.SkuIds)
	}
	if len(in.SkuidsNo) > 0 {
		session.NotIn("c.sku_id", in.SkuidsNo)
	}
	if in.ProductType > 0 {
		session.And("a.product_type=?", in.ProductType)
	}
	// 如果是服务或者活体， 则查询服务或者活体
	if len(in.ProductName) > 0 {
		if in.ProductType == product_po.ProductTypeService || in.ProductType == product_po.ProductTypeLive {
			session.And("c.product_name LIKE ?", "%"+in.ProductName+"%")
		} else {
			session.And("a.name LIKE ?", "%"+in.ProductName+"%")
		}

	}

	if len(in.KeyQuery) > 0 {
		session.And("a.name LIKE ? or c.product_name LIKE ? or e.bar_code like ? or c.bar_code like ?", "%"+in.KeyQuery+"%", "%"+in.KeyQuery+"%", "%"+in.KeyQuery+"%", "%"+in.KeyQuery+"%")
	}
	// 实物商品关键词搜索
	if len(in.KeyQuery2) > 0 {
		session.And("a.name LIKE ?  or e.bar_code=? or a.id=? or e.id=? ", "%"+in.KeyQuery2+"%", in.KeyQuery2, cast.ToInt(in.KeyQuery2), cast.ToInt(in.KeyQuery2))
	}
	if in.ProductId > 0 {
		session.And("a.id=?", in.ProductId)
	}

	if len(in.BarCode) > 0 {
		if in.ProductType == product_po.ProductTypeService || in.ProductType == product_po.ProductTypeLive {
			session.And("c.bar_code=?", in.BarCode)
		} else {
			session.And("e.bar_code=?", in.BarCode)
		}

	}

	if in.CategoryId > 0 {
		session.And("b.channel_category_id = ?", in.CategoryId)
	}
	if len(in.CategoryIds) > 0 {
		session.In("b.channel_category_id", in.CategoryIds)
	}
	if len(in.CategoryIdOffline) > 0 {
		session.In("a.category_id_offline", in.CategoryIdOffline)
	}

	// 分页查询
	if in.PageIndex > 0 && in.PageSize > 0 {
		total, err = session.Limit(in.PageSize, (in.PageIndex-1)*in.PageSize).
			OrderBy("a.id DESC").
			FindAndCount(&StoreProductSkuList)
	} else {
		err = session.OrderBy("a.id DESC").Find(&StoreProductSkuList)
	}
	if err != nil {
		return nil, 0, fmt.Errorf("查询列表失败: %v", err)
	}

	productIds := make([]int, 0)
	skuIds := make([]int, 0)
	skuIdsMap := make(map[int]int)
	CategoryIds := make([]int, 0)

	for _, v := range StoreProductSkuList {
		productIds = append(productIds, v.ProductId)
		for _, skuid := range strings.Split(v.SkuIds, ",") {
			skuId := cast.ToInt(skuid)
			if skuId <= 0 {
				continue
			}
			if _, ok := skuIdsMap[skuId]; !ok {
				skuIds = append(skuIds, skuId)
				skuIdsMap[skuId] = skuId
			}
		}
		CategoryIds = append(CategoryIds, v.ChannelCategoryId)
		CategoryIds = append(CategoryIds, v.CategoryIdOffline)
	}

	_, skuMap, e := new(product_po.ProSku).GetSkuMapInfo(session, product_po.SkuQuery{SkuIdSli: skuIds})
	if e != nil {
		return nil, 0, fmt.Errorf("查询sku信息失败: %v", err)
	}
	// 查询分类信息
	cateNames, e := new(product_po.ProCategory).GetCategoryNameByChildId(s.Engine, CategoryIds)
	if e != nil {
		return nil, 0, fmt.Errorf("查询分类信息失败: %v", e)
	}

	where := product_po.GetProductStoreInfoReq{
		StoreId:   storeId,
		ChannelId: in.ChannelId,
		SkuIds:    skuIds,
		OutType:   2,
	}
	// 查询pro_product_store_info表的详细信息
	_, _, productStoreInfoMap, err := product_po.GetProductStoreInfo(session, where)
	if err != nil {
		return nil, 0, fmt.Errorf("查询商品铺品信息失败: %v", err)
	}

	var ProductChannelInfoMap map[int]map[int]product_po.ProProductChannel
	if in.ChannelId == common.ChannelIdMT || in.ChannelId == common.ChannelIdELM {
		_, ProductChannelInfoMap, err = product_po.GetProductChannelInfo(session, map[string]interface{}{"productIds": productIds, "outType": 1})
		if err != nil {
			log.Error(logPrefix, " 获取商品渠道类目信息，err=", err.Error())
			err = errors.New(" 获取商品渠道类目信息")
			return
		}
	}

	var ChannelStoreIdMap map[string]map[int]omnibus_po.StoreRelation
	if in.ChannelId == common.ChannelIdMT || in.ChannelId == common.ChannelIdELM || in.ChannelId == common.ChannelIdWeChatApp {
		// 获取店铺授权信息
		ChannelStoreIdMap, err = omnibus_po.GetStoreMapChannelStoreId(s.Engine, map[string]interface{}{"financeCode": storeId})
		if err != nil {
			log.Error(logPrefix, " 获取店铺授权信息，err=", err.Error())
			err = errors.New(" 获取店铺授权信息")
			return
		}
	}

	// 获取仓库id
	warehouseMapInfo, err := new(omnibus_po.WarehouseRelationShop).GetWarehouseMapInfo(session, omnibus_po.GetWarehouseMapInfoReq{ShopId: storeId})
	if err != nil {
		log.Error(logPrefix, "获取店铺仓库信息失败,err=", err.Error())
		return
	}
	warehouse, ok := warehouseMapInfo[fmt.Sprintf("%s_%d", storeId, in.ChannelId)]
	if !ok {
		log.Error(logPrefix, "未找到仓库信息")
		return
	}

	// 获取sku库存信息
	inventoryMap, _, _, _, err := new(inventory_po.Inventory).GetSkuInventoryByStore(context.Background(), session, inventory_po.GetSkuInventoryByStoreReq{
		StoreId:     storeId,
		SkuIds:      skuIds,
		WarehouseId: warehouse.WarehouseId,
	})

	// 获取库位信息
	locationMap, err := new(location.Location).GetLocationList(session, location.GetLocationListReq{
		StoreId:     storeId,
		WarehouseId: warehouse.WarehouseId,
		SkuIds:      skuIds,
	})

	outMap := make(map[string]product_vo.FindStoreProductList)
	out = make([]product_vo.FindStoreProductList, 0)
	for _, v := range StoreProductSkuList {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, storeId, v.ProductId)

		if _, ok := outMap[k]; !ok {
			categoryNavOnline, categoryNavOffline := "", "" // 前台分类路径， 后台分类路径
			categoryId, categoryIdOffline := 0, 0           // 前台分类id， 后台分类id
			// 这里是因为 服务活体 其实是后台的分类， 所以需要特殊处理，方便前端处理
			if v.ProductType == product_po.ProductTypeService || v.ProductType == product_po.ProductTypeLive {
				categoryIdOffline = v.ChannelCategoryId
				if _, ok := cateNames[v.ChannelCategoryId]; ok {
					categoryNavOffline = cateNames[v.ChannelCategoryId].ParentName + "/" + cateNames[v.ChannelCategoryId].ChildName
				}
			} else {
				if _, ok := cateNames[v.ChannelCategoryId]; ok {
					categoryNavOnline = cateNames[v.ChannelCategoryId].ParentName + "/" + cateNames[v.ChannelCategoryId].ChildName
					categoryId = v.ChannelCategoryId
				}
				if _, ok := cateNames[v.CategoryIdOffline]; ok {
					categoryNavOffline = cateNames[v.CategoryIdOffline].ParentName + "/" + cateNames[v.CategoryIdOffline].ChildName
					categoryIdOffline = v.CategoryIdOffline
				}
			}

			tmp := product_vo.FindStoreProductList{
				ProductType:       int(v.ProductType),
				ProductId:         v.ProductId,
				Pic:               v.Pic,
				Name:              v.Name,
				ContentPc:         v.ContentPc,
				CategoryId:        categoryId,
				CategoryNavOnline: categoryNavOnline,
				CategoryIdOffline: categoryIdOffline,
				CategoryNav:       categoryNavOffline,
			}
			if v.ChannelId == common.ChannelIdMT || v.ChannelId == common.ChannelIdELM {
				if thirdCate, ok := ProductChannelInfoMap[v.ProductId]; ok {
					tmp.MtCategoryThirdName = thirdCate[common.ChannelIdMT].CategoryThirdName
					tmp.MtCategoryThirdId = cast.ToString(thirdCate[common.ChannelIdMT].CategoryThirdId)
					tmp.ElmCategoryThirdName = thirdCate[common.ChannelIdELM].CategoryThirdName
					tmp.ElmCategoryThirdId = cast.ToString(thirdCate[common.ChannelIdELM].CategoryThirdId)
				}
			}
			tmp.Sku = make([]product_vo.SkuExtend, 0)
			outMap[k] = tmp

		}

		pinfo := outMap[k]

		// 小程序没有设置小程序id、美团、饿了么、没有设置第三方门店id，不展示
		if v.ChannelId != common.ChannelIdOfflineShop && len(ChannelStoreIdMap[storeId][v.ChannelId].ChannelStoreId) == 0 {
			continue
		}
		skuIdsMap := make(map[int]int)
		for _, skuId := range strings.Split(v.SkuIds, ",") {
			skuIdInt := cast.ToInt(skuId)
			if skuIdInt <= 0 {
				continue
			}
			if _, ok := skuIdsMap[skuIdInt]; ok {
				continue
			}
			skuIdsMap[skuIdInt] = skuIdInt
			var skuInfo product_po.ProSku
			var ok bool
			if v.ProductType != product_po.ProductTypeService && v.ProductType != product_po.ProductTypeLive {
				skuInfo, ok = skuMap[skuIdInt]
				if !ok {
					continue
				}
				//  获取sku库存信息
				inventory, ok := inventoryMap[fmt.Sprintf("%s_%d_%d", storeId, warehouse.WarehouseId, skuIdInt)]
				if ok {
					skuInfo.InventoryTotalNum = inventory.TotalNum
					skuInfo.InventoryAvailableNum = inventory.AvailableNum
					skuInfo.InventoryFreezeNum = inventory.FreezeNum
				}

				location, ok := locationMap[fmt.Sprintf("%s_%d_%d", storeId, warehouse.WarehouseId, skuIdInt)]
				if ok {
					skuInfo.LocationCode = location.Code
				}

			} else {
				skuInfo = product_po.ProSku{
					Id:      skuIdInt,
					BarCode: v.BarCode,
				}
			}

			productStoreInfo, ok := productStoreInfoMap[fmt.Sprintf("%d_%s_%d_%d", v.ChannelId, storeId, v.ProductId, skuIdInt)]
			if !ok {
				continue
			}
			if v.ProductType == product_po.ProductTypeService || v.ProductType == product_po.ProductTypeLive {
				pinfo.Name = productStoreInfo.ProductName
				pinfo.Pic = productStoreInfo.ProductPic
				pinfo.ServiceDuration = productStoreInfo.ServiceDuration
			}
			// 获取sku库存信息

			skuExtend := product_vo.SkuExtend{
				BaseSku:     skuInfo,
				ChannelInfo: make([]product_vo.ChannelInfo, 0),
			}

			Desc := ""
			// 若不是线下门店，且不是小程序，且新零售未开启，则标识为不可售卖【即新零售未开启，则美团、饿了么、京东渠道显示不可售卖】
			if v.ChannelId != common.ChannelIdOfflineShop && v.ChannelId != common.ChannelIdWeChatApp && v.NewSell == product_po.NewSellStop {
				Desc = "不可售卖"

			} else if productStoreInfo.IsDistribution == proproductstoreinfo.IsDistributionUnLaunch {
				Desc = "未铺品"

			} else if productStoreInfo.UpDownState == proproductstoreinfo.UpDownStateUp {
				Desc = "上架"
			} else if productStoreInfo.UpDownState == proproductstoreinfo.UpDownStateDown {
				Desc = "下架"
			}
			CategoryThirdId, CategoryThirdName := "", ""
			if v.ChannelId == common.ChannelIdMT || v.ChannelId == common.ChannelIdELM {
				if thirdCate, ok := ProductChannelInfoMap[v.ProductId]; ok {
					CategoryThirdName = thirdCate[v.ChannelId].CategoryThirdName
					CategoryThirdId = cast.ToString(thirdCate[v.ChannelId].CategoryThirdId)
				}
			}
			var (
				ChildCateName  string
				ParentCateName string
			)
			if cate, ok := cateNames[v.ChannelCategoryId]; ok {
				ChildCateName = cate.ChildName
				ParentCateName = cate.ParentName
			}
			if productStoreInfo.Status == 1 {
				productStoreInfo.SyncError = ""
			}
			channelStoreId := ""
			if v.ChannelId == common.ChannelIdMT || v.ChannelId == common.ChannelIdELM {
				channelStoreId = ChannelStoreIdMap[in.StoreId][v.ChannelId].ChannelStoreId
			}
			skuExtend.ChannelInfo = append(skuExtend.ChannelInfo, product_vo.ChannelInfo{
				ChannelId:         v.ChannelId,
				Desc:              Desc,
				RetailPrice:       productStoreInfo.RetailPrice,
				SyncError:         productStoreInfo.SyncError,
				CategoryThirdId:   CategoryThirdId,
				CategoryThirdName: CategoryThirdName,
				ProductThirdId:    v.ProductThirdId,
				SkuThirdId:        productStoreInfo.SkuThirdId,
				ChannelStoreId:    channelStoreId,
				ChildCateName:     ChildCateName,
				ParentCateName:    ParentCateName,
				Status:            productStoreInfo.Status,
			})
			pinfo.Sku = append(pinfo.Sku, skuExtend)
		}

		outMap[k] = pinfo

	}
	for _, v := range outMap {
		out = append(out, v)
	}

	return out, total, nil
}

// 管理后台 - 获取门店商品列表(以spu为维度来查列表)
func (s StoreProductService) FindStoreProductList(in product_vo.FindStoreProductListReq) (out []product_vo.FindStoreProductList, total int64, err error) {
	logPrefix := "管理后台 - 获取门店商品列表===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	chainId := cast.ToInt64(s.JwtInfo.ChainId)
	channelId := in.ChannelId
	// 查询指定仓库的商品
	if in.WarehouseId > 0 {
		warePo := warehouse_po.Warehouse{}
		if warehouses, e := warePo.GetByID(context.Background(), session, in.WarehouseId); e != nil {
			log.Error(logPrefix, "获取仓库信息失败", err.Error())
			err = errors.New("获取仓库信息失败")
			return
		} else {
			// 加盟仓
			if warehouses.Category == 4 {
				chainId = warehouses.AuthorizedChainId
			}
			in.StoreId = warehouses.StoreId
		}

	} else {
		if in.StoreId == "" {
			log.Error(logPrefix, "门店id不能为空")
			err = errors.New("门店id不能为空")
			return
		}
		// 店铺被运营了， 但是当前账号是自营的账号时 , 要么查看店内数据，要么查看新零售数据
		if cast.ToInt(s.JwtInfo.SourceChainId) > 0 && cast.ToInt(s.JwtInfo.RoleType) == 1 && in.DataType != 1 && in.DataType != 2 {
			log.Error(logPrefix, "店铺被运营了， 但是当前账号是自营的账号时 , 要么查看店内数据，要么查看新零售数据,storeId=", in.StoreId)
			return nil, 0, fmt.Errorf("店铺被运营了， 但是当前账号是自营的账号时 , 要么查看店内数据，要么查看新零售数据")
		}

		// 代运营账号 只能查看授权的连锁商品
		if cast.ToInt(s.JwtInfo.RoleType) == 2 {
			chainId = cast.ToInt64(s.JwtInfo.SourceChainId)
		}

		if cast.ToInt(s.JwtInfo.RoleType) == 1 && in.DataType == 2 {
			retailCfg := &offline.TTenantRetailCfg{}
			query := offline.RetailCfgQuery{
				TenantId: cast.ToInt64(in.StoreId),
				OutType:  1,
			}
			_, retailCfgInfoMap, err := retailCfg.QueryByTenantId(s.Engine, query)
			if err != nil {
				log.Error(logPrefix, "查询店铺授权信息失败，err=", err.Error())
				return nil, 0, errors.New("查询店铺授权信息失败")
			}
			if info, ok := retailCfgInfoMap[cast.ToInt64(in.StoreId)]; !ok {
				log.Error(logPrefix, "店铺授权信息不存在")
				return nil, 0, errors.New("店铺授权信息不存在")
			} else {
				chainId = info.AuthorizedChainId
			}
		}

	}

	// 查询条件为：渠道id时， 代运营账号只能查看美团和饿了么渠道商品
	if channelId > 0 && s.JwtInfo.RoleType == 2 && (channelId != common.ChannelIdMT && channelId != common.ChannelIdELM) {
		log.Error(logPrefix, "查询条件 代运营账号只能查看美团和饿了么渠道商品,storeId=", in.StoreId)
		return nil, 0, fmt.Errorf("代运营账号只能查看美团和饿了么渠道商品")
	}

	list := make([]product_vo.FindStoreProductList, 0)

	// 只有在该店铺有下发关系的， 才会展示该商品 stock
	// 注意： 小程序和线下门店一定是同一个仓库id, 美团和饿了么一定是同一个仓库id

	session.Table("eshop.pro_product").Alias("a").
		Select("a.id as product_id,a.category_id,a.category_id_offline,a.pic,a.Name,a.update_date,a.brand_name,a.supplier_name,a.new_sell_str,a.new_sell,g.warehouse_id,h.code as location_code").
		Join("left", "eshop.pro_sku f", "a.id=f.product_id").
		Join("inner", "eshop.pro_product_store b", "b.product_id=a.id and b.store_id=?", in.StoreId).
		Join("inner", "eshop.pro_product_store_info d", "d.product_id=a.id and d.store_id=?", in.StoreId).
		Join("left", "eshop.pro_product_store_spu d1", "d1.product_id=a.id and d1.store_id=?", in.StoreId).
		Join("left", "dc_dispatch.warehouse_relation_shop g", "g.shop_id=? and g.channel_id=d.channel_id", in.StoreId).
		Join("left", "eshop.inventory e", "e.sku_id=d.sku_id and e.store_id=d.store_id and e.warehouse_id=g.warehouse_id").
		Join("left", "eshop.inventory_location h", "h.sku_id=d.sku_id and h.warehouse_id=g.warehouse_id and h.store_id=?", in.StoreId).
		Where("a.chain_id=?", chainId).Where("b.store_id=?", in.StoreId).Where("a.is_del=0").Where("f.is_del=0")

	// 售卖渠道搜索： 指该店铺在该渠道有绑定渠道门店id(线下门店不需要判断第三方渠道是否为空。)
	if channelId > 0 && channelId != common.ChannelIdOfflineShop {
		session.Join("left", "datacenter.store_relation c", "c.finance_code=b.store_id and c.channel_id=?", channelId)
		session.Where("c.channel_id=?", channelId).Where("c.channel_store_id!=''")
	}
	// 代运营账号 只能查看美团和饿了么渠道商品
	if s.JwtInfo.RoleType == 2 {
		session.In("d.channel_id", []int{common.ChannelIdMT, common.ChannelIdELM})
	}
	// 店铺被运营了， 但是当前账号是自营的账号时 , 要么查看店内数据，要么查看新零售数据(如果没有传入仓库id，如果是自营店铺，需要分开展示)
	if cast.ToInt(s.JwtInfo.SourceChainId) > 0 && cast.ToInt(s.JwtInfo.RoleType) == 1 {
		if in.DataType == 2 {
			session.In("d.channel_id", []int{common.ChannelIdMT, common.ChannelIdELM})
		} else if in.DataType == 1 {
			session.In("d.channel_id", []int{common.ChannelIdOfflineShop, common.ChannelIdWeChatApp})
		}
	}

	if in.ChannelId > 0 {
		session.Where("d.channel_id=?", in.ChannelId)
	}
	// 异常商品搜索：  根据商品状态来搜索： 3铺品失败 4上架失败 5下架失败 6更新失败 7删除失败
	if in.Status > 0 {
		session.Where("d.status=?", in.Status)
	}
	// 关键字搜索： 商品名称/条码/商品id/skuid/库位
	if in.Query != "" {
		session.Where("a.id=? or a.name like ? or f.bar_code = ? or f.id=?", cast.ToInt(in.Query), "%"+in.Query+"%", in.Query, cast.ToInt64(in.Query))
	}
	if in.LocationCode != "" {
		session.Where("h.code = ?", in.LocationCode)
	}
	skuIds := make([]int, 0) // 这个是入参传进来指定要的sku
	if len(in.SkuIds) > 0 {
		for _, v := range strings.Split(in.SkuIds, ",") {
			skuIds = append(skuIds, cast.ToInt(v))
		}
		session.In("f.id", skuIds)
	}
	//tab切换搜索 0-全部 1-已上架 2-已下架  3-未铺品 4-有库存 5-有库存&未上架
	switch in.Type {
	case 1:
		session.Where("d.up_down_state=?", proproductstoreinfo.UpDownStateUp)

	case 2:
		session.Where("d.up_down_state=? and d.is_distribution=?", proproductstoreinfo.UpDownStateDown, proproductstoreinfo.IsDistributionLaunched)
	case 3:
		session.Where("d.is_distribution=?", proproductstoreinfo.IsDistributionUnLaunch)
	case 4:

		//  做库存时补充有库存条件
		session.Where("e.total_num>0")
	case 5:
		//  有库存&未上架
		session.Where("d.up_down_state=? and d.is_distribution=? and e.total_num>0", proproductstoreinfo.UpDownStateDown, proproductstoreinfo.IsDistributionLaunched)
	}

	// 根据前端分类来查找商品

	if in.CategoryId > 0 {
		if in.CategoryLevel == 1 {
			// 根据父分类id，找到所有的二级分类id
			proCategory := product_po.ProCategory{}
			if childCateIds, e := proCategory.GetChildrenByParentId(s.Engine, in.CategoryId); e != nil {
				log.Error(logPrefix, "获取前端分类的子孩子失败，err=", e.Error())
				err = errors.New("获取前端分类的子孩子")
				return
			} else if len(childCateIds) > 0 {
				session.In("d1.channel_category_id", childCateIds)
			}

		} else {
			session.Where("d1.channel_category_id=?", cast.ToString(in.CategoryId))
		}
	}
	// 根据后端分类来查找商品
	if in.CategoryIdOffline > 0 {
		if in.CategoryLevel == 1 {
			proCategory := product_po.ProCategory{}
			if childCateOfflineIds, e := proCategory.GetChildrenByParentId(s.Engine, in.CategoryIdOffline); e != nil {
				log.Error(logPrefix, "获取后台分类的子孩子失败，err=", e.Error())
				err = errors.New("获取后台分类的子孩子")
				return
			} else if len(childCateOfflineIds) > 0 {
				session.In("a.category_id_offline", childCateOfflineIds)
			}

		} else {
			session.Where("a.category_id_offline=?", cast.ToInt64(in.CategoryIdOffline))
		}
	}

	if total, err = session.Limit(in.PageSize, in.PageSize*(in.PageIndex-1)).GroupBy("a.id").OrderBy("a.id desc").FindAndCount(&list); err != nil {
		log.Error(logPrefix, "获取门店商品列表失败，err=", err.Error())
		err = errors.New("获取门店商品列表失败")
		return
	}
	// 只需返回数量
	if in.IsOnlyCount {
		return
	}

	productIds := make([]int, 0, len(list))

	for _, v := range list {
		productIds = append(productIds, v.ProductId)
	}

	// 获取sku信息
	SkuMapInfo := make(map[int][]product_po.ProSku)
	skuids := make([]int, 0) //这里是查出的商品的所有sku
	if len(productIds) > 0 {
		skuQuery := product_po.SkuQuery{
			ProductIds: productIds,
		}
		SkuMapInfo, _, err = new(product_po.ProSku).GetSkuMapInfo(session, skuQuery)
		if err != nil {
			log.Error(logPrefix, "获取sku信息失败，err=", err.Error())
			err = errors.New("获取sku信息失败")
			return
		}
		for _, v := range SkuMapInfo {
			for _, vv := range v {
				skuids = append(skuids, vv.Id)
			}
		}
	}

	// 获取门店商品sku信息
	ProductStoreInfo1 := make(map[string][]product_po.ProProductStoreInfoExt2)
	if len(productIds) > 0 {
		where := product_po.GetProductStoreInfoReq{
			ProductIds:          productIds,
			StoreId:             in.StoreId,
			ExcludeProductTypes: []int{product_po.ProductTypeService, product_po.ProductTypeLive},
			OutType:             1,
		}
		_, ProductStoreInfo1, _, err = product_po.GetProductStoreInfo(session, where)
		if err != nil {
			log.Error(logPrefix, " 获取门店商品信息，err=", err.Error())
			err = errors.New(" 获取门店商品信息")
			return
		}
	}

	// 获取门店商品spu信息
	var proProductStoreSpu product_po.ProProductStoreSpu
	spuQuery := product_po.SpuQuery{
		ProductIds: productIds,
		StoreId:    in.StoreId,
	}
	productStoreSpuMap, _, err := proProductStoreSpu.QueryMap(session, spuQuery)
	if err != nil {
		log.Error(logPrefix, "获取门店商品spu信息失败，err=", err.Error())
		err = errors.New("获取门店商品spu信息失败")
		return
	}

	// 获取店铺授权信息
	ChannelStoreIdMap, err := omnibus_po.GetStoreMapChannelStoreId(s.Engine, map[string]interface{}{"financeCode": in.StoreId})
	if err != nil {
		log.Error(logPrefix, " 获取店铺授权信息，err=", err.Error())
		err = errors.New(" 获取店铺授权信息")
		return
	}
	_, ProductChannelInfoMap, err := product_po.GetProductChannelInfo(session, map[string]interface{}{"productIds": productIds, "outType": 1})
	if err != nil {
		log.Error(logPrefix, " 获取商品渠道类目信息，err=", err.Error())
		err = errors.New(" 获取商品渠道类目信息")
		return
	}
	if len(list) == 0 {
		return
	}
	// 获取库存信息

	inventoryMap, inventoryMap1, inventoryMap2, inventoryMap3, err := inventory_po.Inventory{}.GetSkuInventoryByStore(context.Background(), session, inventory_po.GetSkuInventoryByStoreReq{
		StoreId:     in.StoreId,
		WarehouseId: list[0].WarehouseId,
		SkuIds:      skuids,
	})
	if err != nil {
		log.Error(logPrefix, "获取库存信息失败", err.Error())
		return
	}

	// 获取库位信息
	locationMap, err := new(location.Location).GetLocationList(session, location.GetLocationListReq{
		StoreId:     in.StoreId,
		WarehouseId: list[0].WarehouseId,
		SkuIds:      skuids,
	})
	// 组织sku信息 和 渠道信息
	for k, v := range list {

		if thirdCate, ok := ProductChannelInfoMap[v.ProductId]; ok {
			list[k].MtCategoryThirdName = thirdCate[common.ChannelIdMT].CategoryThirdName
			list[k].MtCategoryThirdId = cast.ToString(thirdCate[common.ChannelIdMT].CategoryThirdId)
			list[k].ElmCategoryThirdName = thirdCate[common.ChannelIdELM].CategoryThirdName
			list[k].ElmCategoryThirdId = cast.ToString(thirdCate[common.ChannelIdELM].CategoryThirdId)
		}

		var proCategory product_po.ProCategory
		cateNames, err := proCategory.GetCategoryNameByChildId(s.Engine, []int{cast.ToInt(v.CategoryId), cast.ToInt(v.CategoryIdOffline)})
		if err != nil {
			log.Error(logPrefix, " 获取分类名称失败，err=", err.Error())
		}
		if _, ok := cateNames[cast.ToInt(v.CategoryIdOffline)]; ok {
			list[k].CategoryNav = cateNames[cast.ToInt(v.CategoryIdOffline)].ParentName + ">" + cateNames[cast.ToInt(v.CategoryIdOffline)].ChildName
		}
		if _, ok := cateNames[cast.ToInt(v.CategoryId)]; ok {
			list[k].CategoryNavOnline = cateNames[cast.ToInt(v.CategoryId)].ParentName + ">" + cateNames[cast.ToInt(v.CategoryId)].ChildName
		}
		list[k].Sku = make([]product_vo.SkuExtend, 0)
		// 如果有关键词搜索， 如果搜索的是skuid， 条码 ，库位， 则只展示该sku信息
		for _, skuInfo := range SkuMapInfo[v.ProductId] {
			if len(skuIds) > 0 && !utils.InIntSlice(skuInfo.Id, skuIds) {
				continue
			}

			inventory, ok := inventoryMap[fmt.Sprintf("%s_%d_%d", in.StoreId, list[0].WarehouseId, skuInfo.Id)]
			if ok {
				skuInfo.InventoryTotalNum = inventory.TotalNum
				skuInfo.InventoryFreezeNum = inventory.FreezeNum
				skuInfo.InventoryAvailableNum = inventory.AvailableNum

			}
			if location, ok := locationMap[fmt.Sprintf("%s_%d_%d", in.StoreId, list[0].WarehouseId, skuInfo.Id)]; ok {
				skuInfo.LocationCode = location.Code
				if in.LocationCode != "" && in.IsShowSingleSku && in.LocationCode != location.Code {
					continue

				}
			} else if in.LocationCode != "" && in.IsShowSingleSku {
				continue
			}

			skuExtend := product_vo.SkuExtend{
				BaseSku:     skuInfo,
				ChannelInfo: make([]product_vo.ChannelInfo, 0),
			}

			key := fmt.Sprintf("%s_%d_%d", in.StoreId, v.ProductId, skuInfo.Id)
			for _, vv := range ProductStoreInfo1[key] {
				Desc := ""
				// 小程序没有设置小程序id、美团、饿了么、没有设置第三方门店id，不展示
				if vv.ChannelId != common.ChannelIdOfflineShop && len(ChannelStoreIdMap[in.StoreId][vv.ChannelId].ChannelStoreId) == 0 {
					continue
				}
				// 若不是线下门店，且不是小程序，且新零售未开启，则标识为不可售卖【即新零售未开启，则美团、饿了么、京东渠道显示不可售卖】
				if vv.ChannelId != common.ChannelIdOfflineShop && vv.ChannelId != common.ChannelIdWeChatApp && v.NewSell == product_po.NewSellStop {
					Desc = "不可售卖"

				} else if vv.IsDistribution == proproductstoreinfo.IsDistributionUnLaunch {
					Desc = "未铺品"

				} else if vv.UpDownState == proproductstoreinfo.UpDownStateUp {
					Desc = "上架"
				} else if vv.UpDownState == proproductstoreinfo.UpDownStateDown {
					Desc = "下架"
				}
				CategoryThirdId, CategoryThirdName := "", ""
				if thirdCate, ok := ProductChannelInfoMap[v.ProductId]; ok {
					CategoryThirdName = thirdCate[vv.ChannelId].CategoryThirdName
					CategoryThirdId = cast.ToString(thirdCate[vv.ChannelId].CategoryThirdId)
				}
				var (
					ChildCateName  string
					ParentCateName string
				)
				if cate, ok := cateNames[productStoreSpuMap[fmt.Sprintf("%d_%s_%d", vv.ChannelId, vv.StoreId, vv.ProductId)].ChannelCategoryId]; ok {
					ChildCateName = cate.ChildName
					ParentCateName = cate.ParentName
				}
				if vv.Status == 1 {
					vv.SyncError = ""
				}
				skuExtend.ChannelInfo = append(skuExtend.ChannelInfo, product_vo.ChannelInfo{
					ChannelId:         vv.ChannelId,
					Desc:              Desc,
					RetailPrice:       vv.RetailPrice,
					SyncError:         vv.SyncError,
					CategoryThirdId:   CategoryThirdId,
					CategoryThirdName: CategoryThirdName,
					ProductThirdId:    productStoreSpuMap[fmt.Sprintf("%d_%s_%d", vv.ChannelId, vv.StoreId, vv.ProductId)].ProductThirdId,
					SkuThirdId:        vv.SkuThirdId,
					ChannelStoreId:    ChannelStoreIdMap[in.StoreId][vv.ChannelId].ChannelStoreId,
					ChildCateName:     ChildCateName,
					ParentCateName:    ParentCateName,
					Status:            vv.Status,
				})

			}

			list[k].Sku = append(list[k].Sku, skuExtend)
			list[k].InventoryTotalNum = inventoryMap1[fmt.Sprintf("%s_%d_%d", in.StoreId, list[0].WarehouseId, v.ProductId)]
			list[k].InventoryFreezeNum = inventoryMap2[fmt.Sprintf("%s_%d_%d", in.StoreId, list[0].WarehouseId, v.ProductId)]
			list[k].InventoryAvailableNum = inventoryMap3[fmt.Sprintf("%s_%d_%d", in.StoreId, list[0].WarehouseId, v.ProductId)]
		}

	}
	out = list
	return
}

// 获取所有正在参与活动的商品（marketing_activity存储了满减和特价商品两种活动， 满减可用适用全部商品，以及指定商品分类；而 特价活动只能选定具体的商品）
func (s StoreProductService) GetRunningMarketingSku(in product_vo.RunningMarketingSkuReq) (out product_vo.RunningMarketingSkuRes, marketingProductMap map[string]marketing_po.MarketingProduct, err error) {
	logPrefix := fmt.Sprintf("获取参与营销活动的sku列表====，入参：%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	CouponActivityMap := make(map[int]product_vo.CouponActivity)         //优惠券活动
	ActivityMap := make(map[int]product_vo.Activity)                     //满减或特价活动
	marketingProductMap = make(map[string]marketing_po.MarketingProduct) //key为 活动id_skuid
	if in.Type == 0 || in.Type == marketing_po.TypeFullReduction || in.Type == marketing_po.TypeSpecialPrice {
		// 获取正在参与特价活动和满减的商品列表
		GetRunningActivityProductsReq := marketing_po.RunningActivityProductsQuery{
			ChainId: cast.ToInt64(in.ChainId),
			StoreId: in.StoreId,
			Type:    in.Type,
		}
		if len(in.SkuIds) > 0 {
			GetRunningActivityProductsReq.SkuIds = in.SkuIds
		}
		if len(in.CategoryIdOfflines) > 0 {
			GetRunningActivityProductsReq.CategoryIdOfflines = in.CategoryIdOfflines
		}
		if marketingProductMarketingActivity, _, e := new(marketing_po.MarketingProduct).GetRunningActivityProducts(session, GetRunningActivityProductsReq); e != nil {
			log.Error(logPrefix, "获取正在参与活动的商品列表失败,err=", e.Error())
			err = errors.New("获取正在参与活动的商品列表失败" + e.Error())
			return
		} else {
			for _, v := range marketingProductMarketingActivity {
				info, ok := ActivityMap[v.RefId]
				if !ok {
					info.ActivityInfo = v.MarketingActivity
				}

				// 注意： 如果时满减活动， 适用全部商品时， v.MarketingProduct.SkuId是为0的
				// 注意： 如果是特价活动， 只能适用指定商品
				if v.MarketingProduct.ApplyType == marketing_po.ProductApplyTypeGoods {
					// 用于取特价价格
					marketingProductMap[fmt.Sprintf("%d_%d", v.RefId, v.SkuId)] = v.MarketingProduct
					// 满减活动适用于全部商品时
					if v.MarketingProduct.SkuId == 0 {
						info.ActivityInfo.ApplyType = 1
						out.ApplyAllProduct = true
					} else {
						info.ActivityInfo.ApplyType = 2
						out.SkuIdYes = append(out.SkuIdYes, v.SkuId)
						info.SkuIdYes = append(info.SkuIdYes, v.SkuId)
					}

				} else if v.MarketingProduct.ApplyType == marketing_po.ProductApplyTypeCategory {
					out.CategoryIdOfflineYes = append(out.CategoryIdOfflineYes, v.ProductRefId)
					info.CategoryIdOfflineYes = append(info.CategoryIdOfflineYes, v.ProductRefId)
					info.ActivityInfo.ApplyType = 3
				}
				ActivityMap[v.RefId] = info
			}
		}

	}
	if in.Type == 0 || in.Type == marketing_po.TypeCoupon {
		GetRunningCouponProductsReq := marketing_po.RunningCouponProductsQuery{
			ChainId: cast.ToInt64(in.ChainId),
			StoreId: in.StoreId,
		}
		if len(in.SkuIds) > 0 {
			GetRunningCouponProductsReq.SkuIds = in.SkuIds
		}
		if marketingProductMarketingCoupon, _, marketingCoupons, e := new(marketing_po.MarketingProduct).GetRunningCouponProducts(session, GetRunningCouponProductsReq); e != nil {
			log.Error(logPrefix, "获取正在参与优惠券活动的商品列表失败,err=", e.Error())
			err = errors.New("获取正在参与优惠券活动的商品列表失败" + e.Error())
			return
		} else {
			if len(marketingCoupons) > 0 {
				out.ApplyAllProduct = true
			}
			// 适用于全部商品的优惠券
			for _, v := range marketingCoupons {
				info, ok := CouponActivityMap[v.Id]
				if !ok {
					info.ActivityInfo = v
				}
				CouponActivityMap[v.Id] = info
			}

			for _, v := range marketingProductMarketingCoupon {
				info, ok := CouponActivityMap[v.RefId]
				if !ok {
					info.ActivityInfo = v.MarketingCoupon
				}

				if v.MarketingCoupon.ApplyProduct == marketing_po.ApplyProductYes {
					out.SkuIdYes = append(out.SkuIdYes, v.SkuId)
					info.SkuIdYes = append(info.SkuIdYes, v.SkuId)
				} else if v.MarketingCoupon.ApplyProduct == marketing_po.ApplyProductNo {
					out.SkuIdNo = append(out.SkuIdNo, v.SkuId)
					info.SkuIdNo = append(info.SkuIdNo, v.SkuId)
				}
				CouponActivityMap[v.RefId] = info
			}

		}
	}

	for _, v := range CouponActivityMap {
		out.CouponActivity = append(out.CouponActivity, v)
	}
	for _, v := range ActivityMap {
		out.Activity = append(out.Activity, v)
	}

	return
}

// 查询sku当前正在参与的活动
func (s StoreProductService) GetSkuPromotion(in product_vo.RunningMarketingSkuRes, skuId int, categoryOfflineId int) (out product_vo.GetSkuPromotionRes, err error) {
	promotionTypeMap := make(map[int]int)
	for _, v := range in.CouponActivity {
		// 该优惠券适用于所有商品
		if v.ActivityInfo.ApplyProduct == marketing_po.ApplyProductAll || (v.ActivityInfo.ApplyProduct == marketing_po.ApplyProductYes && utils.InIntSlice(skuId, v.SkuIdYes)) {
			out.MarketingCoupon = append(out.MarketingCoupon, v.ActivityInfo)
			promotionTypeMap[marketing_po.TypeCoupon] = 1
		} else if v.ActivityInfo.ApplyProduct == marketing_po.ApplyProductNo && len(v.SkuIdNo) > 0 && !utils.InIntSlice(skuId, v.SkuIdNo) {
			// 不在不可用商品里， 则表示有这个优惠券
			out.MarketingCoupon = append(out.MarketingCoupon, v.ActivityInfo)
			promotionTypeMap[marketing_po.TypeCoupon] = 1
		}
	}

	for _, v := range in.Activity {
		if v.ActivityInfo.ApplyType == 1 || utils.InIntSlice(skuId, v.SkuIdYes) {
			out.MarketingActivity = append(out.MarketingActivity, v.ActivityInfo)
			if v.ActivityInfo.Type == marketing_po.ActivityTypeSpecialPrice {
				promotionTypeMap[marketing_po.TypeSpecialPrice] = 1
			} else if v.ActivityInfo.Type == marketing_po.ActivityTypeFullReduction {
				promotionTypeMap[marketing_po.TypeFullReduction] = 1
			}

		} else if v.ActivityInfo.ApplyType == 3 && utils.InIntSlice(categoryOfflineId, v.CategoryIdOfflineYes) {
			out.MarketingActivity = append(out.MarketingActivity, v.ActivityInfo)
			// 只有满减 才有 指定商品分类
			promotionTypeMap[marketing_po.TypeFullReduction] = 1

		}
	}

	if _, ok := promotionTypeMap[marketing_po.TypeCoupon]; ok {
		out.PromotionInvolved = append(out.PromotionInvolved, product_vo.PromotionInvolved{
			PromotionName: "优惠券",
			PromotionType: marketing_po.TypeCoupon,
		})
	}
	if _, ok := promotionTypeMap[marketing_po.TypeSpecialPrice]; ok {
		out.PromotionInvolved = append(out.PromotionInvolved, product_vo.PromotionInvolved{
			PromotionName: "限时特价",
			PromotionType: marketing_po.TypeSpecialPrice,
		})

	}
	if _, ok := promotionTypeMap[marketing_po.TypeFullReduction]; ok {
		out.PromotionInvolved = append(out.PromotionInvolved, product_vo.PromotionInvolved{
			PromotionName: "满减",
			PromotionType: marketing_po.TypeFullReduction,
		})
	}
	return
}

// 小程序 - 获取门店商品列表(以spu为维度来查列表)
func (s StoreProductService) StoreProductListApi(in product_vo.StoreProductListApiReq) (out []*product_vo.StoreProductListApiData, total int64, err error) {
	logPrefix := "小程序 - 获取门店商品列表===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	s.Begin()
	defer s.Close()

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	if in.StoreId == "" {
		log.Error(logPrefix, "门店id不能为空")
		err = errors.New("门店id不能为空")
		return
	}

	session := s.Engine.NewSession()
	defer session.Close()
	if in.ChannelId == 0 {
		in.ChannelId = common.ChannelIdWeChatApp
	}
	if in.PageIndex == 1 && in.PageSize == 1 {
		in.HasStock = false
	}
	findStoreSkuListReq := product_vo.FindStoreSkuListReq{
		ChannelId:   in.ChannelId,
		StoreId:     in.StoreId,
		UpDownState: proproductstoreinfo.UpDownStateUp,
		ProductType: product_po.ProductTypeGoods,
		KeyQuery2:   in.Query,
		PageIndex:   in.PageIndex,
		PageSize:    in.PageSize,
		ProductId:   in.ProductId,
		HasStock:    in.HasStock,
	}

	// in.CategoryId == -10 限时特价  in.CategoryId == -9满减  in.CategoryId == -8优惠券

	if in.CategoryId < 0 {
		//  前台分类id=-10 代表需要查出参与限时特价的商品列表； 前台分类id=-9 代表需要查出参与满减活动的商品列表；前台分类id=-8 代表需要查出参与优惠券活动的商品列表；
		cateMapType := map[int]int{
			-10: marketing_po.TypeSpecialPrice,
			-9:  marketing_po.TypeFullReduction,
			-8:  marketing_po.TypeCoupon,
		}
		RunningMarketingSkuReq := product_vo.RunningMarketingSkuReq{
			ChainId: in.ChainId,
			StoreId: in.StoreId,
			Type:    cateMapType[in.CategoryId],
		}

		RunningMarketingSkuRes, _, e := s.GetRunningMarketingSku(RunningMarketingSkuReq)
		if e != nil {
			log.Error(logPrefix, "获取活动商品失败", e.Error())
			err = errors.New("获取活动商品失败")
			return
		}
		if in.CategoryId < 0 && !RunningMarketingSkuRes.ApplyAllProduct {
			// 没有商品参加限时特价和满减和优惠券活动
			if len(RunningMarketingSkuRes.Activity) == 0 && len(RunningMarketingSkuRes.CouponActivity) == 0 {
				return
			}
			// 获取参与指定活动的商品sku列表
			findStoreSkuListReq.SkuIds = RunningMarketingSkuRes.SkuIdYes
			findStoreSkuListReq.SkuidsNo = RunningMarketingSkuRes.SkuIdNo
			findStoreSkuListReq.CategoryIdOffline = RunningMarketingSkuRes.CategoryIdOfflineYes
		}

	} else if in.CategoryId > 0 {
		findStoreSkuListReq.CategoryId = in.CategoryId
	}

	// 第一步： 查询参与活动的所有商品
	RunningMarketingSkuReq := product_vo.RunningMarketingSkuReq{
		ChainId: in.ChainId,
		StoreId: in.StoreId,
	}
	RunningMarketingSkuRes, marketingProductMap, err := s.GetRunningMarketingSku(RunningMarketingSkuReq)
	if err != nil {
		log.Error(logPrefix, "获取活动商品失败", err.Error())
		err = errors.New("获取活动商品失败")
		return
	}

	// 第二步： 查询已上架的商品信息
	FindStoreSkuList, total, err := s.FindStoreSkuList(findStoreSkuListReq)
	if err != nil {
		log.Error(logPrefix, "获取商品列表失败", err.Error())
		err = errors.New("获取商品列表失败")
		return
	}

	// 第三步： 组装数据
	out = make([]*product_vo.StoreProductListApiData, 0)
	for _, v := range FindStoreSkuList {
		skuDetail := make([]product_vo.SkuDetail, 0)
		promotionInvolvedMap := make(map[int]string)
		for _, vv := range v.Sku {
			// 查询当前sku 正在参与哪些活动
			GetSkuPromotionRes, e := s.GetSkuPromotion(RunningMarketingSkuRes, vv.BaseSku.Id, v.CategoryIdOffline)
			if e != nil {
				log.Error(logPrefix, "获取商品参与活动信息失败", e.Error())
				err = errors.New("获取商品参与活动信息失败")
				return
			}
			price := 0
			retailPrice := 0
			// 取小程序价格
			for _, vvv := range vv.ChannelInfo {
				if vvv.ChannelId == in.ChannelId {
					price = vvv.RetailPrice
					retailPrice = vvv.RetailPrice
				}
			}
			// 如果该sku参与了特价活动，则显示特价活动价格
			log.Info(logPrefix, "商品sku", vv.BaseSku.Id, "参与的活动有", utils.JsonEncode(GetSkuPromotionRes))
			for _, p := range GetSkuPromotionRes.MarketingActivity {

				// 特价活动
				if p.Type == marketing_po.ActivityTypeSpecialPrice {
					marketingProduct, ok := marketingProductMap[fmt.Sprintf("%d_%d", p.Id, vv.BaseSku.Id)]
					if !ok {
						log.Error(logPrefix, "获取商品特价价格失败,数据异常")
						err = errors.New("获取商品特价价格失败")
						return
					}
					if p.TypeClass == marketing_po.ActivityTypeClassSpecialPrice { //商品特价
						price = marketingProduct.ActivityPrice
					} else if p.TypeClass == marketing_po.ActivityTypeClassDiscount {
						price = int(float64(price) * marketingProduct.DiscountRate / 10)
					}
				}
			}

			skuDetail = append(skuDetail, product_vo.SkuDetail{
				ProductId:         v.ProductId,
				Name:              v.Name,
				Pic:               v.Pic,
				CategoryId:        v.CategoryId,
				CategoryIdOffline: v.CategoryIdOffline,
				SkuId:             vv.BaseSku.Id,
				RetailPrice:       retailPrice,
				Price:             price,
				BarCode:           vv.BaseSku.BarCode,
				ProductSpecs:      vv.BaseSku.ProductSpecs,
				PromotionInvolved: GetSkuPromotionRes.PromotionInvolved,
				MarketingActivity: GetSkuPromotionRes.MarketingActivity,
				MarketingCoupon:   GetSkuPromotionRes.MarketingCoupon,
				Stock:             vv.BaseSku.InventoryAvailableNum,
			})
			for _, n := range GetSkuPromotionRes.PromotionInvolved {
				promotionInvolvedMap[n.PromotionType] = n.PromotionName
			}
		}
		PromotionInvolved := make([]product_vo.PromotionInvolved, 0)
		for k, v := range promotionInvolvedMap {
			PromotionInvolved = append(PromotionInvolved, product_vo.PromotionInvolved{
				PromotionType: k,
				PromotionName: v,
			})
		}
		contentpc := ""
		if in.ProductId > 0 {
			contentpc = v.ContentPc
		}
		tmp := product_vo.StoreProductListApiData{
			ProductId:         v.ProductId,
			Pic:               v.Pic,
			Name:              v.Name,
			Skus:              skuDetail,
			ContentPc:         contentpc,
			PromotionInvolved: PromotionInvolved,
		}
		out = append(out, &tmp)
	}

	return

}

// 小程序 - 获取门店商品详情(以sku为维度来查列表)
func (s StoreProductService) StoreSkuDetail(in product_vo.StoreSkuDetailReq) (skuDetail []product_vo.SkuDetail, err error) {
	logPrefix := "小程序 - 获取门店商品sku详情===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if in.StoreId == "" {
		log.Error(logPrefix, "门店id不能为空")
		err = errors.New("门店id不能为空")
		return
	}
	if len(in.SkuIds) == 0 {
		log.Error(logPrefix, "商品skuid不能为空")
		err = errors.New("商品skuid不能为空")
		return
	}

	//第一步：获取sku基本信息
	productMap, skuMap, _, productStoreInfoMap, err := product_po.GetProductSnapBySkuid(session, product_po.ProductSnapReq{
		ChannelId: common.ChannelIdWeChatApp,
		StoreId:   in.StoreId,
		SkuIds:    in.SkuIds,
	})
	// 后台分类切片
	CategoryIdOfflines := make([]int, 0)
	for _, v := range productMap {
		CategoryIdOfflines = append(CategoryIdOfflines, v.CategoryIdOffline)
	}
	// 第二步： 获取sku参与的活动信息
	RunningMarketingSkuReq := product_vo.RunningMarketingSkuReq{
		ChainId:            in.ChainId,
		StoreId:            in.StoreId,
		SkuIds:             in.SkuIds,
		CategoryIdOfflines: CategoryIdOfflines,
	}

	RunningMarketingSkuRes, marketingProductMap, err := s.GetRunningMarketingSku(RunningMarketingSkuReq)
	if err != nil {
		log.Error(logPrefix, "获取活动商品失败", err.Error())
		err = errors.New("获取活动商品失败")
		return
	}

	// 第三步： 查询商品库存
	var QueryStockRes omnibus_vo.QueryStockRes
	if len(in.SkuIds) > 0 {
		InventoryService := omnibus_service.InventoryService{}
		QueryStockRes = InventoryService.QueryStock(omnibus_vo.QueryStockReq{
			ShopId: in.StoreId,
			SkuIds: utils.IntSliceToInt64Slice(in.SkuIds),
		})
		if QueryStockRes.Code == 400 {
			log.Error(logPrefix, "查询库存失败", QueryStockRes.Message)
			err = errors.New("查询库存失败" + QueryStockRes.Message)
			return
		}
	}
	// 第四步： 组装数据
	for _, skuid := range in.SkuIds {
		// 查询当前sku 正在参与哪些活动
		productId := skuMap[skuid].ProductId
		if productId <= 0 {
			log.Errorf("%s,未找到skuid(%d)对应的productid", logPrefix, skuid)
			err = fmt.Errorf("未找到skuid(%d)对应的productid", skuid)
			return
		}
		productInfo := productMap[productId]
		if productInfo.Id <= 0 {
			log.Errorf("%s,未找到skuid(%d)对应的product信息", logPrefix, skuid)
			err = fmt.Errorf("未找到skuid(%d)对应的product信息", skuid)
			return
		}

		GetSkuPromotionRes, e := s.GetSkuPromotion(RunningMarketingSkuRes, skuid, productMap[productId].CategoryIdOffline)
		if e != nil {
			log.Error(logPrefix, "获取商品参与活动信息失败", e.Error())
			err = errors.New("获取商品参与活动信息失败")
			return
		}
		retailPrice := 0
		price := 0
		// 取小程序价格
		productStoreInfo, ok := productStoreInfoMap[fmt.Sprintf("%d_%s_%d_%d", common.ChannelIdWeChatApp, in.StoreId, productId, skuid)]
		if !ok {
			log.Errorf("%s,未找到skuid(%d)对应的上架信息", logPrefix, skuid)
			err = fmt.Errorf("未找到skuid(%d)对应的上架信息", skuid)
			return
		} else {
			price = productStoreInfo.RetailPrice
			retailPrice = productStoreInfo.RetailPrice
		}

		// 如果该sku参与了特价活动，则显示特价活动价格
		log.Info(logPrefix, "商品sku", skuid, "参与的活动有", utils.JsonEncode(GetSkuPromotionRes))
		for _, p := range GetSkuPromotionRes.MarketingActivity {

			// 特价活动
			if p.Type == marketing_po.ActivityTypeSpecialPrice {
				marketingProduct, ok := marketingProductMap[fmt.Sprintf("%d_%d", p.Id, skuid)]
				if !ok {
					log.Error(logPrefix, "获取商品特价价格失败,数据异常")
					err = errors.New("获取商品特价价格失败")
					return
				}
				if p.TypeClass == marketing_po.ActivityTypeClassSpecialPrice { //商品特价
					price = marketingProduct.ActivityPrice
				} else if p.TypeClass == marketing_po.ActivityTypeClassDiscount {
					price = int(float64(price) * marketingProduct.DiscountRate / 10)
				}
			}
		}

		skuDetail = append(skuDetail, product_vo.SkuDetail{
			ProductId:         productId,
			Name:              productInfo.Name,
			Pic:               productInfo.Pic,
			CategoryId:        productStoreInfo.ChannelCategoryId,
			CategoryIdOffline: productInfo.CategoryIdOffline,
			ContentPc:         productInfo.ContentPc,
			SkuId:             skuid,
			RetailPrice:       retailPrice,
			Price:             price,
			BarCode:           skuMap[skuid].BarCode,
			ProductSpecs:      skuMap[skuid].ProductSpecs,
			PromotionInvolved: GetSkuPromotionRes.PromotionInvolved,
			MarketingActivity: GetSkuPromotionRes.MarketingActivity,
			MarketingCoupon:   GetSkuPromotionRes.MarketingCoupon,
			Stock:             cast.ToInt(QueryStockRes.Stock[cast.ToInt64(skuid)].AvailableNum),
		})
	}

	return

}

// EditStoreProduct 只编辑商品分类id 并同步给美团、饿了么
func (s StoreProductService) EditStoreProduct(in product_vo.EditStoreProductReq) (err error) {
	logPrefix := "编辑商品分类id 并同步给美团、饿了么===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	s.Begin()
	defer s.Close()

	//第一步：获取门店商品信息
	var proProductStoreSpus []product_po.ProProductStoreSpu
	if err = s.Engine.Table("eshop.pro_product_store_spu").
		Where("product_id = ? and store_id = ?", in.ProductId, in.StoreId).
		In("channel_id", common.ChannelIdArr).Find(&proProductStoreSpus); err != nil {
		log.Error(logPrefix, "获取门店商品信息失败，err=", err.Error())
		err = errors.New("获取门店商品信息失败")
		return
	}
	if len(proProductStoreSpus) == 0 {
		err = errors.New("未找到可更新的商品信息")
		return
	}

	var categoryInfo product_po.ProCategory
	if _, err = s.Engine.Where("id = ?", in.ChannelCategoryId).Get(&categoryInfo); err != nil {
		log.Error(logPrefix, "查询分类信息失败，err=", err.Error())
		err = errors.New("查询分类信息失败")
		return
	}

	// 更新分类id
	_, err = s.Engine.Table("eshop.pro_product_store_spu").
		Where("product_id=? and store_id=?", in.ProductId, in.StoreId).
		In("channel_id", common.ChannelIdArr).
		Update(map[string]interface{}{"channel_category_id": in.ChannelCategoryId})
	if err != nil {
		log.Error(logPrefix, "更新商品分类失败，err=", err.Error())
		err = errors.New("更新商品分类失败")
		return
	}

	type cateMap struct {
		SyncError string `json:"sync_error"`
		Status    int    `json:"status"`
	}
	//第三步： 同步有铺品成功的第三方渠道
	//go func() {
	for _, v := range proProductStoreSpus {
		log.Infof("同步商品分类，请求接口：,params:%s", utils.JsonEncode(v))
		// if v.IsDistribution == 0 {
		// 	continue
		// }
		var data cateMap
		data.SyncError = ""
		switch v.ChannelId {
		case common.ChannelIdMT:
			if err = s.RetailInitDataCategory(v.ChannelId, v.ProductId, v.StoreId, in.ChannelCategoryName); err != nil {
				data.SyncError = err.Error()
			}
		case common.ChannelIdELM:
			req := &product_vo.UpdateElmShopCategoryRequest{
				CustomSkuId:  v.ProductId,
				ShopCustomId: in.ChannelCategoryId,
				Name:         in.ChannelCategoryName,
				Rank:         cast.ToString(categoryInfo.Sort),
			}
			if err = s.UpdateElmShopCategory(v.StoreId, v.ChannelId, req); err != nil {
				data.SyncError = err.Error()
			}
		default:
			continue
		}

		//更新状态及信息
		if _, err = s.Engine.Table("eshop.pro_product_store_spu").Cols("sync_error").Where("id=?", v.Id).Update(&data); err != nil {
			log.Error("更新商品分类失败，err=", err.Error())
			return
		}
	}
	//}()
	return nil
}

// 管理后台 - 获取门店商品详情
func (s StoreProductService) GetProductInfo(in product_vo.GetStoreProductReq) (out product_vo.StoreProduct, err error) {
	logPrefix := "管理后台 - 获取门店商品详情===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	// 查询 连锁库 商品信息
	exists, err := session.Table("eshop.pro_product").Alias("a").Select("a.*,b.channel_category_id,c.name as channel_category_name").
		Join("left", "eshop.pro_product_store_info b", "a.id = b.product_id and b.channel_id=1 and b.store_id =?", in.StoreId).
		Join("left", "eshop.pro_category c", "c.id = b.channel_category_id").
		Where("a.id=?", in.ProductId).Get(&out.Product)
	if err != nil {
		log.Error(logPrefix, "查找商品信息失败，err=", err.Error())
		err = errors.New("查找商品信息失败")
		return
	}
	if !exists {
		err = errors.New("未找到该连锁商品信息")
		return
	}

	// 查询 连锁库 商品渠道信息（目前仅类目信息）
	out.ProductChannel = make([]product_po.ProProductChannel, 0)
	err = session.Table("eshop.pro_product_channel").Where("product_id=?", in.ProductId).Find(&out.ProductChannel)
	if err != nil {
		log.Error(logPrefix, "商品渠道信息失败，err=", err.Error())
		err = errors.New("商品渠道信息失败")
		return
	}

	// 查询 连锁库 商品渠道属性（不同渠道商品类目对应不同的商品渠道属性）
	out.ProductChannelAttr = make([]product_po.ProProductChannelAttr, 0)
	err = session.Table("eshop.pro_product_channel_attr").Where("product_id=?", in.ProductId).Find(&out.ProductChannelAttr)
	if err != nil {
		log.Error(logPrefix, "商品渠道属性信息失败，err=", err.Error())
		err = errors.New("商品渠道属性信息失败")
		return
	}

	// 查询 连锁库-商品规格信息
	out.Sku = make([]product_po.ProSku, 0)
	err = session.Table("eshop.pro_sku").Where("product_id=?", in.ProductId).Where("is_del=0").Find(&out.Sku)
	if err != nil {
		log.Error(logPrefix, "商品SKU信息失败，err=", err.Error())
		err = errors.New("商品SKU信息失败")
		return
	}
	return
}

// StoreProductUpDistinct 去重的上架商品列表
func (s StoreProductService) StoreProductUpDistinct(in product_vo.StoreProductUpDistinctReq) (out product_vo.StoreProductUpDistinctRes, e error) {
	out = product_vo.StoreProductUpDistinctRes{Code: 400}
	s.Begin()
	defer s.Close()

	defer func() {
		if out.Code != 200 {
			log.Info("StoreProductService StoreProductUpDistinct 入参：", utils.JsonEncode(in), "，返回：", utils.JsonEncode(out))
		}
	}()

	if in.ChannelId == 0 {
		out.Message = "渠道id不能为空"
		return
	}

	if len(in.FinanceCode) == 0 {
		out.Message = "门店不能为空"
		return
	}

	db := s.Session

	session := db.Table("eshop.pro_product_store_info").Alias("ppsi").
		Join("inner", "eshop.pro_product pp", "ppsi.product_id =pp.id").
		Join("inner", "eshop.pro_sku ps", "ps.product_id =ppsi.product_id").
		Where("ppsi.channel_id=?", in.ChannelId).In("ppsi.store_id", in.FinanceCode)
	session.Where("ppsi.up_down_state =1")

	if len(in.SkuId) > 0 {
		session.In("ps.id", in.SkuId)
	}

	if in.Name != "" {
		session.And("pp.name like ?", "%"+in.Name+"%")
	}

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	total, err := session.Limit(int(in.PageSize), int(in.PageSize*(in.PageIndex-1))).
		Select("distinct ppsi.product_id,ps.id as sku_id,pp.name").
		FindAndCount(&out.Data)

	if err != nil {
		out.Message = err.Error()
		return
	}

	out.Total = int32(total)
	out.Code = 200
	return
}

// CountByPrice 商品按价格统计
func (s StoreProductService) CountByPrice(in product_vo.ProductCountByPriceReq) (out product_vo.ProductCountByPriceRes, e error) {
	out = product_vo.ProductCountByPriceRes{Code: 400}
	s.Begin()
	defer s.Close()

	defer func() {
		if out.Code != 200 {
			log.Info("StoreProductService CountByPrice 入参：", utils.JsonEncode(in), "，返回：", utils.JsonEncode(out))
		}
	}()

	if in.ChannelId == 0 {
		out.Message = "渠道id不能为空"
		return
	}
	if len(in.SkuId) == 0 {
		out.Message = "商品SkuId不能为空"
		return
	}

	if len(in.FinanceCode) == 0 {
		out.Message = "财务编码不能为空"
		return
	}

	// 当单次统计数量过大时，查询性能衰减非常严重
	// 如约6.5万数据耗时400ms，13万 1.5秒，26万 8.8秒
	var eg errgroup.Group
	pageSize := 65000 / len(in.SkuId)

	idsArr := make([]string, 0, len(in.SkuId))
	for _, id := range in.SkuId {
		idsArr = append(idsArr, strconv.FormatInt(int64(id), 10))
	}
	ids := strings.Join(idsArr, ",")

	db := s.Session
	ch := make(chan struct{}, 8)

	// 分批处理
	for i := 0; i < len(in.FinanceCode); i = i + pageSize {
		ch <- struct{}{}
		end := i + pageSize
		if end > len(in.FinanceCode) {
			end = len(in.FinanceCode)
		}

		shops := in.FinanceCode[i:end]
		eg.Go(func() error {
			defer func() {
				<-ch
			}()
			var data []product_vo.ProductCountByPrice
			if err := db.SQL(fmt.Sprintf(
				` SELECT t.sku_id,t.product_id,t.market_price,t.count,s.name as shop_name,pp.name FROM 
							(     
							SELECT ppsi.product_id,ps.id as  sku_id,ppsi.retail_price as  market_price,MIN(ppsi.store_id) AS finance_code,count(1) as count
							FROM eshop.pro_product_store_info ppsi
							inner join eshop.pro_sku ps on ps.product_id =ppsi.product_id 
							WHERE ppsi.channel_id = ? 
							AND ppsi.up_down_state = 1 
							AND ps.id in (%s) AND ppsi.store_id IN (%s)
							GROUP BY ppsi.product_id,ppsi.retail_price 
							) t
							JOIN datacenter.store s ON s.finance_code = t.finance_code
							JOIN eshop.pro_product pp on pp.id =t.product_id`, ids, strings.Join(shops, "','")), in.ChannelId).
				Find(&data); err != nil {
				return err
			}

			out.Data = append(out.Data, data...)
			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		out.Message = err.Error()
		return
	}

	out.Code = 200
	return
}

/*
// PriceStore 商品同一价格关联的门店

	func (s StoreProductService) PriceStore(ctx context.Context, in *pc.ProductPriceStoreReq) (out *pc.ProductPriceStoreRes, e error) {
		out = &pc.ProductPriceStoreRes{Code: 400}

		defer func() {
			if out.Code != 200 {
				log.Info("ChannelProduct PriceStore 入参：", utils.JsonEncode(in), "，返回：", utils.JsonEncode(out))
			}
		}()

		if in.ChannelId == 0 {
			out.Message = "渠道id不能为空"
			return
		}
		if in.SkuId == 0 {
			out.Message = "商品SkuId不能为空"
			return
		}
		if len(in.FinanceCode) == 0 {
			out.Message = "财务编码不能为空"
			return
		}

		session := NewDbConn().Table("channel_store_product").Alias("csp").
			Join("inner", "datacenter.store s", "s.finance_code = csp.finance_code").
			Where("csp.channel_id = ? and csp.up_down_state = 1 and csp.sku_id = ?", in.ChannelId, in.SkuId).
			Where("csp.market_price = ?", in.Price).
			In("csp.finance_code", in.FinanceCode)

		in.Search = strings.TrimSpace(in.Search)

		if len(in.Search) > 0 {
			switch in.SearchType {
			case 1:
				session.Where("s.name like ?", "%"+in.Search+"%")
			case 2:
				session.Where("csp.finance_code = ?", in.Search)
			}
		}

		pr := utils.PaginateReq{
			Count:    session.Clone(),
			List:     session.Asc("s.id").Select("s.finance_code,s.name"),
			Page:     in.PageIndex,
			PageSize: in.PageSize,
		}
		if err := pr.Paginate(&out.TotalCount, &out.Data); err != nil {
			out.Message = err.Error()
			return
		}

		out.Code = 200
		return
	}
*/
func (s StoreProductService) StoreProductStock(in product_vo.StoreProductStockReq) (out []product_vo.StockStru, err error) {
	logPrefix := fmt.Sprintf("查询商品库存，入参：%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	// 获取sku的库存
	InventoryService := omnibus_service.InventoryService{}
	QueryStockRes := InventoryService.QueryStock(omnibus_vo.QueryStockReq{SkuIds: in.SkuIds, ShopId: in.FinanceCode})
	if QueryStockRes.Code != 200 {
		log.Error(logPrefix, "获取店铺库存失败，错误原因为：", QueryStockRes.Message)
		err = errors.New("获取库存失败")
		return
	}

	out = make([]product_vo.StockStru, 0)
	stockInfo := make(map[string]int32)

	for k, v := range QueryStockRes.Stock {
		stockInfo[cast.ToString(k)] = v.AvailableNum
	}
	out = append(out, product_vo.StockStru{
		FinanceCode: in.FinanceCode,
		StockInfo:   stockInfo,
	})
	return

}
func (s StoreProductService) StoreProductUpdownstatus(in product_vo.StoreProductUpdownstatusReq) (out []product_vo.UpDownStru, err error) {
	logPrefix := fmt.Sprintf("查询商品上下架，入参：%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	type tmp struct {
		UpDownState int `json:"up_down_state"`
		ProductId   int `json:"product_id"`
		SkuId       int `json:"sku_id"`
	}
	data := make([]tmp, 0)
	if err = s.Engine.Table("eshop.pro_product_store_info a").
		Select("a.up_down_state,a.product_id,a.sku_id").
		Where("a.channel_id=?", in.ChannelId).Where("a.store_id=?", in.FinanceCode).In("a.sku_id", in.SkuIds).
		Where("a.is_distribution=?", proproductstoreinfo.IsDistributionLaunched).
		Find(&data); err != nil {
		log.Error(logPrefix, "获取门店商品信息")
		err = errors.New("获取门店商品信息失败" + err.Error())
		return
	}

	dataMap := make(map[int]tmp)
	for _, v := range data {
		dataMap[v.SkuId] = v
	}
	stateInfo := make(map[string]int)
	for _, skuid := range in.SkuIds {
		stateInfo[cast.ToString(skuid)] = dataMap[skuid].UpDownState

	}
	out = append(out, product_vo.UpDownStru{FinanceCode: in.FinanceCode, StateInfo: stateInfo})

	return

}

// UpdateProductLocation 更新商品库位信息
// 支持直接编辑sku库位，库位与sku一一对应，如库位已使用需阻断提示"库位已绑定商品xxxx{商品名称+skuid}"
func (s *StoreProductService) UpdateProductLocation(req product_vo.UpdateLocationRequest) error {
	logPrefix := fmt.Sprintf("更新商品库位信息====,入参:%s", utils.JsonEncode(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if req.StoreId == "" {
		return errors.New("店铺ID不能为空")
	}
	if req.SkuId == 0 {
		return errors.New("商品SKU ID不能为空")
	}
	if req.LocationCode == "" {
		return errors.New("库位码不能为空")
	}

	if len([]rune(req.LocationCode)) > 20 {
		return errors.New("库位码1-20个字")
	}

	// 根据sku和店铺id 找出这个商品是在什么渠道铺品了（如果店铺被代运营了。 自己连锁的商品只能在线下门店和小程序铺品， 饿了么和美团铺品的商品肯定是代运营连锁下的商品。代运营连锁下的商品id和自己连锁的商品id是不可能重复的）
	// 所以通过sku， 就能知道这个商品是哪个连锁的商品

	productStoreInfoList, _, _, err := product_po.GetProductStoreInfo(session, product_po.GetProductStoreInfoReq{
		SkuId:   req.SkuId,
		StoreId: req.StoreId,
	})
	if err != nil {
		log.Error(logPrefix, "获取店铺商品信息失败,err", err.Error())
		return errors.New("获取店铺商品信息失败")
	}
	if len(productStoreInfoList) == 0 {
		log.Error(logPrefix, "门店商品信息不存在")
		return errors.New("门店商品信息不存在")
	}

	// 根据店铺id,渠道id, 找出仓库id datacenter.warehouse_relation_shop
	channelId := productStoreInfoList[0].ChannelId
	productId := productStoreInfoList[0].ProductId
	warehouseMapInfo, err := new(omnibus_po.WarehouseRelationShop).GetWarehouseMapInfo(session, omnibus_po.GetWarehouseMapInfoReq{ShopId: req.StoreId})
	if err != nil {
		log.Error(logPrefix, "获取店铺仓库信息失败,err=", err.Error())
		return errors.New("获取店铺仓库信息失败")
	}
	warehouse, ok := warehouseMapInfo[fmt.Sprintf("%s_%d", req.StoreId, channelId)]
	if !ok {
		log.Error(logPrefix, "店铺仓库信息不存在")
		return errors.New("店铺仓库信息不存在")
	}

	// 根据店铺id , 库位码， 查找库位表， 判断该库位是否已经绑定了其他商品
	locationInfo, has, err := new(location.Location).GetLocation(session, location.GetLocationReq{StoreId: req.StoreId, LocationCode: req.LocationCode})
	if err != nil {
		log.Error(logPrefix, "查询数据库失败，err=", err.Error())
		return errors.New("查询数据库失败")
	}
	if has && locationInfo.SkuId > 0 {
		if locationInfo.SkuId == req.SkuId {
			return errors.New("该商品库位没有发生变化，无需操作")
		} else {
			return fmt.Errorf("库位已绑定商品%d", locationInfo.SkuId)
		}

	}

	// 根据skuid ,店铺id,仓库id 查找库位表inventory_location 看看该商品是否以前已经绑定过库位， 如果绑定过， 这时编辑库位。 如果没有绑定过， 则新增
	locationInfo2, has2, err := new(location.Location).GetLocation(session, location.GetLocationReq{StoreId: req.StoreId, WarehouseId: warehouse.WarehouseId, SkuId: req.SkuId})
	if err != nil {
		log.Error(logPrefix, "查询数据库失败,,err=", err.Error())
		return errors.New("查询数据库失败")
	}
	if has2 {
		// 如果该商品原来绑定了其他库位， 则只需解除绑定关系即可。 老库位还是的留着
		locationInfo2.SkuId = 0
		locationInfo2.ProductId = 0

		if err := locationInfo2.Update(context.Background(), session, "sku_id", "product_id"); err != nil {
			log.Error(logPrefix, "解除老库位的绑定关系,err=", err.Error())
			return errors.New("解除老库位的绑定关系")
		}

	}
	// 库位码已经存在，但是没有绑定任何商品
	if has && locationInfo.SkuId == 0 {
		locationInfo.SkuId = req.SkuId
		locationInfo.ProductId = productId
		if err := locationInfo.Update(context.Background(), session, "sku_id", "product_id", "warehouse_id"); err != nil {
			log.Error(logPrefix, "更新老库位的绑定关系,err=", err.Error())
			return errors.New("更新老库位的绑定关系")
		}
		return nil
	} else {
		// 插入
		loca := new(location.Location)
		loca.StoreId = req.StoreId
		loca.SkuId = req.SkuId
		loca.ProductId = productId
		loca.WarehouseId = warehouse.WarehouseId
		loca.ChainId = req.ChainId
		loca.Code = req.LocationCode

		if err = loca.Create(context.Background(), session); err != nil {
			log.Error(logPrefix, "商品绑定库位失败,err=", err.Error())
			return errors.New("商品绑定库位失败")
		}
	}

	return nil
}

// UpdateLocationSku 库位更换绑定商品(大前提是 ， 在门店商品列表里搜索库位， 然后点击库位， 弹出库位更换绑定商品)
func (s *StoreProductService) UpdateLocationSku(req product_vo.UpdateLocationSkuRequest) error {
	logPrefix := fmt.Sprintf("库位更换绑定商品====,入参:%s", utils.JsonEncode(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if req.StoreId == "" {
		return errors.New("店铺ID不能为空")
	}

	if req.SkuId == "" {
		return errors.New("请输入商品sku或商品条码")
	}
	if req.LocationCode == "" {
		return errors.New("库位码不能为空")
	}

	// 根据sku和店铺id 找出这个商品是在什么渠道铺品了（如果店铺被代运营了。 自己连锁的商品只能在线下门店和小程序铺品， 饿了么和美团铺品的商品肯定是代运营连锁下的商品。代运营连锁下的商品id和自己连锁的商品id是不可能重复的）
	// 所以通过sku， 就能知道这个商品是哪个连锁的商品
	where := product_po.GetProductStoreInfoReq{
		SkuOrBarcode: req.SkuId,
		StoreId:      req.StoreId,
	}
	productStoreInfoList, _, _, err := product_po.GetProductStoreInfo(session, where)
	if err != nil {
		log.Error(logPrefix, "获取店铺商品信息失败,err", err.Error())
		return errors.New("获取店铺商品信息失败")
	}
	if len(productStoreInfoList) == 0 {
		log.Error(logPrefix, "门店商品信息不存在")
		return errors.New("门店商品信息不存在")
	}

	// 根据店铺id,渠道id, 找出仓库id datacenter.warehouse_relation_shop
	channelId := productStoreInfoList[0].ChannelId
	productId := productStoreInfoList[0].ProductId
	skuId := productStoreInfoList[0].SkuId

	// 根据店铺id,渠道id, 找出仓库id datacenter.warehouse_relation_shop

	warehouseMapInfo, err := new(omnibus_po.WarehouseRelationShop).GetWarehouseMapInfo(session, omnibus_po.GetWarehouseMapInfoReq{ShopId: req.StoreId})
	if err != nil {
		log.Error(logPrefix, "获取店铺仓库信息失败,err=", err.Error())
		return errors.New("获取店铺仓库信息失败")
	}
	warehouse, ok := warehouseMapInfo[fmt.Sprintf("%s_%d", req.StoreId, channelId)]
	if !ok {
		log.Error(logPrefix, "店铺仓库信息不存在")
		return errors.New("店铺仓库信息不存在")
	}

	// 根据店铺id,库位码 找到库位绑定商品信息
	locationInfo, has, err := new(location.Location).GetLocation(session, location.GetLocationReq{StoreId: req.StoreId, LocationCode: req.LocationCode})
	if err != nil {
		log.Error(logPrefix, "查询数据库失败，err=", err.Error())
		return errors.New("查询数据库失败")
	}
	if has && locationInfo.SkuId > 0 && locationInfo.SkuId == skuId {
		return errors.New("该库位绑定的商品与当前商品一致，无需操作")
	}

	// 根据新的skuid ,店铺id,仓库id 查找库位表inventory_location 看看该商品是否以前已经绑定过库位， 如果绑定过， 这时编辑库位。
	locationInfo2, has2, err := new(location.Location).GetLocation(session, location.GetLocationReq{StoreId: req.StoreId, WarehouseId: warehouse.WarehouseId, SkuId: skuId})
	if err != nil {
		log.Error(logPrefix, "查询数据库失败,,err=", err.Error())
		return errors.New("查询数据库失败")
	}
	if has2 {
		// 如果该商品原来绑定了其他库位， 则只需解除绑定关系即可。 老库位还是的留着
		locationInfo2.SkuId = 0
		locationInfo2.ProductId = 0
		locationInfo2.WarehouseId = 0
		if err := locationInfo2.Update(context.Background(), session, "sku_id", "product_id", "warehouse_id"); err != nil {
			log.Error(logPrefix, "解除老库位的绑定关系,err=", err.Error())
			return errors.New("解除老库位的绑定关系")
		}

	}

	// 最后更新库位绑定关系
	locationInfo.SkuId = skuId
	locationInfo.ProductId = productId
	locationInfo.WarehouseId = warehouse.WarehouseId
	if err := locationInfo.Update(context.Background(), session, "sku_id", "product_id", "warehouse_id"); err != nil {
		log.Error(logPrefix, "库位更新绑定商品失败,err=", err.Error())
		return errors.New("库位更新绑定商品失败")
	}
	return nil
}

// GetServiceList 获取服务列表
func (s *StoreProductService) GetServiceList(req product_vo.ServiceListReq) ([]product_vo.ServiceInfo, int64, error) {
	var list []product_vo.ServiceInfo
	var total int64
	s.Begin()
	defer s.Close()
	// 查询条件
	session := s.Engine.Table("pro_product_store_info a").
		Select("a.product_name as name,a.id, b.channel_category_id category_id, b.namePath,a.retail_price as price,a.bar_code,a.product_pic,b.create_date,a.sku_id,a.product_id").
		Join("LEFT", "pro_product_store_spu b", "a.product_id = b.product_id").
		Where("a.store_id = ? and a.channel_id=100", req.StoreId)

	if req.ProductName != "" {
		session.And("a.product_name LIKE ? or bar_code=?", "%"+req.ProductName+"%", req.ProductName)
	}
	if req.CategoryId > 0 {
		session.And("b.channel_category_id = ?", req.CategoryId)
	}
	if req.ProductType != 0 {
		session.And("a.product_type= ?", req.ProductType)
	}

	// // 获取总数
	// total, err := session.Clone().Count()
	// if err != nil {
	// 	return nil, 0, fmt.Errorf("查询总数失败: %v", err)
	// }

	// 分页查询
	total, err := session.Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).
		OrderBy("a.id DESC").
		FindAndCount(&list)
	if err != nil {
		return nil, 0, fmt.Errorf("查询列表失败: %v", err)
	}

	return list, total, nil
}

// SaveService 保存服务
func (s *StoreProductService) SaveService(req product_vo.SaveServiceReq) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	// 保存商品信息
	storeInfo := &product_po.ProProductStoreInfo{
		ProductName:     req.Name,
		StoreId:         req.StoreId,
		RetailPrice:     utils.Yuan2Fen(req.Price), // 转为分
		Status:          1,                         // 默认正常状态
		UpDownState:     1,
		ProductPic:      req.Images,
		BarCode:         req.BarCode,
		ServiceDuration: req.ServiceDuration,
		MarketPrice:     utils.Yuan2Fen(req.MarketPrice),
		PetVariety:      req.PetVariety,
		PetVarietyName:  req.PetVarietyName,
		BirthDate:       req.BirthDate,
		ChannelId:       100,
		ProductType:     req.ProductType,
		ProductId:       req.ProductId,
	}

	if req.Id > 0 {
		// 更新
		_, err := session.ID(req.Id).Cols("retail_price,update_date,product_name,product_pic,service_duration,birth_date,pet_variety,pet_variety_name,market_price,channel_category_id,bar_code").Update(storeInfo)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("更新商品信息失败: %v", err)
		}
	} else {
		//先去连锁表里面拿一个product_id防止重复
		proProduct := &product_po.ProProductForUpdate{
			ProductType: req.ProductType,
			Name:        req.Name,
			UseRange:    "3",
		}
		_, err := session.Table("pro_product").Insert(proProduct)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("新增商品信息失败: %v", err)
		}
		if proProduct.Id == 0 {
			session.Rollback()
			return fmt.Errorf("新增商品信息失败: %v", err)
		}

		skuId := cast.ToInt(fmt.Sprintf("%d%s", proProduct.Id, "000")) + 1
		storeInfo.ProductId = proProduct.Id
		storeInfo.SkuId = skuId
		// 新增
		_, err = session.Insert(storeInfo)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("新增商品信息失败: %v", err)
		}
	}

	// 保存SPU信息
	storeSpu := &product_po.ProProductStoreSpu{
		ProductId:         storeInfo.ProductId,
		StoreId:           req.StoreId,
		ChannelCategoryId: req.CategoryId,
		NamePath:          req.NamePath,
		ProductType:       req.ProductType,
		ChannelId:         100,
	}

	if req.Id > 0 {
		_, err := session.Where("product_id = ? and store_id=?", storeInfo.ProductId, req.StoreId).Cols("namePath,channel_category_id").Update(storeSpu)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("更新SPU信息失败: %v", err)
		}
	} else {
		_, err := session.Insert(storeSpu)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("新增SPU信息失败: %v", err)
		}
	}

	return session.Commit()
}

// DeleteService 删除服务
func (s *StoreProductService) DeleteService(id int, storeId string) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	// 查询商品信息,增加 store_id 条件
	var storeInfo product_po.ProProductStoreInfo
	exists, err := session.ID(id).Where("store_id = ?", storeId).Get(&storeInfo)
	if err != nil {
		session.Rollback()
		return fmt.Errorf("查询商品信息失败: %v", err)
	}
	if !exists {
		session.Rollback()
		return errors.New("商品不存在或无权限删除该商品")
	}

	// 删除商品信息,增加 store_id 条件
	_, err = session.ID(id).Where("store_id = ?", storeId).Delete(&product_po.ProProductStoreInfo{})
	if err != nil {
		session.Rollback()
		return fmt.Errorf("删除商品信息失败: %v", err)
	}

	// 删除SPU信息,增加 store_id 条件
	_, err = session.Where("product_id = ? AND store_id = ?", storeInfo.ProductId, storeId).Delete(&product_po.ProProductStoreSpu{})
	if err != nil {
		session.Rollback()
		return fmt.Errorf("删除SPU信息失败: %v", err)
	}

	return session.Commit()
}

// GetService 获取服务详情
func (s *StoreProductService) GetService(req product_vo.GetServiceReq) (product_vo.ServiceDetail, error) {
	s.Begin()
	defer s.Close()
	var data product_vo.ServiceDetail
	if req.Id == 0 {
		return data, errors.New("ID不能为空")
	}
	if req.StoreId == "" {
		return data, errors.New("店铺ID不能为空")
	}
	db := s.Engine

	// 查询商品基础信息
	storeInfo := &product_po.ProProductStoreInfo{}
	_, err := db.Where("id = ? AND store_id = ? ", req.Id, req.StoreId).Get(storeInfo)
	if err != nil {
		return data, err
	}

	// 查询商品基础信息
	spu := &product_po.ProProductStoreSpu{}
	_, err = db.Where("product_id = ? AND store_id = ? ", storeInfo.ProductId, req.StoreId).Get(spu)
	if err != nil {
		return data, err
	}

	// 组装返回数据
	result := product_vo.ServiceDetail{
		Id:                storeInfo.Id,
		ProductName:       storeInfo.ProductName,
		RetailPrice:       storeInfo.RetailPrice,
		MarketPrice:       storeInfo.MarketPrice,
		ChannelCategoryId: cast.ToString(spu.ChannelCategoryId),
		ProductPic:        storeInfo.ProductPic,
		ServiceDuration:   storeInfo.ServiceDuration,
		BirthDate:         storeInfo.BirthDate,
		PetVariety:        storeInfo.PetVariety,
		PetVarietyName:    storeInfo.PetVarietyName,
		BarCode:           storeInfo.BarCode,
		ProductType:       storeInfo.ProductType,
		NamePath:          spu.NamePath,
		ProductId:         storeInfo.ProductId,
	}

	return result, nil
}

func (s StoreProductService) handleAsyncTask(productIdInts []int, channelIds []int, in product_vo.BatchStoreProductReq) (*StoreProductServiceData, error) {
	logPrefix := fmt.Sprintf("批量操作门店商品,入参:商品id为%s,渠道ids为%s,in为%s", utils.InterfaceToJSON(productIdInts), utils.InterfaceToJSON(channelIds), utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	// 1. 生成 Excel 表格
	excelFilePath, err := s.GenerateExcel(productIdInts, channelIds, in)
	log.Info(logPrefix, "生成 Excel 文件成功: %s", excelFilePath)
	if err != nil {
		log.Error(logPrefix, "生成 Excel 文件失败: %v", err)
		return nil, fmt.Errorf("生成 Excel 文件失败: %v", err)
	}

	// 确保在函数结束时删除临时文件
	defer func() {
		if err := os.Remove(excelFilePath); err != nil {
			log.Error(logPrefix, "删除临时文件失败:", err.Error())
		} else {
			log.Info(logPrefix, "成功删除临时文件:", excelFilePath)
		}
	}()

	// 2. 上传 Excel 文件
	uploadURL, err := s.UploadExcelFile(excelFilePath)
	log.Info(logPrefix, "上传 Excel 文件成功: %s", uploadURL)
	if err != nil {
		log.Error(logPrefix, "上传 Excel 文件失败: %v", err)
		return nil, fmt.Errorf("上传 Excel 文件失败: %v", err)
	}

	// 3. 创建异步任务
	task := distribution_po.TaskList{}

	// 根据操作类型设置任务内容和描述
	switch in.Type {
	case 1: // 批量铺品
		task.TaskContent = 23
		task.ExtendedData = "批量铺品商品"
	case 2: // 批量上架
		task.TaskContent = 20
		task.ExtendedData = "批量上架商品"
	case 3: // 批量下架
		task.TaskContent = 21
		task.ExtendedData = "批量下架商品"
	case 4: // 库存同步
		task.TaskContent = 30
		task.ExtendedData = "批量库存同步"
	case 6: // 批量删除
		task.TaskContent = 22
		task.ExtendedData = "批量删除商品"
	default:
		return nil, fmt.Errorf("不支持的批量操作类型: %d", in.Type)
	}
	task.OperationFileUrl = uploadURL
	task.CreateId = s.JwtInfo.UserId
	task.CreateName = s.JwtInfo.UserName
	task.CreateIp = "127.0.0.1"
	task.TaskStatus = enum.TaskStatusNotStart
	task.OrgId = 6
	task.ContextData = s.JwtInfo.TenantId
	task.OperationType = 1

	//添加
	if _, err := s.Engine.Table("task_list").Insert(&task); err != nil {
		return nil, fmt.Errorf("创建异步任务失败: %v", err)
	}
	//请求参数转为字符串
	bt, _ := json.Marshal(task)
	//插入成功之后丢到MQ去消费
	QueueName := enum.TaskQueue

	if ok := mq.PublishRabbitMQ(QueueName, string(bt), "eshop"); !ok {
		errMsg := "mq推送失败"
		log.Error("mq推送失败，", errMsg, "，", string(bt))
		s.Engine.Exec("update task_list set task_status=4,err_mes=?", errMsg)
		return nil, errors.New("创建队列任务失败")
	}
	return &StoreProductServiceData{}, nil
}

// ExcelGenerator 定义Excel生成器接口
type ExcelGenerator interface {
	GenerateExcel(f *excelize.File) error
	GetFileName() string
}

// 基础Excel生成器
type BaseExcelGenerator struct {
	ProductList []product_po.SpuInfo
	ChannelIds  []int
}

// 批量删除Excel生成器
type DeleteExcelGenerator struct {
	BaseExcelGenerator
}

// 批量上下架Excel生成器
type UpDownExcelGenerator struct {
	BaseExcelGenerator
}

// 批量铺品Excel生成器
type DistributeExcelGenerator struct {
	BaseExcelGenerator
}

// 生成Excel文件的主函数
func (s StoreProductService) GenerateExcel(productIdInts []int, channelIds []int, in product_vo.BatchStoreProductReq) (string, error) {
	// 获取商品信息
	proProductStoreList, err := new(product_po.ProProductStoreSpu).QuerySpuByStoreIdAndProductId(s.Session, in.TenantId, productIdInts, channelIds)
	if err != nil {
		return "", err
	}

	if len(proProductStoreList) == 0 {
		return "", errors.New("没有需要操作的商品")
	}

	// 根据操作类型选择对应的生成器
	var generator ExcelGenerator
	base := BaseExcelGenerator{
		ProductList: proProductStoreList,
		ChannelIds:  channelIds,
	}

	switch in.Type {
	case 6: // 批量删除
		generator = &DeleteExcelGenerator{base}
	case 2, 3, 4: // 批量上下架、库存同步
		generator = &UpDownExcelGenerator{base}
	case 1: // 批量铺品
		generator = &DistributeExcelGenerator{base}
	default:
		return "", fmt.Errorf("不支持的批量操作类型: %d", in.Type)
	}

	// 创建Excel文件
	f := excelize.NewFile()
	if err := generator.GenerateExcel(f); err != nil {
		return "", err
	}

	// 保存文件
	tempDir := os.TempDir()
	filePath := filepath.Join(tempDir, generator.GetFileName())
	if err := f.SaveAs(filePath); err != nil {
		return "", err
	}

	return filePath, nil
}

// 实现批量删除Excel生成
func (g *DeleteExcelGenerator) GenerateExcel(f *excelize.File) error {
	// 设置表头
	headers := []string{"商品ID（必填）", "商品名称"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue("Sheet1", cell, header)
	}

	// 填充数据
	for i, product := range g.ProductList {
		row := i + 2
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", row), product.ProductId)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", row), product.Name)
	}
	return nil
}

// 实现批量上下架Excel生成
func (g *UpDownExcelGenerator) GenerateExcel(f *excelize.File) error {
	// 设置表头
	headers := []string{"商品ID（必填）", "skuID", "商品条码", "商品名称", "小程序商城渠道", "美团外卖渠道", "饿了么渠道"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue("Sheet1", cell, header)
	}

	// 填充数据
	for i, product := range g.ProductList {
		row := i + 2
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", row), product.ProductId)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", row), "")
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", row), "")
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", row), product.Name)
		channelIds := strings.Split(product.ChannelIdStr, ",")
		// 设置渠道状态
		for _, channelId := range channelIds {
			switch channelId {
			case "1": // 小程序商城
				f.SetCellValue("Sheet1", fmt.Sprintf("E%d", row), 1)
			case "2": // 美团外卖
				f.SetCellValue("Sheet1", fmt.Sprintf("F%d", row), 1)
			case "3": // 饿了么
				f.SetCellValue("Sheet1", fmt.Sprintf("G%d", row), 1)
			}
		}
	}
	return nil
}

// 实现批量铺品Excel生成
func (g *DistributeExcelGenerator) GenerateExcel(f *excelize.File) error {
	// 设置表头
	headers := []string{"商品ID（必填）", "商品名称", "小程序商城渠道", "美团外卖渠道", "饿了么渠道"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue("Sheet1", cell, header)
	}

	// 填充数据
	for i, product := range g.ProductList {
		row := i + 2
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", row), product.ProductId)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", row), product.Name)
		channelIds := strings.Split(product.ChannelIdStr, ",")

		// 设置渠道状态
		for _, channelId := range channelIds {
			switch channelId {
			case "1": // 小程序商城
				f.SetCellValue("Sheet1", fmt.Sprintf("C%d", row), 1)
			case "2": // 美团外卖
				f.SetCellValue("Sheet1", fmt.Sprintf("D%d", row), 1)
			case "3": // 饿了么
				f.SetCellValue("Sheet1", fmt.Sprintf("E%d", row), 1)
			}
		}
	}
	return nil
}

// 获取文件名
func (g *DeleteExcelGenerator) GetFileName() string {
	return fmt.Sprintf("product_delete_%d_%d.xlsx", time.Now().Unix(), rand.Intn(10000))
}

func (g *UpDownExcelGenerator) GetFileName() string {
	return fmt.Sprintf("product_updown_%d_%d.xlsx", time.Now().Unix(), rand.Intn(10000))
}

func (g *DistributeExcelGenerator) GetFileName() string {
	return fmt.Sprintf("product_distribute_%d_%d.xlsx", time.Now().Unix(), rand.Intn(10000))
}

// 上传 Excel 文件
func (s StoreProductService) UploadExcelFile(filePath string) (string, error) {
	// 设置请求头
	headers := map[string]string{
		"Accept":          "application/json, text/plain, */*",
		"Accept-Language": "zh-CN,zh;q=0.9",
		"ApplicationId":   "1",
		"Authorization":   "bGFtcF93ZWI6bGFtcF93ZWJfc2VjcmV0",
		"Connection":      "keep-alive",
		"Financialcode":   s.JwtInfo.TenantId,
		"Origin":          "http://pets-saas-pre.rvet.cn",
		"RealName":        url.QueryEscape(s.JwtInfo.UserName),
		"Referer":         "http://pets-saas-pre.rvet.cn/app/product/",
		"Token":           s.JwtInfo.Token,
		"structOuterCode": "SAAS001",
		"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
	}

	// 设置表单字段
	formFields := map[string]string{
		"bizType": "product",
	}

	return utils.UploadOfflineFile(filePath, headers, formFields)
}
