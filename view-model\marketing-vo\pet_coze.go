package marketing_vo

type GuizuPetCozeChatReq struct {
	UserInfoId string `json:"user_info_id"` // 用户id
	BotType    string `json:"bot_type"`     // 智能体类型：guizu_pet_blood_coze_bot_id 宠物贵族血统鉴定师；guizu_pet_image_coze_bot_id 宠物图片生成师
	PetName    string `json:"pet_name"`     // 宠物名称
	PetGender  int    `json:"pet_gender"`   // 宠物性别 0未知 1公 2母
	PetPhoto   string `json:"pet_photo"`    // 宠物照片
	RefImg1    string `json:"ref_img1"`     // 参考图1
	RefImg2    string `json:"ref_img2"`     // 参考图2
	RefImg3    string `json:"ref_img3"`     // 参考图3
}
