package dto

type ELMOPartreFund struct {
	//订单ID
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
	//退单ID
	RefundId string `json:"refund_id" form:"refund_id" query:"refund_id"`
	//部分退款类型
	//1表示商户发起的部分退款
	//2表示用户发起的部分退款
	//3表示客服直接发起的部分退款
	Type int32 `json:"type" form:"type" query:"type"`
	//退用户总金额,单位:分
	RefundPrice int32 `json:"refund_price" form:"refund_price" query:"refund_price"`
	//部分退款状态:
	//10表示商家/用户发起部分退款申请
	//20表示部分退款成功
	//30用户申请仲裁,客服介入
	//40表示部分退款失败
	//50表示商家拒绝用户发起的部分退款申请
	Status int32 `json:"status" form:"status" query:"status"`
	//部分退款原因码,该字段只会在完单后用户部分退成功或失败时展示
	//1103表示用户撤销
	//1104表示商家拒绝用户退单,用户未申请仲裁,系统自动退单失败
	//1302表示客服仲裁退款失败
	//1301表示客服仲裁退款成功
	//1202表示商家同意
	//1304表示用户申请仲裁,客服未处理,系统自动退单成功
	//1203表示商家超时未处理,系统自动退单成功
	Reason_type string `json:"reason_type" form:"reason_type" query:"reason_type"`
	//2部分退款原因
	Reason string `json:"reason" form:"reason" query:"reason"`
	//部分退款额外原因
	AdditionReason string `json:"addition_reason" form:"reason" query:"addition_reason"`
	//用户部分退款提供的图片
	Photos []string `json:"photos" form:"photos" query:"photos"`
	//平台补贴信息
	DeliveryFee DeliveryFeeInfo `json:"delivery_fee" form:"delivery_fee" query:"delivery_fee"`
	//退款商品信息
	RefundProducts []RefundProducts `json:"refund_products" form:"refund_products" query:"refund_products"`
	//1整单退款 2部分退款
	FullRefund int32 `json:"full_refund" form:"reason" query:"full_refund"`
	//饿了么退款前的状态
	LastRefundStatus int32 `json:"last_refund_status" form:"reason" query:"last_refund_status"`
	//本次退款的配送费,单位:分
	DeliveryPrice int32 `json:"delivery_price" form:"delivery_price" query:"delivery_price"`
}

//退款配送费信息
type DeliveryFeeInfo struct {
	//2部分退款原因
	UserRate int32 `json:"user_rate" form:"user_rate" query:"user_rate"`
	//平台补贴
	ShopRate int32 `json:"shop_rate" form:"shop_rate" query:"shop_rate"`
}

//退款商品信息
type RefundProducts struct {
	//是否是赠品，0-非赠品，1-赠品
	IsFreeGift int32 `json:"is_free_gift" form:"is_free_gift" query:"is_free_gift"`
	//赠品关联主商品的子订单ID
	GmIds []string `json:"gm_ids" form:"gm_ids" query:"gm_ids"`
	//退款商品sku码
	SkuId string `json:"sku_id" form:"sku_id" query:"sku_id"`
	//退款商品upc
	Upc string `json:"upc" form:"upc" query:"upc"`
	//退款商品自定义ID,未设置为空
	CustomSkuId string `json:"custom_sku_id" form:"custom_sku_id" query:"custom_sku_id"`
	//退款商品名称
	Name string `json:"name" form:"name" query:"name"`
	//退款商品数量
	Number int32 `json:"number" form:"number" query:"number"`
	//退款商品退用户金额,单位:分
	TotalRefund int32 `json:"total_refund" form:"total_refund" query:"total_refund"`
	//子订单ID，订单逆向操作必须字段
	SubBizOrderId string `json:"sub_biz_order_id" form:"sub_biz_order_id" query:"sub_biz_order_id"`
	//商家退还给平台补贴的金额,单位:分
	ShopEleRefund int32 `json:"shop_ele_refund" form:"shop_ele_refund" query:"shop_ele_refund"`
}

type ELMReversePush struct {
	//订单ID
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
	//退单ID
	RefundOrderId interface{} `json:"refund_order_id" form:"refund_order_id" query:"refund_order_id"`
	//逆向单当前操作信息
	CurReverseEvent CurReverseEventInfo `json:"cur_reverse_event" form:"cur_reverse_event" query:"cur_reverse_event"`
}

type CurReverseEventInfo struct {
	//订单ID
	OrderId string `json:"order_id" form:"order_id" query:"order_id"`
	//退单ID
	RefundId string `json:"refund_id" form:"refund_id" query:"refund_id"`
	//逆向单操作者角色：10 用户 ,20商户,30客服 ,40系统
	OperatorRole int32 `json:"operator_role" form:"operator_role" query:"operator_role"`
	//逆向单操作前的逆向单状态：0-初始化，10-申请，20-拒绝，30-仲裁，40-关闭，50-成功，60-失败
	LastRefundStatus int32 `json:"last_refund_status" form:"last_refund_status" query:"last_refund_status"`
	//逆向单操作后的逆向单后状态（即当前的逆向单状态）：0-初始化，10-申请，20-拒绝，30-仲裁，40-关闭，50-成功，60-失败
	RefundStatus int32 `json:"refund_status" form:"refund_status" query:"refund_status"`
	//逆向单操作前的退货状态：0-无效状态，1001-买家已申请退货，等待审批处理，1002-商家拒绝退货申请，1003-退货仲裁已发起，客服介入中，1004-已同意退货，等待发货，1005-已发货，
	//等待卖家确认收货，1006-已收到货，并同意退款， 1007-未收到货，不同意退款，1008-退货关闭
	LastReturnGoodsStatus int32 `json:"last_return_goods_status" form:"last_return_goods_status" query:"last_return_goods_status"`
	//逆向单操作后的退货状态（即当前的退货状态）：0-无效状态，1001-买家已申请退货，等待审批处理，1002-商家拒绝退货申请，1003-退货仲裁已发起，客服介入中，1004-已同意退货，等待发货，1005-已发货，
	//等待卖家确认收货，1006-已收到货，并同意退款， 1007-未收到货，不同意退款，1008-退货关闭
	ReturnGoodsStatus int32 `json:"return_goods_status" form:"return_goods_status" query:"return_goods_status"`
	//逆向单操作原因对应code，具体原因枚举详见文档https://open-retail.ele.me/#/guide?topic=ntmt8f
	ReasonCode int32 `json:"reason_code" form:"reason_code" query:"reason_code"`
	//逆向单操作原因code对应的文案
	RefundReasonDesc string `json:"refund_reason_desc" form:"refund_reason_desc" query:"refund_reason_desc"`
	//逆向单操作原因备注内容
	ReasonContent string `json:"reason_content" form:"reason_content" query:"reason_content"`
	//逆向单操作原因备注内容
	OccurTime int64 `json:"occur_time" form:"occur_time" query:"occur_time"`
	//操作凭证列表
	ImageList []string `json:"image_list" form:"image_list" query:"image_list"`
}
