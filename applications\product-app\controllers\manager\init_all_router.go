package manager

/**
给管理后台用的api
*/
import (
	"github.com/go-chi/chi/v5"
)

func InitAllManager(r chi.Router) {

	// 连锁商品库 相关路由
	InitChainProductRouter(r)

	// 商品分类 相关路由
	InitProductCategoryRouter(r)
}

func InitChainProductRouter(r chi.Router) {
	r.Route("/product-app/manager/chain", func(r chi.Router) {
		// 新增连锁商品
		r.Post("/add", AddChainProduct)
		// 连锁商品详情
		r.Post("/info", GetChainProductInfo)
		// 连锁商品列表
		r.Post("/list", FindChainProductList)
		// 批量下发
		r.Post("/batch-push", BatchPushChainProduct)
		// 编辑连锁商品
		r.Post("/edit", EditChainProduct)
		//删除连锁商品
		r.Post("/del", DelChainProduct)
		//生成商品条码
		r.Post("/gene-barcode", GenerateBarCode)
		//连锁商品导出
		r.Post("/export", ChainProductExport)
	})
}

func InitProductCategoryRouter(r chi.Router) {

	r.Route("/product-app/manager", func(r chi.Router) {

		//门店商品列表
		r.Post("/list", FindStoreProductList)
		//门店商品sku列表
		r.Post("/list-sku", FindStoreSkuList)
		// 门店入库商品sku列表
		r.Post("/list-in-out", FindStoreInOutList)

		//添加、编辑分类
		r.Post("/category/add", AddCategory)
		//删除分类
		r.Post("/category/del", DelCategory)
		//分类列表
		r.Post("/category/list", CategoryList)
		//单一门店同步所有分类
		r.Post("/category/sync", SyncAllCategory)
		//门店商品批量操作
		r.Post("/batch_product", BatchStoreProduct)
		//门店商品编辑
		r.Post("/edit", EditStoreProduct)
		r.Post("/info", GetStoreProductInfo)
		//批量任务导入
		r.Post("/task-import", TaskImport)
		//门店商品导出
		r.Post("/export", Export)
		r.Get("/task-list", GetTaskList)

		//查看所有上架商品
		r.Post("/store-product-up", GetStoreProductUp)

		//查看各门店上架商品
		r.Post("/count-by-price", GetStoresCountByPrice)

		//阿闻上传工具，上传商品和分类
		r.Post("/batch-import-product-and-category", BatchImportProductAndCategory)

		// 更新商品库位信息
		r.Post("/store-product/update-location", UpdateProductLocation)
		// 库位更换绑定商品
		r.Post("/store-product/update-location-sku", UpdateLocationSku)

		// 服务管理相关接口
		r.Post("/service/list", GetServiceList)
		r.Post("/service/save", SaveService)
		r.Post("/service/delete", DeleteService)
		r.Post("/service/detail", GetService)

	})

}
