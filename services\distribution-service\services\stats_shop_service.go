package services

import (
	"eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	dissettlement "eShop/services/distribution-service/enum/dis-settlement"
	diswithdraw "eShop/services/distribution-service/enum/dis-withdraw"
	"eShop/services/distribution-service/enum/stats"
	"eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
)

type StatsShopService struct {
	common.BaseService
}

type Settlement struct {
	ShopId              int
	DisId               int
	UnsettledCommission int    // 未结佣金
	SettledCommission   int    // 已结佣金
	Commission          int    // 总佣金
	Type                int    // 类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）
	ShopName            string // 分销店铺名称
	EnterpriseName      string //企业名称
	EnterpriseId        int64  // 企业编码
	IsDis               int
	StatDate            string
	EndDate             string
}

// 管理端-数据总览-看板数据
func (s StatsShopService) GetKanbanOverview(in distribution_vo.GetKanbanOverviewReq) (out distribution_vo.GetKanbanOverview, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("获取数据总览-看板数据 入参:%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	// 普通订单累计数据 普通订单新增数据 普通订单上一周期数据
	var totalData, curData, lastData distribution_po.StatsShopDistributorDaily
	// 分销订单累计数据 分销订单新增数据 分销订单上一周期数据
	var disTotalData, disCurData, disLastData distribution_po.StatsShopDistributorDaily
	// 提现成功累计数据 提现成功新增数据 提现成功上一周期数据
	var withdrawSucTotal, withdrawSucCur, withdrawSucLast, withdrawApplyTotal, withdrawApplyCur, withdrawApplyLast int
	if len(in.StartDate) == 0 || len(in.EndDate) == 0 {
		log.Error(logPrefix, "开始日期和结束日期都不能为空")
		err = errors.New("开始日期和结束日期都不能为空")
		return
	}
	startDate, err := time.ParseInLocation(utils.DateLayout, in.StartDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "开始日期格式错误err=", err.Error())
		err = errors.New("开始日期格式错误")
		return
	}
	endDate, err := time.ParseInLocation(utils.DateLayout, in.EndDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "结束日期格式错误err=", err.Error())
		err = errors.New("结束日期格式错误")
		return
	}

	// 获取 数据总览-看板-》订单数据概览
	totalData, curData, lastData, err = GetStatsShopDistributorDaily(s.Engine, map[string]interface{}{
		//"types":     []int{stats.StatOrderType, stats.StatInsOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout),
	})
	if err != nil {
		log.Error(logPrefix, "获取订单数据概览失败，err=", err.Error())
		err = errors.New("获取订单数据概览失败")
		return
	}
	// 获取 数据总览-看板-》分销订单数据概览
	disTotalData, disCurData, disLastData, err = GetStatsShopDistributorDaily(s.Engine, map[string]interface{}{
		"isDis":     1,
		"types":     []int{stats.StatDisOrderType, stats.StatDisInsOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout),
	})
	if err != nil {
		log.Error(logPrefix, "获取分销订单数据概览失败，err=", err.Error())
		err = errors.New("获取分销订单数据概览失败")
		return
	}

	// 获取 数据总览-看板 -》佣金概览-》佣金提现 数据
	withdrawSucTotal, withdrawSucCur, withdrawSucLast, err = GetDisWithdraw(s.Engine, map[string]interface{}{"orgId": in.OrgId, "status": diswithdraw.StatusChecked, "startDate": startDate.Format(utils.DateLayout), "endDate": endDate.Format(utils.DateLayout)})
	if err != nil {
		log.Error(logPrefix, "获取佣金提现成功数据失败，err=", err.Error())
		err = errors.New("获取佣金提现成功数据失败")
		return
	}

	withdrawApplyTotal, withdrawApplyCur, withdrawApplyLast, err = GetDisWithdraw(s.Engine, map[string]interface{}{"orgId": in.OrgId, "status": diswithdraw.StatusUncheck, "startDate": startDate.Format(utils.DateLayout), "endDate": endDate.Format(utils.DateLayout)})
	if err != nil {
		log.Error(logPrefix, "获取佣金提现申请数据失败，err=", err.Error())
		err = errors.New("获取佣金提现申请数据失败")
		return
	}

	// 组织返回数据
	out = OrgKanbanOverview(totalData, curData, lastData, disTotalData, disCurData, disLastData)
	// 累计分销佣金 和 累计待结佣金和累计已结佣金 查 dis_settlement表+表dis_insurance_settle
	goodsSettTotal, goodsUnsett, goodsSetted := GetSettleInfo(s.Engine)
	insSettTotal, insUnsett, insSetted := GetInsSettleInfo(s.Engine)

	out.Commission.Comm.CommissionTotal = goodsSettTotal + insSettTotal    // 累计分销佣金
	out.Commission.Comm.UnsettledCommissionTotal = goodsUnsett + insUnsett // 累计未结佣金
	out.Commission.Comm.SettledCommissionTotal = goodsSetted + insSetted   // 累计已结佣金

	out.Commission.Withdraw = distribution_vo.WithdrawData{
		// 累计提现成功(分)
		WithdrawSuccessTotal: withdrawSucTotal,
		// 新增提现成功(分)
		WithdrawSuccess: withdrawSucCur,
		// 新增提现成功 较上一周期百分比
		WithdrawSuccessPercent: utils.CalProportion(withdrawSucCur, withdrawSucLast),
		// 累计提现申请(分)
		WithdrawApplyTotal: withdrawApplyTotal,
		// 新增提现申请(分)
		WithdrawApply: withdrawApplyCur,
		// 新增提现申请 较上一周期百分比
		WithdrawApplyPercent: utils.CalProportion(withdrawApplyCur, withdrawApplyLast),
	}
	return

}

// 管理端-商品订单-看板数据
func (s StatsShopService) GetKanbanOrder(in distribution_vo.GetKanbanOverviewReq) (out distribution_vo.GetKanbanOrder, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("获取商品订单-看板数据 入参:%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	// 普通订单累计数据 普通订单新增数据 普通订单上一周期数据
	var totalData, curData, lastData distribution_po.StatsShopDistributorDaily
	// 分销订单累计数据 分销订单新增数据 分销订单上一周期数据
	var disTotalData, disCurData, disLastData distribution_po.StatsShopDistributorDaily

	if len(in.StartDate) == 0 || len(in.EndDate) == 0 {
		log.Error(logPrefix, "开始日期和结束日期都不能为空")
		err = errors.New("开始日期和结束日期都不能为空")
		return
	}
	startDate, err := time.ParseInLocation(utils.DateLayout, in.StartDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "开始日期格式错误err=", err.Error())
		err = errors.New("开始日期格式错误")
		return
	}
	endDate, err := time.ParseInLocation(utils.DateLayout, in.EndDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "结束日期格式错误err=", err.Error())
		err = errors.New("结束日期格式错误")
		return
	}

	// 获取 商品订单-看板-》订单数据概览
	totalData, curData, lastData, err = GetStatsShopDistributorDaily(s.Engine, map[string]interface{}{
		"type":      1,
		"types":     []int{stats.StatOrderType, stats.StatDisOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout),
	})
	if err != nil {
		log.Error(logPrefix, "获取订单数据概览失败，err=", err.Error())
		err = errors.New("获取订单数据概览失败")
		return
	}
	// 获取 商品订单-看板-》分销订单数据概览
	disTotalData, disCurData, disLastData, err = GetStatsShopDistributorDaily(s.Engine, map[string]interface{}{
		"type":      1,
		"isDis":     1,
		"types":     []int{stats.StatDisOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout),
	})
	if err != nil {
		log.Error(logPrefix, "获取分销订单数据概览失败，err=", err.Error())
		err = errors.New("获取分销订单数据概览失败")
		return
	}

	// 组织返回数据
	out = OrgKanbanOrder(totalData, curData, lastData, disTotalData, disCurData, disLastData)
	// 累计分销佣金 和 累计待结佣金和累计已结佣金 查 dis_settlement表
	goodsSettTotal, goodsUnsett, goodsSetted := GetSettleInfo(s.Engine)

	out.DisOrderData.DisCommission.CommissionTotal = goodsSettTotal
	out.DisOrderData.DisCommission.UnsettledCommissionTotal = goodsUnsett
	out.DisOrderData.DisCommission.SettledCommissionTotal = goodsSetted

	// 获取分销企业数据
	var entTotalData, entCurData, entLastData distribution_po.StatsEnterpriseDaily
	entTotalData, entCurData, entLastData, err = GetStatsEnterpriseDaily(s.Engine, map[string]interface{}{
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout),
	})
	if err != nil {
		log.Error(logPrefix, "获取分销企业数据，err=", err.Error())
		err = errors.New("获取分销企业数据")
		return
	}
	out.DisOrderData.StatsEntTransView = distribution_vo.StatsEntTransView{
		// 商品订单累计成交企业
		TotalTransEnterprise: entTotalData.TotalTransEnterprise,
		// 商品订单累计成交分销员
		TotalTransDistributor: entTotalData.TotalTransDistributor,
		// 商品订单新增成交企业
		TotalTransEnterpriseNew: entCurData.TotalTransEnterprise,
		// 新增成交分销员
		TotalTransDistributorNew: entCurData.TotalTransDistributor,
		// 商品订单新增成交企业 较上一周期百分比
		TotalTransEnterpriseNewPercent: utils.CalProportion(entCurData.TotalTransEnterprise, entLastData.TotalTransEnterprise),
		// 商品订单新增成交分销员 较上一周期百分比
		TotalTransDistributorNewPercent: utils.CalProportion(entCurData.TotalTransDistributor, entLastData.TotalTransDistributor),
	}

	return

}

// 管理端-保险订单-看板数据
func (s StatsShopService) GetKanbanInsOrder(in distribution_vo.GetKanbanOverviewReq) (out distribution_vo.GetKanbanInsOrder, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("获取保险订单-看板数据 入参:%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	// 普通订单累计数据 普通订单新增数据 普通订单上一周期数据
	var totalData, curData, lastData distribution_po.StatsShopDistributorDaily
	// 分销订单累计数据 分销订单新增数据 分销订单上一周期数据
	var disTotalData, disCurData, disLastData distribution_po.StatsShopDistributorDaily

	if len(in.StartDate) == 0 || len(in.EndDate) == 0 {
		log.Error(logPrefix, "开始日期和结束日期都不能为空")
		err = errors.New("开始日期和结束日期都不能为空")
		return
	}
	startDate, err := time.ParseInLocation(utils.DateLayout, in.StartDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "开始日期格式错误err=", err.Error())
		err = errors.New("开始日期格式错误")
		return
	}
	endDate, err := time.ParseInLocation(utils.DateLayout, in.EndDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "结束日期格式错误err=", err.Error())
		err = errors.New("结束日期格式错误")
		return
	}

	// 获取 保险订单-看板-》订单数据概览
	totalData, curData, lastData, err = GetStatsShopDistributorDaily(s.Engine, map[string]interface{}{
		"type":      2,
		"types":     []int{stats.StatInsOrderType, stats.StatDisInsOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout),
	})
	if err != nil {
		log.Error(logPrefix, "获取订单数据概览失败，err=", err.Error())
		err = errors.New("获取订单数据概览失败")
		return
	}
	// 获取 商品订单-看板-》分销订单数据概览
	disTotalData, disCurData, disLastData, err = GetStatsShopDistributorDaily(s.Engine, map[string]interface{}{
		"isDis":     1,
		"type":      2,
		"types":     []int{stats.StatDisInsOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout),
	})
	if err != nil {
		log.Error(logPrefix, "获取分销订单数据概览失败，err=", err.Error())
		err = errors.New("获取分销订单数据概览失败")
		return
	}

	// 组织返回数据
	out = OrgKanbanInsOrder(totalData, curData, lastData, disTotalData, disCurData, disLastData)
	// 累计分销佣金 和 累计待结佣金和累计已结佣金 查表dis_insurance_settle
	insSettTotal, insUnsett, insSetted := GetInsSettleInfo(s.Engine)

	out.DisOrderData.DisCommission.CommissionTotal = insSettTotal
	out.DisOrderData.DisCommission.UnsettledCommissionTotal = insUnsett
	out.DisOrderData.DisCommission.SettledCommissionTotal = insSetted

	// 获取分销企业数据
	var entTotalData, entCurData, entLastData distribution_po.StatsEnterpriseDaily
	entTotalData, entCurData, entLastData, err = GetStatsEnterpriseDaily(s.Engine, map[string]interface{}{
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout)})
	if err != nil {
		log.Error(logPrefix, "获取分销企业数据，err=", err.Error())
		err = errors.New("获取分销企业数据")
		return
	}
	out.DisOrderData.StatsEntInsTransView = distribution_vo.StatsEntInsTransView{
		// 商品订单累计成交企业
		InsTotalTransEnterprise: entTotalData.InsTotalTransEnterprise,
		// 商品订单累计成交分销员
		InsTotalTransDistributor: entTotalData.InsTotalTransDistributor,
		// 商品订单新增成交企业
		InsTotalTransEnterpriseNew: entCurData.InsTotalTransEnterprise,
		// 新增成交分销员
		InsTotalTransDistributorNew: entCurData.InsTotalTransDistributor,
		// 商品订单新增成交企业 较上一周期百分比
		InsTotalTransEnterpriseNewPercent: utils.CalProportion(entCurData.InsTotalTransEnterprise, entLastData.InsTotalTransEnterprise),
		// 商品订单新增成交分销员 较上一周期百分比
		InsTotalTransDistributorNewPercent: utils.CalProportion(entCurData.InsTotalTransDistributor, entLastData.InsTotalTransDistributor),
	}

	return

}

// 数据统计-分销员中心-数据中心-首页
func (s StatsShopService) StatsDisCenterIndex(in distribution_vo.StatsDisCenterIndexReq) (out distribution_vo.StatsDisCenterIndex, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("用户端-分销员中心-数据中心-首页数据，入参：%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	// 获取分销员信息
	disInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{"memberId": in.MemberId, "orgId": in.OrgId}, "id,dis_role,shop_id,member_id")
	if err != nil {
		log.Error(logPrefix, "获取分销员信息失败err=", err.Error())
		return
	}
	if disInfo.Id == 0 || disInfo.ShopId == 0 {
		log.Error(logPrefix, "分销员信息不存在")
		err = errors.New("分销员信息不存在")
		return
	}
	if in.Flag == 1 && disInfo.DisRole != disdistributor.DisRoleBoss {
		log.Error(logPrefix, "参数有误，查看老板数据时，但是分销员自己不是老板")
		err = errors.New("参数有误，查看老板数据时，但是分销员自己不是老板")
		return
	}

	// 获取分销员的累计数据
	totalData, err := distribution_po.StatsDistributorData(s.Engine, map[string]interface{}{"disId": disInfo.Id, "orgId": in.OrgId, "shopId": disInfo.ShopId})
	if err != nil {
		log.Error(logPrefix, "获取累计数据失败err=", err.Error())
		err = errors.New("获取累计数据失败")
		return
	}
	// TransCustomerCount, InsTransCustomerCount := s.GetCustomerCount(map[string]interface{}{"isDis": 1, "disMemberId": disInfo.MemberId, "shopId": disInfo.ShopId, "disId": disInfo.Id})
	// totalData.TotalCustomer = TransCustomerCount + InsTransCustomerCount

	// 获取分销员本月数据
	thisMonthFirstDay, thisMonthLastDay := utils.GetMonthFirstLast()
	w := map[string]interface{}{
		"statDateStart": thisMonthFirstDay,
		"statDateEnd":   thisMonthLastDay,
		"types":         []int{stats.StatDisOrderType, stats.StatDisInsOrderType},
		"disId":         disInfo.Id,
		"isDis":         1,
		"disMemberId":   in.MemberId,
		"shopId":        disInfo.ShopId,
	}
	thisMonthData, err := distribution_po.GetShopDistributorData(s.Engine, w)
	if err != nil {
		log.Error(logPrefix, "获取分销员本月数据失败err=", err.Error())
		err = errors.New("获取分销员本月数据")
		return
	}
	// 实时统计本月佣金和本月待结佣金
	thisMonthCommission1, thisMonthUnsett1 := 0, 0
	thisMonthCommission1, thisMonthUnsett1, err = s.GetThisMonthComm(map[string]interface{}{
		"statDate": thisMonthFirstDay,
		"endDate":  thisMonthLastDay,
		"disId":    disInfo.Id,
		"shopId":   disInfo.ShopId,
	})
	if err != nil {
		log.Error(logPrefix, "获取分销员本月佣金数据失败err=", err.Error())
		err = errors.New("获取分销员本月佣金数据")
		return
	}
	out.Distributor = distribution_vo.StatsDisCenterIndexMy{
		ThisMonthCommission:          thisMonthCommission1,
		ThisMonthUnsettledCommission: thisMonthUnsett1,
		ThisMonthTransCount:          thisMonthData.TransCount,
		ThisMonthTransCustomerCount:  thisMonthData.TransCustomerCount,
		// 累计佣金(单位分)
		Commission: totalData.Commission,
		// 待结佣金
		UnsettledCommission: totalData.UnsettledCommission,
		// 累计订单
		TransCount: totalData.TransCount,
		// 累计客户
		TotalCustomer: totalData.TotalCustomer,
	}

	//获取企业数据
	if in.Flag == 1 {
		// 获取店铺的累计数据
		totalData, err = distribution_po.StatsDistributorData(s.Engine, map[string]interface{}{
			"shopId": disInfo.ShopId,
			"orgId":  in.OrgId,
		})
		if err != nil {
			log.Error(logPrefix, "获取店铺累计数据失败err=", err.Error())
			err = errors.New("获取店铺累计数据失败")
			return
		}
		// TransCustomerCount, InsTransCustomerCount := s.GetCustomerCount(map[string]interface{}{"isDis": 1, "shopId": disInfo.ShopId})
		// totalData.TotalCustomer = TransCustomerCount + InsTransCustomerCount

		w = map[string]interface{}{
			"statDateStart": thisMonthFirstDay,
			"statDateEnd":   thisMonthLastDay,
			"types":         []int{stats.StatDisOrderType, stats.StatDisInsOrderType},
			"shopId":        disInfo.ShopId,
		}
		thisMonthData, err = distribution_po.GetShopDistributorData(s.Engine, w)
		if err != nil {
			log.Error(logPrefix, "获取店铺本月数据失败err=", err.Error())
			err = errors.New("获取店铺本月数据")
			return
		}
		// 实时统计本月佣金和本月待结佣金
		thisMonthCommission2, thisMonthUnsett2 := 0, 0
		thisMonthCommission2, thisMonthUnsett2, err = s.GetThisMonthComm(map[string]interface{}{
			"statDate": thisMonthFirstDay,
			"endDate":  thisMonthLastDay,
			"shopId":   disInfo.ShopId,
		})
		if err != nil {
			log.Error(logPrefix, "获取店铺本月佣金数据失败err=", err.Error())
			err = errors.New("获取店铺本月佣金数据")
			return
		}
		out.Shop = distribution_vo.StatsDisCenterIndexShop{
			// 本月佣金
			ThisMonthCommission: thisMonthCommission2,
			// 本月待结算佣金
			ThisMonthUnsettledCommission: thisMonthUnsett2,
			// 本月订单
			ThisMonthTransCount: thisMonthData.TransCount,
			// 累计分销员数
			DistributorCnt: totalData.DistributorCnt,
			// 累计佣金(单位分)
			Commission: totalData.Commission,
			// 待结佣金
			UnsettledCommission: totalData.UnsettledCommission,
			// 累计订单
			TransCount: totalData.TransCount,
			// 累计客户
			TotalCustomer: totalData.TotalCustomer,
			// 可提现金额
			WaitWithdraw: totalData.WaitWithdraw,
		}

	}
	return
}

// 获取分销员本月佣金和分销员本月待结佣金
// 获取店铺本月佣金和店铺本月待结佣金
func (s StatsShopService) GetThisMonthComm(where map[string]interface{}) (ThisMonthCommission, ThisMonthUnsett int, err error) {

	statDate, ok := where["statDate"]
	if !ok {
		err = errors.New("统计开始日期不能为空")
		return
	}
	start := statDate.(string)
	endDate, ok := where["endDate"]
	if !ok {
		err = errors.New("统计结束日期不能为空")
		return
	}
	end := endDate.(string) + " 23:59:59"
	w1 := map[string]interface{}{"orgId": 3, "createTimeStart": start, "createTimeEnd": end}
	w2 := map[string]interface{}{"orgId": 3, "status": dissettlement.StatusUnSett, "createTimeStart": start, "createTimeEnd": end}
	w3 := map[string]interface{}{"orgId": 3, "createTimeStart": start, "createTimeEnd": end}
	w4 := map[string]interface{}{"orgId": 3, "state": dissettlement.StatusUnSett, "createTimeStart": start, "createTimeEnd": end}
	shopId, ok := where["shopId"]
	if ok && shopId.(int) > 0 {
		w1["shopId"] = shopId.(int)
		w2["shopId"] = shopId.(int)
		w3["shopId"] = shopId.(int)
		w4["shopId"] = shopId.(int)
	}

	disId, ok := where["disId"]
	if ok && disId.(int) > 0 {
		w1["disId"] = disId.(int)
		w2["disId"] = disId.(int)
		w3["disId"] = disId.(int)
		w4["disId"] = disId.(int)
	}
	var goodsComm, goodsUnsettComm, insComm, insUnsettComm int
	// 获取电商本月佣金和本月待结佣金
	data1, err := distribution_po.FindSettCommission(s.Engine, w1)
	if err != nil {
		return
	}

	if len(data1) > 0 {
		goodsComm = data1[0].Commission
	}

	// 商品订单未结佣金
	data2, err := distribution_po.FindSettCommission(s.Engine, w2)
	if err != nil {
		return
	}
	if len(data2) > 0 {
		goodsUnsettComm = data2[0].Commission
	}

	// 保险订单分销佣金

	data3, err := distribution_po.FindInsSettCommission(s.Engine, w3)
	if err != nil {
		return
	}
	if len(data3) > 0 {
		insComm = data3[0].Commission
	}

	// 保险订单未结佣金

	data4, err := distribution_po.FindInsSettCommission(s.Engine, w4)
	if err != nil {
		return
	}

	if len(data4) > 0 {
		insUnsettComm = data4[0].Commission
	}

	ThisMonthCommission = goodsComm + insComm
	ThisMonthUnsett = goodsUnsettComm + insUnsettComm
	return
}

// 用户端-分销员中心-数据中心-数据概览
func (s StatsShopService) StatsDisCenterOverview(in distribution_vo.StatsDisCenterOverviewReq) (out distribution_vo.StatsDisCenterOverview, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("用户端-分销员中心-数据中心-数据概览 入参:%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	if len(in.StartDate) == 0 || len(in.EndDate) == 0 {
		log.Error(logPrefix, "开始日期和结束日期都不能为空")
		err = errors.New("开始日期和结束日期都不能为空")
		return
	}

	startDate, err := time.ParseInLocation(utils.DateLayout2, in.StartDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "开始日期格式错误err=", err.Error())
		err = errors.New("开始日期格式错误")
		return
	}
	endDate, err := time.ParseInLocation(utils.DateLayout2, in.EndDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "结束日期格式错误err=", err.Error())
		err = errors.New("结束日期格式错误")
		return
	}

	// 查询stats_shop_distributor_daily条件
	w1 := map[string]interface{}{
		"types":     []int{stats.StatDisOrderType, stats.StatDisInsOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout)}
	w2 := map[string]interface{}{
		"type":      1,
		"types":     []int{stats.StatDisOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout)}
	w3 := map[string]interface{}{
		"type":      2,
		"types":     []int{stats.StatDisInsOrderType},
		"startDate": startDate.Format(utils.DateLayout),
		"endDate":   endDate.Format(utils.DateLayout)}
	log.Info(logPrefix, "组织的查询条件是：w1为", utils.InterfaceToJSON(w1), ",w2为", utils.InterfaceToJSON(w2), ",w3为", utils.InterfaceToJSON(w3))

	// 获取分销员信息
	disInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{"memberId": in.MemberId, "orgId": in.OrgId}, "id,dis_role,shop_id,member_id")
	if err != nil {
		log.Error(logPrefix, "获取分销员信息失败err=", err.Error())
		err = errors.New("获取分销员信息失败")
		return
	}
	if disInfo.Id == 0 || disInfo.ShopId == 0 {
		log.Error(logPrefix, "分销员信息不存在")
		err = errors.New("分销员信息不存在")
		return
	}
	if in.Flag == 1 && disInfo.DisRole != disdistributor.DisRoleBoss {
		log.Error(logPrefix, "只有老板方可查看")
		err = errors.New("只有老板方可查看")
		return
	}

	if in.Flag == 1 {
		w1["shopId"] = disInfo.ShopId
		w2["shopId"] = disInfo.ShopId
		w3["shopId"] = disInfo.ShopId
	} else {
		w1["disId"] = disInfo.Id
		w2["disId"] = disInfo.Id
		w3["disId"] = disInfo.Id
		w1["disMemberId"] = disInfo.MemberId
		w2["disMemberId"] = disInfo.MemberId
		w3["disMemberId"] = disInfo.MemberId

	}

	// 分销订单累计数据 分销订单新增数据 分销订单上一周期数据
	var curData, lastData distribution_po.StatsShopDistributorDaily
	// 商品分销订单累计数据 商品分销订单新增数据 商品分销订单上一周期数据
	var GoodsCurData, GoodsLastData distribution_po.StatsShopDistributorDaily
	// 保险分销订单累计数据 保险分销订单新增数据 保险分销订单上一周期数据
	var InsCurData, InsLastData distribution_po.StatsShopDistributorDaily
	// 获取 分销员中心-数据中心-》商品分销数据和保险分销数据之和
	_, curData, lastData, err = GetStatsShopDistributorDaily(s.Engine, w1)
	if err != nil {
		log.Error(logPrefix, "获取 分销员中心-数据中心-》商品分销数据和保险分销数据之和失败，err=", err.Error())
		err = errors.New("获取 分销员中心-数据中心-》商品分销数据和保险分销数据之和失败")
		return
	}
	// 获取 分销员中心-数据中心-》商品分销数据
	_, GoodsCurData, GoodsLastData, err = GetStatsShopDistributorDaily(s.Engine, w2)
	if err != nil {
		log.Error(logPrefix, "获取 分销员中心-数据中心-》商品分销数据失败，err=", err.Error())
		err = errors.New("获取 分销员中心-数据中心-》商品分销数据失败")
		return
	}

	// 获取 分销员中心-数据中心-》保险分销数据
	_, InsCurData, InsLastData, err = GetStatsShopDistributorDaily(s.Engine, w3)
	if err != nil {
		log.Error(logPrefix, "获取 分销员中心-数据中心-》保险分销数据失败，err=", err.Error())
		err = errors.New("获取 分销员中心-数据中心-》保险分销数据失败")
		return
	}

	out = OrgStatsDisCenterOverview(curData, lastData, GoodsCurData, GoodsLastData, InsCurData, InsLastData)
	return
}

// 用户端-分销员中心-数据中心-数据概览-曲线图
func (s StatsShopService) StatsDisCenterGraph(in distribution_vo.StatsDisCenterGraphReq) (out []distribution_vo.StatsDisGraphData, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("获取用户端-分销员中心-数据中心-数据概览-曲线图数据 入参:%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	if len(in.StartDate) == 0 || len(in.EndDate) == 0 {
		log.Error(logPrefix, "开始日期和结束日期都不能为空")
		err = errors.New("开始日期和结束日期都不能为空")
		return
	}
	startDate, err := time.ParseInLocation(utils.DateLayout2, in.StartDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "开始日期格式错误err=", err.Error())
		err = errors.New("开始日期格式错误")
		return
	}
	endDate, err := time.ParseInLocation(utils.DateLayout2, in.EndDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "结束日期格式错误err=", err.Error())
		err = errors.New("结束日期格式错误")
		return
	}

	// 获取分销员信息
	disInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{"memberId": in.MemberId, "orgId": in.OrgId}, "id,dis_role,shop_id")
	if err != nil {
		log.Error(logPrefix, "获取分销员信息失败err=", err.Error())
		return
	}
	if disInfo.Id == 0 || disInfo.ShopId == 0 {
		log.Error(logPrefix, "分销员信息不存在")
		err = errors.New("分销员信息不存在")
		return
	}
	if in.Flag == 1 && disInfo.DisRole != disdistributor.DisRoleBoss {
		log.Error(logPrefix, "参数有误，查看老板数据时，但是分销员自己不是老板")
		err = errors.New("参数有误，查看老板数据时，但是分销员自己不是老板")
		return
	}
	w := map[string]interface{}{"types": []int{stats.StatDisOrderType, stats.StatDisInsOrderType}, "groupBy": "stat_date", "statDateStart": startDate.Format(utils.DateLayout), "statDateEnd": endDate.Format(utils.DateLayout)} // 查询stats_shop_distributor_daily条件
	if in.Flag == 1 {
		w["shopId"] = disInfo.ShopId
	} else {
		w["disId"] = disInfo.Id
	}
	log.Info(logPrefix, "组织的查询条件是：w为", utils.InterfaceToJSON(w))
	data, err := distribution_po.GetShopDistributorList(s.Engine, w)
	if err != nil {
		log.Error(logPrefix, "获取数据失败，err=", err.Error())
		err = errors.New("获取数据失败")
		return
	}
	out = make([]distribution_vo.StatsDisGraphData, 0)
	for _, v := range data {
		switch in.GraphType {
		case 1:
			out = append(out, distribution_vo.StatsDisGraphData{StatDate: v.StatDate, Num: v.TransCount})
		case 2:
			out = append(out, distribution_vo.StatsDisGraphData{StatDate: v.StatDate, Num: v.TransAmount})
		case 3:
			out = append(out, distribution_vo.StatsDisGraphData{StatDate: v.StatDate, Num: v.Commission})
		case 4:
			out = append(out, distribution_vo.StatsDisGraphData{StatDate: v.StatDate, Num: v.TransCustomerPrice})
		default:
			err = errors.New("曲线图类型参数有误")
			return
		}
	}

	return
}

// 用户端-分销员中心-数据中心-数据概览-累计数据
func (s StatsShopService) StatsDisCenterTotal(in distribution_vo.StatsDisCenterTotalReq) (out distribution_vo.StatsDisCenterTotal, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("用户端-分销员中心-数据中心-数据概览-累计数据，入参：%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	// 获取分销员信息
	disInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{"memberId": in.MemberId, "orgId": in.OrgId}, "id,dis_role,shop_id")
	if err != nil {
		log.Error(logPrefix, "获取分销员信息失败err=", err.Error())
		return
	}
	if disInfo.Id == 0 || disInfo.ShopId == 0 {
		log.Error(logPrefix, "分销员信息不存在")
		err = errors.New("分销员信息不存在")
		return
	}
	if in.Flag == 1 && disInfo.DisRole != disdistributor.DisRoleBoss {
		log.Error(logPrefix, "参数有误，查看老板数据时，但是分销员自己不是老板")
		err = errors.New("参数有误，查看老板数据时，但是分销员自己不是老板")
		return
	}
	w := make(map[string]interface{})
	if in.Flag == 1 {
		w["shopId"] = disInfo.ShopId
	} else {
		w["disId"] = disInfo.Id

	}
	if out, err = distribution_po.StatsDistributorData(s.Engine, w); err != nil {
		log.Error(logPrefix, "获取数据失败err=", err.Error())
		err = errors.New("获取数据失败")
		return
	}

	return
}

// 用户端-分销员中心-数据中心-分销员数据
func (s StatsShopService) StatsDisCenterDistributor(in distribution_vo.StatsDisCenterDistributorReq) (out []distribution_vo.DisCenterDistributor, total int, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("用户端-分销员中心-数据中心-分销员数据 入参:%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	var startDate, endDate time.Time
	if (len(in.StartDate) > 0 && len(in.EndDate) == 0) || (len(in.StartDate) == 0 && len(in.EndDate) > 0) {
		log.Error(logPrefix, "开始日期和结束日期要么都为空，要么都不为空")
		err = errors.New("开始日期和结束日期要么都为空，要么都不为空")
		return
	}
	if len(in.StartDate) > 0 {
		startDate, err = time.ParseInLocation(utils.DateLayout2, in.StartDate, time.Local)
		if err != nil {
			log.Error(logPrefix, "开始日期格式错误err=", err.Error())
			err = errors.New("开始日期格式错误")
			return
		}
	}
	if len(in.EndDate) > 0 {
		endDate, err = time.ParseInLocation(utils.DateLayout2, in.EndDate, time.Local)
		if err != nil {
			log.Error(logPrefix, "结束日期格式错误err=", err.Error())
			err = errors.New("结束日期格式错误")
			return
		}
	}
	if in.PageIndex == 0 {
		in.PageIndex = 1
	}
	if in.PageSize == 0 {
		in.PageSize = 10
	}

	// 获取分销员信息
	disInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{"memberId": in.MemberId, "orgId": in.OrgId}, "id,dis_role,shop_id")
	if err != nil {
		log.Error(logPrefix, "获取分销员信息失败err=", err.Error())
		return
	}
	if disInfo.Id == 0 || disInfo.ShopId == 0 {
		log.Error(logPrefix, "分销员信息不存在")
		err = errors.New("分销员信息不存在")
		return
	}
	if disInfo.DisRole != disdistributor.DisRoleBoss {
		log.Error(logPrefix, "只有老板才能查看分销员数据")
		err = errors.New("只有老板才能查看分销员数据")
		return
	}
	// 查询stats_shop_distributor_daily条件
	w1 := map[string]interface{}{"shopId": disInfo.ShopId, "pageIndex": in.PageIndex, "pageSize": in.PageSize, "types": []int{stats.StatDisOrderType, stats.StatDisInsOrderType}, "groupBy": "dis_id"}
	w2 := map[string]interface{}{"shopId": disInfo.ShopId, "types": []int{stats.StatDisOrderType, stats.StatDisInsOrderType}, "groupBy": "dis_id,type"}
	if len(in.DisName) > 0 {
		w1["disName"] = in.DisName
	}
	if !startDate.IsZero() {
		w1["startDate"] = startDate.Format(utils.DateLayout)
		w2["startDate"] = startDate.Format(utils.DateLayout)
	}
	if !endDate.IsZero() {
		w1["endDate"] = endDate.Format(utils.DateLayout)
		w2["endDate"] = endDate.Format(utils.DateLayout)
	}
	// 排序:1-分销单数升序 2-分销单数降序 3-分销金额升序 4-分销金额降序 5-分销佣金升序 6-分销佣金降序
	//OrderBy int `json:"order_by"`
	switch in.OrderBy {
	case 1:
		w1["orderBy"] = "trans_count asc"
	case 2:
		w1["orderBy"] = "trans_count desc"
	case 3:
		w1["orderBy"] = "trans_amount asc"
	case 4:
		w1["orderBy"] = "trans_amount desc"
	case 5:
		w1["orderBy"] = "commission asc"
	case 6:
		w1["orderBy"] = "commission desc"
	}

	// 第一步：获取分销金额、分销单数、分销佣金、消费客户数数据
	out, total, err = distribution_po.GetShopDistributorDataList(s.Engine, w1)
	if err != nil {
		log.Error(logPrefix, "获取数据失败err=", err.Error())
		err = errors.New("获取数据失败")
		return
	}

	disIds := make([]int, 0)
	for _, v := range out {
		disIds = append(disIds, v.DisId)
	}

	// 第二步：商品分销成交金额/商品分销成金单数和保险分销成交金额/保险分销成交单数
	w2["disIds"] = disIds
	out2, _, err := distribution_po.GetShopDistributorDataList(s.Engine, w2)
	if err != nil {
		log.Error(logPrefix, "获取数据失败err=", err.Error())
		err = errors.New("获取数据失败")
		return
	}
	type DataStru struct {
		// 商品分销成交金额
		GoodsTransAmount int `json:"goods_trans_amount"`
		// 商品分销成交单数
		GoodsTransCount int `json:"goods_trans_count"`
		// 保险分销成交金额
		InsTransAmount int `json:"ins_trans_amount"`
		// 保险分销成交单数
		InsTransCount int `json:"ins_trans_count"`
	}
	m := make(map[int]DataStru)
	for _, v := range out2 {
		val := m[v.DisId]
		if v.Type == 2 {
			val.GoodsTransAmount = v.TransAmount
			val.GoodsTransCount = v.TransCount
		}
		if v.Type == 4 {
			val.InsTransAmount = v.TransAmount
			val.InsTransCount = v.TransCount
		}
		m[v.DisId] = val

	}

	for k, v := range out {
		//计算消费客户数
		TransCustomerCount, InsTransCustomerCount := s.GetCustomerCount(map[string]interface{}{"statDateS": startDate.Format(utils.DateLayout), "statDateE": endDate.Format(utils.DateLayout), "isDis": 1, "disMemberId": v.DisMemberId, "shopId": v.ShopId, "disId": v.DisId})
		out[k].TransCustomerCount = TransCustomerCount + InsTransCustomerCount
		out[k].GoodsTransAmount = m[v.DisId].GoodsTransAmount
		out[k].GoodsTransCount = m[v.DisId].GoodsTransCount
		out[k].InsTransAmount = m[v.DisId].InsTransAmount
		out[k].InsTransCount = m[v.DisId].InsTransCount
	}

	return
}

func (s StatsShopService) GetCustomerCount(where map[string]interface{}) (TransCustomerCount, TransCustomerCount1 int) {
	//累计客户数统计
	var (
		OrderCustomerCount  int
		OrderCustomerCount1 int
	)
	statDateS, statDateE := "", ""
	start, ok := where["statDateS"]
	if ok {
		statDateS = start.(string)
	}
	end, ok := where["statDateE"]
	if ok {
		statDateE = end.(string)
	}
	session2 := s.Engine.Table("upetmart.upet_orders").Alias("o").Join("left", "upetmart.upet_order_goods b", "o.order_id=b.order_id").
		Select(`COUNT(DISTINCT o.buyer_id) AS order_customer_count,COUNT(DISTINCT CASE WHEN o.payment_time > 0 THEN o.buyer_id END) AS trans_customer_count`).
		Where("o.store_id = 3 and o.order_father =0")

	if statDateS != "" && statDateE != "" {
		startStamp := utils.Date2Timestamp(statDateS)
		endStamp := utils.AddDate2Timestamp(statDateE)
		session2 = session2.And("o.add_time between ? and ?", startStamp, endStamp)
	}
	isDis, ok := where["isDis"]
	if ok {
		session2 = session2.And("o.is_dis=?", isDis)
	}
	disMemberId, ok := where["disMemberId"]
	if ok {
		session2 = session2.And("b.dis_member_id=?", disMemberId)
	}

	shopId, shopIdOk := where["shopId"]
	if shopIdOk {
		session2 = session2.Where("b.shop_id=?", shopId)
	}
	_, err := session2.Get(&OrderCustomerCount, &TransCustomerCount)
	if err != nil {
		log.Error("统计商品消费客户数和保险消费客户数失败，错误为", err.Error())
		return
	}

	session3 := s.Engine.Table("insurance_business.pi_order_info").Alias("oi").
		Select(`COUNT(DISTINCT oi.user_id) AS order_customer_count,
             COUNT(DISTINCT CASE WHEN oi.pay_status =2 and oi.pay_time > 0 THEN oi.user_id END) AS trans_customer_count`)
	if where["statDateS"] != nil && where["statDateE"] != nil {
		session3 = session3.Where("DATE(create_time)  >= ? and DATE(create_time) <= ?", statDateS, statDateE)
	}
	_, ok = where["isDis"]
	if ok {
		session3 = session3.Where("oi.dis_id > 0")
	}
	disId, disIdOk := where["disId"]
	if disIdOk {
		session3 = session3.Where("oi.dis_id=?", disId)
	}

	if shopIdOk {
		session3 = session3.Where("oi.shop_id=?", shopId)
	}
	_, err = session3.Get(&OrderCustomerCount1, &TransCustomerCount1)
	if err != nil {
		return
	}
	return
}

// 用户端-分销员中心-数据中心-商品分析
func (s StatsShopService) StatsDisCenterGoods(in distribution_vo.StatsDisCenterGoodsReq) (out []distribution_vo.StatsDisCenterGoods, total int, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("用户端-分销员中心-数据中心-商品分析 入参:%s====", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	out = make([]distribution_vo.StatsDisCenterGoods, 0)
	var startDate, endDate time.Time
	if (len(in.StartDate) > 0 && len(in.EndDate) == 0) || (len(in.StartDate) == 0 && len(in.EndDate) > 0) {
		log.Error(logPrefix, "开始日期和结束日期要么都为空，要么都不为空")
		err = errors.New("开始日期和结束日期要么都为空，要么都不为空")
		return
	}
	if len(in.StartDate) > 0 {
		startDate, err = time.ParseInLocation(utils.DateLayout2, in.StartDate, time.Local)
		if err != nil {
			log.Error(logPrefix, "开始日期格式错误err=", err.Error())
			err = errors.New("开始日期格式错误")
			return
		}
	}
	if len(in.EndDate) > 0 {
		endDate, err = time.ParseInLocation(utils.DateLayout2, in.EndDate, time.Local)
		if err != nil {
			log.Error(logPrefix, "结束日期格式错误err=", err.Error())
			err = errors.New("结束日期格式错误")
			return
		}
	}
	if in.PageIndex == 0 {
		in.PageIndex = 1
	}
	if in.PageSize == 0 {
		in.PageSize = 10
	}

	// 获取分销员信息
	disInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{"memberId": in.MemberId, "orgId": in.OrgId}, "id,dis_role,shop_id,member_id")
	if err != nil {
		log.Error(logPrefix, "获取分销员信息失败err=", err.Error())
		return
	}
	if disInfo.Id == 0 || disInfo.ShopId == 0 {
		log.Error(logPrefix, "分销员信息不存在")
		err = errors.New("分销员信息不存在")
		return
	}

	w := map[string]interface{}{"pageIndex": in.PageIndex, "pageSize": in.PageSize, "groupBy": "goods_id", "isPayed": 1, "isDis": 1, "orgId": in.OrgId}
	if len(in.GoodsName) > 0 {
		w["goodsName"] = in.GoodsName
	}
	if !startDate.IsZero() {
		w["addTimeStart"] = startDate.Unix()
	}
	if !endDate.IsZero() {
		endDatestr := endDate.Format(utils.DateLayout) + " 23:59:59"
		endDate2, _ := time.ParseInLocation(utils.DateTimeLayout, endDatestr, time.Local)
		w["addTimeEnd"] = endDate2.Unix()
	}
	if in.Flag == 1 && disInfo.DisRole != disdistributor.DisRoleBoss {
		log.Error(logPrefix, "参数有误，查看老板数据时，但是分销员自己不是老板")
		err = errors.New("参数有误，查看老板数据时，但是分销员自己不是老板")
		return
	}
	if in.Flag == 1 {
		w["shopId"] = disInfo.ShopId
	} else {
		w["disMemberId"] = disInfo.MemberId
	}

	// 排序:1-分销件数升序 2-分销件数降序 3-分销金额升序 4-分销金额降序 5-分销佣金升序 6-分销佣金降序
	switch in.OrderBy {
	case 1:
		w["orderBy"] = "trans_count asc"
	case 2:
		w["orderBy"] = "trans_count desc"
	case 3:
		w["orderBy"] = "trans_amount asc"
	case 4:
		w["orderBy"] = "trans_amount desc"
	case 5:
		w["orderBy"] = "commission asc"
	case 6:
		w["orderBy"] = "commission desc"

	}
	log.Info(logPrefix, "查询数据条件为：", utils.InterfaceToJSON(w))
	data, total, err := distribution_po.StatsOrderGoods(s.Engine, w)
	if err != nil {
		log.Error(logPrefix, "查询数据出现错误：", err.Error())
		err = errors.New("查询数据出现错误")

	}
	for _, v := range data {
		GoodsImage := v.GoodsImage
		if len(v.GoodsImage) > 0 && !strings.HasPrefix(v.GoodsImage, "http://") && !strings.HasPrefix(v.GoodsImage, "https://") {
			GoodsImage = config.GetString("bbc_img_path") + v.GoodsImage
		}

		d := distribution_vo.StatsDisCenterGoods{
			// 商品Id
			GoodsId: v.GoodsId,
			// 商品名称
			GoodsName: v.GoodsName,
			// 商品图片
			GoodsImage: GoodsImage,
			// 分销成交件数
			TransCount: v.TransCount,
			// 分销成交金额(单位分)
			TransAmount: cast.ToInt(v.TransAmount),
			// 分销佣金(单位分)
			Commission: cast.ToInt(v.Commission),
		}
		out = append(out, d)
	}
	return
}

// 跑历史数据每周的统计记录
func (s StatsShopService) StatsShopDistributorDailyRun(startTime, endTime string) {
	startDay, err := time.Parse(utils.DateLayout, startTime)
	if err != nil {
		return
	}
	endDay, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return
	}
	for i := startDay; i.Before(endDay) || i.Equal(endDay); i = i.AddDate(0, 0, 1) {
		// 跑一天的数据
		fmt.Println("处理日期范围-分销店铺数据-日:", i.Format("2006-01-02"), "-", i.Format("2006-01-02"))
		s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
			StartDate: i.Format(time.DateOnly),
			EndDate:   i.Format(time.DateOnly),
		})

		// 检测是不是周末，跑一周的数据
		weekday := i.Weekday()
		if weekday == time.Sunday {
			startDate := i.AddDate(0, 0, -6)
			fmt.Println("处理日期范围-分销店铺数据-周:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是月末，跑一月的数据
		tomorrow := i.AddDate(0, 0, 1)
		if tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(0, -1, 0)
			fmt.Println("处理日期范围-分销店铺数据-月:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是年末，跑一年的数据
		if tomorrow.Month() == 1 && tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(-1, 0, 0)
			s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}
	}
}

func (s StatsShopService) StatsShopDistributorDailyDefault() {
	logPrefix := "写入店铺分销员的佣金、待结佣金和已结佣金数据-daily===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			// 记录 panic 信息
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsShopDistributorDailyDataLock, "daily")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := now.AddDate(0, 0, -1)
	s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   startDate.Format("2006-01-02"),
	})
}

func (s StatsShopService) StatsShopDistributorWeeklyDefault() {
	logPrefix := "写入店铺分销员的佣金、待结佣金和已结佣金数据-weekly===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			// 记录 panic 信息
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsShopDistributorDailyDataLock, "weekly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	tmp := now.Weekday()
	fmt.Println(tmp)
	startDate := now.AddDate(0, 0, -int(now.Weekday())-6)
	endDate := startDate.AddDate(0, 0, 6)
	s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s StatsShopService) StatsShopDistributorMonthlyDefault() {
	logPrefix := "写入店铺分销员的佣金、待结佣金和已结佣金数据-monthly===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			// 记录 panic 信息
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsShopDistributorDailyDataLock, "monthly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, -1) // 加一个月，再减去一天
	s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s StatsShopService) StatsShopDistributorYearlyDefault() {
	logPrefix := "写入店铺分销员的佣金、待结佣金和已结佣金数据-yearly===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			// 记录 panic 信息
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsShopDistributorDailyDataLock, "yearly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := time.Date(now.Year()-1, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := time.Date(now.Year()-1, 12, 31, 0, 0, 0, 0, time.Local)
	s.StatsShopDistributorDaily(distribution_vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s StatsShopService) StatsShopDistributorDaily(req ...distribution_vo.StatsShopDistributorDailyReq) {
	logPrefix := fmt.Sprintf("分销店铺数据统计写入，入参：%s=====", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	var StartDate, EndDate string
	if len(req) > 0 {
		StartDate = req[0].StartDate
		EndDate = req[0].EndDate
	}

	//获取商品分销订单的佣金、待结佣金和已结佣金字段
	data1, err := DealGoodsCommission(s.Engine, map[string]interface{}{"statDate": StartDate, "endDate": EndDate})
	if err != nil {
		log.Error(logPrefix, "获取商品分销订单的佣金、待结佣金和已结佣金字段err=", err.Error())
		return
	}

	//获取保险分销订单的佣金、待结佣金和已结佣金字段
	data2, err := DealInsCommission(s.Engine, map[string]interface{}{"statDate": StartDate, "endDate": EndDate})
	if err != nil {
		log.Error(logPrefix, "获取保险分销订单的佣金、待结佣金和已结佣金字段err=", err.Error())
		return
	}
	// 将data2里的数据合并到data1
	for k, v := range data2 {
		data1[k] = v
	}

	for _, v := range data1 {
		// 判断该日期数据是否存在于stats_shop_distributor_daily表中， 存在2024/07/19 02:40:41 0 0则更新，不存在则插入
		l := fmt.Sprintf("%d_%d_%d_%s_%s", v.ShopId, v.DisId, v.Type, v.StatDate, v.EndDate)
		info := distribution_po.StatsShopDistributorDaily{}
		exist, err := s.Session.Table("eshop.stats_shop_distributor_daily").Where("stat_date=? and end_date=?", v.StatDate, v.EndDate).
			Where("shop_id=?", v.ShopId).Where("dis_id=?", v.DisId).Where("type=?", v.Type).Where("is_dis=1").Get(&info)
		if err != nil {
			log.Error(logPrefix, l, "获取分销店铺数据统计表失败err=", err.Error())
			return
		}
	EXIST:
		if exist {
			cols := "commission,settled_commission"
			if affects, err := s.Session.Table("eshop.stats_shop_distributor_daily").Where("id=?", info.Id).Cols(cols).Update(v); err != nil {
				log.Error(logPrefix, l, "更新分销店铺数据统计表失败err=", err.Error())
				return
			} else {
				log.Info(logPrefix, l, "更新分销店铺数据统计表成功，日期=", v.StatDate, ",影响条数=", affects)
			}
		} else {

			if affects, err := s.Session.Table("eshop.stats_shop_distributor_daily").Insert(v); err != nil {
				log.Error(logPrefix, l, "写入分销店铺数据统计表失败err=", err.Error())
				exist = true
				goto EXIST
			} else {
				log.Info(logPrefix, l, "写入分销店铺数据统计表成功，日期=", v.StatDate, ",影响条数=", affects)
			}
		}
	}
}

func (s StatsShopService) StatsShopPageList(req distribution_vo.StatsShopPageReq) ([]distribution_vo.StatsShopPageData, int, error) {
	dataMap := make(map[int]distribution_vo.StatsShopPageData)
	dataDtoList := make([]distribution_vo.StatsShopPageDto, 0)
	data := make([]distribution_vo.StatsShopPageData, 0)

	// 根据时间和筛选条件，进行分页查询，按照shopId归类，获取到map[shopId][]id
	var wg sync.WaitGroup
	s.Begin()
	defer s.Close()
	session := s.Session
	session.Select("shop_id, GROUP_CONCAT(DISTINCT(ssdd.id)) AS stat_ids").Table("stats_shop_distributor_daily ssdd").
		Join("LEFT", "scrm_enterprise_salesperson_bind sesb", "sesb.enterprise_id=ssdd.enterprise_id").
		Join("LEFT", "scrm_salesperson ss", "ss.id=sesb.salesperson_id").
		Where("ssdd.is_dis=1 AND ssdd.enterprise_id>0")

	if len(req.Name) > 0 {
		session.And("ssdd.enterprise_name like ? OR ssdd.shop_name like ? ", "%"+req.Name+"%", "%"+req.Name+"%")
	}
	if len(req.SalesmanName) > 0 {
		session.And("ss.name like ?", "%"+req.SalesmanName+"%")
	}
	if len(req.StartDate) > 0 {
		session.And("ssdd.stat_date = ?", req.StartDate)
	}
	if len(req.EndDate) > 0 {
		session.And("ssdd.end_date = ?", req.EndDate)
	}

	total, err := session.GroupBy("ssdd.shop_id").Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&dataDtoList)
	if err != nil {
		log.Errorf("查询分销店铺数据统计失败：e=%v", err)
		return nil, 0, err
	}
	shopIdStatIdsMap := make(map[int]string)
	for _, daily := range dataDtoList {
		shopIdStatIdsMap[daily.ShopId] = daily.StatIds // 假设我们想要以Name为key，Age为value
	}

	// 开启一组协程完成以下遍历操作
	for shopId, statIds := range shopIdStatIdsMap {
		wg.Add(1)
		go func(shopId int, statIds string) {
			defer wg.Done()
			dataMap[shopId] = s.packStatShopData(shopId, statIds, req, dataMap[shopId])
		}(shopId, statIds)
	}
	wg.Wait()

	// 按顺序组装返回
	for _, daily := range dataDtoList {
		data = append(data, dataMap[daily.ShopId])
	}

	return data, cast.ToInt(total), nil
}

func (s StatsShopService) packStatShopData(shopId int, statIds string, req distribution_vo.StatsShopPageReq, pageData distribution_vo.StatsShopPageData) distribution_vo.StatsShopPageData {
	var subWg sync.WaitGroup
	subWg.Add(4)

	// 查询商品分销+保险分销统计数据
	go func() {
		defer subWg.Done()
		s.GetStatsSimple(statIds, &pageData)
	}()

	// 查询商品分销+保险分销统计数据
	go func() {
		defer subWg.Done()
		s.GetStatDistribute(shopId, statIds, "2,4", req, &pageData)
	}()

	// 查询商品分销统计数据
	go func() {
		defer subWg.Done()
		s.GetStatDistribute(shopId, statIds, "2", req, &pageData)
	}()

	// 查询保险分销统计数据
	go func() {
		defer subWg.Done()
		s.GetStatDistribute(shopId, statIds, "4", req, &pageData)
	}()

	subWg.Wait()
	return pageData
}

func (s StatsShopService) GetStatDistribute(shopId int, statIds string, typeStr string, req distribution_vo.StatsShopPageReq, pageData *distribution_vo.StatsShopPageData) {
	s.Begin()
	defer s.Close()
	session := s.Session

	statDistribute := distribution_vo.StatDistribute{}
	_, err := session.Select("SUM(trans_customer_count) AS trans_customer_count,"+
		"SUM(trans_count) AS trans_count,"+
		"SUM(trans_amount) AS trans_amount,"+
		"SUM(trans_product_count) AS trans_product_count,"+
		"SUM(poster_trans_count) AS poster_trans_count,"+
		"SUM(poster_trans_amount) AS poster_trans_amount,"+
		"SUM(link_trans_count) AS link_trans_count,"+
		"SUM(link_trans_amount) AS link_trans_amount,"+
		"SUM(fan_relation_trans_count) AS fan_relation_trans_count,"+
		"SUM(fan_relation_trans_amount) AS fan_relation_trans_amount,"+
		"SUM(commission) AS commission,"+
		"SUM(settled_commission) AS settled_commission,"+
		"SUM(unsettled_commission) AS unsettled_commission").
		Table("stats_shop_distributor_daily").
		In("id", strings.Split(statIds, ",")).In("type", strings.Split(typeStr, ",")).Get(&statDistribute)
	if err != nil {
		log.Errorf("GetStatDistribute，查询失败：%v", err)
		return
	}

	if statDistribute.TransCustomerCount > 0 {
		statDistribute.TransCustomerPrice = statDistribute.TransAmount / statDistribute.TransCustomerCount
	}
	if typeStr == "2" {
		pageData.ProductDistribute = statDistribute
	} else if typeStr == "4" {
		pageData.InsureDistribute = statDistribute
	} else if typeStr == "2,4" {
		// 查询税前提现金额
		preTaxAmount, err := s.GetPreTaxAmount(shopId, req)
		if err != nil {
			log.Errorf("GetPreTaxAmount，查询失败：%v", err)
		}

		var total = distribution_vo.TotalStatDistribute{
			StatDistribute: statDistribute,
			PreTaxAmount:   preTaxAmount,
		}
		pageData.TotalDistribute = total
	}
}

func (s StatsShopService) GetStatsSimple(statIds string, pageData *distribution_vo.StatsShopPageData) {
	s.Begin()
	defer s.Close()
	session := s.Session

	simple := distribution_vo.StatsShopSimple{}
	_, err := session.Select("enterprise_id,enterprise_name,shop_name,'润合云店' AS upet_shop_name,COUNT(DISTINCT(IF(trans_count>0,dis_id,NULL))) AS trans_dis_count").
		Table("stats_shop_distributor_daily").
		In("id", strings.Split(statIds, ",")).
		GroupBy("shop_id").
		Get(&simple)
	if err != nil {
		log.Errorf("GetStatsSimple-查询店铺统计数据发生错误：err=%s", err.Error())
	}

	// 查询业务员
	enterpriseId := simple.EnterpriseId
	var salesmanName string
	var disCount int
	_, err = session.Select("GROUP_CONCAT(ss.name),COUNT(DISTINCT(dd.id))").
		Table("scrm_enterprise_salesperson_bind sesb").
		Join("LEFT", "scrm_salesperson ss", "sesb.salesperson_id=ss.id").
		Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
		Join("LEFT", "dis_distributor dd", "dd.shop_id=s.id").
		Where("sesb.enterprise_id=?", enterpriseId).GroupBy("sesb.enterprise_id").Get(&salesmanName, &disCount)
	if err != nil {
		log.Errorf("GetStatsSimple-查询业务员名称发生错误：err=%s", err.Error())
	}
	simple.SalesmanName = salesmanName
	simple.DisCount = disCount

	pageData.Simple = simple
}

func (s StatsShopService) GetPreTaxAmount(shopId int, req distribution_vo.StatsShopPageReq) (int, error) {
	s.Begin()
	defer s.Close()
	session := s.Session

	var preTaxAmount int64
	session.Select("SUM(pre_tax_amount)").
		Table("dis_withdraw").
		Where("shop_id=? AND status=2", shopId)
	if len(req.StartDate) > 0 {
		session.And("pay_time >= ?", req.StartDate)
	}
	if len(req.EndDate) > 0 {
		realEndDate := req.EndDate + " 23:59:59"
		session.And("pay_time <= ?", realEndDate)
	}

	_, err := session.Get(&preTaxAmount)
	if err != nil {
		log.Errorf("GetStatsSimple-发生错误：err=%s", err.Error())
		return 0, err
	}
	return cast.ToInt(preTaxAmount), nil
}
