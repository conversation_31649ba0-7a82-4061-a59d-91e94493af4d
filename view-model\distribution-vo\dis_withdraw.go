package distribution_vo

import viewmodel "eShop/view-model"

type DisWithdrawView struct {
	Id                 int    `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`                         //提现自增ID
	OrgId              int    `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`                        //所属主体id
	ShopId             int    `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'"`                      //所属店铺id
	WithdrawNo         string `json:"withdraw_no" xorm:"default '' comment('提现编号') VARCHAR(50) 'withdraw_no'"`       //提现编号
	Status             int    `json:"status" xorm:"default 0 comment('提现状态：0-默认, 1-待审核，2-已打款，3-审核驳回') INT 'status'"` //提现状态：0-默认, 1-未打款，2-已打款，3-已拒绝
	DistributorId      int    `json:"distributor_id" xorm:"default 0 comment('分销员id') INT 'distributor_id'"`         //分销员id
	DisRole            int    `json:"dis_role"`                                                                      //提现人企业角色 0-初始值 1-老板 2-店员
	PreTaxAmount       int    `json:"pre_tax_amount" xorm:"default 0 comment('税前提现金额(分)') INT 'pre_tax_amount'"`     //税前提现金额(分)
	AfterTaxAmount     int    `json:"after_tax_amount" xorm:"default 0 comment('税后提现金额(分)') INT 'after_tax_amount'"` //税后提现金额(分)
	BankName           string `json:"bank_name" xorm:"default '' comment('收款银行') VARCHAR(255) 'bank_name'"`          //收款银行
	BankAccount        string `json:"bank_account" xorm:"default '' comment('收款账号') VARCHAR(255) 'bank_account'"`    //收款账号
	EncryptBankAccount string `json:"encrypt_bank_account" xorm:"default '' comment('收款账号') VARCHAR(255) 'encrypt_bank_account'"`
	AccountName        string `json:"account_name" xorm:"default '' comment('开户姓名') VARCHAR(50) 'account_name'"`                   //开户姓名
	IdCard             string `json:"id_card"`                                                                                     //身份证号码
	EncryptIdCard      string `json:"encrypt_id_card"`                                                                             //身份证号码加密
	BankBranch         string `json:"bank_branch" xorm:"default '' comment('收款支行') VARCHAR(255) 'bank_branch'"`                    //收款支行
	PayTime            string `json:"pay_time" xorm:"default 'null' comment('打款时间') DATETIME 'pay_time'"`                          //打款时间
	RejectReason       string `json:"reject_reason" xorm:"not null default '' comment('拒绝原因') VARCHAR(100)"`                       //拒绝原因
	CreateTime         string `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`       //申请时间
	UpdateTime         string `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`       //更新时间
	DisRoleText        string `json:"dis_role_text"`                                                                               //提现人角色描述
	StatusText         string `json:"status_text"`                                                                                 //提现状态描述
	DistributorName    string `json:"distributor_name"`                                                                            //分销员名称
	Tax                int    `json:"tax"`                                                                                         //代缴个税（单位分）
	ShopName           string `json:"shop_name"`                                                                                   //所属店铺名称
	EnterpriseName     string `json:"enterprise_name"`                                                                             //线下企业名称
	DisEnterpriseName  string `json:"dis_enterprise_name"`                                                                         //线下企业名称
	EnterpriseId       string `json:"enterprise_id"`                                                                               //线下企业id
	BankMobile         string `json:"bank_mobile" xorm:"default '' comment('收款人手机号加*') VARCHAR(50) 'bank_mobile'"`                 //收款人手机号加
	BankEncryptMobile  string `json:"bank_encrypt_mobile" xorm:"default '' comment('收款人加密手机号') VARCHAR(20) 'bank_encrypt_mobile'"` //收款人加密手机号
	Mobile             string `json:"mobile" xorm:"default '' comment('分销员手机号加*') VARCHAR(50) 'mobile'"`                           //分销员手机号加
	EncryptMobile      string `json:"encrypt_mobile" xorm:"default '' comment('分销员加密手机号') VARCHAR(20) 'encrypt_mobile'"`           //分销员加密手机号
	OrderSn            string `json:"order_sn" xorm:"default '' comment('分销订单') VARCHAR(50) 'order_sn'"`                           //分销订单
	GoodsName          string `json:"goods_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'goods_name'"`                      //商品名称
	OrgTypeName        string `json:"org_type_name" xorm:"default '' comment('码包所属企业') VARCHAR(255) 'org_type_name'"`              //码包所属企业
	OrderCommission    int    `json:"order_commission" xorm:"default 0 comment('订单佣金') INT 'order_commission'"`                    //订单佣金
	SwlmCpsId          int    `json:"swlm_cps_id"`                                                                                 //扣减佣金记录id(eshop.dis_distributor_swlm_cps.id)
}

type GetDisWithdrawListReq struct {
	viewmodel.BasePageHttpRequest
	OrgId  int `json:"org_id"`               //所属主体id
	Status int `json:"status" form:"status"` //提现状态：0-默认, 1-待审核，2-已打款，3-审核驳回
	//查询条件的类型（enterprise_name=线下企业，distributor_name=分销员名称，distributor_mobile=分销员手机号,withdraw_no=提现编号,distributor_id=分销员id）
	WhereType2 string `json:"where_type2"`
	//查询条件值
	Where2 string `json:"where2"`
	//查询条件的类型（create_time=申请时间，pay_time=支付时间）
	WhereType  string `json:"where_type"`
	WhereStart string `json:"where_start"` //查询条件值-开始时间
	WhereEnd   string `json:"where_end"`   //查询条件值-结束时间
	ExportType int    `json:"export_type"` //0-导出提现数据，1-导出提现订单数据（含商品明细）

}

type GetSimpleWithListReq struct {
	viewmodel.BasePageHttpRequest
	OrgId         int `json:"org_id"`         //所属主体id
	ShopId        int `json:"shop_id"`        //所属店铺id
	DistributorId int `json:"distributor_id"` //分销员id
}

type GetDisWithdrawListRes struct {
	viewmodel.BasePageHttpResponse
	Data []DisWithdrawView `json:"data"`
}

type GetDisWithdrawDetailReq struct {
	OrgId         int `json:"org_id"`          //所属主体id
	DisWithdrawId int `json:"dis_withdraw_id"` //提现自增ID
}
type GetDisWithdrawDetailRes struct {
	viewmodel.BaseHttpResponse
	Data DisWithdrawView `json:"data"`
}

type DisWithdrawCheckReq struct {
	DisWithdrawId int    `json:"dis_withdraw_id"  validate:"required"` //提现自增ID
	CheckType     int    `json:"check_type"  validate:"required"`      //提现审核类型：1-付款，2-拒绝
	RejectReason  string `json:"reject_reason"`                        //拒绝原因
	OrgId         int    `json:"org_id"`                               //主体id
}

type DisWithdrawBatchPayReq struct {
}
type DisWithdrawBatchPayRes struct {
}
