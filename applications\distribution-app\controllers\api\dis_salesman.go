package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"net/http"
)

// @Summary 业务员列表接口,业务员中心下拉框
// @Description
// @Tags 小程序接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body vo.DisSalesmanListReq true " "
// @Success 200 {object} vo.DisSalesmanListRes
// @Failure 400 {object} vo.DisSalesmanListRes
// @Router /api/salesman/list [POST]
func SaleManList(writer http.ResponseWriter, request *http.Request) {
	//这里只对request的参数做检查,不涉及任何业务逻辑。
	//所有逻辑放在服务层

	resp := vo.DisSalesmanListRes{}
	resp.Code = 400

	server := services.DisSalesmanService{}
	req, err := utils.Bind[vo.DisSalesmanListReq](request)
	if req.OrgId == 0 {
		req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	}
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	if err != nil {
		log.Error("获取业务员列表，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取业务员列表，参数解析失败：" + err.Error())
	} else {
		req.WhereType = "mobile"
		req.Where = jwtInfo.Mobile
		req.PageIndex = 1
		req.PageSize = 100
		data, total, err := server.GetList(req)
		if err != nil {
			log.Error("获取业务员列表失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取业务员列表异常：" + err.Error())
		} else {
			resp.Code = 200
			resp.Total = total
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员详情接口
// @Description
// @Tags 小程序接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body vo.DisSalesmanStopReq true " "
// @Success 200 {object} vo.DisSalesmanRes
// @Failure 400 {object} vo.DisSalesmanRes
// @Router /api/salesman/get [POST]
func SaleManGet(writer http.ResponseWriter, request *http.Request) {
	//这里只对request的参数做检查,不涉及任何业务逻辑。
	//所有逻辑放在服务层

	resp := vo.DisSalesmanRes{}
	resp.Code = 400

	server := services.DisSalesmanService{}
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req, err := utils.Bind[vo.DisSalesmanStopReq](request)
	if err != nil {
		log.Error("获取业务员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取业务员列表，参数解析失败：" + err.Error())
	} else {
		vErr := validate.Validate(req, "upd")
		if vErr != nil {
			resp.Message = fmt.Sprintf("获取业务员参数校验异常：%s", vErr)
			bytes, _ := json.Marshal(resp)
			writer.Write(bytes)
			return
		}
		req.Mobile = jwtInfo.Mobile
		data, err := server.Get(req)
		if err != nil {
			log.Error("获取业务员失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取业务员异常：" + err.Error())
		} else {
			resp.Code = 200
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员中心销售业绩企业，用后台线下企业管理的接口
// @Description
// @Tags 小程序接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body vo.DisSalesmanStopReq true " "
// @Success 200 {object} vo.SalesmanCenterRes
// @Failure 400 {object} vo.SalesmanCenterRes
// @Router /api/salesman/center-enterprise [POST]
func SaleManCenterEnterprise(writer http.ResponseWriter, request *http.Request) {
	resp := vo.SalesmanCenterRes{}
	resp.Code = 400

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员中销售业绩企业详细
// @Description
// @Tags 小程序接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body vo.SalesAchievementReq true " "
// @Success 200 {object} vo.SalesAchievementRes
// @Failure 400 {object} vo.SalesAchievementRes
// @Router /api/salesman/center-details [POST]
func SaleManCenterDetails(writer http.ResponseWriter, request *http.Request) {

	resp := vo.SalesAchievementRes{}
	resp.Code = 400
	server := services.DistributorManageService{}

	req, err := utils.Bind[vo.SalesAchievementReq](request)
	if err != nil {
		log.Error("获取店铺分销员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取店铺分销员，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(request.Header.Get("org_id"))
		}
		data, err := server.SaleManCenterDetails(req)
		if err != nil {
			log.Error("获取店铺分销员：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取店铺分销员异常：" + err.Error())
		} else {
			resp.Code = 200
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// SalesShopDisPage
// @Summary 业务员中心-企业数据
// @Tags  小程序接口-业务员
// @Accept  plain
// @Produce  json
// @Param DisShopListReq query vo.ShopDisPageReq true " "
// @Success 200 {object} vo.SalesShopPageResp
// @Failure 400 {object} vo.SalesShopPageResp
// @Router /api/salesman/shop-dis-page [GET]
func SalesShopDisPage(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := vo.SalesShopPageResp{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	param, err := utils.Bind[vo.ShopDisPageReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("获取业务员中心-企业数据-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	service := services.DistributorManageService{}
	if out.Data, out.Total, err = service.SalesShopDisPage(param); err != nil {
		log.Errorf("获取业务员中心-企业数据失败-错误为%s", err.Error())
		out.Message = "获取业务员中心-企业数据失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
