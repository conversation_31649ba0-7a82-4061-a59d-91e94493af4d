package dispatch_po

import (
	"time"
)

type Warehouse struct {
	Id         int32     `xorm:"not null pk autoincr comment('自增，仓库id') INT(11)"`
	Thirdid    string    `xorm:"not null default '''' comment('第三方仓库ID 例如a8id,管易ID') VARCHAR(50)"`
	Code       string    `xorm:"not null default '''' comment('仓库编号') index index(idx_code_category) VARCHAR(32)"`
	Name       string    `xorm:"not null default '''' comment('仓库名称') VARCHAR(32)"`
	Comefrom   int32     `xorm:"not null default 1 comment('仓库归属(1-A8,2-管易 3-门店（美团）)') index INT(11)"`
	Level      int32     `xorm:"not null default 1 comment('仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)') INT(11)"`
	Category   int32     `xorm:"not null default 1 comment('仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓，5-前置仓虚拟仓)') index index(idx_code_category) INT(11)"`
	Address    string    `xorm:"not null default '''' comment('仓库地址') VARCHAR(255)"`
	Contacts   string    `xorm:"not null default '''' comment('仓库联系人') VARCHAR(50)"`
	Tel        string    `xorm:"not null default '''' comment('仓库联系方式') VARCHAR(20)"`
	Status     int32     `xorm:"not null default 0 comment('仓库状态（0-禁用，1-启用）') index INT(11)"`
	Createdate time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
	Lastdate   time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') DATETIME"`
	Subsystem  int32     `xorm:"default 0 comment('所属系统 0:默认,1:ERP,2:子龙') INT(11)"`
	Ratio      int32     `xorm:"default 100 comment('仓库比例') INT(11)"`
	Lng        int32     `xorm:"default 0 comment('仓库经度') INT(11)"`
	Lat        int32     `xorm:"default 0 comment('仓库纬度') INT(11)"`
	Region     string    `xorm:"default '''' comment('所属区域') VARCHAR(32)"`
	City       string    `xorm:"default '''' comment('所属城市') VARCHAR(32)"`
	ChainId    int32     `xorm:"default 0 comment('连锁id') INT(11)"`
	OrgId      int32     `xorm:"default 0 comment('主体id') INT(11)"`
}

func (w Warehouse) TableName() string {
	return "dc_dispatch.warehouse"
}
