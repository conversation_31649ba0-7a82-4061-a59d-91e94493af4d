package response

import (
	"encoding/json"
	"net/http"
)

// 基础无业务数据返回
type BaseResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Response 通用响应结构
type Response[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data"`
}

// PageResponse 分页响应结构
type PageResponse[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data"`
	Total   int    `json:"total"`
}

// JSON 写入JSON响应
func JSON[T any](w http.ResponseWriter, code int, message string, data T) {
	resp := Response[T]{
		Code:    code,
		Message: message,
		Data:    data,
	}
	w.<PERSON>er().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(resp)
}

// JSONPage 写入分页JSON响应
func JSONPage[T any](w http.ResponseWriter, code int, message string, data T, total int) {
	resp := PageResponse[T]{
		Code:    code,
		Message: message,
		Data:    data,
		Total:   total,
	}
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(resp)
}

// Success 写入成功响应
func Success(w http.ResponseWriter) {
	var r BaseResp
	r.Code = 200
	r.Message = "success"

	JSON[BaseResp](w, http.StatusOK, "success", r)
}

// SuccessWithData 写入成功响应
func SuccessWithData[T any](w http.ResponseWriter, data T) {
	JSON(w, http.StatusOK, "success", data)
}

// SuccessWithPage 写入成功响应
func SuccessWithPage[T any](w http.ResponseWriter, data T, total int) {
	JSONPage(w, http.StatusOK, "success", data, total)
}

// Error 写入错误响应
func error(w http.ResponseWriter, code int, message string) {
	resp := BaseResp{
		Code:    code,
		Message: message,
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(resp)
}

// BadRequest 写入400错误响应
func BadRequest(w http.ResponseWriter, message string) {
	error(w, http.StatusBadRequest, message)
}

// NotFound 写入404错误响应
func NotFound(w http.ResponseWriter, message string) {
	error(w, http.StatusNotFound, message)
}

// InternalError 写入500错误响应
func InternalError(w http.ResponseWriter, message string) {
	error(w, http.StatusInternalServerError, message)
}
