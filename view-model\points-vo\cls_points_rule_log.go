package points_vo

import (
	po "eShop/domain/points-po"
)

type ClsPointsRuleLogSaveVO struct {
	SuperSaveVO[po.ClsPointsRuleLog]
	GoodsCode   string `json:"goods_code" validate:"required"`
	Type        int    `json:"type" validate:"required"`
	BizType     int    `json:"biz_type" validate:"required"`
	Description string `json:"description" validate:"required"`
	Operator    string `json:"operator"`
}

type ClsPointsRuleLogUpdateVO struct {
	SuperUpdateVO[po.ClsPointsRuleLog]
	Id          int    `json:"id" validate:"required"`
	GoodsCode   string `json:"goods_code" validate:"required"`
	Type        int    `json:"type" validate:"required"`
	BizType     int    `json:"biz_type" validate:"required"`
	Description string `json:"description" validate:"required"`
	Operator    string `json:"operator"`
}

type ClsPointsRuleLogQueryVO struct {
	SuperQueryVO[po.ClsPointsRuleLog]
	GoodsCode string `json:"goods_code" query:"eq"`
	Type      int    `json:"type" query:"eq"`
	BizType   int    `json:"biz_type" query:"eq"`
}

type ClsPointsRuleLogResultVO struct {
	SuperResultVO[ClsPointsRuleLogResultVO]
	Id          int    `json:"id"`
	GoodsCode   string `json:"goods_code"`
	Type        int    `json:"type"`
	BizType     int    `json:"biz_type"`
	Description string `json:"description"`
	Operator    string `json:"operator"`
	CreatedAt   string `json:"created_at"`
}
