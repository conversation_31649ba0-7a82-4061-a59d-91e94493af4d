package utils

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
)

func GenerateBarCode() string {
	// 初始化随机数种子
	// 使用当前时间的纳秒数作为种子
	// 由于纳秒数在每次调用时都不同,这确保了每次生成的随机序列都不同
	// 这大大降低了生成重复数字的概率
	// 如果不设置种子,默认种子是1,每次运行程序生成的随机序列都相同
	rand.Seed(time.Now().UnixNano())

	// 生成10位随机数
	// 每位数字有10种可能(0-9)
	// 总共有10^10种可能的组合
	// 理论上生成相同数字的概率是1/10^10 = 1/10000000000 = 0.0000000001 = 0.00000001%
	// 加上纳秒级的随机种子,实际重复的概率会更低
	var digits [10]int
	for i := range digits {
		digits[i] = rand.Intn(10)
	}

	// 计算校验位
	var sum int
	for i, digit := range digits {
		if i%2 == 0 {
			sum += digit
		} else {
			sum += digit * 3
		}
	}
	checkDigit := (10 - sum%10) % 10

	// 拼接生成条码
	var barcode strings.Builder
	barcode.WriteString("69")
	for _, digit := range digits {
		barcode.WriteString(fmt.Sprintf("%d", digit))
	}
	barcode.WriteString(fmt.Sprintf("%d", checkDigit))

	return barcode.String()
}
