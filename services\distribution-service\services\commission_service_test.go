package services

import (
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/services/distribution-service/enum"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestDiscommissionService_DisCommissionApply(t *testing.T) {
	type args struct {
		in vo.DisCommissionApplyReq
	}
	tests := []struct {
		name    string
		h       *DiscommissionService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "提现申请",
			h:    &DiscommissionService{},
			args: args{
				in: vo.DisCommissionApplyReq{
					OrgId:          3,
					Amount:         1106,
					MemberId:       138718,
					BankName:       "",
					BankAccount:    "",
					BankBranch:     "",
					WithdrawIdCard: "362502199001036625",
					AccountName:    "",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache.CacheSources[cache_source.EShop] = "redis://qlwhIPO82@#KDFQAwe@**********:6979?ssl=true&db=0" //config.Get("redis.Addr")
			err := tt.h.DisCommissionApply(tt.args.in)
			t.Errorf(" error = %v", err)

		})
	}
}

func TestDiscommissionService_GetDisCommissionInfo(t *testing.T) {
	type args struct {
		in vo.GetDisCommissionInfoReq
	}
	tests := []struct {
		name      string
		h         *DiscommissionService
		args      args
		wantOut   vo.GetDisCommissionList
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			h:    &DiscommissionService{},
			args: args{
				in: vo.GetDisCommissionInfoReq{
					OrgId:               3,
					MemberId:            ********,
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{PageIndex: 1, PageSize: 10},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.h.GetDisCommissionInfo(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DiscommissionService.GetDisCommissionInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DiscommissionService.GetDisCommissionInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("DiscommissionService.GetDisCommissionInfo() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestDiscommissionService_GetShopCommList(t *testing.T) {
	type args struct {
		in vo.GetShopCommListReq
	}
	tests := []struct {
		name    string
		h       *DiscommissionService
		args    args
		wantOut []vo.GetShopCommListRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			h:    &DiscommissionService{},
			args: args{
				in: vo.GetShopCommListReq{
					OrgId:  3,
					ShopId: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.h.GetShopCommList(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("DiscommissionService.GetShopCommList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("DiscommissionService.GetShopCommList() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestDiscommissionService_WithdrawCommUpdate(t *testing.T) {
	type args struct {
		needUpdateMap map[string]string
	}
	tests := []struct {
		name string
		h    *DiscommissionService
		args args
	}{
		// TODO: Add test cases.
		{
			name: "佣金统计",
			h:    &DiscommissionService{},
			args: args{
				needUpdateMap: map[string]string{
					"4:0:65": "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.h.WithdrawCommUpdate(tt.args.needUpdateMap, enum.WithdrawFlagSelf)
		})
	}
}
