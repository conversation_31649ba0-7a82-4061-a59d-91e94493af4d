package utils

import (
	"eShop/infra/log"

	"github.com/go-redis/redis"
	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type refundSN struct {
	redis *redis.Client
	db    *xorm.Engine
}

func (s refundSN) Generate() string {
	preKey := "order-center:refund-sn-number"
	isOk := s.redis.Exists(preKey)
	if isOk.Val() <= 0 {
		var refundSn string
		_, err := s.db.Table("refund_order").Where("LENGTH(refund_sn)=11").Select("max(`refund_sn`)").Get(&refundSn)
		if err != nil {
			log.Error("查询数据库最大售后订单号异常, " + err.Error())
			panic(err)
		}
		s.redis.SetNX(preKey, refundSn, 0)
	}

	var refundSn int64
	refundSn, err := s.redis.Incr(preKey).Result()
	if err != nil {
		panic(err)
	}

	return cast.ToString(refundSn)
}
