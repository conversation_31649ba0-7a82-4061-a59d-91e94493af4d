package base_service

import (
	"crypto/tls"
	po "eShop/domain/distribution-po"
	"eShop/services/distribution-service/export"
	viewmodel "eShop/view-model"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"time"

	"github.com/spf13/cast"
	"github.com/streadway/amqp"
	"github.com/xuri/excelize/v2"

	tasklist "eShop/infra/enum"
	"eShop/infra/log"
	mq "eShop/infra/mq"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
)

type TaskSwlmService struct {
	common.BaseService
}

type BoxItem struct {
	BoxID        string // 箱号 ID
	Commission   string // 佣金
	LogisticCode []string
}

func (h TaskSwlmService) TaskInit() {
	go func() {
		for {
			mq.Consume(tasklist.TaskBlkyBoxxCodeQueue, tasklist.TaskBlkyBoxxCodeQueue, "eshop", h.<PERSON>shop<PERSON>wlmTask)
		}
	}()
}

type TaskSwlmInterface interface {
	GenerateDownUrl() (string, error)
	//导入的方法
	OperationFunc(row []string, org_id int) string
}

/*
*
mq消息发布测试：
{"id":447,"task_content":20,"task_status":1,"task_detail":"","operation_file_url":"https://rpets-saas-cos-pre.rvet.cn/530219708465609002/product/2024/09/04/a59cc11f034c4d6ba730b879ab1738fe.xlsx","request_header":"","resulte_file_url":"","create_id":"530541534660097044","create_time":"2024-09-04T16:49:53.85765+08:00","create_name":"","create_mobile":"","create_ip":"127.0.0.1","ip_location":"","success_num":0,"fail_num":0,"extended_data":"","context_data":"530219708465609002","org_id":6,"do_count":0,"err_mes":"","update_time":"2024-09-04T16:49:53.85765+08:00","operation_type":1}
*/
func (h TaskSwlmService) EshopSwlmTask(d amqp.Delivery) (response string, err error) {

	log.Info("EshopSwlmTask start")
	model := new(po.TaskList)
	if err := json.Unmarshal(d.Body, model); err != nil {
		log.Error(err)
		return err.Error(), nil
	}
	log.Info("EshopSwlmTask", ", ", model.CreateName, ", ", model.Id, ", 异步任务开始")

	//文件下载链接
	var (
		url         string
		success_num int
		fail_num    int
		err_mes     string
		task_detail string
	)

	defer func() {
		model.TaskStatus = tasklist.TaskStatusFinished
		model.ResulteFileUrl = url
		model.SuccessNum = success_num
		model.FailNum = fail_num
		model.TaskDetail = task_detail
		model.ErrMes = err_mes
		if err != nil {
			model.TaskStatus = 4
		}
		//更新状态
		h.UpdateTask(model)

		d.Ack(false)
	}()

	//修改任务状态
	h.StartTask(model)
	oet := &export.BlkyCodeTask{
		SheetName:  "Sheet1",
		F:          excelize.NewFile(),
		ImportType: cast.ToInt(model.ContextData),
		OrgType:    model.OrgType,
	}

	out, codeNum, err := h.ImportFunc(model.OperationFileUrl, model.OrgId, model.ContextData, oet.OperationFunc, model.OrgType)
	if err != nil {
		log.Error(model.CreateIp, ", 导入文件失败, ", err)
		err = errors.New("导入文件失败, " + err.Error())
		task_detail = "导入失败" + err.Error()
		err_mes = err.Error()
		return "", err
	}
	url = out.QiniuUrl
	success_num = int(out.SuccessNum)
	fail_num = int(out.FailNum)
	if model.ContextData == "1" {
		//本次共上传90个箱码（共计1800个物流码成功执行1800个物流码，失败0个）
		task_detail = fmt.Sprintf("本次共上传%d个箱码（共计%d个物流码成功执行%d个物流码，失败%d个）", codeNum, out.SuccessNum+out.FailNum, out.SuccessNum, out.FailNum)
	} else if model.ContextData == "2" {
		//本次共上传27个物流码成功执行26个物流码，失败1个
		task_detail = fmt.Sprintf("本次共上传%d个物流码成功执行%d个物流码，失败%d个", out.SuccessNum+out.FailNum, out.SuccessNum, out.FailNum)
	}
	return "success", nil
}

func (h TaskSwlmService) ImportFunc(url string, org_id int, contextData string, operation OperationFunc, orgType int) (*viewmodel.ImportResult, int, error) {
	h.Begin()
	defer h.Close()

	db := h.Engine
	out := new(viewmodel.ImportResult)
	out.Code = 400

	var err error
	req := new(http.Request)
	resp := new(http.Response)
	if org_id == enum.SaasOrgId {
		resp, err = http.Get(url)
	} else {
		// 下载excel
		req, err = http.NewRequest("POST", url, nil)
		if err != nil {
			out.Message = err.Error()
			return out, 0, nil
		}
		client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
		resp, err = client.Do(req)
	}

	if err != nil {
		out.Message = err.Error()
		return out, 0, err
	}
	defer resp.Body.Close()
	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		out.Message = err.Error()
		return out, 0, err
	}

	var handleErrList [][]string
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		out.Message = err.Error()
		return out, 0, err
	}
	var (
		oneRow     []string
		boxCodeNum int
		newRow     []string
	)
	for i, row := range rows {
		if i == 0 || i == 1 {
			oneRow = row
			continue
		}
		//如果数据比表头少的话，加空数据对齐列
		rowleng := len(row)
		if len(oneRow) > rowleng {
			for x := 0; x < len(oneRow)-rowleng; x++ {
				row = append(row, "")
			}
		}
		if contextData == "1" { //箱码导入
			boxCodeNum++
			//箱号对应多个物流码
			var swlm []string
			if orgType == 1 {
				if err = db.Table("blky.xkucun").Select("swlm").Where("sdxtm=?", row[0]).Find(&swlm); err != nil {
					log.Error("查询箱码失败", err.Error())
					out.Message = "查询箱码失败"
					return out, 0, nil
				}
			} else {
				if err = db.Table("blky.sync_outbound_log").Select("barcode as swlm").Where("source_barcode=?", row[0]).Find(&swlm); err != nil {
					log.Error("查询箱码失败", err.Error())
					out.Message = "查询箱码失败"
					return out, 0, nil
				}
			}
			code := row[0]
			if len(swlm) == 0 {
				out.FailNum = out.FailNum + 1
				newRow = []string{code, "", row[1]}
				msgs := append(newRow, "箱码id不存在")
				handleErrList = append(handleErrList, msgs)
			} else {
				re := regexp.MustCompile(`^\d{10}$`)
				if !re.MatchString(row[0]) {
					out.FailNum = out.FailNum + 1
					newRow = []string{code, "", row[1]}
					msgs := append(newRow, "箱码格式错误")
					handleErrList = append(handleErrList, msgs)
				} else {
					for _, r := range swlm {
						newRow = []string{code, r, row[1]}
						row[0] = r
						errstr := operation(row, org_id)
						if len(errstr) == 0 {
							out.SuccessNum = out.SuccessNum + 1
							msgs := append(newRow, "执行成功")
							handleErrList = append(handleErrList, msgs)
						} else {
							out.FailNum = out.FailNum + 1
							msgs := append(newRow, errstr)
							handleErrList = append(handleErrList, msgs)
						}
					}
				}
			}
		} else if contextData == "2" {
			errstr := operation(row, org_id)
			if len(errstr) == 0 {
				out.SuccessNum = out.SuccessNum + 1
				msgs := append(row, "执行成功")
				handleErrList = append(handleErrList, msgs)
			} else {
				out.FailNum = out.FailNum + 1
				msgs := append(row, errstr)
				handleErrList = append(handleErrList, msgs)
			}
		}
	}

	if contextData == "1" {
		// 将处理失败的商品信息导入excel上传至七牛云
		//oneRow = append(oneRow, "失败原因")
		//箱码id	物流码id  佣金比例	失败原因
		oneRow := []string{"箱码id", "物流码id", "佣金比例", "结果"}
		errList := append([][]string{}, oneRow)
		errList = append(errList, handleErrList...)
		url, err = utils.ExportHandleErr(errList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, 0, nil
		}
		out.QiniuUrl = url
	} else {
		oneRow = append(oneRow, "结果")
		errList := append([][]string{}, oneRow)
		errList = append(errList, handleErrList...)

		url, err = utils.ExportHandleErr(errList)
		if err != nil {
			out.Message = "错误信息上传失败; err: " + err.Error()
			return out, 0, nil
		}
		out.QiniuUrl = url
	}
	out.Code = 200
	return out, boxCodeNum, nil
}

// 更新任务状态为 进行中
func (h TaskSwlmService) StartTask(task *po.TaskList) {
	log.Info("任务详情", task)
	h.Begin()
	defer h.Close()

	db := h.Engine
	db.Exec("update task_list set task_status=2 where id =?", task.Id)
}

func (h TaskSwlmService) UpdateTask(task *po.TaskList) {
	log.Info("任务详情", task)
	h.Begin()
	defer h.Close()

	db := h.Engine
	db.Where("id =?", task.Id).Update(task)
}
