package distribution_vo

// VoucherPageReq 优惠券列表请求
type VoucherPageReq struct {
	// 页码
	PageIndex int `json:"page_index"`
	// 每页条数
	PageSize int `json:"page_size"`
	// 优惠券名称
	VoucherTitle string `json:"voucher_title"`
	// 优惠券编号
	VoucherCode string `json:"voucher_code"`
	// 状态
	VoucherState int `json:"voucher_state"`
	// 主体id
	OrgId int `json:"org_id"`
	//用户id
	MemberId int `json:"member_id" required:"true" validate:"required"`
}

// VoucherPageResp 优惠券列表响应
type VoucherPageResp struct {
	Code    int           `json:"code"`
	Message string        `json:"message"`
	Total   int64         `json:"total"`
	Data    []VoucherItem `json:"data"`
}

// VoucherItem 新增 VoucherItem 结构体
type VoucherItem struct {
	// 优惠券id
	VoucherId int64 `json:"voucher_id"`
	// 优惠券模板id
	VoucherTId int64 `json:"voucher_t_id"`
	// 优惠券标题
	VoucherTitle string `json:"voucher_title"`
	// 优惠券编号
	VoucherCode string `json:"voucher_code"`
	// 优惠券类型
	VoucherType int64 `json:"voucher_type"`
	// 优惠券面值(元)
	VoucherPrice float64 `json:"voucher_price"`
	// 开始时间
	VoucherStartDate string `json:"voucher_start_date"`
	// 结束时间
	VoucherEndDate string `json:"voucher_end_date"`
	// 状态 代金券状态(1-未用,2-已用,3-过期,4-收回)
	VoucherState int `json:"state"`
	// 代金券发放日期
	VoucherActiveDate string `json:"voucher_active_date"`
	// 领取方式 1积分兑换 2卡密兑换 3免费领取 4自动派发 5支付有礼
	VoucherTGettype int `json:"voucher_t_gettype"`
	// 领取方式文本
	GettypeText string `json:"gettype_text" xorm:"-"`
	// 多少天内可用
	VoucherDays int `json:"voucher_days"`
}
