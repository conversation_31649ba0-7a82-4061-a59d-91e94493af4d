package location

import (
	"context"
	"eShop/domain/inventory-po/location"
	warehousePO "eShop/domain/inventory-po/warehouse"
	"eShop/infra/errors"
	"eShop/services/common"
	vo "eShop/view-model/inventory-vo/location"
	"time"
)

// LocationService 库位服务
type LocationService struct {
	common.BaseService
}

// NewLocationService 创建库位服务
func NewLocationService() *LocationService {
	return &LocationService{}
}

// Create 创建库位
func (s *LocationService) Create(ctx context.Context, cmd vo.CreateCommand) error {
	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 参数校验
	if cmd.Code == "" {
		return errors.NewBadRequest("库位编码不能为空")
	}
	if cmd.Name == "" {
		return errors.NewBadRequest("库位名称不能为空")
	}

	// 2. 创建库位
	loc := &location.Location{
		//ChainID:     cmd.ChainID,
		//TenantID:    s.GetTenantID(),
		//DepotID:     s.GetStoreID(),
		Code: cmd.Code,
		//CreatedBy:   s.GetUserID(),
		CreatedTime: time.Now(),
		//UpdatedBy:   s.GetUserID(),
		UpdatedTime: time.Now(),
	}

	return loc.Create(ctx, session)
}

// Update 更新库位
func (s *LocationService) Update(ctx context.Context, cmd vo.UpdateCommand) error {
	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 获取库位
	loc := &location.Location{
		//ChainID:  s.GetChainID(),
		//TenantID: s.GetTenantID(),
	}
	err := loc.GetByID(ctx, session, cmd.ID)
	if err != nil {
		return err
	}

	// 2. 更新库位
	//loc.UpdatedBy = s.GetUserID()
	loc.UpdatedTime = time.Now()

	return loc.Update(ctx, session)
}

// Delete 删除库位
func (s *LocationService) Delete(ctx context.Context, id int) error {
	s.Begin()
	defer s.Close()
	session := s.Session
	defer session.Close()

	// 1. 获取库位
	loc := &location.Location{}
	err := loc.GetByID(ctx, session, id)
	if err != nil {
		return err
	}

	// 2. 删除库位
	return loc.Delete(ctx, session)
}

// Query 查询库位列表
func (s *LocationService) Query(ctx context.Context, params vo.QueryParams) ([]vo.Location, int, error) {
	s.Begin()
	defer func() {
		if r := recover(); r != nil {
			return
		}
		s.Close()
	}()
	session := s.Session

	// 1. 查询库位列表
	loc := &location.Location{}

	locations, total, err := loc.List(ctx, session, params)
	if err != nil {
		return nil, 0, err
	}

	// 处理仓库信息
	if len(locations) > 0 {
		for i := range locations {
			if locations[i].WarehouseId > 0 {
				warehouse, err := new(warehousePO.Warehouse).GetByID(ctx, session, locations[i].WarehouseId)
				if err != nil {
					return nil, 0, err
				}

				if warehouse.Category == 3 {
					warehouse.CategoryName = "门店仓"
					warehouse.AuthorizedChainId = 0
					warehouse.OperationType = 0
					warehouse.AuthorizedChainName = ""
				}
				if warehouse.Category == 4 {
					warehouse.CategoryName = "加盟仓"
				}
				locations[i].Warehouse = warehouse
			}
		}
	}

	return locations, int(total), nil
}

// BindProduct 绑定商品
func (s *LocationService) BindProduct(ctx context.Context, cmd vo.BindProductCommand) error {
	s.Begin()
	defer func() {
		if r := recover(); r != nil {
			return
		}
		s.Close()
	}()
	session := s.Session

	// 1. 获取库位
	loc := &location.Location{}
	err := loc.GetByID(ctx, session, cmd.LocationID)
	if err != nil || loc.Id == 0 {
		return errors.NewBadRequest("库位不存在")
	}
	// 如果loc中绑定的SKUID已经是参数的SKUID，则不需要进行操作，直接返回
	if loc.SkuId == cmd.SKUID {
		return nil
	}

	// 2. 进行校验操作，校验规则：SKUID已经绑定过其他库位，则提示已绑定
	oldLoc, _, err := new(location.Location).GetLocation(session, location.GetLocationReq{
		StoreId: loc.StoreId,
		SkuId:   cmd.SKUID,
	})
	if err != nil {
		return errors.NewBadRequest("查询库位数据库异常")
	}
	if oldLoc != nil && oldLoc.Id > 0 && oldLoc.Id != cmd.LocationID {
		return errors.NewBadRequest("商品已经绑定库位：" + oldLoc.Code + "，请先解除绑定关系")
	}

	// 3. 绑定商品
	return loc.BindProduct(ctx, session, cmd.SKUID)
}

// UnbindProduct 解绑商品
func (s *LocationService) UnbindProduct(ctx context.Context, locationId int) error {
	s.Begin()
	defer func() {
		if r := recover(); r != nil {
			return
		}
		s.Close()
	}()
	session := s.Session

	// 1. 获取库位
	loc := &location.Location{}
	err := loc.GetByID(ctx, session, locationId)
	if err != nil {
		return err
	}

	// 2. 解绑商品
	return loc.UnbindProduct(ctx, session)
}
