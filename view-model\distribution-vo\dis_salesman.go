package distribution_vo

import (
	viewmodel "eShop/view-model"
	"time"
)

type DisSalesman struct {
	//业务员ID
	Id string `json:"id" xorm:"pk autoincr not null comment('业务员ID') VARCHAR(50) 'id'"`
	//业务员姓名
	Name string `json:"name" xorm:"default '' comment('业务员姓名') VARCHAR(50) 'name'" validate:"add:required;upd:required"`
	//业务员手机号
	Mobile string `json:"mobile" xorm:"default '' comment('业务员手机号') VARCHAR(50) 'mobile'" validate:"add:required;upd:required"`
	//员工编号
	EmployeeNo string `json:"employee_no" xorm:"default '' comment('员工编号') VARCHAR(50) 'employee_no'" validate:"add:required;upd:required"`
	OrgId      int    `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//所属组织id
	RegionId int `json:"region_id" xorm:"default 0 comment('所属组织id') INT 'region_id'" validate:"add:required;upd:required"`
	//所属店铺id
	ShopId int `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'" validate:"add:required;upd:required"`
}

// 业务员列表请求参数
type DisSalesmanListReq struct {
	viewmodel.BasePageHttpRequest
	//查询条件
	Where string `protobuf:"bytes,3,opt,name=where,proto3" json:"where"`
	//查询条件的类型（name=业务员名称，mobile=商品条码，employee_no=员工编号，id=业务员ID,all=名称|手机|员工编码）
	WhereType string `protobuf:"bytes,4,opt,name=where_type,json=whereType,proto3" json:"where_type"`
	//店铺ID  0为全部
	ShopId int `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'"`
	//状态（-1全部 0停用 1启用）
	Status int `json:"status" xorm:"default 0 comment('状态：1-禁用，2-启用，0全部') INT 'status'"`
	//所属组织
	OrgName string `json:"org_name"`
	OrgId   int    `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
}

// 业务员停用/启用 请求参数
type DisSalesmanStopReq struct {
	//业务员ID
	Id string `json:"id" xorm:"pk autoincr not null comment('业务员ID') VARCHAR(50) 'id'"`
	//状态：1-禁用，2-启用，0全部
	Status int `json:"status" xorm:"default 0 comment('状态：1-禁用，2-启用，0全部') INT 'status'"`
	//业务员手机号
	Mobile string `json:"mobile" xorm:"default '' comment('业务员手机号') VARCHAR(50) 'mobile'"`
	//企业名称
	EnterpriseName string `json:"enterprise_name"`
	//店铺ID  0为全部
	ShopId int `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'"`
}

// 业务员列表返回参数
type DisSalesmanListRes struct {
	viewmodel.BasePageHttpResponse
	//业务员列表数据
	Data []DisSalesmanView `json:"data"`
}

// 业务员详情返回参数
type DisSalesmanRes struct {
	viewmodel.BaseHttpResponse
	//业务员列表数据
	Data DisSalesmanView `json:"data"`
}

// 业务员列表展示模型
type DisSalesmanView struct {
	//业务员ID
	Id string `json:"id" xorm:"pk autoincr not null comment('业务员ID') VARCHAR(20) 'id'"`
	//所属组织ID
	OrgCode string `json:"org_code" xorm:"not null comment('组织编码') VARCHAR(20) 'org_code'"`
	//所属组名称
	OrgName string `json:"org_name" xorm:"not null comment('组织名称') VARCHAR(20) 'org_name'"`
	//员工编码
	Code string `json:"code" xorm:"default 'null' comment('业务员编码') VARCHAR(20) 'code'"`
	//业务员名称
	Name string `json:"name" xorm:"not null comment('业务员名称') VARCHAR(100) 'name'"`
	//业务员手机
	Phone string `json:"phone" xorm:"not null comment('手机号') VARCHAR(13) 'phone'"`
	//二维码URL
	BarCode string `json:"bar_code" xorm:"default 0 comment('二维码URL') INT 'bar_code'"`
	//拓客分销员数量
	TuokeDisNum int `json:"tuoke_dis_num" xorm:"not null default 0 comment('拓客分销员数量') INT 'tuoke_dis_num'"`
	//服务分销员数量
	ServiceDisNum int `json:"service_dis_num" xorm:"not null default 0 comment('服务分销员数量') INT 'service_dis_num'"`
	//服务企业数
	ServiceEntNum int `json:"service_ent_num" xorm:"not null default 0 comment('服务企业数') INT 'service_ent_num'"`
	//累计分销单数
	TotalOrderNum int `json:"total_order_num" xorm:"not null default 0 comment('累计分销单数') INT 'total_order_num'"`
	//累计分销销售额
	TotalSales int `json:"total_sales" xorm:"not null default 0 comment('累计分销销售额') INT 'total_sales'"`
	//本月分销单数
	NowOrderNum int `json:"now_order_num" xorm:"not null default 0 comment('累计分销单数') INT 'now_order_num'"`
	//本月分销销售额
	NowSales int `json:"now_sales" xorm:"not null default 0 comment('累计分销销售额') INT 'now_sales'"`
	//上月分销单数
	LastOrderNum int `json:"last_order_num" xorm:"not null default 0 comment('累计分销单数') INT 'last_order_num'"`
	//上月分销销售额
	LastSales int `json:"last_sales" xorm:"not null default 0 comment('累计分销销售额') INT 'last_sales'"`
	//状态：1-禁用，2-启用，0全部
	Status int `json:"status" xorm:"default 0 comment('状态：1-禁用，2-启用，0全部') INT 'status'"`
	//加入时间
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	//更新时间
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	//total_order_pay_num
	TotalOrderPayNum int `json:"total_order_pay_num" xorm:"not null default 0 comment('累计分销单数') INT 'total_order_pay_num'"`
	//total_pay_sales
	TotalPaySales int    `json:"total_pay_sales" xorm:"not null default 0 comment('累计分销销售额') INT 'total_pay_sales'"`
	ShopName      string `json:"shop_name" xorm:"not null default '' comment('店铺名称') VARCHAR(50) 'shop_name'"`
	OrgId         int    `json:"org_id" xorm:"not null default 0 comment('所属主体id') INT 'org_id'"`
}

// 业务员中心请求参数
type SalesmanCenterListReq struct {
	viewmodel.BasePageHttpRequest
	//业务员ID
	Id string `json:"id" xorm:"pk autoincr not null comment('业务员ID') VARCHAR(50) 'id'"`
}

// 业务员中心相关数据
type SalesmanCenterData struct {
	//企业ID
	EnterpriseId string `json:"enterprise_id"`
	//企业名称
	EnterpriseName string `json:"enterprise_name"`
	OrderNum       int    `json:"order_num" xorm:"not null default 0 comment('分销订单数') INT 'order_num'"`
	Sales          int    `json:"sales" xorm:"not null default 0 comment('分销销售额') INT 'sales'"`
}

// 业务员中心相关数据
type SalesmanCenterRes struct {
	viewmodel.BaseHttpResponse
	Data []SalesmanCenterData `json:"data"`
}

// 业务员中心-销售业绩请求参数
type SalesAchievementReq struct {
	//店铺ID  0为全部
	ShopId int `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'"`
	OrgId  int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
}

// 业务员中心-销售业绩返回参数
type SalesAchievementRes struct {
	viewmodel.BaseHttpResponse
	//分销员数据
	Data []DistributorDetail `json:"data"`
}

type ShopDisPageReq struct {
	// 业务员id
	SalesmanId int `json:"salesman_id"`
	// 企业、店铺、老板、分销员
	Name string `json:"name"`
	// 时间筛选-开始时间
	StartTime string `json:"start_time"`
	// 时间筛选-结束时间
	EndTime string `json:"end_time"`
	// 1-分销单数升序，2-分销单数降序，3-分销金额升序，4-分销金额降序，5-分销佣金升序，6-分销佣金降序
	OrderType int `json:"asc_type"`
	//主体id
	OrgId int `json:"org_id"`
	viewmodel.BasePageHttpRequest
}

type SalesShopPageResp struct {
	Data []SalesShopPageData `json:"data"`
	viewmodel.BasePageHttpResponse
}

type SalesShopPageData struct {
	//企业id
	EnterpriseId int `json:"enterprise_id"`
	//企业名称
	EnterpriseName string `json:"enterprise_name"`
	//企业类型：0-企业 1-个人
	EnterpriseType int `json:"enterprise_type"`
	//店铺ID
	ShopId int `json:"shop_id"`
	//店铺名称
	ShopName string `json:"shop_name"`
	//分销金额
	OrderAmount int `json:"order_amount"`
	//分销单数
	OrderCount int `json:"order_count"`
	//分销佣金
	Commission int `json:"commission"`
	//分销员数量
	DisCount int `json:"dis_count"`
	//分销员信息列表
	DisList []SalesDisData `json:"dis_list"`
}

type SalesDisData struct {
	//分销员角色 0-初始值 1-老板 2-店员 3-医生
	DisRole int `json:"dis_role"`
	//分销员名称
	Name string `json:"name"`
	//总分销金额
	OrderCount int `json:"order_count"`
	//总分销单数
	OrderAmount int `json:"order_amount"`
	//商品分销金额
	ProductOrderCount int `json:"product_order_count"`
	//商品分销单数
	ProductOrderAmount int `json:"product_order_amount"`
	//保险分销金额
	InsureOrderCount int `json:"insure_order_count"`
	//保险分销单数
	InsureOrderAmount int `json:"insure_order_amount"`
}
