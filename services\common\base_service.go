package common

import (
	"context"
	"eShop/infra/config"
	"eShop/infra/database"
	"eShop/infra/log"
	"eShop/infra/utils"

	"github.com/spf13/cast"
	xrate "golang.org/x/time/rate"

	"runtime"

	_ "github.com/go-sql-driver/mysql"
	"github.com/opentracing/opentracing-go"
	"xorm.io/xorm"
)

type BaseService struct {
	AppName string
	Ctx     context.Context
	Engine  *xorm.Engine
	Session *xorm.Session
	span    opentracing.Span
	//EventStore events.EventStore
}

func (b *BaseService) DbDmMdm() *xorm.Engine {
	// XORM创建引擎
	sqlConn := config.Get("mysql.maindata.awen")
	//sqlConn := config.Get("mysql.eshop")
	engine, err := xorm.NewEngine("mysql", sqlConn)
	if err != nil {
		panic(err)
	}

	if !utils.IsProEnv() {
		engine.ShowSQL()
	}

	return engine
}

func (b *BaseService) initService(ctx context.Context) {
	// XORM创建引擎
	sqlConn := config.Get("mysql.eshop")
	engine, err := xorm.NewEngine("mysql", sqlConn)
	if err != nil {
		panic(err)
	}

	// 使用钩子函数
	engine.AddHook(database.NewTracingHook())
	if b.Ctx != nil {
		b.Session = engine.Context(b.Ctx)
	} else {
		b.Session = engine.Context(context.Background())
	}

	if !utils.IsProEnv() {
		engine.ShowSQL()
	}

	b.Engine = engine
	b.Ctx = ctx

	// 初始化事件存储
	//b.EventStore, err = events.NewXORMEventStore(engine)
	//if err != nil {
	//	log.Fatal(err)
	//}
}

func (b *BaseService) Begin() {

	callerFuncName := "unKnow"
	//第二个是代码文件，第三个是代码所在行数。本次暂时不用。
	pc, _, _, ok := runtime.Caller(1)
	if ok {
		fn := runtime.FuncForPC(pc)
		if fn != nil {
			callerFuncName = fn.Name()
		}
	}

	b.initService(b.Ctx)
	tracer := opentracing.GlobalTracer()
	if b.Ctx != nil {
		parentSpan := opentracing.SpanFromContext(b.Ctx)
		b.span = tracer.StartSpan(callerFuncName, opentracing.ChildOf(parentSpan.Context()))
	}
}

func (b *BaseService) Close() {
	if b.Ctx != nil {
		b.span.Finish()
	}
}

// 请求第三方接口限制器
type limiterManager struct {
	limiters map[string]*xrate.Limiter
}

var singleton *limiterManager

func GetLimiterManager() *limiterManager {
	if singleton == nil {
		singleton = &limiterManager{
			limiters: make(map[string]*xrate.Limiter),
		}
	}
	return singleton
}

func GetLimiter(rateConfigKey string, rate float64) *xrate.Limiter {
	manager := GetLimiterManager()
	if limiter, ok := manager.limiters[rateConfigKey]; ok {
		return limiter
	}

	if rate == 0 {
		rateConfig := config.Get(rateConfigKey)
		log.Info("获取限流配置", rateConfigKey, ":", rateConfig)
		rate = cast.ToFloat64(rateConfig)
		if rate == 0 {
			rate = 2
		}
	}

	limiter := xrate.NewLimiter(xrate.Limit(rate), 1)
	manager.limiters[rateConfigKey] = limiter
	return limiter
}
