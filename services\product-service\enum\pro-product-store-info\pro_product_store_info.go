package proproductstoreinfo

const (

	//eshop.pro_product_store_info.`up_down_state` int DEFAULT '0' COMMENT '上下架状态（1-上架，0-下架）',
	UpDownStateUp   = 1
	UpDownStateDown = 0

	//eshop.pro_product_store_info.`status` int DEFAULT '0' COMMENT '1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败 ',
	StatusNormal       = 1 // 正常
	StatusIng          = 2 // 操作中
	StatusLaunchFailed = 3 //铺品失败
	StatusUpFailed     = 4 //上架失败
	StatusDownFailed   = 5 //下架失败
	StatusUpdateFailed = 6 //更新失败

	//eshop.pro_product_store_info.`is_distribution` int DEFAULT '0' COMMENT '0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功',
	IsDistributionUnLaunch = 0 //未铺品
	IsDistributionLaunched = 1 // 已铺品

	//商品操作类型 1-铺品 2-上架 3-下架 4库存同步  5-商品调价 6-删除商品
	ProductTypeLaunch = 1 //铺品
	ProductTypeUp     = 2 //上架
	ProductTypeDown   = 3 //下架
	ProductTypeStock  = 4 //库存同步
	ProductTypePrice  = 5 //商品调价
	ProductTypeDel    = 6 //删除商品
	ProductTypeUpdate = 7 //编辑商品
)
