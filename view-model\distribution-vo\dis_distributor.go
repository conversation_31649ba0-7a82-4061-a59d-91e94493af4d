package distribution_vo

import (
	viewmodel "eShop/view-model"
	"time"
)

type DisDistributor struct {
	Id                  int       `json:"id" xorm:"pk autoincr not null comment('分销员ID') INT 'id'"`
	OrgId               int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	ShopId              int       `json:"shop_id" xorm:"default 0 comment('分销店铺id') INT 'shop_id'"`
	MemberId            int       `json:"member_id" xorm:"not null comment('分销员用户id') INT 'member_id'"`
	SocialCreditCode    string    `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	SocialCodeImage     string    `json:"social_code_image" xorm:"default '' comment('统一社会信用代码图片') VARCHAR(255) 'social_code_image'"`
	EncryptIdCard       string    `json:"encrypt_id_card" xorm:"not null default '' comment('加密身份证号码') VARCHAR(100) 'encrypt_id_card'"`
	IdCard              string    `json:"id_card" xorm:"not null default '' comment('身份证号码') VARCHAR(100) 'id_card'"`
	IdcardFront         string    `json:"idcard_front" xorm:"not null default '' comment('分销员身份证正面') VARCHAR(255) 'idcard_front'"`
	IdcardReverse       string    `json:"idcard_reverse" xorm:"not null default '' comment('分销员身份证反面') VARCHAR(255) 'idcard_reverse'"`
	DisRole             int       `json:"dis_role" xorm:"not null default 0 comment('分销员角色 0-初始值 1-老板 2-店员') INT 'dis_role'"`
	Status              int8      `json:"status" xorm:"default 0 comment('分销状态 0-未启用 1-启用 2-禁用') TINYINT(1) 'status'"`
	Name                string    `json:"name" xorm:"default '' comment('分销员姓名') VARCHAR(50) 'name'"`
	Mobile              string    `json:"mobile" xorm:"default '' comment('分销员手机号加*(可修改，有可能跟登录的手机号不一致)') VARCHAR(50) 'mobile'"`
	EncryptMobile       string    `json:"encrypt_mobile" xorm:"default '' comment('加密手机号') VARCHAR(20) 'encrypt_mobile'"`
	BankName            string    `json:"bank_name" xorm:"default '' comment('收款银行') VARCHAR(255) 'bank_name'"`
	BankAccount         string    `json:"bank_account" xorm:"default '' comment('收款账号') VARCHAR(255) 'bank_account'"`
	EncryptBankAccount  string    `json:"encrypt_bank_account" xorm:"default '' comment('加密收款账号') VARCHAR(100) 'encrypt_bank_account'"`
	AccountName         string    `json:"account_name" xorm:"default '' comment('开户姓名') VARCHAR(50) 'account_name'"`
	EncryptWithIdcard   string    `json:"encrypt_with_idcard" xorm:"not null default '' comment('用于提现的加密身份证号码') VARCHAR(100) 'encrypt_with_idcard'"`
	WithdrawIdCard      string    `json:"withdraw_id_card" xorm:"not null default '' comment('用于提现的身份证号码') VARCHAR(100) 'withdraw_id_card'"`
	BankBranch          string    `json:"bank_branch" xorm:"default '' comment('收款支行') VARCHAR(255) 'bank_branch'"`
	CreateTime          time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime          time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	BarCode             string    `json:"bar_code" xorm:"default 'null' comment('分销员二维码') VARCHAR(200) 'bar_code'"`
	HeadImage           string    `json:"head_image" xorm:"default 'null' comment('分销员头像') VARCHAR(200) 'head_image'"`
	TuokeSalespersonId  int       `json:"tuoke_salesperson_id" xorm:"not null default 0 comment('拓客业务员id') BIGINT 'tuoke_salesperson_id'"`
	OrderNum            int       `json:"order_num" xorm:"default 0 comment('分销单数') INT 'order_num'"`
	TotalSales          int       `json:"total_sales" xorm:"default 0 comment('分销销售额(分)') INT 'total_sales'"`
	SettledCommission   int       `json:"settled_commission" xorm:"default 0 comment('已结佣金(分)') INT 'settled_commission'"`
	UnsettledCommission int       `json:"unsettled_commission" xorm:"not null default 0 comment('未结佣金(分)') INT 'unsettled_commission'"`
	TotalCustomer       int       `json:"total_customer" xorm:"default 0 comment('累计客户数') INT 'total_customer'"`
	OrderPayNum         int       `json:"order_pay_num" xorm:"default 0 comment('已支付分销单数') INT 'order_pay_num'"`
	TotalPaySales       int       `json:"total_pay_sales" xorm:"default 0 comment('已支付分销销售额(分)') INT 'total_pay_sales'"`
	InsOrderPayNum      int       `json:"ins_order_pay_num" xorm:"default 0 comment('已支付保险分销单数') INT 'order_pay_num'"`
	InsTotalPaySales    int       `json:"ins_total_pay_sales" xorm:"default 0 comment('已支付保险分销销售额(分)') INT 'total_pay_sales'"`
	ApproveState        int8      `json:"approve_state" xorm:"default 0 comment('认证状态： 0-初始值， 1-待审核 2-审核通过 3-审核失败') TINYINT 'approve_state'"`
	Reason              string    `json:"reason" xorm:"default '' comment('审核拒绝的原因') VARCHAR(255) 'reason'"`
	BankMobile          string    `json:"bank_mobile" xorm:"default '' comment('收款人手机号加*') VARCHAR(50) 'bank_mobile'"`
	BankEncryptMobile   string    `json:"bank_encrypt_mobile" xorm:"default '' comment('收款人加密手机号') VARCHAR(20) 'bank_encrypt_mobile'"`

	RealName string `json:"real_name" xorm:"default '' comment('分销员真实姓名') VARCHAR(50) 'real_name'"`
}

type DisDistributorAddReq struct {
	OrgId int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//分销员用户id
	MemberId int `json:"member_id" xorm:"not null comment('分销员用户id') INT 'member_id'"`
	//身份证号码
	IdCard string `json:"id_card" xorm:"not null default '' comment('身份证号码') VARCHAR(100) 'id_card'"`
	//分销员身份证正面
	IdcardFront string `json:"idcard_front" xorm:"not null default '' comment('分销员身份证正面') VARCHAR(255) 'idcard_front'"`
	//分销员身份证反面
	IdcardReverse string `json:"idcard_reverse" xorm:"not null default '' comment('分销员身份证反面') VARCHAR(255) 'idcard_reverse'"`
	//分销员角色 0-初始值 1-老板 2-店员 3-医生
	DisRole int `json:"dis_role" xorm:"not null default 0 comment('分销员角色 0-初始值 1-老板 2-店员 3-医生') INT 'dis_role'" validate:"4_boss:required;4_worker:required" label:"4_boss:企业角色;4_worker:企业角色"`
	//分销员姓名或百林康源用户名
	Name string `json:"name" xorm:"default '' comment('分销员姓名') VARCHAR(50) 'name'"`
	//分销员真实姓名
	RealName string `json:"real_name" xorm:"default '' comment('分销员姓名') VARCHAR(50) 'real_name'"   validate:"4_boss:required;4_worker:required" label:"4_boss:真实姓名;4_worker:真实姓名"`
	//分销员手机号加
	Mobile string `json:"mobile" xorm:"default '' comment('分销员手机号加*(可修改，有可能跟登录的手机号不一致)') VARCHAR(50) 'mobile'" validate:"4_boss:required;4_worker:required;" label:"4_boss:手机号;4_worker:手机号"`
	//分销员头像
	HeadImage string `json:"head_image" xorm:"default 'null' comment('分销员头像') VARCHAR(200) 'head_image'"`
	//拓客业务员id
	TuokeSalespersonId string `json:"tuoke_salesperson_id" xorm:"not null default 0 comment('拓客业务员id') VARCHAR(50) 'tuoke_salesperson_id'"`
	//百林康源拓客业务员名称
	TuokeSalesmanName string `json:"tuoke_salesman_name" validate:"4:chinese4" label:"4:邀请人"`
	//企业ID
	EnterpriseId string `json:"enterprise_id"`
	//企业名称
	EnterpriseName string `json:"enterprise_name"  validate:"4_boss:required;4_worker:required" label:"4_boss:企业名称;4_worker:企业名称"`
	// 统一社会信用代码
	SocialCreditCode string `json:"social_credit_code" validate:"4_boss:required" label:"4_boss:社会信用代码"`
	//统一社会信用代码图片或百林康源的兽医师资格证
	SocialCodeImage string `json:"social_code_image" validate:"4:required;" label:"4:兽医师资格证;"`

	HospitalName string `json:"hospital_name"  validate:"4:required"`                             //医院名称（百林康源）
	Province     string `json:"province"  validate:"4:required;4_boss:required" label:"4_boss:省"` //省份（百林康源）
	City         string `json:"city"  validate:"4:required;4_boss:required" label:"4_boss:市"`     //城市（百林康源）
	// 区
	District string `json:"district" validate:"4_boss:required" label:"4_boss:区"`
	// 详细地址
	Address      string `json:"address" validate:"4_boss:required" label:"4_boss:详细地址"`
	Professional string `json:"professional"  validate:"4:required"` //身份、职称（百林康源）
	Specialize   string `json:"specialize" `                         //擅长（百林康源）
	// 操作类型：0-百林康源医生身份入驻 1-宠利扫分销员入驻
	OperationType int `json:"operation_type"`
	// 宠利扫老板分销员ID
	BossDisId int `json:"boss_dis_id" validate:"4_worker:required" label:"4_worder:老板分销员ID"`
	// 邀请码
	InviteCode int64 `json:"invite_code" validate:"4_worker:required" label:"4_worker:邀请码"`
}

type BossInviteDistributorReq struct {
	//分销员手机号(前端无需传入)
	Mobile string `json:"mobile"`
	//分销员用户id(前端无需传入)
	MemberId int `json:"member_id"`
	//scrmID(前端无需传入)
	ScrmUserId string `json:"scrm_user_id"`
	//主体id
	OrgId int `json:"org_id"`
}
type InviteData struct {
	BossDisId int    `json:"boss_dis_id"`  // 老板分销员ID
	Phone     string `json:"invite_phone"` // 被邀请人手机号
	EndTime   int64  `json:"end_time"`     // 邀请码有效期
}
type CheckBossInviteCodeData struct {
	IsUsed         int    `json:"is_used"`         // 邀请码是否失效，1-失效，0-未失效
	EnterpriseName string `json:"enterprise_name"` // 企业名称
	BossDisId      int    `json:"boss_dis_id"`     // 老板分销员ID
}

type BossInviteDistributorRes struct {
	viewmodel.BaseHttpResponse
	Data int64 `json:"data"`
}
type CheckBossInviteCodeReq struct {
	// 邀请码
	InviteCode int64 `json:"invite_code" validate:"required" label:"邀请码"`
	// 被邀请人手机号
	Phone string `json:"phone" validate:"required" label:"被邀请人手机号"`
}

type CheckBossInviteCodeRes struct {
	viewmodel.BaseHttpResponse
	Data CheckBossInviteCodeData `json:"data"`
}
type RemoveDistributorReq struct {
	// 店员分销员ID
	DisId int `json:"dis_id" validate:"required" label:"店员分销员ID"`
	//分销员手机号(前端无需传入)
	Mobile string `json:"mobile"`
	//分销员用户id(前端无需传入)
	MemberId int `json:"member_id"`
	//scrmID(前端无需传入)
	ScrmUserId string `json:"scrm_user_id"`
	//主体id
	OrgId int `json:"org_id"`
}
type RemoveDistributorRes struct {
	viewmodel.BaseHttpResponse
	Data int `json:"data"`
}
type SelfRemoveEnterpriseRes struct {
	viewmodel.BaseHttpResponse
	Data int `json:"data"`
}
type SelfRemoveEnterpriseReq struct {
	//分销员手机号(前端无需传入)
	Mobile string `json:"mobile"`
	//分销员用户id(前端无需传入)
	MemberId int `json:"member_id"`
	//scrmID(前端无需传入)
	ScrmUserId string `json:"scrm_user_id"`
	//主体id
	OrgId int `json:"org_id"`
}
type OcrpPredictionSocialCodeReq struct {
	// 营业执照图片
	SocialCodeImage string `json:"social_code_image" validate:"required" label:"营业执照图片"`
}
type OcrpPredictionSocialCodeRes struct {
	viewmodel.BaseHttpResponse
	//业务员列表数据
	Data OcrpPredictionSocialCodeData `json:"data"`
}

type EnterpriseBySocialCodeReq struct {
	// 社会信用代码
	SocialCreditCode string `json:"social_credit_code" validate:"required" label:"社会信用代码"`
}

type EnterpriseBySocialCodeRes struct {
	viewmodel.BaseHttpResponse
	Data OcrpPredictionSocialCodeData `json:"data"`
}
type OcrpPredictionSocialCodeData struct {
	// 社会信用代码
	SocialCreditCode string `json:"social_credit_code"`
	// 企业名称
	EnterpriseName string `json:"enterprise_name"`
}

type CheckEnterpriseBindedRes struct {
	viewmodel.BaseHttpResponse
	// 检查社会信用代码是否被其他人使用
	Data string `json:"data"`
}
type CheckEnterpriseBindedReq struct {
	// 社会信用代码
	SocialCreditCode string `json:"social_credit_code" validate:"required" label:"社会信用代码"`
	// 法人手机号
	Mobile string `json:"mobile" validate:"required" label:"法人手机号"`
}

type DisDistributorAddResp struct {
	viewmodel.BasePageHttpResponse
	//业务员列表数据
	Data DisDistributorAddData `json:"data"`
}

type DisDistributorAddData struct {
	//认证状态：1-待审核 2-审核通过 3-审核失败
	ApproveState int8 `json:"approve_state"`
}

type DistributorPageReq struct {
	//分销状态 1-启用 2-禁用
	Status int8 `json:"status"`
	//认证状态：1-待审核 2-审核通过 3-审核失败
	ApproveState int8 `json:"approve_state"`
	//绑定医院
	HospitalName string `json:"hospital_name"`
	//主体id
	OrgId int `json:"org_id"`
	//分销店铺id
	ShopId int `json:"shop_id"`
	//查询条件
	Where string `json:"where"`
	//查询条件的类型（id=分销员id，name=分销员姓名，mobile=分销员手机号，belong_salesman_id=所属业务员ID，belong_salesman_name=所属业务员，belong_salesman_mobile=业务员手机号，tuoke_salesman_name=注册业务员ID，tuoke_salesman_name=注册业务员）
	WhereType string `json:"where_type"`
	//查询条件,企业名称和r1编码
	EnterpriseName string `json:"enterprise_name"`
	//企业ID  ，用于小程序接口，销售业绩界面查询
	EnterpriseId string `json:"enterprise_id"`
	//百林康源企业id
	DisEnterpriseId int `json:"dis_enterprise_id"`
	viewmodel.BasePageHttpRequest
}

type DistributorPageResp struct {
	viewmodel.BasePageHttpResponse
	//业务员列表数据
	Data []DistributorPageData `json:"data"`
}

type DistributorResp struct {
	viewmodel.BaseHttpResponse
	//业务员列表数据
	Data DistributorPageData `json:"data"`
}

type DistributorReq struct {
	//分销员id
	Id int `json:"id"`
	//分销状态 0-未启用 1-启用 2-禁用
	Status int8 `json:"status"`
	//主体id
	OrgId int `json:"org_id"`
	//分销店铺id
	ShopId int `json:"shop_id"`
	//分销员姓名
	Name string `json:"name"`
	//分销员手机号
	Mobile string `json:"mobile"`
	//所属业务员ID
	BelongSalesmanId string `json:"belong_salesman_id"`
	//所属业务员
	BelongSalesmanName string `json:"belong_salesman_name"`
	//业务员手机号
	BelongSalesmanMobile string `json:"belong_salesman_mobile"`
	//分销员用户id
	MemberId int `json:"member_id"`
	//scrmID
	ScrmUserId string `json:"scrm_user_id"`
	viewmodel.BasePageHttpRequest
}

type GetMonthStatReq struct {
	DisId       int `json:"dis_id"`        //分销员id
	ShopId      int `json:"shop_id"`       //店铺id
	DisMemberId int `json:"dis_member_id"` //分销员会员id
	OrgId       int `json:"org_id"`        //主体id
}
type GetMonthStatData struct {
	// 本月订单数
	MonthOrderNum int `json:"month_order_num"`

	// 本月订单金额
	OrderAmount float64 `json:"order_amount"`
	// 本月佣金
	DeductAmount int `json:"deduct_amount"`
}

type DistributorPageData struct {
	//分销员id
	Id int `json:"id"`
	//会员Id
	MemberId int `json:"member_id"`
	//会员账号
	MemberName string `json:"member_name"`
	//分销员姓名
	Name string `json:"name"`
	//分销员真实姓名
	RealName string `json:"real_name"`
	//分销员二维码
	BarCode string `json:"bar_code"`
	//分销员头像
	HeadImage string `json:"head_image"`
	//分销员手机号
	Mobile string `json:"mobile"`
	//分销员手机号密文
	EncryptMobile string `json:"encrypt_mobile"`
	//拓客业务员，注册业务员名称
	TuokeSalesmanName string `json:"tuoke_salesman_name"`
	//拓客业务员，注册业务员ID
	TuokeSalespersonId string `json:"tuoke_salesperson_id"`
	//分销员角色 0-初始值 1-老板 2-店员 3-医生
	DisRole int `json:"dis_role"`
	//分销店铺id
	ShopId int `json:"shop_id"`
	//店铺名称
	ShopName string `json:"shop_name"`
	//企业ID
	EnterpriseId string `json:"enterprise_id"`
	//企业名称
	EnterpriseName string `json:"enterprise_name"`
	//分销企业名称（宠利扫）
	DisEnterpriseName string `json:"dis_enterprise_name"`
	//企业R1编码
	EnterpriseCode string `json:"code" xorm:"not null default '' comment('企业R1编码') INT 'code'"`
	//分销单数
	OrderNum int `json:"order_num"`
	//分销销售额(分)
	TotalSales int `json:"total_sales"`
	//总佣金(分) (百林康源的累计收益)
	//已支付分销单数
	OrderPayNum int `json:"order_pay_num"`
	//已支付分销销售额(分)
	TotalPaySales int `json:"total_pay_sales"`
	//总佣金(分)
	TotalCommission int `json:"total_commission"`
	// 店铺总佣金(分)
	ShopTotalCommission int `json:"shop_total_commission"`
	//已结佣金(分)
	SettledCommission int `json:"settled_commission"`
	//未结佣金(分)
	UnsettledCommission int `json:"unsettled_commission"`
	//已结总佣金(分)
	SettledCommissionTotal int `json:"settled_commission_total"`
	//未结总佣金(分)
	UnsettledCommissionTotal int `json:"unsettled_commission_total"`
	//分销店铺销售额(分)
	ShopTotalSales int `json:"shop_total_sales"`
	//分销店铺分销单数
	ShopOrderNum int `json:"shop_order_num"`
	//分销店铺已支付分销单数
	ShopOrderPayNum int `json:"shop_order_pay_num"`
	//分销店铺已支付分销销售额(分)
	ShopTotalPaySales int `json:"shop_total_pay_sales"`
	//分销店铺已结佣金(分)
	ShopSettledCommissionTotal int `json:"shop_settled_commission_total"`
	//分销店铺未结佣金(分)
	ShopUnsettledCommissionTotal int `json:"shop_unsettled_commission_total"`
	//提现成功(分)
	WithdrawSuccess int `json:"withdraw_success"`
	//提现申请(分)（百林康源未打款金额）
	WithdrawApply int `json:"withdraw_apply"`
	//待提现(分)  （百林康源可提现金额）
	WaitWithdraw int `json:"wait_withdraw"`
	//提现成功(分) (店铺已提现金额)
	ShopWithdrawSuccess int `json:"shop_withdraw_success"`
	//提现申请(分)（店铺未打款金额）
	ShopWithdrawApply int `json:"shop_withdraw_apply"`
	//待提现(分)  （店铺可提现金额）
	ShopWaitWithdraw int `json:"shop_wait_withdraw"`
	//累计客户数
	TotalCustomer int `json:"total_customer"`
	//有效客户数
	ValidCustomer int `json:"valid_customer"`
	//分销状态 0-初始值   1-启用 2-禁用（已清退） 3-未启用
	Status int8 `json:"status"`
	//加入时间
	CreateTime string `json:"create_time"`
	//更新时间
	UpdateTime string `json:"update_time"`
	//业务员集合
	BelongSalesman []BelongSalesmanData
	// 认证状态： 0-初始值， 1-待审核 2-审核通过 3-审核失败
	ApproveState int `json:"approve_state"`
	// 审核拒绝的原因
	Reason string `json:"reason"`
	// 百林康源分销员 医院名称
	HospitalName string `json:"hospital_name" xorm:"not null default '' VARCHAR(32) 'hospital_name'"`
	// 百林康源分销员 省
	Province string `json:"province" xorm:"not null default '' comment('省份') VARCHAR(20) 'province'"`
	// 百林康源分销员 市
	City string `json:"city" xorm:"not null default '' comment('城市') VARCHAR(20) 'city'"`
	// 百林康源分销员 身份
	Professional string `json:"professional" xorm:"default '' comment('身份、职称') VARCHAR(20) 'professional'"`
	// 百林康源分销员 擅长
	Specialize string `json:"specialize" xorm:"default '' comment('擅长') VARCHAR(20) 'specialize'"`
	//统一社会信用代码图片(百林康源存储兽医资格证)
	SocialCodeImage string `json:"social_code_image"`
	// 统一社会信用代码
	SocialCreditCode string `json:"social_credit_code"`
	// 类型   1：体系内   0：体系外
	InSystem int `json:"in_system"`
	// 兽医资格证编号
	VeterinaryCode string `json:"veterinary_code"`
	// 来源类型：0-润合云店，1-润合SAAS，
	SourceType int `json:"source_type"`
	// 主体数据来源：0-润合云店，1-润合SAAS，2-百林康源，3-宠利扫
	OrgDataSource int `json:"org_data_source"`
	// 润合SAAS店铺ID
	SaasShopId string `json:"saas_shop_id"`
	//类型（0企业 1个人）
	EnterpriseType int `json:"enterprise_type"`
	// 企业所在省份
	EnterpriseProvince string `json:"enterprise_province"`
	// 企业所在城市
	EnterpriseCity string `json:"enterprise_city"`
	// 企业所在区
	District string `json:"district"`
	// 企业所在详细地址
	Address string `json:"address"`

	// 本月订单数
	MonthOrderNum int `json:"month_order_num"`
	// 本月佣金
	MonthCommission int `json:"month_commission"`
	// 是否展示百林康源医生角色时分销订单数据
	IsShowBLKYDoctorOrder bool `json:"is_show_blky_doctor_order"`

	// 剩余积分数
	AvailablePoints int `json:"available_points"`
}

// 临时数据
type BelongSalesmanTemp struct {
	//所属业务员Id
	BelongSalesmanId string `json:"belong_salesman_id"`
	//所属业务员
	BelongSalesmanName string `json:"belong_salesman_name"`
	//企业ID
	EnterpriseId int64  `json:"enterprise_id"`
	OrgName      string `json:"org_name" xorm:"not null comment('组织名称') VARCHAR(20) 'org_name'"`
}

// 业务员LIST
type BelongSalesmanData struct {
	//所属业务员Id
	BelongSalesmanId string `json:"belong_salesman_id"`
	//所属业务员
	BelongSalesmanName string `json:"belong_salesman_name"`
	OrgName            string `json:"org_name" xorm:"not null comment('组织名称') VARCHAR(20) 'org_name'"`
}

type DisIdReq struct {
	//分销员id
	Id int `json:"id" validate:"required"`
	//主体id
	OrgId int `json:"org_id"`
}

type DisChangeReq struct {
	//分销员id
	Id int `json:"id" validate:"required"`
	//主体id
	OrgId int `json:"org_id"`
	//旧的企业ID
	OldEnterpriseId string `json:"old_enterprise_id" validate:"required"`
	//新的企业ID
	EnterpriseId string `json:"enterprise_id" validate:"required"`
	//旧的企业名称
	OldEnterpriseName string `json:"old_enterprise_name"`
	//新的企业名称
	EnterpriseName string `json:"enterprise_name"`
}
type BLKYDisChangeReq struct {
	//分销员id
	Id int `json:"id" validate:"required"`
	//主体id
	OrgId int `json:"org_id"`
	//旧的企业社会信用代码
	OldSocialCreditCode string `json:"old_social_credit_code" validate:"required"`
	//新的企业社会信用代码
	SocialCreditCode string `json:"social_credit_code" validate:"required"`
	//旧的企业名称
	OldEnterpriseName string `json:"old_enterprise_name"`
	//新的企业名称
	EnterpriseName string `json:"enterprise_name"`
}
type DisChangeData struct {
	//旧业务员id
	OldId int `json:"old_id"`
	//旧业务员名称
	OldSalesmanName string `json:"old_salesman_name"`
	//新业务员id
	NewId int `json:"new_id"`
	//新业务员名称
	NewSalesmanName string `json:"new_salesman_name"`
}

type DisShareReq struct {
	//分享路径
	Path string `json:"path"`
	//参数
	Sid string `json:"sid"`
	//主体id
	OrgId int `json:"org_id"`
	// 同个主体id,先区分下来源类型：0-默认百林康源，1-宠利扫
	SourceType int `json:"source_type"`
}

type DisShareRes struct {
	viewmodel.BaseHttpResponse
	//小程序码URL
	Url string `json:"url"`
}

type GetShopDistributorsRes struct {
	viewmodel.BaseHttpResponse
	//小程序码URL
	Data []DisDistributor `json:"data"`
}
type GetShopDistributorsReq struct {
	//主体id
	OrgId int `json:"org_id"`
	//Mobile string `json:"mobile"` //分销员电话号码(前端不用传)
	//分销员用户id
	MemberId int `json:"member_id"`
	//scrmID
	ScrmUserId string `json:"scrm_user_id"`
}

type DisEditReq struct {
	//分销员id
	Id int `json:"id" validate:"add:required;upd:required;4:required"`
	//主体id
	OrgId int `json:"org_id"`
	//分销员头像
	HeadImage string `json:"head_image"`
	//分销员姓名
	Name string `json:"name"`
	//分销员真实姓名
	RealName string `json:"real_name"`
	//分销员手机号
	Mobile string `json:"mobile"`
	//修改类型， 1修改手机号 2修改头像和名称 3百林康源重新认证 4百林康源医生分销员修改信息
	EditType int `json:"edit_type"`
	//修改手机号的时候需要传
	Code string `json:"code"`
	//邀请人
	TuokeSalesmanName string `json:"tuoke_salesman_name" validate:"4:chinese4" label:"4:邀请人"`
	//统一社会信用代码图片或百林康源的兽医师资格证
	SocialCodeImage string `json:"social_code_image" xorm:"default '' comment('统一社会信用代码图片') VARCHAR(255) 'social_code_image'"  validate:"4:required"`

	HospitalName string `json:"hospital_name"  validate:"4:required"` //医院名称（百林康源）
	Province     string `json:"province"  validate:"4:required"`      //省份（百林康源）
	City         string `json:"city"  validate:"4:required"`          //城市（百林康源）
	Professional string `json:"professional"  validate:"4:required"`  //身份、职称（百林康源）
	Specialize   string `json:"specialize" `                          //擅长（百林康源）

}

type DistributorDetailRes struct {
	viewmodel.BaseHttpResponse
	//数据内容
	Data DistributorDetail `json:"data"`
}

type CLSDistributorEditReq struct {
	//主体id
	OrgId int `json:"org_id"`
	//分销员id
	Id int `json:"id" validate:"required"`
	//分销员用户id
	MemberId  int    `json:"member_id"`
	RealName  string `json:"real_name" validate:"required" label:"真实姓名"`
	HeadImage string `json:"head_image"  label:"头像"`

	//企业名称
	EnterpriseName string `json:"enterprise_name"   label:"企业名称"`
	// 统一社会信用代码
	SocialCreditCode string `json:"social_credit_code"  label:"社会信用代码"`
	//统一社会信用代码图片或百林康源的兽医师资格证
	SocialCodeImage string `json:"social_code_image"`
	Province        string `json:"province"  validate:"required" label:"省"` //企业所在省份
	City            string `json:"city"  validate:"required" label:"市"`     //企业所在城市
	// 区
	District string `json:"district" validate:"required" label:"区"`
	// 详细地址
	Address string `json:"address" validate:"required" label:"详细地址"`
	//邀请人
	TuokeSalesmanName string `json:"tuoke_salesman_name" validate:"chinese4" label:"邀请人"`
}
type CLSDistributorEditResp struct {
	viewmodel.BaseHttpResponse
}

// 分销员详情，后端用于展示
type DistributorDetail struct {
	//分销员id
	Id int `json:"id"`
	//分销员姓名
	Name string `json:"name"`
	//分销员手机号
	Mobile string `json:"mobile"`
	//分销员手机号密文
	EncryptMobile string `json:"encrypt_mobile"`
	//收款人手机号加*
	BankMobile string `json:"bank_mobile"`
	//收款人手要号密文
	BankEncryptMobile string `json:"bank_encrypt_mobile"`
	//所属业务员
	BelongSalesmanName string `json:"belong_salesman_name"`
	//所属业务员Id
	BelongSalesmanId string `json:"belong_salesman_id"`
	//所属业务员状态  状态：1-禁用，2-启用
	SalesmanStatus int `json:"salesman_status"`
	//分销员角色 0-初始值 1-老板 2-店员
	DisRole int `json:"dis_role"`
	//企业ID
	EnterpriseId string `json:"enterprise_id"`
	//企业名称
	EnterpriseName string `json:"enterprise_name"`
	//分销企业名称
	DisEnterpriseName string `json:"dis_enterprise_name"`
	//企业状态 状态（0停用 1启用）
	EnterpriseStatus string `json:"enterprise_status"`
	//分销单数
	OrderNum int `json:"order_num"`
	//分销销售额(分)
	TotalSales int `json:"total_sales"`
	//分销店铺销售额(分)
	InsOrderNum int `json:"ins_order_num" xorm:"default 0 comment('保险分销单数') INT 'ins_order_num'"`
	//分销店铺分销单数
	InsTotalSales int `json:"ins_total_sales" xorm:"default 0 comment('保险分销销售额(分)') INT 'ins_total_sales'"`
	//已支付分销单数
	OrderPayNum int `json:"order_pay_num"`
	//已支付分销销售额(分)
	TotalPaySales int `json:"total_pay_sales"`
	//已支付保险分销单数
	InsOrderPayNum int `json:"ins_order_pay_num" xorm:"default 0 comment('已支付保险分销单数') INT 'ins_order_pay_num'"`
	//已支付保险分销销售额(分)
	InsTotalPaySales int `json:"ins_total_pay_sales" xorm:"default 0 comment('已支付保险分销销售额(分)') INT 'ins_total_pay_sales'"`
	//已结佣金(分)
	SettledCommission int `json:"settled_commission"`
	//未结佣金(分)
	UnsettledCommission int `json:"unsettled_commission"`
	//累计客户数
	TotalCustomer int `json:"total_customer"`
	//有效客户数
	ValidCustomer int `json:"valid_customer"`
	//收款银行
	BankName string `json:"bank_name"`
	//收款银行账户
	BankAccount string `json:"bank_account"`
	//加密收款账号
	EncryptBankAccount string `json:"encrypt_bank_account"`
	//收款支行
	BankBranch string `json:"bank_branch"`
	//收款人
	AccountName string `json:"account_name"`
	//用于提现的加密身份证号码
	EncryptWithIdCard string `json:"encrypt_with_idcard" xorm:"not null default '' comment('用于提现的加密身份证号码') VARCHAR(100) 'encrypt_with_idcard'"`
	//用于提现的身份证号码
	WithdrawIdCard string `json:"withdraw_id_card"`
	//分销员头像
	HeadImage string `json:"head_image"`
	//认证状态： 0-初始值， 1-待审核 2-审核通过 3-审核失败
	ApproveState int `json:"approve_state"`
	//审核拒绝的原因
	Reason string `json:"reason"`
	//提现成功
	WithdrawSuccess int `json:"withdraw_success"`
	//提现申请
	WithdrawApply int `json:"withdraw_apply"`
	//待提现
	WaitWithdraw int `json:"wait_withdraw"`
	//统一社会信用代码
	SocialCreditCode string `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	//统一社会信用代码图片
	SocialCodeImage string `json:"social_code_image" xorm:"default '' comment('统一社会信用代码图片') VARCHAR(255) 'social_code_image'"`
	//加密身份证号码
	EncryptIdCard string `json:"encrypt_id_card" xorm:"not null default '' comment('加密身份证号码') VARCHAR(100) 'encrypt_id_card'"`
	//身份证号码
	IdCard string `json:"id_card" xorm:"not null default '' comment('身份证号码') VARCHAR(100) 'id_card'"`
	//分销员身份证正面
	IdcardFront string `json:"idcard_front" xorm:"not null default '' comment('分销员身份证正面') VARCHAR(255) 'idcard_front'"`
	//分销员身份证反面
	IdcardReverse string `json:"idcard_reverse" xorm:"not null default '' comment('分销员身份证反面') VARCHAR(255) 'idcard_reverse'"`
	//业务员集合
	BelongSalesman []BelongSalesmanData
	// 百林康源分销员 医院名称
	HospitalName string `json:"hospital_name"`
	// 百林康源分销员 省
	Province string `json:"province"`
	// 百林康源分销员 企业省
	EnterpriseProvince string `json:"enterprise_province"`
	// 百林康源分销员 市
	City string `json:"city"`
	// 百林康源分销员 企业市
	EnterpriseCity string `json:"enterprise_city"`
	// 百林康源分销员 区
	District string `json:"district"`

	// 百林康源分销员 详细地址
	Address string `json:"address"`

	// 百林康源分销员 身份
	Professional string `json:"professional"`
	// 百林康源分销员 擅长
	Specialize  string `json:"specialize"`
	ApproveTime string `json:"approve_time"`
	//类型   1：体系内   0：体系外
	InSystem string `json:"in_system"`
	//兽医资格证编号
	VeterinaryCode string `json:"veterinary_code"`

	// 来源类型：0-默认百林康源，1-宠利扫
	SourceType int `json:"source_type"`

	// 主体数据来源：0-润合云店，1-润合SAAS，2-百林康源，3-宠利扫
	OrgDataSource int `json:"org_data_source"`
}

// 分销员角色变更
type DisRoleChangeReq struct {
	//企业ID
	EnterpriseId string `json:"enterprise_id" validate:"required"`
	//分销员id
	Id int `json:"id" validate:"required"`
	//分销员角色 0-初始值 1-老板 2-店员
	DisRole int `json:"dis_role" validate:"required"`
	//统一社会信用代码
	SocialCreditCode string `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	//统一社会信用代码图片
	SocialCodeImage string `json:"social_code_image" xorm:"default '' comment('统一社会信用代码图片') VARCHAR(255) 'social_code_image'"`
	//身份证号码
	IdCard string `json:"id_card" xorm:"not null default '' comment('身份证号码') VARCHAR(100) 'id_card'"`
	//身份证正面
	IdcardFront string `json:"idcard_front" xorm:"not null default '' comment('分销员身份证正面') VARCHAR(255) 'idcard_front'"`
	//身份证反面
	IdcardReverse string `json:"idcard_reverse" xorm:"not null default '' comment('分销员身份证反面') VARCHAR(255) 'idcard_reverse'"`
	//主体id
	OrgId int `json:"org_id"`
}

type DisChangeHospitalReq struct {
	//分销员id
	Id int `json:"id" validate:"required"`
	//医院名称
	HospitalName string `json:"hospital_name"`
	//省份
	Province string `json:"province"`
	//城市
	City string `json:"city"`
}

type DisApproveReq struct {
	//分销员id
	Id int `json:"id" validate:"required"`
	//认证状态： 0-初始值， 1-待审核 2-审核通过 3-审核失败
	ApproveState int `json:"approve_state"`
	//审核拒绝的原因
	Reason string `json:"reason"`
}

// ocr识别结果
type OcrPredictionResult struct {
	ErrNo   int               `json:"err_no"`
	ErrMsg  string            `json:"err_msg"`
	Key     []string          `json:"key"`
	Value   map[string]string `json:"value"`
	Tensors []interface{}     `json:"tensors"`
	Orgid   string            `json:"orgid"`
	RegId   string            `json:"reg_id"`
}

type ChangeSalespersonReq struct {
	//分销员id
	Id int `json:"id" validate:"required"`
	//主体id
	OrgId int `json:"org_id"`
	//旧的拓客业务员ID
	OldSalespersonId string `json:"old_salesperson_id" validate:"required"`
	//新的拓客业务员ID
	SalespersonId string `json:"salesperson_id" validate:"required"`
	//旧的拓客业务员名称
	OldSalespersonName string `json:"old_salesperson_name" validate:"required"`
	//新的拓客业务员名称
	SalespersonName string `json:"salesperson_name" validate:"required"`
}

// 同步分销员
type SyncDistributor struct {
	Id              int    `json:"id" xorm:"pk autoincr not null comment('分销员ID') INT 'id'"`
	OrgId           int    `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	EncryptMobile   string `json:"encrypt_mobile" xorm:"default '' comment('加密手机号') VARCHAR(20) 'encrypt_mobile'"`
	VeterinaryCode  string `json:"veterinary_code" xorm:"default '' comment('兽医资格证编号') VARCHAR(100) 'veterinary_code'"`
	InSystem        int8   `json:"in_system" xorm:"default 0 comment('类型   1：体系内   0：体系外') TINYINT 'in_system'"`
	SocialCodeImage string `json:"social_code_image" xorm:"default '' comment('统一社会信用代码图片') VARCHAR(255) 'social_code_image'"`
	//分销员真实姓名
	RealName string `json:"real_name" xorm:"default '' comment('分销员姓名') VARCHAR(50) 'real_name'"`
	//统一社会信用代码
	SocialCreditCode string `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	// 分销员角色
	DisRole int `json:"dis_role" xorm:"default 0 comment('分销员角色') INT 'dis_role'"`
}

// DistributorEnterpriseListReq 获取分销员关联企业列表请求
type DistributorEnterpriseListReq struct {
	OrgId    int `json:"org_id"`    // 主体ID
	MemberId int `json:"member_id"` // 会员ID
}

// DistributorEnterpriseInfo 分销员关联企业信息
type DistributorEnterpriseInfo struct {
	Id             int    `json:"id"`              // 分销员ID
	OrgId          int    `json:"org_id"`          // 主体ID
	ShopId         int    `json:"shop_id"`         // 店铺ID
	IsDefault      int8   `json:"is_default"`      // 是否默认
	EnterpriseName string `json:"enterprise_name"` // 企业名称
}

// UpdateDefaultEnterpriseReq 更新默认企业请求
type UpdateDefaultEnterpriseReq struct {
	DisId int `json:"dis_id" binding:"required"` // 分销员ID
}

type GetEnterprisesByPhoneReq struct {
	Mobile string `json:"mobile" validate:"required" label:"手机号"`
}

type GetEnterpriseInfoRes struct {
	viewmodel.BaseHttpResponse
	Data []ScrmEnterpriseInfo `json:"data"`
}

type ScrmEnterpriseInfo struct {
	Province         string `json:"province"`           // 省
	City             string `json:"city"`               // 市
	District         string `json:"district"`           // 区
	Address          string `json:"address"`            // 详细地址
	EnterpriseId     int64  `json:"enterprise_id"`      // 企业ID
	EnterpriseName   string `json:"enterprise_name"`    // 企业名称
	SocialCreditCode string `json:"social_credit_code"` // 统一社会信用代码
}
