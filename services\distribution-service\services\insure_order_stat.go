package services

import (
	"eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"strings"
	"time"
)

type InsureOrderStatService struct {
	common.BaseService
}

func (s InsureOrderStatService) InsureOrderStatRun(startTime, endTime string) {
	startDay, err := time.Parse(utils.DateLayout, startTime)
	if err != nil {
		return
	}
	endDay, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return
	}
	for i := startDay; i.Before(endDay) || i.Equal(endDay); i = i.AddDate(0, 0, 1) {
		// 跑一天的数据
		s.InsureOrderStat(i, i)

		// 检测是不是周末，跑一周的数据
		weekday := i.Weekday()
		if weekday == time.Sunday {
			startDate := i.AddDate(0, 0, -6)
			s.InsureOrderStat(startDate, i)
		}

		// 检测是不是月末，跑一月的数据
		tomorrow := i.AddDate(0, 0, 1)
		if tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(0, -1, 0)
			s.InsureOrderStat(startDate, i)
		}

		// 检测是不是年末，跑一年的数据
		if tomorrow.Month() == 1 && tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(-1, 0, 0)
			s.InsureOrderStat(startDate, i)
		}
	}
}

func (s InsureOrderStatService) InsureOrderDailyStatCronJob() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock)

	// 计算昨天时间范围
	today := time.Now().Truncate(time.Hour * 24)
	endDate := today.AddDate(0, 0, -1)
	startDate := today.AddDate(0, 0, -1)
	s.InsureOrderStat(startDate, endDate)
}

func (s InsureOrderStatService) InsureOrderWeekStatCronJob() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock)

	// 计算上周时间范围
	today := time.Now().Truncate(time.Hour * 24)
	endDate := today.AddDate(0, 0, -1)
	startDate := today.AddDate(0, 0, -7)
	s.InsureOrderStat(startDate, endDate)
}

func (s InsureOrderStatService) InsureOrderMonthStatCronJob() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock)

	// 计算上月时间范围
	today := time.Now().Truncate(time.Hour * 24)
	endDate := today.AddDate(0, 0, -1)
	startDate := today.AddDate(0, -1, 0)
	s.InsureOrderStat(startDate, endDate)
}

func (s InsureOrderStatService) InsureOrderYearStatCronJob() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.StatsInsureOrderDataLock)

	// 计算去年时间范围
	today := time.Now().Truncate(time.Hour * 24)
	endDate := today.AddDate(0, 0, -1)
	startDate := endDate.AddDate(-1, 0, 0)
	s.InsureOrderStat(startDate, endDate)
}

func (s InsureOrderStatService) InsureOrderStat(startTime, endTime time.Time) (err error) {
	// 凌晨执行的定时任务，获取到昨天的时间范围
	if startTime.IsZero() {
		log.Errorf("InsureOrderStat，开始时间不能为空")
		return errors.New("InsureOrderStat参数异常，开始时间为空")
	}
	if endTime.IsZero() {
		log.Errorf("InsureOrderStat，结束时间不能为空")
		return errors.New("InsureOrderStat参数异常，结束时间为空")
	}

	// 统计数据
	startDate := startTime.Format(utils.DateLayout)
	endDate := endTime.Format(utils.DateLayout)
	realEndDate := endTime.AddDate(0, 0, 1).Format(utils.DateLayout)
	log.Infof("InsureOrderStat，开始统计保险订单数据，时间范围：[startTime:%s,endTime:%s,realEndDate:%s)", startDate, endDate, realEndDate)
	var statsShopDaily = make(map[int]distribution_po.StatsShopDistributorDaily)
	var statsSalespersonDaily = make(map[int64]distribution_po.StatsSalespersonDaily)

	// 统计下单相关数据
	err = s.statOrder(startDate, realEndDate, statsShopDaily)
	if err != nil {
		log.Error("统计保险订单数据失败", err)
	}

	// 统计成交相关数据
	err = s.statPayOrder(startDate, realEndDate, statsShopDaily, statsSalespersonDaily)
	if err != nil {
		log.Error("统计已支付保险订单数据失败", err)
	}

	// 统计取消相关数据
	err = s.statCancelOrder(startDate, realEndDate, statsShopDaily)
	if err != nil {
		log.Error("统计取消保险订单数据失败", err)
	}

	// 统计退款相关数据
	err = s.statRefundOrder(startDate, realEndDate, statsShopDaily)
	if err != nil {
		log.Error("统计退款保险订单数据失败", err)
	}

	// 写入数据库
	s.saveStatsShopDaily(startDate, endDate, statsShopDaily)
	s.saveStatsSalespersonDaily(startDate, endDate, statsSalespersonDaily)
	return nil
}

// 统计下单相关数据
func (s InsureOrderStatService) statOrder(startTime, endTime string, statDisDaily map[int]distribution_po.StatsShopDistributorDaily) error {
	// 检查是否存在数据，如果存在开始统计
	s.Begin()
	defer s.Close()
	session := s.Session
	list := make([]vo.PiPolicyOrderDto, 0)
	err := session.Select("poi.id,poi.del_flag,poi.order_no,poi.dis_id,poi.dis_id,poi.shop_id,poi.dis_type,poi.user_id,"+
		"pipi.real_premium_amt,"+
		"s.enterprise_id").
		Table("insurance_business.pi_order_info").Alias("poi").
		Join("LEFT", "insurance_business.pi_insure_policy_info pipi", "pipi.order_no = poi.order_no").
		Join("LEFT", "shop s", "s.id = poi.shop_id AND poi.shop_id > 0").
		Where("poi.order_create_time >= ? and poi.order_create_time < ?", startTime, endTime).Find(&list)
	if err != nil {
		return err
	}
	if len(list) == 0 {
		log.Error("没有找到数据，请检查数据是否正确", err)
		return nil
	}

	disUserIdMap := make(map[string]struct{})
	for _, order := range list {
		// 将订单划分为分销订单和非分销订单
		var disId, statType int
		if order.DisId == 0 {
			disId = 0
			statType = 3
		} else {
			disId = order.DisId
			statType = 4
		}

		daily, has := statDisDaily[disId]
		if !has {
			daily = distribution_po.StatsShopDistributorDaily{}
		}
		daily.ShopId = order.ShopId
		daily.DisId = disId
		daily.EnterpriseId = order.EnterpriseId
		daily.Type = statType

		// 分销来源：1-分销链接，2-分销海报
		if order.DisType == 1 {
			daily.LinkClickCount++
		} else if order.DisType == 2 {
			daily.PosterScanCount++
		}

		// 统计以下字段：客户数 订单数 订单金额 订单商品数量
		if _, ok := disUserIdMap[fmt.Sprintf("%d-%s", order.DisId, order.UserId)]; !ok {
			disUserIdMap[fmt.Sprintf("%d-%s", order.DisId, order.UserId)] = struct{}{}
			daily.OrderCustomerCount++
		}
		daily.OrderCount++
		daily.OrderAmount = daily.OrderAmount + utils.Yuan2Fen(order.RealPremiumAmt)
		daily.OrderProductCount++
		statDisDaily[disId] = daily
	}

	// 客单价
	for disId, daily := range statDisDaily {
		if daily.OrderCustomerCount > 0 {
			daily.OrderCustomerPrice = daily.OrderAmount / daily.OrderCustomerCount
		}
		statDisDaily[disId] = daily
	}

	return nil
}

// 统计成交相关数据
func (s InsureOrderStatService) statPayOrder(startTime, endTime string, statDisDaily map[int]distribution_po.StatsShopDistributorDaily, statsSalespersonDaily map[int64]distribution_po.StatsSalespersonDaily) error {
	// 检查是否存在数据，如果存在开始统计
	s.Begin()
	defer s.Close()
	session := s.Session
	list := make([]vo.PiPolicyOrderDto, 0)
	err := session.Select("poi.id,poi.del_flag,poi.order_no,poi.dis_id,poi.shop_id,poi.dis_type,poi.salesman_id,poi.dis_rate,poi.user_id,"+
		"pipi.real_premium_amt,"+
		"s.enterprise_id,"+
		"GROUP_CONCAT(pos.salesperson_id) AS salesperson_ids").
		Table("insurance_business.pi_order_info").Alias("poi").
		Join("LEFT", "insurance_business.pi_insure_policy_info pipi", "pipi.order_no = poi.order_no").
		Join("LEFT", "shop s", "s.id = poi.shop_id AND poi.shop_id > 0").
		Join("LEFT", "insurance_business.pi_order_salesperson pos", "pos.order_no = poi.order_no").
		Where("poi.pay_time >= ? and poi.pay_time < ?", startTime, endTime).And("poi.pay_status=2").
		GroupBy("poi.order_no").
		Find(&list)
	if err != nil {
		return err
	}
	if len(list) == 0 {
		log.Error("没有找到统计成交相关数据，请检查数据是否正确", err)
		return nil
	}

	// 企业id去重、shop_id去重
	entIdMap := make(map[int64]struct{})
	disIdMap := make(map[int]struct{})
	disUserIdMap := make(map[string]struct{})
	for _, order := range list {
		if order.EnterpriseId > 0 {
			entIdMap[order.EnterpriseId] = struct{}{}
		}
		if order.DisId > 0 {
			disIdMap[order.DisId] = struct{}{}
		}

		// 组装分销店铺成交统计数据
		packStatsShopDaily(order, statDisDaily, disUserIdMap)

		// 组装业务员保险成交统计数据
		s.PackStatsSalespersonDaily(order, statsSalespersonDaily)
	}

	s.UpdStatsEntDaily(startTime, endTime, entIdMap, disIdMap)

	// 客单价
	for disId, daily := range statDisDaily {
		if daily.TransCustomerCount > 0 {
			daily.TransCustomerPrice = daily.TransAmount / daily.TransCustomerCount
		}
		statDisDaily[disId] = daily
	}

	return nil
}

func (s InsureOrderStatService) UpdStatsEntDaily(startDate, endDate string, entIdMap map[int64]struct{}, disIdMap map[int]struct{}) {
	s.Begin()
	defer s.Close()
	session := s.Session

	var id int64
	endTime, err := time.Parse(utils.DateLayout, endDate)
	if err != nil {
		log.Errorf("时间格式转换失败，startDate=%s, endDate=%s, err=%v", startDate, endDate, err)
		return
	}
	endDate = endTime.AddDate(0, 0, -1).Format(utils.DateLayout)
	_, err = session.Select("id").Table("stats_enterprise_daily").Where("stat_date=? AND end_date=?", startDate, endDate).Get(&id)
	if err != nil || id == 0 {
		log.Errorf("没有找到分销企业数据统计记录，请检查数据是否正确，startDate=%s，endDate=%s, err=%v", startDate, endDate, err)
		return
	}

	entCount := len(entIdMap)
	disCount := len(disIdMap)
	updDaily := distribution_po.StatsEnterpriseDaily{
		InsTotalTransEnterprise:  entCount,
		InsTotalTransDistributor: disCount,
	}
	_, err = session.ID(id).Update(updDaily)
	if err != nil {
		log.Errorf("更新分销企业数据统计记录失败，id=%d，startDate=%s, endDate=%s, entCount=%d, disCount=%d, err=%v", id, startDate, endDate, entCount, disCount, err)
	}
}

func packStatsShopDaily(order vo.PiPolicyOrderDto, statDisDaily map[int]distribution_po.StatsShopDistributorDaily, disUserIdMap map[string]struct{}) {
	// 将订单划分为分销订单和非分销订单
	var disId, statType int
	if order.DisId == 0 {
		disId = 0
		statType = 3
	} else {
		disId = order.DisId
		statType = 4
	}

	daily, has := statDisDaily[disId]
	if !has {
		daily = distribution_po.StatsShopDistributorDaily{}
	}
	daily.ShopId = order.ShopId
	daily.DisId = disId
	daily.EnterpriseId = order.EnterpriseId
	daily.Type = statType

	// 分销来源：1-分销链接，2-分销海报
	if order.DisType == 1 {
		daily.LinkTransCount++
		daily.LinkTransAmount = daily.LinkTransAmount + utils.Yuan2Fen(order.RealPremiumAmt)
	} else if order.DisType == 2 {
		daily.PosterTransCount++
		daily.PosterTransAmount = daily.PosterTransAmount + utils.Yuan2Fen(order.RealPremiumAmt)
	}

	// 统计以下字段：客户数 订单数 订单金额 订单商品数量
	if _, ok := disUserIdMap[fmt.Sprintf("%d-%s", order.DisId, order.UserId)]; !ok {
		disUserIdMap[fmt.Sprintf("%d-%s", order.DisId, order.UserId)] = struct{}{}
		daily.TransCustomerCount++
	}
	daily.TransCount++
	daily.TransAmount = daily.TransAmount + utils.Yuan2Fen(order.RealPremiumAmt)
	daily.TransProductCount++
	statDisDaily[disId] = daily
}

func (s InsureOrderStatService) PackStatsSalespersonDaily(order vo.PiPolicyOrderDto, statsSalespersonDaily map[int64]distribution_po.StatsSalespersonDaily) {
	// 将订单划分为分销订单和非分销订单
	if order.DisId == 0 {
		return
	}
	salespersonIds := order.SalespersonIds
	if len(salespersonIds) == 0 {
		return
	}

	s.Begin()
	defer s.Close()
	session := s.Session
	var salespersons []distribution_po.ScrmSalesperson
	err := session.Select("id,org_name,name").Table("scrm_salesperson").In("id", strings.Split(salespersonIds, ",")).Find(&salespersons)
	if err != nil {
		log.Errorf("获取业务员信息失败, salespersonIds=%s, err=%v", salespersonIds, err)
		return
	}

	for _, salesperson := range salespersons {
		salespersonId := salesperson.Id
		daily, has := statsSalespersonDaily[salespersonId]
		if !has {
			daily = distribution_po.StatsSalespersonDaily{}
		}
		daily.SalesmanId = cast.ToInt(salesperson.Id)
		daily.SalesmanName = salesperson.Name
		daily.Organization = salesperson.OrgName

		// 分销保险成交企业数 分销保险成交单数 分销保险成交金额
		var insuranceEnterpriseCount int
		_, err = session.Select("COUNT(DISTINCT sesb.enterprise_id)").
			Table("scrm_enterprise_salesperson_bind sesb").
			Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
			Where("sesb.salesperson_id=? AND s.id=?", salespersonId, order.ShopId).
			Get(&insuranceEnterpriseCount)
		if err != nil {
			log.Errorf("PackStatsSalespersonDaily，查询分销保险成交企业数异常")
			return
		}
		daily.InsuranceEnterpriseCount = daily.InsuranceEnterpriseCount + insuranceEnterpriseCount

		daily.InsuranceTransCount++
		daily.InsuranceTransAmount = daily.InsuranceTransAmount + utils.Yuan2Fen(order.RealPremiumAmt)
		daily.InsuranceCommission = daily.InsuranceCommission + utils.Yuan2Fen(order.RealPremiumAmt*order.DisRate/100)

		statsSalespersonDaily[salespersonId] = daily
	}
}

// 统计下单相关数据
func (s InsureOrderStatService) statCancelOrder(startTime, endTime string, statDisDaily map[int]distribution_po.StatsShopDistributorDaily) error {
	// 检查是否存在数据，如果存在开始统计
	s.Begin()
	defer s.Close()
	session := s.Session
	list := make([]vo.PiPolicyOrderDto, 0)
	err := session.Select("poi.id,poi.del_flag,poi.order_no,poi.dis_id,poi.dis_id,poi.shop_id,"+
		"pipi.real_premium_amt,"+
		"s.enterprise_id").
		Table("insurance_business.pi_order_info").Alias("poi").
		Join("LEFT", "insurance_business.pi_insure_policy_info pipi", "pipi.order_no = poi.order_no").
		Join("LEFT", "shop s", "s.id = poi.shop_id AND poi.shop_id > 0").
		Where("poi.cancel_time >= ? and poi.cancel_time < ?", startTime, endTime).And("poi.order_status=7").Find(&list)
	if err != nil {
		return err
	}
	if len(list) == 0 {
		log.Error("没有找到统计成交相关数据，请检查数据是否正确", err)
		return nil
	}

	for _, order := range list {
		// 将订单划分为分销订单和非分销订单
		var disId, statType int
		if order.DisId == 0 {
			disId = 0
			statType = 3
		} else {
			disId = order.DisId
			statType = 4
		}

		daily, has := statDisDaily[disId]
		if !has {
			daily = distribution_po.StatsShopDistributorDaily{}
		}
		daily.ShopId = order.ShopId
		daily.DisId = disId
		daily.EnterpriseId = order.EnterpriseId
		daily.Type = statType

		// 统计以下字段：客户数 订单数 订单金额 订单商品数量
		daily.CancelCount++
		daily.CancelAmount = daily.CancelAmount + utils.Yuan2Fen(order.RealPremiumAmt)
		daily.CancelProductCount++
		statDisDaily[disId] = daily
	}

	return nil
}

// 统计下单相关数据
func (s InsureOrderStatService) statRefundOrder(startTime, endTime string, statDisDaily map[int]distribution_po.StatsShopDistributorDaily) error {
	// 检查是否存在数据，如果存在开始统计
	s.Begin()
	defer s.Close()
	session := s.Session
	list := make([]vo.PiPolicyOrderDto, 0)
	err := session.Select("poi.id,poi.del_flag,poi.order_no,poi.dis_id,poi.dis_id,poi.shop_id,"+
		"pipi.real_premium_amt,"+
		"s.enterprise_id").
		Table("insurance_business.pi_order_info").Alias("poi").
		Join("LEFT", "insurance_business.pi_insure_policy_info pipi", "pipi.order_no = poi.order_no").
		Join("LEFT", "shop s", "s.id = poi.shop_id AND poi.shop_id > 0").
		Where("poi.refund_time >= ? AND poi.refund_time < ?", startTime, endTime).And("poi.order_status=6").Find(&list)
	if err != nil {
		return err
	}
	if len(list) == 0 {
		log.Error("没有找到统计成交相关数据，请检查数据是否正确", err)
		return nil
	}

	for _, order := range list {
		// 将订单划分为分销订单和非分销订单
		var disId, statType int
		if order.DisId == 0 {
			disId = 0
			statType = 3
		} else {
			disId = order.DisId
			statType = 4
		}

		daily, has := statDisDaily[disId]
		if !has {
			daily = distribution_po.StatsShopDistributorDaily{}
		}
		daily.ShopId = order.ShopId
		daily.DisId = disId
		daily.EnterpriseId = order.EnterpriseId
		daily.Type = statType

		// 统计以下字段：客户数 订单数 订单金额 订单商品数量
		daily.RefundCount++
		daily.RefundAmount = daily.RefundAmount + utils.Yuan2Fen(order.RealPremiumAmt)
		daily.RefundProductCount++
		statDisDaily[disId] = daily
	}

	return nil
}

func (s InsureOrderStatService) saveStatsShopDaily(startDate, endDate string, statsShopDaily map[int]distribution_po.StatsShopDistributorDaily) error {
	if len(statsShopDaily) == 0 {
		log.Info("没有数据需要写入数据库")
		return nil
	}

	service := StatsShopDistributorDailyService{}

	s.Begin()
	defer s.Close()
	session := s.Session

	for disId, daily := range statsShopDaily {
		daily.StatDate = startDate
		daily.EndDate = endDate
		exist, err := service.InsertAndGet(disId, daily)
		if err != nil {
			log.Errorf("获取到分销员每日统计数据信息失败，disId=%d, statDate=%s, err=%v, ", disId, startDate, err)
			continue
		}
		// 获取到分销员信息
		if exist.Id > 0 {
			session.ID(exist.Id).Update(daily)
		}
	}

	return nil
}

func (s InsureOrderStatService) saveStatsSalespersonDaily(startDate, endDate string, statsSalespersonDaily map[int64]distribution_po.StatsSalespersonDaily) error {
	if len(statsSalespersonDaily) == 0 {
		log.Info("没有数据需要写入数据库")
		return nil
	}

	s.Begin()
	defer s.Close()
	session := s.Session

	salespersonService := StatsSalespersonService{}
	for salespersonId, daily := range statsSalespersonDaily {
		daily.StatDate = startDate
		daily.EndDate = endDate
		exist, err := salespersonService.InsertAndGet(salespersonId, daily)
		if err != nil {
			log.Errorf("获取到分销员每日统计数据信息失败，salespersonId=%d, statDate=%s, endDate=%s, err=%v, ", salespersonId, startDate, endDate, err)
			continue
		}
		// 获取到分销员信息
		if exist.Id > 0 {
			session.ID(exist.Id).Update(daily)
		}
	}

	return nil
}
