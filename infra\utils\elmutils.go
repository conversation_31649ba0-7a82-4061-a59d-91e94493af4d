package utils

import (
	"sort"
	"strings"
)

// 下行返回结果的签名
func RequestSign(data map[string]interface{}) string {
	arr := make(map[string]string)
	for k, v := range data {
		if k == "body" {
			arr[k] = JsonEncode(v)
		} else {
			arr[k] = v.(string)
		}
	}
	arr["secret"] = ElmSecret
	sign := ElmSign(arr)
	return sign
}

func ElmSign(arr map[string]string) string {
	s := make([]string, len(arr))
	for k := range arr {
		s = append(s, k)
	}
	//进行排序
	sort.Strings(s)
	str := ""
	for _, v := range s {
		if v == "" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + arr[v]
	}
	return strings.ToUpper(GetMd5(str))
}
