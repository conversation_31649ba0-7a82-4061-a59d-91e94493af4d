package events

import (
	"time"
)

// Event 基础事件接口
type Event interface {
	GetEventType() string
	GetAggregateID() string
	GetTimestamp() time.Time
	GetVersion() int
}

// BaseEvent 基础事件结构
type BaseEvent struct {
	EventType   string    `json:"event_type"`
	AggregateID string    `json:"aggregate_id"`
	Timestamp   time.Time `json:"timestamp"`
	Version     int       `json:"version"`
}

func (e BaseEvent) GetEventType() string {
	return e.EventType
}

func (e BaseEvent) GetAggregateID() string {
	return e.AggregateID
}

func (e BaseEvent) GetTimestamp() time.Time {
	return e.Timestamp
}

func (e BaseEvent) GetVersion() int {
	return e.Version
}

// EventStore 事件存储接口
type EventStore interface {
	SaveEvent(event Event) error
	GetEvents(aggregateID string) ([]Event, error)
}
