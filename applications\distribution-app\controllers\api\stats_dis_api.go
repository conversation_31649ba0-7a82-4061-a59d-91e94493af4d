// 分销员中心-数据中心
package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	"eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// StatsDisCenterIndex
// @Summary 分销员中心-数据中心-首页
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Param StatsDisCenterIndexReq query distribution_vo.StatsDisCenterIndexReq true " "
// @Success 200 {object} distribution_vo.StatsDisCenterIndexResp
// @Failure 400 {object} distribution_vo.StatsDisCenterIndexResp
// @Router /api/stats/dis-center/index [get]
func StatsDisCenterIndex(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.StatsDisCenterIndexResp{}
	resp.Code = 400

	service := services.StatsShopService{}
	req, err := utils.Bind[distribution_vo.StatsDisCenterIndexReq](request)
	if err != nil {
		log.Error("获取分销员中心-数据中心-首页数据，参数解析失败：err=" + err.Error())
		resp.Message = "参数解析失败：" + err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取分销员中心-数据中心-首页数据-鉴权失败-错误为%s", err.Error())
		resp.Message = "鉴权失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.OrgId = cast.ToInt64(request.Header.Get("org_id"))
	resp.Data, err = service.StatsDisCenterIndex(req)
	if err != nil {
		log.Error("获取分销员中心-数据中心-首页数据失败:err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisCenterOverview
// @Summary 分销员中心-数据中心-数据概览
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Param StatsDisCenterOverviewReq query distribution_vo.StatsDisCenterOverviewReq true " "
// @Success 200 {object} distribution_vo.StatsDisCenterOverviewResp
// @Failure 400 {object} distribution_vo.StatsDisCenterOverviewResp
// @Router /api/stats/dis-center/overview [get]
func StatsDisCenterOverview(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.StatsDisCenterOverviewResp{}
	resp.Code = 400

	service := services.StatsShopService{}
	req, err := utils.Bind[distribution_vo.StatsDisCenterOverviewReq](request)
	if err != nil {
		log.Error("获取分销员中心-数据中心-数据概览数据，参数解析失败：err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取分销员中心-数据中心-数据概览数据-鉴权失败-错误为%s", err.Error())
		resp.Message = "鉴权失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.OrgId = cast.ToInt64(request.Header.Get("org_id"))
	resp.Data, err = service.StatsDisCenterOverview(req)
	if err != nil {
		log.Error("获取分销员中心-数据中心-数据概览数据失败:err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisCenterGraph
// @Summary 分销员中心-数据中心-数据概览-曲线图
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Param StatsDisCenterGraphReq query distribution_vo.StatsDisCenterGraphReq true " "
// @Success 200 {object} distribution_vo.StatsDisCenterGraphResp
// @Failure 400 {object} distribution_vo.StatsDisCenterGraphResp
// @Router /api/stats/dis-center/graph [get]
func StatsDisCenterGraph(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.StatsDisCenterGraphResp{}
	resp.Code = 400

	service := services.StatsShopService{}
	req, err := utils.Bind[distribution_vo.StatsDisCenterGraphReq](request)
	if err != nil {
		log.Error("获取分销员中心-数据中心-数据概览-曲线图数据，参数解析失败：err=" + err.Error())
		resp.Message = fmt.Sprintf("参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取分销员中心-数据中心-数据概览-曲线图数据-鉴权失败-错误为%s", err.Error())
		resp.Message = "鉴权失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.OrgId = cast.ToInt64(request.Header.Get("org_id"))
	resp.Data, err = service.StatsDisCenterGraph(req)
	if err != nil {
		log.Error("获取分销员中心-数据中心-数据概览-曲线图数据失败:err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisCenterTotal
// @Summary 分销员中心-数据中心-数据概览-累计数据
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Param StatsDisCenterTotalReq query distribution_vo.StatsDisCenterTotalReq true " "
// @Success 200 {object} distribution_vo.StatsDisCenterTotalResp
// @Failure 400 {object} distribution_vo.StatsDisCenterTotalResp
// @Router /api/stats/dis-center/total [get]
func StatsDisCenterTotal(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.StatsDisCenterTotalResp{}
	resp.Code = 400

	service := services.StatsShopService{}
	req, err := utils.Bind[distribution_vo.StatsDisCenterTotalReq](request)
	if err != nil {
		log.Error("获取分销员中心-数据中心-数据概览-累计数据，参数解析失败：err=" + err.Error())
		resp.Message = "参数解析失败：" + err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取分销员中心-数据中心-数据概览-累计数据-鉴权失败-错误为%s", err.Error())
		resp.Message = "鉴权失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.OrgId = cast.ToInt64(request.Header.Get("org_id"))
	resp.Data, err = service.StatsDisCenterTotal(req)
	if err != nil {
		log.Error("获取分销员中心-数据中心-数据概览-累计数据失败:err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisCenterDistributor
// @Summary 分销员中心-数据中心-分销员数据
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Param StatsDisCenterDistributorReq query distribution_vo.StatsDisCenterDistributorReq true " "
// @Success 200 {object} distribution_vo.StatsDisCenterDistributorResp
// @Failure 400 {object} distribution_vo.StatsDisCenterDistributorResp
// @Router /api/stats/dis-center/distributor [get]
func StatsDisCenterDistributor(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.StatsDisCenterDistributorResp{}
	resp.Code = 400

	service := services.StatsShopService{}
	req, err := utils.Bind[distribution_vo.StatsDisCenterDistributorReq](request)
	if err != nil {
		log.Error("获取分销员中心-数据中心-分销员数据，参数解析失败：err=" + err.Error())
		resp.Message = "参数解析失败：" + err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取分销员中心-数据中心-分销员数据-鉴权失败-错误为%s", err.Error())
		resp.Message = "鉴权失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.OrgId = cast.ToInt64(request.Header.Get("org_id"))
	resp.Data, resp.Total, err = service.StatsDisCenterDistributor(req)

	if err != nil {
		log.Error("获取分销员中心-数据中心-分销员数据失败：err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisCenterOverview
// @Summary 分销员中心-数据中心-商品分析
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Param StatsDisCenterGoodsReq query distribution_vo.StatsDisCenterGoodsReq true " "
// @Success 200 {object} distribution_vo.StatsDisCenterGoodsResp
// @Failure 400 {object} distribution_vo.StatsDisCenterGoodsResp
// @Router /api/stats/dis-center/goods [get]
func StatsDisCenterGoods(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.StatsDisCenterGoodsResp{}
	resp.Code = 400

	service := services.StatsShopService{}
	req, err := utils.Bind[distribution_vo.StatsDisCenterGoodsReq](request)
	if err != nil {
		log.Error("获取分销员中心-数据中心-商品分析，参数解析失败：err=" + err.Error())
		resp.Message = "参数解析失败：" + err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取分销员中心-数据中心-数据概览数据-鉴权失败-错误为%s", err.Error())
		resp.Message = "鉴权失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.OrgId = cast.ToInt64(request.Header.Get("org_id"))
	if resp.Data, resp.Total, err = service.StatsDisCenterGoods(req); err != nil {
		log.Error("获取分销员中心-数据中心-商品分析失败：err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDoctorRealID
// @Summary SCRM-医生销售看板-医生使用百林康源小程序分布
// @Tags 小程序接口-数据统计-分销员中心
// @Accept plain
// @Produce json
// @Success 200 {object} distribution_vo.StatsDoctorResponse
// @Failure 400 {object} distribution_vo.StatsDoctorResponse
// @Router /api/stats/blky/doctors-real-id [get]
func StatsDoctorRealID(writer http.ResponseWriter, request *http.Request) {
	// @Param StatsDisCenterGoodsReq query distribution_vo.StatsDisCenterGoodsReq true " "
	resp := distribution_vo.StatsDoctorResponse{}
	resp.Code = 400
	service := services.StatsDistributorService{}
	resp.Data = service.StatsDoctor()
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
