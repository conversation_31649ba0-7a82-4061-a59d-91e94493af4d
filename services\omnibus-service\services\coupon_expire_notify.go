package services

import (
	marketing_po "eShop/domain/marketing-po"
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"fmt"
	"time"

	"github.com/spf13/cast"

	"xorm.io/xorm"
)

// NotifyExpireCoupons 检查并通知即将过期的优惠券
func (s *SmsService) NotifyExpireCoupons() error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 获取所有启用了优惠券过期通知的店铺配置
	var configs []omnibus_po.SmsConfig
	err := session.Where("config_type = ? AND is_enabled = ?", "coupon_expire", true).Find(&configs)
	if err != nil {
		log.Error("获取优惠券过期通知配置失败:", err)
		return err
	}

	if len(configs) == 0 {
		log.Info("没有店铺启用优惠券过期通知")
		return nil
	}

	// 2. 遍历每个店铺配置，处理即将过期的优惠券
	for _, config := range configs {
		// 验证配置有效性：ExpireDays必须在1-7之间
		if config.ExpireDays < 1 || config.ExpireDays > 7 {
			continue // 跳过无效的过期天数配置
		}

		// 计算过期日期范围
		now := time.Now()
		targetDate := now.AddDate(0, 0, config.ExpireDays)
		// 设置时间范围为目标日期的0点到23:59:59
		startTime := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())
		endTime := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 23, 59, 59, 0, targetDate.Location())

		// 查询该店铺下即将过期的优惠券
		err = s.processStoreExpireCoupons(session, config, startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
		if err != nil {
			log.Error(fmt.Sprintf("处理店铺[%s]优惠券过期通知失败: %v", config.StoreId, err))
			continue
		}
	}

	return nil
}

// processStoreExpireCoupons 处理单个店铺的即将过期优惠券
func (s *SmsService) processStoreExpireCoupons(session *xorm.Session, config omnibus_po.SmsConfig, startTime, endTime string) error {
	// 1. 查询该店铺下即将过期且未发送过通知的优惠券记录
	var receivers []marketing_po.MarketingCouponReceiver
	err := session.Table(marketing_po.MarketingCouponReceiver{}.TableName()).Where(
		"store_id = ? AND status = ? AND is_deleted = ? AND is_stop = ? AND expire_time BETWEEN ? AND ? AND sms_notify_status != ?",
		config.StoreId,
		marketing_po.StatusUnused, // 未使用状态
		0,                         // 未删除
		0,                         // 未停止
		startTime,
		endTime,
		marketing_po.SmsNotifyStatusSent, // 排除已发送通知的记录
	).Find(&receivers)

	if err != nil {
		return fmt.Errorf("查询即将过期的优惠券失败: %v", err)
	}

	if len(receivers) == 0 {
		log.Info(fmt.Sprintf("店铺[%s]没有%d天后即将过期的优惠券或通知已发送", config.StoreId, config.ExpireDays))
		return nil
	}

	// 2. 获取店铺信息
	var store omnibus_po.Store
	exists, err := session.Where("finance_code = ?", config.StoreId).Get(&store)
	if err != nil || !exists {
		return fmt.Errorf("获取店铺信息失败: %v", err)
	}

	// 3. 按用户分组处理优惠券
	customerCoupons := make(map[int64][]marketing_po.MarketingCouponReceiver)
	for _, receiver := range receivers {
		customerCoupons[receiver.CustomerId] = append(customerCoupons[receiver.CustomerId], receiver)
	}

	// 批量发送成功
	ids := make([]int64, 0)
	// 4. 为每个用户发送通知并更新通知状态
	for customerId, coupons := range customerCoupons {
		// 只取第一张优惠券的手机号
		if len(coupons) == 0 || coupons[0].EncryCustomerMobile == "" {
			continue
		}

		// 解密手机号
		mobile := utils.MobileDecrypt(coupons[0].EncryCustomerMobile)
		if mobile == "" {
			continue
		}

		// 构建短信参数
		expireTimeStr := coupons[0].ExpireTime.Format("2006-01-02")
		params := SmsCouponExpireParams{
			TenantName: store.Name,
			CouponType: "优惠券", // 可以根据实际类型设置
			Content:    fmt.Sprintf("%d张", len(coupons)),
			ExpireTime: expireTimeStr,
		}

		// 转换为JSON
		paramJSON := utils.JsonEncode(params)
		if err != nil {
			log.Error(fmt.Sprintf("构建短信参数失败[客户ID:%d]: %v", customerId, err))
			continue
		}

		// 发送短信
		req := omnibus_vo.SmsSendMessageRequest{
			StoreId:       config.StoreId,
			Mobile:        mobile,
			TemplateCode:  omnibus_po.SmsTemplateCouponExpire,
			TemplateParam: paramJSON,
		}

		err = s.SendMessage(req)
		if err != nil {
			log.Error(fmt.Sprintf("发送优惠券过期通知失败[客户ID:%d]: %v", customerId, err))
			continue
		}

		// 所有发送成功的优惠券记录ID
		ids = append(ids, coupons[0].Id)
	}

	// 更新所有发送成功的优惠券记录状态
	if _, err = session.Table(marketing_po.MarketingCouponReceiver{}.TableName()).
		In("id", ids).
		Update(map[string]interface{}{
			"sms_notify_status": marketing_po.SmsNotifyStatusSent,
		}); err != nil {
		log.Error(fmt.Sprintf("更新优惠券通知状态失败: %v", err))
		return err
	}

	return nil
}

// 储值卡和次卡过期发短信  1储值卡 2次卡
func (s *SmsService) NotifyExpireCards(typeCard int) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	typeStr := "store_card_expire"
	if typeCard == 2 {
		typeStr = "time_card_expire"
	}
	// 1. 获取所有启用了优惠券过期通知的店铺配置
	var configs []omnibus_po.SmsConfig
	err := session.Where("config_type = ? AND is_enabled = ?", typeStr, true).Find(&configs)
	if err != nil {
		log.Error("获取次卡储值卡过期通知配置失败:", err)
		return err
	}

	if len(configs) == 0 {
		log.Info("没有店铺启用次卡储值卡过期通知")
		return nil
	}

	// 2. 遍历每个店铺配置，处理即将过期的优惠券
	for _, config := range configs {
		// 验证配置有效性：ExpireDays必须在1-7之间
		if config.ExpireDays < 1 || config.ExpireDays > 7 {
			continue // 跳过无效的过期天数配置
		}

		// 计算过期日期范围
		now := time.Now()
		targetDate := now.AddDate(0, 0, config.ExpireDays)

		// 设置时间范围为目标日期的0点到23:59:59
		startTime := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())
		endTime := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 23, 59, 59, 0, targetDate.Location())

		// 查询该店铺下即将过期的优惠券
		err = s.processStoreExpireCards(session, config, startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), typeCard)
		if err != nil {
			log.Error(fmt.Sprintf("处理店铺[%s]次卡储值卡过期通知失败: %v", config.StoreId, err))
			continue
		}
	}

	return nil
}

// processStoreExpireCoupons 处理单个店铺的即将过期优惠券
func (s *SmsService) processStoreExpireCards(session *xorm.Session, config omnibus_po.SmsConfig, startTime, endTime string, typeCard int) error {
	// 1. 查询该店铺下即将过期的优惠券记录
	var receivers []marketing_po.MStoreCardRecord
	var receivers1 []marketing_po.MTimeCardRecord
	//储值卡
	if typeCard == 1 {
		err := session.Table(marketing_po.MStoreCardRecord{}.TableName()).Where(
			"tenant_id = ? AND status = ? AND is_deleted = ?  AND expiration_time BETWEEN ? AND ?",
			cast.ToInt64(config.StoreId),
			"IN_USE", // 未使用状态
			0,        // 未删除
			startTime,
			endTime,
		).Find(&receivers)
		if err != nil {
			return fmt.Errorf("查询即将过期的储值卡失败: %v", err)
		}
	} else { //次卡

		err := session.Table(marketing_po.MTimeCardRecord{}.TableName()).Where(
			"tenant_id = ? AND status = ? AND is_deleted = ?  AND expiration_time BETWEEN ? AND ?",
			cast.ToInt64(config.StoreId),
			"IN_USE", // 未使用状态
			0,        // 未删除
			startTime,
			endTime,
		).Find(&receivers1)
		if err != nil {
			return fmt.Errorf("查询即将过期的次卡失败: %v", err)
		}
	}

	if len(receivers) == 0 && len(receivers1) == 0 {
		log.Info(fmt.Sprintf("店铺[%s]没有%d天后即将过期的次卡或储值卡", config.StoreId, config.ExpireDays))
		return nil
	}

	// 2. 获取店铺信息
	var store omnibus_po.Store
	exists, err := session.Where("finance_code = ?", config.StoreId).Get(&store)
	if err != nil || !exists {
		return fmt.Errorf("获取店铺信息失败: %v", err)
	}

	// 4. 为每个用户发送通知
	for _, storeCard := range receivers {

		// 构建短信参数
		params := SmsStoreCardExpireParams{
			TenantName:    store.Name,
			ActivateTime:  storeCard.CreatedTime.Format("2006-01-02"),
			ExpireTime:    storeCard.ExpirationTime.Format("2006-01-02"),
			StoreCardName: storeCard.CardName,
			BalanceAmount: cast.ToString(storeCard.TotalAmount),
		}

		// 转换为JSON
		paramJSON := utils.JsonEncode(params)
		if err != nil {
			log.Error(fmt.Sprintf("构建短信参数失败[客户ID:%s]: %v", storeCard.CustomerMobile, err))
			continue
		}

		// 发送短信
		req := omnibus_vo.SmsSendMessageRequest{
			StoreId:       config.StoreId,
			Mobile:        storeCard.CustomerMobile,
			TemplateCode:  omnibus_po.SmsTemplateStoreCardExpire,
			TemplateParam: paramJSON,
		}

		err = s.SendMessage(req)
		if err != nil {
			log.Error(fmt.Sprintf("发送储值卡过期通知失败[客户ID:%s]: %v", storeCard.CustomerMobile, err))
			continue
		}

		log.Info(fmt.Sprintf("成功发送储值卡过期通知[店铺:%s,客户ID:%s]",
			config.StoreId, storeCard.CustomerMobile))
	}
	// 4. 为每个用户发送通知
	for _, timeCard := range receivers1 {

		// 构建短信参数
		params := SmsTimeCardExpireParams{
			TenantName:   store.Name,
			ActivateTime: timeCard.CreatedTime.Format("2006-01-02"),
			ExpireTime:   timeCard.ExpirationTime.Format("2006-01-02"),
			TimeCardName: timeCard.CardName,
			TotalNum:     cast.ToString(timeCard.TotalNum),
		}

		// 转换为JSON
		paramJSON := utils.JsonEncode(params)
		if err != nil {
			log.Error(fmt.Sprintf("构建短信参数失败[客户ID:%s]: %v", timeCard.CustomerMobile, err))
			continue
		}

		// 发送短信
		req := omnibus_vo.SmsSendMessageRequest{
			StoreId:       config.StoreId,
			Mobile:        timeCard.CustomerMobile,
			TemplateCode:  omnibus_po.SmsTemplateTimeCardExpire,
			TemplateParam: paramJSON,
		}

		err = s.SendMessage(req)
		if err != nil {
			log.Error(fmt.Sprintf("发送次卡过期通知失败[客户ID:%s]: %v", timeCard.CustomerMobile, err))
			continue
		}

		log.Info(fmt.Sprintf("成功发送次卡过期通知[店铺:%s,客户ID:%s]",
			config.StoreId, timeCard.CustomerMobile))
	}

	return nil
}
