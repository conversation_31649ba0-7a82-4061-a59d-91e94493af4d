package viewmodel

// 普通请求必须带的返回结果，加上自己再定义一个结构加上业务数据
type BaseHttpResponse struct {
	// 状态码，200：请求成功，400：请求失败
	Code int `json:"code"`
	// 响应提示信息
	Message string `json:"message"`
}

// 分页请求必须带的返回结果，加上自己再定义一个结构加上业务数据
type BasePageHttpResponse struct {
	// 状态码，200：请求成功，400：请求失败
	Code int `json:"code"`
	// 响应提示信息
	Message string `json:"message"`
	// 总记录大小
	Total int `json:"total"`
}

// 分页集体默认带的数据，统一参数不需要每个结构体去定义
type BasePageHttpRequest struct {
	//页码
	PageIndex int `query:"page_index" json:"page_index" validate:"" label:"页码"`
	//每页数量
	PageSize int `query:"page_size" json:"page_size" validate:"" label:"每页数量"`
}

// 用户端请求的默认信息
type BaseUserRequest struct {
	//分销员id(修改的时候传)
	Id int `json:"id" `
	//所属主体id
	OrgId int `json:"org_id" `
	//手机号
	Mobile string `json:"mobile" `
	//验证码
	Code string `json:"code"  `
	//姓名
	UserName string `json:"user_name" `
	//分销员头像
	HeadImage string `json:"head_image" `
	//获取验证码类型， 1校验旧手机 2校验新手机
	GetType int `json:"get_type"`
}

type ImportResult struct {
	// 200成功，400失败
	Code int32
	// 响应信息
	Message string
	// 成功入库个数
	SuccessNum int32
	// 失败个数
	FailNum int32
	// 错误信息excel链接
	QiniuUrl string
}

type DictDataRes struct {
	BaseHttpResponse
	Data map[string][]DisDataReference `json:"data"`
}

type DisDataReference struct {
	Id   uint   `json:"id"`
	Type int    `json:"type"` //'1-身份 2-擅长'
	Name string `json:"name"`
}

type EmptyReq struct {
}

type HttpResponse struct {
	//业务状态，200：请求成功，400：请求失败
	Code int32 `json:"code"`
	//code=400时，返回错误信息
	Message string `json:"message"`
	//业务数据
	Data interface{} `json:"data"`
}
