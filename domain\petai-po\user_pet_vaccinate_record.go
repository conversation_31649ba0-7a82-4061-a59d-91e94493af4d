package petai_po

import (
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

const (
	UserPetVaccinateRecordTypeVaccinate   = 1   //疫苗记录
	UserPetVaccinateRecordTypeDeworming   = 2   //驱虫记录
	UserPetVaccinateRecordTypeOral        = 3   //口腔
	UserPetVaccinateRecordTypePhysical    = 4   //体检
	UserPetVaccinateRecordTypeWash        = 5   //洗护
	UserPetVaccinateRecordTypeBody        = 6   //体况评分
	UserPetVaccinateRecordTypeThree       = 7   //三围
	UserPetVaccinateRecordTypeWeight      = 8   //体重
	UserPetVaccinateRecordTypeHistory     = 101 //宠物病史记录表
	UserPetVaccinateRecordTypePetDiagnose = 102 //症状自诊
	UserPetVaccinateRecordTypeReport      = 103 //报告解读

	UserPetVaccinateRecordSubType1 = 10301 //化验报告
	UserPetVaccinateRecordSubType2 = 10302 //检查报告
	UserPetVaccinateRecordSubType3 = 10303 //体检报告

	PetVaccinatedCategoryVaccinate     = "200000088" //核心疫苗
	PetVaccinatedCategoryRabiesVaccine = "200000089" //狂犬疫苗
	PetVaccinatedCategoryAntibody      = "200000090" //抗体检测
	PetVaccinatedCategoryAntibody2     = "200000092" //狂犬疫苗抗体检测
	PetVaccinatedCategoryAntibody1     = "200000091" //核心疫苗抗体检测

	DataSourceAi   = 1 //AI问答
	DataSourceSelf = 2 //自建
	DataSourceScrm = 3 //scrm系统
)

var UserPetVaccinateRecordTypeMap = map[int]string{
	UserPetVaccinateRecordTypeVaccinate:   "疫苗记录",
	UserPetVaccinateRecordTypeDeworming:   "驱虫记录",
	UserPetVaccinateRecordTypeOral:        "口腔",
	UserPetVaccinateRecordTypePhysical:    "体检",
	UserPetVaccinateRecordTypeWash:        "洗护",
	UserPetVaccinateRecordTypeBody:        "体况评分",
	UserPetVaccinateRecordTypeThree:       "三围",
	UserPetVaccinateRecordTypeWeight:      "体重",
	UserPetVaccinateRecordTypeHistory:     "宠物病史记录表",
	UserPetVaccinateRecordTypePetDiagnose: "症状自诊",
	UserPetVaccinateRecordTypeReport:      "报告解读",
}

var PetVaccinatedCategoryMap = map[string]string{
	PetVaccinatedCategoryVaccinate:     "核心疫苗",
	PetVaccinatedCategoryRabiesVaccine: "狂犬疫苗",
	PetVaccinatedCategoryAntibody:      "抗体检测",
	PetVaccinatedCategoryAntibody2:     "狂犬疫苗抗体检测",
	PetVaccinatedCategoryAntibody1:     "核心疫苗抗体检测",
}

// 宠物疫苗驱虫记录表
type UserPetVaccinateRecord struct {
	Id             int       `json:"id" xorm:"not null pk autoincr INT(11)"`
	ConversationId int       `json:"conversation_id"`                           //会话id即petai_conversation.id
	MessageUuid    string    `json:"message_uuid"`                              // 消息uuid ,petai_message.uuid
	PetInfoId      string    `json:"pet_info_id" xorm:"not null VARCHAR(32)"`   //宠物id即user_pet_info.pet_info_id
	PetId          string    `json:"pet_id" xorm:"not null VARCHAR(255)"`       // 宠物ID作为唯一标示，若授权了同scrm_organization_db.t_scrm_pet_info.pet_id，若未授权则为空
	OperationYear  int       `json:"operation_year" xorm:"not null INT(11)"`    // 接种日期/驱虫年份
	OperationDate  time.Time `json:"operation_date" xorm:"not null DATETIME"`   // 接种日期/驱虫日期
	ShopName       string    `json:"shop_name" xorm:"not null VARCHAR(255)"`    // 接诊机构
	ProductName    string    `json:"product_name" xorm:"not null VARCHAR(255)"` // 产品名称
	ProductCode    string    `json:"product_code" xorm:"not null VARCHAR(20)"`  // 产品编码

	Type             int       `json:"type" xorm:"not null TINYINT(4)"`               // 0绝育 1疫苗记录 2驱虫记录 3口腔 4体检 5洗护 6体况评分 7三围 8体重 101宠物病史记录表 102症状自诊 103报告解读
	SubType          int       `json:"sub_type" xorm:"not null TINYINT(4)"`           // 子类别:10301化验报告 10302检查报告 10303体检报告
	Category         string    `json:"category"`                                      // 类型（核心疫苗：200000088，狂犬疫苗：200000089，核心疫苗抗体检测：200000091,狂犬疫苗抗体检测:200000092,抗体检测：200000090）
	Number           int       `json:"number" xorm:"not null INT(11)"`                // 第几针
	NumberOf         int       `json:"number_of" xorm:"not null TINYINT(4)"`          // 0：未知，1：首次免疫，2：二次免疫，3：尾次免疫，4:年度免疫
	CreateTime       time.Time `json:"create_time" xorm:"not null DATETIME created"`  // 创建时间
	UpdateTime       time.Time `json:"update_time" xorm:"not null DATETIME updated" ` // 更新时间
	RecordPhoto      string    `json:"record_photo" xorm:"not null VARCHAR(255)"`     // 记录拍照
	TreatmentOutcome string    `json:"treatment_outcome"`                             // 治愈情况
	IsDelete         int       `json:"is_delete" xorm:"not null TINYINT(4)"`          // 是否删除：0否，1是

	//TScrmPetVaccinateRecordId int    `json:"t_scrm_pet_vaccinate_record_id" xorm:"not null INT(11)"` // dc_customer.t_scrm_pet_vaccinate_record.id
	ThirdNo      string `json:"third_no"`      // 第三方唯一标识当type为1,2时,scrm_organization_db.t_scrm_pet_health_record; type为3,4,5,6,7,8时dc_customer.t_scrm_pet_vaccinate_record.id;type为101时是SCMR病史记录唯一标识
	DataSource   int    `json:"data_source"`   // 数据来源：1-AI问答  2-自建 3-scrm系统',
	DeleteReason string `json:"delete_reason"` // 删除原因
}

func (s *UserPetVaccinateRecord) TableName() string {
	return "eshop.user_pet_vaccinate_record"
}

// 根据会话id获取健康记录
func (s *UserPetVaccinateRecord) GetByConversationId(session *xorm.Session, conversationId int) (out []*UserPetVaccinateRecord, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}

	if conversationId == 0 {
		err = errors.New("conversationId is 0")
		return
	}
	out = make([]*UserPetVaccinateRecord, 0)
	if err = session.Table("eshop.user_pet_vaccinate_record").Where("conversation_id=?", conversationId).Where("type!=?", UserPetVaccinateRecordTypePetDiagnose).Where("is_delete=0").Find(&out); err != nil {
		log.Error("根据会话id获取健康记录失败,err:" + err.Error())
		return
	}
	return
}

// 根据会话id获取健康记录map
func (s *UserPetVaccinateRecord) GetUserPetVaccinateRecordMap(session *xorm.Session, conversationId int) (out map[int]*UserPetVaccinateRecord, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}

	if conversationId == 0 {
		err = errors.New("conversationId is 0")
		return
	}
	out = make(map[int]*UserPetVaccinateRecord)
	list := make([]*UserPetVaccinateRecord, 0)
	if err = session.Table("eshop.user_pet_vaccinate_record").Where("conversation_id=?", conversationId).Find(&list); err != nil {
		log.Error("根据会话id获取健康记录失败,err:" + err.Error())
		return
	}

	for _, item := range list {
		out[item.Type] = item
	}
	return
}

func (s *UserPetVaccinateRecord) AuthData(session *xorm.Session, userInfoId string) (err error) {
	logPrefix := fmt.Sprintf("====用户同意授权scrm驱虫免疫数据 到 小闻养宠助手,userInfoId为%s", userInfoId)
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}

	// 获取用户信息
	eshopUserInfo, err := new(EshopUserInfo).GetUserInfoByUserId(session, userInfoId)
	if err != nil {
		log.Error(logPrefix + "查询用户信息失败,err:" + err.Error())
		return
	}
	if eshopUserInfo == nil || len(eshopUserInfo.UserInfoId) == 0 {
		err = errors.New("用户不存在")
		return
	}
	if eshopUserInfo.UserId == "" {
		log.Error(logPrefix + "scrm用户id为空")
		err = errors.New("授权失败")
		return
	}
	if eshopUserInfo.IsAuthorized != 1 {
		err = errors.New("用户未授权")
	}

	userPetInfo := make([]*UserPetInfo, 0)
	if err = session.Table("eshop.user_pet_info").Where("user_info_id=?", userInfoId).Find(&userPetInfo); err != nil {
		log.Error(logPrefix + "查询用户宠物信息失败,err:" + err.Error())
		return
	}

	scrmPetIdSli := make([]string, 0)
	petInfoIdSli := make([]string, 0)
	petIdMapPetInfoId := make(map[string]string)
	for _, petInfo := range userPetInfo {
		if petInfo.PetId == "" {
			log.Info(logPrefix+"scrm的宠物id为空,不需要授权", petInfo.Id)
			continue
		}
		scrmPetIdSli = append(scrmPetIdSli, petInfo.PetId)
		petInfoIdSli = append(petInfoIdSli, petInfo.PetInfoId)
		petIdMapPetInfoId[petInfo.PetId] = petInfo.PetInfoId
	}

	// 获取小闻养宠助手 里已经存在的scrm 驱虫免疫记录
	userPetVaccinateRecord := make([]*UserPetVaccinateRecord, 0)
	UserPetVaccinateRecordMap := make(map[string]int)
	if err = session.Table("eshop.user_pet_vaccinate_record").In("pet_info_id ", petInfoIdSli).
		Where("third_no!=''").Find(&userPetVaccinateRecord); err != nil {
		log.Error(logPrefix + "查询小闻养宠助手 里已经存在的scrm 驱虫免疫记录失败,err:" + err.Error())
		return
	}
	for _, record := range userPetVaccinateRecord {
		UserPetVaccinateRecordMap[record.ThirdNo] = record.Id

	}

	// 获取scrm用户的驱虫疫苗记录
	TScrmPetHealthRecord, err := new(omnibus_po.TScrmPetHealthRecord).GetHealthRecordByPetId(session, scrmPetIdSli)
	if err != nil {
		log.Error(logPrefix + "获取scrm用户的驱虫疫苗记录失败,err:" + err.Error())
		return
	}

	UserPetVaccinateRecordInsert := make([]*UserPetVaccinateRecord, 0)
	for _, record := range TScrmPetHealthRecord {
		if _, exist := UserPetVaccinateRecordMap[cast.ToString(record.Id)]; !exist {
			createTime, _ := time.ParseInLocation(utils.DateTimeLayout, record.CreateTime, time.Local)
			updateTime, _ := time.ParseInLocation(utils.DateTimeLayout, record.UpdateTime, time.Local)
			cdate, _ := time.ParseInLocation(utils.DateLayout, record.CDate, time.Local)
			tmp := &UserPetVaccinateRecord{

				PetInfoId:     petIdMapPetInfoId[record.PetId], // 宠物id即user_pet_info.id,
				PetId:         record.PetId,
				OperationYear: cast.ToInt(record.CYear), // 接种日期/驱虫年份
				OperationDate: cdate,                    // 接种日期/驱虫日期
				ShopName:      record.StructName,        // 接诊机构
				ProductName:   record.ProductName,       // 产品名称
				ProductCode:   record.ProductCode,       // 产品编码
				Category:      record.Category,
				Number:        record.Number,
				NumberOf:      record.NumberOf,
				CreateTime:    createTime,
				UpdateTime:    updateTime,
				IsDelete:      record.IsDelete,
				ThirdNo:       cast.ToString(record.Id),
				DataSource:    DataSourceScrm,
			}
			if record.Type == "200000084" {
				tmp.Type = UserPetVaccinateRecordTypeVaccinate
			} else if record.Type == "200000085" {
				tmp.Type = UserPetVaccinateRecordTypeDeworming
			}

			UserPetVaccinateRecordInsert = append(UserPetVaccinateRecordInsert, tmp)

		}
	}

	// 开始授权阿闻自建健康记录数据
	//   获取scrm用户的口腔 体检 洗护 体况评分 三围 体重信息
	TScrmPetVaccinateRecord, err := new(omnibus_po.TScrmPetVaccinateRecord).GetCustomerPetVaccinateRecord(session, scrmPetIdSli)
	if err != nil {
		log.Error(logPrefix + "获取阿闻用户的健康记录失败,err:" + err.Error())
		return
	}

	for _, record := range TScrmPetVaccinateRecord {
		if _, exist := UserPetVaccinateRecordMap[cast.ToString(record.Id)]; !exist {
			createTime, _ := time.ParseInLocation(utils.DateTimeLayout, record.CreateTime, time.Local)
			updateTime, _ := time.ParseInLocation(utils.DateTimeLayout, record.UpdateTime, time.Local)
			operationDate, _ := time.ParseInLocation(utils.DateLayout, record.OperationDate, time.Local)
			tmp := &UserPetVaccinateRecord{

				PetInfoId:     petIdMapPetInfoId[record.PetId], // 宠物id即user_pet_info.id,
				PetId:         record.PetId,
				OperationYear: cast.ToInt(record.OperationYear), // 接种日期/驱虫年份
				OperationDate: operationDate,                    // 接种日期/驱虫日期
				ShopName:      record.ShopName,                  // 接诊机构
				ProductName:   record.ProductName,               // 产品名称
				ProductCode:   "",                               // 产品编码
				Category:      "",
				Type:          record.Type,
				CreateTime:    createTime,
				UpdateTime:    updateTime,
				ThirdNo:       cast.ToString(record.Id),
				RecordPhoto:   record.RecordPhoto,
				DataSource:    DataSourceScrm,
			}

			UserPetVaccinateRecordInsert = append(UserPetVaccinateRecordInsert, tmp)

		}
	}

	if len(UserPetVaccinateRecordInsert) > 0 {
		_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&UserPetVaccinateRecordInsert)
		if err != nil {
			log.Error(logPrefix + "同步scrm驱虫免疫记录,新增数据失败,err:" + err.Error())
		}
	}

	return

}

// 获取宠物病史记录列表
func (s *UserPetVaccinateRecord) GetMedRecordList(session *xorm.Session, petInfoIdSli []string) (list []UserPetVaccinateRecord, err error) {
	logPrefix := fmt.Sprintf("====获取宠物病史记录列表,petInfoIdSli为%s", utils.JsonEncode(petInfoIdSli))
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if len(petInfoIdSli) == 0 {
		err = errors.New("petInfoIdSli为空")
		return
	}
	list = make([]UserPetVaccinateRecord, 0)
	if err = session.Table("eshop.user_pet_vaccinate_record").In("pet_info_id", petInfoIdSli).Where("type=101").Find(&list); err != nil {
		log.Errorf("%s,err:%s", logPrefix, err.Error())
		return
	}
	return
}
func (s *UserPetVaccinateRecord) ToMap(list []UserPetVaccinateRecord) (listMap map[string]UserPetVaccinateRecord) {
	listMap = make(map[string]UserPetVaccinateRecord)
	for _, v := range list {
		if len(v.PetId) == 0 {
			continue
		}
		if len(v.ThirdNo) == 0 {
			continue
		}
		listMap[v.ThirdNo] = v
	}
	return
}

// 授权scrm宠物的病例数据
func (s *UserPetVaccinateRecord) AuthDataMedicalHistory(session *xorm.Session, userInfoId string) (err error) {
	logPrefix := fmt.Sprintf("====用户同意授权scrm宠物病例数据 到 小闻养宠助手,userInfoId为%s", userInfoId)
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}

	// 获取用户信息
	eshopUserInfo, err := new(EshopUserInfo).GetUserInfoByUserId(session, userInfoId)
	if err != nil {
		log.Error(logPrefix + "查询用户信息失败,err:" + err.Error())
		return
	}
	if eshopUserInfo == nil || len(eshopUserInfo.UserInfoId) == 0 {
		err = errors.New("用户不存在")
		return
	}
	if eshopUserInfo.UserId == "" {
		log.Error(logPrefix + "scrm用户id为空")
		err = errors.New("授权失败")
		return
	}
	if eshopUserInfo.IsAuthorized != 1 {
		err = errors.New("用户未授权")
	}

	userPetInfo := make([]*UserPetInfo, 0)
	if err = session.Table("eshop.user_pet_info").Where("user_info_id=?", userInfoId).Find(&userPetInfo); err != nil {
		log.Error(logPrefix + "查询用户宠物信息失败,err:" + err.Error())
		return
	}

	scrmPetIdSli := make([]string, 0)
	petInfoIdSli := make([]string, 0)
	petIdMapPetInfoId := make(map[string]string)
	for _, petInfo := range userPetInfo {
		if petInfo.PetId == "" {
			log.Info(logPrefix+"scrm的宠物id为空,不需要授权", petInfo.Id)
			continue
		}
		scrmPetIdSli = append(scrmPetIdSli, petInfo.PetId)
		petInfoIdSli = append(petInfoIdSli, petInfo.PetInfoId)
		petIdMapPetInfoId[petInfo.PetId] = petInfo.PetInfoId
	}

	// 获取宠物的病例列表
	medRecordList, err := s.GetMedRecordList(session, petInfoIdSli)
	if err != nil {
		log.Error(logPrefix + "查询宠物病史记录列表失败,err:" + err.Error())
		return
	}
	medRecordMap := s.ToMap(medRecordList)
	inserts := make([]UserPetVaccinateRecord, 0)
	// 获取每个宠物的病例列表
	for _, petId := range scrmPetIdSli {
		if ZLPetCaseList, e := common.FindPetCaseListFromZL(petId, 1, 999); e != nil {
			log.Error(logPrefix + "获取scrm宠物病例列表失败,petId:" + petId + ",err:" + e.Error())
			continue
		} else if ZLPetCaseList == nil {
			continue
		} else if len(ZLPetCaseList.Data.List) == 0 {
			continue
		} else {
			// 处理每个病例
			for _, petCase := range ZLPetCaseList.Data.List {
				if PetCaseDetail, e := common.FindPetCaseDetailFromZL(cast.ToString(petCase.RegId), cast.ToString(petCase.Orgid)); e != nil {
					log.Errorf("%s 获取scrm宠物病例详情失败,petId:%s,regId:%d,orgid:%d,err:%s", logPrefix, petId, petCase.RegId, petCase.Orgid, e.Error())
					continue
				} else {
					_, ok := medRecordMap[fmt.Sprintf("%d_%d", petCase.RegId, petCase.Orgid)]
					if ok {
						continue
					}

					visitDate, e := time.ParseInLocation(utils.DateTimeLayout, PetCaseDetail.CreateTime, time.Local)

					if e != nil {
						log.Errorf("%s 解析时间失败,petId:%s,regId:%d,orgid:%d,err:%s", logPrefix, petId, petCase.RegId, petCase.Orgid, e.Error())
						continue
					}

					inserts = append(inserts, UserPetVaccinateRecord{
						Type:             UserPetVaccinateRecordTypeHistory,
						PetInfoId:        petIdMapPetInfoId[petId],
						PetId:            petId,
						OperationYear:    visitDate.Year(),
						OperationDate:    visitDate,
						ProductName:      PetCaseDetail.MainSymptom,
						ShopName:         PetCaseDetail.HospitalName,
						TreatmentOutcome: PetCaseDetail.MedResultText,
						DataSource:       DataSourceScrm,
						ThirdNo:          fmt.Sprintf("%d_%d", petCase.RegId, petCase.Orgid),
					})

				}
			}
		}
	}
	if len(inserts) > 0 {
		if _, err = session.Table("eshop.user_pet_vaccinate_record").Insert(inserts); err != nil {
			log.Error(logPrefix + "新增用户宠物病例失败,err:" + err.Error())
			err = errors.New("授权失败")
			return
		}
	}
	return

}
