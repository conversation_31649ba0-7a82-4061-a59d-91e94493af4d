package services

import (
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"strings"
	"time"

	"xorm.io/xorm"

	"github.com/leeqvip/gophp/serialize"
	"github.com/spf13/cast"
)

type DisGoodsService struct {
	common.BaseService
}

func (s DisGoodsService) GoodsPage(req vo.GoodsPageReq) ([]vo.GoodsPageData, int, error) {
	s.Begin()
	defer s.Close()
	log.Info("获取分销商品，入参：", utils.InterfaceToJSON(req))
	session := s.Session
	var list []vo.GoodsPageData

	session.Select("ug.store_id,ug.goods_serial,ug.dis_write,ug.id,ug.goods_commonid,ug.goods_id,ug.goods_name," +
		"ug.goods_spec,ug.goods_price,ug.is_dis,ug.is_default_commis_rate,ug.dis_commis_rate,ug.dis_normal_commis_rate," +
		"FROM_UNIXTIME(ug.goods_edittime, '%Y-%m-%d %H:%i:%s') AS update_time,ugc.gc_id,ugc.gc_name,ug.goods_state")
	session.Table("upetmart.upet_goods ug").
		//Join("LEFT", "shop s", "s.org_id=ug.store_id").
		Join("LEFT", "upetmart.upet_goods_common ugc", "ugc.goods_commonid=ug.goods_commonid AND ugc.store_id=ug.store_id")
	if req.IsDis > -1 {
		session.And("ug.is_dis = ?", req.IsDis)
	}
	if req.OrgId > 0 {
		session.And("ug.store_id = ? and ugc.store_id= ?", req.OrgId, req.OrgId)
	}

	if req.GcId > 0 {
		session.And("ug.gc_id_3 = ?", req.GcId)
	}
	if req.GoodsState == 1 {
		session.And("ug.goods_state =1")
	} else if req.GoodsState == 2 {
		session.And("ug.goods_state =0")
	}
	if len(req.Where) > 0 {
		//查询条件的类型（id=分销员id，name=分销员姓名，mobile=分销员手机号，belong_salesman_id=所属业务员ID，belong_salesman_name=所属业务员，belong_salesman_mobile=业务员手机号）
		switch req.WhereType {
		case "":
		case "goods_name":
			session.And("ug.goods_name LIKE ?", "%"+req.Where+"%")
		case "spu":
			session.And("ug.goods_commonid = ?", req.Where)
		case "sku":
			session.And("ug.goods_id = ?", req.Where)
		}
	}

	limit := req.PageSize
	start := (req.PageIndex - 1) * req.PageSize
	total, err := session.OrderBy("ug.goods_edittime DESC").Limit(limit, start).FindAndCount(&list)
	if err != nil {
		log.Error("查询分销商品列表失败：err=", err.Error())
		return nil, 0, err
	}

	if len(list) > 0 {
		for i, e := range list {
			// 格式化商品分类
			list[i].GcName = strings.ReplaceAll(e.GcName, " &gt;", ">")
			list[i].ShopName = enum.OrgMap[req.OrgId]
			// 泛解析商品规格
			specValue, err := serialize.UnMarshal([]byte(e.GoodsSpec)) // 使用 Unserialize 反序列化
			if err != nil || specValue == nil {
				continue
			}

			for _, v := range specValue.(map[string]interface{}) {
				spec := v.(string)
				list[i].GoodsSpec = spec
			}
		}
	}

	return list, cast.ToInt(total), nil
}

func (s DisGoodsService) GoodsGlobalSetting(req vo.GoodsGlobalSetReq) error {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	if req.DisCommisRate < 0 || req.DisCommisRate > 90 {
		log.Error("设置商品为分销商品，分销比例非法")
		return errors.New("分销比例校验失败，请输入0~90的数字")
	}

	req.DisNormalCommisRate = req.DisCommisRate
	updateCols1 := []string{"dis_normal_commis_rate", "goods_edittime"}
	updateCols2 := []string{"dis_normal_commis_rate", "dis_commis_rate", "goods_edittime"}
	_, err := session.Table("upetmart.upet_goods").Where("store_id=?", req.OrgId).Cols(updateCols1...).Where("dis_activity_id>0").Where("is_dis=1").Where("is_default_commis_rate=1").Update(&req)
	if err != nil {
		log.Error("全局佣金设置，修改数据库异常1：e=" + err.Error())
		return errors.New("全局佣金设置异常：e=" + err.Error())
	}
	_, err = session.Table("upetmart.upet_goods").Where("store_id=?", req.OrgId).Cols(updateCols2...).Where("dis_activity_id=0").Where("is_dis=1").Where("is_default_commis_rate=1").Update(&req)
	if err != nil {
		log.Error("全局佣金设置，修改数据库异常2：e=" + err.Error())
		return errors.New("全局佣金设置异常：e=" + err.Error())
	}
	// 将使用默认佣金比例的商品，修改佣金比例
	//_, err := session.Exec("UPDATE "+"upetmart.upet_goods SET dis_commis_rate=?,dis_normal_commis_rate=?,goods_edittime=? WHERE store_id=? AND is_dis=1 AND is_default_commis_rate=1", req.DisCommisRate, req.DisCommisRate, time.Now().Unix(), req.OrgId)

	// 设置默认佣金比例到redis中
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	mCache.Save(string(cache_source.EShop), fmt.Sprintf(cachekey.DefaultCommisRate, req.OrgId), req.DisCommisRate, 0)

	return nil
}

func (s DisGoodsService) GetGoodsGlobalSetting(orgId int) float64 {
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	commisRate := mCache.Get(string(cache_source.EShop), fmt.Sprintf(cachekey.DefaultCommisRate, orgId))
	if commisRate == nil {
		return 0
	}
	return cast.ToFloat64(commisRate[0].(string))
}

func (s DisGoodsService) CommissionSetting(req vo.GoodsCommissionSetReq) error {
	logPrefix := fmt.Sprintf("分销商品设置佣金，store_id：%d,goods_id=%d", req.OrgId, req.GoodsId)
	log.Info(logPrefix, "入参：%s", utils.InterfaceToJSON(req))
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	if req.IsDis == 1 {
		if req.DisCommisRate < 0 || req.DisCommisRate > 90 {
			log.Error("设置商品为分销商品，分销比例非法")
			return errors.New("分销比例校验失败，请输入0~90的数字")
		}

		// 如果是设置为默认佣金设置，从redis获取到配置的默认比例
		if req.IsDefaultCommisRate == 1 {
			req.DisCommisRate = s.GetGoodsGlobalSetting(req.OrgId)
		}
		req.DisNormalCommisRate = req.DisCommisRate
		//判断 是否该商品正在参与计划佣金活动， 如果参与， 则只修改日常佣金字段
	}
	updateCols := []string{"dis_normal_commis_rate", "is_dis", "is_default_commis_rate", "goods_edittime"}
	DisWrite := strings.TrimSpace(req.DisWrite)
	if len(DisWrite) > 0 {
		updateCols = append(updateCols, "dis_write")
	}
	// 获取商品信息
	goodsData := struct {
		StoreId             int
		GoodsId             int
		DisActivityId       int
		DisCommisRate       float64
		DisNormalCommisRate float64
		IsDis               int
		IsDefaultCommisRate int
	}{}
	if exists, err := session.Table("upetmart.upet_goods").Where("store_id=?", req.OrgId).Where("goods_id=?", req.GoodsId).Get(&goodsData); err != nil {
		log.Error(logPrefix, "获取商品信息失败，err=", err.Error())
		return errors.New("获取商品信息失败")
	} else if !exists {
		log.Error(logPrefix, "商品信息不存在")
		return errors.New("商品信息不存在")
	}
	log.Info(logPrefix, "商品信息：", utils.InterfaceToJSON(goodsData))
	if goodsData.DisActivityId == 0 {
		updateCols = append(updateCols, "dis_commis_rate")
	}
	req.GoodsEdittime = int(time.Now().Unix())
	_, err := session.Table("upetmart.upet_goods").Cols(updateCols...).Where("store_id=?", req.OrgId).Where("goods_id=?", req.GoodsId).Update(&req)
	if err != nil {
		log.Error("全局佣金设置，修改数据库异常：e=" + err.Error())
		return errors.New("全局佣金设置异常：e=" + err.Error())
	}

	// 分销商品同步到店铺
	if req.IsDis == 1 {
		go s.GoodsDisSync([]int{req.GoodsId})
	}

	return nil
}

// 分销商品-推广素材
func (s DisGoodsService) GoodsDisWrite(req vo.GoodsDisWriteReq) error {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session
	_, err := session.Exec("UPDATE "+"upetmart.upet_goods SET dis_write=? WHERE store_id=? AND goods_id=?",
		req.DisWrite, req.OrgId, req.GoodsId)
	if err != nil {
		log.Error("分销商品编辑推广素材，修改数据库异常：e=" + err.Error())
		return errors.New("编辑推广素材异常：e=" + err.Error())
	}

	return nil
}

// 实现是设置为分销的商品，根据未认领的店铺进行同步
// 支持多个goods_id,
// goods_id查询在upemart.upet_goods_eshop的shop_id的门店id，取差集
// 再添加到upetmart.upet_goods_eshop
func (s DisGoodsService) GoodsDisSync(GoodsIds []int) error {
	s.Begin()
	defer s.Close()
	session := s.Session

	var (
		shopIds []int //所有店铺
	)

	err := session.Table("eshop.shop").Select("id").Where("is_setted_shop=1").Find(&shopIds)
	if err != nil {
		return errors.New("查询店铺id失败")
	}

	type GoodsEshop struct {
		StoreId int
		ShopId  int
		GoodsId int
	}
	//查询所有未认领的店铺，遍历GoodsIds查询没有认领的店铺，然后批量添加
	for _, gid := range GoodsIds {
		var hasShopIds []int //已认领的店铺
		err = session.Table("upetmart.upet_goods_eshop").Select("distinct shop_id").Where("goods_id = ?", gid).Find(&hasShopIds)
		if err != nil {
			return errors.New("查询店铺id失败")
		}
		// 取shopIds与hasShopIds的差集
		unclaimedShopIds := difference(shopIds, hasShopIds)
		//分批每500条批量添加 upetmart.upet_goods_eshop
		for i := 0; i < len(unclaimedShopIds); {
			end := (i + 100)
			if end > len(unclaimedShopIds) {
				end = len(unclaimedShopIds)
			}
			batchUnclaimeds := unclaimedShopIds[i:end]

			var records []GoodsEshop
			var essync []distribution_po.UpetGoodsHandlelog
			for _, shopID := range batchUnclaimeds {
				records = append(records, GoodsEshop{
					StoreId: 3,
					ShopId:  shopID,
					GoodsId: gid,
				})
				essync = append(essync, distribution_po.UpetGoodsHandlelog{
					StoreId:   3,
					ShopId:    shopID,
					GhGoodIds: cast.ToString(gid),
					GhNotes:   "设置分销商品",
					GhAddtime: time.Now().Unix(),
				})
			}
			_, err = session.Table("upetmart.upet_goods_eshop").Insert(&records)
			if err != nil {
				return errors.New("批量插入未认领店铺失败")
			}

			_, err = session.Table("upetmart.upet_goods_handlelog").Insert(&essync)
			if err != nil {
				return errors.New("批量添加商品操作同步失败")
			}
			i = end
		}
	}
	return nil
}

func difference(a, b []int) []int {
	// 使用map来存储b中的元素
	bMap := make(map[int]struct{})
	for _, id := range b {
		bMap[id] = struct{}{}
	}

	// 遍历a，找出不在bMap中的元素
	var diff []int
	for _, id := range a {
		if _, found := bMap[id]; !found {
			diff = append(diff, id)
		}
	}
	return diff
}

// SyncEsGoods 宠商云店铺新增商品，同步es记录
func SyncEsGoods(session *xorm.Session, goodIds []int, shopId, Type int) error {
	session.Begin()
	for i := 0; i < len(goodIds); {
		end := (i + 5000)
		if end > len(goodIds) {
			end = len(goodIds)
		}
		goodSlice := goodIds[i:end]
		eshopGoods := make([]distribution_po.UpetGoodsEshop, 0)
		ghGoodIds := ""
		for _, gid := range goodSlice {
			eshopGoods = append(eshopGoods, distribution_po.UpetGoodsEshop{
				StoreId: 3,
				ShopId:  shopId,
				GoodsId: gid,
			})
			ghGoodIds += cast.ToString(gid) + ","
		}
		if len(goodSlice) > 0 {
			if ghGoodIds[len(ghGoodIds)-1] == ',' {
				ghGoodIds = ghGoodIds[:len(ghGoodIds)-1]
			}
		}
		desc := ""
		switch Type {
		case enum.SyncEsDisGoodsEnum:
			desc = "设置分销商品"
		case enum.SyncEsSetShopEnum:
			desc = "设置店铺默认"
		case enum.SyncEsGoodsShopEnum:
			desc = "同步商品数据"
		}
		esSync := distribution_po.UpetGoodsHandlelog{
			StoreId:   3,
			ShopId:    shopId,
			GhGoodIds: ghGoodIds,
			GhNotes:   desc,
			GhAddtime: time.Now().Unix(),
		}
		if Type != enum.SyncEsGoodsShopEnum {
			_, err := session.Table("upetmart.upet_goods_eshop").Insert(&eshopGoods)
			if err != nil {
				session.Rollback()
				return fmt.Errorf("%s失败,添加店铺eshop商品失败：err=%s", desc, err.Error())
			}
		}

		_, err := session.Table("upetmart.upet_goods_handlelog").Insert(&esSync)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("%s失败,添加商品handlelog记录失败：err=%s", desc, err.Error())
		}
		i = end
	}
	return nil
}
