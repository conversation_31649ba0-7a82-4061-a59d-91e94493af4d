package inventory

import (
	"context"
	"eShop/infra/errors"
	inventoryVO "eShop/view-model/inventory-vo/inventory"
	"time"

	"xorm.io/xorm"
)

// InventoryFlow 库存流水表
type InventoryFlow struct {
	ID                 int       `json:"id" xorm:"pk autoincr 'id'"`                         // 主键id
	ChainId            int64     `json:"chain_id" xorm:"'chain_id'"`                         // 连锁id
	StoreId            string    `json:"store_id" xorm:"'store_id'"`                         // 门店的主键
	WarehouseId        int       `json:"warehouse_id" xorm:"'warehouse_id'"`                 // 仓库id
	ProductId          int       `json:"product_id" xorm:"'product_id'"`                     // 商品id
	SkuId              int       `json:"sku_id" xorm:"'sku_id'"`                             // sku id
	BoundType          int       `json:"bound_type" xorm:"'bound_type'"`                     // 出库/入库类型：1-入库，2-出库
	ItemType           int       `json:"item_type" xorm:"'item_type'"`                       // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12. 自用出库
	ItemRefId          int       `json:"item_ref_id" xorm:"'item_ref_id'"`                   // 出入库关联的单据id
	ItemRefNo          string    `json:"item_ref_no" xorm:"'item_ref_no'"`                   // 出入库关联的单据编号
	ItemRefType        int       `json:"item_ref_type" xorm:"'item_ref_type'"`               // 出入库关联的单据类型
	ChangeNum          int       `json:"change_num" xorm:"'change_num'"`                     // 变更数量
	CurrentCostPrice   int       `json:"current_cost_price" xorm:"'current_cost_price'"`     // 当前变更时商品平均成本价(分)
	ChangeAmount       int       `json:"change_amount" xorm:"'change_amount'"`               // 当前变更金额(分)
	TotalNumBefore     int       `json:"total_num_before" xorm:"'total_num_before'"`         // 变更前总库存
	FreezeNumBefore    int       `json:"freeze_num_before" xorm:"'freeze_num_before'"`       // 变更前锁定库存
	AvailableNumBefore int       `json:"available_num_before" xorm:"'available_num_before'"` // 变更前可用库存
	TotalAmountBefore  int       `json:"total_amount_before" xorm:"'total_amount_before'"`   // 变更前总成本(分)
	TotalNumAfter      int       `json:"total_num_after" xorm:"'total_num_after'"`           // 变更后总库存
	FreezeNumAfter     int       `json:"freeze_num_after" xorm:"'freeze_num_after'"`         // 变更后锁定库存
	AvailableNumAfter  int       `json:"available_num_after" xorm:"'available_num_after'"`   // 变更后可用库存
	TotalAmountAfter   int       `json:"total_amount_after" xorm:"'total_amount_after'"`     // 变更后总成本(分)
	IsDeleted          int       `json:"is_deleted" xorm:"'is_deleted'"`                     // 删除标识:0未删除,1已删除
	Operator           string    `json:"operator" xorm:"'operator'"`                         // 操作人
	Remark             string    `json:"remark" xorm:"'remark'"`                             // 备注
	CreatedTime        time.Time `json:"created_time" xorm:"created 'created_time'"`         // 创建时间
	UpdatedTime        time.Time `json:"updated_time" xorm:"updated 'updated_time'"`         // 修改时间
}

type InventoryFlowCount struct {
	SkuId int `json:"sku_id" xorm:"'sku_id'"`
	Count int `json:"count" xorm:"'count'"`
}

func (i InventoryFlow) GetSkuHasFlow(ctx context.Context, session *xorm.Session, skuIds []int) (map[int]int, error) {
	if len(skuIds) == 0 {
		return nil, nil
	}

	var inventoryFlowCount []InventoryFlowCount
	if err := session.Select("sku_id, count(sku_id)").Table("inventory_flow").In("sku_id", skuIds).And("bound_type = 'INBOUND'").Find(&inventoryFlowCount); err != nil {
		return nil, err
	}

	// inventoryFlowCount转map[int]int
	skuFlowMap := make(map[int]int, 0)
	for _, v := range inventoryFlowCount {
		skuFlowMap[v.SkuId] = v.Count
	}
	return skuFlowMap, nil
}

func (i InventoryFlow) BatchInsert(ctx context.Context, session *xorm.Session, inventoryFlows []InventoryFlow) error {
	_, err := session.Context(ctx).InsertMulti(inventoryFlows)
	if err != nil {
		session.Rollback()
		return errors.Wrap(err, "创建出入库流水记录失败")
	}
	return nil
}

func (i InventoryFlow) Page(ctx context.Context, session *xorm.Session, cmd inventoryVO.InventoryFlowPageRequest) ([]inventoryVO.InventoryFlowResponse, int64, error) {
	var flows []inventoryVO.InventoryFlowResponse

	query := session.Table("inventory_flow").Alias("f").
		Join("left", "pro_sku s", "s.id = f.sku_id").
		Join("left", "pro_product p", "p.id = s.product_id").
		Where("f.warehouse_id = ?", cmd.WarehouseId)

	// 构建查询条件
	if cmd.SkuId > 0 {
		query = query.And("f.sku_id = ?", cmd.SkuId)
	}

	if cmd.BoundType > 0 {
		query = query.And("f.bound_type = ?", cmd.BoundType)
	}

	if cmd.ItemType > 0 {
		query = query.And("f.item_type = ?", cmd.ItemType)
	}

	// 选择字段
	query.Select(`
		f.id,
		f.chain_id,
		f.store_id,
		f.warehouse_id,
		f.bound_type,
		f.item_type,
		f.item_ref_id,
		f.item_ref_no,
		f.item_ref_type,
		f.change_num,
		f.current_cost_price,
		f.change_amount,
		f.total_num_before,
		f.freeze_num_before,
		f.available_num_before,
		f.total_amount_before,
		f.total_num_after,
		f.freeze_num_after,
		f.available_num_after,
		f.total_amount_after,
		f.operator,
		f.remark,
		f.created_time,
		s.id as sku_id,
		s.bar_code,
		s.product_specs,
		p.id as product_id,
		p.name as product_name,
		p.category_name
	`)

	// 排序
	if len(cmd.Sort) > 0 && len(cmd.Order) > 0 {
		query = query.OrderBy("f." + cmd.Sort + " " + cmd.Order)
	}

	// 分页
	offset := (cmd.Current - 1) * cmd.Size
	total, err := query.Limit(cmd.Size, offset).FindAndCount(&flows)
	if err != nil {
		return nil, 0, errors.Wrap(err, "查询库存流水失败")
	}

	return flows, total, nil
}
