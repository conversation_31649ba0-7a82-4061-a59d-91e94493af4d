package services

import (
	order_po "eShop/domain/order-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	order_vo "eShop/view-model/order-vo"
)

// OrderService 订单服务
type OrderService struct {
	common.BaseService
}

// NewOrderService 创建订单服务实例
func NewOrderService() *OrderService {
	return &OrderService{}
}

// GetOrderList 获取订单列表
func (s *OrderService) GetOrderList(req order_vo.OrderListReq) ([]order_vo.OrderDetailVo, int64, error) {
	s.<PERSON>gin()
	defer s.Close()

	query := order_po.NewOrderQuery(s.Engine.NewSession())

	// 转换请求参数
	queryReq := order_po.OrderQueryReq{
		OrderSn:     req.OrderSn,
		OrderType:   req.OrderType,
		OrderStatus: req.OrderStatus,
		PageIndex:   req.PageIndex,
		PageSize:    req.PageSize,
		OrgId:       req.OrgId,
		ScrmUserId:  req.ScrmUserId,
	}

	// 查询数据
	orders, total, err := query.GetOrderList(queryReq)
	if err != nil {
		log.Errorf("获取订单列表失败: %v", err)
		return nil, 0, err
	}

	// 转换响应数据
	result := make([]order_vo.OrderDetailVo, 0, len(orders))
	for _, order := range orders {
		// 转换订单基本信息
		orderDetail := order_vo.OrderDetailVo{
			OrderSn:     order.OrderSn,
			OrderType:   order.OrderType,
			OrderStatus: order.OrderStatus,
			Total:       order.Total,
			PayTime:     utils.FormatTime(order.PayTime),
			CreateTime:  utils.FormatTime(order.CreateTime),
			ShopName:    order.ShopName,
			PayMode:     order.PayMode,
		}

		// 转换商品信息
		products := make([]order_vo.ProductItemVo, 0, len(order.Products))
		for _, product := range order.Products {
			products = append(products, order_vo.ProductItemVo{
				ProductId:    product.ProductId,
				ProductName:  product.ProductName,
				ProductType:  product.ProductType,
				Image:        product.Image,
				SkuId:        product.SkuId,
				Number:       product.Number,
				PayPrice:     product.PayPrice,
				PaymentTotal: product.PaymentTotal,
				BarCode:      product.BarCode,
				ThirdSkuId:   product.ThirdSkuId,
				OrderStatus:  order.OrderStatus,
				OrderType:    order.OrderType,
			})
		}
		orderDetail.Products = products

		result = append(result, orderDetail)
	}

	return result, total, nil
}
