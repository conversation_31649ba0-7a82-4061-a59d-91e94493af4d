/*
瑞鹏体制内的数据
*/
package api

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
)

// @Summary 获取医院列表@blky-v1.0
// @Description
// @Tags 小程序接口-瑞鹏体制内和体制外的数据
// @Accept json
// @Produce json
// @Param AllHospitalListReq query vo.RPHospitalListReq true " "
// @Success 200 {object} vo.AllHospitalListRes
// @Failure 400 {object} vo.AllHospitalListRes
// @Router /api/rp/all_hospital-list [GET]
func AllHospitalList(w http.ResponseWriter, r *http.Request) {
	resp := vo.AllHospitalListRes{}
	resp.Code = 400

	req, err := utils.Bind[vo.AllHospitalListReq](r)
	if err != nil {
		log.Error("获取瑞鹏医院，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取瑞鹏医院，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	server := services.RPService{}
	resp.Data, resp.Total, err = server.AllHospitalList(req)
	if err != nil {
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 获取医院列表@blky-v1.0
// @Description
// @Tags 小程序接口-瑞鹏体制内的数据
// @Accept json
// @Produce json
// @Param RPHospitalListReq query vo.RPHospitalListReq true " "
// @Success 200 {object} vo.RPHospitalListRes
// @Failure 400 {object} vo.RPHospitalListRes
// @Router /api/rp/hospital-list [GET]
func RPHospitalList(w http.ResponseWriter, r *http.Request) {
	resp := vo.RPHospitalListRes{}
	resp.Code = 400

	req, err := utils.Bind[vo.RPHospitalListReq](r)
	if err != nil {
		log.Error("获取瑞鹏医院，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取瑞鹏医院，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	server := services.RPService{}
	data, err := server.RPHospitalList(req)
	if err != nil {
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	resp.Data = make([]vo.Hospital, 1, len(data)+1)
	resp.Data[0] = vo.Hospital{HospitalName: "其他"}
	resp.Data = append(resp.Data, data...)

	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 获取字典数据 @blky-v1.0
// @Description
// @Tags C端共用API
// @Accept json
// @Produce json
// @Success 200 {object} viewmodel.DictDataRes
// @Failure 400 {object} viewmodel.DictDataRes
// @Router /eshop/api/rp/dict-data [Get]
func DictData(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.DictDataRes{}
	out.Code = 400

	server := services.RPService{}
	var err error
	if out.Data, err = server.DictData(); err != nil {
		log.Errorf("获取字典数据失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
