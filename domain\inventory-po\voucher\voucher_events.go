package voucher

import (
	vo "eShop/view-model/inventory-vo/voucher"
	"time"
)

// Event 事件接口
type Event interface {
	EventType() string
}

// VoucherCreatedEvent 单据创建事件
type VoucherCreatedEvent struct {
	ID          int            `json:"id"`
	VoucherNo   string         `json:"voucher_no"`
	VoucherType vo.VoucherType `json:"voucher_type"`
	//ItemType       vo.ItemType        `json:"item_type"`
	TotalNum       int                `json:"total_num"`
	TotalAmount    float64            `json:"total_amount"`
	OccurrenceTime time.Time          `json:"occurrence_time"`
	CreatedTime    time.Time          `json:"created_time"`
	Items          []VoucherItemEvent `json:"items"`
}

func (e VoucherCreatedEvent) EventType() string {
	return "voucher.created"
}

// VoucherCancelledEvent 单据取消事件
type VoucherCancelledEvent struct {
	ID          int       `json:"id"`
	CancelledAt time.Time `json:"cancelled_at"`
}

func (e VoucherCancelledEvent) EventType() string {
	return "voucher.cancelled"
}

// VoucherCompletedEvent 单据完成事件
type VoucherCompletedEvent struct {
	ID          int       `json:"id"`
	CompletedAt time.Time `json:"completed_at"`
}

func (e VoucherCompletedEvent) EventType() string {
	return "voucher.completed"
}

// VoucherItemEvent 单据明细事件
type VoucherItemEvent struct {
	ID          int     `json:"id"`
	ProductID   int     `json:"product_id"`
	ProductName string  `json:"product_name"`
	SKUID       int     `json:"sku_id"`
	BarCode     string  `json:"bar_code"`
	IOCount     int     `json:"io_count"`
	IOPrice     float64 `json:"io_price"`
	IOAmount    float64 `json:"io_amount"`
}
