package services

import (
	petai_po "eShop/domain/petai-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	petai_vo "eShop/view-model/petai-vo"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/spf13/cast"
)

// NewPetaiMedRecordService 创建宠物病史病例
func NewDataAcquisitionService() *DataAcquisitionService {
	return &DataAcquisitionService{}
}

type DataAcquisitionService struct {
	common.BaseService
	Request *http.Request
	JwtInfo *jwtauth.JwtInfo
}

// 数据采集新增数据
func (s *DataAcquisitionService) DataAcquisitionAdd(in petai_vo.DataAcquisitionAddReq) (err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====数据采集新增数据,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	if len(in.PetInfoId) == 0 {
		log.Error(logPrefix, "发生错误:宠物信息id不能为空")
		return errors.New("宠物信息id不能为空")
	}
	if len(in.QuestionUuid) == 0 {
		log.Error(logPrefix, "发生错误:消息uuid不能为空")
		return errors.New("消息uuid不能为空")
	}
	if len(in.ThreadId) <= 0 {
		log.Error(logPrefix, "发生错误:会话id不能为空")
		return errors.New("会话id不能为空")
	}
	conversationId := cast.ToInt(in.ThreadId)
	// 查询该条消息是否存在
	// message := petai_po.PetaiMessage{}
	// exist, err := session.Table("eshop.petai_message").Where("uuid = ?", in.QuestionUuid).Get(&message)
	// if err != nil {
	// 	log.Error(logPrefix, "发生错误:", err.Error())
	// 	return errors.New("查询消息发生错误")
	// } else if !exist {
	// 	log.Error(logPrefix, "获取消息发生错误:该消息不存在")
	// 	return errors.New("该消息不存在")
	// }
	// // 获取会话消息
	// conversation := petai_po.PetaiConversation{}
	// exist, err = session.Table("eshop.petai_conversation").Where("id=?", message.ConversationId).Get(&conversation)
	// if err != nil {
	// 	log.Error(logPrefix, "获取会话发生错误:", err.Error())
	// 	return errors.New("获取会话发生错误")
	// } else if !exist {
	// 	log.Error(logPrefix, "发生错误:该会话不存在")
	// 	return errors.New("该会话不存在")
	// }

	// // 数据采集的前置条件是：该会话关联了宠物
	// if len(conversation.PetInfoId) == 0 {
	// 	log.Error(logPrefix, "发生错误:该会话没有绑定宠物")
	// 	return errors.New("该会话没有绑定宠物")
	// }

	// 绝育数据处理
	if len(in.PetNeuteringStr) > 0 && in.PetNeuteringStr == "是" {
		if _, err = session.Table("eshop.user_pet_info").Where("pet_info_id=?", in.PetInfoId).Cols("pet_neutering").Update(&petai_po.UserPetInfo{
			PetNeutering: 1,
		}); err != nil {
			log.Error(logPrefix, "更新绝育状态失败:", err.Error())
		}

		data := petai_po.PetaiMessageExtend{
			ConversationId: conversationId,
			MessageUuid:    in.QuestionUuid,
			CollectedType:  petai_po.CollectedTypePetInfo,
			CorrelationId:  in.PetInfoId,
		}
		_, err = session.Table("eshop.petai_message_extend").Insert(&data)
		if err != nil {
			log.Error(logPrefix, "插入消息扩展失败:", err.Error())
		}

	}

	userPetVaccinateRecordListMap, err := new(petai_po.UserPetVaccinateRecord).GetUserPetVaccinateRecordMap(session, conversationId)
	if err != nil {
		log.Error(logPrefix, "获取健康档案数据失败:", err.Error())
		return errors.New("获取健康档案数据失败")
	}

	// messageExtendMap, err := new(petai_po.PetaiMessageExtend).GetMessageExtendMap(session, conversationId)
	// if err != nil {
	// 	log.Error(logPrefix, "获取消息扩展数据失败:", err.Error())
	// 	return errors.New("获取消息扩展数据失败")
	// }

	cols := "message_uuid,conversation_id,pet_info_id,operation_year,operation_date,shop_name,product_name,record_photo,type,category,number_of,data_source,is_delete,treatment_outcome"

	// 疫苗相关字段(插入user_pet_vaccinate_record,且type=1)
	// PetVaccinatedName     string `json:"pet_vaccinated_name"`      //疫苗产品名称 （必需）
	// PetVaccinatedTime     string `json:"pet_vaccinated_time"`      // 疫苗接种时间（年-月-日）（必需）
	// PetVaccinatedCategory string `json:"pet_vaccinated_category"`  //  疫苗类型（核心疫苗：200000088，狂犬疫苗：200000089，核心疫苗抗体检测：200000091,狂犬疫苗抗体检测:200000092,抗体检测：200000090） （非必需）
	// PetVaccinatedNumberOf int    `json:"pet_vaccinated_number_of"` //疫苗次数： 0-未知，1-首次免疫，2-二次免疫，3-尾次免疫，4-年度免疫（非必需）
	// PetVaccinatedHospital string `json:"pet_vaccinated_hospital"`  // 疫苗医院（非必需）
	if len(in.PetVaccinatedName) > 0 && len(in.PetVaccinatedTime) > 0 {
		PetVaccinatedTime, err := time.ParseInLocation("2006-01-02", in.PetVaccinatedTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "疫苗接种时间日期格式错误:", err.Error())
		} else {
			category := ""
			if _, ok := petai_po.PetVaccinatedCategoryMap[in.PetVaccinatedCategory]; ok {
				category = in.PetVaccinatedCategory
			}
			data := petai_po.UserPetVaccinateRecord{
				ConversationId: conversationId,
				MessageUuid:    in.QuestionUuid,
				PetInfoId:      in.PetInfoId,
				OperationYear:  PetVaccinatedTime.Year(),
				OperationDate:  PetVaccinatedTime,
				ShopName:       in.PetVaccinatedHospital,
				ProductName:    in.PetVaccinatedName,
				Type:           petai_po.UserPetVaccinateRecordTypeVaccinate,
				Category:       category,
				NumberOf:       in.PetVaccinatedNumberOf,
				DataSource:     petai_po.DataSourceAi,
				IsDelete:       0,
			}
			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypeVaccinate]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypeVaccinate).Update(&data)
				session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})

			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入疫苗数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session) // 插入消息扩展表petai_message_extend
				}
			}

		}
	}

	// 驱虫相关字段(插入user_pet_vaccinate_record,且type=2)
	// PetDewormingName     string `json:"pet_deworming_name"`     //驱虫产品名称（必需）
	// PetDewormingTime     string `json:"pet_deworming_time"`     //驱虫时间（年-月-日）（必需）
	// PetDewormingHospital string `json:"pet_deworming_hospital"` // 驱虫医院（非必需）
	if len(in.PetDewormingName) > 0 && len(in.PetDewormingTime) > 0 {
		PetDewormingTime, err := time.ParseInLocation("2006-01-02", in.PetDewormingTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "驱虫时间日期格式错误:", err.Error())
		} else {
			data := petai_po.UserPetVaccinateRecord{
				MessageUuid:    in.QuestionUuid,
				ConversationId: conversationId,
				PetInfoId:      in.PetInfoId,
				OperationYear:  PetDewormingTime.Year(),
				OperationDate:  PetDewormingTime,
				ShopName:       in.PetDewormingHospital,
				ProductName:    in.PetDewormingName,
				Type:           petai_po.UserPetVaccinateRecordTypeDeworming,
				DataSource:     petai_po.DataSourceAi,
				IsDelete:       0,
			}

			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypeDeworming]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypeDeworming).Update(&data)
				session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})

			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入驱虫数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session)
				}
			}
		}
	}

	// // 口腔相关字段(插入user_pet_vaccinate_record,且type=3)
	// PetTeethCleanName     string `json:"pet_teeth_clean_name"`     //洁牙产品名称（必需）
	// PetTeethCleanTime     string `json:"pet_teeth_clean_time"`     //洁牙时间（年-月-日）（必需）
	// PetTeethCleanHospital string `json:"pet_teeth_clean_hospital"` // 洁牙医院（非必需）
	if len(in.PetTeethCleanName) > 0 && len(in.PetTeethCleanTime) > 0 {
		PetTeethCleanTime, err := time.ParseInLocation("2006-01-02", in.PetTeethCleanTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "洁牙时间日期格式错误:", err.Error())
		} else {
			data := petai_po.UserPetVaccinateRecord{
				MessageUuid:    in.QuestionUuid,
				ConversationId: conversationId,
				PetInfoId:      in.PetInfoId,
				OperationYear:  PetTeethCleanTime.Year(),
				OperationDate:  PetTeethCleanTime,
				ShopName:       in.PetTeethCleanHospital,
				ProductName:    in.PetTeethCleanName,
				Type:           petai_po.UserPetVaccinateRecordTypeOral,
				DataSource:     petai_po.DataSourceAi,
				IsDelete:       0,
			}

			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypeOral]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypeOral).Update(&data)
				session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})
			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入口腔数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session)
				}
			}
		}
	}

	// // 体检相关字段(插入user_pet_vaccinate_record,且type=4)
	// PetPhysicalExamName     string `json:"pet_physical_exam_name"`     //体检产品名称（必需）
	// PetPhysicalExamTime     string `json:"pet_physical_exam_time"`     //体检时间（年-月-日）（必需）
	// PetPhysicalExamHospital string `json:"pet_physical_exam_hospital"` //体检医院（非必需）
	if len(in.PetPhysicalExamName) > 0 && len(in.PetPhysicalExamTime) > 0 {
		PetPhysicalExamTime, err := time.ParseInLocation("2006-01-02", in.PetPhysicalExamTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "体检时间日期格式错误:", err.Error())
		} else {
			data := petai_po.UserPetVaccinateRecord{
				MessageUuid:    in.QuestionUuid,
				ConversationId: conversationId,
				PetInfoId:      in.PetInfoId,
				OperationYear:  PetPhysicalExamTime.Year(),
				OperationDate:  PetPhysicalExamTime,
				ShopName:       in.PetPhysicalExamHospital,
				ProductName:    in.PetPhysicalExamName,
				Type:           petai_po.UserPetVaccinateRecordTypePhysical,
				DataSource:     petai_po.DataSourceAi,
				IsDelete:       0,
			}

			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypePhysical]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypePhysical).Update(&data)
				session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})
			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入体检数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session)
				}
			}
		}
	}

	// // 洗护相关字段(插入user_pet_vaccinate_record,且type=5)
	// PetCareName     string `json:"pet_care_name"`     //洗护产品名称（必需）
	// PetCareTime     string `json:"pet_care_time"`     //洗护时间（年-月-日）（必需）
	// PetCareHospital string `json:"pet_care_hospital"` //洗护医院（非必需）
	if len(in.PetCareName) > 0 && len(in.PetCareTime) > 0 {
		PetCareTime, err := time.ParseInLocation("2006-01-02", in.PetCareTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "洗护时间日期格式错误:", err.Error())
		} else {
			data := petai_po.UserPetVaccinateRecord{
				MessageUuid:    in.QuestionUuid,
				ConversationId: conversationId,
				PetInfoId:      in.PetInfoId,
				OperationYear:  PetCareTime.Year(),
				OperationDate:  PetCareTime,
				ShopName:       in.PetCareHospital,
				ProductName:    in.PetCareName,
				Type:           petai_po.UserPetVaccinateRecordTypeWash,
				DataSource:     petai_po.DataSourceAi,
				IsDelete:       0,
			}

			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypeWash]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypeWash).Update(&data)
				session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})
			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入洗护数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session)
				}
			}
		}
	}

	// // 体况相关字段(插入user_pet_vaccinate_record,且type=6)
	// PetBcsName  string `json:"pet_bcs_name"`  //体况评分（必需）
	// PetBcsTime  string `json:"pet_bcs_time"`  //体况时间（年-月-日）（必需）
	// PetBcsImage string `json:"pet_bcs_image"` //体况图片 （非必需）
	if len(in.PetBcsName) > 0 && len(in.PetBcsTime) > 0 {
		PetBcsTime, err := time.ParseInLocation("2006-01-02", in.PetBcsTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "体况时间日期格式错误:", err.Error())
		} else {
			data := petai_po.UserPetVaccinateRecord{
				MessageUuid:    in.QuestionUuid,
				ConversationId: conversationId,
				PetInfoId:      in.PetInfoId,
				OperationYear:  PetBcsTime.Year(),
				OperationDate:  PetBcsTime,
				ProductName:    in.PetBcsName,
				RecordPhoto:    in.PetBcsImage,
				Type:           petai_po.UserPetVaccinateRecordTypeBody,
				DataSource:     petai_po.DataSourceAi,
				IsDelete:       0,
			}

			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypeBody]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypeBody).Update(&data)
				session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})
			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入体况数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session)
				}
			}
		}
	}

	// // 体重相关字段(插入user_pet_vaccinate_record,且type=8)
	// PetWeightName string `json:"pet_weight_name"` //体重（必需）
	// PetWeightTime string `json:"pet_weight_time"` //体重记录时间（年-月-日）（必需）
	if len(in.PetWeightName) > 0 && len(in.PetWeightTime) > 0 {
		PetWeightTime, err := time.ParseInLocation("2006-01-02", in.PetWeightTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "洗护时间日期格式错误:", err.Error())
		} else {
			data := petai_po.UserPetVaccinateRecord{
				MessageUuid:    in.QuestionUuid,
				ConversationId: conversationId,
				PetInfoId:      in.PetInfoId,
				OperationYear:  PetWeightTime.Year(),
				OperationDate:  PetWeightTime,
				ProductName:    in.PetWeightName,
				Type:           petai_po.UserPetVaccinateRecordTypeWeight,
				DataSource:     petai_po.DataSourceAi,
				IsDelete:       0,
			}
			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypeWeight]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypeWeight).Update(&data)
				rows, err := session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})
				if err != nil {
					log.Error(logPrefix, "更新消息扩展表失败,err:", err.Error())
				} else {
					log.Info(logPrefix, "更新消息扩展表成功,rows:", rows)
				}
			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入体重数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session)
				}
			}
		}
	}

	// // 病史相关字段(插入petai_med_record)
	// PetDiseaseName             string `json:"pet_disease_name"`              //疾病名称（必需）
	// PetDiseaseTime             string `json:"pet_disease_time"`              //疾病记录时间（年-月-日）（必需）
	// PetDiseaseHospital         string `json:"pet_disease_hospital"`          //就诊医院（非必需）
	// PetDiseaseTreatmentOutcome string `json:"pet_disease_treatment_outcome"` //治愈情况（必需）

	if len(in.PetDiseaseName) > 0 && len(in.PetDiseaseTime) > 0 && len(in.PetDiseaseTreatmentOutcome) > 0 {
		PetDiseaseTime, err := time.ParseInLocation("2006-01-02", in.PetDiseaseTime, time.Local)
		if err != nil {
			log.Error(logPrefix, "疾病记录时间日期格式错误:", err.Error())
		} else {

			data := petai_po.UserPetVaccinateRecord{
				MessageUuid:      in.QuestionUuid,
				ConversationId:   conversationId,
				PetInfoId:        in.PetInfoId,
				OperationYear:    PetDiseaseTime.Year(),
				OperationDate:    PetDiseaseTime,
				ShopName:         in.PetDiseaseHospital,
				ProductName:      in.PetDiseaseName,
				Type:             petai_po.UserPetVaccinateRecordTypeHistory,
				DataSource:       petai_po.DataSourceAi,
				TreatmentOutcome: in.PetDiseaseTreatmentOutcome, // 治愈情况
				IsDelete:         0,
			}
			if v, ok := userPetVaccinateRecordListMap[petai_po.UserPetVaccinateRecordTypeHistory]; ok {
				session.Table("eshop.user_pet_vaccinate_record").Cols(cols).Where("conversation_id=?", conversationId).Where("type=?", petai_po.UserPetVaccinateRecordTypeHistory).Update(&data)
				session.Table("eshop.petai_message_extend").Cols("message_uuid").Where("conversation_id=?", conversationId).Where("correlation_id=?", cast.ToString(v.Id)).Update(&petai_po.PetaiMessageExtend{
					MessageUuid: in.QuestionUuid,
				})
			} else {
				_, err = session.Table("eshop.user_pet_vaccinate_record").Insert(&data)
				if err != nil {
					log.Error(logPrefix, "插入病史数据失败:", err.Error())
				} else {
					data := &petai_po.PetaiMessageExtend{
						ConversationId: conversationId,
						MessageUuid:    in.QuestionUuid,
						CollectedType:  petai_po.CollectedTypeHealthFile,
						CorrelationId:  cast.ToString(data.Id),
					}
					data.Insert(session)
				}
			}
		}
	}

	return

}

// 数据采集编辑数据
func (s *DataAcquisitionService) DataAcquisitionEdit(in petai_vo.DataAcquisitionEditReq) (err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====数据采集编辑数据,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	for _, v := range in.Data {
		switch v.Type {
		case 0:
			// 更新绝育状态
			if _, err = session.Table("eshop.user_pet_info").Where("pet_info_id=?", v.PetInfoId).Cols("pet_neutering").Update(&petai_po.UserPetInfo{
				PetNeutering: v.SubType,
			}); err != nil {
				log.Error(logPrefix, "更新绝育状态失败:", err.Error())
				continue
			}

		default:
			if _, err = session.Table("eshop.user_pet_vaccinate_record").Where("id=?", v.Id).Update(&v); err != nil {
				log.Error(logPrefix, "更新数据采集数据失败:id=", v.Id, "|错误为", err.Error())
				continue
			}
		}

	}

	return

}

func (s *DataAcquisitionService) DataAcquisitionList(in petai_vo.DataAcquisitionListReq) (out []*petai_po.UserPetVaccinateRecord, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====数据采集列表,会话id:%d", in.ConversationId)
	log.Info(logPrefix)

	if out, err = new(petai_po.UserPetVaccinateRecord).GetByConversationId(session, in.ConversationId); err != nil {
		log.Error(logPrefix, "查询数据采集列表失败:", err.Error())
		return
	}
	// messageExtend := &petai_po.PetaiMessageExtend{}
	// exist, err := session.Table("eshop.petai_message_extend").Where("conversation_id=?", in.ConversationId).
	// 	Where("collected_type=?", petai_po.CollectedTypePetInfo).Get(messageExtend)
	// if err != nil {
	// 	log.Error(logPrefix, "查询消息扩展表失败:", err.Error())
	// 	return
	// }
	// if exist {
	// 	petInfo := &petai_po.UserPetInfo{}
	// 	if _, err = session.Table("eshop.user_pet_info").Where("pet_info_id=?", messageExtend.CorrelationId).Cols("pet_neutering").Get(petInfo); err != nil {
	// 		log.Error(logPrefix, "查询宠物绝育状态:", err.Error())
	// 	}
	// 	out = append(out, &petai_po.UserPetVaccinateRecord{
	// 		ConversationId: in.ConversationId,
	// 		PetInfoId:      messageExtend.CorrelationId,
	// 		SubType:        petInfo.PetNeutering,
	// 	})
	// }

	return
}
func (s *DataAcquisitionService) DataAcquisitionListByMessageUuid(in petai_vo.DataAcquisitionListByMessageUuidReq) (out map[string]petai_vo.DataAcquisitionListByMessageUuidRes, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====数据采集列表,消息uuid:%s", in.QuestionUuid)
	log.Info(logPrefix)
	if len(in.QuestionUuid) == 0 {
		err = errors.New("消息uuid不能为空")
		return
	}
	list := make([]*petai_po.PetaiMessageExtend, 0)
	if err = session.Table("eshop.petai_message_extend").In("message_uuid", in.QuestionUuid).Find(&list); err != nil {
		log.Error(logPrefix, "查询数据采集列表失败:", err.Error())
		err = errors.New("查询数据采集列表失败")
		return
	}
	if len(list) == 0 {
		return
	}
	collectedTypeMap := make(map[string][]string, 0)
	for _, item := range list {
		collectedTypeMap[fmt.Sprintf("%s_%d", item.MessageUuid, item.CollectedType)] = append(collectedTypeMap[fmt.Sprintf("%s_%d", item.MessageUuid, item.CollectedType)], item.CorrelationId)
	}
	out = make(map[string]petai_vo.DataAcquisitionListByMessageUuidRes)
	PetVaccinateRecord := make([]*petai_po.UserPetVaccinateRecord, 0)
	PetVaccinateRecordIdMap := make(map[int]*petai_po.UserPetVaccinateRecord)
	petInfoList := make([]*petai_po.UserPetInfo, 0)
	PetInfoIdMap := make(map[string]*petai_po.UserPetInfo)
	for collectedType, correlationIdList := range collectedTypeMap {
		sli := strings.Split(collectedType, "_")
		collectedType := cast.ToInt(sli[1])
		//messageUuid := sli[0]
		if collectedType == petai_po.CollectedTypeHealthFile {
			ids := utils.StringSliceToIntSlice(correlationIdList)
			if err = session.Table("eshop.user_pet_vaccinate_record").In("id", ids).Find(&PetVaccinateRecord); err != nil {
				log.Error(logPrefix, "查询健康档案数据失败:", err.Error())
				continue
			}

		} else if collectedType == petai_po.CollectedTypePetInfo {
			if err = session.Table("eshop.user_pet_info").Select("id,pet_info_id,pet_neutering").In("pet_info_id", correlationIdList).Find(&petInfoList); err != nil {
				log.Error(logPrefix, "查询宠物信息失败:", err.Error())
				continue
			}

		} else {
			log.Error(logPrefix, "collectedType错误:", collectedType)
			continue
		}
	}

	for _, v := range PetVaccinateRecord {
		PetVaccinateRecordIdMap[v.Id] = v
	}

	for _, v := range petInfoList {
		PetInfoIdMap[v.PetInfoId] = v
	}

	for _, v := range list {
		tmp, ok := out[v.MessageUuid]
		if !ok {
			tmp = petai_vo.DataAcquisitionListByMessageUuidRes{
				PetVaccinateRecord: make([]*petai_po.UserPetVaccinateRecord, 0),
			}
		}
		if v.CollectedType == petai_po.CollectedTypeHealthFile {
			tmp.PetVaccinateRecord = append(tmp.PetVaccinateRecord, PetVaccinateRecordIdMap[cast.ToInt(v.CorrelationId)])
		} else if v.CollectedType == petai_po.CollectedTypePetInfo {
			tmp.PetNeutering = PetInfoIdMap[v.CorrelationId].PetNeutering
			tmp.PetInfoId = PetInfoIdMap[v.CorrelationId].PetInfoId
		}
		out[v.MessageUuid] = tmp
	}

	return
}
