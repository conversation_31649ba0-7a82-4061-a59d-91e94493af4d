package controllers

import (
	"eShop/infra/enum"
	jwt "eShop/infra/jwtauth"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/inventory-service/location"
	distribution_vo "eShop/view-model/distribution-vo"
	baseVO "eShop/view-model/inventory-vo"
	vo "eShop/view-model/inventory-vo/location"
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/spf13/cast"

	"github.com/gorilla/mux"
)

// LocationController 库位控制器
type LocationController struct {
	service location.LocationService
}

// NewLocationController 创建库位控制器
func NewLocationController(service location.LocationService) *LocationController {
	return &LocationController{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (c *LocationController) RegisterRoutes(r chi.Router) {
	r.Route("/inventory-app/location", func(r chi.Router) {
		r.Get("/list", c.Query)
		r.Post("/bind", c.BindProduct)
		r.Post("/unbind", c.UnbindProduct)
		r.Post("/delete", c.Delete)
		r.Post("/export", c.Export)
	})
}

// Create 创建库位
func (c *LocationController) Create(w http.ResponseWriter, r *http.Request) {
	var cmd vo.CreateCommand
	if err := json.NewDecoder(r.Body).Decode(&cmd); err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	err := c.service.Create(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Update 更新库位
func (c *LocationController) Update(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		response.BadRequest(w, "无效的ID")
		return
	}

	var cmd vo.UpdateCommand
	if err := json.NewDecoder(r.Body).Decode(&cmd); err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}
	cmd.ID = id

	if err := c.service.Update(r.Context(), cmd); err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Delete 删除库位
// @Summary 删除库位
// @Description 根据ID删除库位
// @Tags 库位管理
// @Accept x-www-form-urlencoded
// @Produce json
// @Param cmd body baseVO.IdRequest true "库位ID"
// @Success 200 {object} response.BaseResp "删除成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/location/delete [post]
func (c *LocationController) Delete(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[baseVO.IdRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的ID")
		return
	}

	if err := c.service.Delete(r.Context(), cmd.Id); err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// @Summary 查询库位列表
// @Description 根据仓库ID查询库位列表，支持分页和条件筛选
// @Tags 库位管理
// @Accept json
// @Produce json
// @Param params body vo.QueryParams true "查询参数"
// @Success 200 {object} []vo.Location "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/location/list [get]
func (c *LocationController) Query(w http.ResponseWriter, r *http.Request) {
	params, err := utils.Bind[vo.QueryParams](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}
	if params.WarehouseID == 0 {
		response.BadRequest(w, "必须指定仓库ID")
		return
	}

	locations, total, err := c.service.Query(r.Context(), params)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, locations, total)
}

// @Summary 绑定商品到库位
// @Description 将指定商品绑定到指定库位
// @Tags 库位管理
// @Accept json
// @Produce json
// @Param command body vo.BindProductCommand true "绑定商品命令"
// @Success 200 {object} response.BaseResp "绑定成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/location/bind [post]
func (c *LocationController) BindProduct(w http.ResponseWriter, r *http.Request) {
	var cmd vo.BindProductCommand
	if err := json.NewDecoder(r.Body).Decode(&cmd); err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	if err := c.service.BindProduct(r.Context(), cmd); err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// @Summary 解绑库位商品
// @Description 解除指定库位的商品绑定
// @Tags 库位管理
// @Accept x-www-form-urlencoded
// @Produce json
// @Param cmd body baseVO.IdRequest true "库位ID"
// @Success 200 {object} response.BaseResp "解绑成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/location/unbind [post]
func (c *LocationController) UnbindProduct(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[baseVO.IdRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的库位ID")
		return
	}

	if err := c.service.UnbindProduct(r.Context(), cmd.Id); err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// @Summary 导出库位数据
// @Description 导出指定仓库的库位数据
// @Tags 库位管理
// @Accept json
// @Produce json
// @Param params body vo.QueryParams true "查询参数"
// @Success 200 {object} response.BaseResp "解绑成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /inventory-app/location/export [post]
func (c *LocationController) Export(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.QueryParams](r)
	if err != nil {
		response.BadRequest(w, "导出库位数据请求参数解析失败")
		return
	}

	s := common.TaskListService{}
	var task distribution_vo.TaskList
	params, _ := json.Marshal(cmd)
	task.OperationFileUrl = string(params)
	task.OrgId = cast.ToInt(r.Header.Get("org_id"))
	task.TaskContent = enum.TaskContentInventoryLocationExport
	task.CreateId = jwt.CtxGet[string](r.Context(), "user_id")
	task.CreateName = jwt.CtxGet[string](r.Context(), "user_name")
	err = s.CreatTask(r, task)
	if err != nil {
		response.BadRequest(w, "导出库位数据失败")
		return
	}

	response.Success(w)
}
