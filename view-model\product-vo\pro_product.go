package product_vo

import (
	product_po "eShop/domain/product-po"
	viewmodel "eShop/view-model"
	"time"

	"xorm.io/xorm"
)

// 宠物连锁SAAS 新增连锁商品库商品所需字段
type AddChainProductSimple struct {
	Id                int    `json:"id"`                                                                                                                  // 商品自增id
	BarCode           string `json:"bar_code" xorm:"default 'null' comment('商品条码') VARCHAR(36) 'bar_code'"`                                               //商品条码
	Name              string `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`                                                      //商品名称
	CategoryIdOffline string `json:"category_id_offline" xorm:"not null comment('商品分类,关联商品分类表(原武汉那边的分类)') BIGINT 'category_id_offline'"`                  //商品分类,关联商品分类表(原武汉那边的分类)
	CategoryName      string `json:"category_name" xorm:"default 'null' comment('分类名称') VARCHAR(100) 'category_name'"`                                    //分类名称
	CategoryNav       string `json:"category_nav" xorm:"default 'null' comment('分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁') VARCHAR(500) 'category_nav'"` //分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁
	BrandId           string `json:"brand_id" xorm:"not null default 0 comment('品牌ID,关联品牌表') BIGINT 'brand_id'"`                                          //品牌ID,关联品牌表
	BrandName         string `json:"brand_name" xorm:"default '' comment('品牌名称,只商品编辑取这个字段') VARCHAR(64) 'brand_name'"`                                    //品牌名称,只商品编辑取这个字段
	SupplierId        string `json:"supplier_id" xorm:"not null default 0 comment('供应商ID,关联供应商表id') BIGINT 'supplier_id'"`                                //供应商ID,关联供应商表id
	SupplierName      string `json:"supplier_name" xorm:"default '' comment('供应商名称') VARCHAR(64) 'supplier_name'"`                                        //供应商名称
	Pic               string `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"`                                                   //商品图片（多图）
	NewSell           int    `json:"new_sell" xorm:"default 0 comment('是否开启新零售，1：未开启，2：已开启') INT 'new_sell'"`                                             //是否开启新零售，1：未开启，2：已开启
	NewSellStr        string `json:"new_sell_str" xorm:"not null default '' comment('新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接') VARCHAR(100) 'new_sell_str'"` //新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接
	CategoryId        int    `json:"category_id" xorm:"default 'null' comment('分类id，线上') INT 'category_id'"`                                              //分类id，线上
	ContentPc         string `json:"content_pc" xorm:"comment('电脑端详情内容') MEDIUMTEXT 'content_pc'"`                                                        //电脑端详情内容
	ContentMobile     string `json:"content_mobile" xorm:"comment('手机端详情内容') MEDIUMTEXT 'content_mobile'"`                                                //手机端详情内容
	//连锁ID
	ChainId int64 `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	//创建人ID
	CreatedBy int64 `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	//创建人名称
	CreatedName string `json:"created_name" xorm:"not null default '' comment('创建人名称') VARCHAR(100) 'created_name'"`
	//更新人ID
	UpdatedBy int64 `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	//更新人名称
	UpdatedName string `json:"updated_name" xorm:"not null default '' comment('更新人名称') VARCHAR(100) 'updated_name'"`
}

type ChainProduct struct {
	Product            product_po.ProProduct              `json:"product"`              // 连锁商品信息
	Sku                []product_po.ProSku                `json:"sku"`                  //连锁商品sku信息
	ProductChannel     []product_po.ProProductChannel     `json:"product_channel"`      // 渠道商品类目
	ProductChannelAttr []product_po.ProProductChannelAttr `json:"product_channel_attr"` // 渠道商品类目属性
	PushStoreIds       string                             `json:"push_store_ids"`       //需要下发的店铺id,多个用英文逗号隔开
	DataSource         int                                `json:"data_source"`          //数据来源：0-管理后台添加连锁商品 1-excel导入且不是多规格商品 2-excel导入且是多规格商品【注意： 如果一个商品有多个sku, excel里会有多条数据， 商品名称是一样的，sku信息不一样】
}

type StoreProduct struct {
	Product            product_po.ProProductExd           `json:"product"`              // 连锁商品信息
	Sku                []product_po.ProSku                `json:"sku"`                  //连锁商品sku信息
	ProductChannel     []product_po.ProProductChannel     `json:"product_channel"`      // 渠道商品类目
	ProductChannelAttr []product_po.ProProductChannelAttr `json:"product_channel_attr"` // 渠道商品类目属性
}

// 商品更新ES 请求参数  ChainId  和  StoreId必须2选1
type ProductEs struct {
	ChainId    int64  `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"` //连锁ID
	ProductIds []int  `json:"product_ids"`                                                          //skuid的集合
	StoreId    string `json:"store_id"`
	OrgId      int    `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	//操作类型  1新增和更新 2删除
	Type int
}

type ProSkuEs struct {
	Id int `json:"id" xorm:"pk autoincr not null comment('SKU ID') INT 'id'"`
	//商品库中的商品ID
	ProductId int `json:"product_id" xorm:"default 'null' comment('商品库中的商品ID') INT 'product_id'"`
	//市场价
	MarketPrice int `json:"market_price" xorm:"default 'null' comment('市场价') INT 'market_price'"`
	//建议价格/零售价
	RetailPrice int `json:"retail_price" xorm:"default 'null' comment('建议价格/零售价') INT 'retail_price'"`
	//成本价
	BasicPrice int `json:"basic_price" xorm:"default 0 comment('成本价') INT 'basic_price'"`
	//商品条码
	BarCode string `json:"bar_code" xorm:"default 'null' comment('商品条码') VARCHAR(36) 'bar_code'"`
	//重量
	WeightForUnit float64 `json:"weight_for_unit" xorm:"default 'null' comment('重量') DOUBLE(8) 'weight_for_unit'"`
	//前置仓价格
	PreposePrice int `json:"prepose_price" xorm:"default 'null' comment('前置仓价格') INT 'prepose_price'"`
	//门店仓价格
	StorePrice int `json:"store_price" xorm:"default 'null' comment('门店仓价格') INT 'store_price'"`
	//库存单位key
	StoreUnitKey string `json:"store_unit_key" xorm:"default '' comment('库存单位key') VARCHAR(255) 'store_unit_key'"`
	//库存单位
	StoreUnit string `json:"store_unit" xorm:"default '' comment('库存单位') VARCHAR(255) 'store_unit'"`
	//规格信息
	ProductSpecs string `json:"product_specs" xorm:"default '' comment('规格信息') VARCHAR(64) 'product_specs'"`
	//商品货号
	SkuCode string `json:"sku_code" xorm:"not null default '' comment('商品货号') VARCHAR(64) 'sku_code'"`
}

type ProductEsBody struct {
	ChainId           string     `json:"chain_id"`
	StoreId           string     `json:"store_id"`
	ProductId         int        `json:"product_id"`
	ChannelCategoryId int        `json:"channel_category_id"`
	UpdateDate        time.Time  `json:"update_date"`
	Name              string     `json:"name"`
	BarCode           string     `json:"bar_code"`
	ProductType       string     `json:"product_type"`
	Pic               string     `json:"pic"`
	SkuInfo           []ProSkuEs `json:"sku_info"`
}

type GetChainProductReq struct {
	ProductId int `json:"product_id"` // 连锁库商品ID
	ChainId   int `json:"chain_id"`   // 连锁ID(前端不用传)
}
type GetChainProductRes struct {
	viewmodel.BaseHttpResponse
	Data ChainProduct `json:"data"`
}

type FindChainProductListReq struct {
	viewmodel.BasePageHttpRequest
	ChainId                string `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"` //连锁ID
	CategoryIdOffline      int    `json:"category_id_offline"`                                                  //商品分类的末级分类id(只传末级分类id就好)
	CategoryIdOfflineFirst int    `json:"category_id_offline_first"`                                            //商品分类的一级分类id
	NewSell                uint   `json:"new_sell"`                                                             //是否开启新零售，1：未开启，2：已开启
	ChannelId              int    `json:"channel_id"`                                                           //渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)
	SearchBy               string `json:"search_by"`                                                            //商品信息搜索（商品名称、条码、id）

}

type FindChainProductList struct {
	ProductId    int    `json:"product_id"`                                                                                                          //商品id
	Pic          string `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"`                                                   //商品图片（多图）
	Name         string `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`                                                      //商品名称
	PushStoreCnt int    `json:"push_store_cnt"`                                                                                                      // 已下发店铺数
	PushStoreStr string `json:"push_store_str"`                                                                                                      // 下发店铺数
	CategoryNav  string `json:"category_nav" xorm:"default 'null' comment('分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁') VARCHAR(500) 'category_nav'"` //分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁

	NewSell    uint   `json:"new_sell" xorm:"default 0 comment('是否开启新零售，1：未开启，2：已开启') INT 'new_sell'"`                                             //是否开启新零售，1：未开启，2：已开启
	NewSellStr string `json:"new_sell_str" xorm:"not null default '' comment('新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接') VARCHAR(100) 'new_sell_str'"` //新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接
	BrandName  string `json:"brand_name"`
	//供应商名称
	SupplierName          string              `json:"supplier_name" xorm:"default '' comment('供应商名称') VARCHAR(64) 'supplier_name'"`
	Sku                   []product_po.ProSku `json:"sku"`                      //连锁商品sku信息
	CategoryId            int                 `json:"category_id"`              //前端末级分类id
	ShopCateFirst         string              `json:"shop_cate_first"`          //前端一级分类名称
	ShopCateSecond        string              `json:"shop_cate_second"`         //前端二级分类名称
	CategoryThirdNameMt   string              `json:"category_third_name_mt"`   //美团商品类目
	CategoryThirdNameElm  string              `json:"category_third_name_elm"`  //饿了么商品类目
	CategoryThirdNameJddj string              `json:"category_third_name_jddj"` //京东到家商品类目
}

type FindChainProductListRes struct {
	viewmodel.BasePageHttpResponse
	Data []FindChainProductList `json:"data"`
}

type BatchPushChainProductReq struct {
	ChainId    int    `json:"chain_id"`   // 连锁ID
	ProductIds string `json:"productIds"` // 商品id1,商品id2,商品id3（多个商品用逗号英文逗号隔开）
	StoreIds   string `json:"storeIds"`   // 店铺1,店铺2,店铺3 （多个店铺用逗号英文逗号隔开）
	CreatedId  int64  `json:"createdId"`  //创建人id
	CreateName string `json:"create_name"`
}

type DelChainProductReq struct {
	ChainId     int    `json:"chain_id"` // 前端不用传
	ProductId   int    `json:"product_id"`
	UpdatedBy   int64  `json:"updated_by"`
	UpdatedName string `json:"updated_name"`
}

type AddProductStoreDataReq struct {
	StoreIds   []string `json:"store_ids"`
	ProductIds []int    `json:"product_ids"`
	Session    *xorm.Session
}

type BatchStoreProductReq struct {
	//连锁商品id,多个以英文逗号拼接
	ProductIds string `json:"product_ids"`
	//支持sku批量,以英文逗号拼接
	SkuIds string `json:"sku_ids"`
	//类型 1-铺品 2-上架 3-下架 4库存同步  5-商品调价 6-删除 7-编辑
	Type int `json:"type"`
	//渠道 多个以英文逗号拼接 1-小程序商城，2-美团，3-饿了么，4-京东到家 100-线上门店
	ChannelIds string `json:"channel_ids"`
	//调价类型:1-统一调价 2-渠道调价
	PriceType int `json:"price_type"`
	//统一调价（分）；渠道调价时:渠道1-价格1|渠道2-价格2（1-10|2-11）
	ChannlePriceStr string `json:"channle_price_str"`
	//门店id
	TenantId string `json:"tenant_id"`
	//操作人
	UserId string `json:"UserId"`
	//操作人id
	UserName string `json:"UserName"`
	//判断账号是否代运营
	RoleType int `json:"role_type"`
	//是否异步任务 0-默认异步  1-请求接口
	SyncType int `json:"sync_type"`
}

type GenerateBarCodeRes struct {
	viewmodel.BaseHttpResponse
	Data struct {
		BarCode string `json:"bar_code"` //sku条码
	} `json:"data"`
}

// 商品库新增商品的SKU信息
type SkuInfo struct {
	//第三方SKU货号
	SkuThird []*SkuThird `protobuf:"bytes,1,rep,name=sku_third,json=skuThird,proto3" json:"sku_third"`
	//规格信息
	Skuv []*SkuValue `protobuf:"bytes,2,rep,name=skuv,proto3" json:"skuv"`
	//建议价格
	RetailPrice int32 `protobuf:"varint,3,opt,name=retail_price,json=retailPrice,proto3" json:"retail_price"`
	// SKU ID
	SkuId int32 `protobuf:"varint,4,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//商品ID
	ProductId int32 `protobuf:"varint,5,opt,name=product_id,json=productId,proto3" json:"product_id"`
	//市场价
	MarketPrice int32 `protobuf:"varint,6,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	//组合商品（类型为组合商品才需要）
	SkuGroup []*SkuGroup `protobuf:"bytes,7,rep,name=sku_group,json=skuGroup,proto3" json:"sku_group"`
	//商品条码
	BarCode string `protobuf:"bytes,8,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//渠道ID
	ChannelId int32 `protobuf:"varint,9,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//是否可用
	IsUse int32 `protobuf:"varint,10,opt,name=is_use,json=isUse,proto3" json:"is_use"`
	//重量
	WeightForUnit float64 `protobuf:"fixed64,11,opt,name=weight_for_unit,json=weightForUnit,proto3" json:"weight_for_unit"`
	//重量单位
	WeightUnit string `protobuf:"bytes,12,opt,name=weight_unit,json=weightUnit,proto3" json:"weight_unit"`
	//最小购买数量
	MinOrderCount int32 `protobuf:"varint,13,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
	//价格单位
	PriceUnit string `protobuf:"bytes,14,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit"`
	//前置仓价格
	PreposePrice int32 `protobuf:"varint,15,opt,name=prepose_price,json=preposePrice,proto3" json:"prepose_price"`
	//门店仓价格
	StorePrice int32 `protobuf:"varint,16,opt,name=store_price,json=storePrice,proto3" json:"store_price"`
	//商品销量
	SalesVolume int32 `protobuf:"varint,17,opt,name=sales_volume,json=salesVolume,proto3" json:"sales_volume"`
	//会员价
	MemberPrice int32 `protobuf:"varint,18,opt,name=member_price,json=memberPrice,proto3" json:"member_price"`
	//促销类型 0无促销，1团购，2限时折扣
	PromotionType int32 `protobuf:"varint,19,opt,name=promotion_type,json=promotionType,proto3" json:"promotion_type"`
	//商品促销价格
	PromotionPrice int32 `protobuf:"varint,20,opt,name=promotion_price,json=promotionPrice,proto3" json:"promotion_price"`
}

type SkuThird struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品库SKU ID
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//第三方SKUID
	ThirdSkuId string `protobuf:"bytes,3,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	// ERP仓库ID
	ErpId int32 `protobuf:"varint,4,opt,name=erp_id,json=erpId,proto3" json:"erp_id"`
	//商品ID
	ProductId int32 `protobuf:"varint,5,opt,name=product_id,json=productId,proto3" json:"product_id"`
	//仓库名称
	ErpName string `protobuf:"bytes,6,opt,name=erp_name,json=erpName,proto3" json:"erp_name"`
	//第三方SPUID
	ThirdSpuId string `protobuf:"bytes,7,opt,name=third_spu_id,json=thirdSpuId,proto3" json:"third_spu_id"`
	//渠道id
	ChannelId int32 `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//是否可用
	IsUse int32 `protobuf:"varint,9,opt,name=is_use,json=isUse,proto3" json:"is_use"`
}

type SkuValue struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//规格ID
	SpecId int32 `protobuf:"varint,2,opt,name=spec_id,json=specId,proto3" json:"spec_id"`
	//规格值ID
	SpecValueId int32 `protobuf:"varint,3,opt,name=spec_value_id,json=specValueId,proto3" json:"spec_value_id"`
	// SKU ID
	SkuId int32 `protobuf:"varint,4,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//商品ID
	ProductId int32 `protobuf:"varint,5,opt,name=product_id,json=productId,proto3" json:"product_id"`
	//规格属性图片
	Pic string `protobuf:"bytes,6,opt,name=pic,proto3" json:"pic"`
	//规格名称
	SpecName string `protobuf:"bytes,7,opt,name=spec_name,json=specName,proto3" json:"spec_name"`
	//规格值
	SpecValueValue string      `protobuf:"bytes,8,opt,name=spec_value_value,json=specValueValue,proto3" json:"spec_value_value"`
	Details        []*SkuValue `protobuf:"bytes,9,rep,name=details,proto3" json:"details"`
	//渠道id
	ChannelId int32 `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
}

type SkuGroup struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品ID
	ProductId int32 `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id"`
	// SKU
	SkuId int32 `protobuf:"varint,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//组合的商品ID
	GroupProductId int32 `protobuf:"varint,4,opt,name=group_product_id,json=groupProductId,proto3" json:"group_product_id"`
	//组合的商品SKUID
	GroupSkuId int32 `protobuf:"varint,5,opt,name=group_sku_id,json=groupSkuId,proto3" json:"group_sku_id"`
	//组合的商品数量
	Count int32 `protobuf:"varint,6,opt,name=count,proto3" json:"count"`
	//商品名称
	ProductName string `protobuf:"bytes,7,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 折扣类型(1-按折扣优惠，2-按固定价格优惠)
	DiscountType int32 `protobuf:"varint,8,opt,name=discount_type,json=discountType,proto3" json:"discount_type"`
	// 折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）
	DiscountValue int32 `protobuf:"varint,9,opt,name=discount_value,json=discountValue,proto3" json:"discount_value"`
	// 市场价
	MarketPrice int32 `protobuf:"varint,10,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	// 渠道id
	ChannelId int32 `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 子商品快照信息
	Snapshot *ChannelProductRequest `protobuf:"bytes,12,opt,name=snapshot,proto3" json:"snapshot"`
	// 子商品类型
	ProductType int32 `protobuf:"varint,13,opt,name=product_type,json=productType,proto3" json:"product_type"`
	// 规格信息(仅QuerySkuGroup有值)
	SpecList []*SpecValue `protobuf:"bytes,14,rep,name=spec_list,json=specList,proto3" json:"spec_list"`
	// term_type只是针对虚拟有效： 有效期类型 1标识有效期至多少，
	// 2标识购买后多少天失效
	TermType int32 `protobuf:"varint,16,opt,name=term_type,json=termType,proto3" json:"term_type"`
	// term_value 有效期的value
	TermValue int32 `protobuf:"varint,17,opt,name=term_value,json=termValue,proto3" json:"term_value"`
}

type SpecValue struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//规格ID
	SpecId int32 `protobuf:"varint,2,opt,name=spec_id,json=specId,proto3" json:"spec_id"`
	//规格值
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value"`
	//规格名
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
}

// 新建商品库商品请求内容
type ChannelProductRequest struct {
	//商品主表
	Product *ChannelProduct `protobuf:"bytes,1,opt,name=product,proto3" json:"product"`
	// SKU信息
	SkuInfo []*SkuInfo `protobuf:"bytes,2,rep,name=sku_info,json=skuInfo,proto3" json:"sku_info"`
	//商品属性
	ProductAttr []*ChannelProductAttr `protobuf:"bytes,3,rep,name=product_attr,json=productAttr,proto3" json:"product_attr"`
	//财务编码
	FinanceCode string `protobuf:"bytes,4,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	//商品标签
	Tags                 string   `protobuf:"bytes,5,opt,name=tags,proto3" json:"tags"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

type ChannelProduct struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//分类id
	CategoryId int32 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	//品牌id
	BrandId int32 `protobuf:"varint,3,opt,name=brand_id,json=brandId,proto3" json:"brand_id"`
	//商品名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//商品编号
	Code string `protobuf:"bytes,5,opt,name=code,proto3" json:"code"`
	//商品条码
	BarCode string `protobuf:"bytes,6,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//商品添加日期
	CreateDate string `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date"`
	//商品最后更新日期
	UpdateDate string `protobuf:"bytes,8,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	//是否删除
	IsDel int32 `protobuf:"varint,9,opt,name=is_del,json=isDel,proto3" json:"is_del"`
	//是否为组合商品
	IsGroup int32 `protobuf:"varint,10,opt,name=is_group,json=isGroup,proto3" json:"is_group"`
	//商品图片（多图）
	Pic string `protobuf:"bytes,11,opt,name=pic,proto3" json:"pic"`
	//商品卖点
	SellingPoint string `protobuf:"bytes,12,opt,name=selling_point,json=sellingPoint,proto3" json:"selling_point"`
	//商品视频地址
	Video string `protobuf:"bytes,13,opt,name=video,proto3" json:"video"`
	//电脑端详情内容
	ContentPc string `protobuf:"bytes,14,opt,name=content_pc,json=contentPc,proto3" json:"content_pc"`
	//手机端详情内容
	ContentMobile string `protobuf:"bytes,15,opt,name=content_mobile,json=contentMobile,proto3" json:"content_mobile"`
	//是否参与优惠折扣
	IsDiscount int32 `protobuf:"varint,16,opt,name=is_discount,json=isDiscount,proto3" json:"is_discount"`
	//商品类别（1-实物商品，2-虚拟商品, 3-组合商品）
	ProductType int32 `protobuf:"varint,17,opt,name=product_type,json=productType,proto3" json:"product_type"`
	//商品是否被使用过（认领或者其它第三方使用，否则不能被删除商品本身及SKU）
	IsUse int32 `protobuf:"varint,18,opt,name=is_use,json=isUse,proto3" json:"is_use"`
	//分类名称
	CategoryName string `protobuf:"bytes,19,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	// sku信息
	Sku []*Sku `protobuf:"bytes,20,rep,name=sku,proto3" json:"sku"`
	//商品属性
	Attr []*ChannelProductAttr `protobuf:"bytes,21,rep,name=attr,proto3" json:"attr"`
	//渠道id
	ChannelId string `protobuf:"bytes,22,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//渠道名称（美团，饿了么，阿闻，京东）
	ChannelName string `protobuf:"bytes,23,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	//渠道的分类id
	ChannelCategoryId int32 `protobuf:"varint,24,opt,name=channel_category_id,json=channelCategoryId,proto3" json:"channel_category_id"`
	//渠道商品类目id
	ChannelTagId int32 `protobuf:"varint,25,opt,name=channel_tag_id,json=channelTagId,proto3" json:"channel_tag_id"`
	//最后编辑用户
	LastEditUser string `protobuf:"bytes,26,opt,name=last_edit_user,json=lastEditUser,proto3" json:"last_edit_user"`
	//渠道的分类名称
	ChannelCategoryName string `protobuf:"bytes,27,opt,name=channel_category_name,json=channelCategoryName,proto3" json:"channel_category_name"`
	//是否是推荐商品,1是0否
	IsRecommend int32 `protobuf:"varint,28,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend"`
	//列表SKUID
	SkuId int32 `protobuf:"varint,29,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//品牌名称
	BrandName string `protobuf:"bytes,30,opt,name=brand_name,json=brandName,proto3" json:"brand_name"`
	// 组合类型(1:实实组合,2:虚虚组合,3.虚实组合)
	GroupType int32 `protobuf:"varint,31,opt,name=group_type,json=groupType,proto3" json:"group_type"`
	// 只有虚拟商品才有值(1.有效期至多少 2.有效期天数)
	TermType int32 `protobuf:"varint,32,opt,name=term_type,json=termType,proto3" json:"term_type"`
	// 如果term_type=1 存：时间戳 如果term_type=2 存多少天
	TermValue int32 `protobuf:"varint,33,opt,name=term_value,json=termValue,proto3" json:"term_value"`
	// 商品应用范围（1电商，2前置仓，3门店仓）
	UseRange string `protobuf:"bytes,34,opt,name=use_range,json=useRange,proto3" json:"use_range"`
	// 是否支持过期退款 1：是 0：否
	VirtualInvalidRefund int32 `protobuf:"varint,35,opt,name=virtual_invalid_refund,json=virtualInvalidRefund,proto3" json:"virtual_invalid_refund"`
	//商品短标题
	ShortName string `protobuf:"bytes,36,opt,name=short_name,json=shortName,proto3" json:"short_name"`
	//渠道的第三级分类名称
	ChannelLastCategoryName string `protobuf:"bytes,37,opt,name=channel_last_category_name,json=channelLastCategoryName,proto3" json:"channel_last_category_name"`
}

type ChannelProductAttr struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品ID
	ProductId int32 `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id"`
	//属性ID
	AttrId int32 `protobuf:"varint,3,opt,name=attr_id,json=attrId,proto3" json:"attr_id"`
	//属性值ID
	AttrValueId string `protobuf:"bytes,4,opt,name=attr_value_id,json=attrValueId,proto3" json:"attr_value_id"`
	//属性名称
	AttrName string `protobuf:"bytes,5,opt,name=attr_name,json=attrName,proto3" json:"attr_name"`
	//属性值
	AttrValue string `protobuf:"bytes,6,opt,name=attr_value,json=attrValue,proto3" json:"attr_value"`
	//渠道id
	ChannelId int32 `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
}

type Sku struct {
	// SKU ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//商品库中的商品ID
	ProductId int32 `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id"`
	//市场价
	MarketPrice int32 `protobuf:"varint,3,opt,name=market_price,json=marketPrice,proto3" json:"market_price"`
	//建议价格
	RetailPrice int32 `protobuf:"varint,4,opt,name=retail_price,json=retailPrice,proto3" json:"retail_price"`
	// sku规格值组合
	SkuValue []*SkuValue `protobuf:"bytes,5,rep,name=sku_value,json=skuValue,proto3" json:"sku_value"`
	//第三方货号
	SkuThird []*SkuThird `protobuf:"bytes,6,rep,name=sku_third,json=skuThird,proto3" json:"sku_third"`
	//渠道id
	ChannelId int32 `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//是否可用
	IsUse int32 `protobuf:"varint,8,opt,name=is_use,json=isUse,proto3" json:"is_use"`
	//商品条码
	BarCode string `protobuf:"bytes,9,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//重量
	WeightForUnit float64 `protobuf:"fixed64,10,opt,name=weight_for_unit,json=weightForUnit,proto3" json:"weight_for_unit"`
	//重量单位
	WeightUnit string `protobuf:"bytes,11,opt,name=weight_unit,json=weightUnit,proto3" json:"weight_unit"`
	//价格单位
	PriceUnit     string `protobuf:"bytes,12,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit"`
	MinOrderCount int32  `protobuf:"varint,13,opt,name=min_order_count,json=minOrderCount,proto3" json:"min_order_count"`
	//前置仓价格
	PreposePrice int32 `protobuf:"varint,14,opt,name=prepose_price,json=preposePrice,proto3" json:"prepose_price"`
	//门店仓价格
	StorePrice int32 `protobuf:"varint,15,opt,name=store_price,json=storePrice,proto3" json:"store_price"`
	//组合商品信息
	SkuGroup []*SkuGroup `protobuf:"bytes,16,rep,name=sku_group,json=skuGroup,proto3" json:"sku_group"`
}

type EditStoreProductReq struct {
	ProductId int `json:"product_id"  validate:"required"`
	// 分类id
	ChannelCategoryId string `json:"channel_category_id"  validate:"required"`
	//分类名称
	ChannelCategoryName string `json:"channel_category_name"  validate:"required"`
	//门店id
	StoreId string `json:"store_id"`
}

// GetServiceReq 获取服务详情请求
type GetServiceReq struct {
	Id      int64  `json:"id"`       // 服务ID
	StoreId string `json:"store_id"` // 店铺ID
}

// GetServiceRes 获取服务详情响应
type GetServiceRes struct {
	viewmodel.BaseHttpResponse
	Data ServiceDetail `json:"data"`
}

// ServiceDetail 服务详情
type ServiceDetail struct {
	// 主键
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`
	// skuId
	SkuId int `json:"sku_id" xorm:"default 0 INT 'sku_id'"`
	// 商品ID
	ProductId int `json:"product_id" xorm:"default 0 comment('商品ID') INT 'product_id'"`
	// 门店的主键
	StoreId string `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`
	// 分类id
	ChannelCategoryId string `json:"channel_category_id" xorm:"default '' comment('分类id') VARCHAR(50) 'channel_category_id'"`

	// 建议价格/零售价
	RetailPrice int `json:"retail_price" xorm:"default 0 comment('建议价格/零售价') INT 'retail_price'"`
	//市场价
	MarketPrice int `json:"market_price" xorm:"default 'null' comment('市场价') INT 'market_price'"`
	//商品名称
	ProductName string `json:"product_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'product_name'"`

	ProductPic string `json:"product_pic" xorm:"default '' comment('商品图片') VARCHAR(255) 'product_pic'"`
	//服务时长
	ServiceDuration int `json:"service_duration" xorm:"default 0 comment('服务时长') INT 'service_duration'"`
	//出生日期
	BirthDate string `json:"birth_date" xorm:"default 'null' comment('出生日期') VARCHAR(255) 'birth_date'"`
	//宠物品种
	PetVariety string `json:"pet_variety" xorm:"default '' comment('宠物品种') VARCHAR(255) 'pet_variety'"`
	//宠物品种名称
	PetVarietyName string `json:"pet_variety_name" xorm:"default '' comment('宠物品种名称') VARCHAR(255) 'pet_variety_name'"`
	//条码
	BarCode string `json:"bar_code" xorm:"default 'null' comment('条码') VARCHAR(36) 'bar_code'"`
	//商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)
	ProductType int `json:"product_type" xorm:"default 1 comment('商品类型：1-商品，2-服务 3-活体') INT 'product_type'"`
	//商品分类
	NamePath string `json:"namePath" xorm:"default 'null' comment('分类组合名称') VARCHAR(500) 'namePath'"`
	// ... 根据需要添加更多字段
}
type ProductSkuInfo struct {
	ChainId             int64  `json:"chain_id"`
	ProductId           int    `json:"product_id"`
	CategoryId          int    `json:"category_id"`
	CategoryIdOffline   int    `json:"category_id_offline"`
	ProductName         string `json:"product_name"`          // 商品名称
	ProductType         int    `json:"product_type"`          // 商品类型：1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体
	BarCode             string `json:"bar_code"`              // 条形码
	ProductCategoryPath string `json:"product_category_path"` // 商品分类路径
	SkuId               int    `json:"sku_id"`
	RetailPrice         int    `json:"retail_price"`
	BasicPrice          int    `json:"basic_price"`
	StorePrice          int    `json:"store_price"`
	ElmPrice            int    `json:"elm_price"`
	MtPrice             int    `json:"mt_price"`
	XcxPrice            int    `json:"xcx_price"`
}
