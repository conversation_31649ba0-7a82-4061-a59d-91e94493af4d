package voucher

import (
	"context"
	po "eShop/domain/inventory-po/voucher"
	jwt "eShop/infra/jwtauth"
	vo "eShop/view-model/inventory-vo/voucher"
	"strconv"
	"time"
)

// SaveReturn 保存采购退货单
func (s VoucherService) SaveReturn(ctx context.Context, cmd vo.VoucherSaveCommand) (int, error) {
	s.<PERSON>gin()
	session := s.Engine.NewSession()
	session.Begin()
	defer func() {
		if r := recover(); r != nil {
			session.Rollback()
		}
		session.Close()
		s.Close()
	}()

	voucherNo, err := s.GenerateVoucherNo(cmd.VoucherType)
	if err != nil {
		return -1, err
	}

	vourcher := po.Voucher{
		ChainId:      jwt.CtxGet[int64](ctx, "ChainId"),
		StoreId:      jwt.CtxGet[string](ctx, "TenantId"),
		WarehouseId:  cmd.WarehouseId,
		SupplierId:   cmd.SupplierId,
		VoucherNo:    voucherNo,
		Name:         cmd.Name,
		VoucherType:  vo.VoucherTypeMap[cmd.VoucherType],
		Status:       cmd.Status,
		ChangeNum:    0,
		ChangeAmount: 0,
		ProfitStatus: 0,
		Remark:       cmd.Remark,
		IsDeleted:    0,
		Operator:     jwt.CtxGet[string](ctx, "UserName"),
		SourceType:   cmd.SourceType,
		DeliveryTime: cmd.DeliveryTime,
		PurchaseTime: cmd.PurchaseTime,
		VoucherTime:  time.Now(),
		CreatedTime:  time.Now(),
		UpdatedTime:  time.Now(),
		ReturnTime:   cmd.ReturnTime,
	}
	voucherId, err := vourcher.Insert(ctx, session)
	if err != nil {
		panic(err)
	}

	// 保存单据详情
	var voucherDetails []po.VoucherDetail
	for _, detail := range cmd.Details {
		voucherDetail := po.VoucherDetail{
			ChainId:        jwt.CtxGet[int64](ctx, "ChainId"),
			StoreId:        strconv.Itoa(cmd.ShopId),
			WarehouseId:    cmd.WarehouseId,
			VoucherId:      voucherId,
			ProductId:      detail.ProductId,
			SkuId:          detail.SkuId,
			Price:          detail.Price,
			Quantity:       detail.Quantity,
			Amount:         detail.Price * detail.Quantity,
			ActualNum:      0, //采购单不写入实际数量
			ActualAmount:   0, //采购单不写入实际金额
			TotalNumBefore: 0, //需要确认这个两个字段是否有实际意义，如有需从库存中心拿实时库存
			TotalNumAfter:  0,
			ProfitStatus:   0,
			ChangeNum:      0,
			ChangeAmount:   0,
			AvgCostPrice:   0,
			Reason:         detail.Reason,
			Remark:         cmd.Remark,
			IsDeleted:      0,
			CreatedTime:    time.Now(),
			UpdatedTime:    time.Now(),
		}

		voucherDetails = append(voucherDetails, voucherDetail)
	}

	session.Insert(voucherDetails)
	session.Commit()

	return voucherId, nil
}

// RequestDetail 获取采购单据详情
func (s VoucherService) ReturnDetail(ctx context.Context, id int) (vo.VoucherRequestResponse, error) {
	s.Begin()
	session := s.Session
	defer func() {
		if r := recover(); r != nil {
			return
		}
		session.Close()
		s.Close()
	}()
	var response vo.VoucherRequestResponse

	_, err := session.SQL(`
	select
    iv.id,
    iv.voucher_no as current_voucher_no,
	civ.id as child_id,
    civ.voucher_no as child_voucher_no,
    iv.chain_id,
    iv.store_id,
    s.name as store_name,
    sbs.mobile as store_contact_method,
    iv.warehouse_id,
    w.name as warehouse_name,
    iv.supplier_id,
    ins.name as supplier_name,
    ins.contact_person as supplier_contact_person,
    ins.contact_method as supplier_contact_method,
	ins.address as supplier_address,
    iv.name,
    iv.voucher_type,
    iv.status,
    iv.source_type,
    iv.profit_status,
    iv.change_num,
    iv.change_amount,
    iv.remark,
	iv.logistics_no,
	iv.logistics_company,
    iv.is_deleted,
    iv.purchase_time,
    iv.delivery_time,
    iv.return_time,
    iv.voucher_time,
    iv.operator,
    iv.created_time,
    iv.updated_time
from
    eshop.inventory_voucher  iv
        left join eshop.inventory_suppliers ins on iv.supplier_id =ins.id
        left join datacenter.store s on s.id=iv.store_id
        left join datacenter.store_business_setup sbs on sbs.finance_code=s.finance_code
        left join dc_dispatch.warehouse w  on iv.warehouse_id=w.id
        left join eshop.inventory_voucher civ on civ.pid=iv.id
where iv.id=?
	`, id).Get(&response.VoucherInfo)

	err = session.SQL(`
select
    iv.id,
    vd.sku_id,
    vd.product_id,
    pp.name as product_name,
    ps.bar_code,
    ps.product_specs,
    ps.store_unit,
    ps.weight_for_unit,
    ps.tax_rate,
    vd.price,
    vd.quantity as  expect_quantity,
    vd.amount as  expect_amount,
    vd.actual_num as  actual_quantity,
    vd.actual_amount as  actual_amount,
	vd.reason,
    i.total_num AS inventory_total,
    i.freeze_num AS inventory_freeze,
    i.available_num AS inventory_available
from
    eshop.inventory_voucher  iv
        left join eshop.inventory_voucher_detail vd on iv.id=vd.voucher_id
        LEFT JOIN eshop.pro_sku ps ON vd.sku_id=ps.id
        LEFT JOIN eshop.pro_product pp ON vd.product_id=pp.id
        LEFT JOIN eshop.inventory i ON i.warehouse_id = vd.warehouse_id AND i.sku_id=vd.sku_id
where iv.id=?`, id).Find(&response.ProductInfo)

	if err != nil {
		panic(err)
	}
	return response, nil
}
