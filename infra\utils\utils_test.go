package utils

import (
	"testing"
)

func TestGetLocationByIP(t *testing.T) {
	type args struct {
		ip string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "测试获取地理位置",
			args: args{ip: "**************"},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetLocationByIP(tt.args.ip); got != tt.want {
				t.Errorf("GetLocationByIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetAfterTaxAmount(t *testing.T) {
	type args struct {
		amount int
		rate   string
	}
	tests := []struct {
		name               string
		args               args
		wantAfterTaxAmount int
	}{
		{
			name:               "测试获取税后金额",
			args:               args{amount: 1560, rate: "0.08"},
			wantAfterTaxAmount: 14400,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotAfterTaxAmount := GetAfterTaxAmount(tt.args.amount, tt.args.rate); gotAfterTaxAmount != tt.wantAfterTaxAmount {
				t.Errorf("GetAfterTaxAmount() = %v, want %v", gotAfterTaxAmount, tt.wantAfterTaxAmount)
			}
		})
	}
}
