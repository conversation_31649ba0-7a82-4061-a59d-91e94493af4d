package omnibus_po

type MessageRelationship struct {
	Id         string `json:"id" xorm:"not null pk VARCHAR(100)"`
	MessageId  string `json:"message_id" xorm:"comment('消息id') VARCHAR(100)"`
	MemberMain string `json:"member_main" xorm:"comment('用户id') VARCHAR(100)"`
	IsRead     int    `json:"is_read" xorm:"comment('是否已读1 未读 2 已读') INT(11)"`
	ChannelId  int    `json:"channel_id" xorm:"comment('渠道id 1:阿闻 2美团 3饿了么') INT(11)"`
}

func (m *MessageRelationship) TableName() string {
	return "datacenter.message_relationship"
}
