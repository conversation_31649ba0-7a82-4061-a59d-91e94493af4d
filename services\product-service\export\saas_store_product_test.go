package export

import (
	"eShop/infra/log"
	"eShop/services/common"
	"eShop/view-model/product-vo"
	_ "github.com/go-sql-driver/mysql"
	"github.com/xuri/excelize/v2"
	"testing"
)

func TestSaasStoreProductExport_DataExport(t *testing.T) {
	type fields struct {
		F            *excelize.File
		SheetName    string
		ExportParams *product_vo.FindStoreProductListReq
		writer       *excelize.StreamWriter
		BaseService  common.BaseService
	}
	type args struct {
		taskParams string
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		wantSuccessNum int
		wantFailNum    int
		wantErr        bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				taskParams: `{"page_index":1,"page_size":5,"chain_id":"530185752454150978","store_id":"530185752454150979","channel_id":0,"status":0,"query":"","type":0,"category_id":0,"category_id_offline":"","category_level":0,"is_stats":false,"exprot":0}`,
			},
			fields: fields{
				SheetName: "Sheet1",
				F:         excelize.NewFile(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := SaasStoreProductExport{
				F:            tt.fields.F,
				SheetName:    tt.fields.SheetName,
				ExportParams: tt.fields.ExportParams,
				writer:       tt.fields.writer,
				BaseService:  tt.fields.BaseService,
			}
			log.Init()
			gotSuccessNum, gotFailNum, err := e.DataExport(tt.args.taskParams)
			if (err != nil) != tt.wantErr {
				t.Errorf("DataExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotSuccessNum != tt.wantSuccessNum {
				t.Errorf("DataExport() gotSuccessNum = %v, want %v", gotSuccessNum, tt.wantSuccessNum)
			}
			if gotFailNum != tt.wantFailNum {
				t.Errorf("DataExport() gotFailNum = %v, want %v", gotFailNum, tt.wantFailNum)
			}
		})
	}
}
