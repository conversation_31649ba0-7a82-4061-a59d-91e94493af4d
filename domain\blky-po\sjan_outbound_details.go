package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SjanOutboundDetail 北京世纪安诺出库明细表领域模型
type SjanOutboundDetail struct {
	// 自增主键
	Id int `xorm:"pk autoincr not null 'id'" json:"id"`
	// 出库单号
	BillNo string `xorm:"not null VARCHAR(50) 'bill_no'" json:"bill_no"`
	// 物流码
	Barcode string `xorm:"not null VARCHAR(100) 'barcode'" json:"barcode"`
	// 产品编号
	PNo string `xorm:"not null VARCHAR(50) 'p_no'" json:"p_no"`
	// 扫码时间
	InDate time.Time `xorm:"not null datetime 'in_date'" json:"in_date"`
	// 包装数量
	Number int `xorm:"not null 'number'" json:"number"`
	// 创建时间
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"`
}

// TableName 表名
func (s *SjanOutboundDetail) TableName() string {
	return "blky.sjan_outbound_details"
}

// GetByBillNo 根据出库单号获取出库明细列表
func (s *SjanOutboundDetail) GetByBillNo(session *xorm.Session, billNo string) ([]SjanOutboundDetail, error) {
	var details []SjanOutboundDetail
	err := session.Where("bill_no = ?", billNo).Find(&details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// GetByBarcode 根据物流码获取出库明细列表
func (s *SjanOutboundDetail) GetByBarcode(session *xorm.Session, barcode string) ([]SjanOutboundDetail, error) {
	var details []SjanOutboundDetail
	err := session.Where("barcode = ?", barcode).Find(&details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// GetByPNo 根据产品编号获取出库明细列表
func (s *SjanOutboundDetail) GetByPNo(session *xorm.Session, pNo string) ([]SjanOutboundDetail, error) {
	var details []SjanOutboundDetail
	err := session.Where("p_no = ?", pNo).Find(&details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// GetByTimeRange 根据扫码时间范围查询出库明细
func (s *SjanOutboundDetail) GetByTimeRange(session *xorm.Session, startTime, endTime time.Time) ([]SjanOutboundDetail, error) {
	var details []SjanOutboundDetail
	err := session.Where("in_date BETWEEN ? AND ?", startTime, endTime).Find(&details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// GetProductCount 获取指定出库单号的产品数量统计
func (s *SjanOutboundDetail) GetProductCount(session *xorm.Session, billNo string) (map[string]int, error) {
	var details []SjanOutboundDetail
	err := session.Where("bill_no = ?", billNo).Find(&details)
	if err != nil {
		return nil, err
	}

	productCount := make(map[string]int)
	for _, detail := range details {
		productCount[detail.PNo] += detail.Number
	}

	return productCount, nil
}

// AddOutboundDetail 添加出库明细
func (s *SjanOutboundDetail) AddOutboundDetail(session *xorm.Session) (int64, error) {
	return session.Insert(s)
}
