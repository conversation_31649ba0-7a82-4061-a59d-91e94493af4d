package services

import (
	distribution_po "eShop/domain/distribution-po"
	external_po "eShop/domain/external-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	points_service "eShop/services/points-service"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"

	"xorm.io/xorm"

	"eShop/infra/utils"
	"eShop/services/distribution-service/enum"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"encoding/json"

	//base_service "eShop/services/base-service"
	"eShop/services/common"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/copier"

	_ "github.com/go-sql-driver/mysql"
	"github.com/spf13/cast"
)

type DistributorManageService struct {
	common.BaseService
}

// DistributorPage 分页查询分销员信息
func (s DistributorManageService) DistributorPage(req distribution_vo.DistributorPageReq) ([]distribution_vo.DistributorPageData, int, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var list []distribution_vo.DistributorPageData

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	var EnterpriseList []int64
	//如果包含了所属业务员，就需要查询业务员数据
	if strings.Contains(req.WhereType, "belong_salesman") {

		session.Select("distinct b.enterprise_id").Table("scrm_salesperson").Alias("a").
			Join("inner", "scrm_enterprise_salesperson_bind b", "b.salesperson_id = a.id ").
			Join("inner", "shop s", "s.enterprise_id=b.enterprise_id").
			Where("1=1")

		switch req.WhereType {
		case "belong_salesman_id":
			session.And("a.id = ?", req.Where)
		case "belong_salesman_name":
			session.And("a.name LIKE '%" + req.Where + "%'")
		case "belong_salesman_mobile":
			session.And("a.phone = ?", req.Where)
		}
		err := session.Find(&EnterpriseList)
		if err != nil {
			log.Error("查询分销员列表失败：err=", err.Error())
			return nil, 0, err
		}
	}
	session.Select("dd.id,dd.dis_role,s.enterprise_id,s.shop_name,s.saas_shop_id,dd.source_type,dd.member_id,dd.name,dd.real_name,dd.mobile,dd.encrypt_mobile,dd.shop_id,dd.status,dd.create_time,dd.approve_state,dd.reason,dd.social_code_image,"+
		"dd.update_time,um.member_name,en.enterprise_name,en.enterprise_type,en.code,de.enterprise_name as dis_enterprise_name,dd.tuoke_salesperson_id,sale.name tuoke_salesman_name,dd.total_customer,dd.in_system,dd.veterinary_code,df.valid_customer,"+
		"dd.order_num,dd.total_sales,dd.order_pay_num,dd.total_pay_sales,(dd.unsettled_commission+dd.settled_commission+dd.ins_settled_commission+dd.ins_unsettled_commission) as total_commission,"+
		"(dd.settled_commission+dd.ins_settled_commission) as settled_commission_total,(dd.unsettled_commission + dd.ins_unsettled_commission) as unsettled_commission_total,"+
		"dt.order_num AS shop_order_num,dt.total_sales AS shop_total_sales,dt.order_pay_num AS shop_order_pay_num,dt.total_pay_sales AS shop_total_pay_sales,(dt.settled_commission+dt.ins_settled_commission) AS shop_settled_commission_total,"+
		"(dt.unsettled_commission+dt.ins_unsettled_commission) AS shop_unsettled_commission_total,dt.withdraw_success,dt.withdraw_apply,dt.wait_withdraw,"+
		"ddd.hospital_name,ddd.province,ddd.city,ddd.professional,ddd.specialize,"+
		"cpa.available_points").
		Table("dis_distributor dd").
		Join("left", ""+"upetmart.upet_member um", "um.member_id=dd.member_id").
		Join("left", "shop s", "s.id=dd.shop_id").
		Join("left", "scrm_salesperson sale", "sale.id=dd.tuoke_salesperson_id").
		Join("left", "scrm_enterprise en", "en.id=s.enterprise_id").
		Join("left", "dis_enterprise de", "s.enterprise_id=de.scrm_enterprise_id and de.org_id=?", req.OrgId).
		Join("left", "(SELECT dis_id,COUNT(CASE WHEN expire_time IS NOT NULL AND expire_time > NOW() THEN 1 END) AS valid_customer FROM dis_distributor_fans GROUP BY dis_id) df", "dd.id = df.dis_id").
		Join("left", "dis_distributor_total dt", "dd.id = dt.`dis_id` AND dd.`shop_id` = dt.`shop_id`").
		Join("left", "dis_distributor_detail ddd", "ddd.dis_id = dd.id").
		Join("left", "cls_points_account cpa", "cpa.dis_id = dd.id")

	session.And("dd.hide_state=0")

	if req.OrgId > 0 {
		session.And("dd.org_id = ?", req.OrgId)
	}
	if req.Status > 0 {
		session.And("dd.status = ?", req.Status)
	}
	if req.ApproveState > 0 {
		session.And("dd.approve_state = ?", req.ApproveState)
	}
	if len(req.HospitalName) > 0 {
		session.And("ddd.hospital_name LIKE '%" + req.HospitalName + "%'")
	}
	if req.ShopId > 0 {
		session.And("dd.shop_id = ?", req.ShopId)
	}
	if req.EnterpriseName != "" {
		if req.OrgId == 4 {
			nowEnterprise := cast.ToInt64(req.EnterpriseName)
			if nowEnterprise != 0 {
				session.And("de.enterprise_name LIKE '%"+req.EnterpriseName+"%' or  de.scrm_enterprise_id=?", nowEnterprise)
			} else {
				session.And("de.enterprise_name LIKE '%" + req.EnterpriseName + "%'")
			}
		} else {
			nowEnterprise := cast.ToInt64(req.EnterpriseName)
			if nowEnterprise != 0 {
				session.And("en.enterprise_name LIKE '%"+req.EnterpriseName+"%' or en.id=?", nowEnterprise)
			} else {
				session.And("en.enterprise_name LIKE '%" + req.EnterpriseName + "%'")
			}
		}

	}

	if len(req.Where) > 0 {
		//查询条件的类型（id=分销员id，name=分销员姓名，mobile=分销员手机号，belong_salesman_id=所属业务员ID，belong_salesman_name=所属业务员，belong_salesman_mobile=业务员手机号，tuoke_salesman_name=注册业务员ID，tuoke_salesman_name=注册业务员）
		switch req.WhereType {
		case "":
		case "id":
			session.And("dd.id = ?", req.Where)
		case "name":
			if req.OrgId == 4 {
				session.And("dd.real_name LIKE '%" + req.Where + "%'")
			} else {
				session.And("dd.name LIKE '%" + req.Where + "%'")
			}
		case "mobile":
			session.And("dd.encrypt_mobile = ?", utils.MobileEncrypt(req.Where))
		case "tuoke_salesman_id":
			session.And("dd.tuoke_salesperson_id = ?", req.Where)
		case "tuoke_salesman_name":
			session.And("sale.name LIKE '%" + req.Where + "%'")

		}
	}

	if len(EnterpriseList) > 0 {
		session.In("en.id", EnterpriseList)
	}

	limit := req.PageSize
	start := (req.PageIndex - 1) * req.PageSize
	total, err := session.OrderBy("dd.id DESC").Limit(limit, start).FindAndCount(&list)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return nil, 0, err
	}
	//根据所有的企业SHOP_ID，补全业务员信息
	shopIds := make([]int64, 0)
	m := make(map[int64]int)
	for k, x := range list {
		EnterpriseIdStr := cast.ToInt64(x.EnterpriseId)
		if _, ok := m[EnterpriseIdStr]; !ok {
			shopIds = append(shopIds, EnterpriseIdStr)
			m[EnterpriseIdStr] = 0
		}
		if req.OrgId == enum.BLKYOrgId {
			if x.DisRole == distribution_po.DisRoleBoss || x.DisRole == distribution_po.DisRoleStaff {
				list[k].EnterpriseName = x.DisEnterpriseName
				list[k].OrgDataSource = enum.OrgDataSourceCLS
			} else {
				list[k].OrgDataSource = enum.OrgDataSourceBLKY
			}
		}

		//如果是百林康源，则返回真实名字
		//如果是百林康源，则返回真实名字
		if req.OrgId == enum.BLKYOrgId {
			list[k].Name = x.RealName
		}
	}
	var temp []distribution_vo.BelongSalesmanTemp

	//查询所有的企业对应的业务员
	session.Select("b.enterprise_id,a.id belong_salesman_id,a.name as belong_salesman_name").Table("scrm_salesperson").Alias("a").
		Join("inner", "scrm_enterprise_salesperson_bind b", "b.salesperson_id = a.id ").
		Where("1=1").In("b.enterprise_id", shopIds)
	err = session.Find(&temp)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return nil, 0, err
	}
	mapdata := make(map[int64][]distribution_vo.BelongSalesmanData)
	for _, x := range temp {
		data := distribution_vo.BelongSalesmanData{}
		data.BelongSalesmanName = x.BelongSalesmanName
		data.BelongSalesmanId = x.BelongSalesmanId
		mapdata[x.EnterpriseId] = append(mapdata[x.EnterpriseId], data)
	}
	for i, _ := range list {
		list[i].BelongSalesman = mapdata[cast.ToInt64(list[i].EnterpriseId)]
		list[i].ShopName = enum.OrgMap[req.OrgId]

	}

	return list, cast.ToInt(total), nil
}

func (s DistributorManageService) DistributorUnbind(req distribution_vo.DisIdReq) error {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Engine.NewSession()
	session.Begin()

	// 查询到分销员信息
	distributor := distribution_po.DisDistributor{}
	_, err := session.Where(" id=?", req.Id).Get(&distributor)
	if err != nil || distributor.Id == 0 {
		log.Error("查询分销员信息失败：err=", err.Error())
		return errors.New("分销员清退，查询分销员信息异常：e=" + err.Error())
	}

	// 当角色为“老板”时，清退需要将角色更新为“店员”
	if distributor.DisRole == 1 {
		_, err = session.Exec("UPDATE dis_distributor SET status=2,dis_role=2,update_time=NOW() WHERE id=?", req.Id)
	} else {
		_, err = session.Exec("UPDATE dis_distributor SET status=2,update_time=NOW() WHERE id=?", req.Id)
	}
	if err != nil {
		log.Error("分销员清退操作，修改分销员状态异常：e=" + err.Error())
		return errors.New("分销员清退，分销员状态修改异常：e=" + err.Error())
	}
	//分销员下的所有客户关系记录更新过期时间为当前时间
	nowStr := time.Now().Format(utils.DateTimeLayout)
	_, err = session.Exec("UPDATE dis_distributor_fans SET expire_time=? WHERE dis_id =? AND expire_time>?", nowStr, req.Id, nowStr)
	if err != nil {
		log.Error("分销员清退操作，分销员客户关系修改过期时间异常：e=" + err.Error())
		session.Rollback()
		return errors.New("分销员清退，分销员客户关系修改过期时间异常：e=" + err.Error())
	}

	session.Commit()
	return nil
}

// todo 需要修改为更换企业
func (s DistributorManageService) DistributorChange(req distribution_vo.DisChangeReq) (err error) {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Engine.NewSession()
	defer session.Close()
	dataSource := distribution_po.DisEnterpriseDataSourceCloud
	scrmEnterprise := distribution_po.ScrmEnterprise{}
	_, err = session.Where("id=?", cast.ToInt64(req.EnterpriseId)).Get(&scrmEnterprise)
	if err != nil {
		log.Error("查询企业信息失败，EnterpriseId=", req.EnterpriseId)
		return errors.New("查询企业信息失败")
	}

	// 百林康源企业，需要查询dis_enterprise表
	if req.OrgId == enum.BLKYOrgId {
		if scrmEnterprise.Id <= 0 {
			disEnterprise := distribution_po.DisEnterprise{}
			_, err = session.Where("scrm_enterprise_id=?", cast.ToInt64(req.EnterpriseId)).Get(&disEnterprise)
			if err != nil {
				log.Error("查询企业信息失败，EnterpriseId=", req.EnterpriseId)
				return errors.New("查询企业信息失败")
			}
			if disEnterprise.Id <= 0 {
				return errors.New("企业信息不存在")
			}
			dataSource = distribution_po.DisEnterpriseDataSourceSelf
			scrmEnterprise.SocialCreditCode = disEnterprise.SocialCreditCode
			scrmEnterprise.EnterpriseName = disEnterprise.EnterpriseName
			scrmEnterprise.Phone = utils.MobileDecrypt(disEnterprise.Phone)
			scrmEnterprise.Province = disEnterprise.Province
			scrmEnterprise.City = disEnterprise.City
			scrmEnterprise.District = disEnterprise.District
			scrmEnterprise.Address = disEnterprise.Address

		}
	}

	//先查询要变更的企业是否存在
	disShop := distribution_po.Shop{}
	disEnterprise := distribution_po.DisEnterprise{}
	ishave := false
	if req.OrgId == enum.BLKYOrgId {
		ishave, err = session.Table("eshop.dis_enterprise").Where("scrm_enterprise_id=?", cast.ToInt64(req.EnterpriseId)).Get(&disEnterprise)
		session.Where("enterprise_id=? and org_id=?", cast.ToInt64(req.EnterpriseId), req.OrgId).Get(&disShop)
	} else {
		ishave, err = session.Where("enterprise_id=? and org_id=?", cast.ToInt64(req.EnterpriseId), req.OrgId).Get(&disShop)
	}
	if err != nil {
		log.Error("查询企业信息失败，EnterpriseId=", req.EnterpriseId)
		return errors.New("查询企业信息失败")
	}

	Distributor := distribution_po.DisDistributor{}
	_, err = session.Where(" id=?", req.Id).Get(&Distributor)
	if err != nil {
		log.Error("分销员信息，查询异常=", err.Error())
		return errors.New("查询分销员信息失败")
	}

	session.Begin()
	//如果不存在，先创建店铺信息
	if !ishave {
		if req.OrgId == enum.BLKYOrgId {
			EncryptMobile := utils.MobileEncrypt(scrmEnterprise.Phone)
			StarMobile := utils.AddStar(scrmEnterprise.Phone) //加星星的手机号
			disEnterprise.ScrmEnterpriseId = cast.ToInt64(req.EnterpriseId)
			disEnterprise.EnterpriseName = scrmEnterprise.EnterpriseName
			disEnterprise.SocialCreditCode = scrmEnterprise.SocialCreditCode
			disEnterprise.DataSource = int(dataSource)
			disEnterprise.OrgId = req.OrgId
			disEnterprise.Province = scrmEnterprise.Province
			disEnterprise.City = scrmEnterprise.City
			disEnterprise.District = scrmEnterprise.District
			disEnterprise.Address = scrmEnterprise.Address
			disEnterprise.Phone = StarMobile
			disEnterprise.EncryptPhone = EncryptMobile
			_, err = session.Insert(&disEnterprise)
			if err != nil {
				session.Rollback()
				log.Error("更换企业，插入企业异常：e=" + err.Error())
				return errors.New("更换企业异常：e=" + err.Error())
			}
		}
		disShop.OrgId = req.OrgId
		disShop.IsSettedShop = 2
		disShop.IsMain = 2
		disShop.EnterpriseId = cast.ToInt64(req.EnterpriseId)
		_, err = session.Insert(&disShop)
		if err != nil {
			session.Rollback()
			log.Error("更换企业，插入店铺异常：e=" + err.Error())
			return errors.New("更换企业异常：e=" + err.Error())
		}
	}

	//先查询要变更的企业是否存在
	total := distribution_po.DisDistributorTotal{}
	ishave, err = session.Where("shop_id=? and org_id=? and dis_id=?", disShop.Id, req.OrgId, req.Id).Get(&total)
	if err != nil {
		log.Error("更换企业，查询异常=", err.Error())
		return errors.New("查询total失败")
	}

	//如果不存在，默认插入一个数据
	if !ishave {

		distotal := distribution_po.DisDistributorTotal{}
		distotal.MemberId = Distributor.MemberId
		distotal.ShopId = disShop.Id
		distotal.DisId = req.Id
		distotal.OrgId = req.OrgId
		_, err = session.Insert(&distotal)
		if err != nil {
			session.Rollback()
			log.Error("分销员入住失败：err=", err.Error())
			return err
		}

	}

	_, err = session.Exec("UPDATE dis_distributor SET shop_id=?,dis_role=2 WHERE id=?", disShop.Id, req.Id)
	if err != nil {
		session.Rollback()
		log.Error("更换企业，修改数据库异常：e=" + err.Error())
		return errors.New("更换企业异常：e=" + err.Error())
	}

	//判断dis_distributor_total表是否有记录。没有就插入一个

	//分销员下的所有客户关系记录更新过期时间为当前时间
	nowStr := time.Now().Format(utils.DateTimeLayout)
	_, err = session.Exec("UPDATE dis_distributor_fans SET expire_time=? WHERE dis_id =? AND expire_time>?", nowStr, req.Id, nowStr)
	if err != nil {
		log.Error("更换企业，分销员客户关系修改过期时间异常：e=" + err.Error())
		session.Rollback()
		return errors.New("更换企业，分销员客户关系修改过期时间异常：e=" + err.Error())
	}
	session.Commit()
	return nil
}

func (s DistributorManageService) DistributorAble(req distribution_vo.DisIdReq) error {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	//启用分销员
	_, err := session.Exec("UPDATE dis_distributor SET status=1,update_time=NOW() WHERE id=?", req.Id)
	if err != nil {
		log.Error("启用分销员操作，修改分销员状态异常：e=" + err.Error())
		return errors.New("启用分销员，分销员状态修改异常：e=" + err.Error())
	}

	return nil
}

// 获取分销员信息 ,用于分销员入住界面，查询分销员是否存在，存在的话，状态是什么
func (s DistributorManageService) DistributorGet(req distribution_vo.DistributorReq) (distribution_vo.DistributorPageData, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	//var info vo.DistributorPageData
	var info distribution_vo.DistributorPageData
	member := 0
	//用SCRMID去查
	if req.MemberId == 0 {
		_, err := session.SQL("select member_id from "+"upetmart.upet_member where scrm_user_id=?", req.ScrmUserId).Get(&member)
		if err != nil {
			log.Error("查询分销员失败：err=", err.Error())
			return info, err
		}
	}
	if member > 0 {
		req.MemberId = member
	}

	session.Select("dd.id,dd.member_id,dd.head_image,dd.name,dd.mobile,s.enterprise_id,dd.encrypt_mobile,dd.dis_role,dd.shop_id,dd.status,"+
		"en.enterprise_name ,en.code,en.enterprise_status,"+
		"dd.total_customer,s.wait_withdraw,"+
		"dd.order_num,dd.total_sales,dd.order_pay_num,dd.total_pay_sales,dd.unsettled_commission+dd.settled_commission as total_commission,dd.settled_commission,dd.unsettled_commission").
		Table("dis_distributor dd").
		Join("left", "shop s", "s.id=dd.shop_id").
		Join("left", "scrm_enterprise en", "en.id=s.enterprise_id")
	session.Where("dd.member_id = ? and dd.org_id=?", req.MemberId, req.OrgId)
	if req.ShopId > 0 {
		session.Where("dd.shop_id=?", req.ShopId)
	} else {
		session.OrderBy("dd.is_default desc,dd.create_time desc")
	}
	_, err := session.Get(&info)
	if err != nil {
		log.Error("查询分销员失败：err=", err.Error())
		return info, err
	}

	var temp []distribution_vo.BelongSalesmanTemp

	//查询所有的企业对应的业务员
	session.Select("b.enterprise_id,a.id belong_salesman_id,a.name as belong_salesman_name,a.org_name").Table("scrm_salesperson").Alias("a").
		Join("inner", "scrm_enterprise_salesperson_bind b", "b.salesperson_id = a.id ").
		Where("b.enterprise_id=?", cast.ToInt64(info.EnterpriseId))
	err = session.Find(&temp)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return info, err
	}
	for _, x := range temp {
		data := distribution_vo.BelongSalesmanData{}
		data.BelongSalesmanName = x.BelongSalesmanName
		data.BelongSalesmanId = x.BelongSalesmanId
		data.OrgName = x.OrgName
		info.BelongSalesman = append(info.BelongSalesman, data)
	}
	//_, err := session.Table("dis_distributor").Alias("a").
	//	Join("inner", "shop b", "b.id = a.shop_id ").
	//	Join("inner", "dis_salesman c", "c.id = a.belong_salesman_id ").
	//	Join("left", "dis_distributor_total d", "a.id = d.dis_id ").
	//	Where("a.org_id=? and a.shop_id=? and a.member_id=?", req.OrgId, req.ShopId, req.MemberId).
	//	Select("a.*,b.shop_name,c.name BelongSalesmanName,c.status salesman_status,d.settled_commission+d.unsettled_commission as total_commission,settled_commission,unsettled_commission,order_num,total_customer,wait_withdraw").
	//	Get(&info)
	//if err != nil {
	//	log.Error("查询分销员失败：err=", err.Error())
	//	return info, err
	//}

	return info, nil
}

// 百林康源 获取分销员信息 你好
func (s DistributorManageService) BLKYDistributorGet(req distribution_vo.DistributorReq) (distribution_vo.DistributorPageData, error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("百林康源 获取分销员信息：入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	session := s.Session
	var info distribution_vo.DistributorPageData
	member := 0
	//用SCRMID去查
	if req.MemberId == 0 {
		_, err := session.SQL("select member_id from "+"upetmart.upet_member where scrm_user_id=?", req.ScrmUserId).Get(&member)
		if err != nil {
			log.Error("查询分销员失败：err=", err.Error())
			return info, err
		}
	}
	if member > 0 {
		req.MemberId = member
	}
	selectStr := `a.id,a.member_id,a.head_image,a.name,a.real_name,a.mobile,a.encrypt_mobile,a.dis_role,a.shop_id,a.status,a.approve_state,a.reason,a.social_code_image,a.social_credit_code,
	b.total_customer,b.order_num,b.total_sales,b.withdraw_success,b.wait_withdraw,b.withdraw_apply,b.withdraw_success+b.withdraw_apply+b.wait_withdraw as total_commission,
	c.*,d.name as tuoke_salesman_name,a.tuoke_salesperson_id,a.in_system,
	f.enterprise_name,f.province as enterprise_province,f.city as enterprise_city,f.district,f.address,f.id AS enterprise_id,
	e.withdraw_success as shop_withdraw_success,e.withdraw_apply as shop_withdraw_apply,e.wait_withdraw as shop_wait_withdraw,e.withdraw_success+e.withdraw_apply+e.wait_withdraw as shop_total_commission`
	session.Select(selectStr).
		Table("dis_distributor a").
		Join("left", "dis_distributor_total b", "a.id=b.dis_id and a.shop_id=b.shop_id").
		Join("left", "dis_distributor_detail c", "a.id=c.dis_id").
		Join("left", "scrm_salesperson d", "a.tuoke_salesperson_id=d.id").
		Join("left", "shop e", "a.shop_id=e.id").
		Join("left", "dis_enterprise f", "e.enterprise_id=f.scrm_enterprise_id")
	session.Where("a.member_id = ? and a.org_id=?", req.MemberId, req.OrgId)
	_, err := session.Get(&info)
	if err != nil {
		log.Error(logPrefix, "查询分销员失败：err=", err.Error())
		return info, err
	}

	// 由于前端，即使是老板分销员和店员分销员， 取企业信息时， 还是取得Province字段，所以把真实的企所在省，市赋值
	if info.DisRole != distribution_po.DisRoleDoctor {
		info.Province = info.EnterpriseProvince
		info.City = info.EnterpriseCity
		info.WaitWithdraw = info.ShopWaitWithdraw
		info.WithdrawApply = info.ShopWithdrawApply
		info.WithdrawSuccess = info.ShopWithdrawSuccess
		info.TotalCommission = info.ShopTotalCommission
	}

	if info.DisRole == distribution_po.DisRoleStaff {
		monthStat, err := s.GetMonthStat(distribution_vo.GetMonthStatReq{
			DisId:       info.Id,
			ShopId:      info.ShopId,
			DisMemberId: info.MemberId,
			OrgId:       req.OrgId,
		})
		if err != nil {
			log.Error("获取店员分销员本月统计数据失败：err=", err.Error())
			return info, err
		}
		info.MonthOrderNum = monthStat.MonthOrderNum
		info.MonthCommission = cast.ToInt(monthStat.OrderAmount) - cast.ToInt(monthStat.DeductAmount)
	}
	// 只有当前分销员不是医生角色时，才需要判断是否曾经为医生角色
	info.IsShowBLKYDoctorOrder, err = new(distribution_po.DisDistributorTotal).IsBLKYDoctor(session, info.Id, req.OrgId)
	if err != nil {
		log.Error("获取分销员是否曾经为百林康源医生角色失败：err=", err.Error())
		return info, err
	}

	return info, nil
}

// 获取店员分销员本月统计数据
func (s DistributorManageService) GetMonthStat(req distribution_vo.GetMonthStatReq) (out distribution_vo.GetMonthStatData, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	// 本月第一天的日期
	firstDayOfMonth := time.Now().AddDate(0, 0, -time.Now().Day()+1).Unix()
	_, err = session.Table("upetmart.upet_orders").Alias("a").
		Select("count(1) as month_order_num, (sum(b.goods_pay_price * b.dis_commis_rate / 100 * 100)) as order_amount,sum(c.dis_commis_amount) as deduct_amount").
		Join("left", "upetmart.upet_order_goods b", "a.order_id=b.order_id and b.shop_id=?", req.ShopId).
		Join("left", "dis_distributor_swlm_cps c", "a.order_sn=c.order_sn and a.logistics_code=c.swlm").
		Where("a.is_dis=1 and a.add_time>=?", firstDayOfMonth).
		Where("a.store_id=?", req.OrgId).
		Where("a.order_demolition=0").
		Where("b.dis_member_id=?", req.DisMemberId).
		Where("b.is_dis=1").
		Get(&out)
	if err != nil {
		log.Error("获取店员分销员本月统计数据失败：err=", err.Error())
		return out, err
	}
	return

}

// 验证手机号是否存在
func (s DistributorManageService) DistributorMobileCheck(req viewmodel.BaseUserRequest) string {
	s.Begin()
	defer s.Close()

	session := s.Session
	var info distribution_po.DisDistributor
	EncryptMobile := utils.MobileEncrypt(req.Mobile)

	_, err := session.Table("dis_distributor").Alias("a").
		Where("a.org_id=? and a.member_id=?", req.OrgId, req.Id).
		Get(&info)
	if err != nil {
		log.Error("查询分销员失败：err=", err.Error())
		return "查询分销员失败"
	}

	if req.GetType == 1 && info.EncryptMobile != EncryptMobile {
		return "请输入当前绑定的手机号"
	}
	if req.GetType == 2 {
		if info.EncryptMobile == EncryptMobile {
			return "新旧手机号不能相同"
		}
		ishave, err := session.Table("dis_distributor").Alias("a").
			Where("a.org_id=? and a.encrypt_mobile=?", req.OrgId, EncryptMobile).
			Get(&info)
		if err != nil {
			log.Error("查询分销员失败：err=", err.Error())
			return "查询分销员失败"
		}

		if ishave {
			return "此手机号已有用户使用"
		}

	}

	return ""
}

// 润合云店 分销员入住
func (s DistributorManageService) DistributorInsert(req distribution_vo.DisDistributorAddReq) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	EncryptMobile := utils.MobileEncrypt(req.Mobile)
	//加星星的手机号
	StarMobile := utils.AddStar(req.Mobile)
	//加星星的身份证
	StarIdCard := utils.AddStar(req.IdCard)
	DisDistributor := distribution_po.DisDistributor{}
	copier.Copy(&DisDistributor, req)
	DisDistributor.Mobile = StarMobile
	DisDistributor.EncryptMobile = EncryptMobile
	DisDistributor.Status = 1
	DisDistributor.EncryptIdCard = utils.MobileEncrypt(req.IdCard)
	DisDistributor.IdCard = StarIdCard
	DisDistributor.SocialCodeImage = utils.MobileEncrypt(DisDistributor.SocialCodeImage)
	DisDistributor.IdcardFront = utils.MobileEncrypt(DisDistributor.IdcardFront)
	DisDistributor.IdcardReverse = utils.MobileEncrypt(DisDistributor.IdcardReverse)
	DisDistributor.TuokeSalespersonId = cast.ToInt64(req.TuokeSalespersonId)

	//如果是老板要校验证件
	if req.DisRole == 1 {
		var enterprise distribution_vo.ScrmEnterprise
		_, err := session.Where("id=? ", cast.ToInt64(req.EnterpriseId)).Select("id_card_no,social_credit_code,enterprise_type").Get(&enterprise)
		if err != nil {
			log.Error("分销员入驻失败：err=", err.Error())
			return errors.New("分销员入驻失败：e=" + err.Error())
		}

		//说明是个人企业，验证身份证
		if enterprise.EnterpriseType == 1 {
			if req.IdCard != enterprise.IdCardNo {
				return errors.New("证照信息有误,请修改。如确认无误请联系业务员处理")
			}

		} else { //验证企业统一社会信用代码
			if req.SocialCreditCode != enterprise.SocialCreditCode {
				return errors.New("证照信息有误,请修改。如确认无误请联系业务员处理")
			}
		}
	}

	//先查询店铺是否存在
	shop := distribution_po.Shop{}
	ishaveShop, err := session.Table("shop").Alias("a").
		Where("a.enterprise_id=? ", cast.ToInt64(req.EnterpriseId)).Get(&shop)
	if err != nil {
		log.Error("分销员入驻失败：err=", err.Error())
		return errors.New("分销员入驻失败：e=" + err.Error())
	}
	session.Begin()
	//如果存在，需要判断注册的是否是老板。并且老板是否存在
	if req.DisRole == 1 {
		item := distribution_po.DisDistributor{}
		ishave, err := session.Table("dis_distributor").Alias("a").
			Where("a.shop_id=? and a.dis_role=1", shop.Id).Get(&item)
		if err != nil {
			log.Error("分销员入驻失败：err=", err.Error())
			return errors.New(" e=" + err.Error())
		}
		if ishave {
			log.Error("分销员入驻失败:当前企业已有“老板”角色，请核实，或联系业务员确认，或选择注册店员“角色")
			return errors.New("当前企业已有“老板”角色，请核实，或联系业务员确认，或选择注册店员“角色")
		}
	}
	if !ishaveShop { //不存在的话，插入店铺数据
		disShop := distribution_po.Shop{}
		disShop.OrgId = req.OrgId
		disShop.IsSettedShop = 1
		disShop.IsSettedTime = time.Now()
		disShop.IsMain = 2
		disShop.EnterpriseId = cast.ToInt64(req.EnterpriseId)
		disShop.RegisteredSalesperson = cast.ToInt64(req.TuokeSalespersonId)
		//默认给小店数据
		disShop.ShopName = req.Name + "的小店"
		disShop.HeadImage = req.HeadImage
		disShop.Welcome = "欢迎来到我的小店，快来选购宠物用品吧"
		_, err = session.Insert(&disShop)
		if err != nil {
			session.Rollback()
			log.Error("分销员入驻失败，插入店铺异常：e=" + err.Error())
			return errors.New("e=" + err.Error())
		}
		shop = disShop

		//如果没有设置过店铺，需要默认插入所有的分销商品到店铺
		var goodIds []int
		session.SQL("select goods_id from upetmart.upet_goods where store_id = 3 and goods_state =1 and goods_verify=1 and is_dis =1")
		err = session.Find(&goodIds)
		if err != nil {
			return errors.New("设置店铺信息失败：err=" + err.Error())
		}

		//添加商品记录，同步到es
		err = SyncEsGoods(session, goodIds, shop.Id, enum.SyncEsSetShopEnum)
		if err != nil {
			return errors.New(err.Error())
		}
	}

	DisDistributor.ShopId = shop.Id
	_, err = session.Insert(&DisDistributor)

	if err != nil {
		session.Rollback()
		log.Error("分销员入驻失败：err=", err.Error())
		return err
	}
	o := DisSalesmanService{}
	o.Session = session
	reqpar := distribution_vo.DisSalesman{}
	reqpar.Id = req.TuokeSalespersonId
	err = o.SaleManAddNum(reqpar)
	if err != nil {
		session.Rollback()
		log.Error("分销员入驻失败：err=", err.Error())
		return err
	}
	distotal := distribution_po.DisDistributorTotal{}
	distotal.MemberId = req.MemberId
	distotal.ShopId = shop.Id
	distotal.DisId = DisDistributor.Id
	distotal.OrgId = req.OrgId
	_, err = session.Insert(&distotal)
	if err != nil {
		session.Rollback()
		log.Error("分销员入驻失败：err=", err.Error())
		return err
	}

	session.Commit()
	return nil
}

func (s DistributorManageService) OcrpPrediction(session *xorm.Engine, realName, socialCodeImage, mobile string) (approveState int8, inSystem int8, veterinaryCode string, err error) {
	engine := s.DbDmMdm()
	defer engine.Close()

	if realName == "" {
		err = errors.New("真实姓名不能为空")
		return
	}
	if socialCodeImage == "" {
		err = errors.New("资质证明不能为空")
		return
	}

	if mobile == "" {
		err = errors.New("手机号不能为空")
		return
	}

	var staffMobileMap []map[string]string
	if staffMobileMap, err = engine.QueryString(fmt.Sprintf("select * from dm_mdm.shr_t_staff_info where mobile = '%s' and status = 2", mobile)); err != nil {
		log.Error("OcrpPrediction 查询员工信息失败:", err.Error())
		return
	}

	if len(staffMobileMap) > 0 {
		if staffMobileMap[0]["real_name"] != realName {
			err = errors.New("您输入的姓名和体系内姓名不一致，请重新输入")
			return
		}
		inSystem = 1
	}

	var dataJson = make(map[string]interface{})
	dataJson["orgid"] = "2"                                          //必须写"2"
	dataJson["reg_id"] = ""                                          //可以放一些内容，比如某些唯一标识，或者为空
	dataJson["key"] = []string{"image"}                              //固定
	dataJson["value"] = map[string]string{realName: socialCodeImage} //名字：图片url  目前只支持一张图片
	dataStr, _ := json.Marshal(dataJson)
	//将执业兽医师资格证进行验证
	var res []byte
	//res, err = utils.HttpPost(config.Get("ocr_domain")+"/ocr/prediction", dataStr, "")
	res, err = utils.HttpPost("https://ocr-test-medical-documents.rp-field.com/ocr/prediction", dataStr, "")
	log.Info("OcrpPrediction 远程调ocr接口返回数据:", res, err)
	if err != nil {
		return
	}

	var resJson distribution_vo.OcrPredictionResult
	err = json.Unmarshal(res, &resJson)
	if err != nil {
		return
	}

	approveState = disdistributor.ApproveStateFail
	if resJson.ErrNo == 0 { //0:正常；其他值异常
		//要求一定有返回兽医资格证编号
		for _, v := range resJson.Key {
			if strings.Contains(v, "编号:") {
				veterinaryCode = strings.Split(v, ":")[1]
				break
			}
		}

		if veterinaryCode == "" {
			err = errors.New("未识别到兽医资格证编号")
			return
		}

		//判断这个兽医资格证是否已存在
		var distributorItemList []*distribution_vo.SyncDistributor
		if err = session.Table("eshop.dis_distributor").Select("id,org_id,encrypt_mobile,veterinary_code,in_system").Where("org_id = 4 and approve_state in (1,2)  and veterinary_code = ?", veterinaryCode).Find(&distributorItemList); err != nil {
			log.Error("OcrpPrediction 查询员工信息失败:", err.Error())
			return
		}

		for _, distributorItem := range distributorItemList {
			checkMobile := utils.MobileDecrypt(distributorItem.EncryptMobile)
			if mobile != checkMobile {
				err = errors.New("兽医资格证已被认证使用，请确保是您本人上传")
				return
			}
		}

		//判断 兽医资格证编号是否在体系内
		var staffVeterinaryCodeMap []map[string]string
		if staffVeterinaryCodeMap, err = engine.QueryString(fmt.Sprintf("select * from dm_mdm.shr_t_staff_info where vet_id_number = '%s' and status = 2", veterinaryCode)); err != nil {
			log.Error("OcrpPrediction 查询员工信息失败:", err.Error())
			return
		}
		if len(staffVeterinaryCodeMap) > 0 {
			inSystem = 1
		}

		//判断是否识别到 1、兽医师资格证书 ；2、中华人民共和国
		keyStr := strings.Join(resJson.Key, ",")
		if strings.Contains(keyStr, "兽医师资格证书") && strings.Contains(keyStr, "中华人民共和国") {
			//再判断是否识别到名字
			if resJson.Value[realName] == "1" {
				approveState = disdistributor.ApproveStatePass
			}
		}
	}

	//如果识别不通过，则veterinaryCode置空
	//if approveState != disdistributor.ApproveStatePass {
	//	veterinaryCode = ""
	//}

	//验证失败次数,如果第三次都不能通过，则将用户填写的内容传输到后台，变成人工审核
	if approveState == disdistributor.ApproveStateFail {
		redisKey := fmt.Sprintf("ocrPredictionErrNum:%s", realName)
		redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
		ocrPredictionErrArr := redisConn.Get("eShop:distributor", redisKey)

		if len(ocrPredictionErrArr) == 1 {
			ocrPredictionErrNum := cast.ToInt(ocrPredictionErrArr[0])
			if ocrPredictionErrNum >= 2 {
				approveState = disdistributor.ApproveStateWait
			} else {
				ocrPredictionErrNum++
				redisConn.Save("eShop:distributor", redisKey, cast.ToInt(ocrPredictionErrNum), time.Second*3600)
				return
			}
		} else {
			err = errors.New("redis数据错误")
			return
		}

	}

	return
}

// ocr识别营业执照上的社会信用代码和企业名称
func (s DistributorManageService) OcrpPredictionSocialCode(req distribution_vo.OcrpPredictionSocialCodeReq) (out distribution_vo.OcrpPredictionSocialCodeData, err error) {
	logPrefix := fmt.Sprintf("ocr识别营业执照上的社会信用代码和企业名称:营业执照图片=%s", req.SocialCodeImage)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	engine := s.DbDmMdm()
	defer engine.Close()

	if req.SocialCodeImage == "" {
		err = errors.New("营业执照不能为空")
		return
	}

	var dataJson = make(map[string]interface{})
	dataJson["orgid"] = "3"                                          //必须写"2"
	dataJson["reg_id"] = ""                                          //可以放一些内容，比如某些唯一标识，或者为空
	dataJson["key"] = []string{"image"}                              //固定
	dataJson["value"] = map[string]string{"编码": req.SocialCodeImage} //名字：图片url  目前只支持一张图片
	dataStr, _ := json.Marshal(dataJson)
	log.Info(logPrefix, "OcrpPredictionSocialCode 入参：", utils.InterfaceToJSON(dataJson))
	//将营业执照进行验证
	var res []byte
	//res, err = utils.HttpPost(config.Get("ocr_domain")+"/ocr/prediction", dataStr, "")
	// todo cls ocr预测营业执照上的社会信用代码和企业名称 需要换域名
	res, err = utils.HttpPost("https://ocr-test-medical-documents.rp-field.com/ocr/prediction", dataStr, "")
	log.Info(logPrefix, "OcrpPredictionSocialCode 远程调ocr接口返回数据:", res, err)
	if err != nil {
		return
	}

	var resJson distribution_vo.OcrPredictionResult
	err = json.Unmarshal(res, &resJson)
	if err != nil {
		return
	}

	if resJson.ErrNo != 0 {
		log.Error(logPrefix, "ocr识别营业执照上的社会信用代码和企业名称失败:", resJson.ErrMsg)
		err = errors.New("ocr识别营业执照上的社会信用代码和企业名称失败")
		return
	}

	//要求一定有返回兽医资格证编号
	for _, v := range resJson.Key {
		// todo cls
		if strings.Contains(v, "统一社会信用代码:") {
			out.SocialCreditCode = strings.Split(v, ":")[1]
			break
		}
	}

	if out.SocialCreditCode == "" {
		log.Error(logPrefix, "未识别到社会信用代码，可选择手动输入或再次拍照上传")
		err = errors.New("未识别到社会信用代码，可选择手动输入或再次拍照上传")
		return
	}

	// 根据社会信用代码查询企业名称
	// 查询云订货企业信息
	scrmEnterprises, err := new(distribution_po.ScrmEnterprise).GetScrmEnterprises(session, distribution_po.GetScrmEnterprisesReq{
		SocialCreditCode: out.SocialCreditCode,
	})
	if err != nil {
		log.Error(logPrefix, "查询企业信息失败:", err.Error())
		return
	}

	if len(scrmEnterprises) > 0 {
		if len(scrmEnterprises) != 1 {
			log.Error(logPrefix, "根据社会信用代码查出多个企业信息")
			err = errors.New("根据社会信用代码查出多个企业信息")
			return
		}
		out.EnterpriseName = scrmEnterprises[0].EnterpriseName

	}
	// 如果企业名称为空，则根据社会信用代码查企查查 查出企业名称
	if out.EnterpriseName == "" {
		enterpriseName, e := utils.GetEnterpriseNameBySocialCreditCode(out.SocialCreditCode)
		// todo cls 暂时屏蔽报错，返回固定值
		if e != nil {
			log.Error(logPrefix, "社会信用代码不存在:", e.Error())
			err = errors.New("社会信用代码不存在")
			return
		}
		out.EnterpriseName = enterpriseName
	}
	if len(out.EnterpriseName) == 0 {
		err = errors.New("未识别到社会信用代码，可选择手动输入或再次拍照上传")
		return
	}
	return
}

func (s DistributorManageService) EnterpriseBySocialCode(req distribution_vo.EnterpriseBySocialCodeReq) (out distribution_vo.OcrpPredictionSocialCodeData, err error) {
	logPrefix := fmt.Sprintf("根据社会信用代码查询企业名称:社会信用代码=%s", req.SocialCreditCode)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	if len(req.SocialCreditCode) == 0 {
		err = errors.New("社会信用代码不能为空")
		return
	}
	scrmEnterprises, err := new(distribution_po.ScrmEnterprise).GetScrmEnterprises(session, distribution_po.GetScrmEnterprisesReq{
		SocialCreditCode: req.SocialCreditCode,
	})
	if err != nil {
		log.Error(logPrefix, "查询企业信息失败:", err.Error())
		return
	}

	if len(scrmEnterprises) > 0 {
		if len(scrmEnterprises) != 1 {
			log.Error(logPrefix, "根据社会信用代码查出多个企业信息")
			err = errors.New("根据社会信用代码查出多个企业信息")
			return
		}
		out.EnterpriseName = scrmEnterprises[0].EnterpriseName
	}

	if out.EnterpriseName == "" {
		enterpriseName, e := utils.GetEnterpriseNameBySocialCreditCode(req.SocialCreditCode)
		if e != nil {
			log.Error(logPrefix, "社会信用代码不存在:", e.Error())
			err = errors.New("社会信用代码不存在")
			return
		}
		out.EnterpriseName = enterpriseName
	}

	if len(out.EnterpriseName) == 0 {
		err = errors.New("未识别到社会信用代码")
		return
	}
	return
}

func (s DistributorManageService) CheckEnterpriseBinded(req distribution_vo.CheckEnterpriseBindedReq) (out string, err error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	out, err = new(distribution_po.DisDistributor).CheckSocialCreditCodeUsed(session, req.SocialCreditCode, req.Mobile)
	if err != nil {
		return
	}

	return
}

func (s DistributorManageService) GetEnterprisesByPhone(mobile string) (out []distribution_vo.ScrmEnterpriseInfo, err error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	scrmEnterprises, err := new(distribution_po.ScrmEnterprise).GetScrmEnterprises(session, distribution_po.GetScrmEnterprisesReq{
		Phone: mobile,
	})
	if err != nil {
		return nil, err
	}

	for _, scrmEnterprise := range scrmEnterprises {
		out = append(out, distribution_vo.ScrmEnterpriseInfo{
			EnterpriseId:     scrmEnterprise.Id,
			EnterpriseName:   scrmEnterprise.EnterpriseName,
			SocialCreditCode: scrmEnterprise.SocialCreditCode,
			Province:         scrmEnterprise.Province,
			City:             scrmEnterprise.City,
			District:         scrmEnterprise.District,
			Address:          scrmEnterprise.Address,
		})
	}

	return out, nil
}

// 百林康源 医生身份 分销员入驻
func (s DistributorManageService) BLKYDistributorInsert(req distribution_vo.DisDistributorAddReq) (data distribution_vo.DisDistributorAddData, err error) {
	var (
		inSystem       int8
		veterinaryCode string
	)
	logPrefix := fmt.Sprintf("百林康源 分销员入驻:会员id=%d,手机号=%s", req.MemberId, req.Mobile)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(req))
	s.Begin()
	defer s.Close()

	disDistributorStatus := disdistributor.StatusNotOpen
	if req.SocialCodeImage != "" {
		data.ApproveState, inSystem, veterinaryCode, err = s.OcrpPrediction(s.Engine, req.RealName, req.SocialCodeImage, req.Mobile)
		if err != nil {
			log.Error(logPrefix, "插入分销员数据失败，err=", err.Error())
			err = errors.New(err.Error())
			return
		}

		//如果是审核失败，直接返回告诉前端，弹窗提醒用户
		if data.ApproveState == disdistributor.ApproveStateFail {
			return
		} else if data.ApproveState == disdistributor.ApproveStatePass {
			//如果是审核通过
			disDistributorStatus = disdistributor.StatusValid
		}
	}

	session := s.Engine.NewSession()
	defer session.Close()
	var tuokeSalespersonId int64
	if len(req.TuokeSalesmanName) > 0 {
		//查询邀请人信息
		salesperson := make([]distribution_po.ScrmSalesperson, 0)
		if err = session.Table("eshop.scrm_salesperson").Select("id,name").Where("name = ?", req.TuokeSalesmanName).Find(&salesperson); err != nil {
			log.Error(logPrefix, "推荐人不存在，err=", err.Error())
			err = errors.New("推荐人不存在")
			return
		} else if len(salesperson) == 0 {
			err = errors.New("邀请人信息不存在")
			return
		}
		log.Info(logPrefix, "邀请人信息：", utils.InterfaceToJSON(salesperson))
		tuokeSalespersonId = salesperson[0].Id
	}

	EncryptMobile := utils.MobileEncrypt(req.Mobile)
	//加星星的手机号
	StarMobile := utils.AddStar(req.Mobile)
	DisDistributor := distribution_po.DisDistributor{
		OrgId:              req.OrgId,
		ShopId:             0,
		MemberId:           req.MemberId,
		DisRole:            disdistributor.DisRoleDoctor,
		Status:             cast.ToInt8(disDistributorStatus),
		ApproveState:       data.ApproveState,
		Name:               req.Name,
		RealName:           req.RealName,
		Mobile:             StarMobile,
		EncryptMobile:      EncryptMobile,
		HeadImage:          req.HeadImage,
		SocialCodeImage:    req.SocialCodeImage,
		InSystem:           inSystem,
		VeterinaryCode:     veterinaryCode,
		TuokeSalespersonId: tuokeSalespersonId,
	}
	if data.ApproveState == disdistributor.ApproveStatePass {
		//如果是审核通过
		DisDistributor.ApproveTime = time.Now()
	}

	DisDistributorDetail := distribution_po.DisDistributorDetail{}
	copier.Copy(&DisDistributorDetail, req)
	DisDistributorTotal := distribution_po.DisDistributorTotal{OrgId: req.OrgId, MemberId: req.MemberId}

	session.Begin()
	if _, err = session.Insert(&DisDistributor); err != nil {
		log.Error(logPrefix, "插入分销员数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}
	DisDistributorDetail.DisId = DisDistributor.Id
	if _, err = session.Insert(&DisDistributorDetail); err != nil {
		log.Error(logPrefix, "插入分销员详情数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}

	DisDistributorTotal.DisId = DisDistributor.Id
	if _, err = session.Insert(&DisDistributorTotal); err != nil {
		log.Error(logPrefix, "插入分销员统计数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}

	session.Commit()
	return
}

// 宠利扫 分销员入驻（老板身份）
func (s DistributorManageService) CLSBossDistributorInsert(req distribution_vo.DisDistributorAddReq) (data distribution_vo.DisDistributorAddData, err error) {
	var (
		inSystem                int8                                                // 分销员是否在系统内
		disEnterpriseDataSource int   = distribution_po.DisEnterpriseDataSourceSelf // 企业数据来源：1-自建 2-云订货
		tuokeSalespersonId      int64                                               // 邀请人id
	)
	logPrefix := fmt.Sprintf("宠利扫 老板入驻:会员id=%d,手机号=%s", req.MemberId, req.Mobile)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(req))
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	engine := s.DbDmMdm()
	defer engine.Close()

	if req.TuokeSalesmanName != "" {
		// 查询邀请人信息
		tuokeSalespersonId, err = new(distribution_po.ScrmSalesperson).GetSalespersonId(session, distribution_po.GetSalespersonInfoReq{
			Name: req.TuokeSalesmanName,
		})
		if err != nil {
			log.Error(logPrefix, "推荐人不存在，err=", err.Error())
			err = errors.New("推荐人不存在")
			return
		}
	}

	// 查询企业信息
	disEnterpriseInfo, shopInfo, err := new(distribution_po.DisEnterprise).CheckEnterpriseExist(session, distribution_po.CheckEnterpriseExistReq{
		SocialCreditCode: req.SocialCreditCode,
		DisId:            0,
		OrgId:            req.OrgId,
	})
	if err != nil {
		log.Error(logPrefix, "查询企业信息失败，err=", err.Error())
		return
	}

	// 判断员工是否存在于SHR系统
	inSystem, err = external_po.DisIsInSystem(engine, req.Mobile, req.RealName, req.SocialCreditCode)
	if err != nil {
		log.Error(logPrefix, "判断员工是否存在于SHR系统失败，err=", err.Error())
		err = errors.New("判断员工是否存在于SHR系统失败" + err.Error())
		return
	}

	// 如果企业id不为空，则该企业是否存在于eshop.scrm_enterprise
	if req.EnterpriseId != "" {
		if scrmEnterprises, e := new(distribution_po.ScrmEnterprise).GetScrmEnterprises(session, distribution_po.GetScrmEnterprisesReq{
			Id: cast.ToInt64(req.EnterpriseId),
		}); e != nil {
			log.Error(logPrefix, "查询云订货企业信息失败：err=", e.Error())
			err = errors.New("查询云订货企业信息失败：e=" + e.Error())
			return
		} else if len(scrmEnterprises) == 0 {
			err = errors.New("云订货企业信息不存在")
			return
		}
		disEnterpriseDataSource = distribution_po.DisEnterpriseDataSourceCloud
	}

	// 组装分销员数据
	EncryptMobile := utils.MobileEncrypt(req.Mobile)
	StarMobile := utils.AddStar(req.Mobile) //加星星的手机号
	DisDistributor := distribution_po.DisDistributor{
		OrgId:              req.OrgId,
		ShopId:             0,
		MemberId:           req.MemberId,
		DisRole:            req.DisRole,
		Status:             disdistributor.StatusValid,
		ApproveState:       disdistributor.ApproveStatePass,
		Name:               req.Name,
		RealName:           req.RealName,
		Mobile:             StarMobile,
		EncryptMobile:      EncryptMobile,
		HeadImage:          req.HeadImage,
		SocialCodeImage:    utils.MobileEncrypt(req.SocialCodeImage),
		InSystem:           inSystem,
		SocialCreditCode:   req.SocialCreditCode,
		ApproveTime:        time.Now(),
		TuokeSalespersonId: tuokeSalespersonId,
	}

	// 组装宠利扫企业数据
	disEnterpriseInfo.OrgId = req.OrgId
	disEnterpriseInfo.ScrmEnterpriseId = cast.ToInt64(req.EnterpriseId)
	disEnterpriseInfo.EnterpriseName = req.EnterpriseName
	disEnterpriseInfo.SocialCreditCode = req.SocialCreditCode
	disEnterpriseInfo.Province = req.Province
	disEnterpriseInfo.City = req.City
	disEnterpriseInfo.District = req.District
	disEnterpriseInfo.Address = req.Address
	disEnterpriseInfo.Phone = StarMobile
	disEnterpriseInfo.EncryptPhone = EncryptMobile
	disEnterpriseInfo.DataSource = int(disEnterpriseDataSource)

	// 组装店铺数据
	shopInfo.ShopName = req.EnterpriseName
	shopInfo.OrgId = req.OrgId

	DisDistributorDetail := new(distribution_po.DisDistributorDetail)
	copier.Copy(DisDistributorDetail, req)
	DisDistributorTotal := new(distribution_po.DisDistributorTotal)
	DisDistributorTotal.OrgId = req.OrgId
	DisDistributorTotal.MemberId = req.MemberId

	session.Begin()
	err = disEnterpriseInfo.UpdateOrCreate(session)
	if err != nil {
		log.Error(logPrefix, "更新或创建企业数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}

	shopInfo.EnterpriseId = disEnterpriseInfo.ScrmEnterpriseId

	if shopInfo.Id <= 0 {
		if _, err = session.Insert(&shopInfo); err != nil {
			log.Error(logPrefix, "插入店铺数据失败，err=", err.Error())
			session.Rollback()
			err = errors.New(err.Error())
			return
		}
	}
	DisDistributor.ShopId = shopInfo.Id
	if _, err = session.Insert(&DisDistributor); err != nil {
		log.Error(logPrefix, "插入分销员数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}
	DisDistributorDetail.DisId = DisDistributor.Id
	if err = DisDistributorDetail.Insert(session); err != nil {
		log.Error(logPrefix, "插入分销员详情数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}

	DisDistributorTotal.DisId = DisDistributor.Id
	DisDistributorTotal.ShopId = shopInfo.Id
	if err = DisDistributorTotal.Insert(session, DisDistributor.Id, req.OrgId, shopInfo.Id, req.MemberId); err != nil {
		log.Error(logPrefix, "插入分销员统计数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}

	session.Commit()

	// 执行积分的更新
	go func(mobile string, disId int) {
		pointsFlowSrv := points_service.NewClsPointsFlowService()
		disMobileMap := make(map[int]string)
		disMobileMap[disId] = mobile
		pointsFlowSrv.UpdatebyStaffNo(session, disMobileMap)
	}(req.Mobile, DisDistributor.Id)

	return
}

// 成为宠利扫店员（使用场景：用户扫码老板邀请二维码，但该用户可能已经注册过宠利扫小程序）
func (s DistributorManageService) CLSBeWorkerDistributor(req distribution_vo.DisDistributorAddReq) (data distribution_vo.DisDistributorAddData, err error) {
	var (
		inSystem int8
	)
	logPrefix := fmt.Sprintf("宠利扫 店员入驻:会员id=%d,手机号=%s", req.MemberId, req.Mobile)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(req))
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	engine := s.DbDmMdm()
	defer engine.Close()

	// 检查邀请码是否有效
	inviteData := s.GetBossInviteInfo(req.InviteCode)
	if inviteData.BossDisId == 0 {
		err = errors.New("该邀请码已失效,请联系老板重新获取")
		return
	}
	// if inviteData.Phone != req.Mobile {
	// 	err = errors.New("该邀请码已被其他分销员使用,请联系老板重新获取")
	// 	return
	// }
	// 获取老板分销员信息
	bossDis := new(distribution_po.DisDistributor)
	if err = bossDis.CheckBossDisInfo(session, inviteData.BossDisId, req.OrgId); err != nil {
		log.Error(logPrefix, "检查老板信息失败，err=", err.Error())
		return
	}

	// 判断该手机号是否已经是分销员医生角色
	disInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{
		"encryptMobile": utils.MobileEncrypt(req.Mobile),
		"orgId":         req.OrgId,
	})
	if err != nil {
		log.Error(logPrefix, "查询分销员数据失败，err=", err.Error())
		err = errors.New("查询分销员数据失败")
		return
	}

	if disInfo.Id > 0 && disInfo.ShopId > 0 && disInfo.DisRole == disdistributor.DisRoleBoss {
		err = errors.New("您已注册宠利扫小程序店老板，如需解绑成为当前企业的店员，请联系客服进行解绑")
		return
	}
	if disInfo.Id > 0 && disInfo.ShopId > 0 && disInfo.DisRole == disdistributor.DisRoleWorker {
		err = errors.New("您已加入其他企业，如需绑定新企业，请前往“宠利扫”小程序中【我的】-【解绑当前企业】操作解绑")
		return
	}

	// 判断员工是否存在于SHR系统
	inSystem, err = external_po.DisIsInSystem(engine, req.Mobile, req.RealName, bossDis.SocialCreditCode)
	if err != nil {
		log.Error(logPrefix, "判断员工是否存在于SHR系统失败，err=", err.Error())
		err = errors.New("判断员工是否存在于SHR系统失败" + err.Error())
		return
	}

	EncryptMobile := utils.MobileEncrypt(req.Mobile)
	//加星星的手机号
	StarMobile := utils.AddStar(req.Mobile)

	disInfo.ShopId = bossDis.ShopId
	disInfo.DisRole = req.DisRole
	disInfo.Status = cast.ToInt8(disdistributor.StatusValid)
	disInfo.ApproveState = disdistributor.ApproveStatePass
	disInfo.Name = req.Name
	disInfo.RealName = req.RealName
	disInfo.Mobile = StarMobile
	disInfo.EncryptMobile = EncryptMobile
	disInfo.HeadImage = req.HeadImage
	disInfo.InSystem = inSystem
	disInfo.ApproveTime = time.Now()
	disInfo.OrgId = req.OrgId
	disInfo.MemberId = req.MemberId

	DisDistributorDetail := new(distribution_po.DisDistributorDetail)
	copier.Copy(DisDistributorDetail, req)
	session.Begin()
	if err = disInfo.UpdateOrCreate(session); err != nil {
		log.Error(logPrefix, "更新或创建分销员数据失败，err=", err.Error(), "disInfo=", utils.InterfaceToJSON(disInfo))
		session.Rollback()
		err = errors.New("店员认证失败")
		return
	}
	DisDistributorDetail.DisId = disInfo.Id
	if err = DisDistributorDetail.Insert(session); err != nil {
		log.Error(logPrefix, "插入分销员详情数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New("店员认证失败")
		return
	}

	if err = new(distribution_po.DisDistributorTotal).Insert(session, disInfo.Id, req.OrgId, bossDis.ShopId, req.MemberId); err != nil {
		log.Error(logPrefix, "插入分销员统计数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}

	session.Commit()

	// 执行积分的更新
	go func(mobile string, disId int) {
		pointsFlowSrv := points_service.NewClsPointsFlowService()
		disMobileMap := make(map[int]string)
		disMobileMap[disId] = mobile
		pointsFlowSrv.UpdatebyStaffNo(session, disMobileMap)
	}(req.Mobile, disInfo.Id)

	// 构建Redis key
	// redisKey := fmt.Sprintf("bossInvite:%d", req.InviteCode)

	// // 获取Redis连接
	// redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	// redisConn.Delete(string(cache_source.EShop), redisKey)

	return

}

// 老板邀请注册店员二维码
// 老板专属二维码，每次只能成功邀请一名店员，当邀请的第一个店员已识别且注册中或认证完成后，同一个链接分享给其他人时，点击进入的页面提示“该邀请码已失效，请联系老板重新获取”
func (s *DistributorManageService) BossInviteDistributor(in distribution_vo.BossInviteDistributorReq) (inviteCode int64, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("老板邀请注册店员二维码:入参=%s", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	member := 0
	//用SCRMID去查
	if in.MemberId == 0 {
		_, err = session.SQL("select member_id from upetmart.upet_member where scrm_user_id=?", in.ScrmUserId).Get(&member)
		if err != nil {
			log.Error(logPrefix, "查询会员信息失败：err=", err.Error())
			err = errors.New("查询会员信息失败")
			return
		}
	}
	if member > 0 {
		in.MemberId = member
	}

	bossDisInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{
		"memberId": in.MemberId,
		"orgId":    in.OrgId,
	})
	if err != nil {
		log.Error(logPrefix, "查询老板分销员信息失败：err=", err.Error())
		err = errors.New("查询老板分销员信息失败")
		return
	}
	if bossDisInfo.Id == 0 {
		log.Error(logPrefix, "老板分销员信息不存在")
		err = errors.New("老板分销员信息不存在")
		return
	}
	inviteData := distribution_vo.InviteData{
		BossDisId: bossDisInfo.Id,
		Phone:     "",
		EndTime:   time.Now().Add(time.Hour * 48).Unix(),
	}
	// 生成随机邀请码
	inviteCode = utils.GenerateRandomInt(1000000000, 9999999999)

	// 构建Redis key
	redisKey := fmt.Sprintf("bossInvite:%d", inviteCode)

	// 获取Redis连接
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	// 将数据存入Redis，设置24小时过期
	redisConn.Save(string(cache_source.EShop), redisKey, utils.InterfaceToJSON(inviteData), time.Hour*48)

	return inviteCode, nil
}

// CheckBossInviteCode 检查老板邀请码
// 返回值: disId-老板分销员ID, enterpriseInfo-企业信息, err-错误信息
func (s *DistributorManageService) CheckBossInviteCode(in distribution_vo.CheckBossInviteCodeReq) (out distribution_vo.CheckBossInviteCodeData, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("检查老板邀请码:入参=%s", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	inviteData := s.GetBossInviteInfo(in.InviteCode)
	if inviteData.BossDisId == 0 {
		out.IsUsed = 1
		return
	}
	log.Info(logPrefix, "邀请码数据:", utils.InterfaceToJSON(inviteData))

	out.BossDisId = inviteData.BossDisId
	// 暂时去除一个老板分销码只能一个店员扫的限制
	// if inviteData.Phone != "" && inviteData.Phone != in.Phone {
	// 	out.IsUsed = 1
	// 	return
	// }
	// inviteData.Phone = in.Phone
	// log.Info(logPrefix, "邀请码数据:", utils.InterfaceToJSON(inviteData))
	// // 修改redis的值， 有效期为原来剩余的有效期
	// t := inviteData.EndTime - time.Now().Unix()
	// if t < 0 {
	// 	out.IsUsed = 1
	// 	return
	// }
	// // 获取Redis连接
	// redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	// // 查找对应的邀请码记录
	// key := fmt.Sprintf("bossInvite:%d", in.InviteCode)
	// redisConn.Save(string(cache_source.EShop), key, utils.InterfaceToJSON(inviteData), time.Duration(t)*time.Second)

	disEnterprise, err := new(distribution_po.DisDistributor).GetDistributorEnterpriseInfo(session, out.BossDisId)
	if err != nil {
		log.Error(logPrefix, "查询分销员企业信息失败:", err.Error())
		return
	}
	out.EnterpriseName = disEnterprise.EnterpriseName

	return
}

// 老板分销员将店员分销员移出企业
func (s *DistributorManageService) RemoveDistributor(in distribution_vo.RemoveDistributorReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("老板分销员将店员分销员移出企业:入参=%s", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	disDistributor := new(distribution_po.DisDistributor)
	disDistributorInfo, err := disDistributor.GetDisInfoByMemberIdOrScrmUserId(session, distribution_po.GetDisInfoReq{
		MemberId:   in.MemberId,
		ScrmUserId: in.ScrmUserId,
		OrgId:      in.OrgId,
	})
	if err != nil {
		log.Error(logPrefix, "查询分销员信息失败:", err.Error())
		return err
	}
	err = distribution_po.CheckBossDisValid(disDistributorInfo)
	if err != nil {
		log.Error(logPrefix, "分销员信息不合法:", err.Error())
		return err
	}
	if disDistributorInfo.ShopId == 0 {
		log.Error(logPrefix, "分销员信息不合法:店铺id不能为0")
		return errors.New("分销员信息不合法:店铺id不能为0")
	}

	log.Info(logPrefix, "老板分销员信息合法:", utils.InterfaceToJSON(disDistributorInfo))
	workerDisInfo, err := distribution_po.GetDisInfo(s.Engine, map[string]interface{}{
		"id":     in.DisId,
		"orgId":  in.OrgId,
		"shopId": disDistributorInfo.ShopId,
	})
	if err != nil {
		log.Error(logPrefix, "查询店员分销员信息失败:", err.Error())
		return err
	}
	if workerDisInfo.Id == 0 {
		log.Error(logPrefix, "店员分销员信息不存在")
		return errors.New("店员分销员信息不存在")
	}
	_, err = disDistributor.RemoveFromEnterprise(session, workerDisInfo.Id, in.OrgId)
	if err != nil {
		log.Error(logPrefix, "更新店员分销员信息失败:", err.Error())
		return err
	}
	return

}

// 店员自己解绑所在企业
func (s *DistributorManageService) SelfRemoveEnterprise(in distribution_vo.SelfRemoveEnterpriseReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("店员自己解绑所在企业:入参=%s", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	disDistributor := new(distribution_po.DisDistributor)
	disDistributorInfo, err := disDistributor.GetDisInfoByMemberIdOrScrmUserId(session, distribution_po.GetDisInfoReq{
		MemberId:   in.MemberId,
		ScrmUserId: in.ScrmUserId,
		OrgId:      in.OrgId,
	})
	if err != nil {
		log.Error(logPrefix, "查询分销员信息失败:", err.Error())
		return err
	}
	if disDistributorInfo.Id == 0 {
		log.Error(logPrefix, "分销员信息不存在")
		return errors.New("分销员信息不存在")
	}
	// if disDistributorInfo.ShopId == 0 {
	// 	log.Error(logPrefix, "分销员信息不合法:店铺id不能为0")
	// 	return errors.New("不是店员分销员")
	// }

	_, err = disDistributor.RemoveFromEnterprise(session, disDistributorInfo.Id, in.OrgId)

	if err != nil {
		log.Error(logPrefix, "更新分销员信息失败:", err.Error())
		return err
	}
	return
}

func (s *DistributorManageService) GetBossInviteInfo(inviteCode int64) (out distribution_vo.InviteData) {
	logPrefix := fmt.Sprintf("获取老板邀请码信息:入参=%d", inviteCode)
	log.Info(logPrefix)
	// 获取Redis连接
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	// 查找对应的邀请码记录
	key := fmt.Sprintf("bossInvite:%d", inviteCode)
	inviteDataArr := redisConn.Get(string(cache_source.EShop), key)
	if len(inviteDataArr) == 0 {
		return
	}
	var inviteData distribution_vo.InviteData
	err := json.Unmarshal([]byte(inviteDataArr[0].(string)), &inviteData)
	if err != nil {
		log.Error(logPrefix, "解析邀请码数据失败:", err.Error())
		return
	}
	return inviteData
}

// 分销员分享海报
func (s *DistributorManageService) SharePosters(in distribution_vo.DisShareReq) (string, error) {

	s.Begin()
	defer s.Close()

	url, err := utils.GetQrImage(in.Path, in.Sid, in.OrgId, 1, in.SourceType)
	if err != nil {
		log.Error("生成分享码失败：err=", err.Error())
		return "", err
	}
	return url, nil
}

// 修改分销员信息
func (s *DistributorManageService) DistributorEdit(in distribution_vo.DisEditReq) (data distribution_vo.DisDistributorAddData, err error) {
	var (
		inSystem       int8
		veterinaryCode string
	)
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("修改分销员信息：id=%d", in.Id)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	session := s.Session

	//修改手机号，
	if in.EditType == 1 {
		if in.OrgId == enum.BLKYOrgId {
			err = errors.New("不可编辑手机号")
			return
		}
		//先验证CODE是否正确
		err = utils.CheckCode(in.Mobile, in.Code, cast.ToString(in.OrgId))
		if err != nil {
			return
		}

		StarMobile := utils.AddStar(in.Mobile)
		EncryptMobile := utils.MobileEncrypt(in.Mobile)
		_, err = session.Exec("UPDATE dis_distributor SET mobile=?,encrypt_mobile=?,update_time=NOW() WHERE id=?", StarMobile, EncryptMobile, in.Id)
		if err != nil {
			err = errors.New("修改分分销员信息失败：err=" + err.Error())
			return
		}

	} else {
		disInfo := distribution_po.DisDistributor{}
		if _, err = session.Table("eshop.dis_distributor").Where("id=?", in.Id).Get(&disInfo); err != nil {
			log.Error(logPrefix, "获取分销员信息失败，err=", err.Error())
			err = errors.New("获取分销员信息失败")
			return
		} else if disInfo.Id == 0 {
			log.Error(logPrefix, "分销员信息不存在")
			err = errors.New("分销员信息不存在")
			return
		}
		var tuokeSalespersonId int64
		if in.OrgId == enum.BLKYOrgId && disInfo.TuokeSalespersonId == 0 && len(in.TuokeSalesmanName) > 0 {
			//查询邀请人信息
			salesperson := make([]distribution_po.ScrmSalesperson, 0)
			if err = session.Table("eshop.scrm_salesperson").Select("id,name").Where("name = ?", in.TuokeSalesmanName).Find(&salesperson); err != nil {
				log.Error(logPrefix, "推荐人不存在，err=", err.Error())
				err = errors.New("推荐人不存在")
				return
			} else if len(salesperson) == 0 {
				err = errors.New("邀请人信息不存在")
				return
			}
			log.Info(logPrefix, "邀请人信息：", utils.InterfaceToJSON(salesperson))
			tuokeSalespersonId = salesperson[0].Id

		}
		if in.EditType == 2 { //修改头像和姓名
			if in.OrgId == enum.BLKYOrgId {
				disInfo.HeadImage = in.HeadImage
				if tuokeSalespersonId != 0 {
					disInfo.TuokeSalespersonId = tuokeSalespersonId
				}
				if _, err = session.Table("eshop.dis_distributor").Cols("head_image,tuoke_salesperson_id").Where("id=?", in.Id).Update(&disInfo); err != nil {
					log.Error(logPrefix, "修改分销员信息失败，err=", err.Error())
					err = errors.New(err.Error())
					return
				}

			} else {
				_, err = session.Exec("UPDATE dis_distributor SET name=?,head_image=?,update_time=NOW() WHERE id=?", in.Name, in.HeadImage, in.Id)
			}

		} else if in.EditType == 3 && in.OrgId == enum.BLKYOrgId { //百林康源重新认证
			if disInfo.ApproveState != disdistributor.ApproveStateFail {
				log.Error(logPrefix, "只有认证信息未通过审核，才可重新认证")
				err = errors.New("只有认证信息未通过审核，才可重新认证")
				return
			}

			disDistributorStatus := disdistributor.StatusNotOpen
			if in.SocialCodeImage != "" {
				mobile := utils.MobileDecrypt(disInfo.EncryptMobile)
				data.ApproveState, inSystem, veterinaryCode, err = s.OcrpPrediction(s.Engine, in.RealName, in.SocialCodeImage, mobile)
				if err != nil {
					log.Error(logPrefix, "插入分销员数据失败，err=", err.Error())
					err = errors.New(err.Error())
					return
				}

				//如果是审核失败，直接返回告诉前端，弹窗提醒用户
				if data.ApproveState == disdistributor.ApproveStateFail {
					return
				} else if data.ApproveState == disdistributor.ApproveStatePass {
					//如果是审核通过
					disDistributorStatus = disdistributor.StatusValid
				}
			}

			session2 := s.Engine.NewSession()
			defer session2.Close()
			session2.Begin()
			disInfo.Status = cast.ToInt8(disDistributorStatus)
			disInfo.ApproveState = cast.ToInt8(data.ApproveState)
			disInfo.Reason = ""
			disInfo.Name = in.Name
			disInfo.HeadImage = in.HeadImage
			disInfo.SocialCodeImage = in.SocialCodeImage
			disInfo.RealName = in.RealName
			disInfo.InSystem = inSystem
			disInfo.VeterinaryCode = veterinaryCode
			if tuokeSalespersonId > 0 {
				disInfo.TuokeSalespersonId = tuokeSalespersonId
			}
			if _, err = session2.Table("eshop.dis_distributor").Cols("status,reason,approve_state,name,real_name,head_image,social_code_image,in_system,veterinary_code,tuoke_salesperson_id").
				Where("id=?", in.Id).
				Where("approve_state=?", disdistributor.ApproveStateFail).Update(&disInfo); err != nil {
				log.Error(logPrefix, "修改分销员信息失败，err=", err.Error())
				session2.Rollback()
				err = errors.New(err.Error())
				return
			}

			disDetail := distribution_po.DisDistributorDetail{}
			if err = copier.Copy(&disDetail, in); err != nil {
				log.Error(logPrefix, "数据赋值失败：err=", err.Error())
				session2.Rollback()
				err = errors.New("修改分销员信息失败。")
				return
			}
			if _, err = session2.Table("eshop.dis_distributor_detail").Where("dis_id=?", in.Id).Update(&disDetail); err != nil {
				log.Error(logPrefix, "修改分销员详情失败，err=", err.Error())
				session2.Rollback()
				err = errors.New(err.Error())
				return
			}
			session2.Commit()

		} else if in.EditType == 4 && in.OrgId == enum.BLKYOrgId { //百林康源医生分销员修改信息
			if disInfo.ApproveState != disdistributor.ApproveStatePass {
				log.Error(logPrefix, "只有审核通过的医生分销员才能修改信息")
				err = errors.New("只有审核通过的医生分销员才能修改信息")
				return
			}

			// 获取dis_distributor_detail信息
			disDetail := distribution_po.DisDistributorDetail{}
			if exists, e := session.Table("eshop.dis_distributor_detail").Where("dis_id=?", in.Id).Get(&disDetail); e != nil {
				log.Error(logPrefix, "获取分销员详情信息失败，e=", e.Error())
				err = errors.New("获取分销员详情信息失败")
				return
			} else if !exists {
				log.Error(logPrefix, "分销员详情信息不存在")
				err = errors.New("分销员详情信息不存在")
				return
			}
			disDetail.HospitalName = in.HospitalName
			disDetail.Province = in.Province
			disDetail.City = in.City
			if _, err = session.Table("eshop.dis_distributor_detail").Where("dis_id=?", in.Id).Update(&disDetail); err != nil {
				log.Error(logPrefix, "修改分销员详情信息失败，err=", err.Error())
				err = errors.New(err.Error())
				return
			}
			// 更新dis_distributor_detail信息
			if tuokeSalespersonId > 0 {
				disInfo.TuokeSalespersonId = tuokeSalespersonId
			}
			if _, err = session.Table("eshop.dis_distributor").Cols("tuoke_salesperson_id").Where("id=?", in.Id).Update(&disInfo); err != nil {
				log.Error(logPrefix, "修改分销员信息失败，err=", err.Error())
				err = errors.New(err.Error())
				return
			}
		}
		if err != nil {
			err = errors.New("修改分分销员信息失败：err=" + err.Error())
			return
		}
	}

	return
}

// 宠利扫老板分销员重新认证企业， 或者修改企业的信息
func (s DistributorManageService) CLSDistributorEdit(in distribution_vo.CLSDistributorEditReq) (err error) {
	logPrefix := fmt.Sprintf("宠利扫分销员重新认证信息：id=%d", in.Id)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	engine := s.DbDmMdm()
	defer engine.Close()
	var (
		inSystem          int8                                          // 是否在系统内
		dataSource        = distribution_po.DisEnterpriseDataSourceSelf // 数据来源:1-自建 2-云订货
		disEnterpriseInfo distribution_po.DisEnterprise                 // 宠利扫企业信息
		shopInfo          distribution_po.Shop                          // 店铺信息
	)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	if in.Id == 0 {
		err = errors.New("分销员id不能为0")
		return
	}

	//查询分销员信息
	disInfo := distribution_po.DisDistributor{}
	if _, err = session.Table("eshop.dis_distributor").Where("member_id=?", in.MemberId).Where("org_id=?", in.OrgId).Get(&disInfo); err != nil {
		log.Error(logPrefix, "获取分销员信息失败，err=", err.Error())
		err = errors.New("获取分销员信息失败")
		return
	}
	if disInfo.Id == 0 {
		err = errors.New("分销员信息不存在")
		return
	}

	mobile := utils.MobileDecrypt(disInfo.EncryptMobile)
	// if disInfo.DisRole == distribution_po.DisRoleDoctor && disInfo.ApproveState != disdistributor.ApproveStatePass {
	// 	err = errors.New("只有审核通过的医生分销员才能修改信息")
	// 	return
	// }

	// 查询企业信息
	disEnterpriseInfo, shopInfo, err = new(distribution_po.DisEnterprise).CheckEnterpriseExist(session, distribution_po.CheckEnterpriseExistReq{
		SocialCreditCode: in.SocialCreditCode,
		DisId:            disInfo.Id,
		OrgId:            in.OrgId,
	})
	if err != nil {
		log.Error(logPrefix, "查询企业信息失败，err=", err.Error())
		return
	}

	if in.TuokeSalesmanName != "" {

		disInfo.TuokeSalespersonId, err = new(distribution_po.ScrmSalesperson).GetSalespersonId(session, distribution_po.GetSalespersonInfoReq{
			Name: in.TuokeSalesmanName,
		})
		if err != nil {
			log.Error(logPrefix, "推荐人不存在，err=", err.Error())
			err = errors.New("推荐人不存在")
			return
		}
	}

	// 根据信用代码查询R1企业信息
	enterprise := new(distribution_po.ScrmEnterprise)
	enterpriseList, err := enterprise.GetScrmEnterprises(session, distribution_po.GetScrmEnterprisesReq{
		SocialCreditCode: in.SocialCreditCode,
	})
	if err != nil {
		log.Error(logPrefix, "查询企业信息失败，err=", err.Error())
		err = errors.New("查询企业信息失败")
		return
	}
	if len(enterpriseList) > 0 {
		disEnterpriseInfo.ScrmEnterpriseId = enterpriseList[0].Id
		dataSource = distribution_po.DisEnterpriseDataSourceCloud
	}

	// 判断员工是否存在于SHR系统
	inSystem, err = external_po.DisIsInSystem(engine, mobile, in.RealName, in.SocialCreditCode)
	if err != nil {
		log.Error(logPrefix, "判断员工是否存在于SHR系统失败，err=", err.Error())
		err = errors.New("判断员工是否存在于SHR系统失败" + err.Error())
		return
	}

	session.Begin()

	disEnterpriseInfo.OrgId = in.OrgId
	disEnterpriseInfo.EnterpriseName = in.EnterpriseName
	disEnterpriseInfo.SocialCreditCode = in.SocialCreditCode
	disEnterpriseInfo.Province = in.Province
	disEnterpriseInfo.City = in.City
	disEnterpriseInfo.District = in.District
	disEnterpriseInfo.Address = in.Address
	disEnterpriseInfo.Phone = disInfo.Mobile
	disEnterpriseInfo.EncryptPhone = disInfo.EncryptMobile
	disEnterpriseInfo.DataSource = int(dataSource)
	err = disEnterpriseInfo.UpdateOrCreate(session)
	if err != nil {
		log.Error(logPrefix, "更新或创建企业数据失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}

	if shopInfo.Id <= 0 {
		shopInfo.OrgId = in.OrgId
		shopInfo.EnterpriseId = disEnterpriseInfo.ScrmEnterpriseId
		shopInfo.ShopName = in.EnterpriseName
		if _, err = session.Insert(&shopInfo); err != nil {
			session.Rollback()
			log.Error(logPrefix, "新增店铺信息失败，err=", err.Error())
			return
		}

	}

	// 判断dis_distributor_total是否存在，不存在则新增
	disDistributorTotal := new(distribution_po.DisDistributorTotal)
	err = disDistributorTotal.Insert(session, in.Id, in.OrgId, shopInfo.Id, disInfo.MemberId)
	if err != nil {
		log.Error(logPrefix, "新增dis_distributor_total失败，err=", err.Error())
		session.Rollback()
		err = errors.New(err.Error())
		return
	}
	disInfo.RealName = in.RealName
	disInfo.HeadImage = in.HeadImage
	disInfo.ShopId = shopInfo.Id
	disInfo.SocialCreditCode = in.SocialCreditCode
	if disInfo.SocialCodeImage == in.SocialCodeImage {
		disInfo.SocialCodeImage = in.SocialCodeImage
	} else {
		disInfo.SocialCodeImage = utils.MobileEncrypt(in.SocialCodeImage)
	}
	disInfo.DisRole = distribution_po.DisRoleBoss
	disInfo.InSystem = inSystem
	disInfo.ApproveState = disdistributor.ApproveStatePass
	disInfo.Status = disdistributor.StatusValid
	cols := "shop_id,real_name,head_image,social_credit_code,social_code_image,dis_role,tuoke_salesperson_id,in_system,approve_state,status,customer_channel_id"

	if _, err = session.Table("eshop.dis_distributor").Cols(cols).Where("id=?", in.Id).Update(&disInfo); err != nil {
		session.Rollback()
		log.Error(logPrefix, "修改分销员信息失败，err=", err.Error())
		return
	}

	session.Commit()

	// 执行积分的更新
	go func(mobile string, disId int) {
		pointsFlowSrv := points_service.NewClsPointsFlowService()
		disMobileMap := make(map[int]string)
		disMobileMap[disId] = mobile
		pointsFlowSrv.UpdatebyStaffNo(session, disMobileMap)
	}(mobile, disInfo.Id)

	return
}

// 获取指定店铺的分销员列表
func (s *DistributorManageService) GetShopDistributors(in distribution_vo.GetShopDistributorsReq) (out []distribution_vo.DisDistributor, err error) {
	s.Begin()
	defer s.Close()
	session := s.Session
	//用SCRMID去查
	member := 0
	if in.MemberId == 0 {
		_, err := session.SQL("select member_id from "+"upetmart.upet_member where scrm_user_id=?", in.ScrmUserId).Get(&member)
		if err != nil {
			log.Error("查询会员信息失败：err=", err.Error())
			return nil, err
		}
	}
	if member > 0 {
		in.MemberId = member
	}

	// 先获取老板所在的分销店铺
	bossInfo := distribution_po.DisDistributor{}
	exists, err := session.Table("eshop.dis_distributor").Cols("shop_id,dis_role").Where("org_id=?", in.OrgId).Where("member_id=?", in.MemberId).Get(&bossInfo)
	if err != nil {
		return nil, errors.New("获取老板信息失败")
	}

	if !exists {
		return nil, errors.New("老板信息不存在")
	}
	out = make([]distribution_vo.DisDistributor, 0)
	if err := session.Table("eshop.dis_distributor").Where("shop_id=?", bossInfo.ShopId).Where("org_id=?", in.OrgId).Find(&out); err != nil {
		return nil, errors.New("获取店员信息失败")
	}
	return

}

// 后端获取分销员详情
func (s DistributorManageService) DistributorDetail(req distribution_vo.DisIdReq) (distribution_vo.DistributorDetail, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var info distribution_vo.DistributorDetail

	session.Select("dd.id,dd.real_name name,dd.mobile,s.enterprise_id,dd.encrypt_mobile,dd.bank_mobile,dd.bank_encrypt_mobile,dd.dis_role,dd.shop_id,dd.status,dd.create_time,dd.update_time,dd.head_image,dd.approve_state,dd.reason,"+
		"dd.bank_branch,dd.social_credit_code,dd.social_code_image,dd.encrypt_id_card,dd.id_card,dd.idcard_front,dd.idcard_reverse,dd.bank_name,dd.bank_account,dd.encrypt_bank_account,dd.account_name,dd.encrypt_with_idcard,dd.withdraw_id_card,dd.approve_time,"+
		"en.enterprise_name ,de.enterprise_name as dis_enterprise_name,de.province as enterprise_province,de.city as enterprise_city,de.district,de.address,en.code,en.enterprise_status,"+
		"dd.tuoke_salesperson_id ,sale.name tuoke_salesman_name,"+
		"dd.total_customer,df.valid_customer,"+
		"dd.order_num,dd.total_sales,dd.unsettled_commission+dd.settled_commission as total_commission,dd.settled_commission,dd.unsettled_commission,dd.order_pay_num,dd.total_pay_sales,"+
		"dt.withdraw_success,dt.withdraw_apply,dt.wait_withdraw,"+
		"ddd.professional,ddd.province,ddd.city,ddd.hospital_name,ddd.specialize,dd.veterinary_code,dd.in_system").
		Table("dis_distributor dd").
		Join("left", "shop s", "s.id=dd.shop_id").
		Join("left", "scrm_salesperson sale", "sale.id=dd.tuoke_salesperson_id").
		Join("left", "scrm_enterprise en", "en.id=s.enterprise_id").
		Join("left", "eshop.dis_enterprise de", "de.scrm_enterprise_id=s.enterprise_id and de.org_id=?", req.OrgId).
		Join("left", "(SELECT dis_id,COUNT(CASE WHEN expire_time IS NOT NULL AND expire_time > NOW() THEN 1 END) AS valid_customer FROM dis_distributor_fans GROUP BY dis_id) df", "dd.id = df.dis_id").
		Join("left", "dis_distributor_total dt", "dd.id = dt.`dis_id` AND dd.`shop_id` = dt.`shop_id`").
		Join("left", "dis_distributor_detail ddd", "ddd.dis_id = dd.id")
	session.Where("dd.id = ?", req.Id)
	_, err := session.Get(&info)
	if err != nil {
		log.Error("查询分销员失败：err=", err.Error())
		return info, err
	}

	var temp []distribution_vo.BelongSalesmanTemp

	//查询所有的企业对应的业务员
	session.Select("b.enterprise_id,a.id belong_salesman_id,a.name as belong_salesman_name").Table("scrm_salesperson").Alias("a").
		Join("inner", "scrm_enterprise_salesperson_bind b", "b.salesperson_id = a.id ").
		Where("b.enterprise_id=?", cast.ToInt64(info.EnterpriseId))
	err = session.Find(&temp)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return info, err
	}
	// 宠利扫 老板和店员角色 展示dis_enterprise表里的企业数据
	if req.OrgId == enum.BLKYOrgId {
		if info.DisRole == distribution_po.DisRoleBoss || info.DisRole == distribution_po.DisRoleStaff {
			info.EnterpriseName = info.DisEnterpriseName
			info.Province = info.EnterpriseProvince
			info.City = info.EnterpriseCity
			info.OrgDataSource = enum.OrgDataSourceCLS
		} else {
			info.OrgDataSource = enum.OrgDataSourceBLKY
		}
	}

	for _, x := range temp {
		data := distribution_vo.BelongSalesmanData{}
		data.BelongSalesmanName = x.BelongSalesmanName
		data.BelongSalesmanId = x.BelongSalesmanId
		info.BelongSalesman = append(info.BelongSalesman, data)
	}

	return info, nil
}

// 按店铺ID查询分销员信息
func (s DistributorManageService) SaleManCenterDetails(in distribution_vo.SalesAchievementReq) ([]distribution_vo.DistributorDetail, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var list []distribution_vo.DistributorDetail

	session.Select("dd.id,dd.name,dis_role,"+
		"s.order_num,s.total_sales,s.ins_order_num,s.ins_total_sales,s.order_pay_num,s.total_pay_sales,s.ins_order_pay_num,s.ins_total_pay_sales").
		Table("dis_distributor dd").
		Join("inner", "dis_distributor_total s", "dd.id = s.dis_id ")

	session.And("s.shop_id = ? and dd.org_id=?", in.ShopId, in.OrgId)

	err := session.OrderBy("dd.id DESC").Find(&list)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return nil, err
	}

	return list, nil
}

// 百林康源分销员角色变更
func (s DistributorManageService) BLKYDistributorRoleChange(req distribution_vo.DisRoleChangeReq) (string, error) {
	logPrefix := fmt.Sprintf("宠利扫分销员角色变更,入参:%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	//如果是老板，需要判断是否已经有老板
	if req.DisRole == 1 {
		item := distribution_po.DisDistributor{}
		ishave, err := session.Table("dis_distributor").Alias("a").
			Join("LEFT", "shop s", "s.id = a.shop_id").
			Join("LEFT", "dis_enterprise de", "de.scrm_enterprise_id = s.enterprise_id").
			Where("s.enterprise_id=? and a.dis_role=1", cast.ToInt64(req.EnterpriseId)).Get(&item)
		if err != nil {
			log.Error(logPrefix, "分销员角色变更失败：err=", err.Error())
			return "", err
		}
		if ishave {
			if item.Id == req.Id {
				return "", errors.New("您已经是当前企业的老板")
			} else {
				log.Error(logPrefix, "分销员角色变更失败:当前企业已有“老板”角色，请核实，或联系业务员确认，或选择注册店员“角色")
				return "", errors.New("当前企业已有“老板”角色，请核实，或联系业务员确认，或选择注册店员“角色")
			}

		}
	}

	type shopMap struct {
		DisRole          int    `xorm:"dis_role"`
		EnterpriseId     int    `xorm:"enterprise_id"`
		EnterpriseName   string `xorm:"enterprise_name"`
		ScrmEnterpriseId int    `xorm:"scrm_enterprise_id"`
		SocialCreditCode string `xorm:"social_credit_code"`
		RealName         string `xorm:"real_name"`
	}
	shop := shopMap{}
	_, err := session.Table("dis_distributor dd").
		Select("dd.dis_role,dd.real_name,s.enterprise_id,de.enterprise_name,de.scrm_enterprise_id,de.social_credit_code").
		Join("INNER", "shop s", "dd.shop_id = s.id").
		Join("INNER", "dis_enterprise de", "s.enterprise_id = de.scrm_enterprise_id").
		Where("dd.id = ?", req.Id).
		Get(&shop)
	if err != nil {
		log.Error("分销员角色变更失败：err=", err.Error())
		return "", err
	}
	if shop.DisRole == req.DisRole {
		return "", errors.New("分销员角色未变更")
	}
	if req.DisRole == 1 { //企业 老板身份校验
		if req.SocialCodeImage == "" || req.SocialCreditCode == "" {
			return "", errors.New("企业老板社会信用代码信息不完整")
		}
		if !utils.ValidateUSCC(req.SocialCreditCode) {
			return "", errors.New("企业老板社会信用代码不正确")
		}
		//企业 营业执照加密
		socialCodeImage := utils.MobileEncrypt(req.SocialCodeImage)
		if req.SocialCreditCode != shop.SocialCreditCode {
			log.Error(logPrefix, "分销员角色变更失败:企业老板社会信用代码不正确")
			return "", errors.New("企业老板社会信用代码不正确")
		}
		_, err = session.Exec("UPDATE dis_distributor SET dis_role=?,social_credit_code=?,social_code_image=?,update_time=NOW() WHERE id=?", req.DisRole, req.SocialCreditCode, socialCodeImage, req.Id)
		if err != nil {
			log.Error("分销员角色变更失败：err=", err.Error())
			return "", err
		}
	} else { //店员身份
		//更新提交的分销员角色及统一社会信用代码、社会信用代码图片等提交的数据
		_, err = session.Exec("UPDATE dis_distributor SET dis_role=?,update_time=NOW() WHERE id=?", req.DisRole, req.Id)
		if err != nil {
			log.Error(logPrefix, "分销员角色变更失败：err=", err.Error())
			return "", err
		}
	}
	//添加操作日志
	var Description string
	if req.DisRole == 1 {
		Description = fmt.Sprintf("%s角色由“店员”变更为“老板”", shop.RealName)
	} else {
		Description = fmt.Sprintf("%s角色由“老板”变更为“店员”", shop.RealName)
	}

	return Description, nil

}

// 分销员角色变更
func (s DistributorManageService) DistributorRoleChange(req distribution_vo.DisRoleChangeReq) (string, error) {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	//如果是老板，需要判断是否已经有老板
	if req.DisRole == 1 {
		item := distribution_po.DisDistributor{}
		ishave, err := session.Table("dis_distributor").Alias("a").
			Join("LEFT", "shop s", "s.id = a.shop_id").
			Where("s.enterprise_id=? and a.dis_role=1", cast.ToInt64(req.EnterpriseId)).Get(&item)
		if err != nil {
			log.Error("分销员角色变更失败：err=", err.Error())
			return "", err
		}
		if ishave {
			log.Error("分销员角色变更失败:当前企业已有“老板”角色，请核实，或联系业务员确认，或选择注册店员“角色")
			return "", errors.New("当前企业已有“老板”角色，请核实，或联系业务员确认，或选择注册店员“角色")
		}
	}

	//查询分销员所在关联的企类型 0-企业  1-个体，如果是企业，需要验证社会信用代码，如果是个体，需要验证身份证正反面
	type shopMap struct {
		EnterpriseType int    `xorm:"enterprise_type"`
		EnterpriseId   int    `xorm:"enterprise_id"`
		Name           string `xorm:"name"`
		DisRole        int    `xorm:"dis_role"`
	}
	shop := shopMap{}
	_, err := session.Table("dis_distributor dd").
		Join("LEFT", "shop s", "dd.shop_id = s.id").
		Join("INNER", "scrm_enterprise se", "s.enterprise_id = se.id").
		Where("dd.id = ?", req.Id).
		Get(&shop)
	if err != nil {
		log.Error("分销员角色变更失败：err=", err.Error())
		return "", err
	}
	if shop.DisRole == req.DisRole {
		return "", errors.New("分销员角色未变更")
	}
	if shop.EnterpriseType == 1 && req.DisRole == 1 { //个体 老板身份校验
		if req.IdCard == "" || req.IdcardFront == "" || req.IdcardReverse == "" {
			return "", errors.New("个体工商户老板身份证信息不完整")
		}
		if !utils.ValidateIDCard(req.IdCard) {
			return "", errors.New("个体工商户老板身份证号码不正确")
		}
		idcardFront := utils.MobileEncrypt(req.IdcardFront)
		IdcardReverse := utils.MobileEncrypt(req.IdcardReverse)
		IdCard := utils.AddStar(req.IdCard)
		encryptIdCard := utils.MobileEncrypt(req.IdCard)
		_, err = session.Exec("UPDATE dis_distributor SET dis_role=?,encrypt_id_card=?,id_card=?,idcard_front=?,idcard_reverse=?,update_time=NOW() WHERE id=?", req.DisRole, encryptIdCard, IdCard, idcardFront, IdcardReverse, req.Id)
		if err != nil {
			log.Error("分销员角色变更失败：err=", err.Error())
			return "", err
		}
	} else if shop.EnterpriseType == 0 && req.DisRole == 1 { //企业 老板身份校验
		if req.SocialCodeImage == "" || req.SocialCreditCode == "" {
			return "", errors.New("企业老板社会信用代码信息不完整")
		}
		if !utils.ValidateUSCC(req.SocialCreditCode) {
			return "", errors.New("企业老板社会信用代码不正确")
		}
		//企业 营业执照加密
		socialCodeImage := utils.MobileEncrypt(req.SocialCodeImage)
		_, err = session.Exec("UPDATE dis_distributor SET dis_role=?,social_credit_code=?,social_code_image=?,update_time=NOW() WHERE id=?", req.DisRole, req.SocialCreditCode, socialCodeImage, req.Id)
		if err != nil {
			log.Error("分销员角色变更失败：err=", err.Error())
			return "", err
		}
	} else { //店员身份
		//更新提交的分销员角色及统一社会信用代码、社会信用代码图片等提交的数据
		_, err = session.Exec("UPDATE dis_distributor SET dis_role=?,update_time=NOW() WHERE id=?", req.DisRole, req.Id)
		if err != nil {
			log.Error("分销员角色变更失败：err=", err.Error())
			return "", err
		}
	}
	//添加操作日志
	var Description string
	if req.DisRole == 1 {
		Description = fmt.Sprintf("%s角色由“店员”变更为“老板”", shop.Name)
	} else {
		Description = fmt.Sprintf("%s角色由“老板”变更为“店员”", shop.Name)
	}

	return Description, nil
}

// ChangeHospital 更换绑定的医院操作
func (s DistributorManageService) ChangeHospital(req distribution_vo.DisChangeHospitalReq) (string, error) {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	//先查询要变更的业务员拓展信息是否存在
	detail := distribution_po.DisDistributorDetail{}
	_, err := session.Where("dis_id=?", req.Id).Get(&detail)
	if err != nil || detail.DisId == 0 {
		log.Error("查询分销员扩展信息失败，id=", req.Id)
		return "", errors.New("查询分销员扩展信息失败")
	}

	_, err = session.Exec("UPDATE dis_distributor_detail SET hospital_name=?,province=?,city=? WHERE dis_id=?", req.HospitalName, req.Province, req.City, req.Id)
	if err != nil {
		log.Error("更换绑定的医院操作，修改数据库异常：e=" + err.Error())
		return "", errors.New("更换绑定的医院操作：e=" + err.Error())
	}

	logMsg := fmt.Sprintf("分销员id:%d，医院名称由'%s%s%s'，变更成 '%s%s%s'", req.Id, detail.Province, detail.City, detail.HospitalName, req.Province, req.City, req.HospitalName)
	return logMsg, nil
}

// Approve 审核操作
func (s DistributorManageService) Approve(req distribution_vo.DisApproveReq) error {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Engine.NewSession()

	//检查分销员当前状态
	distributor := distribution_po.DisDistributor{}
	_, err := session.Where(" id=?", req.Id).Get(&distributor)
	if err != nil || distributor.Id == 0 {
		log.Error("查询分销员信息失败，id=", req.Id)
		return errors.New("查询分销员信息失败")
	}

	if distributor.ApproveState != 1 {
		log.Errorf("检查到分销员当前认证状态不是待审核，id=%d, approveState=%d", req.Id, distributor.ApproveState)
		return errors.New("当前分销员的认证状态不是待审核")
	}
	var field = "approve_state,reason,status"
	// 审核通过时，修改分销员状态为启用
	status := 1
	if req.ApproveState != 2 {
		status = cast.ToInt(distributor.Status)
	}
	distributor.Status = int8(status)
	distributor.ApproveState = int8(req.ApproveState)
	distributor.Reason = req.Reason
	if req.ApproveState == 2 {
		field = "approve_state,reason,status,approve_time"
		distributor.ApproveTime = time.Now()
	}
	distributor.UpdateTime = time.Now()
	_, err = session.Table("dis_distributor").Where("id=?", req.Id).Cols(field).Update(&distributor)

	if err != nil {
		session.Rollback()
		log.Error("分销员审核操作，修改数据库异常：e=" + err.Error())
		return errors.New("分销员审核操作异常：e=" + err.Error())
	}

	session.Commit()
	return nil
}

func (s DistributorManageService) SalesShopDisPage(req distribution_vo.ShopDisPageReq) ([]distribution_vo.SalesShopPageData, int, error) {
	var data []distribution_vo.SalesShopPageData
	s.Begin()
	defer s.Close()
	session := s.Session

	// 根据业务员id进行分页查询
	session.Select("DISTINCT sesb.id AS enterprise_id,"+
		"se.enterprise_name,se.enterprise_type,"+
		"s.id AS shop_id,s.shop_name,"+
		"SUM(ssdd.trans_amount) AS order_amount,"+
		"SUM(ssdd.trans_count) AS order_count,"+
		"SUM(ssdd.commission) AS commission").
		Table("scrm_enterprise_salesperson_bind sesb").
		Join("LEFT", "scrm_enterprise se", "se.id=sesb.enterprise_id").
		Join("LEFT", "shop s", "s.enterprise_id=sesb.enterprise_id").
		Join("LEFT", "stats_shop_distributor_daily ssdd", "ssdd.shop_id=s.id").
		Join("LEFT", "dis_distributor dd", "dd.shop_id=s.id").
		Where("sesb.salesperson_id=? AND is_dis=1 AND s.org_id=?", req.SalesmanId, req.OrgId)

	if len(req.Name) > 0 {
		session.And("se.enterprise_name LIKE ? OR s.shop_name LIKE ? OR dd.`name` LIKE ?", "%"+req.Name+"%", "%"+req.Name+"%", "%"+req.Name+"%")
	}
	if len(req.StartTime) > 0 {
		session.And("ssdd.stat_date = ?", req.StartTime)
	}
	if len(req.EndTime) > 0 {
		session.And("ssdd.end_date = ?", req.EndTime)
	}
	session.GroupBy("ssdd.shop_id,dd.id")

	if req.OrderType > 0 {
		switch req.OrderType {
		case 1:
			session.OrderBy("SUM(ssdd.order_count)")
		case 2:
			session.OrderBy("SUM(ssdd.order_count) DESC")
		case 3:
			session.OrderBy("SUM(ssdd.order_amount)")
		case 4:
			session.OrderBy("SUM(ssdd.order_amount) DESC")
		case 5:
			session.OrderBy("SUM(ssdd.commission)")
		case 6:
			session.OrderBy("SUM(ssdd.commission) DESC")
		}
	}

	total, err := session.Limit(req.PageSize, (req.PageIndex-1)*req.PageSize).FindAndCount(&data)
	if err != nil {
		log.Error("查询分销员下企业和店铺信息列表失败：err=", err.Error())
		return nil, 0, err
	}

	// 遍历店铺列表，查询店铺分销员信息
	if len(data) > 0 {
		//var wg sync.WaitGroup
		//wg.Add(len(data))
		for ind, _ := range data {
			//go func() {
			s.SalesDisList(req, &data[ind])
			data[ind].DisCount = len(data[ind].DisList)
			//wg.Done()
			//}()
		}
		//wg.Wait()
	}

	return data, cast.ToInt(total), nil
}

func (s DistributorManageService) SalesDisList(req distribution_vo.ShopDisPageReq, data *distribution_vo.SalesShopPageData) error {
	var salesDisData []distribution_vo.SalesDisData
	s.Begin()
	defer s.Close()
	session := s.Session

	session.Select("dd.dis_role,dd.`name`,"+
		"SUM(ssdd.trans_count) AS order_count,"+
		"SUM(ssdd.trans_amount) AS order_amount,"+
		"SUM(IF(ssdd.type=2,ssdd.trans_count,0)) AS product_order_count,"+
		"SUM(IF(ssdd.type=2,ssdd.trans_amount,0)) AS product_order_amount,"+
		"SUM(IF(ssdd.type=4,ssdd.trans_count,0)) AS insure_order_count,"+
		"SUM(IF(ssdd.type=4,ssdd.trans_amount,0)) AS insure_order_amount").
		Table("stats_shop_distributor_daily ssdd").
		Join("LEFT", "dis_distributor dd", "dd.id=ssdd.dis_id").
		Where("ssdd.shop_id=? AND ssdd.is_dis=1 AND dd.org_id=?", data.ShopId, req.OrgId)
	if len(req.StartTime) > 0 {
		session.And("ssdd.stat_date = ?", req.StartTime)
	}
	if len(req.EndTime) > 0 {
		session.And("ssdd.end_date = ?", req.EndTime)
	}
	session.GroupBy("ssdd.dis_id")

	err := session.Find(&salesDisData)
	if err != nil {
		log.Error("查询店铺分销员信息列表失败：err=", err.Error())
		return err
	}

	data.DisList = salesDisData
	return nil
}

func (s DistributorManageService) ChangeSalesperson(req distribution_vo.ChangeSalespersonReq) error {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	// 检查变更前后的业务员是否为同一个
	if req.SalespersonId == req.OldSalespersonId {
		log.Error("变更的新业务员与原业务员相同，old=new=%s" + req.SalespersonId)
		return errors.New("变更的新业务员与原业务员相同，请重新选择")
	}

	// 查询需要变更的业务员是否存在且状态为启用
	salesperson := distribution_po.ScrmSalesperson{}
	_, err := session.Table("scrm_salesperson").Select("id,status").Where("id=?", req.SalespersonId).Get(&salesperson)
	if err != nil || salesperson.Id == 0 {
		log.Error("查询业务员信息失败，id=", req.SalespersonId)
		return errors.New("查询业务员信息失败")
	}
	if salesperson.Status != 1 {
		return errors.New("业务员状态非启用，请重新选择")
	}

	_, err = session.Exec("UPDATE dis_distributor SET tuoke_salesperson_id=? WHERE id=?", req.SalespersonId, req.Id)
	if err != nil {
		log.Errorf("变更分销员的注册业务员失败：err=%s", err.Error())
		return errors.New("变更分销员的注册业务员失败：e=" + err.Error())
	}

	return nil
}

// GetDistributorEnterpriseList 获取分销员关联的企业列表
func (s *DistributorManageService) GetDistributorEnterpriseList(req distribution_vo.DistributorEnterpriseListReq) ([]distribution_vo.DistributorEnterpriseInfo, error) {
	logPrefix := fmt.Sprintf("获取分销员关联企业列表====，主体ID：%d, 会员ID：%d", req.OrgId, req.MemberId)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()

	// 使用领域模型获取企业列表
	distributor := new(distribution_po.DisDistributor)
	enterprises, err := distributor.GetDistributorEnterpriseList(s.Engine, req.OrgId, req.MemberId)
	if err != nil {
		log.Error(logPrefix, "获取企业列表失败,err=", err.Error())
		return nil, errors.New("获取企业列表失败")
	}

	// 转换为响应结构
	result := make([]distribution_vo.DistributorEnterpriseInfo, 0)
	for _, enterprise := range enterprises {
		result = append(result, distribution_vo.DistributorEnterpriseInfo{
			Id:             enterprise.Id,
			OrgId:          enterprise.OrgId,
			ShopId:         enterprise.ShopId,
			IsDefault:      enterprise.IsDefault,
			EnterpriseName: enterprise.EnterpriseName,
		})
	}

	return result, nil
}

// UpdateDefaultEnterprise 更新默认企业
func (s *DistributorManageService) UpdateDefaultEnterprise(req distribution_vo.UpdateDefaultEnterpriseReq) error {
	logPrefix := fmt.Sprintf("更新默认企业====，入参：%+v", req)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()

	// 开启事务
	if err := s.Session.Begin(); err != nil {
		log.Error(logPrefix, "开启事务失败,err=", err.Error())
		return errors.New("系统异常")
	}
	defer func() {
		if err := recover(); err != nil {
			s.Session.Rollback()
			log.Error(logPrefix, "发生panic:", err)
		}
	}()

	// 使用领域模型更新默认企业
	distributor := new(distribution_po.DisDistributor)
	if err := distributor.UpdateDefaultEnterprise(s.Session, req.DisId); err != nil {
		s.Session.Rollback()
		log.Error(logPrefix, "更新默认企业失败,err=", err.Error())
		return err
	}

	// 提交事务
	if err := s.Session.Commit(); err != nil {
		s.Session.Rollback()
		log.Error(logPrefix, "提交事务失败,err=", err.Error())
		return errors.New("系统异常")
	}

	return nil
}
