package dto

// 批量更新售卖（上下架）状态  返回结果
type RetailSellStatusResult struct {
	// 返回成功与否
	Data string `json:"data" form:"data" query:"data"`
	// 返回信息
	Msg string `json:"msg,omitempty" form:"msg,omitempty" query:"msg,omitempty"`
	// 错误 信息
	Error RetailSellStatusResultError `json:"error,omitempty" form:"error,omitempty" query:"error,omitempty"`
	// 返回码
	Code int32 `json:"code,omitempty" form:"code,omitempty" query:"code,omitempty"`
	// 返回信息
	Message string `json:"message,omitempty" form:"message,omitempty" query:"message,omitempty"`
}

type RetailSellStatusResultError struct {
	// 错误代码
	Code int32 `json:"code,omitempty" form:"code,omitempty" query:"code,omitempty"`
	// 错误 信息
	Msg string `json:"msg,omitempty" form:"msg,omitempty" query:"msg,omitempty"`
}
