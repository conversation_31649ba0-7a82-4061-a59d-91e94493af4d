package marketing_po

import "time"

type PetPrize struct {
	Id               int     `xorm:"pk autoincr comment('主键') BIGINT(20)" json:"id"`
	UserId           string    `xorm:"varchar(50) notnull default('') comment('获奖人用户id')" json:"user_id"`
	NickName         string    `xorm:"varchar(50) notnull default('') comment('获奖人昵称')" json:"nick_name"`
	Mobile           string    `xorm:"varchar(20) notnull default('') comment('获奖人手机号')" json:"mobile"`
	EnMobile         string    `xorm:"varchar(20) notnull default('') comment('获奖人手机号')" json:"en_mobile"`
	PrizeType        int       `xorm:"tinyint notnull default(0) comment('获奖类型 1-排名奖 2-投票奖 3-创作奖')" json:"prize_type"`
	WorkCode         string    `xorm:"varchar(20) notnull default('') comment('获奖作品编号')" json:"work_code"`
	PrizeCount       int       `xorm:"int notnull default(0) comment('获奖票数')" json:"prize_count"`
	ReceiveStatus    int       `xorm:"tinyint notnull default(0) comment('领奖状态 1待领取(获得资格) 2已领取(已提交地址) 3已核销')" json:"receive_status"`
	PrizeContent     string    `xorm:"varchar(100) notnull default('') comment('奖励内容')" json:"prize_content"`
	CouponCode       string    `xorm:"varchar(50) notnull default('') comment('优惠券码')" json:"coupon_code"`
	OrderSn          string    `xorm:"varchar(50) notnull default('') comment('核销订单号')" json:"order_sn"`
	Address          string    `xorm:"varchar(255) notnull default('') comment('收货地址')" json:"address"`
	Receiver         string    `xorm:"varchar(50) notnull default('') comment('收货人')" json:"receiver"`
	ReceiverMobile   string    `xorm:"varchar(20) notnull default('') comment('收货人手机号带*')" json:"receiver_mobile"`
	ReceiverEnMobile string    `xorm:"varchar(20) notnull default('') comment('收货人加密手机号')" json:"receiver_en_mobile"`
	ReceiveTime      time.Time `xorm:"datetime notnull default('1970-01-01 00:00:00') comment('领取时间')" json:"receive_time"`
	VerifyTime       time.Time `xorm:"datetime notnull default('1970-01-01 00:00:00') comment('核销时间')" json:"verify_time"`
	CreateTime       time.Time `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime       time.Time `json:"update_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

func (PetPrize) TableName() string {
	return "pet_prize"
}
