package api

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/omnibus-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"fmt"
	"net/http"
)

// @Summary 店铺管理-添加店铺
// @Description
// @Tags 宠物连锁SAAS-第三方回调
// @Accept json
// @Produce json
// @Param AddStoreRequest body vo.AddStoreRequest true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/add_store [POST]
func AddStore(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[vo.AddStoreRequest](r)
	if err != nil {
		log.Error("添加店铺，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("添加店铺，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	} else {
		server := services.StoreService{}
		err = server.AddStore(req)
		if err != nil {
			resp.Message = err.Error()
		} else {
			resp.Code = 200
		}
	}
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 店铺管理-添加店铺
// @Description
// @Tags 宠物连锁SAAS-第三方回调
// @Accept json
// @Produce json
// @Param AddStoreRequest body vo.AddStoreRequest true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/wx_code [POST]
func GetShopWxAppCode(w http.ResponseWriter, r *http.Request) {
	server := services.StoreService{}
	server.GetShopWxAppCode()

}

// @Summary 店铺管理-更新店铺
// @Description
// @Tags 宠物连锁SAAS-第三方回调
// @Accept json
// @Produce json
// @Param AddStoreRequest body vo.AddStoreRequest true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/edit_store [POST]
func EditStore(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[vo.AddStoreRequest](r)
	if err != nil {
		log.Error("添加店铺，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("添加店铺，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	} else {
		server := services.StoreService{}
		err = server.EditStore(req)
		if err != nil {
			resp.Message = err.Error()
		}
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// 给saas 提供个接口，同步店铺注册，店铺注册后，同步到eshop
// @Summary 门店信息-同步店铺注册
// @Tags 宠物连锁SAAS-第三方回调
// @Accept  json
// @Produce  json
// @Param SyncStoreRegisterReq query vo.SyncStoreRegisterReq true " "
// @Success 200 {object} vo.SyncStoreRegisterResp
// @Failure 400 {object} vo.SyncStoreRegisterResp
// @Router /external-app/api/store/sync [Post]
func SyncStoreRegister(w http.ResponseWriter, r *http.Request) {
	resp := vo.SyncStoreRegisterResp{}
	resp.Code = 400

	req, err := utils.Bind[vo.SyncStoreRegisterReq](r)
	if err != nil {
		log.Error("同步店铺注册，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("同步店铺注册，参数解析失败：%s", err.Error())
	} else {
		s := services.StoreService{}
		err = s.SyncStoreRegister(req)
		if err != nil {
			log.Error("同步店铺注册失败：err=" + err.Error())
			resp.Message = err.Error()
		} else {
			resp.Code = 200
			resp.Message = "同步成功"
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 同步员工信息
// @Description 同步员工信息到会员和分销商系统
// @Tags 宠物连锁SAAS-第三方回调
// @Accept json
// @Produce json
// @Param SyncEmployeeReq body vo.SyncEmployeeReq true "同步员工信息请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/employee/sync [Post]
func SyncEmployee(w http.ResponseWriter, r *http.Request) {

	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[vo.SyncEmployeeReq](r)
	logPrefix := fmt.Sprintf("同步员工信息,入参:%s", utils.JsonEncode(req))
	log.Info(logPrefix)

	if err != nil {
		log.Error("同步员工信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("同步员工信息，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	s := services.StoreService{}
	err = s.SyncEmployee(req)
	if err != nil {
		log.Error("同步员工信息失败：err=" + err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
		resp.Message = "同步成功"
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// @Summary 删除员工信息
// @Description 将员工在分销商系统中软删除
// @Tags 宠物连锁SAAS-第三方回调
// @Accept json
// @Produce json
// @Param DeleteEmployeeReq body vo.DeleteEmployeeReq true "删除员工信息请求"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /external-app/api/employee/delete [Post]
func DeleteEmployee(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[vo.DeleteEmployeeReq](r)
	if err != nil {
		log.Error("删除员工信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("删除员工信息，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	s := services.StoreService{}
	err = s.DeleteEmployee(req)
	if err != nil {
		log.Error("删除员工信息失败：err=" + err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
		resp.Message = "删除成功"
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
