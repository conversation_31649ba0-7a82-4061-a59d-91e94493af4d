package product_po

type ProStoreFrontCategory struct {
	Id         int    `xorm:"pk autoincr 'id'" json:"id"`                        // 分类id
	StoreId    string `xorm:"varchar(50) 'store_id'" json:"store_id"`            // 门店的id
	CateName   string `xorm:"varchar(100) 'cate_name'" json:"cate_name"`         // 分类名称
	ChannelId  int16  `xorm:"smallint 'channel_id'" json:"channel_id"`           // 渠道 2-美团 3-饿了么
	SyncError  string `xorm:"varchar(1000) 'sync_error'" json:"sync_error"`      // 错误信息
	Status     int8   `xorm:"tinyint 'status'" json:"status"`                    // 0-默认不同步 1-同步
	CreateDate string `xorm:"datetime created 'create_date'" json:"create_date"` // 添加时间
	UpdateDate string `xorm:"datetime updated 'update_date'" json:"update_date"` // 修改时间
}

// 表名
func (p *ProStoreFrontCategory) TableName() string {
	return "pro_store_front_category"
}
