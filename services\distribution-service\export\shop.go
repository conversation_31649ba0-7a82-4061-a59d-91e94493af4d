package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type ShopTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.DisShopListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销员导出
func (e ShopTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.DisShopListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000
	orgName := "润合云店"
	if e.ExportParams.OrgId == 4 {
		orgName = "百林康源"
	}

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName()
	client := services.DisShopService{}

	k := 0
	for {
		ret, _, err := client.DisShopList(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++
			var EnterpriseStatus = "停用"
			if ret[i].EnterpriseStatus == 1 {
				EnterpriseStatus = "正常"
			}
			var EnterpriseType = "企业"
			if ret[i].EnterpriseType == 1 {
				EnterpriseType = "个人"
			}
			axis := fmt.Sprintf("A%d", k+1)
			//企业编码,企业名称,企业类型,企业状态,老板姓名,老板手机号,所属业务员,电商店铺,分销员数量,分销店铺设置,分销店铺名称,分销单数,分销销售额(元),分销佣金总额(元),已结佣金(元),未结佣金(元),提现成功(元),提现申请(元),待提现(元),累计客户数,有效客户数,注册业务员,加入时间,更新时间
			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].EnterpriseId,                             //企业编码
				ret[i].EnterpriseName,                           //企业名称
				EnterpriseType,                                  //企业类型
				EnterpriseStatus,                                //企业状态
				ret[i].DistributorName,                          //老板姓名
				ret[i].DistributorMobile,                        //老板手机号
				ret[i].SalespersonNames,                         //所属业务员
				orgName,                                         //电商店铺
				ret[i].DisNum,                                   //分销员数量
				ret[i].IsSettedShop,                             //分销店铺设置
				ret[i].ShopName,                                 //分销店铺名称
				ret[i].OrderPayNum,                              //分销单数
				utils.Fen2Yuan(ret[i].PaySales),                 //分销销售额(元)
				utils.Fen2Yuan(ret[i].CommissionTotal),          //分销佣金总额(元)
				utils.Fen2Yuan(ret[i].SettledCommissionTotal),   //已结佣金(元)
				utils.Fen2Yuan(ret[i].UnsettledCommissionTotal), //未结佣金(元)
				utils.Fen2Yuan(ret[i].WithdrawSuccess),          //提现成功(元)
				utils.Fen2Yuan(ret[i].WithdrawApply),            //提现申请(元)
				utils.Fen2Yuan(ret[i].WaitWithdraw),             //待提现(元)
				ret[i].TotalCustomer,                            //累计客户数
				ret[i].EffectiveDisNum,                          //有效客户数
				ret[i].RegSalespersonName,                       //注册业务员
				ret[i].CreateTime,                               //加入时间
				ret[i].UpdateTime,                               //更新时间
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return

}

// SetSheetName 分销员导出列表头
func (e ShopTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"企业编码", "企业名称", "企业类型", "企业状态", "老板姓名", "老板手机号", "所属业务员", "电商店铺", "分销员数量", "分销店铺设置", "分销店铺名称", "分销单数", "分销销售额(元)", "分销佣金总额(元)", "已结佣金(元)", "未结佣金(元)", "提现成功(元)", "提现申请(元)", "待提现(元)", "累计客户数", "有效客户数", "注册业务员", "加入时间", "更新时间",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e ShopTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("企业列表导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e ShopTask) OperationFunc(row []string, orgId int) string {
	return ""
}
