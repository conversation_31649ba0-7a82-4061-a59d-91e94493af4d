package services

import (
	po "eShop/domain/omnibus-po"
	orderpo "eShop/domain/order-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	so "eShop/view-model/omnibus-vo"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/spf13/cast"
)

type PrinterService struct {
	common.BaseService
}

func (p PrinterService) DelPrinter(req so.DelPrinterReq) (err error) {
	logPrefix := fmt.Sprintf("添加或编辑打印机，参数是：%s ", utils.InterfaceToJSON(req))
	log.Info(logPrefix)

	p.Begin()
	defer p.Close()

	session := p.Engine.NewSession()
	defer session.Close()

	var printerManage po.PrinterManage
	if _, err = session.Table("eshop.printer_manage").Where("store_id = ? and id = ?", req.StoreId, req.Id).Get(&printerManage); err != nil {
		log.Error(logPrefix, "数据库查询打印机数据失败，err=", err.Error())
		err = errors.New("数据库查询打印机数据失败")
		return
	}

	if printerManage.Id == 0 {
		log.Error(logPrefix, "该门店不存在此打印机")
		err = errors.New("该门店不存在此打印机")
		return
	}

	if _, err = session.Where("id = ?", req.Id).Delete(&po.PrinterManage{}); err != nil {
		log.Error(logPrefix, "数据库删除打印机数据失败，err=", err.Error())
		err = errors.New("数据库删除打印机数据失败")
		return
	}

	return
}

func (p PrinterService) PrinterInfo(req so.PrinterInfoReq) (out so.PrinterInfo, err error) {
	logPrefix := fmt.Sprintf("添加或编辑打印机，参数是：%s ", utils.InterfaceToJSON(req))
	log.Info(logPrefix)

	p.Begin()
	defer p.Close()

	session := p.Engine.NewSession()
	defer session.Close()

	if _, err = session.Table("eshop.printer_manage").Where("store_id = ?", req.StoreId).Get(&out); err != nil {
		log.Error(logPrefix, "数据库查询打印机数据失败，err=", err.Error())
		err = errors.New("数据库查询打印机数据失败")
		return
	}

	return
}

func (p PrinterService) AddPrinter(req so.AddPrinterReq) (err error) {
	logPrefix := fmt.Sprintf("添加或编辑打印机，参数是：%s ", utils.InterfaceToJSON(req))
	log.Info(logPrefix)

	p.Begin()
	defer p.Close()

	session := p.Engine.NewSession()
	defer session.Close()

	var printerManage po.PrinterManage
	if _, err = session.Table("eshop.printer_manage").Where("store_id = ?", req.StoreId).Get(&printerManage); err != nil {
		log.Error(logPrefix, "数据库查询打印机数据失败，err=", err.Error())
		err = errors.New("数据库查询打印机数据失败")
		return
	}

	if req.Id == 0 {
		if printerManage.Id > 0 {
			log.Error(logPrefix, "该门店已存在打印机，请勿重复添加")
			err = errors.New("该门店已存在打印机，请勿重复添加")
			return
		}

		//调用飞鹅云打印接口添加打印机
		snlist := fmt.Sprintf("%s#%s#%s", req.PrinterSn, req.PrinterKey, req.PrinterName)
		err = utils.FeieYunService{}.Addprinter(snlist)

		//已被添加过，也当作是成功的
		if err != nil && !strings.Contains(err.Error(), "已被添加过") {
			log.Error(logPrefix, "飞鹅云添加打印机失败，err=", err.Error())
			err = errors.New("添加打印机失败," + err.Error())
			return
		}

		if _, err = session.Insert(&po.PrinterManage{
			PrinterSn:   req.PrinterSn,
			StoreId:     req.StoreId,
			CreateDate:  time.Now(),
			UpdateDate:  time.Now(),
			PrinterName: req.PrinterName,
			PrinterKey:  req.PrinterKey,
		}); err != nil {
			log.Error(logPrefix, "数据库插入打印机数据失败，err=", err.Error())
			err = errors.New("数据库插入打印机数据失败")
			return
		}
	} else {
		//一个门店只能绑定一个打印机
		if printerManage.Id > 0 && printerManage.Id != req.Id {
			log.Error(logPrefix, "该门店不已存在此打印机")
			err = errors.New("该门店不已存在此打印机")
			return
		}

		if printerManage.PrinterSn != req.PrinterSn {
			// 打印机变了,添加打印机，调用飞鹅云打印接口添加打印机
			snlist := fmt.Sprintf("%s#%s#%s", req.PrinterSn, req.PrinterKey, req.PrinterName)
			err = utils.FeieYunService{}.Addprinter(snlist)

			//已被添加过，也当作是成功的
			if err != nil && !strings.Contains(err.Error(), "已被添加过") {
				log.Error(logPrefix, "飞鹅云添加打印机失败，err=", err.Error())
				err = errors.New("添加打印机失败," + err.Error())
				return
			}
		}

		if _, err = session.Table("eshop.printer_manage").Where("id = ?", req.Id).Cols("printer_sn", "update_date", "printer_name", "printer_key").Update(&po.PrinterManage{
			PrinterSn:   req.PrinterSn,
			UpdateDate:  time.Now(),
			PrinterName: req.PrinterName,
			PrinterKey:  req.PrinterKey,
		}); err != nil {
			log.Error(logPrefix, "数据库更新打印机数据失败，err=", err.Error())
			err = errors.New("数据库更新打印机数据失败")
		}
	}

	return nil
}

// 打印收银台小票
func (s *PrinterService) PrintCashierTicket(orderSn string) error {
	// 获取打印数据
	info, err := s.GetCashierTicketInfo(orderSn)
	if err != nil {
		return err
	}
	// 查询打印机
	var printerManage po.PrinterManage
	has, err := s.Engine.Table("eshop.printer_manage").Where("store_id = ?", info.StoreId).Get(&printerManage)
	if err != nil || !has {
		return errors.New("未找到对应门店的打印机")
	}

	//查询是否需要打印
	type PrintField struct {
		DisplayColumn string
		DisplaySort   int64
	}

	var printFields []PrintField
	err = s.Engine.Table("eshop_saas.sys_print_config").
		Alias("c").
		Join("LEFT", "eshop_saas.sys_print_template t", "c.template_id = t.id").
		Where("c.tenant_id = ? AND t.template_type = 1 AND c.is_display = 1", printerManage.StoreId).
		Select("t.display_column, c.display_sort").
		OrderBy("c.display_sort asc").
		Find(&printFields)
	if err != nil {
		return errors.New("查询打印模板配置失败: " + err.Error())
	}
	fieldSet := map[string]bool{}
	for _, f := range printFields {
		fieldSet[f.DisplayColumn] = true
	}

	feieYunService := utils.FeieYunService{}
	content := "<CB>收银台小票</CB><BR>"

	if fieldSet["店铺名称"] {
		content += fmt.Sprintf("%s<BR>", info.StoreName)
	}
	if fieldSet["订单号"] {
		content += fmt.Sprintf("订单号：%s<BR>", info.OrderSn)
	}
	if fieldSet["下单时间"] {
		content += fmt.Sprintf("下单时间：%s<BR>", info.OrderTime)
	}
	if fieldSet["收银员"] {
		content += fmt.Sprintf("收银员：%s<BR>", info.CashierName)
	}
	if fieldSet["客户名称"] {
		content += fmt.Sprintf("客户名称：%s<BR>", info.CustomerName)
	}
	if fieldSet["手机号"] {
		content += fmt.Sprintf("手机号：%s<BR>", info.CustomerMobile)
	}
	if fieldSet["宠物名称"] && info.PetName != "" {
		content += fmt.Sprintf("宠物名称：%s<BR>", info.PetName)
	}

	content += "--------------------------------<BR>"
	content += "商品    单价   数量   小计<BR>"
	for _, p := range info.Products {
		nameArr := feieYunService.SplitStringByNum(p.ProductName, 16)
		for i := 0; i < len(nameArr); i++ {
			if i == 0 {
				content += fmt.Sprintf("%s %s %d %s<BR>", nameArr[i], p.UnitPrice, p.Quantity, p.Subtotal)
			} else {
				content += fmt.Sprintf("%s<BR>", nameArr[i])
			}
		}
	}
	content += "--------------------------------<BR>"

	if fieldSet["账单总价"] {
		content += fmt.Sprintf("账单总价：%s<BR>", info.TotalAmount)
	}
	if fieldSet["优惠金额"] {
		content += fmt.Sprintf("优惠金额：%s<BR>", info.DiscountAmount)
	}
	if fieldSet["支付方式"] {
		content += fmt.Sprintf("支付方式：%s<BR>", info.PayType)
	}
	if fieldSet["应付金额"] {
		content += fmt.Sprintf("应付金额：%s<BR>", info.ShouldPay)
	}
	if fieldSet["实付"] {
		content += fmt.Sprintf("实付：%s<BR>", info.ActualPay)
	}
	if fieldSet["退款金额"] {
		content += fmt.Sprintf("退款金额：%s<BR>", info.RefundAmount)
	}
	if fieldSet["订单备注"] {
		content += fmt.Sprintf("订单备注：%s<BR>", info.OrderRemark)
	}
	content += "<CUT>"

	// 打印
	err = feieYunService.PrintOrder(printerManage.PrinterSn, content)
	if err != nil {
		return errors.New("请求飞鹅打印机接口失败: " + err.Error())
	}
	return nil
}

// 打印充值开卡小票
func (s *PrinterService) PrintRechargeTicket(orderSn string) error {
	// 获取打印数据
	info, err := s.GetRechargeTicketInfo(orderSn)
	if err != nil {
		return err
	}
	// 查询打印机
	var printerManage po.PrinterManage
	has, err := s.Engine.Table("eshop.printer_manage").Where("store_id = ?", info.StoreId).Get(&printerManage)
	if err != nil || !has {
		return errors.New("未找到对应门店的打印机")
	}

	type PrintField struct {
		DisplayColumn string
		DisplaySort   int64
	}
	var printFields []PrintField
	err = s.Engine.Table("eshop_saas.sys_print_config").
		Alias("c").
		Join("LEFT", "eshop_saas.sys_print_template t", "c.template_id = t.id").
		Where("c.tenant_id = ? AND t.template_type = 2 AND c.is_display = 1", printerManage.StoreId).
		Select("t.display_column, c.display_sort").
		OrderBy("c.display_sort asc").
		Find(&printFields)
	if err != nil {
		return errors.New("查询打印模板配置失败: " + err.Error())
	}
	fieldSet := map[string]bool{}
	for _, f := range printFields {
		fieldSet[f.DisplayColumn] = true
	}

	feieYunService := utils.FeieYunService{}
	content := "<CB>充值/开卡小票</CB><BR>"

	if fieldSet["店铺名称"] {
		content += fmt.Sprintf("%s<BR>", info.StoreName)
	}
	if fieldSet["订单号"] {
		content += fmt.Sprintf("订单号：%s<BR>", info.OrderSn)
	}
	if fieldSet["下单时间"] {
		content += fmt.Sprintf("下单时间：%s<BR>", info.OrderTime)
	}
	if fieldSet["收银员"] {
		content += fmt.Sprintf("收银员：%s<BR>", info.CashierName)
	}
	if fieldSet["客户名称"] {
		content += fmt.Sprintf("客户名称：%s<BR>", info.CustomerName)
	}
	if fieldSet["手机号"] {
		content += fmt.Sprintf("手机号：%s<BR>", info.CustomerMobile)
	}

	content += "--------------------------------<BR>"
	content += "卡名    充值   赠送<BR>"
	for _, c := range info.Cards {
		content += fmt.Sprintf("%s %s %s<BR>", c.CardName, c.Recharge, c.Gift)
	}
	content += "--------------------------------<BR>"

	if fieldSet["支付方式"] {
		content += fmt.Sprintf("支付方式：%s<BR>", info.PayType)
	}
	if fieldSet["实付"] {
		content += fmt.Sprintf("实付：%s<BR>", info.ActualPay)
	}
	content += "<CUT>"

	err = feieYunService.PrintOrder(printerManage.PrinterSn, content)
	if err != nil {
		return errors.New("请求飞鹅打印机接口失败: " + err.Error())
	}
	return nil
}

// 打印预存押金小票
func (s *PrinterService) PrintDepositTicket(orderSn string) error {
	// 获取打印数据
	info, err := s.GetDepositTicketInfo(orderSn)
	if err != nil {
		return err
	}
	// 查询打印机
	var printerManage po.PrinterManage
	has, err := s.Engine.Table("eshop.printer_manage").Where("store_id = ?", info.StoreId).Get(&printerManage)
	if err != nil || !has {
		return errors.New("未找到对应门店的打印机")
	}
	type PrintField struct {
		DisplayColumn string
		DisplaySort   int64
	}
	var printFields []PrintField
	err = s.Engine.Table("eshop_saas.sys_print_config").
		Alias("c").
		Join("LEFT", "eshop_saas.sys_print_template t", "c.template_id = t.id").
		Where("c.tenant_id = ? AND t.template_type = 3 AND c.is_display = 1", printerManage.StoreId).
		Select("t.display_column, c.display_sort").
		OrderBy("c.display_sort asc").
		Find(&printFields)
	if err != nil {
		return errors.New("查询打印模板配置失败: " + err.Error())
	}
	fieldSet := map[string]bool{}
	for _, f := range printFields {
		fieldSet[f.DisplayColumn] = true
	}

	feieYunService := utils.FeieYunService{}
	content := "<CB>预存押金小票</CB><BR>"

	if fieldSet["店铺名称"] {
		content += fmt.Sprintf("%s<BR>", info.StoreName)
	}
	if fieldSet["客户名称"] {
		content += fmt.Sprintf("客户名称：%s<BR>", info.CustomerName)
	}
	if fieldSet["手机号"] {
		content += fmt.Sprintf("手机号：%s<BR>", info.CustomerMobile)
	}
	if fieldSet["充值金额"] {
		content += fmt.Sprintf("充值金额：%s<BR>", info.RechargeAmount)
	}
	if fieldSet["押金金额"] {
		content += fmt.Sprintf("押金金额：%s<BR>", info.DepositAmount)
	}
	if fieldSet["收银员"] {
		content += fmt.Sprintf("收银员：%s<BR>", info.CashierName)
	}
	if fieldSet["收款时间"] {
		content += fmt.Sprintf("收款时间：%s<BR>", info.PayTime)
	}
	if fieldSet["支付方式"] {
		content += fmt.Sprintf("支付方式：%s<BR>", info.PayType)
	}
	if fieldSet["实付"] {
		content += fmt.Sprintf("实付：%s<BR>", info.ActualPay)
	}
	content += "<CUT>"

	err = feieYunService.PrintOrder(printerManage.PrinterSn, content)
	if err != nil {
		return errors.New("请求飞鹅打印机接口失败: " + err.Error())
	}
	return nil
}

// 获取收银台小票信息
func (s *PrinterService) GetCashierTicketInfo(orderSn string) (*so.CashierOrderInfo, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 查询主订单
	var order orderpo.OrderMain
	has, err := session.Table("dc_order.order_main").Where("order_sn = ?", orderSn).Get(&order)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("未找到对应订单")
	}

	// 查询订单详情
	var detail orderpo.OrderDetail
	has, err = session.Table("dc_order.order_detail").Where("order_sn = ?", orderSn).Get(&detail)
	if err != nil {
		return nil, fmt.Errorf("查询订单详情失败: %v", err)
	}

	// 查询商品明细
	var products []orderpo.OrderProduct
	err = session.Table("dc_order.order_product").Where("order_sn = ?", orderSn).Find(&products)
	if err != nil {
		return nil, fmt.Errorf("查询商品明细失败: %v", err)
	}

	// 查询商品明细
	var payments []orderpo.OrderPayment
	err = session.Table("dc_order.order_payment").Where("order_sn = ?", orderSn).Find(&payments)
	if err != nil {
		return nil, fmt.Errorf("查询支付明细失败: %v", err)
	}

	// 组装商品明细
	var productList []so.CashierProductInfo
	for _, p := range products {
		productList = append(productList, so.CashierProductInfo{
			ProductName: p.ProductName,
			UnitPrice:   fmt.Sprintf("%.2f", float64(p.PayPrice)/100),
			Quantity:    p.Number,
			Subtotal:    fmt.Sprintf("%.2f", float64(p.PayPrice*p.Number)/100),
		})
	}
	payName := ""
	for _, p := range payments {
		payName += orderpo.PayTypeTextMap[p.PayType] + "|"
	}
	payName = strings.TrimSuffix(payName, "|")

	// 手机号脱敏
	mobile := maskMobile(order.MemberTel)

	info := &so.CashierOrderInfo{
		StoreName:      order.ShopName,
		OrderSn:        order.OrderSn,
		OrderTime:      order.CreateTime.Format("2006-01-02 15:04:05"),
		CashierName:    detail.CashierName,
		CustomerName:   order.MemberName,
		CustomerMobile: mobile,
		PetName:        detail.PetName, // 如有宠物名字段请补充
		Products:       productList,
		TotalAmount:    fmt.Sprintf("%.2f", float64(order.Total+order.Privilege)/100),
		DiscountAmount: fmt.Sprintf("%.2f", float64(order.Privilege)/100),
		PayType:        payName,
		ShouldPay:      fmt.Sprintf("%.2f", float64(order.Total)/100),
		ActualPay:      fmt.Sprintf("%.2f", float64(order.PayAmount)/100),
		RefundAmount:   fmt.Sprintf("%.2f", float64(order.RefundAmount)/100),
		OrderRemark:    detail.BuyerMemo,
		StoreId:        cast.ToString(order.ShopId),
	}
	return info, nil
}

// 获取充值开卡小票信息
func (s *PrinterService) GetRechargeTicketInfo(orderSn string) (*so.RechargeTicketInfo, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 查询主订单
	var order orderpo.OrderMain
	has, err := session.Table("dc_order.order_main").
		Where("order_sn = ? ", orderSn).
		Get(&order)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("未找到对应的储值卡订单")
	}

	// 查询主订单
	var orderDetail orderpo.OrderDetail
	has, err = session.Table("dc_order.order_detail").
		Where("order_sn = ? ", orderSn).
		Get(&orderDetail)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("未找到对应的储值卡订单")
	}

	// 查询门店名称（如有门店表可查）
	storeName := order.ShopName

	// 查询卡信息
	var products []orderpo.OrderProduct
	err = session.Table("dc_order.order_product").
		Where("order_sn = ?", orderSn).
		Find(&products)
	if err != nil {
		return nil, fmt.Errorf("查询卡信息失败: %v", err)
	}

	// 组装卡信息
	var cards []so.RechargeCardInfo
	for _, p := range products {
		re := regexp.MustCompile(`\d+(\.\d+)?`) // 匹配整数或小数
		nums := re.FindAllString(p.Specs, -1)
		if len(nums) >= 2 {
			cards = append(cards, so.RechargeCardInfo{
				CardName: p.ProductName,
				Recharge: nums[0], // 假设PayPrice为充值金额，单位分
				Gift:     nums[1], // 假设Privilege为赠送金额，单位分
			})
		}
	}

	// 查询商品明细
	var payments []orderpo.OrderPayment
	err = session.Table("dc_order.order_payment").Where("order_sn = ?", orderSn).Find(&payments)
	if err != nil {
		return nil, fmt.Errorf("查询支付明细失败: %v", err)
	}

	payName := ""
	for _, p := range payments {
		payName += orderpo.PayTypeTextMap[p.PayType] + "|"
	}
	payName = strings.TrimSuffix(payName, "|")

	// 手机号脱敏
	//mobile := maskMobile(order.MemberTel)

	info := &so.RechargeTicketInfo{
		StoreName:      storeName,
		OrderSn:        order.OrderSn,
		OrderTime:      order.CreateTime.Format("2006-01-02 15:04:05"),
		CashierName:    orderDetail.CashierName, // 假设收银员为AcceptUsername
		CustomerName:   order.MemberName,
		CustomerMobile: order.MemberTel,
		Cards:          cards,
		PayType:        payName, // 支付方式转换
		ActualPay:      fmt.Sprintf("%.2f", float64(order.PayAmount)/100),
		QRCode:         "", // 如有二维码可补充
		StoreId:        cast.ToString(order.ShopId),
	}
	return info, nil
}

// 手机号脱敏
func maskMobile(mobile string) string {
	if len(mobile) != 11 {
		return mobile
	}
	return mobile[:3] + "****" + mobile[7:]
}

// 支付方式转换
func getPayTypeName(payMode int) string {
	switch payMode {
	case 1:
		return "支付宝支付"
	case 2:
		return "微信支付"
	case 8:
		return "储值卡支付"
	default:
		return "其他"
	}
}

// 获取预存押金小票信息
func (s *PrinterService) GetDepositTicketInfo(orderSn string) (*so.DepositTicketInfo, error) {

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	var deposit po.FosterDeposit
	has, err := s.Engine.Table("f_foster_deposit").Where("id = ? and deposit_type='RECHARGE'", cast.ToInt64(orderSn)).Get(&deposit)
	if err != nil {
		return nil, fmt.Errorf("数据库查询失败: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("未找到对应押金订单")
	}
	financeCode := cast.ToString(deposit.TenantId)
	var financeCodes []string
	financeCodes = append(financeCodes, financeCode)
	storemap, err := new(po.Store).GetStoreInfoByFinanceCode(session, financeCodes)
	if err != nil {
		return nil, fmt.Errorf("获取店铺信息失败")
	}

	// 这里你可以根据实际业务补充门店名称、二维码等信息
	info := &so.DepositTicketInfo{
		StoreName:      storemap[financeCode].Name, // 示例，实际应查门店表
		CustomerName:   deposit.CustomerName,
		CustomerMobile: maskMobile(deposit.CustomerMobile),
		RechargeAmount: fmt.Sprintf("%.2f", deposit.Amount), // 假设充值金额即为amount
		DepositAmount:  fmt.Sprintf("%.2f", deposit.Amount), // 假设押金金额即为amount
		CashierName:    deposit.SellerName,
		PayTime:        deposit.CreatedTime.Format("2006-01-02 15:04:05"),
		PayType:        deposit.PayType,
		ActualPay:      fmt.Sprintf("%.2f", deposit.Amount),
		QRCode:         "", // 如有二维码可补充
		StoreId:        cast.ToString(deposit.TenantId),
	}
	return info, nil
}
