package utils

import (
	"github.com/spf13/cast"
)

func IntSliceToStringSlice(intSlice []int) []string {
	strSlice := make([]string, len(intSlice))
	for i, v := range intSlice {
		strSlice[i] = cast.ToString(v)
	}
	return strSlice
}

// InIntSlice 检查整数是否在切片中
func InIntSlice(needle int, haystack []int) bool {
	for _, v := range haystack {
		if needle == v {
			return true
		}
	}
	return false
}

// StringSliceToIntSlice 将字符串切片转换为整数切片
func StringSliceToIntSlice(strSlice []string) []int {
	intSlice := make([]int, 0)
	for _, v := range strSlice {
		if cast.ToInt(v) > 0 {
			intSlice = append(intSlice, cast.ToInt(v))
		}
	}
	return intSlice
}

// StringSliceToInt64Slice 将字符串切片转换为整数切片
func StringSliceToInt64Slice(strSlice []string) []int64 {
	int64Slice := make([]int64, 0)
	for _, v := range strSlice {
		if cast.ToInt64(v) > 0 {
			int64Slice = append(int64Slice, cast.ToInt64(v))
		}
	}
	return int64Slice
}

// IntSliceToInt64Slice 将字符串切片转换为整数切片
func IntSliceToInt64Slice(strSlice []int) []int64 {
	int64Slice := make([]int64, 0)
	for _, v := range strSlice {
		if cast.ToInt(v) > 0 {
			int64Slice = append(int64Slice, cast.ToInt64(v))
		}
	}
	return int64Slice
}
