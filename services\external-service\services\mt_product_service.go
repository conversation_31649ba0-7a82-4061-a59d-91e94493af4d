package services

import (
	"context"
	"eShop/domain/product-po"
	"eShop/services/common"
	dto2 "eShop/view-model/external-vo/dto"
	meituan2 "eShop/view-model/external-vo/meituan"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/golang/glog"
	"net/http"
	"strconv"
	"strings"
	"time"

	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
)

type MtProductService struct {
}

// /   retail/sellStatus 批量更新售卖（上下架）状态
func (gy *MtProductService) RetailSellStatus(ctx context.Context, model *proto.RetailSellStatusRequest) (*proto.RetailSellStatusResult, error) {
	jsonByte, err := json.Marshal(model) // 参数对像转Json

	storeMasterId := model.StoreMasterId // GetAppChannelByStoreId(model.AppPoiCode)

	//r := mtRatelimitedRetailSellStatus.Reserve()
	r := common.GetLimiter("batch.sell.status", 0).Reserve()
	time.Sleep(r.Delay())

	return PostHandleFunc("【批量更新售卖（上下架）状态】", jsonByte, err, utils.MtRetailSellStatus, storeMasterId)
}

// v6.4.0
// retailCat/list 查询门店商品分类列表                                                                      //  OK
func (gy *MtProductService) RetailCatList(ctx context.Context, model *proto.AppPoiCodeRequest) (*proto.AppPoiCodeResult, error) {

	result := &proto.AppPoiCodeResult{}
	jsonByte, err := json.Marshal(model)
	storeMasterId, _ := GetAppChannelByStoreId(model.AppPoiCode)
	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【查询门店商品分类列表】", jsonByte, err, utils.MtRetailCatList, storeMasterId)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, &result)
		if err != nil {
			result.Code = 400
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultJson
			result.Error = "值转Json异常,错误信息：" + err.Error()
			logBuilder.WriteString(result.Message + result.Error)
			log.Info(logBuilder.String()) // 记录日志
			return result, nil
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			result.Error = retailSellStatusResult.Msg
		} else {
			result.Error = resultJson
		}
	}
	result.Code = retailSellStatusResult.Code
	result.Message = retailSellStatusResult.Message
	return result, nil
}

// v6.4.0 批量更新编辑商品
// / retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑]  （20次/秒。可传商品数据限定不能超过200组）
func (gy *MtProductService) RetailBatchinitdata(ctx context.Context, model *proto.RetailBatchinitdataRequest) (*proto.RetailSellStatusResult, error) {
	log.Info("美团更新参数：", utils.JsonEncode(model))
	reserve := common.GetLimiter("mt.limit.batch.init.data", 0).Reserve()
	time.Sleep(reserve.Delay())

	var jsonByte []byte
	var err error
	if model.OperateType == 2 {
		var allData = make(map[string]interface{})
		allData["app_poi_code"] = model.AppPoiCode
		allData["operate_type"] = model.OperateType

		var datas = make([]map[string]interface{}, 0)
		for _, v := range model.FoodData {
			var data = make(map[string]interface{})
			data["app_food_code"] = v.AppFoodCode
			if len(v.Name) > 0 {
				data["name"] = v.Name
			}
			data["is_specialty"] = v.IsSpecialty
			if len(v.Description) > 0 {
				data["description"] = v.Description
			}
			if len(v.SellPoint) > 0 {
				data["sell_point"] = v.SellPoint
			}
			if len(v.Picture) > 0 {
				data["picture"] = v.Picture
			}
			if len(v.CommonAttrValue) > 0 {
				data["common_attr_value"] = v.CommonAttrValue
			}
			if v.TagId > 0 {
				data["tag_id"] = v.TagId
			}

			if len(v.CategoryCode) > 0 {
				data["category_code"] = v.CategoryCode
			} else if len(v.CategoryName) > 0 {
				data["category_name"] = v.CategoryName
			}
			if v.VideoId > 0 {
				data["video_id"] = v.VideoId
			}
			if len(v.PictureContents) > 0 {
				data["picture_contents"] = v.PictureContents
			}
			if len(v.Skus) > 0 {
				sku := updateSkusPrice(v.Skus)
				if len(sku) > 0 {
					data["skus"] = sku
				}
			}
			datas = append(datas, data)
		}
		allData["food_data"] = datas
		jsonByte, err = json.Marshal(allData)
	} else {
		//2023年8月美团将box_num从string类型变为long类型。我司解决办法如下：
		var data meituan2.RetailBatchinitdataRequest
		copier.Copy(&data, model)
		//copier无法从string类型copy到int64类型，所以需要手动赋值
		for m := 0; m < len(model.FoodData); m++ {
			for n := 0; n < len(model.FoodData[m].Skus); n++ {
				boxNum, err := strconv.Atoi(model.FoodData[m].Skus[n].BoxNum)
				if err != nil {
					boxNum = 0
				}
				data.FoodData[m].Skus[n].BoxNum = int64(boxNum)
			}
		}

		jsonByte, err = json.Marshal(data) // 参数对像转Json
	}
	storeMasterId := model.StoreMasterId //GetAppChannelByStoreId(model.AppPoiCode)
	return PostHandleFunc("【批量创建/更新商品[支持商品多规格,不含删除逻辑]】", jsonByte, err, utils.MtRetailBatchinitdata, storeMasterId)
}

// v6.4.0 批量更新编辑商品
// /    retail/initdata 创建/更新商品[支持商品多规格,不含删除逻辑]   50次/S
func (gy *MtProductService) RetailInitdata(ctx context.Context, model *proto.RetailInitdataRequest) (*proto.RetailSellStatusResult, error) {
	var jsonByte []byte
	var err error
	if model.OperateType == 2 {
		var allData = make(map[string]interface{})
		allData["app_poi_code"] = model.AppPoiCode
		allData["operate_type"] = model.OperateType

		var datas = make([]map[string]interface{}, 0)
		v := model
		var data = make(map[string]interface{})
		data["app_food_code"] = v.AppFoodCode
		if len(v.Name) > 0 {
			data["name"] = v.Name
		}
		data["is_specialty"] = v.IsSpecialty
		if len(v.Description) > 0 {
			data["description"] = v.Description
		}
		if len(v.SellPoint) > 0 {
			data["sell_point"] = v.SellPoint
		}
		if len(v.Picture) > 0 {
			data["picture"] = v.Picture
		}
		if len(v.CommonAttrValue) > 0 {
			data["common_attr_value"] = v.CommonAttrValue
		}
		if v.TagId > 0 {
			data["tag_id"] = v.TagId
		}

		if len(v.CategoryCode) > 0 {
			data["category_code"] = v.CategoryCode
		} else if len(v.CategoryName) > 0 {
			data["category_name"] = v.CategoryName
		}
		if v.VideoId > 0 {
			data["video_id"] = v.VideoId
		}
		if len(v.PictureContents) > 0 {
			data["picture_contents"] = v.PictureContents
		}
		if len(v.Skus) > 0 {
			sku := updateSkusPrice(v.Skus)
			if len(sku) > 0 {
				data["skus"] = sku
			}
		}
		datas = append(datas, data)

		allData["food_data"] = datas
		jsonByte, err = json.Marshal(allData)
	} else {
		//2023年8月美团将box_num从string类型变为long类型。我司解决办法如下：
		var data meituan2.RetailInitData
		copier.Copy(&data, model)
		//copier无法从string类型copy到int64类型，所以需要手动赋值
		for i := 0; i < len(model.Skus); i++ {
			boxNum, err := strconv.Atoi(model.Skus[i].BoxNum)
			if err != nil {
				boxNum = 0
			}
			data.Skus[i].BoxNum = int64(boxNum)
		}
		jsonByte, err = json.Marshal(data) // 参数对像转Json
	}

	storeMasterId := model.StoreMasterId //GetAppChannelByStoreId(model.AppPoiCode)
	return PostHandleFunc("【创建/更新商品[支持商品多规格,不含删除逻辑]】", jsonByte, err, utils.MtRetailInitdata, storeMasterId)
}

// 未使用
// / retail/multipois/batchinitdata 批量创建/更新商品信息至多店 （1次/5分钟，200个商品，最多可同步至3000个门店）
func (gy *MtProductService) RetailMultipoisBatchinitdata(ctx context.Context, model *proto.MultipoisBatchinitdataRequest) (*proto.RetailSellStatusResult, error) {
	var jsonByte []byte
	var err error
	if model.InitData.Type == 2 {
		var bigData = make(map[string]interface{})
		var allData = make(map[string]interface{})
		allData["app_poi_codes"] = model.InitData.AppPoiCodes
		allData["type"] = model.InitData.Type
		allData["is_all_pois"] = model.InitData.IsAllPois
		var datas = make([]map[string]interface{}, 0)
		for _, v := range model.InitData.RetailInfo {
			var data = make(map[string]interface{})
			if len(v.Name) > 0 {
				data["name"] = v.Name
			}
			if len(v.Description) > 0 {
				data["description"] = v.Description
			}
			if len(v.SellPoint) > 0 {
				data["sell_point"] = v.SellPoint
			}
			if len(v.Picture) > 0 {
				data["picture"] = v.Picture
			}
			if v.VideoId > 0 {
				data["video_id"] = v.VideoId
			}
			if len(v.PictureContents) > 0 {
				data["picture_contents"] = v.PictureContents
			}
			if len(v.Skus) > 0 {
				sku := updateSkusPrice(v.Skus)
				if len(sku) > 0 {
					data["skus"] = sku
				}
			}
			datas = append(datas, data)
		}
		allData["retail_info"] = datas
		bigData["init_data"] = allData
		jsonByte, err = json.Marshal(bigData)
	} else {
		jsonByte, err = json.Marshal(model) // 参数对像转Json
	}
	//jsonByte,err :=json.Marshal(model)    // 参数对像转Json
	requestStr := strings.Replace(string(jsonByte), "\"is_all_pois\":0,", "", 1)
	jsonByte = []byte(requestStr)
	storeMasterId, _ := GetAppChannelByStoreId(model.InitData.AppPoiCodes)
	return PostHandleFunc("【批量创建/更新商品信息至多店】", jsonByte, err, utils.MtMultipoisBatchinitdata, storeMasterId)
}

// v 6.4.0
// retailCat/update 创建/更新商品分类
// 当前接口最高调用5次/秒
func (gy *MtProductService) RetailCatUpdate(model *proto.RetailCatUpdateRequest) (*proto.RetailSellStatusResult, error) {
	//r := mtRateLimitedRetailcatUpdate.Reserve()
	r := common.GetLimiter("mt-rate-limited-retailcat-update", 0).Reserve()
	time.Sleep(r.Delay())
	var request dto2.RetailCatUpdateRequest
	// APP方门店id 必镇
	request.App_poi_code = model.AppPoiCode
	// 原始的商品分类id     与category_name_origin至多填写一个
	request.Category_code_origin = model.CategoryCodeOrigin
	//  原始的商品分类名称 与category_code_origin至多填写一个
	request.Category_name_origin = model.CategoryNameOrigin
	// 商品分类id 非必镇
	request.Category_code = model.CategoryCode
	// 商品分类名称 必镇
	request.Category_name = model.CategoryName
	//  二级商品分类id  非必镇
	request.Secondary_category_code = model.SecondaryCategoryCode
	// 二级商品分类名称 非必镇
	request.Secondary_category_name = model.SecondaryCategoryName
	// 商品分类的排序 非必镇
	request.Sequence = model.Sequence
	//  调整分类层级时的目标等级   非必镇
	request.Target_level = model.TargetLevel
	//  调整为二级分类时所属的一级分类名称  仅当target_level=2时填写
	request.Target_parent_name = model.TargetParentName
	//  是否开启置顶 非必镇
	request.Top_flag = model.TopFlag
	// 置顶周期 当top_flag=1时必填，且需要与period字段同时设置。
	request.Weeks_time = model.WeeksTime
	// 置顶时段   当top_flag=1时必填，且需要与weeks_time字段同时设置
	request.Period = model.Period
	jsonByte, err := json.Marshal(request) // 参数对像转Json
	storeMasterId := model.StoreMasterId   //GetAppChannelByStoreId(model.AppPoiCode)
	return PostHandleFunc("【创建/更新商品分类】", jsonByte, err, utils.MtRetailCatUpdate, storeMasterId)
}

// retail/getSpTagIds 获取美团后台商品类目（末级类目id） 20次/秒
// todo.sean 如果获取的是全部类目信息，缓存到redis
func (gy *MtProductService) RetailGetSpTagIds(ctx context.Context, model *proto.AppPoiCodeRequest) (*proto.RetailGetSpTagIdsResult, error) {
	result := &proto.RetailGetSpTagIdsResult{}
	jsonByte, err := json.Marshal(model)
	// 	APP方门店id。
	//  若上传本参数，则仅返回该门店经营品类有售卖权限的类目信息；若不传，则返回美团闪购各品类的全部类目信息。
	var storeMasterId int32
	if model.AppPoiCode == "" {
		storeMasterId = int32(1)
	} else {
		storeMasterId, _ = GetAppChannelByStoreId(model.AppPoiCode)
	}

	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【获取美团后台商品类目（末级类目id）】", jsonByte, err, utils.MtRetailGetSpTagIds, storeMasterId)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, &result)
		if err != nil {
			result.Code = 400
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultJson
			result.Error = "值转Json异常,错误信息：" + err.Error()
			logBuilder.WriteString(result.Message + result.Error)
			log.Info(logBuilder.String()) // 记录日志
			return result, nil
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			result.Error = retailSellStatusResult.Msg
		} else {
			result.Error = resultJson
		}
	}
	result.Code = retailSellStatusResult.Code
	result.Message = retailSellStatusResult.Message
	return result, nil
}

// category/attr/list 根据末级类目id获取类目属性列表
func (gy *MtProductService) CategoryAttrList(ctx context.Context, model *proto.CategoryAttrListRequest) (*proto.CategoryAttrListResult, error) {
	result := &proto.CategoryAttrListResult{}
	jsonByte, err := json.Marshal(model)
	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【查询特殊属性的属性值列表】", jsonByte, err, utils.MtGwCategoryAttrListApi, 1)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, &result)
		if err != nil {
			result.Code = 400
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultJson
			result.Error = "值转Json异常,错误信息：" + err.Error()
			logBuilder.WriteString(result.Message + result.Error)
			log.Info(logBuilder.String()) // 记录日志
			return result, nil
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			result.Error = retailSellStatusResult.Msg
		} else {
			result.Error = resultJson
		}
	}
	result.Code = retailSellStatusResult.Code
	result.Message = retailSellStatusResult.Message
	return result, nil
}

// category/attr/value/list 查询特殊属性的属性值列表
func (gy *MtProductService) CategoryAttrValueList(ctx context.Context, model *proto.CategoryAttrValueListRequest) (*proto.CategoryAttrValueListResult, error) {
	model.PageSize = 200
	model.PageNum = 1
	result := &proto.CategoryAttrValueListResult{}
	jsonByte, err := json.Marshal(model)
	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【查询特殊属性的属性值列表】", jsonByte, err, utils.MtCategoryAttrValueListApi, 1)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, &result)
		if err != nil {
			result.Code = 400
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultJson
			result.Error = "值转Json异常,错误信息：" + err.Error()
			logBuilder.WriteString(result.Message + result.Error)
			log.Info(logBuilder.String()) // 记录日志
			return result, nil
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			result.Error = retailSellStatusResult.Msg
		} else {
			result.Error = resultJson
		}
	}
	result.Code = retailSellStatusResult.Code
	result.Message = retailSellStatusResult.Message
	return result, nil
}

/// retail/sku/delete 删除SKU信息 50次/秒
/**
https://open-shangou.meituan.com/home/<USER>/274
接口限流：按app维度，当前接口最高调用50次/秒。
接口说明：
1.用于删除商品的sku信息。
2.当商品目前只有一个sku时，删除此sku即会彻底此商品；若此商品所属分类下仅此一个商品，则可通过is_delete_retail_cat字段选择是否删除此商品所在分类。
3.当分类下无可售商品(包括商品库存为0或商品下架）时，分类在用户端不展示。
*/
func (gy *MtProductService) RetailSkuDelete(ctx context.Context, model *proto.RetailSkuDeleteRequest) (*proto.RetailSellStatusResult, error) {

	var vo = dto2.MtDeleteDto{
		AppPoiCode:        model.AppPoiCode,
		AppSpuCode:        model.AppFoodCode,
		SkuId:             model.SkuId,
		IsDeleteRetailCat: cast.ToInt(model.IsDeleteRetailCat),
	}
	jsonByte, err := json.Marshal(vo) // 参数对像转Json
	storeMasterId, _ := GetAppChannelByStoreId(model.AppPoiCode)

	//var res = &proto.RetailSellStatusResult{}
	//限流
	//rate := LimiterImportantRate(limiterMtDeleteProductRate, MtSkuDelete)
	//if !rate {
	//	log.Error(" 删除美团商品限流： ", rate)
	//	res.Code = 400
	//	return res, errors.New("删除美团商品限流")
	//}
	reserve := common.GetLimiter("retail.sku.delete", 50).Reserve()
	time.Sleep(reserve.Delay())

	return PostHandleFunc("【删除SKU信息】", jsonByte, err, utils.MtRetailSkuDelete, storeMasterId)
}

// RetailSkuBatchDelete 批量删除
// 接口限流：按app维度，当前接口最高调用5次/秒。
// 接口说明：
// https://opendj.meituan.com/home/<USER>/286
// 1.用于批量删除门店内分类及分类下商品，删除规则请详见各参数描述。
// 2.当仅传app_poi_code信息时，会删除此门店下所有的分类和商品。这种以门店维度清空全部分类和商品的方式属高风险性操作，故平台启用接口权限校验工具。当开发者应用app是首次或者授权期外调用此接口进行全部删除时，会触发接口权限校验逻辑，需开发者在官网开发者中心->常用工具->接口授权 的页面中进行授权操作。开发者完成授权后，系统会继续执行当前接口请求；如拒绝授权则会终止当前接口请求。授权方法请见：接口授权说明。
// 3.该接口为异步处理方式，请开发者在调用后检查分类、商品是否已删除；若未删除请继续等待，直到分类、商品成功删除之后，再进行重新创建操作。
// 4.同一次调用中所传的category_codes、category_names、secondary_category_codes 、secondary_category_names、app_spu_codes字段信息之间不允许存在关联关系。
func (gy *MtProductService) RetailSkuBatchDelete(AppPoiCode, AppFoodCode string, storeMasterId int32) (*proto.RetailSellStatusResult, error) {
	var vo = dto2.MtBatchDeleteDto{
		AppPoiCode:  AppPoiCode,
		AppSpuCodes: AppFoodCode,
	}
	jsonByte, err := json.Marshal(vo) // 参数对像转Json

	//var res = &proto.RetailSellStatusResult{}
	//限流
	//rate := LimiterImportantRate(limiterMtBatchDeleteProductRate, MtSkuBatchDelete)
	//if !rate {
	//	log.Error(" 删除美团商品限流： ", rate)
	//	res.Code = 400
	//	return res, errors.New("删除美团商品限流")
	//}
	reserve := common.GetLimiter("retailCat.batchdelete.catandretail", 50).Reserve()
	time.Sleep(reserve.Delay())

	return PostHandleFunc("【删除SKU信息】", jsonByte, err, utils.MtRetailSkuBatchDelete, storeMasterId)
}

// / retail/sku/stock 批量更新SKU库存20次/秒,可传商品数据（app_food_code维度）限定不超过200组
func (gy *MtProductService) RetailRetailSkuStock(model *dto2.RetailSkuStockRequest) *dto2.RetailSellStatusResult {
	var result dto2.RetailSellStatusResult
	jsonByte, err := json.Marshal(model) // 参数对像转Json
	storeMasterId := model.StoreMasterId //GetAppChannelByStoreId(model.App_poi_code)
	retailResult, err := PostHandleFunc("【批量更新SKU库存】", jsonByte, err, utils.MtRetailSkuStock, storeMasterId)
	result.Code = retailResult.Code
	result.Msg = retailResult.Msg
	//if len(retailResult.Error.Msg)>0 {
	//	result.Error.Msg = retailResult.Error.Msg
	//	result.Error.Code = retailResult.Error.Code
	//}
	result.Message = retailResult.Message
	return &result
}

// / retail/sku/stock 批量更新SKU库存20次/秒,可传商品数据（app_food_code维度）限定不超过200组
func (gy *MtProductService) RetailSkuStock(ctx context.Context, model *proto.RetailSkuStockRequest) (*proto.RetailSellStatusResult, error) {

	// 批量更新库存限流
	//reserve := mtRatelimitedRetailSkuStock.Reserve()
	r := common.GetLimiter("mt.limit.retail.sku.stock", 0).Reserve()
	time.Sleep(r.Delay())

	var result proto.RetailSellStatusResult
	req := dto2.RetailSkuStockRequest{
		App_poi_code:  model.AppPoiCode,
		Food_data:     []dto2.FoodData{},
		StoreMasterId: model.StoreMasterId,
	}
	for _, v := range model.FoodData {
		foodData := dto2.FoodData{
			App_food_code: v.AppFoodCode,
			Skus:          []dto2.Skus{},
		}
		for _, item := range v.Skus {
			foodData.Skus = append(foodData.Skus, dto2.Skus{
				Sku_id: item.SkuId,
				Stock:  item.Stock,
			})
		}

		req.Food_data = append(req.Food_data, foodData)
	}
	jsonByte, err := json.Marshal(req)   // 参数对像转Json
	storeMasterId := model.StoreMasterId //GetAppChannelByStoreId(model.App_poi_code)
	retailResult, err := PostHandleFunc("【批量更新SKU库存】", jsonByte, err, utils.MtRetailSkuStock, storeMasterId)
	if err != nil {
		log.Errorf("批量更新SKU库存异常:%+v,返回数据:%s", err, utils.JsonEncode(retailResult))
		result.Code = http.StatusBadRequest
		result.Message = fmt.Sprintf("批量更新SKU库存异常:%+v", err)
		return &result, nil
	}
	result.Code = retailResult.Code
	result.Msg = retailResult.Msg
	result.Message = retailResult.Message
	return &result, nil
}

// / task/status 查询多店同步任务的进程
func (gy *MtProductService) TaskStatus(ctx context.Context, model *proto.TaskStatusRequest) (*proto.TaskStatusResult, error) {
	result := &proto.TaskStatusResult{}
	jsonByte, err := json.Marshal(model)
	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【查询多店同步任务的进程】", jsonByte, err, utils.MtTaskStatus, model.AppChannel)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, &result)
		if result.Data == "ng" && err != nil {
			result.Code = 400
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultJson
			result.Error = "值转Json异常,错误信息：" + err.Error()
			logBuilder.WriteString(result.Message + result.Error)
			log.Info(logBuilder.String()) // 记录日志
			return result, nil
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			result.Error = retailSellStatusResult.Msg
		} else {
			result.Error = resultJson
		}
	}
	result.Data = resultJson
	if result.Code == 0 {
		result.Code = retailSellStatusResult.Code
	}
	result.Message = retailSellStatusResult.Message
	return result, nil
}

// v6.4.0
// 接口最高调用10次/秒。
// 删除商品分类
// 接口限流：按app维度，当前接口最高调用5次/秒。
// 接口说明：
// 1.用于删除商品分类。
// 2.当分类下存在子级分类或商品时，不允许直接删除此分类。
func (gy *MtProductService) MtRetailCatDelete(params *proto.MtRetailCatDeleteRequest) (*proto.ExternalResponse, error) {
	//r := mtRateLimitedRetailcatDelete.Reserve()
	r := common.GetLimiter("mt-rate-limited-retailcat-delete", 0).Reserve()
	time.Sleep(r.Delay())

	jsonStr, _ := json.Marshal(params)
	storeMasterId := params.StoreMasterId //GetAppChannelByStoreId(params.AppPoiCode)
	res, _ := Mt(jsonStr, utils.MtRetailCatDeleteApi, true, "POST", storeMasterId)
	return res, nil
}

// 处理Skus更新
func updateSkusPrice(skus []*proto.SkuParam) []map[string]interface{} {
	var skuMap = make([]map[string]interface{}, 0)
	for _, s := range skus {
		sku := make(map[string]interface{})
		if len(s.SkuId) == 0 {
			continue
		}
		sku["sku_id"] = s.SkuId
		sku["spec"] = s.Spec
		sku["min_order_count"] = s.MinOrderCount
		if len(s.Upc) > 0 {
			sku["upc"] = s.Upc
		}
		if len(s.WeightForUnit) > 0 {
			sku["weight_for_unit"] = s.WeightForUnit
			sku["weight_unit"] = s.WeightUnit
		}
		if len(s.Price) > 0 {
			sku["price"] = s.Price
		}
		if s.Stock != "" {
			sku["stock"] = s.Stock
		}
		skuMap = append(skuMap, sku)
	}
	return skuMap
}

// 美团批量更新SKU价格
func (gy *MtProductService) MtRetailSkuPrice(ctx context.Context, in *proto.MtRetailSkuPriceRequest) (*proto.ExternalResponse, error) {
	var result = new(proto.ExternalResponse)
	params_map := make(map[string]interface{}, 5)
	params_map["app_poi_code"] = in.AppPoiCode
	params_map["food_data"] = in.FoodData
	jsonByte, err := json.Marshal(params_map) // 参数对像转Json
	storeMasterId := in.StoreMasterId         //GetAppChannelByStoreId(in.AppPoiCode)
	retailResult, err := PostHandleFunc("【批量更新SKU价格】", jsonByte, err, utils.MtUpdateSkuPrice, storeMasterId)
	if err != nil {
		log.Error("美团价格同步失败:", err)
		result.Code = 400
		result.Message = "美团价格同步失败:" + err.Error()
		return result, nil
	}
	log.Info("美团价格同步返回结果:", retailResult)
	result.Code = retailResult.Code
	result.Message = retailResult.Msg + retailResult.Message
	return result, nil
}

// / retail/get 按app维度，当前接口最高调用50次/秒。
//
//	情接口说明：
//
// 1.用于查询商品信息详情。
// 2.每次调用仅支持查询1个商品数据。
func (gy *MtProductService) RetailGet(ctx context.Context, request *proto.RetailGetRequest) (*proto.RetailGetResult, error) {
	result := &proto.RetailGetResult{}
	jsonByte, err := json.Marshal(request) // 参数对像转Json
	storeMasterId := request.StoreMasterId //GetAppChannelByStoreId(request.AppPoiCode)
	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【查询商品信息详情】", jsonByte, err, utils.MtRetailGet, storeMasterId)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, &result)
		if err != nil {
			result.Code = 400
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultJson
			result.Error = "值转Json异常,错误信息：" + err.Error()
			logBuilder.WriteString(result.Message + result.Error)
			log.Info(logBuilder.String()) // 记录日志
			return result, nil
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			result.Error = retailSellStatusResult.Msg
		} else {
			result.Error = resultJson
		}
	}
	result.Code = retailSellStatusResult.Code
	result.Message = retailSellStatusResult.Message
	return result, nil
}

// 接口限流：按app维度，当前接口最高调用50次/秒。
// 接口说明：
// 1.用于查询商品信息详情。
// 2.每次调用最多支持查询100个商品数据。
func (gy *MtProductService) RetailBatchGet(ctx context.Context, request *proto.RetailBatchGetRequest) (*proto.RetailBatchGetResult, error) {
	result := &proto.RetailBatchGetResult{}
	log.Info("批量查询MT商品信息详情： ", JsonToString(request))

	//reserve := mtRatelimitedRetailBatchGet.Reserve()
	r := common.GetLimiter("mt.limit.batch.retail.get", 0).Reserve()
	time.Sleep(r.Delay())
	// 通过财务编码获取第三方的id
	redisConn := cache.GetRedisConn()
	App_poi_code := redisConn.HGet("store:relation:dctomt", request.FinanceCode).Val()

	if len(App_poi_code) < 0 {
		result.Code = 400
		result.Error = "App_poi_code为空，store:relation:dctomt没有查询到门店绑定关系"
		return result, nil
	}

	var storeMasterId int32
	if request.StoreMasterId > 0 {
		storeMasterId = request.StoreMasterId
	} else {
		storeMasterId, _ = GetAppChannelByStoreId(App_poi_code)
	}

	batchGetDto := dto2.BatchGetDto{
		AppPoiCode:  App_poi_code,
		AppSpuCodes: request.AppFoodCode,
	}
	jsonByte, err := json.Marshal(batchGetDto) // 参数对像转Json
	log.Info("批量查询MT参数：", string(jsonByte))
	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【批量查询商品信息详情】", jsonByte, err, utils.MtRetailBatchGet, storeMasterId)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, &result)
		if err != nil {
			result.Code = 400
			result.Message = "接口返回信息Code=400;返回的Json数据:" + resultJson
			result.Error = "值转Json异常,错误信息：" + err.Error()
			logBuilder.WriteString(result.Message + result.Error)
			log.Info(logBuilder.String()) // 记录日志
			return result, nil
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			result.Error = retailSellStatusResult.Msg
		} else {
			result.Error = resultJson
		}
	}
	result.Code = retailSellStatusResult.Code
	result.Message = retailSellStatusResult.Message
	return result, nil
}

// 查询门店商品列表 接口限流：按app维度，当前接口最高调用20次/秒
// 接口说明：
// 1.此接口分页查的逻辑，是根据公式计算得到需查询的页码，再根据每页商品数量最终得到结果。(1)【offset】字段信息根据公式计算得到需查询的页码：页码(舍弃小数)=offset/limit+1；(2)【limit】字段信息表示每页商品数量。
// 例如：offset=23，limit=5，根据公式计算结果表示从第5页开始查询，且每页5个商品，即本次请求结果将展示门店内第21～第25条商品数据。
// 2.如不传分页字段，一次查询最多返回30200条商品数据；建议分多次查询。
// 2.如果商品是在二级分类下面，则category_name为其一级分类，secondary_category_name为其所属分类（二级分类）。
// 3.如果商品是在一级分类下面，则category_name为其所属分类，secondary_category_name字段为空。
// 4.返回的商品信息默认是按sequence值升序排序。
func (gy *MtProductService) RetailList(ctx context.Context, req *proto.RetailListRequest) (*proto.RetailListResponse, error) {
	//r := mtRateLimitedretailList.Reserve()
	r := common.GetLimiter("mt-rate-limited-retail-list", 0).Reserve()
	time.Sleep(r.Delay())
	log.Info("查询门店商品列表请求参数： ", JsonToString(req))
	var err error
	// 通过财务编码获取第三方的id
	appPoiCode := req.AppPoiCode //有的请求发起出就可以直接传递该参数 不需要查询
	storeMasterId := req.StoreMasterId
	if appPoiCode == "" {
		redisConn := cache.GetRedisConn()
		appPoiCode = redisConn.HGet("store:relation:dctomt", req.FinanceCode).Val()
	}
	if appPoiCode == "" {
		return nil, fmt.Errorf("从store:relation:dctomt中查询不到门店绑定关系:%s", req.FinanceCode)
	}
	if storeMasterId == 0 {
		storeMasterId, _ = GetAppChannelByStoreId(appPoiCode)
	}
	params := dto2.RetailListRequest{
		AppPoiCode: appPoiCode,
	}
	params.Offset = (req.PageIndex - 1) * req.PageSize
	params.Limit = req.PageSize
	jsonByte, _ := json.Marshal(params) // 参数对像转Json

	log.Infof("查询门店商品列表mt参数：%s,storeMasterId：%d", string(jsonByte), storeMasterId)
	retailSellStatusResult, resultbyte, requestLogString := GetHandleFunc("【查询门店商品列表】", jsonByte, err, utils.MtRetailList, storeMasterId)
	resultJson := string(resultbyte)
	if retailSellStatusResult.Code == 200 {
		resp := &proto.RetailListResponse{}
		var logBuilder strings.Builder
		logBuilder.WriteString(requestLogString)
		err = json.Unmarshal(resultbyte, resp)
		if err != nil {
			logBuilder.WriteString(fmt.Sprintf("接口返回信息Code=400;返回的Json数据:%s,值转Json异常,错误信息：%+v", resultJson, err))
			log.Info(logBuilder.String()) // 记录日志
			return nil, fmt.Errorf("值转Json异常,错误信息：%+v", err)
		}
		logBuilder.WriteString("接口返回信息Code=200;Message=成功！返回的Json=" + resultJson)
		log.Info(logBuilder.String()) // 记录日志
		return resp, nil
	} else {
		if len(retailSellStatusResult.Msg) > 0 {
			log.Errorf("查询门店商品列表异常:%s,%s", retailSellStatusResult.Msg, requestLogString)
			return nil, errors.New(retailSellStatusResult.Msg)
		}
		log.Errorf("查询门店商品列表异常:%s,%s", resultJson, requestLogString)
		return nil, errors.New(resultJson)
	}
}

// 指定分类字段更新商品信息 只更新商品的分类
func (gy *MtProductService) RetailInitDataCategory(ctx context.Context, model *proto.RetailInitDataCategoryRequest) (*proto.RetailSellStatusResult, error) {
	var jsonByte []byte
	var err error
	//r := mtRateLimitedRetailInitdata.Reserve()
	r := common.GetLimiter("mt-rate-limited-retail-initdata", 0).Reserve()
	time.Sleep(r.Delay())
	jsonByte, err = json.Marshal(model) // 参数对像转Json
	storeMasterId := model.StoreMasterId
	res, err := PostHandleFunc("【创建/更新商品[支持商品多规格,不含删除逻辑] 只更新分类】", jsonByte, err, "retail/initdata", storeMasterId)
	return res, err
}

/// retail/delete 删除单个商品 50次/秒
/**
https://tscc.meituan.com/home/<USER>/market/272
接口限流：按app维度，当前接口最高调用50次/秒。
接口说明：
1.根据APP方门店id和商品id，删除商品。
2.如调用此接口删除商品，将会彻底删除此商品，即门店内所有分类下均无此商品。
3.如被删除商品所在的直属分类下没有其他商品，即此商品是分类下唯一商品，则可通过is_delete_retail_cat字段选择是否删除此商品所在的商品分类。
*/
func (gy *MtProductService) RetailDelete(model product_po.RetailDeleteBySpuReq) (*proto.RetailSellStatusResult, error) {

	var vo = dto2.MtDeleteDto{
		AppPoiCode:        model.AppPoiCode,
		AppSpuCode:        model.AppFoodCode,
		IsDeleteRetailCat: cast.ToInt(model.IsDeleteRetailCat),
	}
	jsonByte, err := json.Marshal(vo) // 参数对像转Json
	storeMasterId, _ := GetAppChannelByStoreId(model.AppPoiCode)

	var res = &proto.RetailSellStatusResult{}
	//限流
	rate := utils.LimitRate("retail/delete", 50, 1*time.Second, 1)
	if !rate {
		glog.Error(" 删除美团商品[删除单个商品]限流： ", rate)
		res.Code = 400
		return res, errors.New("删除美团商品[删除单个商品]限流")
	}

	return PostHandleFunc("【删除单个商品】", jsonByte, err, "retail/delete", storeMasterId)
}

// 关闭美团门店
func (gy *MtProductService) CloseShop(model product_po.RetailDeleteBySpuReq) (*proto.RetailSellStatusResult, error) {

	detailRequest := map[string]string{
		"app_poi_code": model.AppPoiCode,
	}
	var res = &proto.RetailSellStatusResult{}
	storeMasterId, _ := GetAppChannelByStoreId(model.AppPoiCode)
	jsonStr, _ := json.Marshal(detailRequest)
	result, err := HttpPostToMeiTuan(string(jsonStr), "poi/close", true, storeMasterId, utils.ContentTypeToForm)
	glog.Info("SyncMtStoreStatus请求美团接口，", err, model, string(jsonStr), string(result)) // 记录请求日志
	var resMode = dto2.MtStoreSaveResponse{}
	err = json.Unmarshal(result, &resMode)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
	}
	if resMode.ResultCode != 1 {
		res.Code = 400
		for _, x := range resMode.Error {
			res.Msg += x.Msg + "| "
		}

		return res, nil
	}
	res.Code = 200
	return res, nil
}
