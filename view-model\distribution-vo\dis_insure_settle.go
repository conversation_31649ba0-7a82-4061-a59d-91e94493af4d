package distribution_vo

import (
	"eShop/view-model"
)

type InsureSettlePageReq struct {
	//主体id
	OrgId int `json:"org_id"`
	//结算状态：1-待结算，2-已结算
	State int `json:"state"`
	//查询条件
	Where string `json:"where"`
	//查询条件的类型（id=结算编号，order_no=订单号，policy_no=保单号，product_name=保险名称，dis_name=分销员名称，dis_mobile=分销员手机号，dis_id=分销员Id，enterprise_name=线下企业）
	WhereType string `json:"where_type"`
	//时间范围，开始时间
	TimeBegin string `json:"time_begin"`
	//时间范围，结束时间
	TimeEnd string `json:"time_end"`
	//时间范围查询条件的类型（settle_time=结算时间，create_time=下单时间，insure_begin_time=保险起期，insure_end_time=保险止期）
	TimeWhereType string `json:"time_where_type"`

	viewmodel.BasePageHttpRequest
}

type InsureSettlePageResp struct {
	viewmodel.BasePageHttpResponse
	Data []InsureSettlePageData `json:"data"`
}

type InsureSettlePageData struct {
	//结算编号
	Id string `json:"id"`
	//结算状态：1-待结算，2-已结算
	State int `json:"state"`
	//结算时间
	SettleTime string `json:"settle_time"`
	//订单号
	OrderNo string `json:"order_no"`
	//保单号
	PolicyNo string `json:"policy_no"`
	//下单时间
	CreateTime string `json:"create_time"`
	//保险起期
	InsureBeginDate string `json:"insure_begin_date"`
	//保险止期
	InsureEndDate string `json:"insure_end_date"`
	//保险订单状态：1-待支付，3-投保成功，4-投保中，5-退款处理中，6-已退款，7-已取消，11-保障中，12-已过期，19-投保失败，20-已退保
	OrderStatus int `json:"order_status"`
	//支付金额（分）
	PayPrice float64 `json:"pay_price"`
	//退款金额（分）
	RefundPrice float64 `json:"refund_price"`
	//保险名称
	ProductName string `json:"product_name"`
	//佣金比例
	DisRate float64 `json:"dis_rate"`
	//分销佣金（分）
	DisAmount int `json:"dis_amount"`
	//分销员
	DisName string `json:"dis_name"`
	//分销员1d
	DisId int `json:"dis_id"`
	//线下企业
	EnterpriseName string `json:"enterprise_name"`
	//线下企业
	Code string `json:"code"`
}

type InsureSettlePageApiReq struct {
	//主体id
	OrgId int `json:"org_id"`
	//店铺id
	ShopId int `json:"shop_id"`
	//分销员id
	DisId int `json:"dis_id"`
	//订单状态：0-全部，1-待完成，2-已取消，3-已完成
	State int `json:"state"`
	//结算状态：0-全部，1-待结算，2-已结算
	SettleState int `json:"settle_state"`
	//条件查询，包括用户手机号、订单号、保险名称
	Where string `json:"where"`
	//开始时间
	AddTimeStart string `json:"add_time_start"`
	//结束时间
	AddTimeEnd string `json:"add_time_end"`

	viewmodel.BasePageHttpRequest
}

type InsureSettlePageApiResp struct {
	viewmodel.BasePageHttpResponse
	//合计订单金额（分）
	TotalSale int                       `json:"total_sale"`
	Data      []InsureSettlePageApiData `json:"data"`
}

type InsureSettlePageApiData struct {
	//结算编号
	Id int64 `json:"id"`
	//订单号
	OrderNo string `json:"order_no"`
	//保险订单状态：1-待支付，3-投保成功，4-投保中，5-退款处理中，6-已退款，7-已取消，11-保障中，12-已过期，19-投保失败，20-已退保
	OrderStatus int `json:"order_status"`
	//产品主图
	ProductImageUrl string `json:"product_image_url"`
	//产品名称
	ProductName string `json:"product_name"`
	//价格（元】
	ProductPrice float64 `json:"product_price"`
	//佣金比例
	DisRate float64 `json:"dis_rate"`
	//分销佣金（元）
	DisAmount float64 `json:"dis_amount"`
	//下单时间
	CreateTime string `json:"create_time"`
	//保险起期
	InsureBeginDate string `json:"insure_begin_date"`
	//保险止期
	InsureEndDate string `json:"insure_end_date"`
	//结算状态：1-待结算，2-已结算
	SettleState int `json:"settle_state"`
	//分销员id
	DisId int `json:"dis_id"`
	//分销员名称
	DisName string `json:"dis_name"`
}
