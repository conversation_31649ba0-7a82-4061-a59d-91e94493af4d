package petai_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

// 小闻养宠助手会话对应coze会话，一对多的关系
type PetaiConversationCoze struct {
	Id                       int       `json:"id"  xorm:"pk autoincr 'id' comment('主键')"` //自增id
	ConversationId           int       `json:"conversation_id"`                           //会话id即petai_conversation.id
	BotId                    string    `json:"botId"`                                     // coze智能体id
	BotType                  int       `json:"bot_type" xorm:"not null TINYINT(4)"`       // 智能体类型：1-养宠助手火山 2-小闻模型 3-宠物自诊 4-宠物识别；5-健康建议  6-互联网医院
	CozeConversationId       string    `json:"coze_conversation_id"`                      //coze中的会话id
	CozeConversationCreateAt int       `json:"coze_conversation_create_at"`               //coze会话创建时间
	CreateTime               time.Time `json:"create_time" xorm:"created"`                //创建时间
	UpdateTime               time.Time `json:"update_time" xorm:"updated"`                //更新时间

}

func (m *PetaiConversationCoze) TableName() string {
	return "eshop.petai_conversation_coze"
}

func (m *PetaiConversationCoze) GetMap(session *xorm.Session, conversationId int) (out map[int]PetaiConversationCoze, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}

	if conversationId == 0 {
		err = errors.New("conversationId is 0")
		return
	}
	data := make([]PetaiConversationCoze, 0)
	err = session.Table("eshop.petai_conversation_coze").Where("conversation_id = ?", conversationId).Find(&data)
	if err != nil {
		return
	}

	out = make(map[int]PetaiConversationCoze)
	for _, v := range data {
		out[v.BotType] = v
	}
	return
}
