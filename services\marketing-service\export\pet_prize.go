package export

import (
	"eShop/infra/utils"
	"eShop/services/marketing-service/services"
	vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type PetPrizeTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.PetPrizePageReq
	writer       *excelize.StreamWriter
}

func prizeTypeText(t int) string {
	switch t {
	case 1:
		return "排名奖"
	case 2:
		return "投票奖"
	case 3:
		return "创作奖"
	default:
		return "-"
	}
}

func receiveStatusText(s int) string {
	switch s {
	case 1:
		return "待领取"
	case 2:
		return "已领取"
	case 3:
		return "已核销"
	default:
		return "-"
	}
}

func (e *PetPrizeTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.PetPrizePageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	e.ExportParams.PageSize = 10000

	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	e.SetSheetName()
	client := services.NewPetPrizeService()
	k := 0
	for {
		list, total, err := client.List(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex++
		for i := 0; i < len(list); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)
			_ = e.writer.SetRow(axis, []interface{}{
				list[i].CreateTime,                       // 奖励生效日期
				list[i].UserId,                           // 获奖人用户id
				list[i].NickName,                         // 获奖人昵称
				list[i].Mobile,                           // 获奖人手机号
				prizeTypeText(list[i].PrizeType),         // 获奖类型（转文字）
				list[i].WorkCode,                         // 获奖作品编号
				list[i].PrizeCount,                       // 获奖票数
				receiveStatusText(list[i].ReceiveStatus), // 领奖状态（转文字）
				list[i].ReceiveTime,                      // 领取时间
				list[i].VerifyTime,                       // 核销时间
				list[i].PrizeContent,                     // 奖励内容
				list[i].CouponCode,                       // 优惠券码
				list[i].OrderSn,                          // 核销订单号
				list[i].Address,                          // 收货地址
				list[i].Receiver,                         // 收货人
				list[i].ReceiverMobile,                   // 收货人电话
			})
		}
		if len(list) < int(e.ExportParams.PageSize) || int64(k) >= total {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

func (e *PetPrizeTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"奖励生效日期", "获奖人用户id", "获奖人昵称", "获奖人手机号", "获奖类型", "获奖作品编号", "获奖票数", "领奖状态", "领取时间", "核销时间", "奖励内容", "优惠券码", "核销订单号", "收货地址", "收货人", "收货人电话",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e *PetPrizeTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("奖品明细导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e *PetPrizeTask) OperationFunc(row []string, orgId int) string {
	return ""
}
