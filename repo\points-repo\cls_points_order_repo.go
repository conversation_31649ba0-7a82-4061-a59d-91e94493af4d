package points_repo

import (
	po "eShop/domain/points-po"
	"eShop/infra/utils"
	vo "eShop/view-model/points-vo"

	"xorm.io/xorm"
)

type ClsPointsOrderRepo struct {
	SuperRepo[po.ClsPointsOrder]
}

func NewClsPointsOrderRepo() ClsPointsOrderRepo {
	return ClsPointsOrderRepo{
		NewSuperRepo[po.ClsPointsOrder](),
	}
}

func (r ClsPointsOrderRepo) Page(session *xorm.Session, queryVO vo.ClsPointsOrderQueryVO) ([]vo.ClsPointsOrderResultVO, int64, error) {
	var results []vo.ClsPointsOrderResultVO
	query := session.Table("cls_points_order")

	// 选择需要的字段
	query.Select(`
		cls_points_order.id,
		cls_points_order.order_no,
		cls_points_order.dis_id,
		cls_points_order.goods_id,
		cls_points_order.goods_name,
		cls_points_order.goods_type,
		cls_points_order.points_cost,
		cls_points_order.quantity,
		cls_points_order.status,
		cls_points_order.address_id,
		cls_points_order.remark,
		cls_points_order.express_company,
		cls_points_order.express_no,
		cls_points_order.express_time,
		cls_points_order.payment_method,
		cls_points_order.order_time,
		cls_points_order.order_source,
		cls_points_order.order_type,
		cls_points_order.complete_time,
		cls_points_goods.market_price,
		cls_points_goods.points_price,
		cls_points_goods.image_url,
		cls_dis_address.name as addr_name,
		cls_dis_address.phone as addr_phone,
		cls_dis_address.encrypt_phone as addr_encrypt_phone,
		cls_dis_address.province,
		cls_dis_address.city,
		cls_dis_address.district,
		cls_dis_address.ress as ress,
		dis_distributor.member_id,
		dis_distributor.real_name AS member_name,
		dis_distributor.mobile AS member_mobile,
		dis_distributor.encrypt_mobile AS member_encrypt_mobile
	`)

	// 左连接商品表和地址表
	query.Join("LEFT", "dis_distributor", "dis_distributor.id=cls_points_order.dis_id")
	query.Join("LEFT", "upetmart.upet_member", "upet_member.member_id=dis_distributor.member_id")
	query.Join("LEFT", "cls_points_goods", "cls_points_goods.id=cls_points_order.goods_id")
	query.Join("LEFT", "cls_dis_address", "cls_dis_address.id=cls_points_order.address_id")

	// 添加查询条件
	conditions := utils.GetQueryCondition(queryVO)
	query.Where(conditions).OrderBy(queryVO.GetOrderBy())

	// 分页
	// 计算偏移量
	offset := (queryVO.GetCurrent() - 1) * queryVO.GetSize()
	query.Limit(queryVO.GetSize(), offset)

	// 执行查询
	total, err := query.FindAndCount(&results)
	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

func (r ClsPointsOrderRepo) Detail(session *xorm.Session, id int) (vo.ClsPointsOrderResultVO, error) {
	var results vo.ClsPointsOrderResultVO
	query := session.Table("cls_points_order")

	// 选择需要的字段
	query.Select(`
		cls_points_order.id,
		cls_points_order.order_no,
		cls_points_order.dis_id,
		cls_points_order.goods_id,
		cls_points_order.goods_name,
		cls_points_order.goods_type,
		cls_points_order.points_cost,
		cls_points_order.quantity,
		cls_points_order.status,
		cls_points_order.address_id,
		cls_points_order.remark,
		cls_points_order.express_company,
		cls_points_order.express_no,
		cls_points_order.express_time,
		cls_points_order.payment_method,
		cls_points_order.order_time,
		cls_points_order.order_source,
		cls_points_order.order_type,
		cls_points_goods.market_price,
		cls_points_goods.points_price,
		cls_points_goods.image_url,
		cls_dis_address.name as addr_name,
		cls_dis_address.phone as addr_phone,
		cls_dis_address.encrypt_phone as addr_encrypt_phone,
		cls_dis_address.province,
		cls_dis_address.city,
		cls_dis_address.district,
		cls_dis_address.ress as ress,
		dis_distributor.member_id,
		dis_distributor.real_name AS member_name,
		dis_distributor.mobile AS member_mobile,
		dis_distributor.encrypt_mobile AS member_encrypt_mobile
	`)

	// 左连接商品表和地址表
	query.Join("LEFT", "dis_distributor", "dis_distributor.id=cls_points_order.dis_id")
	query.Join("LEFT", "upetmart.upet_member", "upet_member.member_id=dis_distributor.member_id")
	query.Join("LEFT", "cls_points_goods", "cls_points_goods.id=cls_points_order.goods_id")
	query.Join("LEFT", "cls_dis_address", "cls_dis_address.id=cls_points_order.address_id")

	// 添加查询条件
	query.Where("cls_points_order.id=?", id)

	// 执行查询
	_, err := query.Get(&results)
	if err != nil {
		return vo.ClsPointsOrderResultVO{}, err
	}

	return results, nil
}

func (r ClsPointsOrderRepo) FinishOrder(session *xorm.Session, startDate, endDate string) error {
	sql := `
	UPDATE cls_points_order
	SET status = 3, complete_time = NOW()
	WHERE status = 2 AND express_time >= ? AND express_time <= ?
	`
	_, err := session.Exec(sql, startDate, endDate)
	return err
}

func (r ClsPointsOrderRepo) CountById(session *xorm.Session, id int) (int, error) {
	var quantity int
	_, err := session.Select("SUM(quantity)").Table("cls_points_order").Where("goods_id=?", id).Get(&quantity)
	return quantity, err
}
