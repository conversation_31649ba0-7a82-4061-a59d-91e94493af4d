package services

import (
	"eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

/*
select

	sum(a.commission) as commission,
		a.distributor_id as dis_id,
		a.shop_id,
		b.shop_name,
		c.enterprise_name,
		c.code as enterprise_code

from

	`eshop`.`dis_settlement` as `a`

left join `eshop`.`shop` `b` on

	a.shop_id = b.id

left join `eshop`.`scrm_enterprise` `c` on

	b.enterprise_id = c.id

where

	(a.distributor_id>0)
	and (a.shop_id>0)
	and (a.org_id =3)
	and (a.create_time >="2024-05-10")
	and (a.create_time <="2024-05-10 23:59:59")

group by

	a.distributor_id ,
	a.shop_id;

select

	sum(a.commission) as commission,
		a.distributor_id as dis_id,
		a.shop_id,
		b.shop_name,
		c.enterprise_name,
		c.code as enterprise_code

from

	`eshop`.`dis_settlement` as `a`

left join `eshop`.`shop` `b` on

	a.shop_id = b.id

left join `eshop`.`scrm_enterprise` `c` on

	b.enterprise_id = c.id

where

	(a.distributor_id>0)
	and (a.shop_id>0)
	and (a.org_id =3)
	and (a.status =1)
	and (a.create_time >="2024-05-10")
	and (a.create_time <="2024-05-10 23:59:59")

group by

	a.distributor_id ,
	a.shop_id;

select

	sum(a.commission) as commission,
		a.distributor_id as dis_id,
		a.shop_id,
		b.shop_name,
		c.enterprise_name,
		c.code as enterprise_code

from

	`eshop`.`dis_settlement` as `a`

left join `eshop`.`shop` `b` on

	a.shop_id = b.id

left join `eshop`.`scrm_enterprise` `c` on

	b.enterprise_id = c.id

where

	(a.distributor_id>0)
	and (a.shop_id>0)
	and (a.org_id =3)
	and (a.status =2)
	and (a.settlement_time >="2024-05-10")
	and (a.settlement_time <="2024-05-10 23:59:59")

group by

	a.distributor_id ,
	a.shop_id;

select

	sum(a.dis_amount) as commission,
		a.dis_id,
		a.shop_id,
		b.shop_name,
		c.enterprise_name,
		c.code as enterprise_code

from

	`eshop`.`dis_insurance_settle` as `a`

left join `eshop`.`shop` `b` on

	a.shop_id = b.id

left join `eshop`.`scrm_enterprise` `c` on

	b.enterprise_id = c.id

where

	(a.dis_id>0)
	and (a.shop_id>0)
	and (a.org_id =3)
	and (a.create_time >="2024-05-13")
	and (a.create_time <="2024-05-13 23:59:59")

group by

	a.dis_id ,
	a.shop_id;

select

	sum(a.dis_amount) as commission,
		a.dis_id,
		a.shop_id,
		b.shop_name,
		c.enterprise_name,
		c.code as enterprise_code

from

	`eshop`.`dis_insurance_settle` as `a`

left join `eshop`.`shop` `b` on

	a.shop_id = b.id

left join `eshop`.`scrm_enterprise` `c` on

	b.enterprise_id = c.id

where

	(a.dis_id>0)
	and (a.shop_id>0)
	and (a.org_id =3)
	and (a.state =1)
	and (a.create_time >="2024-05-13")
	and (a.create_time <="2024-05-13 23:59:59")

group by

	a.dis_id ,
	a.shop_id;
	select
	sum(a.dis_amount) as commission,
		a.dis_id,
		a.shop_id,
		b.shop_name,
		c.enterprise_name,
		c.code as enterprise_code

from

	`eshop`.`dis_insurance_settle` as `a`

left join `eshop`.`shop` `b` on

	a.shop_id = b.id

left join `eshop`.`scrm_enterprise` `c` on

	b.enterprise_id = c.id

where

	(a.dis_id>0)
	and (a.shop_id>0)
	and (a.org_id =3)
	and (a.state =2)
	and (a.create_time >="2024-05-13")
	and (a.create_time <="2024-05-13 23:59:59")

group by

		a.dis_id ,
		a.shop_id;

	 select
		*

from

	`eshop`.`stats_shop_distributor_daily`

where

	(stat_date ="2024-05-13")
	and (shop_id =33)
	and (dis_id =19)
	and (type =2)
	and (is_dis = 1)

limit 1;
*/
func TestStatsShopService_StatsShopDistributorDaily(t *testing.T) {
	type args struct {
		req []distribution_vo.StatsShopDistributorDailyReq
	}
	tests := []struct {
		name string
		s    StatsShopService
		args args
	}{
		// TODO: Add test cases.
		{
			name: "店铺分销数据",
			s:    StatsShopService{},
			args: args{
				req: []distribution_vo.StatsShopDistributorDailyReq{
					distribution_vo.StatsShopDistributorDailyReq{StartDate: "2024-05-13", EndDate: ""},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.s.StatsShopDistributorDaily(tt.args.req...)
		})
	}
}

/*
select

	a.goods_id,
	a.goods_name,
	a.goods_image,
	a.store_id,
	a.is_dis,
	a.shop_id,
	a.dis_member_id,
	sum(a.goods_num) as trans_count,
	sum(a.goods_pay_price * a.goods_num) as trans_amount,
	sum(a.dis_commis_rate * a.goods_pay_price * a.goods_num) as commission

from

	`upetmart`.`upet_order_goods` as `a`

left join `upetmart`.`upet_orders` `b` on

	a.order_id = b.order_id

where

	(b.payment_time>0)
	and (a.is_dis =1)
	and (a.add_time >=1719849600)
	and (a.add_time <=1719849600)
	and (a.dis_member_id =103855)
	and (a.goods_name like "%aa%")

group by

	goods_id

order by

	trans_amount asc

limit 10;
---------------------------------------------------------
select

	a.goods_id,
	a.goods_name,
	a.goods_image,
	sum(a.goods_num) as trans_count,
	sum(a.goods_pay_price * a.goods_num) as trans_amount,
	sum(a.dis_commis_rate * a.goods_pay_price * a.goods_num) as commission

from

	`upetmart`.`upet_order_goods` as `a`

left join `upetmart`.`upet_orders` `b` on

	a.order_id = b.order_id

where

	(b.payment_time>0)
	and (a.shop_id =33)
	and (a.is_dis =1)
	and (a.goods_name like "%皇家%")

group by

	goods_id

limit 10
*/
func TestStatsShopService_StatsDisCenterGoods(t *testing.T) {

	type args struct {
		in distribution_vo.StatsDisCenterGoodsReq
	}
	tests := []struct {
		name      string
		s         StatsShopService
		args      args
		wantOut   []distribution_vo.StatsDisCenterGoods
		wantTotal int
		wantErr   bool
	}{
		{name: " 用户端-分销员中心-数据中心-商品分析"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.s.StatsDisCenterGoods(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsShopService.StatsDisCenterGoods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsShopService.StatsDisCenterGoods() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("StatsShopService.StatsDisCenterGoods() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

/*
	 SELECT sum(total_pay_sales) as total_pay_sales,
			sum(ins_total_pay_sales) as ins_total_pay_sales,
			sum(order_pay_num) as order_pay_num,
			sum(ins_order_pay_num) as ins_order_pay_num,
			sum(settled_commission) as settled_commission,
			sum(unsettled_commission) as unsettled_commission,
			sum(ins_settled_commission) as ins_settled_commission,
			sum(ins_unsettled_commission) as ins_unsettled_commission,
			(sum(total_pay_sales)+sum(ins_total_pay_sales)) as trans_amount,
			(sum(order_pay_num)+sum(ins_order_pay_num)) as trans_count,
			(sum(settled_commission)+sum(unsettled_commission)+sum(ins_settled_commission)+sum(ins_unsettled_commission)) as commission,
			(sum(settled_commission)+sum(ins_settled_commission)) as settled_commission,
			(sum(unsettled_commission)+sum(ins_unsettled_commission)) as unsettled_commission,
			sum(total_customer) as total_customer,
			sum(withdraw_success) as withdraw_success,
			sum(withdraw_apply) as withdraw_apply,
			sum(wait_withdraw) as wait_withdraw,
			count(distinct dis_id)  as distributor_cnt
			 FROM `eshop`.`dis_distributor_total` WHERE (dis_id=?) LIMIT 1

--	------------------------------------------------------
SELECT sum(order_customer_count) as order_customer_count,

	sum(order_count) as order_count,
	sum(order_amount) as order_amount,
	sum(order_product_count) as order_product_count,
	sum(order_customer_price) as order_customer_price,
	sum(trans_customer_count) as trans_customer_count,
	sum(trans_count) as  trans_count,
	sum(trans_amount) as trans_amount,
	sum(trans_product_count) as trans_product_count ,
	sum(trans_customer_price) as trans_customer_price,
	sum(cancel_count) as cancel_count,
	sum(cancel_amount) as cancel_amount,
	sum(cancel_product_count) as cancel_product_count ,
	sum(refund_count) as refund_count,
	sum(refund_amount) as refund_amount,
	sum(refund_product_count) as refund_product_count,
	sum(poster_scan_count) as poster_scan_count,
	sum(poster_trans_count) as poster_trans_count,
	sum(poster_trans_amount) as  poster_trans_amount,
	sum(link_click_count) as link_click_count,
	sum(link_trans_count) as link_trans_count,
	sum(link_trans_amount) as link_trans_amount,
	sum(fan_relation_trans_count) as fan_relation_trans_count,
	sum(fan_relation_trans_amount) as fan_relation_trans_amount,
	sum(commission) as  commission,
	sum(unsettled_commission) as unsettled_commission,
	sum(settled_commission) as settled_commission FROM `eshop`.`stats_shop_distributor_daily` WHERE (stat_date>=?) AND (stat_date<=?) AND `type` IN (?,?) AND (dis_id=?) LIMIT 1
*/
func TestStatsShopService_StatsDisCenterIndex(t *testing.T) {
	type args struct {
		in distribution_vo.StatsDisCenterIndexReq
	}
	tests := []struct {
		name    string
		s       StatsShopService
		args    args
		wantOut distribution_vo.StatsDisCenterIndex
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				in: distribution_vo.StatsDisCenterIndexReq{
					OrgId:    3,
					MemberId: 10000167,
					Flag:     0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.s.StatsDisCenterIndex(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsShopService.StatsDisCenterIndex() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsShopService.StatsDisCenterIndex() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

/**
SELECT sum(order_customer_count) as order_customer_count,
				sum(order_count) as order_count,
				sum(order_amount) as order_amount,
				sum(order_product_count) as order_product_count,
				sum(order_customer_price) as order_customer_price,
				sum(trans_customer_count) as trans_customer_count,
				sum(trans_count) as  trans_count,
				sum(trans_amount) as trans_amount,
				sum(trans_product_count) as trans_product_count ,
				sum(trans_customer_price) as trans_customer_price,
				sum(cancel_count) as cancel_count,
				sum(cancel_amount) as cancel_amount,
				sum(cancel_product_count) as cancel_product_count ,
				sum(refund_count) as refund_count,
				sum(refund_amount) as refund_amount,
				sum(refund_product_count) as refund_product_count,
				sum(poster_scan_count) as poster_scan_count,
				sum(poster_trans_count) as poster_trans_count,
				sum(poster_trans_amount) as  poster_trans_amount,
				sum(link_click_count) as link_click_count,
				sum(link_trans_count) as link_trans_count,
				sum(link_trans_amount) as link_trans_amount,
				sum(fan_relation_trans_count) as fan_relation_trans_count,
				sum(fan_relation_trans_amount) as fan_relation_trans_amount,
				sum(commission) as  commission,
				sum(unsettled_commission) as unsettled_commission,
				sum(settled_commission) as settled_commission FROM `eshop`.`stats_shop_distributor_daily` WHERE (stat_date>="2024-05-31") AND (stat_date<="2024-06-30") AND (shop_id=33) AND `type` IN (2,4) LIMIT 1 [  33 2 4] - 111.8482ms

*/

func TestStatsShopService_StatsDisCenterOverview(t *testing.T) {
	type args struct {
		in distribution_vo.StatsDisCenterOverviewReq
	}
	tests := []struct {
		name    string
		s       StatsShopService
		args    args
		wantOut distribution_vo.StatsDisCenterOverview
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.s.StatsDisCenterOverview(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsShopService.StatsDisCenterOverview() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsShopService.StatsDisCenterOverview() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

/*
*
select

	stat_date,
	sum(trans_count) as trans_count,
	sum(trans_amount) as trans_amount,
	sum(commission) as commission,
	sum(trans_customer_count) as trans_customer_count

from

	`eshop`.`stats_shop_distributor_daily`

where

	(stat_date >="2024-07-01")
	and (stat_date <="2024-07-31")
	and `type` in (2,4)
	and (dis_id =62)

group by

	stat_date;
*/
func TestStatsShopService_StatsDisCenterGraph(t *testing.T) {
	type args struct {
		in distribution_vo.StatsDisCenterGraphReq
	}
	tests := []struct {
		name    string
		s       StatsShopService
		args    args
		wantOut []distribution_vo.StatsDisGraphData
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.s.StatsDisCenterGraph(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsShopService.StatsDisCenterGraph() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsShopService.StatsDisCenterGraph() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

/*
SELECT id,dis_role,shop_id FROM `eshop`.`dis_distributor` WHERE (member_id=?) AND (org_id=?) LIMIT 1;
SELECT sum(total_pay_sales) as total_pay_sales,

	sum(ins_total_pay_sales) as ins_total_pay_sales,
	sum(order_pay_num) as order_pay_num,
	sum(ins_order_pay_num) as ins_order_pay_num,
	sum(settled_commission) as settled_commission,
	sum(unsettled_commission) as unsettled_commission,
	sum(ins_settled_commission) as ins_settled_commission,
	sum(ins_unsettled_commission) as ins_unsettled_commission,
	(sum(total_pay_sales)+sum(ins_total_pay_sales)) as trans_amount,
	(sum(order_pay_num)+sum(ins_order_pay_num)) as trans_count,
	(sum(settled_commission)+sum(unsettled_commission)+sum(ins_settled_commission)+sum(ins_unsettled_commission)) as commission,
	(sum(settled_commission)+sum(ins_settled_commission)) as settled_commission,
	(sum(unsettled_commission)+sum(ins_unsettled_commission)) as unsettled_commission,
	sum(total_customer) as total_customer,
	sum(withdraw_success) as withdraw_success,
	sum(withdraw_apply) as withdraw_apply,
	sum(wait_withdraw) as wait_withdraw,
	count(distinct dis_id)  as distributor_cnt
	 FROM `eshop`.`dis_distributor_total` WHERE (dis_id=62) LIMIT 1;
*/
func TestStatsShopService_StatsDisCenterTotal(t *testing.T) {
	type args struct {
		in distribution_vo.StatsDisCenterTotalReq
	}
	tests := []struct {
		name    string
		s       StatsShopService
		args    args
		wantOut distribution_vo.StatsDisCenterTotal
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.s.StatsDisCenterTotal(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsShopService.StatsDisCenterTotal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsShopService.StatsDisCenterTotal() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

/*
select

	b.name as dis_name,
			b.dis_role,
			b.status,
			b.total_customer,
			a.shop_id,
			a.dis_id,
			a.type,
			sum(a.trans_customer_count) as trans_customer_count,
			sum(a.trans_count) as trans_count,
			sum(a.trans_amount) as trans_amount,
			sum(a.commission) as commission

from

	`eshop`.`stats_shop_distributor_daily` as `a`

left join `eshop`.`dis_distributor` `b` on

	a.dis_id = b.id

where

	(a.shop_id =33)
	and `a`.`type` in (2,4)

group by

	dis_id

order by

	trans_amount desc

limit 10;
--------------------------------------------------------------------
select

	b.name as dis_name,
			b.dis_role,
			b.status,
			b.total_customer,
			a.shop_id,
			a.dis_id,
			a.type,
			sum(a.trans_customer_count) as trans_customer_count,
			sum(a.trans_count) as trans_count,
			sum(a.trans_amount) as trans_amount,
			sum(a.commission) as commission

from

	`eshop`.`stats_shop_distributor_daily` as `a`

left join `eshop`.`dis_distributor` `b` on

	a.dis_id = b.id

where

	(a.shop_id =33)
	and `a`.`type` in (2,4)
	and `a`.`dis_id` in (62,1,2)

group by

	dis_id,
	type;
*/
func TestStatsShopService_StatsDisCenterDistributor(t *testing.T) {
	type args struct {
		in distribution_vo.StatsDisCenterDistributorReq
	}
	tests := []struct {
		name      string
		s         StatsShopService
		args      args
		wantOut   []distribution_vo.DisCenterDistributor
		wantTotal int
		wantErr   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, gotTotal, err := tt.s.StatsDisCenterDistributor(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsShopService.StatsDisCenterDistributor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsShopService.StatsDisCenterDistributor() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("StatsShopService.StatsDisCenterDistributor() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

/*
select

	sum(order_customer_count) as order_customer_count,
				sum(order_count) as order_count,
				sum(order_amount) as order_amount,
				sum(order_product_count) as order_product_count,
				sum(order_customer_price) as order_customer_price,
				sum(trans_customer_count) as trans_customer_count,
				sum(trans_count) as trans_count,
				sum(trans_amount) as trans_amount,
				sum(trans_product_count) as trans_product_count ,
				sum(trans_customer_price) as trans_customer_price,
				sum(cancel_count) as cancel_count,
				sum(cancel_amount) as cancel_amount,
				sum(cancel_product_count) as cancel_product_count ,
				sum(refund_count) as refund_count,
				sum(refund_amount) as refund_amount,
				sum(refund_product_count) as refund_product_count,
				sum(poster_scan_count) as poster_scan_count,
				sum(poster_trans_count) as poster_trans_count,
				sum(poster_trans_amount) as poster_trans_amount,
				sum(link_click_count) as link_click_count,
				sum(link_trans_count) as link_trans_count,
				sum(link_trans_amount) as link_trans_amount,
				sum(fan_relation_trans_count) as fan_relation_trans_count,
				sum(fan_relation_trans_amount) as fan_relation_trans_amount,
				sum(commission) as commission,
				sum(unsettled_commission) as unsettled_commission,
				sum(settled_commission) as settled_commission

from

	`eshop`.`stats_shop_distributor_daily`

where

	(stat_date >="2024-06-11")
	and (stat_date <="2024-06-30")
	and `type` in (1,3)

limit 1;

SELECT sum(pre_tax_amount) as pre_tax_amount FROM `eshop`.`dis_withdraw` WHERE (org_id=3) AND (status=2) LIMIT 1;
SELECT sum(pre_tax_amount) as pre_tax_amount FROM `eshop`.`dis_withdraw` WHERE (org_id=3) AND (pay_time>="2024-06-11") AND (pay_time<="2024-06-30 23:59:59") AND (status=2) LIMIT 1;
SELECT sum(pre_tax_amount) as pre_tax_amount FROM `eshop`.`dis_withdraw` WHERE (org_id=3) AND (pay_time>="2024-07-01") AND (pay_time<="2024-07-20 23:59:59") AND (status=2) LIMIT 1;
*/
func TestStatsShopService_GetKanbanOverview(t *testing.T) {
	type args struct {
		in distribution_vo.GetKanbanOverviewReq
	}
	tests := []struct {
		name    string
		s       StatsShopService
		args    args
		wantOut distribution_vo.GetKanbanOverview
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotOut, err := tt.s.GetKanbanOverview(tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatsShopService.GetKanbanOverview() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("StatsShopService.GetKanbanOverview() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestStatsShopService_StatsShopDistributorWeeklyRun(t *testing.T) {
	s := StatsShopService{}
	s.StatsShopDistributorDailyRun("2024-04-01", "2024-07-14")
	//s.StatsShopDistributorWeeklyRun()
}

func TestStatsShopService_StatsShopDistributorDailyDefault(t *testing.T) {
	tests := []struct {
		name string
		s    StatsShopService
	}{
		// TODO: Add test cases.
		{
			name: "佣金",
			s:    StatsShopService{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.s.StatsShopDistributorDailyDefault()
		})
	}
}

func TestStatsShopService_StatsShopDistributorWeeklyDefault(t *testing.T) {
	tests := []struct {
		name string
		s    StatsShopService
	}{
		// TODO: Add test cases.
		{
			name: "aa",
			s:    StatsShopService{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.s.StatsShopDistributorWeeklyDefault()
		})
	}
}

func TestStatsShopService_StatsShopDistributorMonthlyDefault(t *testing.T) {
	tests := []struct {
		name string
		s    StatsShopService
	}{
		// TODO: Add test cases.
		{name: "aa",
			s: StatsShopService{}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.s.StatsShopDistributorMonthlyDefault()
		})
	}
}
