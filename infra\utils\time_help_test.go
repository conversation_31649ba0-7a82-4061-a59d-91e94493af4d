package utils

import (
	"reflect"
	"testing"
	"time"
)

func TestGetMonthFirstLast(t *testing.T) {
	tests := []struct {
		name         string
		wantFirstDay string
		wantLastDay  string
	}{
		// TODO: Add test cases.
		{
			name:         "",
			wantFirstDay: "",
			wantLastDay:  "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotFirstDay, gotLastDay := GetMonthFirstLast()
			if gotFirstDay != tt.wantFirstDay {
				t.<PERSON><PERSON>("GetMonthFirstLast() gotFirstDay = %v, want %v", gotFirstDay, tt.wantFirstDay)
			}
			if gotLastDay != tt.wantLastDay {
				t.<PERSON><PERSON>("GetMonthFirstLast() gotLastDay = %v, want %v", gotLastDay, tt.wantLastDay)
			}
		})
	}
}

func TestGetStatDate(t *testing.T) {
	type args struct {
		start string
		end   string
	}
	tests := []struct {
		name    string
		args    args
		wantS   time.Time
		wantE   time.Time
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "aaa",
			args: args{
				start: "2024-07-15",
				end:   "2024-07-21",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotS, gotE, err := GetStatDate(tt.args.start, tt.args.end)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStatDate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotS, tt.wantS) {
				t.Errorf("GetStatDate() gotS = %v, want %v", gotS, tt.wantS)
			}
			if !reflect.DeepEqual(gotE, tt.wantE) {
				t.Errorf("GetStatDate() gotE = %v, want %v", gotE, tt.wantE)
			}
		})
	}
}

func TestGetLastCycleDate(t *testing.T) {
	type args struct {
		start string
		end   string
	}
	tests := []struct {
		name    string
		args    args
		wantS   time.Time
		wantE   time.Time
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "aaa",
			args: args{
				start: "2024-01-01",
				end:   "2024-12-31",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotS, gotE, err := GetLastCycleDate(tt.args.start, tt.args.end)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLastCycleDate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotS, tt.wantS) {
				t.Errorf("GetLastCycleDate() gotS = %v, want %v", gotS, tt.wantS)
			}
			if !reflect.DeepEqual(gotE, tt.wantE) {
				t.Errorf("GetLastCycleDate() gotE = %v, want %v", gotE, tt.wantE)
			}
		})
	}
}
