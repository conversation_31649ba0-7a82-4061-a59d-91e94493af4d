package points

import (
	"eShop/infra/log"
	service "eShop/services/points-service"
	cachekey "eShop/services/points-service/enum"
	"time"
)

func FinishOrder() {
	lock(cachekey.FinishOrder, func() {
		s := service.NewClsPointsOrderService()
		log.Info("积分-积分兑换订单自动完成：" + time.Now().Format("2006-01-02 00:00:00"))
		err := s.FinishOrder(nil, "")
		if err != nil {
			log.Error("积分-积分兑换订单自动完成失败", err)
		}
	})
}
