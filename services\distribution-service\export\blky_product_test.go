package export

import (
	vo "eShop/view-model/distribution-vo"
	"testing"
)

func Test_sync2SqlServer(t *testing.T) {
	type args struct {
		ret []vo.BlkyProductData
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ret: []vo.BlkyProductData{
					{
						Swlm: "602102822605",
						Sfwm: "44813428690215529160",
					},
					{
						Swlm: "602102822606",
						Sfwm: "44813428690215529161",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := sync2SqlServer(tt.args.ret); (err != nil) != tt.wantErr {
				t.Errorf("sync2SqlServer() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
