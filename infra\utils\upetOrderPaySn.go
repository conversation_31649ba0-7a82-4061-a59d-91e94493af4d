package utils

import (
	"fmt"
	"github.com/spf13/cast"
	"math/rand"
	"time"
)

type UPetPaySN struct {
	MemberId int32
}

// Generate
// 翻译自商城方法：makePaySn
// 生成商城支付单编号(两位随机 + 从2000-01-01 00:00:00 到现在的秒数+微秒+会员ID%1000)，该值会传给第三方支付接口
// 长度 =2位 + 10位 + 3位 + 3位  = 18位
// 1000个会员同一微秒提订单，重复机率为1/100
// @return string
func (s UPetPaySN) Generate() string {
	rand.Seed(time.Now().Unix())
	//两位随机数
	segOne := cast.ToString(rand.Intn(89) + 10)
	//从2000-01-01 00:00:00 到现在的秒数 10位
	segTwo := fmt.Sprintf("%010d", time.Now().Unix()-946656000)
	//微秒数 3位
	tailMillSecond := (time.Now().UnixNano() / 1000000) % 1000
	segThree := cast.ToString(tailMillSecond)
	//会员ID%1000 3位
	segFour := fmt.Sprintf("%03d", s.MemberId%1000)

	return segOne + segTwo + segThree + segFour
}
