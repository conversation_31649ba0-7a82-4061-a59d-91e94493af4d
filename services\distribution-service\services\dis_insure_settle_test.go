package services

import (
	"eShop/services/common"
	"eShop/view-model"
	"eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

func TestDisInsureSettleService_InsureSettleApiPage(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo.InsureSettlePageApiReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []distribution_vo.InsureSettlePageApiData
		want1   int
		want2   int
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "case1",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				req: distribution_vo.InsureSettlePageApiReq{
					OrgId:       3,
					State:       0,
					SettleState: 0,
					ShopId:      61,
					DisId:       0,
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{
						PageIndex: 1,
						PageSize:  10,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := DisInsureSettleService{
				BaseService: tt.fields.BaseService,
			}
			got, got1, got2, err := s.InsureSettleApiPage(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InsureSettleApiPage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InsureSettleApiPage() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("InsureSettleApiPage() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("InsureSettleApiPage() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}
