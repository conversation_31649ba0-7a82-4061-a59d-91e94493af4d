package order_po

import (
	"eShop/infra/log"
	"time"

	"xorm.io/xorm"
)

// 定义支付状态：1:退款中，2：退款成功 3:退款失败
const (
	RefundStatusPending = 1 // 退款中
	RefundStatusSuccess = 2 // 退款成功
	RefundStatusFailed  = 3 // 退款失败
)

// RefundOrderPayDetail 订单退款支付明细表
type RefundOrderPayDetail struct {
	// 主键
	Id                int64     `json:"id" xorm:"pk 'id'"`
	// 店铺Id
	TenantId          int64     `json:"tenant_id" xorm:"'tenant_id'"`
	// 退款支付id
	OrderRefundPayId  int64     `json:"order_refund_pay_id" xorm:"'order_refund_pay_id'"`
	// 退款表id
	OrderRefundId     int64     `json:"order_refund_id" xorm:"'order_refund_id'"`
	// 退款单号
	RefundSn          string    `xorm:"not null default '''' comment('退款单号') unique VARCHAR(50)"`
	// 订单表id
	OrderId           int64     `json:"order_id" xorm:"'order_id'"`
	// 订单号
	OrderSn           string    `json:"order_sn" xorm:"'order_sn'"`
	// 对应的原始支付方式id
	SourcePaymentId   int64     `json:"source_payment_id" xorm:"'source_payment_id'"`
	// 对应的原始支付方式
	SourcePaymentType string    `json:"source_payment_type" xorm:"'source_payment_type'"`
	// 支付方式id
	PayId             int64     `json:"pay_id" xorm:"'pay_id'"`
	// 支付方式:1:现金,2:余额,3:押金,4:标记收款,5:微信,6:支付宝,7:自有POS，8：挂账，9 其它,10：储值卡 11扫码 12-次卡
	PaymentType       int       `json:"payment_type" xorm:"'payment_type'"`
	// 支付方式退款金额(分)
	PaymentAmount     int       `json:"payment_amount" xorm:"'payment_amount'"`
	// 退款收入金额,针对储值卡退款(分)
	RevenueAmount     int       `json:"revenue_amount" xorm:"'revenue_amount'"`
	// 支付状态：1:退款中，2：退款成功 3:退款失败
	PaymentStatus     int       `json:"payment_status" xorm:"'payment_status'"`
	// 失败原因
	PaymentFailRemark string    `json:"payment_fail_remark" xorm:"'payment_fail_remark'"`
	// 删除标识： 0未删除,1已删除
	IsDeleted         int       `json:"is_deleted" xorm:"'is_deleted'"`
	// 创建人
	CreatedBy         string    `json:"created_by" xorm:"'created_by'"`
	// 创建时间
	CreatedTime       time.Time `json:"created_time" xorm:"created 'created_time'"`
	// 更新人
	UpdatedBy         string    `json:"updated_by" xorm:"'updated_by'"`
	// 更新时间
	UpdatedTime       time.Time `json:"updated_time" xorm:"updated 'updated_time'"`
}

func (r *RefundOrderPayDetail) TableName() string {
	return "dc_order.refund_order_pay_detail"
}

// RefundOrderPayDetailRequest 查询请求参数
type RefundOrderPayDetailRequest struct {
	// 订单号
	OrderSn string
	// 退款支付id
	OrderRefundPayId int64
	// 店铺id
	TenantId int64
	// 订单id
	OrderId int64
	// 退款单号
	RefundSns []string
}

// Create 创建退款支付明细记录
func (r *RefundOrderPayDetail) Create(session *xorm.Session, data *RefundOrderPayDetail) error {
	_, err := session.Insert(data)
	if err != nil {
		log.Errorf("创建退款支付明细失败, err: %v", err)
		return err
	}
	return nil
}

// BatchCreate 批量创建退款支付明细记录
func (r *RefundOrderPayDetail) BatchCreate(session *xorm.Session, data []*RefundOrderPayDetail) error {
	_, err := session.Insert(data)
	if err != nil {
		log.Errorf("批量创建退款支付明细失败, err: %v", err)
		return err
	}
	return nil
}

// GetRefundOrderPayDetail 获取退款支付明细
func (r *RefundOrderPayDetail) GetRefundOrderPayDetail(session *xorm.Session, req RefundOrderPayDetailRequest) (out []*RefundOrderPayDetail, outMap map[string][]*RefundOrderPayDetail, total int64, err error) {
	// 如果没有退款订单号则直接返回
	if len(req.RefundSns) == 0 {
		return nil, nil, 0, nil
	}
	query := session.Table("dc_order.refund_order_pay_detail").Where("is_deleted = ?", 0).In("refund_sn", req.RefundSns)

	if req.OrderSn != "" {
		query = query.And("order_sn = ?", req.OrderSn)
	}
	if req.OrderRefundPayId > 0 {
		query = query.And("order_refund_pay_id = ?", req.OrderRefundPayId)
	}
	if req.TenantId > 0 {
		query = query.And("tenant_id = ?", req.TenantId)
	}
	if req.OrderId > 0 {
		query = query.And("order_id = ?", req.OrderId)
	}

	out = make([]*RefundOrderPayDetail, 0)
	total, err = query.FindAndCount(&out)
	if err != nil {
		log.Errorf("查询退款支付明细失败, err: %v", err)
		return nil, nil, 0, err
	}
	outMap = make(map[string][]*RefundOrderPayDetail)
	for _, v := range out {
		outMap[v.RefundSn] = append(outMap[v.RefundSn], v)
	}

	return out, outMap, total, nil
}

// Update 更新退款支付明细
func (r *RefundOrderPayDetail) Update(session *xorm.Session, data *RefundOrderPayDetail) error {
	_, err := session.ID(data.Id).Update(data)
	if err != nil {
		log.Errorf("更新退款支付明细失败, err: %v", err)
		return err
	}
	return nil
}

// UpdatePaymentStatus 更新支付状态
func (r *RefundOrderPayDetail) UpdatePaymentStatus(session *xorm.Session, id int64, status string, failRemark string) error {
	updates := map[string]interface{}{
		"payment_status": status,
	}
	if failRemark != "" {
		updates["payment_fail_remark"] = failRemark
	}

	_, err := session.Table("refund_order_pay_detail").
		ID(id).
		Update(updates)

	if err != nil {
		log.Errorf("更新退款支付状态失败, id: %d, status: %s, err: %v", id, status, err)
		return err
	}
	return nil
}
