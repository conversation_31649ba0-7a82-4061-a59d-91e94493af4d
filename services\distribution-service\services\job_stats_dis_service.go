package services

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/distribution-vo"
	"fmt"
	"github.com/shopspring/decimal"
	"sync"
	"time"
	"xorm.io/xorm"
)

type JobStatsDisService struct {
	common.BaseService
}

type JobStatsDisServiceData struct {
	Engine         *xorm.Engine
	Data           *po.StatsShopDistributorDaily
	StartTimestamp int64
	EndTimestamp   int64
}

// 跑历史数据，生成起始每天的统计记录
func (s JobStatsDisService) StatsDisOrderDaliyDataRun(startTime, endTime string) {
	startDay, err := time.Parse(utils.DateLayout, startTime)
	if err != nil {
		return
	}
	endDay, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return
	}
	for i := startDay; i.Before(endDay) || i.Equal(endDay); i = i.AddDate(0, 0, 1) {
		// 跑一天的数据
		fmt.Println("处理日期范围-商品分销订单-日:", i.Format("2006-01-02"), "-", i.Format("2006-01-02"))
		s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
			StartDate: i.Format(time.DateOnly),
			EndDate:   i.Format(time.DateOnly),
		})

		// 检测是不是周末，跑一周的数据
		weekday := i.Weekday()
		if weekday == time.Sunday {
			startDate := i.AddDate(0, 0, -6)
			fmt.Println("处理日期范围-商品分销订单-周:", i.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是月末，跑一月的数据
		tomorrow := i.AddDate(0, 0, 1)
		if tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(0, -1, 0)
			fmt.Println("处理日期范围-商品分销订单-月:", i.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是年末，跑一年的数据
		if tomorrow.Month() == 1 && tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(-1, 0, 0)
			s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}
	}
}

func (s *JobStatsDisService) StatsDisOrderDaliyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsDisOrderDataLock, "daily")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := now.AddDate(0, 0, -1)
	s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   startDate.Format("2006-01-02"),
	})
}

func (s *JobStatsDisService) StatsDisOrderWeeklyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsDisOrderDataLock, "weekly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := now.AddDate(0, 0, -int(now.Weekday())-6) // 减去今天是星期几，再减去6天
	endDate := startDate.AddDate(0, 0, 6)
	s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s *JobStatsDisService) StatsDisOrderMonthlyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsDisOrderDataLock, "monthly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, -1) // 加一个月，再减去一天
	s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s *JobStatsDisService) StatsDisOrderYearlyData() {
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsDisOrderDataLock, "yearly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*10)
	if !setNxReslt {
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)

	now := time.Now()
	startDate := time.Date(now.Year()-1, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := time.Date(now.Year()-1, 12, 31, 0, 0, 0, 0, time.Local)
	s.StatsDisOrderDailyData(vo.StatsShopDistributorDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

// 商品分销订单概览
// 1 累计下单指标
// 2 累计成交指标
// 3 分销新增来源数据
// 4 查询出的结果更新到StatsShopDistributorDaily表
// StatsDisOrderDailyData 定时任务-统计商品分销订单每天的数据
func (s *JobStatsDisService) StatsDisOrderDailyData(req ...vo.StatsShopDistributorDailyReq) {
	log.Infof("StatsDisOrderDailyData cron begin:%v", req)
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("StatsDisOrderDailyData panic error:%v", err) // 这里的err其实就是panic传入的内容，55
		}
	}()
	s.Begin()
	defer s.Close()
	//日期字符串StartDate 2024-06-01 转成时间戳
	var StartDate, EndDate string
	if len(req) > 0 {
		StartDate = req[0].StartDate
		EndDate = req[0].EndDate
	}

	startTimestamp := utils.Date2Timestamp(StartDate)
	endTimestamp := utils.AddDate2Timestamp(EndDate)

	serv := &JobStatsDisServiceData{
		Engine:         s.Engine,
		StartTimestamp: startTimestamp,
		EndTimestamp:   endTimestamp,
	}
	//先按日期查询订单商品表分销员，然后再遍历下面
	type DisMemeberInfo struct {
		DisMemberId    int
		DisId          int
		ShopId         int
		ShopName       string
		EnterpriseId   int64
		EnterpriseName string
	}
	var disMemeberInfo = make([]DisMemeberInfo, 0)
	if err := s.Engine.SQL(`
select dis_member_id, d.id as dis_id, d.shop_id, s.shop_name, s.enterprise_id, e.enterprise_name
from (select distinct og.dis_member_id
      from upetmart.upet_orders o
               left join upetmart.upet_order_goods og on o.order_id = og.order_id
      where o.store_id = 3
        and og.store_id = 3
        and o.add_time >= ?
        and o.add_time < ?
        and o.order_father = 0
        and og.dis_member_id > 0) m
         left join eshop.dis_distributor d on m.dis_member_id = d.member_id and d.org_id = 3
         left join eshop.shop s on d.shop_id = s.id and s.org_id = 3
         left join eshop.scrm_enterprise e on s.enterprise_id = e.id;
`, startTimestamp, endTimestamp).Find(&disMemeberInfo); err != nil {
		log.Errorf("StatsDisOrderDailyData 查询分销员id失败 error:%v", err)
	}

	if len(disMemeberInfo) == 0 {
		log.Infof("StatsDisOrderDailyData %s-%s-无分销员统计数据", StartDate, EndDate)
		return
	}
	// 遍历分销员的数据统计
	for _, v := range disMemeberInfo {
		if v.ShopId == 0 || v.EnterpriseId == 0 {
			log.Errorf("StatsDisOrderDailyData 分销员数据异常 error:%v", v)
			continue
		}
		serv.Data = new(po.StatsShopDistributorDaily)
		//判断表已存在
		_, err := serv.Engine.Table("eshop.stats_shop_distributor_daily").
			Where("stat_date = ? and end_date =? and type = 2 and shop_id = ? and dis_id = ?", StartDate, EndDate, v.ShopId, v.DisId).Get(serv.Data)
		if err != nil {
			log.Errorf("StatsDisOrderDailyData 查统计表数据失败 error:%v", err)
		}

		wg := sync.WaitGroup{}
		wg.Add(3)
		// 累计分销下单指标
		go func(disMemberId int) {
			defer wg.Done()
			serv.StatsDisOrderDailyData1(disMemberId)
		}(v.DisMemberId)

		// 累计分销成交指标
		go func(disMemberId int) {
			defer wg.Done()
			serv.StatsDisOrderDailyData2(disMemberId)
		}(v.DisMemberId)

		// 分销海报链接统计
		go func(disMemberId int) {
			defer wg.Done()
			serv.StatsDisOrderDailyData3(disMemberId)
		}(v.DisMemberId)

		wg.Wait()

		//公共数据
		serv.Data.StatDate = StartDate
		serv.Data.EndDate = EndDate
		serv.Data.ShopId = v.ShopId
		serv.Data.ShopName = v.ShopName
		serv.Data.Type = 2
		serv.Data.DisId = v.DisId
		serv.Data.EnterpriseId = v.EnterpriseId
		serv.Data.EnterpriseName = v.EnterpriseName
		serv.Data.IsDis = 1

		if serv.Data.Id > 0 {
			if _, err = serv.Engine.Table("eshop.stats_shop_distributor_daily").ID(serv.Data.Id).Update(serv.Data); err != nil {
				log.Errorf("StatsDisOrderDailyData 更新数据失败 error:%s", err.Error())
			}
		} else {
			_, err = serv.Engine.Table("eshop.stats_shop_distributor_daily").Insert(&serv.Data)
			if err != nil {
				log.Errorf("StatsDisOrderDailyData 添加数据失败 error:%v", err)
			}
		}
	}
}

// StatsDisOrderDailyData1 累计分销下单指标
func (s *JobStatsDisServiceData) StatsDisOrderDailyData1(memberId int) {
	var (
		OrderCustomerCount int
		OrderCount         int
		OrderAmount        float64
		OrderProductCount  int
		OrderCustomerPrice int
	)
	session := s.Engine
	_, err := session.SQL(`
select COUNT(DISTINCT buyer_id) AS 'order_customer_count',
       COUNT(DISTINCT order_id) AS 'order_count' ,
       ifnull(sum(order_amount),0) as order_amount
from upetmart.upet_orders where order_id in (
    SELECT og.order_id
    FROM upetmart.upet_order_goods og
         LEFT JOIN upetmart.upet_orders o ON og.order_id = o.order_id
    WHERE o.store_id = 3
      AND og.store_id = 3
      AND o.add_time >= ?
      AND o.add_time < ?
      AND o.order_father = 0
      AND o.is_dis = 1
      AND og.dis_member_id = ?
    );
`, s.StartTimestamp, s.EndTimestamp, memberId).
		Get(&OrderCustomerCount, &OrderCount, &OrderAmount)
	if err != nil {
		log.Errorf("StatsDisOrderDailyData 累计分销下单指标 error:%v", err)
	}
	if OrderCustomerCount > 0 {
		result := decimal.NewFromFloat(OrderAmount).Mul(decimal.NewFromInt(100)).Div(decimal.NewFromInt(int64(OrderCustomerCount))).Round(0).IntPart()
		OrderCustomerPrice = int(result)
	}

	//商品数
	_, err = session.SQL(`
select 
    ifnull(sum(goods_num),0) AS 'order_product_count'
from upetmart.upet_order_goods where order_id in (
    SELECT og.order_id
    FROM upetmart.upet_order_goods og
         LEFT JOIN upetmart.upet_orders o ON og.order_id = o.order_id
    WHERE o.store_id = 3
      AND og.store_id = 3
      AND o.add_time >= ?
      AND o.add_time < ?
      AND o.order_father = 0
      AND o.is_dis = 1
      AND og.dis_member_id = ?
);
`, s.StartTimestamp, s.EndTimestamp, memberId).
		Get(&OrderProductCount)
	if err != nil {
		log.Errorf("StatsDisOrderDailyData 累计分销下单商品指标 error:%v", err)
	}
	s.Data.OrderCustomerCount = OrderCustomerCount
	s.Data.OrderCount = OrderCount
	s.Data.OrderAmount = int(OrderAmount * 100)
	s.Data.OrderProductCount = OrderProductCount
	s.Data.OrderCustomerPrice = OrderCustomerPrice
}

// StatsDisOrderDailyData2 累计成交指标
func (s *JobStatsDisServiceData) StatsDisOrderDailyData2(memberId int) {
	var (
		TransCustomerCount int
		TransCount         int
		TransAmount        float64
		TransProductCount  int
		TransCustomerPrice int
	)
	session := s.Engine
	_, err := session.SQL(`
select COUNT(DISTINCT buyer_id) AS 'trans_customer_count',
       COUNT(DISTINCT order_id) AS 'trans_count' ,
       ifnull(sum(order_amount),0) as trans_amount
from upetmart.upet_orders where order_id in (
    SELECT og.order_id
    FROM upetmart.upet_order_goods og
         LEFT JOIN upetmart.upet_orders o ON og.order_id = o.order_id
    WHERE o.store_id = 3
      AND og.store_id = 3
      AND o.add_time >= ?
      AND o.add_time < ?
      AND o.order_father > 0
      AND o.payment_time > 0
      AND o.is_dis = 1
      AND og.dis_member_id = ?
    );
`, s.StartTimestamp, s.EndTimestamp, memberId).
		Get(&TransCustomerCount, &TransCount, &TransAmount)

	if err != nil {
		log.Errorf("StatsDisOrderDailyData 累计分销成交指标 error:%v", err)
	}
	if TransCustomerCount > 0 {
		TransCustomerPrice = int(TransAmount / float64(TransCustomerCount) * 100)
	}

	//商品成交数
	_, err = session.SQL(`
select 
    ifnull(sum(goods_num),0) AS 'trans_product_count'
from upetmart.upet_order_goods where order_id in (
    SELECT og.order_id
    FROM upetmart.upet_order_goods og
         LEFT JOIN upetmart.upet_orders o ON og.order_id = o.order_id
    WHERE o.store_id = 3
      AND og.store_id = 3
      AND o.add_time >= ?
      AND o.add_time < ?
      AND o.order_father > 0
      AND o.payment_time > 0
      AND o.is_dis = 1
      AND og.dis_member_id = ?
);
`, s.StartTimestamp, s.EndTimestamp, memberId).
		Get(&TransProductCount)
	if err != nil {
		log.Errorf("StatsDisOrderDailyData 累计分销成交商品数指标 error:%v", err)
	}

	s.Data.TransCustomerCount = TransCustomerCount
	s.Data.TransCount = TransCount
	s.Data.TransAmount = int(TransAmount * 100)
	s.Data.TransProductCount = TransProductCount
	s.Data.TransCustomerPrice = TransCustomerPrice
}

// StatsDisOrderDailyData3 分销海报链接统计
func (s *JobStatsDisServiceData) StatsDisOrderDailyData3(memberId int) {
	var (
		PosterScanCount        int
		PosterTransCount       int
		PosterTransAmount      float64
		LinkClickCount         int
		LinkTransCount         int
		LinkTransAmount        float64
		FanRelationTransCount  int
		FanRelationTransAmount float64
	)

	session := s.Engine
	_, err := session.SQL(`
SELECT IFNULL(SUM(CASE WHEN o.dis_type IN (2, 3) THEN 1 ELSE 0 END), 0) AS poster_scan_count,
       COUNT(DISTINCT CASE WHEN o.dis_type =2 AND payment_time > 0 THEN o.order_id END) AS poster_trans_count,
       IFNULL(SUM(DISTINCT CASE WHEN o.dis_type = 2 AND payment_time > 0 THEN o.order_amount END), 0) AS poster_trans_amount,
       COUNT(DISTINCT CASE WHEN o.dis_type = 1 THEN og.order_id END) AS link_click_count,
       COUNT(DISTINCT CASE WHEN o.dis_type = 1 AND payment_time > 0 THEN o.order_id END) AS link_trans_count,
       IFNULL(SUM(CASE WHEN o.dis_type = 1 AND payment_time > 0 THEN og.goods_pay_price END), 0) AS link_trans_amount,
       COUNT(DISTINCT CASE WHEN o.dis_type = 5 AND payment_time > 0 THEN o.order_id END) AS fan_relation_trans_count,
       IFNULL(SUM(CASE WHEN o.dis_type = 5 AND payment_time > 0 THEN og.goods_pay_price END),0) AS fan_relation_trans_amount
FROM upetmart.upet_orders o
     LEFT JOIN upetmart.upet_order_goods og ON o.order_id = og.order_id
WHERE o.store_id = 3
  AND o.add_time >= ?
  AND o.add_time < ?
  AND o.order_father = 0
  AND og.dis_member_id = ?;
`, s.StartTimestamp, s.EndTimestamp, memberId).Get(&PosterScanCount, &PosterTransCount, &PosterTransAmount,
		&LinkClickCount, &LinkTransCount, &LinkTransAmount, &FanRelationTransCount, &FanRelationTransAmount)

	if err != nil {
		log.Errorf("StatsOrderDailyData 分销海报链接统计 error:%v", err)
	}
	s.Data.PosterScanCount = PosterScanCount
	s.Data.PosterTransCount = PosterTransCount
	s.Data.PosterTransAmount = int(PosterTransAmount * 100)
	s.Data.LinkClickCount = LinkClickCount
	s.Data.LinkTransCount = LinkTransCount
	s.Data.LinkTransAmount = int(LinkTransAmount * 100)
	s.Data.FanRelationTransCount = FanRelationTransCount
	s.Data.FanRelationTransAmount = int(FanRelationTransAmount * 100)
}
