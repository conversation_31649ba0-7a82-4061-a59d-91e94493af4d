CREATE TABLE `cls_dis_address` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dis_id` int NOT NULL COMMENT '分销员ID',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区/县',
  `ress` varchar(200) NOT NULL COMMENT '详细地址',
  `name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL COMMENT '收货人电话',
  `encrypt_phone` varchar(100) NOT NULL DEFAULT '' COMMENT '加密手机号',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址，0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_dis_id` (`dis_id`)
) ENGINE=InnoDB COMMENT='收货地址表';

CREATE TABLE `cls_points_rule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_code` varchar(50) NOT NULL COMMENT '商品编码',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '规则类型：1-体系内，2-体系外',
  `start_time` datetime NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '规则生效开始时间',
  `end_time` datetime NOT NULL DEFAULT '9999-12-31 23:59:59' COMMENT '规则生效结束时间',
  `region` varchar(50) NOT NULL DEFAULT '' COMMENT '地区',
  `points` int NOT NULL DEFAULT '0' COMMENT '积分值',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '规则状态：1-启用，2-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_code` (`goods_code`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_region` (`region`)
) ENGINE=InnoDB COMMENT='积分规则配置表';

CREATE TABLE `cls_points_rule_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_code` varchar(50) NOT NULL COMMENT '商品编码',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '规则类型：1-体系内，2-体系外',
  `biz_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '操作类型：1-规则变更，2-规则时间变更，3-积分数变更',
  `description` varchar(1000) NOT NULL DEFAULT '' COMMENT '操作详情',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_code` (`goods_code`)
) ENGINE=InnoDB COMMENT='积分规则日志表';

CREATE TABLE `cls_points_goods` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `type` tinyint(1) NOT NULL COMMENT '商品类型：1-实物商品，2-虚拟商品',
  `sub_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品子类型：1-第三方商品',
  `cost_price` int NOT NULL COMMENT '采购成本',
  `market_price` int NOT NULL COMMENT '市场价格',
  `points_price` int NOT NULL COMMENT '兑换所需积分',
  `stock` int NOT NULL DEFAULT '0' COMMENT '兑换库存',
  `image_url` varchar(255) NOT NULL DEFAULT '0' COMMENT '商品主图URL',
  `description` longtext COMMENT '商品描述',
  `exchange_limit` int NOT NULL DEFAULT '0' COMMENT '每人最多兑换数量，NULL表示不限制',
  `start_time` datetime NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '兑换开始时间',
  `end_time` datetime NOT NULL DEFAULT '9999-12-31 23:59:59' COMMENT '兑换结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '商品状态：1-上架，2-下架',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_points_price` (`points_price`)
) ENGINE=InnoDB COMMENT='积分商品表';

CREATE TABLE `cls_points_order` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `dis_id` int NOT NULL COMMENT '分销员ID',
  `goods_id` int NOT NULL COMMENT '商品ID',
  `goods_name` varchar(100) NOT NULL COMMENT '商品名称',
  `goods_type` tinyint(1) NOT NULL COMMENT '商品类型: 1-实物商品，2-虚拟商品',
  `points_cost` int NOT NULL COMMENT '消耗积分',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '兑换数量',
  `status` tinyint NOT NULL COMMENT '订单状态：1-待发货，2-已发货，3-已完成',
  `address_id` int DEFAULT NULL COMMENT '收货地址ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `express_company` varchar(50) DEFAULT NULL COMMENT '快递公司',
  `express_no` varchar(50) DEFAULT NULL COMMENT '快递单号',
  `express_time` datetime DEFAULT NULL COMMENT '快递发货时间',
  `payment_method` tinyint(1) NOT NULL DEFAULT '1' COMMENT '支付方式:1-积分',
  `order_time` datetime NOT NULL COMMENT '下单时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `order_source` tinyint(1) NOT NULL COMMENT '订单来源: 1-宠利扫小程序',
  `order_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '订单类型:1-积分兑换',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_no` (`order_no`),
  KEY `idx_dis_id` (`dis_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_address_id` (`address_id`),
  KEY `idx_order_time` (`order_time`)
) ENGINE=InnoDB COMMENT='积分兑换订单表';

CREATE TABLE `cls_points_order_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int NOT NULL COMMENT '订单ID',
  `status` tinyint NOT NULL COMMENT '订单状态: 1-待发货，2-已发货，3-已完成',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB COMMENT='订单状态变更日志表';

CREATE TABLE `cls_points_account` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dis_id` int NOT NULL COMMENT '分销员ID：0-企业汇总记录',
  `enterprise_id` int NOT NULL COMMENT '企业ID',
  `available_points` int NOT NULL DEFAULT '0' COMMENT '可用积分',
  `total_points` int NOT NULL DEFAULT '0' COMMENT '获取积分总数',
  `consume_points` int NOT NULL DEFAULT '0' COMMENT '消耗积分总数',
  `expired_points` int NOT NULL DEFAULT '0' COMMENT '失效积分总数',
  `expiring_points` int NOT NULL DEFAULT '0' COMMENT '即将到期积分',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_dis_id` (`dis_id`),
  KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE=InnoDB COMMENT='积分账户表';

CREATE TABLE `cls_points_flow` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dis_id` int NOT NULL COMMENT '分销员ID',
  `enterprise_id` int NOT NULL COMMENT '企业ID',
  `type` tinyint(1) NOT NULL COMMENT '类型:1-积分进账，2-积分出账',
  `biz_type` int NOT NULL COMMENT '业务类型:1-开单商品，2-分销商品，3-积分兑换，4-商品退货积分扣减，5-积分到期自动扣减，6-积分赠送收入，7-积分赠送支出，8-获赠积分到期',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:1-待使用，2-部分使用，3-已使用，4-已过期',
  `points` int NOT NULL DEFAULT '0' COMMENT '积分值',
  `remaining_points` int NOT NULL DEFAULT '0' COMMENT '剩余积分',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `bill_id` int NOT NULL DEFAULT '0' COMMENT '开单记录id',
  `bill_order_no` varchar(32) NOT NULL DEFAULT '' COMMENT '开单单号',
  `bill_goods_code` varchar(32) NOT NULL DEFAULT '' COMMENT '开单商品编码',
  `points_order_id` int NOT NULL DEFAULT '0' COMMENT '积分兑换订单id',
  `points_blky` int NOT NULL DEFAULT '0' COMMENT '积分值(北京百林康源)',
  `points_szld` int NOT NULL DEFAULT '0' COMMENT '积分值(深圳利都)',
  `region` varchar(50) NOT NULL DEFAULT '' COMMENT '区域',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `staff_no` varchar(50) NOT NULL DEFAULT '' COMMENT '员工编号',
  `occur_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_dis_id` (`dis_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_bill_order_no` (`bill_order_no`),
  KEY `idx_bill_goods_code` (`bill_goods_code`),
  KEY `idx_points_order_id` (`points_order_id`),
  KEY `idx_staff_no` (`staff_no`),
  KEY `idx_bill_id` (`bill_id`)
) ENGINE=InnoDB COMMENT='积分流水表';

CREATE TABLE `cls_points_daily_stats` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `issue_total` int NOT NULL DEFAULT '0' COMMENT '发放总额',
  `issue_blky` int NOT NULL DEFAULT '0' COMMENT '发放总额(北京百林)',
  `issue_szld` int NOT NULL DEFAULT '0' COMMENT '发放总额(深圳利都)',
  `consume_total` int NOT NULL DEFAULT '0' COMMENT '消耗总额',
  `consume_blky` int NOT NULL DEFAULT '0' COMMENT '消耗总额(北京百林)',
  `consume_szld` int NOT NULL DEFAULT '0' COMMENT '消耗总额(深圳利都)',
  `expired_total` int NOT NULL DEFAULT '0' COMMENT '失效总额',
  `expired_blky` int NOT NULL DEFAULT '0' COMMENT '失效总额(北京百林)',
  `expired_szld` int NOT NULL DEFAULT '0' COMMENT '失效总额(深圳利都)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB COMMENT='积分每日统计表';

CREATE TABLE `cls_points_source` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_id` int NOT NULL COMMENT '来源id',
  `brand_id` int NOT NULL COMMENT '商品品牌',
  `brand_name` varchar(50) NOT NULL DEFAULT '' COMMENT '商品品牌',
  `source_name` varchar(50) DEFAULT NULL COMMENT '来源公司名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='积分来源表';

INSERT INTO cls_points_source(source_id, brand_id, brand_name, source_name)
VALUES (1, 448, '科学喵', '北京百林康源'),
       (1, 136, '宠儿香', '北京百林康源'),
       (2, 121, '贵族', '深圳利都');


CREATE INDEX idx_shr_staff_no ON dm_mdm.shr_t_staff_info (shr_staff_no);
    
    
INSERT INTO datacenter.wechat_subscribe_message_template(template_key, template_type, template_id, page, content, comment, store_id, create_time, update_time)
VALUES
('user-integral-change',2,'S5eqvE-ZYNwnvHgmM8lIsrG8a6xG87aelCJIG-e9ybE','/app/points/details','{"time1":{"value":"%s"},"number2":{"value":"%d"},"number3":{"value":"%d"},"thing4":{"value":"%s"}}','小程序积分变更通知模板',4,now(),now()),
('user-integral-expire',2,'20W7rR6S0nNOkQvT7KfPCruLxRCdG7Y13MTmetpZ3B8','/app/points/index','{"number2":{"value":"%d"},"time3":{"value":"%s"},"thing4":{"value":"%s"}}','小程序积分过期通知模板',4,now(),now());