package points_po

import (
	"time"
)

// ClsPointsGoods 积分商品表
type ClsPointsGoods struct {
	SuperEntity[int] `xorm:"extends"`
	Name             string    `json:"name" xorm:"'name' notnull"`
	Type             int       `json:"type" xorm:"'type' notnull"`                   // 1-实物商品，2-虚拟商品
	SubType          int       `json:"sub_type" xorm:"'sub_type' notnull default 0"` // 1-第三方商品
	CostPrice        int       `json:"cost_price" xorm:"'cost_price' notnull"`
	MarketPrice      int       `json:"market_price" xorm:"'market_price' notnull"`
	PointsPrice      int       `json:"points_price" xorm:"'points_price' notnull"`
	Stock            int       `json:"stock" xorm:"'stock' notnull default 0"`
	ImageUrl         string    `json:"image_url" xorm:"'image_url'"`
	Description      string    `json:"description" xorm:"'description'"`
	ExchangeLimit    int       `json:"exchange_limit" xorm:"'exchange_limit'"` // 0 代表没有限制
	StartTime        time.Time `json:"start_time" xorm:"'start_time' notnull default '0001-01-01 00:00:00'"`
	EndTime          time.Time `json:"end_time" xorm:"'end_time' notnull default '9999-12-32 23:59:59'"`
	Status           int       `json:"status" xorm:"'status' notnull default 1"` //  1-上架，2-下架
}

// TableName 指定表名
func (e ClsPointsGoods) TableName() string {
	return "cls_points_goods"
}

// AsPointer 返回一个指向结构体实例的指针
func (e ClsPointsGoods) AsPointer() any {
	return &e
}
