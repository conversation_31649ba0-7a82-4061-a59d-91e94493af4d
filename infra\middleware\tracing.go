package middleware

import (
	"bytes"
	"fmt"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"io/ioutil"
	"net/http"
	"time"
)

func WithTracing(handle http.Handler) http.Handler {
	fun := func(writer http.ResponseWriter, request *http.Request) {

		var (
			span     opentracing.Span
			traceId  = request.Header.Get("Trace-Id")
			spanName = fmt.Sprintf("%s-%s", request.URL.Path, time.Now().String())
		)

		tracer := opentracing.GlobalTracer()
		if traceId == "" {
			carrier := opentracing.HTTPHeadersCarrier{}
			span = tracer.StartSpan(spanName)
			err := tracer.Inject(span.Context(), opentracing.HTTPHeaders, carrier)
			if err != nil {
				panic(err)
			} else {
				traceId = carrier["Uber-Trace-Id"][0]
			}
		} else {
			carrier := opentracing.HTTPHeadersCarrier{}
			carrier.Set("Uber-Trace-Id", traceId)
			wireContext, err := tracer.Extract(opentracing.HTTPHeaders, carrier)
			if err != nil {
				panic(err)
			}
			span = opentracing.StartSpan(spanName, ext.RPCServerOption(wireContext))
		}

		//获取request body
		if request.ContentLength > 0 {
			var bodyBuffer []byte
			bodyBuffer, err := ioutil.ReadAll(request.Body)
			if err != nil {

			}

			request.Body = ioutil.NopCloser(bytes.NewReader(bodyBuffer))
			span.SetTag("Request-body", string(bodyBuffer))
		}

		span.SetTag("RequestURI", request.RequestURI)
		span.SetTag("Trace-Id", traceId)
		defer span.Finish()

		request.Header.Set("Trace-Id", traceId)
		ctx := opentracing.ContextWithSpan(request.Context(), span)
		request = request.WithContext(ctx)

		handle.ServeHTTP(writer, request)
	}
	return http.HandlerFunc(fun)
}
