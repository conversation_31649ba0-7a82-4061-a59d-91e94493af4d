package manager

import (
	"eShop/domain/product-po"
	"eShop/infra/utils"
	"eShop/services/product-service/export"
	"eShop/services/product-service/services"
	"eShop/view-model"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"net/http"
)

// DelCategory
// @Summary 商品服务-商品分类-删除分类
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param DelCategoryReq body vo.DelCategoryReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/category/del [POST]
func DelCategory(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.DelCategoryReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	server := services.CategoryService{Request: request}
	if err := server.DelCategory(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "删除成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// CategoryList
// @Summary 商品服务-商品分类-分类列表
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param CategoryListReq body vo.CategoryListReq true " "
// @Success 200 {object} vo.CategoryListRes
// @Router /product-app/manager/category/list [POST]
func CategoryList(writer http.ResponseWriter, request *http.Request) {
	out := vo.CategoryListRes{}
	out.Code = 400
	req, err := utils.Bind[vo.CategoryListReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	server := services.CategoryService{Request: request}

	if out.Data, err = server.CategoryList(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "查询成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// SyncAllCategory
// @Summary 商品服务-商品分类-门店同步所有分类
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param SyncAllCategoryReq body vo.SyncAllCategoryReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/category/sync [POST]
func SyncAllCategory(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req := vo.SyncAllCategoryReq{}
	server := services.CategoryService{Request: request}
	if err := server.SyncAllCategory(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "同步成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// AddCategory
// @Summary 商品服务-商品分类-添加、编辑分类 @v1.3.0
// @Tags 宠物连锁SAAS-管理后台
// @Accept  json
// @Produce  json
// @Param AddCategoryReq body vo.AddCategoryReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Router /product-app/manager/category/add [POST]
func AddCategory(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.AddCategoryReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	// 默认是前台分类
	if req.Type <= 0 {
		req.Type = product_po.CategoryTypeOnline
	}
	server := services.CategoryService{Request: request}
	if err := server.AddCategory(req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "操作成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

func BatchImportProductAndCategory(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400
	req, err := utils.Bind[vo.BatchImportProductAndCategoryReq](request)
	if err != nil {
		out.Message = "解析参数错误"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.CategoryService{Request: request}

	switch req.BatchType {
	case 1:
		err = server.BatchImportFrontCategory(req)
	case 2:
		err = server.BatchImportBackCategory(req)
	case 3:
		err = server.BatchImportSupplier(req)
	case 4:
		err = server.BatchImportProductBrand(req)
	case 5:
		err = export.SaasChainProductExport{}.BatchImportProduct(req, request)
	}

	if err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200
	out.Message = "操作成功"
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}
