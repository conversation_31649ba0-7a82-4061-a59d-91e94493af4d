package order_vo

import (
	order_po "eShop/domain/order-po"
	"eShop/infra/response"
)

// CalcRequest 计算购物车请求参数
type CalcRequest struct {
	Data     CalcInfo `json:"data"`
	IsCommit int      `json:"is_commit"` // 是否提交订单
	//订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 13在线问诊 14年夜饭活动 15团长开团 16爱心币订单 17付费会员卡 18家庭医生服务包 19会员卡0元购, 21电商VIP实物订单, 22次卡, 23储值卡, 99助力订单
	OrderType int `json:"order_type"`
}

// 支付订单请求参数
type CommitRequest struct {
	// 计算结果信息
	CalcInfo CalcInfo `json:"calc_info"`
	// 支付详情
	OrderPayment []OrderPayment `json:"order_payment"`
	// 订单备注
	Remark string `json:"remark"`
	// 收银员ID
	CashierId string `json:"cashier_id"`
	// 收银员名称
	CashierName string `json:"cashier_name"`
	// 整单折后价(分)
	OrderDiscountAmount int `json:"order_discount_amount"`
	// 订单原总价(分)
	OriginalAmount int `json:"original_amount"`
	// 抹零金额(分)
	RoundingAmount int `json:"rounding_amount"`
	// 店铺名称
	ShopName string `json:"shop_name"`
	//订单类型 1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 13在线问诊 14年夜饭活动 15团长开团 16爱心币订单 17付费会员卡 18家庭医生服务包 19会员卡0元购, 21电商VIP实物订单, 22次卡, 23储值卡, 99助力订单
	OrderType int `json:"order_type"`
	//主体
	OrgId int `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	// 宠物ID
	PetId string `json:"pet_id"`
	// 宠物名称
	PetName string `json:"pet_name"`
	// 购买储值卡信息
	BuyStoreCard BuyStoreCard `json:"buy_store_card"`
	// 购买次卡信息
	BuyTimeCard BuyTimeCard `json:"buy_time_card"`
	// 预计送达时间
	ExpectedTime string `json:"expected_time"`
	//押金退款类型
	YJPayType string `json:"yj_pay_type" xorm:"default 0 comment('押金退款类型') INT 'yj_pay_type'"`
}
type OrderSubmitResponse struct {
	//提交订单成功后，返回订单ID
	OrderSn string `json:"order_sn"`
	//提交订单成功后的支付单号
	PaySn string `json:"pay_sn"`
}

type BuyStoreCard struct {
	// 次卡名称
	CardName string `json:"card_name"`
	// 次卡ID
	CardID string `json:"card_id"`
	// 次卡卡号
	CardNo string `json:"card_no"`
	//1开卡 2续卡
	Type string `json:"type"`
	//开卡金额
	Amount int `json:"amount"`
	//赠送金额
	AmountGift int `json:"amount_gift"`
}

type BuyTimeCard struct {
	// 次卡名称
	CardName string `json:"card_name"`
	// 次卡ID
	CardID string `json:"card_id"`
	// 次卡卡号
	CardNo string `json:"card_no"`
	//1开卡 2续卡
	Type string `json:"type"`
	//购买次数
	Num string `json:"num"` // 次卡卡号
	//赠送次数
	NumGift string `json:"num_gift"` // 次卡卡号
	//开卡金额
	Amount int `json:"amount"` // 次卡卡号
}

type DetailRequest struct {
	OrderSn       string `json:"order_sn"`        // 子订单号
	ParentOrderSn string `json:"parent_order_sn"` // 主订单号
}

type DetailResponse struct {
	// 子订单号
	OrderSn string `json:"order_sn"`
	// 订单状态：0已取消,10(默认)未付款,20已付款,30已完成
	OrderStatus int `json:"order_status"`
	// 订单状态：0已取消,10(默认)未付款,20已付款,30已完成
	OrderStatusChild int `json:"order_status_child"`
	// 会员名称
	MemberName string `json:"member_name"`
	// 会员电话
	MemberTel string `json:"member_tel"`
	// 会员电话
	EnMemberTel string `json:"en_member_tel"`
	// 宠物名称
	PetName string `json:"pet_name"`
	// 收银员名称
	CashierName string `json:"cashier_name"`
	// 订单创建时间
	CreateTime string `json:"create_time"`
	// 支付时间
	PayTime string `json:"pay_time"`
	// 实收金额（单位分）| 应付金额（单位分）
	PayAmount int `json:"pay_amount"`
	// 优惠金额（单位分）
	PrivilegeAmount int `json:"privilege_amount"`
	// 账单总价（单位分）
	TotalAmount int `json:"total_amount"`
	// 备注
	SellerMemo string `json:"seller_memo"`
	// 优惠列表(这里指整个订单参与了哪些活动)
	PromotionList []PromotionInfo `json:"promotion_list"`
	// 支付详情
	OrderPayment []OrderPayment `json:"order_payment"`
	// 订单商品
	OrderProduct []OrderProduct `json:"order_product"`
	// 订单退款
	OrderRefundList []OrderRefund `json:"order_refund_list"`
	// 退款方式
	RefundMethod []*order_po.RefundOrderPayDetail `json:"refund_method"`
	// 储值卡订单
	StoreCardOrder *order_po.StoreCardOrder `json:"store_card_order"`
	// 次卡订单
	TimeCardOrder *order_po.TimeCardOrder `json:"time_card_order"`
}
type OrderRefund struct {
	// skuId
	SkuID string `json:"sku_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 零售价（单位分）
	MarkingPrice int `json:"marking_price"`
	// 折后价（单位分）
	PayPrice int `json:"pay_price"`
	// sku实际退款数量
	TkCount int `json:"tk_count"`
	// 小计=零售价*数量（单位分）
	SubtotalAmount int `json:"subtotal_amount"`
	// 实收金额（单位分）
	PaymentTotal int `json:"payment_total"`
	// 退货类型：1为仅退款,2为退款退货
	RefundType int `json:"refund_type"`
	// 退款金额（单位分）
	RefundAmount int `json:"refund_amount"`
	// 退款原因
	RefundReason string `json:"refund_reason"`
	// 操作人
	Operator string `json:"operator"`
	// 退款方式
	RefundMethod string `json:"refund_method"`
	// 退款时间
	RefundTime string `json:"refund_time"`
}
type OrderProduct struct {
	// 商品ID
	Id int `json:"id"`
	// 商品ID
	ProductID string `json:"product_id"`
	// skuId
	SkuID string `json:"sku_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品图片
	Image string `json:"image"`
	// 商品类型
	ProductType int `json:"product_type"`
	// 规格
	Specs string `json:"specs"`
	// 条码
	BarCode string `json:"bar_code"`
	// 库位编码
	LocationCode string `json:"location_code"`
	// 零售价（单位分）
	MarkingPrice int `json:"marking_price"`
	// 折后价（单位分）
	PayPrice int `json:"pay_price"`
	// 小计=零售价*数量（单位分）
	SubtotalAmount int `json:"subtotal_amount"`
	// 购买数量
	Number int `json:"number"`
	// 实收金额（单位分）
	PaymentTotal int `json:"payment_total"`
	// 销售员
	EmployeeName string `json:"employee_name"`
	// 优惠列表(这里指单个商品参与的活动)
	PromotionList []PromotionInfo `json:"promotion_list"`
	// 退货状态：CANNOT_REFUND CAN_REFUND 可以退货
	RefundStatus string `json:"refund_status"`
	// 剩余可退数量
	RemainCount int `json:"remain_count"`
	// 剩余可退金额(单位分)
	RemainAmount int `json:"remain_amount"`
	// 购买类型：0 普通购买，1次卡，2赠品 100 买储值卡 101续储值卡 102买次卡 103续次卡
	BuyType int `json:"buy_type"`
	// 实际拣货数量
	PickedNumber int `json:"picked_number"`
	// 是否拣货,0否1是(该商品是否拣货)
	IsPicking int `json:"is_picking"`
	// 可用库存
	AvailableNum int `json:"available_num"`
	// 有效期 PERMANENT永久有效 VALID_DAYS办卡起多少天有效，FIXED_TIME指定时间内有效, DEADLINE:截至日期有效
	Validity string `json:"validity"`
	// 指定范围 UN_LIMIT 无限制,MONEY_LIMIT每次使用可抵扣多少元,DISCOUNT_LIMIT每次使用打几折 ALL:全部商品,SPEC_AVAILABLE:部分商品可用,SPEC_NOT_AVAILABLE:部分商品不可用
	DiscountRange string `json:"discount_range"`
}

type PromotionInfo struct {
	// 优惠活动id
	PromotionID int `json:"promotion_id"`
	// 优惠活动类型
	PromotionType int `json:"promotion_type"`
	// 活动名称
	PromotionTitle string `json:"promotion_title"`
	// 总优惠金额
	PromotionFee int `json:"promotion_fee"`
}

// CalcResponse 计算购物车返回结果
type CalcResponse struct {
	// 计算结果信息
	CalcInfo CalcInfo `json:"calc_info"`
	// 优惠详情列表
	DiscountDetailsList DiscountDetailsList `json:"discount_details_list"`
}

// DiscountDetailsList 优惠详情列表
type DiscountDetailsList struct {
	DiscountDetails []DiscountDetails `json:"discount_details"`
}

// DiscountDetails 优惠详情
type DiscountDetails struct {
	// 优惠类型
	DiscountType int `json:"discount_type"`
	// 商品SkuID
	SkuID string `json:"sku_id"`
	// 购买数量
	BuyCount int `json:"buy_count"`
	// 优惠名称
	DiscountName string `json:"discount_name"`
	// 优惠金额
	DiscountMoney string `json:"discount_money"`
}

// CalcInfo 计算信息
type CalcInfo struct {
	// 连锁ID
	ChainID string `json:"chain_id"`
	// 门店ID
	TenantID string `json:"tenant_id"`
	// 优惠金额(分)
	PromotionAmount int `json:"promotion_amount"`
	// 应付金额(分)
	SettlementAmount int `json:"settlement_amount"`
	// 合计金额(分)
	Amount int `json:"amount"`
	// 订单明细
	Details []OrderDetail `json:"details"`
	// 用户信息
	CustomerInfo CustomerInfo `json:"customer_info"`
	// 储值卡信息
	CalculateStoreCard StoreCardInfo `json:"calculate_store_card"`
	// 优惠券信息
	Coupons []CouponInfo `json:"coupons"`
	// 优惠信息
	DiscountInfo []DiscountInfo `json:"discount_info"`
	// 优惠汇总记录
	Promotions []order_po.OrderPromotion `json:"promotions"`
	// 商品优惠明细
	PromotionRecords []order_po.OrderPromotionRecord `json:"promotion_records"`
	// 渠道Id
	ChannelId int `json:"channelId"`
	//目的地坐标X 不传递或传递0，不计算运费
	DestinationX float64 `json:"destination_x"`
	//目的地坐标Y 不传递或传递0，不计算运费
	DestinationY float64 `json:"destination_y"`
	// 总运费金额实收,以分为单位
	Freight int `json:"freight"`
	// 总重量
	TotalWeight float64 `json:"total_weight"`
	//收件人
	ReceiverName string `json:"receiver_name"`
	//收件省
	ReceiverState string `json:"receiver_state"`
	//收件市
	ReceiverCity string `json:"receiver_city"`
	//收件区
	ReceiverDistrict string `json:"receiver_district"`
	//收件地址
	ReceiverAddress string `json:"receiver_address"`
	//收件电话
	ReceiverPhone string `json:"receiver_phone"`
}

// OrderDetail 订单明细
type OrderDetail struct {
	// 商品类别（1-实物商品，2-虚拟商品，3-组合商品,4-服务,5-活体,6-寄养)
	ProductType int `json:"product_type"`
	// 1 普通商品 2 赠品 3特价
	BuyType int `json:"buy_type"`
	// 购买数量
	TotalBuyCount int `json:"total_buy_count"`
	// 抵扣后支付数量
	DeductionBuyCount int `json:"deduction_buy_count"`
	// 次卡抵扣信息
	BuyTypeList []BuyTypeInfo `json:"buy_type_list"`
	// 折后价(分)
	DiscountPrice int `json:"discount_price"`
	// 折扣率
	DiscountRate float64 `json:"discount_rate"`
	// 是否改价
	IsEdit bool `json:"is_edit"`
	// 商品ID
	ProductID string `json:"product_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品图片
	ProductPic string `json:"product_pic"`
	// 零售价(分)
	RetailPrice int `json:"retail_price"`
	// skuId
	SkuID int `json:"sku_id"`
	// 小计(分)
	SubtotalAmount int `json:"subtotal_amount"`
	// 临时计算用的金额(分)
	ItemAmount int `json:"item_amount"`
	// 临时计算用小计(分)
	ItemSubtotalAmount int `json:"item_subtotal_amount"`
	// 员工ID
	EmployeeID string `json:"employee_id"`
	// 员工名称
	EmployeeName string `json:"employee_name"`
	// 特价活动ID
	SpecialActivityID string `json:"special_activity_id"`
	// 商品分类ID
	ProductCategoryID string `json:"product_category_id"`
	// 商品分类路径
	ProductCategoryPath string `json:"product_category_path"`
	// 行ID
	ItemID string `json:"item_id"`
	// 引用ID
	RefID int `json:"ref_id"`
	// 寄养ID
	JyID string `json:"jy_id"`
	// 是否寄养可以储值卡打折  0不打折，1打折
	IsCanDis int `json:"is_can_dis"`
}

// BuyTypeInfo 次卡抵扣信息
type BuyTypeInfo struct {
	// 次卡名称
	CardName string `json:"card_name"`
	// 次卡ID
	CardID string `json:"card_id"`
	// 次卡卡号
	CardNo string `json:"card_no"`
	// 次卡抵扣次数
	Num int `json:"num"`
	// 次卡剩余可用次数
	UnusedNum int `json:"unused_num"`
}

// CustomerInfo 用户信息
type CustomerInfo struct {
	// 用户ID
	ID string `json:"id"`
	// 微信OpenID
	WxOpenID string `json:"wx_open_id"`
	// 用户名称
	Name string `json:"name"`
	// 昵称
	NiceName string `json:"nice_name"`
	// 手机号
	Phone string `json:"phone"`
}

// StoreCardInfo 储值卡信息
type StoreCardInfo struct {
	// 储值卡ID
	CardID string `json:"card_id"`
	// 储值卡卡号
	CardNo string `json:"card_no"`
}

// CouponInfo 优惠券信息
type CouponInfo struct {
	// 优惠券ID
	CouponID string `json:"coupon_id"`
	// 优惠券Code
	Code string `json:"code"`
}

// DiscountInfo 优惠信息
type DiscountInfo struct {
	// 优惠类型 优惠活动类型:1改价,2特价,3储值卡,4满减,5优惠券,6次卡,7赠品,8储值卡,9抹零,10整单改价
	DiscountType string `json:"discount_type"`
	// 优惠名称
	DiscountName string `json:"discount_name"`
	// 优惠金额(分)
	DiscountMoney int `json:"discount_money"`
}

// 支付信息表
type OrderPayment struct {
	//支付类型 1:现金,2:余额,3:押金,4:标记收款,5:微信,6:支付宝,7:自有POS：8,挂账：9，10：储值卡'
	PayType int `json:"pay_type" xorm:"default 0 comment('支付方式:1:现金,2:余额,3:押金,4:标记收款,5:微信,6:支付宝,7:自有POS，8：挂账，10：储值卡 11:扫码支付 12：小程序微信') INT 'pay_type'"`
	//支付金额
	Amount int `json:"amount" xorm:"not null default 0 comment('支付金额') INT 'amount'"`
	//找零金额
	Change int `json:"change" xorm:"not null default 0 comment('找零金额') INT 'change'"`
	//实收金额
	Pay int `json:"pay" xorm:"not null default 0 comment('实收金额') INT 'pay'"`
	//支付交易号
	TradeNo string `json:"trade_no" xorm:"not null default '' comment('支付交易号') VARCHAR(64) 'trade_no'"`
	//储值卡号,次卡号,押金支付为0
	CardNo string `json:"card_no" xorm:"not null default '' comment('储值卡号,次卡号,押金支付为0') VARCHAR(100) 'card_no'"`
	// 付款码
	BarCode string `protobuf:"bytes,2,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
}

// OrderListReq 订单列表请求
type OrderListReq struct {
	PageIndex int `json:"page_index"` // 页码
	PageSize  int `json:"page_size"`  // 每页数量
	// 订单类型
	OrderType int `json:"order_type"`
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 订单状态
	OrderStatus int `json:"order_status"`
	// 组织ID
	OrgId int `json:"org_id"`
	// 用户ID
	ScrmUserId string `json:"scrm_user_id"`
}

// OrderListResp 订单列表响应
type OrderListResp struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Total   int64           `json:"total"`
	Data    []OrderDetailVo `json:"data"`
}

// OrderDetailVO 订单详情(包含商品)
type OrderDetailVo struct {
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 订单类型
	OrderType int `json:"order_type"`
	// 订单状态：0已取消,10(默认)未付款,20已付款,30已完成
	OrderStatus int `json:"order_status"`
	// 订单金额(分)
	Total int `json:"total"`
	// 支付时间
	PayTime string `json:"pay_time"`
	// 下单时间
	CreateTime string `json:"create_time"`
	// 所属门店
	ShopName string `json:"shop_name"`
	// 订单来源
	OrderSource string `json:"order_source"`
	// 支付方式
	PayMode int `json:"pay_mode"`
	// 订单商品列表
	Products []ProductItemVo `json:"products"`
}

// ProductItemVO 商品信息
type ProductItemVo struct {
	// 商品ID
	ProductId string `json:"product_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品类型
	ProductType int `json:"product_type"`
	// 商品图片
	Image string `json:"image"`
	// 商品SKU
	SkuId string `json:"sku_id"`
	// 数量
	Number int `json:"number"`
	// 实付单价
	PayPrice int `json:"pay_price"`
	// 实付总额
	PaymentTotal int `json:"payment_total"`
	//条码
	BarCode string `json:"bar_code"`
	// 第三方货号
	ThirdSkuId string `json:"third_sku_id"`
	// 订单状态
	OrderStatus int `json:"order_status"`
	// 订单类型
	OrderType int `json:"order_type"`
}

// AssignCommissionRequest 手动分配业绩请求
type AssignCommissionRequest struct {
	// 订单号
	OrderSn string `json:"order_sn" validate:"required" label:"订单号"`
	// 员工ID
	StaffID string `json:"staff_id" validate:"required" label:"员工ID"`
	// 员工名称
	StaffName string `json:"staff_name" validate:"required" label:"员工名称"`
	// 操作人ID
	OperatorId string `json:"operator_id" validate:"required" label:"操作人ID"`
	// 操作人名称
	OperatorName string `json:"operator_name" validate:"required" label:"操作人名称"`
}

// AssignCommissionResponse 手动分配业绩响应
type AssignCommissionResponse struct {
	response.BaseResp
}
