package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	"testing"
)

func TestSmsService_NotifyExpireCoupons(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	tests := []struct {
		name    string
		field   fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			field: fields{
				BaseService: common.BaseService{},
			},
			wantErr: false,
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SmsService{
				BaseService: tt.field.BaseService,
			}
			if err := s.NotifyExpireCoupons(); (err != nil) != tt.wantErr {
				t.Errorf("SmsService.NotifyExpireCoupons() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSmsService_NotifyExpireCards(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		typeCard int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "储值卡过期短信"},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SmsService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.NotifyExpireCards(2); (err != nil) != tt.wantErr {
				t.Errorf("NotifyExpireCards() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
