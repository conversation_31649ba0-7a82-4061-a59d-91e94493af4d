package services

import (
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/proto/dac"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"os"
	"strings"
	"xorm.io/xorm"
)

type AuthTokenReq struct {
	FinanceCode            string `protobuf:"bytes,1,opt,name=finance_code,json=financeCode,proto3" json:"finance_code"`
	ComponentAppid         string `protobuf:"bytes,2,opt,name=component_appid,json=componentAppid,proto3" json:"component_appid"`
	AuthorizerAppid        string `protobuf:"bytes,3,opt,name=authorizer_appid,json=authorizerAppid,proto3" json:"authorizer_appid"`
	AuthorizerRefreshToken string `protobuf:"bytes,4,opt,name=authorizer_refresh_token,json=authorizerRefreshToken,proto3" json:"authorizer_refresh_token"`
	ChainId                string `json:"chain_id"`
}

type AuthTokenRes struct {
	AuthorizerAccessToken  string `protobuf:"bytes,1,opt,name=authorizer_access_token,json=authorizerAccessToken,proto3" json:"authorizer_access_token"`
	ExpiresIn              int64  `protobuf:"varint,2,opt,name=expires_in,json=expiresIn,proto3" json:"expires_in"`
	AuthorizerRefreshToken string `protobuf:"bytes,3,opt,name=authorizer_refresh_token,json=authorizerRefreshToken,proto3" json:"authorizer_refresh_token"`
}
type ComponentTokenReq struct {
	ComponentAppid        string `protobuf:"bytes,1,opt,name=component_appid,json=componentAppid,proto3" json:"component_appid"`
	ComponentAppsecret    string `protobuf:"bytes,2,opt,name=component_appsecret,json=componentAppsecret,proto3" json:"component_appsecret"`
	ComponentVerifyTicket string `protobuf:"bytes,3,opt,name=component_verify_ticket,json=componentVerifyTicket,proto3" json:"component_verify_ticket"`
}
type ComponentTokenRes struct {
	ComponentAccessToken string `protobuf:"bytes,1,opt,name=component_access_token,json=componentAccessToken,proto3" json:"component_access_token"`
	ExpiresIn            int64  `protobuf:"varint,2,opt,name=expires_in,json=expiresIn,proto3" json:"expires_in"`
}

type WxacodeErrResponse struct {
	Errcode int32  //	错误码
	Errmsg  string //	错误信息
}

type WxacodeRequest struct {
	Access_token string `json:"access_token,omitempty"` //	是	接口调用凭证
	Scene        string `json:"scene,omitempty"`        //	是	最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
	CheckPath    bool   `json:"check_path"`
	EnvVersion   string `json:"env_version"`
	Page         string `json:"page,omitempty"`       //主页	否	必须是已经发布的小程序存在的页面（否则报错），例如 pages/index/index, 根路径前不要填加 /,不能携带参数（参数请放在scene字段里），如果不填写这个字段，默认跳主页面
	Width        int32  `json:"width,omitempty"`      //430	否	二维码的宽度，单位 px，最小 280px，最大 1280px
	Auto_color   bool   `json:"auto_color,omitempty"` //	false	否	自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false
	Line_color   string `json:"line_color,omitempty"` //{"r":0,"g":0,"b":0}	否	auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"} 十进制表示
	Is_hyaline   bool   `json:"is_hyaline,omitempty"` //false	否	是否需要透明底色，为 true 时，生成透明底色的小程序
}

// 获取授权方的token
func GetAuthToken(engine *xorm.Engine, params *AuthTokenReq) (out *AuthTokenRes, err error) {
	logPrefix := fmt.Sprintf("eshop获取授权方的token,入参：%s", utils.JsonEncode(params))
	var exists bool
	log.Info(logPrefix)
	out = new(AuthTokenRes)
	//redisConn8 := cache.GetRedisConn8()
	appid := ""                  //授权方appid
	authorizerRefreshToken := "" //授权方刷新token

	// 第一步： 获取店铺的appid
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if env == "staging" || env == "uat" {
		//appid = "wx443c57d6371fa136" //好兽医
		appid = "wx8a769978c9217af6" //枫叶
	} else {
		exists, err = engine.Table("eshop_saas.c_client_info").Select("app_id").Where("chain_id=?", cast.ToInt64(params.ChainId)).Get(&appid)
		if !exists {
			log.Error(logPrefix, "未找到线下门店微信小程序appid")
			return out, errors.New("未找到线下门店微信小程序appid")
		}
		if err != nil {
			log.Error(logPrefix, "查找线下门店微信小程序appid失败，err=", err.Error())
			err = errors.New("查找线下门店微信小程序appid失败" + err.Error())
			return
		}
	}

	// 第二步： 从redis里获取授权方的accessToken
	//redisKey := fmt.Sprintf("eshop_wechat_%s_authorizer_access_token", appid)
	//authorizerAccessToken := redisConn8.Get(redisKey).Val()
	//log.Info(logPrefix, "店铺的appid为", appid, "从redis里获取授权方的accessToken为", authorizerAccessToken)
	//if len(authorizerAccessToken) > 0 {
	//	out.AuthorizerAccessToken = authorizerAccessToken
	//	return
	//}

	// 第三步： 如果redis里没有授权方的accessToken， 则需要从微信端获取， 但是获取之前， 先获取第三方平台的accessToken
	exists, err = engine.Table("eshop_saas.c_applet_info").Select("authorizer_refresh_token").Where("app_id=?", appid).Get(&authorizerRefreshToken)
	if !exists {
		log.Error(logPrefix, "未找到线下门店微信小程序刷新令牌")
		err = errors.New("未找到线下门店微信小程序刷新令牌")
		return
	}
	if err != nil {
		log.Error(logPrefix, "查找线下门店微信小程序刷新令牌失败，err=", err.Error())
		err = errors.New("查找线下门店微信小程序刷新令牌失败" + err.Error())
		return
	}

	params.ComponentAppid = config.GetString("eshop_wechat_third_appid")
	params.AuthorizerAppid = appid
	params.AuthorizerRefreshToken = authorizerRefreshToken

	ComponentTokenResult, err := GetComponentToken(engine, &ComponentTokenReq{})
	if err != nil {
		log.Error(logPrefix, "获取第三方平台token失败,err=", err.Error())
		err = errors.New("获取第三方平台token失败" + err.Error())
		return
	}

	codeUrl := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=%s", ComponentTokenResult.ComponentAccessToken)
	codeByte, _ := json.Marshal(params)
	log.Infof("%s获取授权方的token,componentAccessToken=%s,入参:%s", logPrefix, ComponentTokenResult.ComponentAccessToken, string(codeByte))
	byteBuffer, _ := utils.HttpPost(codeUrl, codeByte, "")
	log.Infof("%s获取授权方的token,数据返回：%s", logPrefix, string(byteBuffer))
	var wxacodeErrResponse WxacodeErrResponse
	err = json.Unmarshal(byteBuffer, &wxacodeErrResponse)
	log.Info(logPrefix, "wxacodeErrResponse=", utils.JsonEncode(wxacodeErrResponse), ",err=", utils.JsonEncode(err))
	if err == nil && wxacodeErrResponse.Errcode != 0 {
		err = errors.New("获取授权方的token失败" + wxacodeErrResponse.Errmsg)
		return
	}

	err = json.Unmarshal(byteBuffer, out)
	log.Info(logPrefix, "out=", utils.JsonEncode(out), ",err=", utils.JsonEncode(err))
	if err != nil {
		log.Error(logPrefix, "解析结果出错,err", err.Error())
		return
	}
	if len(out.AuthorizerAccessToken) == 0 {
		err = errors.New("获取授权方的token失败,值为空")
		return
	}

	// 缓存第三方平台的token 为1小时50分钟
	//redisConn8.SetNX(redisKey, out.AuthorizerAccessToken, 110*time.Minute)
	return

}

// 获取第三方平台的component_access_token
func GetComponentToken(engine *xorm.Engine, params *ComponentTokenReq) (out *ComponentTokenRes, err error) {
	logPrefix := "eshop获取第三方平台的token===="
	out = new(ComponentTokenRes)
	redisConn8 := cache.GetRedisConn8()
	//redisKey := "eshop_wechat_third_component_access_token"
	//componentAccessToken := redisConn8.Get(redisKey).Val()
	//log.Info(logPrefix, "从redis获取到第三方平台的token为", componentAccessToken)
	//if len(componentAccessToken) > 0 {
	//	out.ComponentAccessToken = componentAccessToken
	//	return
	//}
	params.ComponentAppid = config.GetString("eshop_wechat_third_appid")
	params.ComponentAppsecret = config.GetString("eshop_wechat_third_appsecret")
	ticketRedisKey := "pets:client:wx_open:wechat_component_verify_ticket:" + params.ComponentAppid
	params.ComponentVerifyTicket = redisConn8.Get(ticketRedisKey).Val()
	log.Info(logPrefix, "获取第三方平台的component_access_token，入参:", utils.JsonEncode(params))
	if len(params.ComponentAppid) == 0 || len(params.ComponentAppsecret) == 0 || len(params.ComponentVerifyTicket) == 0 {
		err = errors.New("获取第三方平台的component_access_token入参无效")
		return
	}
	codeUrl := "https://api.weixin.qq.com/cgi-bin/component/api_component_token"
	codeByte, _ := json.Marshal(params)
	log.Infof("%s获取第三方平台的component_access_token,请求地址：%s,入参：%s", logPrefix, codeUrl, string(codeByte))
	byteBuffer, _ := utils.HttpPost(codeUrl, codeByte, "")
	log.Infof("%s获取第三方平台的component_access_token,数据返回：%s", logPrefix, string(byteBuffer))
	var wxacodeErrResponse WxacodeErrResponse
	err = json.Unmarshal(byteBuffer, &wxacodeErrResponse)
	if err == nil && wxacodeErrResponse.Errcode != 0 {
		err = errors.New("获取第三方平台的component_access_token失败" + wxacodeErrResponse.Errmsg)
		return
	}
	err = json.Unmarshal(byteBuffer, out)
	if err != nil {
		log.Error(logPrefix, "解析结果出错:", err.Error())
		return
	}
	if len(out.ComponentAccessToken) == 0 {
		err = errors.New("获取第三方平台的component_access_token失败,值为空")
		return
	}

	// 缓存第三方平台的token 为1小时50分钟
	//redisConn8.SetNX(redisKey, out.ComponentAccessToken, 110*time.Minute)
	return

}

// 获取店铺二维码
func GetShopWxAppCode(engine *xorm.Engine, params *dac.GetShopWxAppCodeRequest) (QiniuUrl string, err error) {
	logPrefix := fmt.Sprintf("eshop获取店铺小程序码,入参:%s", utils.JsonEncode(params))
	log.Info(logPrefix)

	if len(params.FinanceCode) == 0 {
		err = errors.New("财务编码不能为空")
		return
	}
	// 微信小程序的Accesstoken为空
	if len(params.AccessToken) == 0 {
		AuthTokenResult := new(AuthTokenRes)
		AuthTokenResult, err = GetAuthToken(engine, &AuthTokenReq{FinanceCode: params.FinanceCode, ChainId: params.ChainId})
		log.Info(logPrefix, "获取授权方accessToken,返回值为", utils.JsonEncode(AuthTokenResult), ",错误为", utils.JsonEncode(err))
		if err != nil {
			log.Error(logPrefix, "获取授权方token失败，错误为", utils.JsonEncode(err))
			return
		}
		params.AccessToken = AuthTokenResult.AuthorizerAccessToken
	}

	//生成二维码
	env := strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	log.Info(logPrefix, "环境变量为", env)
	var wxacode = WxacodeRequest{
		Scene:      params.FinanceCode,
		Page:       "pages/home/<USER>",
		CheckPath:  true,
		EnvVersion: "release",
	}
	log.Info(logPrefix, "环境变量值为", env)
	if env == "staging" || env == "uat" {
		wxacode.EnvVersion = "trial"
		wxacode.CheckPath = false
	}

	codeUrl := fmt.Sprintf("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s", params.AccessToken)
	codeByte, _ := json.Marshal(wxacode)
	log.Info(logPrefix, "生成小程序二维码,请求AccessToken为", params.AccessToken, ",参数为", string(codeByte))
	byteBuffer, err := utils.HttpPost(codeUrl, codeByte, "")
	log.Info(logPrefix, "生成小程序二维码,返回数据为", utils.JsonEncode(byteBuffer), ",err为", utils.JsonEncode(err))
	var wxacodeErrResponse WxacodeErrResponse
	codeErr := json.Unmarshal(byteBuffer, &wxacodeErrResponse)
	if codeErr == nil {
		log.Error(logPrefix, "wxacodeErrResponse=", utils.JsonEncode(wxacodeErrResponse))
		err = errors.New(wxacodeErrResponse.Errmsg)
		return
	}
	QiniuUrl, err = utils.UploadToQiNiu(byteBuffer)
	log.Info(logPrefix, "上传七牛云,QiniuUrl=", QiniuUrl, ",err=", utils.JsonEncode(err))
	if err != nil {
		log.Error(logPrefix, "上传七牛云失败,err=", err.Error())
		err = errors.New("上传七牛云失败" + err.Error())
		return
	}

	return
}
