package controllers

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/order-service/services"
	vo "eShop/view-model/order-vo"
	"net/http"

	"github.com/go-chi/chi/v5"
)

type RefundOrderController struct {
	service services.RefundOrderService
}

func NewRefundOrderController(service services.RefundOrderService) *RefundOrderController {
	return &RefundOrderController{
		service: service,
	}
}

func (c *RefundOrderController) RegisterRoutes(r chi.Router) {
	r.Route("/order-app/manager/refund", func(r chi.Router) {
		r.Post("/apply", c.ApplyRefund)        // 申请退款
		r.Post("/return", c.ProcessReturn)     // 处理退货
		r.Get("/return/list", c.GetReturnList) // 退货记录列表
	})
}

// ApplyRefund 申请退款
// @Summary 申请退款
// @Description 申请退款
// @Tags 订单退款
// @Accept json
// @Produce json
// @Param command body vo.RefundRequest true "退款请求参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /order-app/manager/refund/apply [post]
func (c *RefundOrderController) ApplyRefund(w http.ResponseWriter, r *http.Request) {
	// 1. 参数绑定
	cmd, err := utils.Bind[vo.RefundRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	// 2. 获取JWT信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	// 3. 调用service
	err = c.service.ApplyRefund(cmd, jwtInfo)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 4. 返回结果
	response.Success(w)
}

// ProcessReturn 处理退货
// @Summary 处理退货
// @Description B端-处理退货
// @Tags 订单退款
// @Accept json
// @Produce json
// @Param command body vo.ProcessReturnRequest true "处理退货请求参数"
// @Success 200 {object} response.BaseResp "成功"
// @Router /order-app/manager/refund/return [post]
func (c *RefundOrderController) ProcessReturn(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.ProcessReturnRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	err = c.service.ProcessReturn(cmd, jwtInfo)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// GetReturnList 获取退货记录列表
// @Summary 获取退货记录列表
// @Description B端-获取退货记录列表
// @Tags 订单退款
// @Accept json
// @Produce json
// @Param request query vo.RefundReturnListRequest true "查询参数"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Success 200 {object} vo.RefundReturnListResponse
// @Router /order-app/manager/refund/return/list [get]
func (c *RefundOrderController) GetReturnList(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.RefundReturnListRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	// 获取JWT信息
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 设置店铺ID
	req.ShopId = jwtInfo.TenantId

	data, total, err := c.service.GetRefundReturnList(req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, data, int(total))
}
