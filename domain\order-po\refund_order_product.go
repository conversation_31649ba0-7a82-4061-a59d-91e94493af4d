package order_po

import (
	"eShop/infra/log"

	"xorm.io/xorm"
)

type RefundOrderProduct struct {
	Id               int     `json:"id" xorm:"pk autoincr not null comment('自增主键') INT 'id'"`
	RefundSn         string  `json:"refund_sn" xorm:"default 'null' comment('退款单号') VARCHAR(50) 'refund_sn'"`
	SkuId            string  `json:"sku_id" xorm:"not null default '' comment('商品skuid') VARCHAR(20) 'sku_id'"`
	ParentSkuId      string  `json:"parent_sku_id" xorm:"not null default '' comment('组合商品父级skuid') VARCHAR(50) 'parent_sku_id'"`
	OrderProductId   int     `json:"order_product_id" xorm:"not null default 0 comment('订单商品表主键id') INT 'order_product_id'"`
	ProductName      string  `json:"product_name" xorm:"not null default '' comment('商品名称') VARCHAR(200) 'product_name'"`
	ProductType      int     `json:"product_type" xorm:"not null default 1 comment('商品类别（1-实物商品，2-虚拟商品，3-组合商品）4-服务 5-活体 6-寄养') TINYINT 'product_type'"`
	ProductPrice     int     `json:"product_price" xorm:"not null default 0 comment('商品原优惠单价') INT 'product_price'"`
	MarkingPrice     int     `json:"marking_price" xorm:"not null default 0 comment('商品原单价') INT 'marking_price'"`
	Quantity         int     `json:"quantity" xorm:"not null default 0 comment('sku申请退款数量') INT 'quantity'"`
	Tkcount          int     `json:"tkcount" xorm:"not null default 0 comment('sku实际退款数量') INT 'tkcount'"`
	RefundAmount     string  `json:"refund_amount" xorm:"not null default '' comment('sku总退款金额，单位元') VARCHAR(20) 'refund_amount'"`
	RefundPrice      int     `json:"refund_price" xorm:"not null default 0 comment('sku单件退款金额') INT 'refund_price'"`
	Itemcode         string  `json:"itemcode" xorm:"default 'null' comment('管易专用 商品代码') VARCHAR(50) 'itemcode'"`
	Skucode          string  `json:"skucode" xorm:"default 'null' comment('管易专用 带规格的商品此字段必填') VARCHAR(50) 'skucode'"`
	OcId             string  `json:"oc_id" xorm:"default 'null' comment('订单明细Id (全渠道需要)') VARCHAR(50) 'oc_id'"`
	Barcode          string  `json:"barcode" xorm:"default 'null' comment('管易专用 商品条码') VARCHAR(50) 'barcode'"`
	Spec             string  `json:"spec" xorm:"default 'null' comment('商品sku的规格名称') VARCHAR(200) 'spec'"`
	BoxPrice         int     `json:"box_price" xorm:"default 'null' comment('当前商品sku需使用包装盒的单价，即单个包装盒的价格，单位是元。') INT 'box_price'"`
	BoxNum           float64 `json:"box_num" xorm:"default 'null' comment('单件商品sku需使用的包装盒数量，同商家同步商品时维护的此相同字段的信息。') FLOAT(10) 'box_num'"`
	SubBizOrderId    string  `json:"sub_biz_order_id" xorm:"default 'null' comment('饿了么子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段') VARCHAR(200) 'sub_biz_order_id'"`
	VerifyCodes      string  `json:"verify_codes" xorm:"comment('退款核销码，多个以英文逗号相隔') TEXT 'verify_codes'"`
	CreateTime       string  `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime       string  `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	DelRefundorderid string  `json:"del_refundorderid" xorm:"not null default '' comment('退款主表Id') VARCHAR(36) 'del_refundorderid'"`
	DelMtorder       string  `json:"del_mtorder" xorm:"default 'null' comment('渠道订单号，同old_order_sn') VARCHAR(50) 'del_mtorder'"`
	DelId            string  `json:"del_id" xorm:"not null default '' comment('原主键，待删除') VARCHAR(50) 'del_id'"`
	ActualReturnNum  int     `json:"actual_return_num" xorm:"not null default 0 comment('实际退回数量') INT"`
	Remark           string  `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(255)"`
	BuyType          int     `json:"buy_type" xorm:"default 0 comment('购买类型：0 普通购买，1次卡，2赠品') INT 'buy_type'"`
}

func (r *RefundOrderProduct) TableName() string {
	return "dc_order.refund_order_product"
}

type RefundOrderProductRequest struct {
	Ids       []int    `json:"ids"`        // 退款商品id
	RefundSn  string   `json:"refund_sn"`  // 退款单号
	RefundSns []string `json:"refund_sns"` // 退款单号列表
	Fields    string   `json:"fields"`     // 查询字段
	StoreId   string   `json:"store_id"`   // 店铺id
}

func (r *RefundOrderProduct) GetRefundOrderProduct(session *xorm.Session, req RefundOrderProductRequest) (out []*RefundOrderProduct, out1 map[string][]*RefundOrderProduct, out2 map[int][]*RefundOrderProduct, err error) {
	out = make([]*RefundOrderProduct, 0)

	if req.Fields == "" {
		req.Fields = "*"
	}
	session = session.Table("dc_order.refund_order_product").Select(req.Fields)
	if req.RefundSn != "" {
		session = session.Where("refund_sn = ?", req.RefundSn)
	}
	if len(req.RefundSns) > 0 {
		session = session.In("refund_sn", req.RefundSns)
	}

	err = session.OrderBy("id asc").Find(&out)

	if err != nil {
		return nil, nil, nil, err
	}

	out1 = make(map[string][]*RefundOrderProduct)
	out2 = make(map[int][]*RefundOrderProduct)

	for _, product := range out {
		out1[product.RefundSn] = append(out1[product.RefundSn], product)
		out2[product.OrderProductId] = append(out2[product.OrderProductId], product)
	}
	return out, out1, out2, nil
}

// BatchCreate 批量创建退款商品记录
func (r *RefundOrderProduct) BatchCreate(session *xorm.Session, records []*RefundOrderProduct) error {
	if len(records) == 0 {
		return nil
	}

	// 使用xorm的批量插入
	_, err := session.Insert(records)
	if err != nil {
		log.Errorf("批量创建退款商品记录失败, err: %v", err)
		return err
	}
	return nil
}

// GetRefundRecords 获取退款记录
func (r *RefundOrderProduct) GetRefundRecords(session *xorm.Session, orderSn string) ([]*RefundOrderProduct, error) {
	records := make([]*RefundOrderProduct, 0)
	err := session.Table("dc_order.refund_order_product").Alias("rop").
		Join("left", "dc_order.refund_order ro", "rop.refund_sn = ro.refund_sn").
		Where("ro.order_sn = ? AND ro.refund_state = ?", orderSn, RefundStateSuccess). // 3表示退款成功
		Find(&records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// ReturnProductUpdate 退货商品更新参数
type ReturnProductUpdate struct {
	// refund_order_product表主键id
	Id int `json:"id"`
	// 实际退回数量
	ActualReturnNum int `json:"actual_return_num"`
	// 备注
	Remark string `json:"remark"`
}

// BatchUpdateReturnNum 批量更新退款商品的实际退回数量和处理时间
func (r *RefundOrderProduct) BatchUpdateReturnNum(session *xorm.Session, products []*ReturnProductUpdate) error {
	for _, item := range products {
		_, err := session.Table(r.TableName()).
			Where("id = ?", item.Id).
			Update(map[string]interface{}{
				"actual_return_num": item.ActualReturnNum,
				"remark":            item.Remark,
			})
		if err != nil {
			return err
		}
	}
	return nil
}

// RefundOrderProductDetail 扩展结构体，包含关联查询字段
type RefundOrderProductDetail struct {
	RefundOrderProduct `xorm:"extends"`
	// 规格
	Specs string `json:"specs" xorm:"specs"`
	// 库位
	LocationCode string `json:"location_code" xorm:"code"`
	// 图片
	Image string `json:"image" xorm:"image"`
	// 原价
	MarkingPrice int `json:"marking_price" xorm:"marking_price"`
	// 条形码
	BarCode string `json:"bar_code" xorm:"bar_code"`
}

func (r *RefundOrderProduct) GetRefundOrderProductDetail(session *xorm.Session, req RefundOrderProductRequest) (out []*RefundOrderProductDetail, err error) {
	out = make([]*RefundOrderProductDetail, 0)

	if req.Fields == "" {
		req.Fields = "rp.*,op.specs,op.image,l.code,op.marking_price,op.bar_code"
	}

	session = session.Table("dc_order.refund_order_product").Alias("rp").
		Select(req.Fields).
		Join("LEFT", "dc_order.order_product op", "rp.order_product_id = op.id").
		Join("LEFT", "eshop.inventory_location l", "rp.sku_id = l.sku_id AND l.store_id = ?", req.StoreId)

	if req.RefundSn != "" {
		session = session.Where("rp.refund_sn = ?", req.RefundSn)
	}
	if len(req.RefundSns) > 0 {
		session = session.In("rp.refund_sn", req.RefundSns)
	}
	if len(req.Ids) > 0 {
		session = session.In("rp.id", req.Ids)
	}

	err = session.OrderBy("rp.id asc").Find(&out)
	if err != nil {
		return nil, err
	}

	return out, nil
}
