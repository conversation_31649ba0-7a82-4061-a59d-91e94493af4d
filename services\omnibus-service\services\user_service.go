package services

import (
	offline2 "eShop/domain/external-po/offline"
	vo "eShop/view-model/omnibus-vo"
	"net/http"
	"time"

	"github.com/spf13/cast"

	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"

	"github.com/dgrijalva/jwt-go"
	micjwt "github.com/limitedlee/microservice/common/jwt"
)

type UserService struct {
	common.BaseService
}

func (s *UserService) GetToken(r *http.Request) (out *vo.SessionInfo, err error) {
	s.<PERSON>()
	defer s.Close()
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息,解析失败: err", err.Error())
		return nil, err
	}
	orgId := cast.ToInt(r.Header.Get("org_id"))
	//根据UserId、CustomerId判断来源是小程序还是后台，获取不同表的用户信息
	if jwtInfo.UserId != "" && jwtInfo.UserId != "0" { //eshop_saas.t_employee、eshop_saas.a_user 员工
		var userInfo offline2.User
		if _, err = s.Engine.Table("eshop_saas.a_user").
			Where("id=?", jwtInfo.UserId).Get(&userInfo); err != nil {
			log.Error("获取登录信息,失败: err", err.Error())
			return nil, err
		}
		return &vo.SessionInfo{
			UserId:    jwtInfo.UserId,
			OpenId:    userInfo.WxOpenId,
			UnionId:   userInfo.WxUnionId,
			TenantId:  jwtInfo.TenantId,
			ChainId:   jwtInfo.ChainId,
			TenantIds: jwtInfo.TenantIds,
			//SourceChainId: jwtInfo.SourceChainId,
			Token: s.generateJwtToken(userInfo.Id, userInfo.Mobile, userInfo.WxUnionId, userInfo.WxOpenId, userInfo.NickName, jwtInfo.TenantId, jwtInfo.TenantIds, jwtInfo.ChainId, jwtInfo.SourceChainId, orgId),
		}, nil
	} else if jwtInfo.CustomerId != "" && jwtInfo.CustomerId != "0" { //c端用户+小程序
		var customerInfo offline2.CustomerInfo
		if _, err = s.Engine.Table("eshop_saas.c_customer_info").
			Where("id=?", jwtInfo.CustomerId).Get(&customerInfo); err != nil {
			log.Error("获取登录信息,失败: err", err.Error())
			return nil, err
		}
		return &vo.SessionInfo{
			UserId:    jwtInfo.CustomerId,
			OpenId:    customerInfo.WxOpenId,
			UnionId:   customerInfo.UnionId,
			TenantId:  jwtInfo.TenantId,
			ChainId:   jwtInfo.ChainId,
			TenantIds: jwtInfo.TenantIds,
			//SourceChainId: jwtInfo.SourceChainId,
			Token: s.generateJwtToken(jwtInfo.CustomerId, customerInfo.Phone, customerInfo.UnionId, customerInfo.WxOpenId, customerInfo.Name, customerInfo.TenantId, jwtInfo.TenantIds, customerInfo.ChainId, jwtInfo.SourceChainId, orgId),
		}, nil
	}

	return
}

// 生成jwt token
func (s *UserService) generateJwtToken(Id, Mobile, UnionId, WxOpenId, Name, TenantId, TenantIds, ChainId, SourceChainId string, orgId int) string {
	claims := make(jwt.MapClaims)
	claims["org_id"] = orgId
	claims["scrmid"] = Id
	claims["unionid"] = UnionId
	claims["openid"] = WxOpenId
	claims["name"] = Name
	claims["TenantId"] = TenantId
	claims["TenantIds"] = TenantIds
	claims["ChainId"] = ChainId
	claims["SourceChainId"] = SourceChainId
	claims["mobile"] = Mobile
	claims["exp"] = time.Now().Add(time.Hour * 24).Unix()
	claims["role_type"] = 0
	if SourceChainId != "0" {
		if ChainId == SourceChainId {
			claims["role_type"] = 1
		} else {
			claims["role_type"] = 2
		}
	}
	//查询upetmart.upet_member.member_id
	var memberId int
	if _, err := s.Engine.Table("upetmart.upet_member").Cols("member_id").Where("member_mobile=?", Mobile).Get(&memberId); err != nil {
		log.Errorf("GetToken/generateJwtToken 查询upetmart.upet_member.member_id失败,参数:%s,err:%+v", utils.JsonEncode(claims), err)
		return ""
	}
	claims["member_id"] = memberId
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	//需要添加私钥进行加密
	tokenStr, err := token.SignedString(micjwt.PrivateKey)
	if err != nil {
		log.Errorf("GetToken/generateJwtToken 生成签名异常,参数:%s,私钥:%v,err:%+v", utils.JsonEncode(claims), micjwt.PrivateKey, err)
		return ""
	}
	return tokenStr
}
