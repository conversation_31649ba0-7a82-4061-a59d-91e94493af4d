package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/inventory-service/supplier"
	vo "eShop/view-model/inventory-vo/supplier"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
)

type SupplierController struct {
	service *supplier.SupplierService
}

func NewSupplierController() *SupplierController {
	return &SupplierController{
		service: supplier.NewService(),
	}
}

// RegisterRoutes 注册路由
func (c *SupplierController) RegisterRoutes(r chi.Router) {
	r.Route("/inventory-app/suppliers", func(r chi.Router) {
		r.Post("/create", c.Create)
		r.Post("/update", c.Update)
		r.Get("/page", c.Query)
		r.Get("/get-by-id", c.Get)
		r.Post("/enable", c.Enable)   // 启用供应商
		r.Post("/disable", c.Disable) // 禁用供应商
	})
}

// Create 创建供应商 ✅
// @Summary 创建供应商
// @Description 创建新的供应商
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param body body vo.CreateCommand true "创建供应商请求参数"
// @Success 201 {object} response.BaseResp "成功创建供应商"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Failure 500 {object} response.BaseResp "服务器内部错误"
// @Router /inventory-app/suppliers/create [post]
func (c *SupplierController) Create(w http.ResponseWriter, r *http.Request) {
	var cmd vo.CreateCommand
	cmd, err := utils.Bind[vo.CreateCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	err = c.service.Create(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Update 更新供应商✅
// @Summary 更新供应商
// @Description 更新指定ID的供应商信息
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param id path int true "供应商ID"
// @Param body body vo.UpdateCommand true "更新供应商请求参数"
// @Success 200 {object} response.BaseResp "成功更新供应商"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Failure 500 {object} response.BaseResp "服务器内部错误"
// @Router /inventory-app/suppliers/update [post]
func (c *SupplierController) Update(w http.ResponseWriter, r *http.Request) {
	var cmd vo.UpdateCommand
	cmd, err := utils.Bind[vo.UpdateCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	err = c.service.Update(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Query 查询供应商列表 ✅
// @Summary 查询供应商列表
// @Description 根据条件查询供应商列表
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param params query vo.QueryParams true "查询参数"
// @Success 200 {object} response.Response[vo.Supplier] "成功获取供应商列表"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Failure 500 {object} response.BaseResp "服务器内部错误"
// @Router /inventory-app/suppliers/page [get]
func (c *SupplierController) Query(w http.ResponseWriter, r *http.Request) {
	var params vo.QueryParams
	params, err := utils.Bind[vo.QueryParams](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	result, total, err := c.service.Query(r.Context(), params)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, result, total)
}

// Get 获取供应商 ✅
// @Summary 获取供应商详情
// @Description 根据ID获取供应商详细信息
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param id path int true "供应商ID"
// @Success 200 {object} response.BaseResp "成功获取供应商信息"
// @Failure 404 {object} response.BaseResp "供应商不存在"
// @Failure 500 {object} response.BaseResp "服务器内部错误"
// @Router /inventory-app/suppliers/get-by-id [get]
func (c *SupplierController) Get(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	result, err := c.service.Get(r.Context(), id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, result)
}

// Enable 启用供应商 ✅
// @Summary 启用供应商
// @Description 启用指定ID的供应商
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param body body vo.EnableCommand true "启用供应商请求参数"
// @Success 200 {object} response.BaseResp "成功启用供应商"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Failure 500 {object} response.BaseResp "服务器内部错误"
// @Router /inventory-app/suppliers/enable [post]
func (c *SupplierController) Enable(w http.ResponseWriter, r *http.Request) {

	var cmd vo.EnableCommand
	cmd, err := utils.Bind[vo.EnableCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	err = c.service.Enable(r.Context(), cmd.ID)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Disable 禁用供应商 ✅
// @Summary 禁用供应商
// @Description 禁用指定ID的供应商
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param body body vo.DisableCommand true "禁用供应商请求参数"
// @Success 200 {object} response.BaseResp "成功禁用供应商"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Failure 500 {object} response.BaseResp "服务器内部错误"
// @Router /inventory-app/suppliers/disable [post]
func (c *SupplierController) Disable(w http.ResponseWriter, r *http.Request) {

	var cmd vo.DisableCommand
	cmd, err := utils.Bind[vo.DisableCommand](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	err = c.service.Disable(r.Context(), cmd.ID)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}
