package inventory

type PageRequest struct {
	Current int `json:"current"` // 当前页码
	Size    int `json:"size"`    // 每页大小
}

type TimeQueryRequest struct {
	TimeStart string `json:"time_st"` // 开始时间
	TimeEnd   string `json:"time_ed"` // 结束时间
}

type SortRequest struct {
	Sort  string `json:"sort"`  // 排序字段
	Order string `json:"order"` // 排序顺序
}

type IdRequest struct {
	Id int `json:"id"` // 主键
}

type IdsRequest struct {
	Ids []int `json:"ids"` // 主键
}
