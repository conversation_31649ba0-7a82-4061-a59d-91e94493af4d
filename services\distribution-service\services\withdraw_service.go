package services

import (
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	diswithdraw "eShop/services/distribution-service/enum/dis-withdraw"
	diswithdrawrecord "eShop/services/distribution-service/enum/dis-withdraw-record"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"runtime"
	"time"

	"github.com/spf13/cast"
)

type DisWithdrawService struct {
	common.BaseService
}

func (h *DisWithdrawService) GetDisWithdrawList(in vo.GetDisWithdrawListReq) (out []vo.DisWithdrawView, total int, err error) {

	h.Begin()
	defer h.Close()

	// 将子上下文传入Session
	session := h.Session

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	session.Table("dis_withdraw").Alias("a").
		Join("left", "shop b", "b.id = a.shop_id").
		Join("left", "scrm_enterprise d", "d.id=b.enterprise_id").
		Join("left", "dis_enterprise de", "de.scrm_enterprise_id = b.enterprise_id and de.org_id = ?", in.OrgId).
		Join("left", "dis_distributor c", "c.id = a.distributor_id").And("a.org_id=?", in.OrgId).
		Join("left", "dis_withdraw_order f", "f.withdraw_no = a.withdraw_no").
		Join("left", "upetmart.upet_orders g", "g.order_sn = f.order_sn").
		Join("left", "upetmart.upet_order_goods h", "h.order_id = g.order_id")
	if in.ExportType != 1 {
		session.GroupBy("a.id")
	}
	if in.Status != 0 {
		session.And("a.status=?", in.Status)
	}
	//过滤删除的数据
	session.And("a.hide_state=0")

	if in.WhereType2 != "" && in.Where2 != "" {
		whereType2Map := map[string]string{
			//"distributor_name":   "c.name like ?",
			"distributor_mobile": "c.encrypt_mobile = ?",
			"withdraw_no":        "a.withdraw_no = ?",
			"distributor_id":     "a.distributor_id = ? ",
			"enterprise_name":    "d.enterprise_name like ?",
		}
		if in.OrgId == enum.BLKYOrgId {
			whereType2Map["enterprise_name"] = "de.enterprise_name like ?"
		}
		if v, ok := whereType2Map[in.WhereType2]; ok {
			if in.WhereType2 == "distributor_mobile" {
				in.Where2 = utils.MobileEncrypt(in.Where2)
			} else if in.WhereType2 == "distributor_name" || in.WhereType2 == "enterprise_name" {
				in.Where2 = "%" + in.Where2 + "%"
			}
			session.And(v, in.Where2)
		}
		if in.WhereType2 == "distributor_name" {
			if in.OrgId == enum.BLKYOrgId {
				session.And("c.real_name like ?", "%"+in.Where2+"%")
			} else {
				session.And("c.name like ?", "%"+in.Where2+"%")
			}
		}
	}

	if in.WhereType != "" && (in.WhereStart != "" || in.WhereEnd != "") {
		if in.WhereType == "create_time" || in.WhereType == "pay_time" {
			if in.WhereStart != "" {
				session.And("a."+in.WhereType+">=?", in.WhereStart)
			}
			if in.WhereEnd != "" {
				session.And("a."+in.WhereType+"<=?", in.WhereEnd)
			}
		}

	}

	session.OrderBy("a.create_time desc")
	s := `
	f.swlm_cps_id,
	g.order_sn,
	ROUND(h.goods_pay_price * h.dis_commis_rate, 0) AS order_commission,
	h.goods_name,
	(case
		when g.org_type = 1 then '北京百林康源'
		when g.org_type = 2 then '深圳利都'
		else ''
	end ) as org_type_name,
	a.*,
	IF(c.org_id = 4, c.real_name, c.name) as distributor_name,
	c.mobile,c.encrypt_mobile,
	(case
		when a.org_id = 4 then '百林康源'
		else b.shop_name
	end ) as shop_name,
	pre_tax_amount-after_tax_amount as tax,
	(case
		when a.dis_role = 1 then '老板'
		when a.dis_role = 2 then '店员'
		when a.dis_role = 3 then '医生'
		else ''
	end ) as dis_role_text,
	(case
		when a.status = 1 then '未打款'
		when a.status = 2 then '已打款'
		when a.status = 3 then '已拒绝'
		else ''
	end ) as status_text,
	d.id as enterprise_id,
	d.enterprise_name,
	de.enterprise_name as dis_enterprise_name`
	out = make([]vo.DisWithdrawView, 0)
	count, err := session.Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).
		Select(s).
		FindAndCount(&out)
	for k, v := range out {
		if in.OrgId == enum.BLKYOrgId && (v.DisRole == disdistributor.DisRoleBoss || v.DisRole == disdistributor.DisRoleWorker) {
			out[k].EnterpriseName = v.DisEnterpriseName
		}
	}
	total = cast.ToInt(count)

	return out, total, err
}

// 获取提现记录
func (h *DisWithdrawService) GetSimpleWithList(in vo.GetSimpleWithListReq) (out []vo.DisWithdrawView, total int, err error) {

	h.Begin()
	defer h.Close()
	logPrefix := fmt.Sprintf("获取提现记录列表，分销员id：%d，所在主体：%d", in.DistributorId, in.OrgId)
	log.Infof("%s 入参：%s", logPrefix, utils.InterfaceToJSON(in))
	// 将子上下文传入Session
	session := h.Session

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}
	d := &distribution_po.DisDistributor{Id: in.DistributorId}
	exist, err := session.Table("dis_distributor").Get(d)
	if err != nil {
		return out, total, errors.New("查询分销员数据失败")
	}

	if !exist {
		return out, total, errors.New("分销员不存在")
	}
	if d.OrgId != in.OrgId {
		return out, total, errors.New("分销员不存在")
	}

	session.Table("dis_withdraw").Alias("a").Join("left", "dis_distributor b", "b.id=a.distributor_id").
		Where("a.org_id=?", in.OrgId).
		And("a.shop_id=?", in.ShopId).
		OrderBy("a.create_time desc")
	if in.OrgId == enum.BLKYOrgId {
		session.Where("a.distributor_id=?", in.DistributorId)
	}
	s := `a.*,
	(case
		when a.status = 1 then '未打款'
		when a.status = 2 then '已打款'
		when a.status = 3 then '已拒绝'
		else ''
	end ) as status_text,a.pre_tax_amount-a.after_tax_amount as tax,
	b.name as distributor_name`
	out = make([]vo.DisWithdrawView, 0)
	count, err := session.Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).
		Select(s).
		FindAndCount(&out)

	total = cast.ToInt(count)

	return out, total, err
}

func (h *DisWithdrawService) GetDisWithdrawDetail(in vo.GetDisWithdrawDetailReq) (out vo.DisWithdrawView, err error) {

	if in.OrgId <= 0 {
		return out, errors.New("主体id无效")
	}
	if in.DisWithdrawId <= 0 {
		return out, errors.New("提现id无效")
	}
	h.Begin()
	defer h.Close()

	_, err = h.Session.Table("dis_withdraw").Alias("a").
		Join("inner", "shop b", "b.id = a.shop_id").
		Join("inner", "dis_distributor c", "c.id = a.distributor_id ").And("a.org_id=?", in.OrgId).And("a.id=?", in.DisWithdrawId).
		Select(`a.*,
		c.name as distributor_name,
		b.shop_name,
		a.pre_tax_amount-a.after_tax_amount as tax,
		(case
			when a.status = 1 then '未打款'
			when a.status = 2 then '已打款'
			when a.status = 3 then '已拒绝'
			else ''
		end ) as status_text`).Get(&out)

	return out, err
}

func (h *DisWithdrawService) DisWithdrawCheck(in vo.DisWithdrawCheckReq) (err error) {
	logPrefix := fmt.Sprintf("提现审核操作-提现id为%d", in.DisWithdrawId)
	log.Infof("%s-入参为%s", logPrefix, utils.InterfaceToJSON(in))
	defer func() {
		if err := recover(); err != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER-提现申请] %v %s\n", err, stack[:length])

		}
	}()

	h.Begin()
	defer h.Close()

	//加锁， 同一个提现只能有一个审核操作
	cacheK := fmt.Sprintf(cachekey.WithdrawCheck, in.DisWithdrawId)
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cacheK, time.Minute*10)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, cacheK)
		return errors.New("该提现正在审核中，请稍后再试")
	}
	defer mCache.Delete(string(cache_source.EShop), cacheK)

	// 获取提现信息
	var DisWithdraw distribution_po.DisWithdraw
	DisWithdraw.Id = in.DisWithdrawId
	exists, err := h.Session.Table("dis_withdraw").Get(&DisWithdraw)
	if err != nil {
		log.Errorf("%s-获取数据失败-错误为%s", logPrefix, utils.InterfaceToJSON(err))
		return err
	}
	if !exists {
		log.Errorf("%s-未找到提现信息", logPrefix)
		return errors.New("未找到提现信息")
	}
	if DisWithdraw.Status != diswithdraw.StatusUncheck {
		log.Errorf("%s-只有未打款的的提现申请才可操作", logPrefix)
		return errors.New("只有未打款的的提现申请才可操作")
	}

	// 提现维度： shop代表提现店铺金额  myself代表分销员个人
	withdrawFlag := enum.WithdrawFlagShop
	withdrawOrder := make([]distribution_po.DisWithdrawOrderExt, 0)
	if in.OrgId == enum.BLKYOrgId && DisWithdraw.DisRole == disdistributor.DisRoleDoctor {
		withdrawFlag = enum.WithdrawFlagSelf
	} else if in.OrgId == enum.BLKYOrgId && DisWithdraw.DisRole == disdistributor.DisRoleBoss { //百林康源老板提现
		withdrawOrder, err = new(distribution_po.DisWithdrawOrder).GetWithdrawOrderListByWithdrawNo(h.Session, DisWithdraw.WithdrawNo)
		if err != nil {
			log.Errorf("%s-获取提现订单失败-错误为%s", logPrefix, utils.InterfaceToJSON(err))
			return err
		}
	}

	var applyWithRecords = make([]*distribution_po.DisWithdrawRecord, 0)
	err = h.Session.Table("dis_withdraw_record").
		Where("org_id=?", DisWithdraw.OrgId).
		Where("shop_id=?", DisWithdraw.ShopId).
		Where("third_id=?", DisWithdraw.Id).
		Where("type=?", diswithdrawrecord.TypeApply).
		Find(&applyWithRecords)
	if err != nil {
		log.Errorf("%s-获取提现记录失败-错误为%s", logPrefix, utils.InterfaceToJSON(err))
		return err
	}
	if len(applyWithRecords) < 1 {
		log.Errorf("%s-未找到提现申请记录数据", logPrefix)
		return errors.New("未找到提现申请记录数据")
	}

	cols := []string{"status"}

	session := h.Engine.NewSession()
	defer session.Close()
	session.Begin()
	if in.CheckType == 2 {
		DisWithdraw.Status = diswithdraw.StatusRejected
		DisWithdraw.RejectReason = in.RejectReason
		cols = append(cols, "reject_reason")
	} else {
		DisWithdraw.Status = diswithdraw.StatusChecked
		DisWithdraw.PayTime = time.Now()
		cols = append(cols, "pay_time")
	}
	//第一步： 修改提现状态
	if affectRows, err := session.ID(DisWithdraw.Id).Where("status=?", diswithdraw.StatusUncheck).Cols(cols...).Update(&DisWithdraw); err != nil {
		log.Errorf("%s-更新提现状态失败-错误为-%s", logPrefix, err.Error())
		session.Rollback()
		return errors.New("更新提现状态失败")
	} else if affectRows == 0 {
		log.Errorf("%s-更新提现状态失败-错误为-提现状态已改变", logPrefix)
		session.Rollback()
		return errors.New("提现状态已改变")
	}

	needUpdateMap := make(map[string]string)
	for _, v := range applyWithRecords {

		var insertRecord = distribution_po.DisWithdrawRecord{
			OrgId:         DisWithdraw.OrgId,
			ShopId:        DisWithdraw.ShopId,
			DisId:         v.DisId, //  计算出具体提到谁的身上
			WithdrawDisId: DisWithdraw.DistributorId,
			ThirdId:       cast.ToInt64(DisWithdraw.Id),
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		}

		if in.CheckType == 2 { //拒绝
			insertRecord.Type = diswithdrawrecord.TypeJeject
			insertRecord.WaitWithdraw = v.WithdrawApply
			insertRecord.WithdrawApply = -v.WithdrawApply
		} else { //同意
			insertRecord.Type = diswithdrawrecord.TypeAgree
			insertRecord.WithdrawApply = -v.WithdrawApply
			insertRecord.WithdrawSuccess = v.WithdrawApply
		}

		//第二步： 插入一条提现记录
		if _, err := session.Table("eshop.dis_withdraw_record").Insert(&insertRecord); err != nil {
			log.Errorf("%s-插入提现记录失败-错误为-%s", logPrefix, err.Error())
			session.Rollback()
			return errors.New("插入提现记录失败")
		}
	}

	// 百林康源老板提现， 如果提现订单里有逆向订单，则需要更新逆向订单状态，如果审核通过， 需要增加逆向订单的金钱变动
	if in.OrgId == enum.BLKYOrgId && DisWithdraw.DisRole == disdistributor.DisRoleBoss {
		CpsIds := make([]int, 0)

		for _, v := range withdrawOrder {
			needUpdateMap[fmt.Sprintf("%d:%d:%d", v.OrgId, v.ShopId, v.DisId)] = ""
			if v.SwlmCpsId > 0 {
				CpsIds = append(CpsIds, v.SwlmCpsId)
				insertRecord := make([]distribution_po.DisWithdrawRecord, 0)
				if in.CheckType == 2 {
					//增加逆向订单的金钱变动
					insertRecord = append(insertRecord, distribution_po.DisWithdrawRecord{
						OrgId:         DisWithdraw.OrgId,
						ShopId:        DisWithdraw.ShopId,
						DisId:         v.DisId, //  计算出具体提到谁的身上
						WithdrawDisId: DisWithdraw.DistributorId,
						ThirdId:       cast.ToInt64(DisWithdraw.Id),
						Type:          diswithdrawrecord.TypeDeductCommission,
						WaitWithdraw:  -v.DisCommisAmount,
						CreateTime:    time.Now(),
						UpdateTime:    time.Now(),
					})
					// 插入佣金扣减平账记录
					if _, err := session.Table("eshop.dis_withdraw_record").Insert(&insertRecord); err != nil {
						log.Errorf("%s-插入佣金扣减平账记录失败-错误为-%s", logPrefix, err.Error())
						session.Rollback()
						return errors.New("插入佣金扣减平账记录失败")
					}
				}

			}

		}
		// 更新逆向订单状态
		if len(CpsIds) > 0 {
			status := distribution_po.StatusNotDeduct
			if in.CheckType == 1 {
				status = distribution_po.StatusDeducted
			}
			session.Table("eshop.dis_distributor_swlm_cps").
				In("id", CpsIds).
				Update(map[string]interface{}{"status": status})
		}
	}

	if err := session.Commit(); err != nil {
		log.Errorf("%s-提交事务失败-错误为-%s", logPrefix, err.Error())
		session.Rollback()
		return errors.New("提交事务失败")
	}
	//第三步： 更新分销员金额
	go func() {
		c := &DiscommissionService{}
		c.WithdrawCommUpdate(needUpdateMap, withdrawFlag)
	}()

	return nil
}
