package omnibus_po

import (
	"eShop/infra/log"
	"fmt"

	"xorm.io/xorm"
)

type TScrmPetInfo struct {
	Id            int    `xorm:"not null pk autoincr INT(10)"`
	PetId         string `xorm:"not null comment('宠物ID作为唯一标示') unique VARCHAR(32)"`
	UserId        string `xorm:"not null comment('用户ID作为唯一标示') index VARCHAR(32)"`
	PetName       string `xorm:"not null comment('宠物昵称') VARCHAR(32)"`
	PetSex        int    `xorm:"not null comment('宠物性别 0未知 1公 2母') TINYINT(3)"`
	PetKindof     int    `xorm:"not null default -1 comment('品种 -1未知') INT(9)"`
	PetVariety    int    `xorm:"not null default -1 comment('种类 -1 未知') INT(9)"`
	PetNeutering  int    `xorm:"not null default -1 comment('绝育 -1未知,0未绝育,1已绝育') TINYINT(3)"`
	PetVaccinated int    `xorm:"not null default -1 comment('疫苗 -1未知,0未接种,1已接种') TINYINT(3)"`
	PetDeworming  int    `xorm:"not null default -1 comment('驱虫  -1未知,0未驱虫,1 已驱虫') TINYINT(3)"`
	PetWeight     int    `xorm:"not null default 0 comment('宠物体重,单位g') INT(10)"`
	PetLong       int    `xorm:"not null default 0 comment('宠物体长,单位mm') INT(10)"`
	PetHeight     int    `xorm:"not null default 0 comment('宠物体高,单位mm') INT(10)"`
	PetSource     int    `xorm:"not null default 0 comment('宠物来源 0：子龙 1：小暖 2：瑞鹏 3:小程序') TINYINT(3)"`
	PetStatus     int    `xorm:"not null default 0 comment('宠物状态 0正常,1死亡,2走失,4送人,8隐藏') index TINYINT(3)"`
	PetAvatar     string `xorm:"not null default '''' comment('宠物头像') VARCHAR(256)"`

	PetBirthday      string `xorm:"<-"`
	PetHomeday       string `xorm:"<-"`
	PetRemark        string `xorm:"not null default '''' comment('用户备注') VARCHAR(128)"`
	CreateTime       string `xorm:"<-"`
	UpdateTime       string `xorm:"<-"`
	BigdataPetId     string `xorm:"default 'NULL' comment('大数据唯一Id') index VARCHAR(32)"`
	FaceId           string `xorm:"default 'NULL' comment('宠物faceId') index VARCHAR(60)"`
	PetCode          string `xorm:"default 'NULL' comment('鼻纹识别宠物code') VARCHAR(15)"`
	InsuranceFaceId  string `xorm:"default 'NULL' comment('保险-宠物faceId') index VARCHAR(60)"`
	DogLicenceCode   string `xorm:"default 'NULL' comment('养犬许可证号码') VARCHAR(20)"`
	DogVaccinateCode string `xorm:"default 'NULL' comment('犬类免疫证号码') VARCHAR(20)"`
	EnterSource      string `xorm:"not null default '''' comment('宠物鼻纹录入医院') VARCHAR(32)"`
	ChipCode         string `xorm:"not null default '''' comment('芯片号') VARCHAR(50)"`
	PetFlower        string `xorm:"not null default '''' comment('宠物花色') VARCHAR(50)"`
	FlowerCode       string `xorm:"not null default '''' comment('花色编码') VARCHAR(50)"`
}

// 根据UserId获取宠物列表
func (s *TScrmPetInfo) GetPetListByUserId(session *xorm.Session, ScrmUserId string) (petList []*TScrmPetInfo, err error) {
	logPrefix := fmt.Sprintf("====根据ScrmUserId获取宠物列表,入参:%s", ScrmUserId)
	log.Info(logPrefix)
	petList = make([]*TScrmPetInfo, 0)
	if session == nil {
		err = fmt.Errorf("session is nil")
		return
	}
	err = session.Table("scrm_organization_db.t_scrm_pet_info").Where("user_id = ?", ScrmUserId).Find(&petList)
	if err != nil {
		log.Error(logPrefix+"查询失败,err:%s", err.Error())
		return
	}
	return
}
