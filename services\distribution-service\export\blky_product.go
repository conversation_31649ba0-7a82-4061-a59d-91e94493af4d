package export

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	_ "github.com/denisenkom/go-mssqldb"
	"github.com/xuri/excelize/v2"
	"os"
	"slices"
	"xorm.io/xorm"
)

type BlkyProductTask struct {
	F            *os.File
	SheetName    string
	ExportParams *vo.BlkyProductPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 分销员导出
func (e BlkyProductTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.BlkyProductPageReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.F, err = os.Create(e.SheetName)
	defer e.F.Close()
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	client := services.BlkyProductService{}
	k := 0
	for {
		txt := ""
		ret, _, err := client.BlkyProductPage(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		if len(ret) > 0 {
			if e.ExportParams.DataType == disdistributor.XkucunCompanySzld {
				for i := 0; i < len(ret); i++ {
					k++
					txt += "https://t.rvet.cn/#/cls?id=" + ret[i].Swlm + "," + ret[i].Swlm + "\n"
				}
			} else if e.ExportParams.DataType == disdistributor.XkucunCompanyBlky {
				// 将查询结果同步到其他数据
				sync2SqlServer(ret)

				for i := 0; i < len(ret); i++ {
					k++
					txt += ret[i].Swlm + "," + ret[i].Sfwm + ",https://t.rvet.cn/#/blky?id=" + ret[i].Swlm + "\n"
				}
			}
		}
		if len(ret) < e.ExportParams.PageSize {
			if len(txt) > 1 {
				txt = txt[:len(txt)-1]
			}
			e.F.WriteString(txt)
			break
		}
		e.F.WriteString(txt)
	}
	successNum = k
	return

}

func sync2SqlServer(ret []vo.BlkyProductData) error {
	if len(ret) == 0 {
		return nil
	}

	// 数据库连接字符串
	dsn := config.Get("mssql.eshop")

	db, err := xorm.NewEngine("mssql", dsn)
	if err != nil {
		log.Error("sync2SqlServer，同步百林康源商品到sql server数据库连接失败:", err.Error())
		return err
	}

	// 检查库中已经存在的防伪码记录
	var wlmSlice []string
	for i := 0; i < len(ret); i++ {
		wlmSlice = append(wlmSlice, ret[i].Swlm)
	}
	var existWlm []string
	err = db.Table("xkucun").Select("DISTINCT(swlm)").In("swlm", wlmSlice).Find(&existWlm)
	if err != nil {
		log.Error("sync2SqlServer，同步百林康源商品到sql server数据库连接失败:", err.Error())
		return nil
	}

	// 将数据库中不存在的记录写入
	var values []po.Xkucun
	for i := 0; i < len(ret); i++ {
		if !slices.Contains(existWlm, ret[i].Swlm) {
			values = append(values, po.Xkucun{
				Sfwm: ret[i].Sfwm,
				Swlm: ret[i].Swlm,
			})
		}
	}

	if len(values) > 0 {
		_, err := db.Insert(&values)
		db.ShowSQL()
		if err != nil {
			log.Error("sync2SqlServer，同步百林康源商品到sql server数据库写入失败:", err.Error())
			return err
		}
	}

	return nil
}

// SetSheetName 百林康源商品物流码导出列表头
func (e BlkyProductTask) SetSheetName(args ...interface{}) {
}

func (e BlkyProductTask) GenerateDownUrl() (url string, err error) {
	fileName := e.SheetName
	file, err := os.Open(fileName)
	if err != nil {
		fmt.Println("打开百林康源商品物流码导出文件失败:", err.Error())
		return
	}
	//defer file.Close()

	uf := &utils.UploadFile{
		Name:   fileName,
		Reader: file,
	}
	url, err = uf.ToQiNiu()
	defer os.Remove(fileName)
	return url, err
}

func (e BlkyProductTask) OperationFunc(row []string, orgId int) string {
	return ""
}
