package async

import (
	"bufio"
	po "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/view-model"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"net/http"
	"strings"
	"time"
)

type Xkucun struct {
	common.BaseService
}

func (x Xkucun) OperationFunc(parJson string, org_id int) (*viewmodel.ImportResult, error) {
	logPrefix := fmt.Sprintf("异步任务-处理物流码：%s ", utils.InterfaceToJSON(parJson))
	log.Info(logPrefix)
	out := new(viewmodel.ImportResult)
	//200 表示处理正常，会确认MQ，
	out.Code = 400
	//错误数量 有需要就写，比如批量操作100个商品，有成功有失败，就需要设置这个
	out.FailNum = 1
	out.SuccessNum = 0

	var params map[string]interface{}
	err := json.Unmarshal([]byte(parJson), &params)
	if err != nil {
		log.Error(logPrefix + "解析json参数错误")
		return out, errors.New("解析json参数错误")
	}

	// 发送 HTTP GET 请求到指定的 URL
	resp, err := http.Get(params["file_url"].(string))
	if err != nil {
		log.Error(logPrefix+"读取文件请求出错: %v", err)
		return out, errors.New("读取文件请求出错:" + err.Error())
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.Error(logPrefix+"请求失败，状态码: %v", resp.StatusCode)
		return out, errors.New("请求失败，状态码:" + cast.ToString(resp.StatusCode))
	}

	x.Begin()
	defer x.Close()
	session := x.Engine.NewSession()
	session.Begin()

	// 创建一个 bufio.Scanner 对象，用于逐行读取响应体内容
	scanner := bufio.NewScanner(resp.Body)

	insertXKucunList := make([]po.XLogisticsCode, 0)
	insertXSecurityCodeList := make([]po.XSecurityCode, 0)

	// 遍历每一行
	for scanner.Scan() {
		// 获取当前行的内容
		line := scanner.Text()

		if params["file_type"].(string) == "1" {
			// 处理每一行的值，这里简单打印出来
			valList := strings.Split(line, ",")
			if len(valList) != 3 {
				session.Rollback()
				log.Error(logPrefix+"文件内容格式错误，请检查文件内容是否正确", err)
				return out, errors.New("文件内容格式错误，请检查文件内容是否正确")
			}
			insertXKucunList = append(insertXKucunList, po.XLogisticsCode{
				Swlm:     strings.TrimSpace(valList[1]),
				Dregtime: time.Now(),
				Sfwm:     strings.TrimSpace(valList[2]),
				Sn:       strings.TrimSpace(valList[0]),
			})

			if len(insertXKucunList) >= 1000 {
				if _, err := session.Table("blky.xlogistics_code").Insert(insertXKucunList); err != nil {
					session.Rollback()
					log.Error(logPrefix+"批量插入出错: %v", err)
					return out, errors.New("批量插入出错:" + err.Error())
				}
				insertXKucunList = make([]po.XLogisticsCode, 0)
			}
		} else if params["file_type"].(string) == "2" {
			if strings.TrimSpace(line) == "" {
				session.Rollback()
				log.Error(logPrefix+"文件内容格式错误，请检查文件内容是否正确", err)
				return out, errors.New("文件内容格式错误，请检查文件内容是否正确")
			}
			insertXSecurityCodeList = append(insertXSecurityCodeList, po.XSecurityCode{
				Code:       strings.TrimSpace(line),
				CreateTime: time.Now(),
			})

			if len(insertXSecurityCodeList) >= 1000 {
				if _, err := session.Table("blky.xsecurity_code").Insert(insertXSecurityCodeList); err != nil {
					session.Rollback()
					log.Error(logPrefix+"批量插入出错: %v", err)
					return out, errors.New("批量插入出错:" + err.Error())
				}
				insertXSecurityCodeList = make([]po.XSecurityCode, 0)
			}
		}

		out.SuccessNum++
	}

	if len(insertXKucunList) > 0 {
		if _, err := session.Table("blky.xlogistics_code").Insert(insertXKucunList); err != nil {
			session.Rollback()
			log.Error(logPrefix+"批量插入出错: %v", err)
			return out, errors.New(logPrefix + "批量插入出错:" + err.Error())
		}
	}

	if len(insertXSecurityCodeList) > 0 {
		if _, err := session.Table("blky.xsecurity_code").Insert(insertXSecurityCodeList); err != nil {
			session.Rollback()
			log.Error(logPrefix+"批量插入出错: %v", err)
			return out, errors.New(logPrefix + "批量插入出错:" + err.Error())
		}
	}

	if err := session.Commit(); err != nil {
		log.Error(logPrefix+"提交事务出错: %v", err)
		return out, errors.New(logPrefix + "提交事务出错:" + err.Error())
	}

	out.FailNum = 0
	out.Code = 200
	return out, nil
}
