package iobound

import (
	baseVO "eShop/view-model/inventory-vo"
	"time"
)

// IoBoundCreateCommand 创建库存命令
type IoBoundCreateCommand struct {
	ChainId        int64                        `json:"chain_id"`        // 连锁id
	StoreId        string                       `json:"store_id"`        // 店铺id
	WarehouseId    int                          `json:"warehouse_id"`    // 仓库id
	ItemType       int                          `json:"item_type"`       // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	ItemRefId      int                          `json:"item_ref_id"`     // 出入库关联的单据id
	ItemRefNo      string                       `json:"item_ref_no"`     // 出入库关联的单据编号
	ItemRefType    int                          `json:"item_ref_type"`   // 出入库关联的单据类型，1.无，2.采购单，3.订单，4.盘点单，5.其他
	Remark         string                       `json:"remark"`          // 备注
	Details        []IoBoundDetailCreateCommand `json:"details"`         // 商品出入库详情
	OccurrenceTime time.Time                    `json:"occurrence_time"` // 发生时间
	Operator       string                       `json:"operator"`        // 操作人
}

// IoBoundDetailCreateCommand 商品出入库详情命令
type IoBoundDetailCreateCommand struct {
	BoundType       int    `json:"bound_type"`         // 出入库类型：1. 入库，2. 出库
	ItemType        int    `json:"item_type"`          // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	ItemDetailRefId string `json:"item_detail_ref_id"` // 出入库关联的单据详情id
	SkuId           int    `json:"sku_id"`             // SKU ID
	ProductName     string `json:"product_name"`       // 商品名称
	IoPrice         int    `json:"io_price"`           // 出入库单价，单位为分
	IoCount         int    `json:"io_count"`           // 出入库数量
	RealIoAmount    int    `json:"real_io_amount"`     // 实际出入库小计，单位为分
}

// IoBoundPageRequest 库存分页查询命令
type IoBoundPageRequest struct {
	ChainId     int64  `json:"chain_id"`     // 连锁id
	StoreId     string `json:"store_id"`     // 门店id
	WarehouseId int    `json:"warehouse_id"` // 仓库id
	BoundNo     string `json:"bound_no"`     // 出入库单号
	BoundType   int    `json:"bound_type"`   // 出库/入库类型
	ItemType    int    `json:"item_type"`    // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12. 自用出库
	ItemRefId   int    `json:"item_ref_id"`  // 出入库关联的单据id
	ItemRefNo   string `json:"item_ref_no"`  // 出入库关联的单据编号
	Remark      string `json:"remark"`       // 备注
	Operator    string `json:"operator"`     // 操作人
	Query       string `json:"query"`        // 查询条件
	baseVO.PageRequest
	baseVO.TimeQueryRequest
}

// IoBoundResponse 库存分页查询结果
type IoBoundResponse struct {
	Id             int       `json:"id"`              // 主键
	ChainId        int64     `json:"chain_id"`        // 连锁id
	StoreId        string    `json:"store_id"`        // 门店id
	WarehouseId    int       `json:"warehouse_id"`    // 仓库id
	WarehouseName  string    `json:"warehouse_name"`  // 仓库名称
	BoundNo        string    `json:"bound_no"`        // 出入库单号
	BoundType      int       `json:"bound_type"`      // 出库/入库类型
	ItemType       int       `json:"item_type"`       // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12. 自用出库
	ItemRefId      int       `json:"item_ref_id"`     // 出入库关联的单据id
	ItemRefNo      string    `json:"item_ref_no"`     // 出入库关联的单据编号
	ItemRefType    int       `json:"item_ref_type"`   // 出入库关联的单据类型
	TotalNum       int       `json:"total_num"`       // 出入库总数量
	TotalAmount    int       `json:"total_amount"`    // 出入库总价(分)
	SellAmount     int       `json:"sell_amount"`     // 出库售卖总价, 入库则为0(分)
	Remark         string    `json:"remark"`          // 备注
	IsDeleted      int       `json:"is_deleted"`      // 删除标识:0未删除,1已删除
	Operator       string    `json:"operator"`        // 操作人
	OccurrenceTime time.Time `json:"occurrence_time"` // 发生时间
	CreatedTime    string    `json:"created_time"`    // 创建时间
	UpdatedTime    string    `json:"updated_time"`    // 更新时间
}

// OutBoundCommand 销售出库
type OutBoundCommand struct {
	ChainId     int64                   `json:"chain_id"`     // 连锁id
	StoreId     string                  `json:"store_id"`     // 店铺id
	WarehouseId int                     `json:"warehouse_id"` // 仓库ID
	ChannelId   int                     `json:"channel_id"`   // 渠道ID
	RefId       int                     `json:"ref_id"`       // 出库关联单id
	RefNo       string                  `json:"ref_no"`       // 出库关联单单号
	RefType     int                     `json:"ref_type"`     // 库存关联对象类型 1.无，2.采购单，3.订单，4.盘点单，5.其他
	Remark      string                  `json:"remark"`       // 备注
	Details     []OutBoundDetailCommand `json:"details"`      // 出库详情
	Operator    string                  `json:"operator"`     // 操作人
}

// OutBoundDetailCommand 出库详情
type OutBoundDetailCommand struct {
	ItemDetailRefId string `json:"item_detail_ref_id"` // 出入库关联的单据详情id
	SkuId           int    `json:"sku_id"`             // SKU ID
	ProductName     string `json:"product_name"`       // 商品名称
	IoPrice         int    `json:"io_price"`           // 出库单价，单位为分
	IoCount         int    `json:"io_count"`           // 出库数量
}

// UnFreezeCommand 解锁库存
type UnFreezeCommand struct {
	ChainId     int64          `json:"chain_id"`     // 连锁id
	StoreId     string         `json:"store_id"`     // 店铺id
	WarehouseId int            `json:"warehouse_id"` // 仓库ID,渠道id不为空时，可以不填
	RefId       int            `json:"ref_id"`       // 出库关联单id
	RefNo       string         `json:"ref_no"`       // 出库关联单单号
	RefType     int            `json:"ref_type"`     // 库存关联对象类型 1.无，2.采购单，3.订单，4.盘点单，5.其他
	Remark      string         `json:"remark"`       // 备注
	DetailMap   map[string]int `json:"details"`      // 出库详情: key为出入库关联的单据详情id, value为解冻数量
	Operator    string         `json:"operator"`     // 操作人
}

// InBoundCommand 销售出库
type InBoundCommand struct {
	ChainId     int64                  `json:"chain_id"`     // 连锁id
	StoreId     string                 `json:"store_id"`     // 店铺id
	WarehouseId int                    `json:"warehouse_id"` // 仓库ID,渠道id不为空时，可以不填
	ChannelId   int                    `json:"channel_id"`   // 渠道ID,仓库id不为空时，可以不填
	RefId       int                    `json:"ref_id"`       // 出库关联单id
	RefNo       string                 `json:"ref_no"`       // 出库关联单单号
	RefType     int                    `json:"ref_type"`     // 库存关联对象类型 1.无，2.采购单，3.订单，4.盘点单，5.其他
	Remark      string                 `json:"remark"`       // 备注
	Details     []InBoundDetailCommand `json:"details"`      // 出库详情
	Operator    string                 `json:"operator"`     // 操作人
}

// InBoundDetailCommand 出库详情
type InBoundDetailCommand struct {
	ItemDetailRefId string `json:"item_detail_ref_id"` // 出入库关联的单据详情id
	SkuId           int    `json:"sku_id"`             // SKU ID
	ProductName     string `json:"product_name"`       // 商品名称
	IoPrice         int    `json:"io_price"`           // 出库单价，单位为分
	IoCount         int    `json:"io_count"`           // 出库数量
}
