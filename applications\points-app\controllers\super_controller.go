package controllers

import (
	po "eShop/domain/points-po"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/points-service"
	distribution_vo "eShop/view-model/distribution-vo"
	vo "eShop/view-model/points-vo"
	"encoding/json"
	"net/http"

	"github.com/go-chi/chi"
	"github.com/spf13/cast"
)

type SuperController[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO] struct {
	service service.ISuperService[Id, E, S, U, Q, R]
	hooks   IControllerHooks[Id, E, S, U, Q, R]
}

func NewSuperController[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO](
	service service.ISuperService[Id, E, S, U, Q, R],
	hooks IControllerHooks[Id, E, S, U, Q, R],
) SuperController[Id, E, S, U, Q, R] {
	return SuperController[Id, E, S, U, Q, R]{
		service: service,
		hooks:   hooks,
	}
}

func (c SuperController[Id, E, S, U, Q, R]) Routes(r chi.Router) {
	// 建议子类实现接口时，和以下定义的路由避开重复，否则会出现后定义的路由覆盖前定义的路由
	r.Post("/", c.Create)
	r.Put("/", c.Update)
	r.Get("/detail", c.Detail)
	r.Get("/query", c.Query)
	r.Post("/page", c.Page)
	r.Post("/list", c.List)
	r.Delete("/", c.Delete)
	r.Post("/export", c.Export)
}

func (c SuperController[Id, E, S, U, Q, R]) Create(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	saveVO, err := utils.Bind[S](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 定义一个预处理方法，由子类实现
	saveVO, err = c.hooks.BeforeCreate(w, r, saveVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 执行创建操作
	result, err := c.service.Create(nil, saveVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

func (c SuperController[Id, E, S, U, Q, R]) Update(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	updateVO, err := utils.Bind[U](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 定义一个预处理方法，由子类实现
	updateVO, err = c.hooks.BeforeUpdate(w, r, updateVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 执行创建操作
	result, err := c.service.Update(nil, updateVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

func (c SuperController[Id, E, S, U, Q, R]) Detail(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	idVO, err := utils.Bind[vo.IdVO[Id]](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	result, err := c.service.Detail(nil, idVO.Id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

func (c SuperController[Id, E, S, U, Q, R]) Query(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	queryVO, err := utils.Bind[Q](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 定义一个预处理方法，由子类实现
	queryVO, err = c.hooks.BeforeQuery(w, r, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 执行查询操作
	result, err := c.service.Query(nil, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

func (c SuperController[Id, E, S, U, Q, R]) Page(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	queryVO, err := utils.Bind[Q](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 定义一个预处理方法，由子类实现
	queryVO, err = c.hooks.BeforePage(w, r, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 执行分页查询操作
	result, total, err := c.service.Page(nil, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithPage(w, result, int(total))
}

// List 获取所有数据
func (c SuperController[Id, E, S, U, Q, R]) List(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	queryVO, err := utils.Bind[Q](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 定义一个预处理方法，由子类实现
	queryVO, err = c.hooks.BeforeList(w, r, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 执行查询操作
	result, err := c.service.List(nil, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

// Delete 删除数据
func (c SuperController[Id, E, S, U, Q, R]) Delete(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	idVO, err := utils.Bind[vo.IdVO[Id]](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 执行删除操作
	err = c.service.DeleteByIds(nil, idVO.Id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.Success(w)
}

func (c SuperController[Id, E, S, U, Q, R]) Export(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	queryVO, err := utils.Bind[Q](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 定义一个预处理方法，由子类实现
	queryVO, err = c.hooks.BeforeImport(w, r, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 执行导入操作
	taskServ := common.TaskListService{}
	var task distribution_vo.TaskList
	par, _ := json.Marshal(queryVO)
	task.OperationFileUrl = string(par)
	task.OrgId = cast.ToInt(r.Header.Get("org_id"))

	importType := queryVO.GetExportType()
	if importType <= 0 {
		response.BadRequest(w, "不支持导出")

	}
	task.TaskContent = cast.ToInt8(importType)
	err = taskServ.CreatTask(r, task)
	if err != nil {
		log.Error("super导出：err=" + err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.Success(w)
}

// IControllerHooks 定义控制器的钩子方法
type IControllerHooks[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO] interface {
	// 钩子方法
	BeforeCreate(w http.ResponseWriter, r *http.Request, saveVO S) (S, error)
	BeforeUpdate(w http.ResponseWriter, r *http.Request, updateVO U) (U, error)
	BeforeQuery(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error)
	BeforePage(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error)
	BeforeList(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error)
	BeforeImport(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error)
}

// ControllerHooks 提供默认的钩子实现
type ControllerHooks[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO] struct{}

func NewControllerHooks[Id po.Id, E po.Entity, S vo.SaveVO, U vo.UpdateVO, Q vo.QueryVO, R vo.ResultVO]() ControllerHooks[Id, E, S, U, Q, R] {
	return ControllerHooks[Id, E, S, U, Q, R]{}
}

// BeforeCreate 钩子方法
func (h ControllerHooks[Id, E, S, U, Q, R]) BeforeCreate(w http.ResponseWriter, r *http.Request, saveVO S) (S, error) {
	return saveVO, nil
}

// BeforeUpdate 钩子方法
func (h ControllerHooks[Id, E, S, U, Q, R]) BeforeUpdate(w http.ResponseWriter, r *http.Request, updateVO U) (U, error) {
	return updateVO, nil
}

// BeforeQuery 钩子方法
func (h ControllerHooks[Id, E, S, U, Q, R]) BeforeQuery(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error) {
	return queryVO, nil
}

// BeforePage 钩子方法
func (h ControllerHooks[Id, E, S, U, Q, R]) BeforePage(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error) {
	return queryVO, nil
}

// BeforeList 钩子方法
func (h ControllerHooks[Id, E, S, U, Q, R]) BeforeList(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error) {
	return queryVO, nil
}

// BeforeImport 钩子方法
func (h ControllerHooks[Id, E, S, U, Q, R]) BeforeImport(w http.ResponseWriter, r *http.Request, queryVO Q) (Q, error) {
	return queryVO, nil
}
