package voucher

import (
	"context"
	"eShop/infra/errors"
	vo "eShop/view-model/inventory-vo/voucher"
	"time"

	"xorm.io/xorm"
)

type Voucher struct {
	Id               int       `json:"id" xorm:"'id' pk autoincr comment('主键id')"`
	ChainId          int64     `json:"chain_id" xorm:"'chain_id' comment('连锁id')"`
	StoreId          string    `json:"store_id" xorm:"'store_id' default('0') comment('门店id')"`
	WarehouseId      int       `json:"warehouse_id" xorm:"'warehouse_id' comment('仓库id')"`
	SupplierId       int       `json:"supplier_id" xorm:"'supplier_id' default(0) comment('供应商id')"`
	Name             string    `json:"name" xorm:"'name' default('') comment('单据名称')"`
	VoucherNo        string    `json:"voucher_no" xorm:"'voucher_no' default('') comment('单据单号')"`
	VoucherType      int       `json:"voucher_type" xorm:"'voucher_type' default(0) comment('单据类型: 1-采购单, 2-采购退货单, 3-盘点单,4-采购入库单，5-采购出库单')"`
	Status           int       `json:"status" xorm:"'status' default(0) comment('单据状态: 1-草稿, 2-待处理, 3-部分完成, 4-已完成, 5-已作废, 6-已取消')"`
	SourceType       int       `json:"source_type" xorm:"'source_type' default(0) comment('单据来源: 1-连锁采购, 2-店铺自采, 3-代运营采购')"`
	ProfitStatus     int       `json:"profit_status" xorm:"'profit_status' default(0) comment('盈亏状态: 0-无盈亏, 1-盈利, 2-亏损, 3-平账')"`
	ChangeNum        int       `json:"change_num" xorm:"'change_num' default(0) comment('变更数量(盘盈/盘亏/入库/出库)')"`
	ChangeAmount     int       `json:"change_amount" xorm:"'change_amount' default(0) comment('变更金额(分)')"`
	Remark           string    `json:"remark" xorm:"'remark' default('') comment('备注')"`
	PurchaseTime     string    `json:"purchase_time" xorm:"'purchase_time' null comment('采购日期')"`
	LogisticsNo      string    `json:"logistics_no" xorm:"'logistics_no' null comment('物流单号')"`
	LogisticsCompany string    `json:"logistics_company" xorm:"'logistics_company' null comment('物流公司')"`
	DeliveryTime     string    `json:"delivery_time" xorm:"'delivery_time' null comment('预计到货时间')"`
	ReturnTime       string    `json:"return_time" xorm:"'return_time' null comment('退货时间')"`
	IsDeleted        int       `json:"is_deleted" xorm:"'is_deleted' default(0) comment('删除标识:0未删除,1已删除')"`
	Operator         string    `json:"operator" xorm:"'operator' null comment('操作人')"`
	VoucherTime      time.Time `json:"voucher_time" xorm:"'voucher_time' comment('单据日期')"`
	CreatedTime      time.Time `json:"created_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedTime      time.Time `json:"updated_time" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'updated_time' updated"`
	Pid              int       `json:"pid" xorm:"'pid' null comment('上级id')"`
}

// TableName 表名
func (p Voucher) TableName() string {
	return "inventory_voucher"
}

func (p Voucher) Insert(ctx context.Context, session *xorm.Session) (int, error) {
	_, err := session.Context(ctx).Insert(&p)
	if err != nil {
		return 0, errors.Wrap(err, "创建库存单据失败")
	}
	return p.Id, nil
}

// Update 更新库存单据
func (p Voucher) Update(ctx context.Context, session *xorm.Session) error {
	_, err := session.Context(ctx).ID(p.Id).Update(p)
	if err != nil {
		return errors.Wrap(err, "更新库存单据失败")
	}
	return nil
}

// Delete 删除库存单据（软删除）
func (p Voucher) Delete(ctx context.Context, session *xorm.Session) error {
	p.IsDeleted = 1
	_, err := session.Context(ctx).ID(p.Id).Cols("is_deleted", "updated_time").Update(p)
	if err != nil {
		return errors.Wrap(err, "删除库存单据失败")
	}
	return nil
}

// GetById 根据ID获取库存单据
func (p Voucher) GetById(ctx context.Context, session *xorm.Session, id int) (Voucher, error) {
	var voucher Voucher
	exists, err := session.Context(ctx).ID(id).Where("is_deleted = 0").Get(&voucher)
	if err != nil {
		return voucher, errors.Wrap(err, "查询库存单据失败")
	}
	if !exists {
		return voucher, nil
	}
	return voucher, nil
}

// Query 查询库存单据列表
func (p Voucher) Query(ctx context.Context, session *xorm.Session) ([]Voucher, error) {
	var vouchers []Voucher
	err := session.Context(ctx).Where("is_deleted = 0").Find(&vouchers)
	if err != nil {
		return nil, errors.Wrap(err, "查询库存单据列表失败")
	}
	return vouchers, nil
}

func (p Voucher) Page(ctx context.Context, session *xorm.Session, cmd vo.VoucherPageRequest) ([]vo.VoucherResponse, int64, error) {
	var vouchers []vo.VoucherResponse

	// 构建查询条件
	query := session.Table("inventory_voucher").Alias("iv").
		Join("LEFT", "dc_dispatch.warehouse w", "w.id = iv.warehouse_id").
		Join("LEFT", "inventory_voucher_detail ivd", "ivd.voucher_id = iv.id").
		Where("iv.store_id=? AND iv.voucher_type=3 AND iv.is_deleted=0", cmd.TenantId)

	query = buildCondition(cmd, query)

	query.Select("iv.*,w.name as warehouse_name,SUM(IF(ivd.actual_num<=0, 0, ivd.actual_num)) AS sku_count")

	// 执行分页查询
	offset := (cmd.Current - 1) * cmd.Size
	total, err := query.Limit(cmd.Size, offset).GroupBy("iv.id").FindAndCount(&vouchers)
	if err != nil {
		return nil, 0, errors.Wrap(err, "分页查询盘点记录失败")
	}

	return vouchers, total, nil
}

func (p Voucher) Detail(ctx context.Context, session *xorm.Session, Id int) (vo.VoucherResponse, error) {
	var voucher vo.VoucherResponse

	// 构建查询条件
	query := session.Table("inventory_voucher").Alias("iv").
		Join("LEFT", "inventory_voucher_detail ivd", "ivd.voucher_id = iv.id").
		Where("iv.id=?", Id)

	query.Select("iv.*,SUM(IF(ivd.actual_num<=0, 0, ivd.actual_num)) AS sku_count")

	// 执行分页查询
	_, err := query.Get(&voucher)
	if err != nil {
		return voucher, errors.Wrap(err, "查询盘点详情记录失败")
	}

	return voucher, nil
}

func (p Voucher) Summary(ctx context.Context, session *xorm.Session, cmd vo.VoucherPageRequest) (int, error) {
	var changeAmount int

	// 构建查询条件
	query := session.Table("inventory_voucher").Alias("iv").
		Where("iv.store_id=? AND iv.voucher_type=3 AND iv.is_deleted=0", cmd.TenantId)

	query = buildCondition(cmd, query)

	query.Select("SUM(change_amount)")

	// 执行分页查询
	_, err := query.Get(&changeAmount)
	if err != nil {
		return 0, errors.Wrap(err, "盘点分页列表统计查询失败")
	}

	return changeAmount, nil
}

func buildCondition(cmd vo.VoucherPageRequest, query *xorm.Session) *xorm.Session {
	// 添加时间查询条件
	if len(cmd.TimeStart) > 0 {
		query = query.And("iv.created_time >= ?", cmd.TimeStart)
	}
	if len(cmd.TimeEnd) > 0 {
		query = query.And("iv.created_time <= ?", cmd.TimeEnd)
	}

	// 盘点人
	if len(cmd.Operator) > 0 {
		query = query.And("iv.operator = ?", cmd.Operator)
	}

	// 盈亏状态
	if cmd.ProfitStatus > 0 {
		query = query.And("iv.profit_status = ?", cmd.ProfitStatus)
	}

	// 单据单号
	if len(cmd.VoucherNo) > 0 {
		query = query.And("iv.voucher_no = ?", cmd.VoucherNo)
	}

	// 库存单据状态
	if cmd.Status > 0 {
		query = query.And("iv.status = ?", cmd.Status)
	}

	// 仓库id
	if cmd.WarehouseId > 0 {
		query = query.And("iv.warehouse_id = ?", cmd.WarehouseId)
	}
	return query
}

// Cancel 取消单据
func (p *Voucher) Cancel(ctx context.Context, session *xorm.Session) {
	p.Status = 6
	p.UpdatedTime = time.Now()
}

// ManualComplete 手动完成单据
func (p *Voucher) ManualComplete(ctx context.Context, session *xorm.Session) {
	p.Status = 4
	p.UpdatedTime = time.Now()
}
