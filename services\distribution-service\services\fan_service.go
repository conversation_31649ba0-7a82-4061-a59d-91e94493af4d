package services

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	"eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

type DisFansService struct {
	common.BaseService
}

func (h *DisFansService) GetDisFansList(in distribution_vo.GetDisFansListReq) (out []distribution_vo.DisDistributorFansView, total int, err error) {
	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	h.Begin()
	defer h.Close()
	session := h.Session
	selectStr := `a.id,a.dis_id,a.member_id,a.org_id,a.order_amount,a.order_num,a.order_pay_amount,a.order_pay_num,a.expire_time,a.create_time,a.update_time,b.member_name,b.member_mobile,b.member_avatar,b.scrm_user_id`
	session.Table("eshop.dis_distributor_fans").Alias("a").
		Select(selectStr).
		Join("inner", "upetmart.upet_member b", "a.member_id=b.member_id").
		Join("inner", "eshop.dis_distributor c", "a.dis_id=c.id").
		Where("c.member_id=?", in.MemberId)

	if in.IsValid == 1 {
		session.And("a.expire_time>=?", time.Now().Format(utils.DateTimeLayout))
	} else if in.IsValid == 2 {
		session.And("a.expire_time<?", time.Now().Format(utils.DateTimeLayout))
	}

	if in.Mobile != "" {
		session.And("b.member_mobile=?", in.Mobile)
	}
	out = make([]distribution_vo.DisDistributorFansView, 0)
	count, err := session.OrderBy("a.expire_time desc").Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).
		FindAndCount(&out)
	if err != nil {
		log.Errorf("获取分销客户列表失败-错误为%s", err.Error())
		return nil, 0, errors.New("获取分销客户列表失败")
	}
	scrmUserSli := make([]string, len(out))
	for k, v := range out {
		out[k].MemberMobile = utils.AddStar(v.MemberMobile)
		scrmUserSli[k] = v.ScrmUserId
	}

	scrmInfo := make([]struct {
		UserId     string `json:"user_id"`
		UserAvatar string `json:"user_avatar"`
	}, 0)
	if err := session.Table("scrm_organization_db.t_scrm_user_info").Select("user_id,user_avatar").In("user_id", scrmUserSli).Find(&scrmInfo); err != nil {
		log.Errorf("获取分销粉丝的头像信息失败-错误为%s", err.Error())
	}
	scrmUserMap := make(map[string]string)
	for _, v := range scrmInfo {
		scrmUserMap[v.UserId] = v.UserAvatar
	}
	for k, v := range out {
		out[k].MemberAvatar = scrmUserMap[v.ScrmUserId]

	}

	total = cast.ToInt(count)
	return out, total, nil

}

func (h *DisFansService) EditDisFansProtectSetting(in distribution_vo.DistributorFansProtect) (err error) {

	logPrefix := fmt.Sprintf("设置粉丝保护期-入参%s", utils.InterfaceToJSON(in))
	log.Info(logPrefix)
	h.Begin()
	defer h.Close()
	data := struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	}{}
	name := fmt.Sprintf(cachekey.ProtectDay, in.OrgId)
	exists, err := h.Session.Table("upetmart.upet_setting").Where("name=?", name).Get(&data)
	if err != nil {
		log.Errorf("%s-获取数据失败-错误为%s", logPrefix, err.Error())
		return errors.New("获取数据失败")
	}
	if exists {
		if _, err := h.Session.Table("upetmart.upet_setting").Where("name=?", name).Cols("value").Update(map[string]interface{}{"value": in.ProtectDay}); err != nil {
			log.Errorf("%s-更新粉丝保护期失败-%s", logPrefix, err.Error())
			return errors.New("更新粉丝保护期失败")
		}
	} else {
		data.Name = name
		data.Value = cast.ToString(in.ProtectDay)

		if _, err := h.Session.Table("upetmart.upet_setting").Insert(&data); err != nil {
			log.Errorf("%s-插入粉丝保护期天数配置失败-%s", logPrefix, err.Error())
			return errors.New("插入粉丝保护期天数配置失败")
		}
	}
	if in.HasProtect != 1 {
		//前一分钟
		t := time.Now().Add(-1 * time.Minute).Format(utils.DateTimeLayout)
		if _, err := h.Session.Table("dis_distributor_fans").Cols("expire_time").Where("expire_time>=?", time.Now().Format(utils.DateTimeLayout)).Update(map[string]interface{}{"expire_time": t}); err != nil {
			log.Errorf("%s-更新历史粉丝保护期为过期失败-错误为%s", logPrefix, err.Error())

		}
	}
	return nil

}

func (h *DisFansService) GetDisFansProtectSetting(orgId int) (out distribution_vo.DistributorFansProtect, err error) {
	h.Begin()
	defer h.Close()
	name := fmt.Sprintf(cachekey.ProtectDay, orgId)
	data := struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	}{}
	out.OrgId = orgId
	if _, err := h.Session.Table("upetmart.upet_setting").Where("name=?", name).Get(&data); err != nil {
		log.Errorf("获取粉丝保护期失败%d-错误为%s", orgId, err.Error())
		return out, errors.New("获取粉丝保护期失败")
	}

	out.ProtectDay = cast.ToInt(data.Value)
	if cast.ToInt(out.ProtectDay) > 0 {
		out.HasProtect = 1
	} else {
		out.HasProtect = 2
		out.ProtectDay = 0
	}
	return out, nil

}

// 查询用户绑定有效分销员信息
func (h *DisFansService) GetBindDistributor(memberId int) (out []distribution_vo.DisDistributorFans, err error) {
	h.Begin()
	defer h.Close()
	out = make([]distribution_vo.DisDistributorFans, 0)
	session := h.Session
	err = session.Table("eshop.dis_distributor_fans").Where("member_id=?", memberId).
		Where("expire_time>=?", time.Now().Format(utils.DateTimeLayout)).Find(&out)
	if err != nil {
		log.Errorf("获取分销员信息失败-错误为%s", err.Error())
		return out, errors.New("获取分销员信息失败")
	}

	return
}
