package services

import (
	marketing_po "eShop/domain/marketing-po"
	"eShop/infra/log"
	"eShop/services/common"
	viewmodel "eShop/view-model"
	marketing_vo "eShop/view-model/marketing-vo"
	"fmt"

	_ "github.com/go-sql-driver/mysql"

	"github.com/stretchr/testify/assert"

	"testing"
)

func TestCouponService_SaveCoupon(t *testing.T) {
	// 初始化必要的依赖
	log.Init()
	type fields struct {
		BaseService common.BaseService
	}

	tests := []struct {
		name    string
		fields  fields
		param   *marketing_vo.SaveCouponReq
		wantErr string
	}{
		{
			name: "成功保存-满减券",
			param: &marketing_vo.SaveCouponReq{
				ChainId:      530185752454150978,
				StoreId:      "530236368643759103",
				Type:         1, // 满减
				Name:         "满100减10元",
				TotalCount:   1000,
				ApplyProduct: 1,
				Threshold:    100,
				Discount:     10,
				BestOffer:    10,
				UseStartType: 1,
				WithinDay:    30,
				StartTime:    "2024-12-27 00:00:00",
				EndTime:      "2025-01-31 23:59:59",
				PersonLimit:  2,
				Remark:       "测试优惠券",
			},
			wantErr: "",
		},
		{
			name: "成功保存-折扣券",
			param: &marketing_vo.SaveCouponReq{
				ChainId:      530185752454150978,
				StoreId:      "530236368643759103",
				Type:         2, // 折扣
				Name:         "满100打8折",
				TotalCount:   500,
				ApplyProduct: 2, // 指定商品
				SkuIds:       "100095001, 100095002",
				Threshold:    100, // 满100
				Discount:     8,   // 8折
				BestOffer:    50,  // 最高优惠50元
				UseStartType: 2,   // 指定时间生效
				StartTime:    "2024-12-27 00:00:00",
				EndTime:      "2025-01-31 23:59:59",
				PersonLimit:  1, // 每人限领1张
				Remark:       "折扣券测试",
			},
			wantErr: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := CouponService{
				BaseService: tt.fields.BaseService,
			}
			err := s.SaveCoupon(tt.param)
			if tt.wantErr != "" {
				assert.Contains(t, err.Error(), tt.wantErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCouponService_GetCouponList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		param *marketing_vo.CouponListReq
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		wantOut   []marketing_vo.CouponListRes
		wantTotal int64
		wantErr   assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "成功获取优惠券列表",
			args: args{
				param: &marketing_vo.CouponListReq{
					BasePageHttpRequest: viewmodel.BasePageHttpRequest{
						PageIndex: 1,
						PageSize:  10,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CouponService{
				BaseService: tt.fields.BaseService,
			}
			gotOut, gotTotal, err := s.GetCouponList(tt.args.param)
			if !tt.wantErr(t, err, fmt.Sprintf("GetCouponList(%v)", tt.args.param)) {
				return
			}
			assert.Equalf(t, tt.wantOut, gotOut, "GetCouponList(%v)", tt.args.param)
			assert.Equalf(t, tt.wantTotal, gotTotal, "GetCouponList(%v)", tt.args.param)
		})
	}
}

func TestCouponService_GetCouponReceiverList(t *testing.T) {
	// 初始化必要的依赖
	log.Init()
	type fields struct {
		BaseService common.BaseService
	}

	tests := []struct {
		name      string
		fields    fields
		param     *marketing_vo.CouponReceiverListReq
		wantTotal int64
		wantErr   bool
	}{
		{
			name: "成功获取优惠券领取列表",
			param: &marketing_vo.CouponReceiverListReq{
				BasePageHttpRequest: viewmodel.BasePageHttpRequest{
					PageIndex: 1,
					PageSize:  10,
				},
				CouponId: 1,
				//CustomerName: "张三",
				Status:  0,
				StoreId: "530236368643759103",
			},
			wantTotal: 0,
			wantErr:   false,
		},
		{
			name: "参数校验-优惠券ID必填",
			param: &marketing_vo.CouponReceiverListReq{
				BasePageHttpRequest: viewmodel.BasePageHttpRequest{
					PageIndex: 1,
					PageSize:  10,
				},
			},
			wantErr: true,
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CouponService{
				BaseService: tt.fields.BaseService,
			}
			gotList, gotTotal, err := s.GetCouponReceiverList(tt.param)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCouponReceiverList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				assert.NotNil(t, gotList)
				assert.GreaterOrEqual(t, gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestCouponService_StopCoupon(t *testing.T) {
	// 初始化必要的依赖
	log.Init()
	type fields struct {
		BaseService common.BaseService
	}

	tests := []struct {
		name    string
		fields  fields
		param   *marketing_vo.StopCouponReq
		wantErr bool
	}{
		{
			name: "成功停止单个客户优惠券",
			param: &marketing_vo.StopCouponReq{
				CouponId:   1,
				CustomerId: "540571614736509963",
				StoreId:    "530236368643759103",
			},
			wantErr: false,
		},
		{
			name: "成功停止所有客户优惠券",
			param: &marketing_vo.StopCouponReq{
				CouponId:   1,
				CustomerId: "",
				StoreId:    "530236368643759103",
			},
			wantErr: false,
		},
		{
			name: "优惠券不存在",
			param: &marketing_vo.StopCouponReq{
				CouponId:   999999,
				CustomerId: "540571614736509963",
				StoreId:    "530236368643759103",
			},
			wantErr: true,
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CouponService{
				BaseService: tt.fields.BaseService,
			}
			err := s.StopCoupon(tt.param)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopCoupon() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCouponService_IssueCoupon(t *testing.T) {
	// 初始化必要的依赖
	log.Init()
	type fields struct {
		BaseService common.BaseService
	}

	tests := []struct {
		name    string
		fields  fields
		param   *marketing_vo.IssueCouponReq
		storeId string
		wantErr bool
	}{
		{
			name: "成功发放优惠券",
			param: &marketing_vo.IssueCouponReq{
				CouponIds:  "1",
				CustomerId: "540571614736509963",
				Source:     1,
			},
			wantErr: false,
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CouponService{
				BaseService: tt.fields.BaseService,
			}
			err := s.IssueCoupon(tt.param)
			if (err != nil) != tt.wantErr {
				t.Errorf("IssueCoupon() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCouponService_GetUserCouponList(t *testing.T) {
	// 初始化必要的依赖
	log.Init()
	type fields struct {
		BaseService common.BaseService
	}

	tests := []struct {
		name      string
		fields    fields
		param     *marketing_vo.CouponUserListReq
		wantTotal int64
		wantErr   bool
	}{
		//所有商品可用
		//{
		//	name: "成功获取用户优惠券列表-全部",
		//	param: &marketing_vo.CouponUserListReq{
		//		StoreId:    "576534157590154085",
		//		CustomerId: "551611098026463436",
		//		PageIndex:  1,
		//		PageSize:   10,
		//	},
		//	wantTotal: 0,
		//	wantErr:   false,
		//},
		//指定商品查询
		{
			name: "成功获取用户优惠券列表-全部",
			param: &marketing_vo.CouponUserListReq{
				StoreId:    "576534157590154085",
				CustomerId: "551611098026463355",
				SkuIds:     "105510001",
				PageIndex:  1,
				PageSize:   10,
			},
			wantTotal: 0,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CouponService{
				BaseService: tt.fields.BaseService,
			}
			gotList, gotTotal, err := s.GetUserCouponList(tt.param)
			t.Log(gotList)
			t.Log(gotTotal)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserCouponList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				t.Errorf("IssueCoupon() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCouponService_SendIssueCouponNotification(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		coupon    *marketing_po.MarketingCoupon
		customer  *marketing_po.Customer
		storeName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "成功发送优惠券发放短信通知",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				coupon: &marketing_po.MarketingCoupon{
					Id:         90,
					Type:       1,
					StoreId:    "576534157590153850",
					Name:       "满100减10元",
					TotalCount: 100,
					Discount:   5,
				},
				customer: &marketing_po.Customer{
					Name:  "test",
					Phone: "15118811943",
				},
				storeName: "测试门店",
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CouponService{
				BaseService: tt.fields.BaseService,
			}
			s.SendIssueCouponNotification(tt.args.coupon, tt.args.customer, tt.args.storeName)
		})
	}
}
