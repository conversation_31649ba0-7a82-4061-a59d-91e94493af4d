package distribution_vo

import viewmodel "eShop/view-model"

// 企业信息表
type ScrmEnterprise struct {
	Id                  string `json:"id" xorm:"pk autoincr not null comment('id') BIGINT 'id'"`                                                                                                                //id
	Code                string `json:"code" xorm:"default '' comment('企业编码') VARCHAR(50) 'code'"`                                                                                                               //企业编码
	EnterpriseName      string `json:"enterprise_name" xorm:"not null default '' comment('企业名称') VARCHAR(128) 'enterprise_name'"`                                                                               //企业名称
	EnterpriseType      int    `json:"enterprise_type" xorm:"default NULL comment('类型（0企业 1个人）') TINYINT 'enterprise_type'"`                                                                                    //类型（0企业 1个人）
	MarketingChannel    int    `json:"marketing_channel" xorm:"default NULL comment('渠道（1:宠物医院,2:宠物店,3:军警犬渠道,4:电商,5:繁育场,6:本地生活,7:散客,8:内部往来,9:商超,10:宠物诊所,11:经销商,12:OMS,13:其他招投标）') TINYINT 'marketing_channel'"` //渠道（1:宠物医院,2:宠物店,3:军警犬渠道,4:电商,5:繁育场,6:本地生活,7:散客,8:内部往来,9:商超,10:宠物诊所,11:经销商,12:OMS,13:其他招投标）
	SettlementCycleType int    `json:"settlement_cycle_type" xorm:"default NULL comment('结算类型（1现结 2账期 3预收款）') TINYINT 'settlement_cycle_type'"`                                                                 //结算类型（1现结 2账期 3预收款）
	Province            string `json:"province" xorm:"not null default '' comment('省') VARCHAR(255) 'province'"`                                                                                                //省
	City                string `json:"city" xorm:"not null default '' comment('市') VARCHAR(255) 'city'"`                                                                                                        //市
	District            string `json:"district" xorm:"not null default '' comment('区') VARCHAR(255) 'district'"`                                                                                                //区
	Address             string `json:"address" xorm:"default '' comment('详细地址') VARCHAR(255) 'address'"`                                                                                                        //详细地址
	Phone               string `json:"phone" xorm:"default '' comment('手机号') VARCHAR(20) 'phone'"`                                                                                                              //手机号
	SocialCreditCode    string `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`                                                                               //统一社会信用代码
	IdCardNo            string `json:"id_card_no" xorm:"default NULL comment('身份证号') VARCHAR(20) 'id_card_no'"`                                                                                                 //身份证号
	EnterpriseStatus    int    `json:"enterprise_status" xorm:"not null default 1 comment('状态（0停用 1启用）') TINYINT(1) 'enterprise_status'"`                                                                       //状态（0停用 1启用）
	DataSource          int    `json:"data_source" xorm:"not null default 1 comment('来源：1、自建   2 云订货') TINYINT 'data_source'"`                                                                                  //来源：1、自建   2 云订货
	LastTrajectoryTime  string `json:"last_trajectory_time" xorm:"default NULL comment('最后跟进时间') DATETIME 'last_trajectory_time'"`                                                                              //最后跟进时间
	CreateTime          string `json:"create_time" xorm:"not null default CURRENT_TIMESTAMP comment('创建时间') DATETIME 'create_time'"`                                                                            //创建时间
	CreateUser          string `json:"create_user" xorm:"not null default '' comment('创建人') VARCHAR(20) 'create_user'"`                                                                                         //创建人
	CreateUserId        string `json:"create_user_id" xorm:"not null comment('创建人id') BIGINT 'create_user_id'"`                                                                                                 //创建人id
	UpdateTime          string `json:"update_time" xorm:"not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment('更新时间') DATETIME 'update_time'"`                                                //更新时间
	UpdateUser          string `json:"update_user" xorm:"not null default '' comment('更新人') VARCHAR(20) 'update_user'"`                                                                                         //更新人
	UpdateUserId        string `json:"update_user_id" xorm:"not null comment('更新人id') BIGINT 'update_user_id'"`
	ShopId              int    `json:"shop_id" xorm:"default 0 comment('分销店铺') INT 'shop_id'"` //分销店铺//更新人id
}

// 返回企业信息
type EnterpriseRes struct {
	viewmodel.BasePageHttpResponse
	Data []ScrmEnterprise `json:"data"`
}

// 企业名称查询
type EnterpriseReq struct {
	//分页信息
	viewmodel.BasePageHttpRequest
	EnterpriseName string `json:"enterprise_name"`
}
