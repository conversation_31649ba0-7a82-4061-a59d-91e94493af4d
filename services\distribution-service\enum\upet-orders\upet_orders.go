package upetorders

// 电商的订单状态upet_orders.order_state  订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;50:部分发货;
// todo 等以后有关于电商的服务，放在那里取
const (
	StateCancel   = 0  //已取消
	StateNew      = 10 // 待付款
	StatePay      = 20 // 待发货
	StateSend     = 30 //待收货
	StateSuccess  = 40 //已完成
	StatePartSend = 50 // 部分发货

	SystemOut = 0
	SystemIn  = 1
)

var StateMap = map[int]string{
	StateCancel:   "已取消",
	StateNew:      "待付款",
	StatePay:      "待发货",
	StateSend:     "待收货",
	StateSuccess:  "已完成",
	StatePartSend: "部分发货",
}

var InSystemMap = map[int]string{
	SystemOut: "体系外",
	SystemIn:  "体系内",
}
