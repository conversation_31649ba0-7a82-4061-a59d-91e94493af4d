package services

import (
	"context"
	"eShop/domain/external-po/offline"
	omnibus_po2 "eShop/domain/omnibus-po"
	product_po2 "eShop/domain/product-po"
	"eShop/infra/cache"
	"eShop/infra/enum"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/services/inventory-service/iobound"

	omnibus_service "eShop/services/omnibus-service/services"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	inventoryVO "eShop/view-model/inventory-vo/inventory"
	omnibus_vo2 "eShop/view-model/omnibus-vo"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

type ProductService struct {
	common.BaseService
	Request *http.Request
	JwtInfo *jwtauth.XCShopPayload
}

// 管理后台 - 添加单个连锁商品
func (s ProductService) AddChainProduct(in vo.ChainProduct) (err error) {
	logPrefix := "管理后台 - 添加单个连锁商品===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in), "jwtInfo:", utils.InterfaceToJSON(s.JwtInfo))

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	// 第一步： 数据校验
	if err = ChainProductValidate(session, &in); err != nil {
		log.Error(logPrefix, "数据校验失败，err=", err.Error())
		return
	}

	// 第二步： 插入pro_product数据、pro_sku、pro_product_channel、pro_product_channel_attr 数据
	session.Begin()
	// 如果开启多规格，则只需要插入pro_sku数据
	if in.DataSource == 2 {
		//加一个redis锁，防止并发加入连锁商品
		redisKey := fmt.Sprintf(cachekey.AddChainProductLock, in.Product.ChainId, in.Product.Name)
		var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
		setNxReslt := mCache.TryLock(string(cache_source.EShop), redisKey, time.Second*10)
		if !setNxReslt {
			log.Errorf("%s-设置redis锁(%s)失败", logPrefix, redisKey)
			return errors.New("设置redis锁失败")
		}
		defer mCache.Delete(string(cache_source.EShop), redisKey)

		// 根据商品名称 查询商品id
		where := map[string]interface{}{
			"productName": in.Product.Name,
			"chainId":     in.Product.ChainId,
			"outType":     2,
		}
		_, _, productNameMap, err := product_po2.GetProductMapInfo(session, where)
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "根据商品名称查询商品信息失败，err=", err.Error())
			return errors.New("根据商品名称查询商品信息失败")
		}
		// 如果查询不到商品，则需要添加连锁商品信息以及第一个sku信息
		if len(productNameMap) == 0 {
			goto addChainProduct
		}
		// 获取该连锁商品已经有几个sku
		skuQuery := product_po2.SkuQuery{
			ProductId: productNameMap[in.Product.Name].Id,
			OrderBy:   "id desc",
		}
		skuMap, _, err := new(product_po2.ProSku).GetSkuMapInfo(session, skuQuery)
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "获取连锁商品sku信息失败，err=", err.Error())
			return errors.New("获取连锁商品sku信息失败")
		}

		for k := range in.Sku {
			in.Sku[k].ProductId = productNameMap[in.Product.Name].Id
			in.Sku[k].Id = skuMap[productNameMap[in.Product.Name].Id][0].Id + k + 1
		}

		_, err = session.Table("eshop.pro_sku").Insert(&in.Sku)
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "添加连锁商品sku信息失败，err=", err.Error())
			return errors.New("添加连锁商品sku信息失败")
		}
		if err = session.Commit(); err != nil {
			session.Rollback()
			log.Error(logPrefix, "提交连锁商品信息失败，err=", err.Error())
			return errors.New("提交连锁商品信息失败")
		}
		return nil

	}
addChainProduct:

	updateData := product_po2.CopyProProductForUpdate(in.Product)

	updateData.ProductType = product_po2.ProductTypeGoods
	row, err := session.Table("eshop.pro_product").Omit("del_date").Insert(&updateData)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "添加连锁商品失败，err=", err.Error())
		err = errors.New("添加连锁商品失败")
		return
	}
	if row != 1 {
		session.Rollback()
		log.Error(logPrefix, "添加连锁商品失败，影响条数不等于1")
		err = errors.New("添加连锁商品失败")
		return
	}

	for k := range in.Sku {
		in.Sku[k].ProductId = updateData.Id
		in.Sku[k].Id = cast.ToInt(fmt.Sprintf("%d%s", updateData.Id, "000")) + k + 1
	}
	_, err = session.Table("eshop.pro_sku").Insert(&in.Sku)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "添加连锁商品sku信息失败，err=", err.Error())
		err = errors.New("添加连锁商品sku信息失败")
		return
	}

	for k := range in.ProductChannel {
		in.ProductChannel[k].ProductId = updateData.Id
	}
	if len(in.ProductChannel) > 0 {
		_, err = session.Table("eshop.pro_product_channel").Insert(&in.ProductChannel)
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "添加连锁商品渠道类目信息失败，err=", err.Error())
			err = errors.New("添加连锁商品渠道类目信息失败")
			return
		}
	}

	for k := range in.ProductChannelAttr {
		in.ProductChannelAttr[k].ProductId = updateData.Id
	}
	if len(in.ProductChannelAttr) > 0 {
		_, err = session.Table("eshop.pro_product_channel_attr").Insert(&in.ProductChannelAttr)
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "添加连锁商品渠道类目属性信息失败，err=", err.Error())
			err = errors.New("添加连锁商品渠道类目属性信息失败")
			return
		}
	}

	if err = session.Commit(); err != nil {
		session.Rollback()
		log.Error(logPrefix, "提交连锁商品信息失败，err=", err.Error())
		err = errors.New("提交连锁商品信息失败")
		return
	}
	// 如果连锁下只有一个店铺， 则创建连锁商品时，自动下发
	tenantModel := offline.TTenant{}
	tenantList, err := tenantModel.GetChainStores(session, cast.ToInt64(in.Product.ChainId))
	if err != nil {
		log.Error(logPrefix, "获取连锁店铺列表失败，err=", err.Error())
		return errors.New("获取连锁店铺列表失败")
	}
	if len(tenantList) == 1 {
		in.PushStoreIds = cast.ToString(tenantList[0].Id)
	}
	// 需要下发店铺列表
	if len(in.PushStoreIds) > 0 {
		pushStoreIdSli := strings.Split(in.PushStoreIds, ",")
		// 获取该用户在该连锁下所有有权限的店铺列表
		omnibusService := omnibus_service.StoreService{}
		req := omnibus_vo2.GetStoreListReq{
			ChainId:   cast.ToInt64(in.Product.ChainId),
			UserId:    cast.ToInt64(in.Product.CreatedBy),
			ProductId: updateData.Id,
		}
		if storeList, err := omnibusService.GetStoreList(req); err != nil {
			log.Error(logPrefix, "获取可下发商品列表，err=", err.Error())
		} else if len(storeList) > 0 {
			storeListMap := make(map[string]omnibus_vo2.GetStoreList)
			for _, v := range storeList {
				storeListMap[v.StoreId] = v
			}

			for _, storeId := range pushStoreIdSli {
				if info, ok := storeListMap[storeId]; ok {
					s.BatchPushChainProduct(vo.BatchPushChainProductReq{
						ProductIds: cast.ToString(updateData.Id),
						StoreIds:   cast.ToString(info.StoreId),
						CreatedId:  cast.ToInt64(in.Product.CreatedBy),
					})
				}
			}
		}
	}

	return
}

// 管理后台 - 获取单个连锁商品
func (s ProductService) GetChainProductInfo(in vo.GetChainProductReq) (out vo.ChainProduct, err error) {
	logPrefix := "管理后台 - 获取单个连锁商品===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	// 查询 连锁库 商品信息
	exists, err := session.Table("eshop.pro_product").Where("chain_id=?", in.ChainId).Where("id=?", in.ProductId).Where("product_type!=?", product_po2.ProductTypeService).Where("product_type!=?", product_po2.ProductTypeLive).Get(&out.Product)
	if err != nil {
		log.Error(logPrefix, "查找连锁商品信息失败，err=", err.Error())
		err = errors.New("查找连锁商品信息失败")
		return
	}
	if !exists {
		log.Error(logPrefix, "未找到该连锁商品信息")
		err = errors.New("未找到该连锁商品信息")
		return
	}
	// 查询分类名称
	cateNames, err := new(product_po2.ProCategory).GetCategoryNameByChildId(s.Engine, []int{out.Product.CategoryId})
	if err != nil {
		log.Error(logPrefix, "查询前台分类名称失败，err=", err.Error())
		err = errors.New("查询前台分类名称失败")
		return
	}

	if cateName, ok := cateNames[out.Product.CategoryId]; ok {
		out.Product.CategoryNavOnline = cateName.ParentName + ">" + cateName.ChildName
	}

	// 查询 连锁库 商品渠道信息（目前仅类目信息）
	out.ProductChannel = make([]product_po2.ProProductChannel, 0)
	err = session.Table("eshop.pro_product_channel").Where("product_id=?", in.ProductId).Find(&out.ProductChannel)
	if err != nil {
		log.Error(logPrefix, "查询连锁库-商品渠道信息失败，err=", err.Error())
		err = errors.New("查询连锁库-商品渠道信息失败")
		return
	}

	// 查询 连锁库 商品渠道属性（不同渠道商品类目对应不同的商品渠道属性）
	out.ProductChannelAttr = make([]product_po2.ProProductChannelAttr, 0)
	err = session.Table("eshop.pro_product_channel_attr").Where("product_id=?", in.ProductId).Find(&out.ProductChannelAttr)
	if err != nil {
		log.Error(logPrefix, "查询连锁库-商品渠道属性信息失败，err=", err.Error())
		err = errors.New("查询连锁库-商品渠道属性信息失败")
		return
	}

	// 查询 连锁库-商品规格信息
	out.Sku = make([]product_po2.ProSku, 0)
	err = session.Table("eshop.pro_sku").Where("product_id=?", in.ProductId).Where("is_del=0").Find(&out.Sku)
	if err != nil {
		log.Error(logPrefix, "查询连锁库-商品SKU信息失败，err=", err.Error())
		err = errors.New("查询连锁库-商品SKU信息失败")
		return
	}
	return
}
func NewSellText(newSellStr string) string {
	NewSellSli := strings.Split(newSellStr, ",")
	NewSellText := ""
	for _, v := range NewSellSli {
		NewSellText += common.ChannelIdMap[cast.ToInt(v)]
		NewSellText += ","
	}
	return strings.Trim(NewSellText, ",")

}

// 管理后台 - 获取连锁商品列表(以spu为维度来查列表)
func (s ProductService) FindChainProductList(in vo.FindChainProductListReq) (out []vo.FindChainProductList, total int64, err error) {
	logPrefix := "管理后台 - 获取连锁商品列表===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	s.Begin()
	defer s.Close()
	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	session := s.Engine.NewSession()
	defer session.Close()

	// 排除服务和活体商品
	sel := `a.id as product_id,a.category_id,c.name as shop_cate_second,d.name as shop_cate_first,a.pic,a.name,a.category_nav,a.new_sell,a.new_sell_str,a.brand_name,a.supplier_name`
	session.Table("eshop.pro_product").Alias("a").
		Join("left", "eshop.pro_sku b", "a.id=b.product_id").
		Join("left", "eshop.pro_category c", "c.id=a.category_id").
		Join("left", "eshop.pro_category d", "d.id=c.parent_id").
		Select(sel).Where("a.is_del=0").Where("a.chain_id=?", in.ChainId).Where("a.product_type!=?", product_po2.ProductTypeService).
		Where("a.product_type!=?", product_po2.ProductTypeLive).
		Where("b.is_del=0")

	if in.SearchBy != "" {
		session.Where("b.bar_code=? or a.name like ? or a.id=?", in.SearchBy, "%"+in.SearchBy+"%", cast.ToInt(in.SearchBy))
	}

	//渠道， 这里指该商品补齐了第三方的类目以及类目属性
	// if in.ChannelId != 0 {
	// 	//select id,new_sell_str from eshop.pro_product pp where  new_sell_str   REGEXP '(^|,)100(,|$)';
	// 	session.Where("a.new_sell_str REGEXP  ?", "(^|,)"+cast.ToString(in.ChannelId)+"(,|$)")
	// }

	if in.NewSell > 0 {
		session.Where("a.new_sell=?", in.NewSell)
	}

	// 根据后端分类来查找商品
	// 二级分类有值， 直接搜索
	if in.CategoryIdOffline > 0 {
		session.Where("a.category_id_offline=?", cast.ToInt64(in.CategoryIdOffline))
	} else {
		//只传了一级分类， 则查出一级分类下的所有二级分类
		if in.CategoryIdOfflineFirst > 0 {
			cate := product_po2.ProCategory{}
			if childCateOfflineIds, e := cate.GetChildrenByParentId(s.Engine, in.CategoryIdOfflineFirst); e != nil {
				log.Error(logPrefix, "获取后端分类的子孩子失败，err=", e.Error())
				err = errors.New("获取后端分类的子孩子")
				return
			} else if len(childCateOfflineIds) > 0 {
				session.In("a.category_id_offline", childCateOfflineIds)
			}

		}
	}

	out = make([]vo.FindChainProductList, 0)
	// 以eshop.pro_product为主表查询连锁商品列表
	if total, err = session.OrderBy("a.create_date desc").Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).GroupBy("a.id").FindAndCount(&out); err != nil {
		log.Error(logPrefix, "查询连锁商品列表失败，err=", err.Error())
		err = errors.New("查询连锁商品列表失败")
		return
	}

	// 计算该连锁下面总共包含多少店铺数
	totalStore := 0
	store := omnibus_po2.Store{}
	stores, _, err := store.GetStores(context.Background(), s.Engine, map[string]interface{}{"userId": s.JwtInfo.UserId})
	if err != nil {
		log.Error(logPrefix, "获取用户店铺列表失败，err=", err.Error())
		err = errors.New("获取用户店铺列表失败")
		return
	}
	// 获取用户有权限的店铺列表
	userCanStoreIds := make([]string, 0)
	for _, v := range stores {
		userCanStoreIds = append(userCanStoreIds, v.FinanceCode)
	}
	totalStore = len(stores)

	productIds := make([]int, 0, len(out))

	for _, v := range out {
		productIds = append(productIds, v.ProductId)
	}
	productSkuMap := make(map[int][]product_po2.ProSku)
	ProductPushedStoresMap := make(map[int][]product_po2.ProProductStore)
	ProductChannelMap := make(map[int]map[int]product_po2.ProProductChannel)
	if len(productIds) > 0 {
		// 根据商品ids 获取商品已下发店铺列表
		productStoreModel := product_po2.NewProProductStoreModel()
		ProductPushedStoresMap, _, err = productStoreModel.QueryProductStores(context.Background(), s.Engine, product_po2.QueryProductStoresReq{ProductIds: productIds, StoreIds: userCanStoreIds})
		if err != nil {
			log.Error(logPrefix, "获取商品下发店铺列表失败，err=", err.Error())
			err = errors.New("获取商品下发店铺列表失败")
			return
		}
		// 根据商品ids 获取商品sku信息
		skuQuery := product_po2.SkuQuery{
			ProductIds: productIds,
		}
		productSkuMap, _, err = new(product_po2.ProSku).GetSkuMapInfo(session, skuQuery)
		if err != nil {
			log.Error(logPrefix, "获取商品sku信息失败，err=", err.Error())
			err = errors.New("获取商品sku信息失败")
			return
		}

		if _, ProductChannelMap, err = product_po2.GetProductChannelInfo(session, map[string]interface{}{"productIds": productIds, "outType": 1}); err != nil {
			log.Error(logPrefix, "获取商品渠道类目信息失败，err=", err.Error())
			err = errors.New("获取商品渠道类目信息失败")
			return
		}

	}

	for k, v := range out {
		out[k].PushStoreStr = fmt.Sprintf("%d/%d", len(ProductPushedStoresMap[v.ProductId]), totalStore)
		out[k].Sku = productSkuMap[v.ProductId]
		out[k].CategoryThirdNameMt = ProductChannelMap[v.ProductId][common.ChannelIdMT].CategoryThirdName
		out[k].CategoryThirdNameElm = ProductChannelMap[v.ProductId][common.ChannelIdELM].CategoryThirdName
		out[k].CategoryThirdNameJddj = ProductChannelMap[v.ProductId][common.ChannelIdJD].CategoryThirdName
	}
	return
}

// 管理后台 - 批量下发连锁商品到门店（以商品的维度下发到多门店）
// a： 往eshop.pro_product_store下发表插入数据
// b： 往eshop.pro_product_store_spu 插入门店商品spu信息
// c: 往eshop.pro_product_store_info 插入门店商品sku信息
// 备注：  线下门店线下门店端 的商品 直接铺品上架（这里只需将pro_product_store_info.is_distribution改为已铺品，up_down_state改为已上架）
func (s ProductService) BatchPushChainProduct(in vo.BatchPushChainProductReq) (err error) {
	logPrefix := "管理后台 - 批量下发连锁商品到门店===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	productIds := strings.Split(in.ProductIds, ",")
	storeIds := strings.Split(in.StoreIds, ",")
	s.Begin()
	defer s.Close()

	if s.JwtInfo == nil || s.JwtInfo.UserId == "" {
		err = errors.New("获取用户信息失败")
		return
	}
	// 获取正确的连锁id
	// 接口操作时， 不能操作连锁B的商品来下发到自营店铺
	// 接口操作时， 代运营连锁 只能下发自己的商品到店铺
	shopChainId := s.JwtInfo.ChainId
	chainId := s.JwtInfo.ChainId
	if cast.ToInt64(s.JwtInfo.SourceChainId) > 0 {
		chainId = s.JwtInfo.SourceChainId
	}
	// 获取门店信息
	storeModel := omnibus_po2.NewStore()
	_, storeMap, err := storeModel.GetStores(context.Background(), s.Engine, map[string]interface{}{
		"FinanceCodes": storeIds,
		"outType":      1,
		"userId":       s.JwtInfo.UserId,
	})
	if err != nil {
		log.Error(logPrefix, "获取门店信息失败，err=", err.Error())
		return errors.New("获取门店信息失败")
	}

	// 校验入参店铺id是该用户有权限下发的店铺
	for _, v := range storeIds {
		if _, ok := storeMap[v]; !ok {
			log.Error(logPrefix, "门店信息不存在，门店id=", utils.InterfaceToJSON(v))
			return errors.New("门店信息不存在")
		}
	}

	session := s.Engine.NewSession()
	defer session.Close()

	// 查询店铺的仓库信息
	WarehouseRelationShopMap, err := new(omnibus_po2.WarehouseRelationShop).GetWarehouseMapInfo(session, omnibus_po2.GetWarehouseMapInfoReq{ShopIds: storeIds})
	if err != nil {
		log.Error(logPrefix, "获取店铺的仓库信息失败", err.Error())
		return fmt.Errorf("获取店铺的仓库信息失败%s", err.Error())
	}

	ProdutStore := make([]product_po2.ProProductStore, 0)
	productIdSli := make([]int, 0)
	//   下发前 检查 该商品是否开启了相应渠道的下发  以及店铺是否有第三方店铺id
	for _, productId := range productIds {
		productIdSli = append(productIdSli, cast.ToInt(productId))
		for _, storeId := range storeIds {
			ProdutStore = append(ProdutStore, product_po2.ProProductStore{
				StoreId:   string(storeId),
				ProductId: cast.ToInt(productId),
			})
		}
	}
	if len(ProdutStore) == 0 {
		log.Error(logPrefix, "需要下发的商品为空")
		err = errors.New("需要下发的商品为空")
		return
	}

	productMap, err := GetProductMapInfo(session, productIdSli, logPrefix)
	if err != nil {
		log.Error(logPrefix, "获取商品信息失败，err=", err.Error())
		err = errors.New("获取商品信息失败")
		return
	}
	for _, v := range productMap {
		for _, vv := range v {
			if vv.ChainId != cast.ToInt64(chainId) {
				log.Error(logPrefix, "只能下发自己连锁下面的商品.", "商品信息", utils.InterfaceToJSON(v), "jwtInfo信息", utils.InterfaceToJSON(s.JwtInfo))
				return errors.New("只能下发自己连锁下面的商品")
			}
		}
	}

	//  第一步：下发表pro_product_store插入商品id与门店的关系
	session.Begin()
	//  查看下发是否已经有数据了 有了的话 ，就不用插入了。
	insertData0 := make([]product_po2.ProProductStore, 0)
	m0, err := product_po2.GetPushedProductStore(s.Engine, map[string]interface{}{"productIds": productIds, "storeIds": storeIds})
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "获取已下发关系数据失败，err=", err.Error())
		err = errors.New("获取已下发关系数据失败")
		return
	}
	for _, v := range ProdutStore {
		k := fmt.Sprintf("%s_%d", v.StoreId, v.ProductId)
		if _, ok := m0[k]; !ok {
			insertData0 = append(insertData0, v)
		}
	}
	if len(insertData0) > 0 {
		if _, err = session.Table("eshop.pro_product_store").Insert(&insertData0); err != nil {
			session.Rollback()
			log.Error(logPrefix, "插入已下发关系数据失败，err=", err.Error())
			err = errors.New("插入已下发关系数据失败")
			return
		}
	}

	// 第二步： 组织门店商品spu数据和门店商品数据
	ProductStoreSpu := make([]product_po2.ProProductStoreSpu, 0)   // 组织门店商品spu数据
	ProductStoreInfo := make([]product_po2.ProProductStoreInfo, 0) // 组织门店商品数据
	for _, v := range ProdutStore {
		if _, ok := productMap[v.ProductId]; !ok {
			log.Info(logPrefix, "批量下发商品时，商品数据不存在，商品ID=", v.ProductId)
			continue
		}
		//获取spu的 线下门店分类id 和 线上分类id
		ChannelCategoryIdOffline, ChannelCategoryId := 0, 0
		for _, vv := range productMap[v.ProductId] {
			ChannelCategoryIdOffline = vv.CategoryIdOffline
			ChannelCategoryId = vv.CategoryId
			break
		}

		// 店铺是否被代运营:1是，0否
		storeIsAuthorized := false
		if storeMap[v.StoreId].AuthorizedChainId > 0 {
			storeIsAuthorized = true
		}
		// 账号是否是代运营账号:1是，0否
		accountAgent := false
		// 店铺被代运营了， 且账号是代运营账号
		if storeIsAuthorized && storeMap[v.StoreId].SourceChainId == storeMap[v.StoreId].AuthorizedChainId {
			accountAgent = true
		}
		for channelId := range common.ChannelIdMap {
			if channelId == common.ChannelIdJD {
				continue
			}

			// 店铺被代运营了， 如果是自己的员工账号点击下发， 不能下发新零售
			if storeIsAuthorized && !accountAgent && (channelId == common.ChannelIdMT || channelId == common.ChannelIdELM) {
				continue
			}
			// 店铺被代运营了， 如果是代运营的员工账号点击下发， 不能下发线下门店
			if storeIsAuthorized && accountAgent && (channelId == common.ChannelIdOfflineShop || channelId == common.ChannelIdWeChatApp) {
				continue
			}

			spuInfo := product_po2.ProProductStoreSpu{
				ProductId:   v.ProductId,
				StoreId:     v.StoreId,
				ChannelId:   channelId,
				ProductType: product_po2.ProductTypeGoods,
			}
			if channelId == common.ChannelIdOfflineShop {
				spuInfo.ChannelCategoryId = ChannelCategoryIdOffline
			} else {
				spuInfo.ChannelCategoryId = ChannelCategoryId
			}
			ProductStoreSpu = append(ProductStoreSpu, spuInfo)
		}

		for _, vv := range productMap[v.ProductId] {

			priceMap := make(map[int]int)
			priceMap[common.ChannelIdOfflineShop] = vv.StorePrice
			priceMap[common.ChannelIdELM] = vv.ElmPrice
			priceMap[common.ChannelIdMT] = vv.MtPrice
			priceMap[common.ChannelIdWeChatApp] = vv.XcxPrice

			ProProductStoreInfo := product_po2.ProProductStoreInfo{
				ProductId:   v.ProductId,
				SkuId:       vv.SkuId,
				StoreId:     v.StoreId,
				Status:      proproductstoreinfo.StatusNormal,
				ProductType: product_po2.ProductTypeGoods,
			}
			for channelId := range common.ChannelIdMap {
				if channelId == common.ChannelIdJD {
					continue
				}

				// 店铺被代运营了， 如果是自己的员工账号点击下发， 不能下发新零售
				if storeIsAuthorized && !accountAgent && (channelId == common.ChannelIdMT || channelId == common.ChannelIdELM) {
					continue
				}
				// 店铺被代运营了， 如果是代运营的员工账号点击下发， 不能下发线下门店
				if storeIsAuthorized && accountAgent && (channelId == common.ChannelIdOfflineShop || channelId == common.ChannelIdWeChatApp) {
					continue
				}
				if channelId == common.ChannelIdOfflineShop {
					ProProductStoreInfo.UpDownState = proproductstoreinfo.UpDownStateUp
					ProProductStoreInfo.IsDistribution = proproductstoreinfo.IsDistributionLaunched
				} else if channelId == common.ChannelIdWeChatApp {
					ProProductStoreInfo.UpDownState = proproductstoreinfo.UpDownStateDown
					ProProductStoreInfo.IsDistribution = proproductstoreinfo.IsDistributionLaunched
				} else {
					ProProductStoreInfo.UpDownState = proproductstoreinfo.UpDownStateDown
					ProProductStoreInfo.IsDistribution = proproductstoreinfo.IsDistributionUnLaunch
				}
				ProProductStoreInfo.ChannelId = channelId
				ProProductStoreInfo.RetailPrice = priceMap[channelId]
				ProductStoreInfo = append(ProductStoreInfo, ProProductStoreInfo)
			}
		}

	}

	//  根据条件 product_id、store_id、channel_id 如果存在，则不用处理， 不存在， 则插入到铺品表
	insertData := make([]product_po2.ProProductStoreInfo, 0)
	insertSpuData := make([]product_po2.ProProductStoreSpu, 0)
	ProductStoreInfoMap, err := product_po2.GetProductStoreInfoExist(s.Engine, map[string]interface{}{"productIds": productIds, "storeIds": storeIds, "excludeProductTypes": []int{product_po2.ProductTypeService, product_po2.ProductTypeLive}})
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "获取已下发的数据失败，err=", err.Error())
		err = errors.New("获取已下发的数据失败")
		return
	}

	spuModel := product_po2.ProProductStoreSpu{}
	spuMap, _, err := spuModel.QueryMap(session, product_po2.SpuQuery{
		ProductIds:          productIdSli,
		StoreIds:            storeIds,
		ExcludeProductTypes: []int{product_po2.ProductTypeService, product_po2.ProductTypeLive},
	})
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "查询商品SPU信息失败，err=", err.Error())
		err = errors.New("查询商品SPU信息失败")
		return
	}
	initInventoryMap := make(map[string]inventoryVO.InventoryInitCommand) // map[店铺id_仓库id]
	initInventoryDetailMap := make(map[string]int)                        //[店铺id_仓库id_skuid]
	for _, v := range ProductStoreInfo {
		k := fmt.Sprintf("%d_%s_%d_%d", v.ChannelId, v.StoreId, v.ProductId, v.SkuId)
		warehouseId := WarehouseRelationShopMap[fmt.Sprintf("%s_%d", v.StoreId, v.ChannelId)].WarehouseId
		if warehouseId <= 0 {
			continue
		}
		initInventory, ok := initInventoryMap[fmt.Sprintf("%s_%d", v.StoreId, warehouseId)]
		if !ok {
			initInventory = inventoryVO.InventoryInitCommand{
				ChainId:     cast.ToInt64(shopChainId),
				StoreId:     v.StoreId,
				WarehouseId: WarehouseRelationShopMap[fmt.Sprintf("%s_%d", v.StoreId, v.ChannelId)].WarehouseId,
				BoundType:   1,
				Operator:    s.JwtInfo.UserName,
				Details:     make([]inventoryVO.InventoryInitDetailCommand, 0),
			}
		}

		if _, ok := initInventoryDetailMap[fmt.Sprintf("%s_%d_%d", v.StoreId, warehouseId, v.SkuId)]; !ok {
			initInventoryDetailMap[fmt.Sprintf("%s_%d_%d", v.StoreId, warehouseId, v.SkuId)] = 1
			initInventory.Details = append(initInventory.Details, inventoryVO.InventoryInitDetailCommand{
				ProductId:           v.ProductId,
				SkuId:               v.SkuId,
				ProductName:         productMap[v.ProductId][v.SkuId].ProductName,
				ProductType:         productMap[v.ProductId][v.SkuId].ProductType,
				BarCode:             productMap[v.ProductId][v.SkuId].BarCode,
				ProductCategoryPath: productMap[v.ProductId][v.SkuId].ProductCategoryPath,
				AvgCostPrice:        productMap[v.ProductId][v.SkuId].BasicPrice,
			})

		}
		initInventoryMap[fmt.Sprintf("%s_%d", v.StoreId, warehouseId)] = initInventory
		if _, ok := ProductStoreInfoMap[k]; !ok {
			insertData = append(insertData, v)
		}
	}
	for _, v := range ProductStoreSpu {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, v.StoreId, v.ProductId)
		if _, ok := spuMap[k]; !ok {
			insertSpuData = append(insertSpuData, v)
		}
	}

	if len(insertData) > 0 {
		if _, err = session.Table("eshop.pro_product_store_info").Insert(&insertData); err != nil {
			session.Rollback()
			log.Error(logPrefix, "插入门店商品信息失败，err=", err.Error())
			err = errors.New("插入门店商品信息失败")
			return
		}
	}

	if len(insertSpuData) > 0 {
		if _, err = session.Table("eshop.pro_product_store_spu").Insert(&insertSpuData); err != nil {
			session.Rollback()
			log.Error(logPrefix, "插入门店商品SPU信息失败，err=", err.Error())
			err = errors.New("插入门店商品SPU信息失败")
			return
		}
	}

	if err = session.Commit(); err != nil {
		session.Rollback()
		log.Error(logPrefix, "提交事务失败,err=", err.Error())
		err = errors.New("插入门店商品信息失败")
		return
	}

	// 下发时,初始化库存
	ioBoundService := iobound.NewIoBoundService()
	initInventoryData := make([]inventoryVO.InventoryInitCommand, 0)
	for _, v := range initInventoryMap {
		initInventoryData = append(initInventoryData, v)
	}
	a := utils.JsonEncode(initInventoryData)

	log.Info(logPrefix, "下发商品时，初始化库存数据", a)
	for _, v := range initInventoryData {
		if _, err = ioBoundService.InitInventory(context.Background(), nil, v); err != nil {
			log.Error(logPrefix, "下发商品时，初始化库存数据出错", err.Error())
		}
	}

	return
}

// 管理后台 - 编辑单个连锁商品
// 编辑的商品 需要检查是否铺品成功到第三方， 如果铺品成功到第三方，需要同步修改第三方的商品信息
func (s ProductService) EditChainProduct(in vo.ChainProduct) (err error) {
	logPrefix := "管理后台 - 编辑单个连锁商品===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	// 第一步： 数据校验
	if in.Product.Id <= 0 {
		session.Rollback()
		log.Error(logPrefix, "商品ID不能为空")
		err = errors.New("商品不能为空")
		return
	}
	if err = ChainProductValidate(session, &in); err != nil {
		session.Rollback()
		log.Error(logPrefix, "数据校验失败，err=", err.Error())
		return
	}

	// 第二步： 查pro_product数据、pro_sku、pro_product_channel、pro_product_channel_attr 数据
	chainProductDataMap, err := GetChainProductData(session, []int{in.Product.Id})
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "获取商品相关信息失败，err=", err.Error())
		err = errors.New("获取商品相关信息失败")
		return
	}

	chainProductData, ok := chainProductDataMap[in.Product.Id]
	if !ok {
		session.Rollback()
		log.Error(logPrefix, "未找到商品相关信息")
		err = errors.New("未找到商品相关信息")
		return
	}

	// 第三步： 修改商品相关数据
	delSKuMap := make(map[int]bool)
	delSku, err := DealChainProductData(session, in, chainProductData)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "编辑商品相关数据失败，err=", err.Error())
		return
	}

	//获取门店商品spu信息
	spuQuery := product_po2.SpuQuery{
		ProductId:           in.Product.Id,
		ExcludeProductTypes: []int{product_po2.ProductTypeService, product_po2.ProductTypeLive},
	}
	_, spuData, err := new(product_po2.ProProductStoreSpu).QueryMap(session, spuQuery)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "获取商品spu信息失败，err=", err.Error())
		err = errors.New("获取商品spu信息失败")
		return
	}

	if len(delSku) > 0 {
		//  删除门店商品数据
		_, err = session.Table("eshop.pro_sku").In("id", delSku).Update(map[string]interface{}{"is_del": 1})
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "删除门店商品数据失败，err=", err.Error())
			err = errors.New("删除门店商品数据失败")
			return
		}
		for _, v := range delSku {
			delSKuMap[v] = true
		}

	}
	// 第四步： 若店铺现在是自营店铺， 需要确保有 4个渠道的数据（出现场景是： 店铺A被代运营了， 之后店铺A又自营了， 这时需要确保有 4个渠道的数据）
	// 查下店铺是否自营
	if err = s.AddProductStoreData(vo.AddProductStoreDataReq{
		ProductIds: []int{in.Product.Id},
		Session:    session,
	}); err != nil {
		session.Rollback()
		log.Error(logPrefix, "补充门店商品数据失败，err=", err.Error())
		err = errors.New("补充门店商品数据失败")
		return
	}

	// 第五步： 修改商品spu的渠道分类
	// 场景1-若连锁商品后端分类有改动，则需要同步修改渠道为线下门店的商品渠道分类数据（pro_product_store_spu.channel_category_id）
	// 场景2 - 若连锁商品前端分类有改动，则需要同步修改渠道为小程序前端分类

	for _, v := range spuData {
		needUpdate := false
		oldChannelCategoryId := v.ChannelCategoryId
		if v.ChannelId == common.ChannelIdOfflineShop {
			v.ChannelCategoryId = in.Product.CategoryIdOffline
		} else {
			v.ChannelCategoryId = in.Product.CategoryId
		}
		// 场景1-若连锁商品后端分类有改动，则需要同步修改渠道为线下门店的商品渠道分类数据（pro_product_store_spu.channel_category_id）
		if v.ChannelId == common.ChannelIdOfflineShop && oldChannelCategoryId != in.Product.CategoryIdOffline {
			needUpdate = true
		}
		// 场景2 - 若连锁商品前端分类有改动，则需要同步修改渠道为小程序、美团、饿了么的前端分类
		if (v.ChannelId == common.ChannelIdWeChatApp || v.ChannelId == common.ChannelIdMT || v.ChannelId == common.ChannelIdELM) && oldChannelCategoryId != in.Product.CategoryId {
			needUpdate = true
		}

		if needUpdate {
			if err = v.UpdateProductStoreSpu(s.Engine, map[string]interface{}{"id": v.Id}, "channel_category_id"); err != nil {
				session.Rollback()
				log.Error(logPrefix, "更新商品spu渠道分类失败,err=", err.Error())
				err = errors.New("更新商品spu渠道分类失败")
				return
			}
		}
	}
	//获取美团、饿了么的门店商品sku信息
	where := product_po2.GetProductStoreInfoReq{
		ProductId:           in.Product.Id,
		OutType:             2,
		ExcludeProductTypes: []int{product_po2.ProductTypeService, product_po2.ProductTypeLive},
	}
	productStoreInfoData, _, _, err := product_po2.GetProductStoreInfo(session, where)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "获取商品铺品信息失败，数据：,err=", err.Error())
		err = errors.New("获取商品铺品信息失败")
		return
	}

	// 第六步： 渠道为美团、饿了么的 门店sku商品已铺品， 则需要下发编辑商品的异步任务
	storeInfoToThirdMap := make(map[string]product_po2.ProProductStoreInfoExt2, 0)
	needDelStoreInfoThirdMap := make(map[string]product_po2.ProProductStoreInfoExt2, 0)
	needDelStoreInfoThird := make([]product_po2.ProProductStoreInfoExt2, 0) // 需要删除第三方商品
	needDelStoreInfo := make([]int, 0)                                      // 需要删除本地商品

	const mapKeyFormat = "%d_%s_%d" // channelId_storeId_skuId
	for _, v := range productStoreInfoData {
		isDeleted := delSKuMap[v.SkuId]
		isThirdParty := v.ChannelId == common.ChannelIdMT || v.ChannelId == common.ChannelIdELM
		isDistributed := v.IsDistribution == proproductstoreinfo.IsDistributionLaunched

		if isDeleted {
			// 处理已删除的SKU
			if isThirdParty && isDistributed {
				needDelStoreInfoThirdMap[fmt.Sprintf(mapKeyFormat, v.ChannelId, v.StoreId, v.SkuId)] = v
			} else {
				needDelStoreInfo = append(needDelStoreInfo, v.Id)
			}
			continue
		}

		// 处理需要同步到第三方的SKU
		if isThirdParty && isDistributed {
			storeInfoToThirdMap[fmt.Sprintf(mapKeyFormat, v.ChannelId, v.StoreId, v.SkuId)] = v
		}
	}

	for _, v := range needDelStoreInfoThirdMap {
		needDelStoreInfoThird = append(needDelStoreInfoThird, v)
	}

	if err = s.handleDeleteThirdPartyProducts(in, needDelStoreInfoThird, logPrefix); err != nil {
		return session.Rollback()
	}

	if len(needDelStoreInfo) > 0 {
		_, err = session.Table("eshop.pro_product_store_info").In("id", needDelStoreInfo).Delete()
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "删除门店商品sku信息失败,err=", err.Error())
			err = errors.New("删除门店商品sku信息失败")
			return
		}
	}

	if err = session.Commit(); err != nil {
		session.Rollback()
		log.Error(logPrefix, "编辑商品失败，提交事务失败,err=", err.Error())
		err = errors.New("编辑商品失败")
		return
	}
	// 处理商品下发到店铺
	if err = s.handlePushStores(in, logPrefix); err != nil {
		return err
	}
	// 同步第三方商品数据
	if err = s.handleSyncThirdPartyProducts(in, logPrefix); err != nil {
		return err
	}

	return
}

// 管理后台 - 删除单个连锁商品
func (s ProductService) DelChainProduct(in vo.DelChainProductReq) (err error) {
	logPrefix := "管理后台 - 删除单个连锁商品===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	// 第一步： 软删除 pro_product、pro_sku 【备注：以下表不用删除  pro_product_channel 和 pro_product_channel_attr ， 因为主表pro_product是软删除】
	session.Begin()
	productData := product_po2.ProProduct{IsDel: 1, DelDate: time.Now().Format(utils.DateTimeLayout), UpdatedBy: cast.ToString(in.UpdatedBy), UpdatedName: in.UpdatedName}
	row, err := session.Table("eshop.pro_product").Cols("is_del,del_date,updated_by,updated_name").Where("id=?", in.ProductId).Where("chain_id=?", in.ChainId).Update(&productData)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "删除连锁商品失败,err=", err.Error())
		err = errors.New("删除连锁商品失败")
		return
	}
	if row == 0 {
		session.Rollback()
		log.Error(logPrefix, "连锁商品不存在,err=", err.Error())
		err = errors.New("连锁商品不存在")
		return
	}

	skuData := product_po2.ProSku{IsDel: 1}
	_, err = session.Table("eshop.pro_sku").Cols("is_del").Where("product_id=?", in.ProductId).Update(&skuData)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix, "删除连锁商品sku失败,err=", err.Error())
		err = errors.New("删除连锁商品sku失败")
		return
	}

	// 第二步： 删除下发关系
	if _, err = session.Table("eshop.pro_product_store").Where("product_id=?", in.ProductId).Delete(); err != nil {
		session.Rollback()
		log.Error(logPrefix, "删除连锁商品下发信息失败,err=", err.Error())
		err = errors.New("删除连锁商品下发信息失败")
		return
	}

	where := product_po2.GetProductStoreInfoReq{
		ProductId:           in.ProductId,
		ExcludeProductTypes: []int{product_po2.ProductTypeService, product_po2.ProductTypeLive},
	}
	// 获取该连锁商品的门店商品sku信息
	productStoreInfo, _, _, err := product_po2.GetProductStoreInfo(session, where)
	if err != nil {
		log.Error(logPrefix, "获取门店信息失败,err=", err.Error())
		err = errors.New("获取门店信息失败")
		return
	}
	needAsyncDelete := make([]product_po2.ProProductStoreInfoExt2, 0)
	needDeleteStoreInfo := make([]int, 0)
	needDeleteSpu := make([]product_po2.ProProductStoreSpu, 0)
	for _, v := range productStoreInfo {
		// 如果是美团、饿了么渠道， 如果已铺品则需要同步删除第三方，删除第三方后， 再删除本地
		if (v.ChannelId == common.ChannelIdMT || v.ChannelId == common.ChannelIdELM) && v.IsDistribution == proproductstoreinfo.IsDistributionLaunched {
			needAsyncDelete = append(needAsyncDelete, v)
		} else {
			needDeleteStoreInfo = append(needDeleteStoreInfo, v.Id)
			needDeleteSpu = append(needDeleteSpu, product_po2.ProProductStoreSpu{
				ProductId: v.ProductId,
				StoreId:   v.StoreId,
				ChannelId: v.ChannelId,
			})
		}
	}

	// 第三步： 如果是美团或饿了么渠道， 且 已经铺品， 则插入异步任务 【备注：异步任务里会删除美团或饿了么的商品，且随后再删除本地商品】
	if len(needAsyncDelete) > 0 {
		asyncParam := omnibus_vo2.AsyncProductParam{
			Data: needAsyncDelete,
		}
		asyncTask := omnibus_po2.TaskListAsync{
			CreateId:         cast.ToString(in.UpdatedBy),
			CreateName:       in.UpdatedName,
			TaskContent:      enum.SyncProductDelTaskContent,
			OperationFileUrl: utils.JsonEncode(asyncParam),
			KeyStr:           "",
			ExtendedData:     enum.SyncTaskContentMap[enum.SyncProductDelTaskContent],
		}
		if err = asyncTask.CreateAsyncTask(s.Engine); err != nil {
			session.Rollback()
			log.Error(logPrefix, "创建异步任务失败,err=", err.Error())
			err = errors.New("创建异步任务失败")
			return
		}
	}

	// 第四步： 删除门店商品sku信息
	if len(needDeleteStoreInfo) > 0 {
		_, err = session.Table("eshop.pro_product_store_info").In("id", needDeleteStoreInfo).Delete()
		if err != nil {
			session.Rollback()
			log.Error(logPrefix, "删除门店商品sku信息和门店商品spu信息失败,err=", err.Error())
			err = errors.New("删除门店商品sku信息和门店商品spu信息失败")
			return
		}
	}

	// 第五步： 删除门店商品spu信息【注意： 只有门店商品sku信息全部删除了， 才能删除门店商品spu信息，有可能美团或饿了么第三方商品还没有删除， 且对应的pro_product_store_info表中还存在数据】
	if len(needDeleteSpu) > 0 {
		for _, v := range needDeleteSpu {
			if err = v.DelSpuByStoreAndProduct(session); err != nil {
				session.Rollback()
				log.Error(logPrefix, "删除门店商品spu信息失败,err=", err.Error())
				err = errors.New("删除门店商品spu信息失败")
				return
			}
		}
	}

	if err = session.Commit(); err != nil {
		session.Rollback()
		log.Error(logPrefix, "删除商品失败，提交事务失败,err=", err.Error())
		err = errors.New("删除商品失败")
		return
	}

	return
}

// 生成商品sku条码
func (s ProductService) GenerateBarCode(chainId int64) (code string, err error) {
	logPrefix := "管理后台 - 商品sku条码生成====,连锁id=" + cast.ToString(chainId)
	log.Info(logPrefix)
	exists := false

	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	Gen := func() {
		code = utils.GenerateBarCode()
		//判断条码是否已经生成了
		exists, err = product_po2.SkuBarcodeIsExist(session, map[string]interface{}{"barCode": code, "chainId": chainId})
	}
	i := 0
	for {
		if i == 0 || exists {
			Gen()
			if err != nil {
				log.Error(logPrefix, "查询数据失败，err=", err.Error())
				err = errors.New("查询数据失败")
				return
			}
		}
		// 已经生成过了商品条码 ， 如果不存在重复的， 就跳出
		if !exists {
			break
		}
		i++

		if i > 3 {
			break
		}

	}

	if exists {
		code = ""
		err = errors.New("生成商品条码失败，请重试")
		return
	}

	return
}

// 更新门店信息到ES
func (s ProductService) ProductToEs(in vo.ProductEs) error {
	s.Begin()
	defer s.Close()
	//session := s.Session

	SaasIndex := "SaasProduct"
	if in.ChainId == 0 && in.StoreId == "" {
		return errors.New("连锁ID或者门店ID必须有一个不为空")
	}
	//先删除ES数据
	esClient := utils.NewEsClient()
	//utils.CreateESIndexByName(SaasIndex)
	query := elastic.NewBoolQuery()
	if in.StoreId != "" {
		query.Must(elastic.NewTermQuery("zilong_id", in.StoreId))
	}
	if in.ChainId != 0 {
		query.Must(elastic.NewTermQuery("chain_id", in.ChainId))
	}
	if len(in.ProductIds) > 0 {
		var ids []interface{}
		for _, v := range in.ProductIds {
			ids = append(ids, v)
		}
		query.Must(elastic.NewTermsQuery("product_id", ids...))
	}

	if _, err := esClient.DeleteByQuery().Index(SaasIndex).Query(query).Do(context.Background()); err != nil {
		//log.Error(in, " ,删除SAAS商品数据失败:", err.Error())
		//return err
	}

	//如果是新增或者修改的，才重新去添加ES数据
	if in.Type == 1 {
		//自己根据条件去查询商品
		session := s.Engine.Table("pro_product_store_info").Alias("a").Join("inner", "pro_product b", "b.id=a.product_id").Where("1=1")
		if in.StoreId != "" {
			session.And("a.store_id=?", in.StoreId)
		}
		if in.ChainId != 0 {
			session.And("b.chain_id=?", in.ChainId)
		}

		if len(in.ProductIds) > 0 {
			var ids []interface{}
			for _, v := range in.ProductIds {
				ids = append(ids, v)

			}
			session.In("b.id", in.ProductIds)
		}
		var productInfo []vo.ProductEsBody
		err := session.Select("b.chain_id,a.store_id,a.product_id,a.channel_category_id,a.update_date,b.name,b.bar_code,b.product_type,b.pic").Find(&productInfo)
		if err != nil {
			log.Error("插入ES查询商品数据报错:", err.Error())
		}
		var productIds []int
		for _, v := range productInfo {
			productIds = append(productIds, v.ProductId)
		}

		var skuInfo []vo.ProSkuEs
		//再查询SKU信息
		err = s.Engine.Table("pro_sku").In("product_id", productIds).Find(&skuInfo)
		if err != nil {
			log.Error("插入ES查询商品数据报错:", err.Error())
		}
		if len(skuInfo) > 0 {
			mapInfo := make(map[int][]vo.ProSkuEs)
			for _, x := range skuInfo {
				mapInfo[x.ProductId] = append(mapInfo[x.ProductId], x)
			}
			for i, x := range productInfo {
				productInfo[i].SkuInfo = mapInfo[x.ProductId]
			}

		}

		bulkRequest := esClient.Bulk()

		//批量插入ES
		for _, x := range productInfo {
			body, err := json.Marshal(x)
			if err != nil {
				log.Error("插入ES，JSON商品转换失败", x, err.Error())
				continue
			}
			id := cast.ToString(in.OrgId) + cast.ToString(x.StoreId) + "-" + cast.ToString(x.ProductId)
			bulkRequest.Add(elastic.NewBulkIndexRequest().Index(SaasIndex).Id(id).Doc(body))
		}
		if _, err := bulkRequest.Do(context.Background()); err != nil {
			log.Error("商品同步ES出错: ", in, err.Error())
		}

	}

	return nil
}

// 如果渠道门店数据有缺失， 这里补充门店商品数据
// 示例场景1： 店铺以前是代运营的，下发了商品a 现在要改为自营的， 点击a商品铺品前需要补充a美团和饿了么渠道的商品数据
// 示例场景2： 店铺以前是代运营的，下发了商品a,b,c 现在要改为自营的， 点击批量铺品时， 选中商品a,b,c铺品到店铺1时 a,b,c在店铺1的美团和饿了么渠道的商品数据
// 示例场景3： 店铺以前是代运营的， 下发了商品a到店铺1，店铺2，店铺3. 现在要改为自营了， 编辑连锁商品a时， 需要补充店铺1，店铺2，店铺3的美团、饿了么渠道的商品数据
// 示例场景4： 不管是代运营的还是自营的商品， 某个商品已经下发到了店铺1，店铺2，店铺3， 之后编辑商品时新增了一个sku规格， 需要补充店铺1，店铺2，店铺3的该sku规格数据
// 注意： 指定连锁只能下发自己连锁的商品
func (s ProductService) AddProductStoreData(in vo.AddProductStoreDataReq) (err error) {
	logPrefix := "添加门店商品数据===="
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(in))

	if len(in.ProductIds) == 0 && len(in.StoreIds) == 0 {
		log.Error(logPrefix, "商品ID和店铺ID不能同时为空")
		return errors.New("商品ID和店铺ID不能同时为空")
	}

	// 如果店铺ID为空， 则需要查询指定商品已经下发到哪些店铺中
	if len(in.StoreIds) == 0 && len(in.ProductIds) == 1 {
		// 查出指定商品已经下发到哪些店铺中
		productStoreModel := product_po2.NewProProductStoreModel()
		productStoreMap, _, err := productStoreModel.QueryProductStores(context.Background(), in.Session, product_po2.QueryProductStoresReq{
			ProductId: cast.ToInt(in.ProductIds[0]),
		})
		if err != nil {
			log.Error(logPrefix, "查询商品已下发的店铺列表失败，err=", err.Error())
			return errors.New("查询商品已下发的店铺列表失败")
		}

		if _, ok := productStoreMap[cast.ToInt(in.ProductIds[0])]; ok {
			for _, store := range productStoreMap[cast.ToInt(in.ProductIds[0])] {
				in.StoreIds = append(in.StoreIds, store.StoreId)
			}
		}
	}

	// 如果店铺ID为空， 则不需要补充门店商品数据
	if len(in.StoreIds) == 0 {
		log.Info(logPrefix, "店铺数据为空")
		return nil
	}
	// 查询店铺授权相关信息
	retailCfgQuery := offline.RetailCfgQuery{
		TenantIds: utils.StringSliceToInt64Slice(in.StoreIds),
		OutType:   1,
	}
	_, retailCfgMap, err := new(offline.TTenantRetailCfg).QueryByTenantId(in.Session, retailCfgQuery)
	if err != nil {
		log.Error(logPrefix, "获取店铺授权信息失败，err=", err.Error())
		return errors.New("获取店铺授权信息失败")
	}

	errMsg := ""
	for _, v := range in.StoreIds {
		if _, ok := retailCfgMap[cast.ToInt64(v)]; !ok {
			errMsg += cast.ToString(v) + ","
		}
	}
	if errMsg != "" {
		log.Error(logPrefix, "店铺授权信息不存在", "店铺id=", errMsg)
		return errors.New("店铺授权信息不存在" + errMsg)
	}

	// 获取商品铺品信息
	where := product_po2.GetProductStoreInfoReq{
		ProductIds: in.ProductIds,
		StoreIds:   in.StoreIds,
		OutType:    2,
	}
	_, _, productStoreInfoMap, err := product_po2.GetProductStoreInfo(in.Session, where)
	if err != nil {
		log.Error(logPrefix, "获取商品铺品信息失败，err=", err.Error())
		err = errors.New("获取商品铺品信息失败")
		return
	}

	// 获取商品SPU信息
	spuQuery := product_po2.SpuQuery{
		ProductIds: in.ProductIds,
		StoreIds:   in.StoreIds,
	}
	productStoreSpuMap, _, err := new(product_po2.ProProductStoreSpu).QueryMap(in.Session, spuQuery)
	if err != nil {
		log.Error(logPrefix, "获取商品SPU信息失败，err=", err.Error())
		return errors.New("获取商品SPU信息失败")
	}

	productMap, err := GetProductMapInfo(in.Session, in.ProductIds, logPrefix)
	if err != nil {
		log.Error(logPrefix, "获取商品信息失败，err=", err.Error())
		err = errors.New("获取商品信息失败")
		return
	}
	insertProductStoreInfo := make([]product_po2.ProProductStoreInfo, 0)
	insertProductStoreSpu := make([]product_po2.ProProductStoreSpu, 0)
	// 检查美团和饿了么渠道是否存在
	for productId, v := range productMap {
		for skuid, info := range v {
			for _, storeId := range in.StoreIds {

				if retailCfgMap[cast.ToInt64(storeId)].OperationType == 1 {
					if retailCfgMap[cast.ToInt64(storeId)].ChainId != info.ChainId {
						log.Error(logPrefix, "商品所属连锁和店铺所属连锁不一致", "商品信息", utils.InterfaceToJSON(info), "店铺授权配置信息", utils.InterfaceToJSON(retailCfgMap[cast.ToInt64(storeId)]))
						continue
					}

				} else if retailCfgMap[cast.ToInt64(storeId)].OperationType == 2 {
					if retailCfgMap[cast.ToInt64(storeId)].AuthorizedChainId != info.ChainId {
						log.Error(logPrefix, "商品所属连锁和店铺代运营连锁不一致", "商品信息", utils.InterfaceToJSON(info), "店铺授权配置信息", utils.InterfaceToJSON(retailCfgMap[cast.ToInt64(storeId)]))
						continue
					}

				} else {
					log.Error(logPrefix, "店铺授权信息有误", "店铺授权信息", utils.InterfaceToJSON(retailCfgMap[cast.ToInt64(storeId)]))
					continue
				}

				// 组织pro_product_store_info表数据
				storeInfo := product_po2.ProProductStoreInfo{
					StoreId:     cast.ToString(storeId),
					ProductId:   productId,
					SkuId:       skuid,
					Status:      proproductstoreinfo.StatusNormal,
					ProductType: product_po2.ProductTypeGoods,
				}
				// 组织pro_product_store_spu表数据
				storeSpu := product_po2.ProProductStoreSpu{
					StoreId:     cast.ToString(storeId),
					ProductId:   productId,
					ProductType: product_po2.ProductTypeGoods,
				}
				for channelId := range common.ChannelIdMap {
					if channelId == common.ChannelIdJD {
						continue
					}

					// 如果店铺被代运营了， 如果下发的商品是自己连锁的。 则补充线下门店端和小程序端数据
					// 如果店铺被代运营了， 如果下发的商品是代运营连锁的商品， 则补充美团和饿了么渠道数据
					// 如果店铺是自营的， 则下发线下门店端、小程序端、美团、饿了么渠道数据
					if retailCfgMap[cast.ToInt64(storeId)].OperationType == 2 && retailCfgMap[cast.ToInt64(storeId)].AuthorizedChainId == info.ChainId && (channelId == common.ChannelIdOfflineShop || channelId == common.ChannelIdWeChatApp) {
						continue
					}

					if retailCfgMap[cast.ToInt64(storeId)].OperationType == 1 && retailCfgMap[cast.ToInt64(storeId)].ChainId == info.ChainId && (channelId == common.ChannelIdMT || channelId == common.ChannelIdELM) {
						continue
					}

					if _, ok := productStoreInfoMap[fmt.Sprintf("%d_%s_%d_%d", channelId, cast.ToString(storeId), productId, skuid)]; !ok {

						storeInfo.ChannelId = channelId
						switch channelId {
						case common.ChannelIdOfflineShop:
							storeInfo.RetailPrice = info.StorePrice
							storeInfo.UpDownState = proproductstoreinfo.UpDownStateUp
							storeInfo.IsDistribution = proproductstoreinfo.IsDistributionLaunched
						case common.ChannelIdWeChatApp: // 小程序端 已铺品， 但是没有上架
							storeInfo.RetailPrice = info.XcxPrice
							storeInfo.UpDownState = proproductstoreinfo.UpDownStateDown
							storeInfo.IsDistribution = proproductstoreinfo.IsDistributionLaunched
						case common.ChannelIdMT:
							storeInfo.RetailPrice = info.MtPrice
							storeInfo.UpDownState = proproductstoreinfo.UpDownStateDown
							storeInfo.IsDistribution = proproductstoreinfo.IsDistributionUnLaunch
						case common.ChannelIdELM:
							storeInfo.RetailPrice = info.ElmPrice
							storeInfo.UpDownState = proproductstoreinfo.UpDownStateDown
							storeInfo.IsDistribution = proproductstoreinfo.IsDistributionUnLaunch
						}
						insertProductStoreInfo = append(insertProductStoreInfo, storeInfo)
					}

					if _, ok := productStoreSpuMap[fmt.Sprintf("%d_%s_%d", channelId, cast.ToString(storeId), productId)]; !ok {
						storeSpu.ChannelId = channelId
						if channelId == common.ChannelIdOfflineShop {
							storeSpu.ChannelCategoryId = info.CategoryIdOffline
						} else {
							storeSpu.ChannelCategoryId = info.CategoryId
						}
						insertProductStoreSpu = append(insertProductStoreSpu, storeSpu)
					}

				}

			}

		}

	}

	log.Info(logPrefix, "插入门店商品信息, 门店商品信息=", insertProductStoreInfo)
	log.Info(logPrefix, "插入门店商品SPU信息, 门店商品SPU信息=", insertProductStoreSpu)
	if len(insertProductStoreInfo) > 0 || len(insertProductStoreSpu) > 0 {
		if len(insertProductStoreInfo) > 0 {
			if _, err = in.Session.Table("eshop.pro_product_store_info").Insert(&insertProductStoreInfo); err != nil {
				log.Error(logPrefix, "插入门店商品信息失败，err=", err.Error())
				err = errors.New("插入门店商品信息失败")
				return
			}
		}

		if len(insertProductStoreSpu) > 0 {
			if _, err = in.Session.Table("eshop.pro_product_store_spu").Insert(&insertProductStoreSpu); err != nil {
				log.Error(logPrefix, "插入门店商品SPU信息失败，err=", err.Error())
				err = errors.New("插入门店商品SPU信息失败")
				return
			}
		}
	}

	return nil

}

// 处理商品下发到店铺
func (s ProductService) handlePushStores(in vo.ChainProduct, logPrefix string) error {
	if len(in.PushStoreIds) == 0 {
		return nil
	}

	pushStoreIdSli := strings.Split(in.PushStoreIds, ",")
	// 获取该用户在该连锁下所有有权限的店铺列表
	omnibusService := omnibus_service.StoreService{}
	req := omnibus_vo2.GetStoreListReq{
		UserId:    cast.ToInt64(in.Product.UpdatedBy),
		ProductId: in.Product.Id,
	}
	storeList, err := omnibusService.GetStoreList(req)
	if err != nil {
		log.Error(logPrefix, "获取可下发商品列表，err=", err.Error())
		return nil
	}

	if len(storeList) > 0 {
		storeListMap := make(map[string]omnibus_vo2.GetStoreList)
		for _, v := range storeList {
			storeListMap[v.StoreId] = v
		}

		for _, storeId := range pushStoreIdSli {
			if info, ok := storeListMap[storeId]; ok {
				s.BatchPushChainProduct(vo.BatchPushChainProductReq{
					ProductIds: cast.ToString(in.Product.Id),
					StoreIds:   cast.ToString(info.StoreId),
					CreatedId:  cast.ToInt64(in.Product.UpdatedBy),
				})
			}
		}
	}
	return nil
}

// 处理删除第三方商品
func (s ProductService) handleDeleteThirdPartyProducts(in vo.ChainProduct, needDelStoreInfoThird []product_po2.ProProductStoreInfoExt2, logPrefix string) error {
	if len(needDelStoreInfoThird) == 0 {
		return nil
	}

	log.Info("需要删除美团第三方商品：", utils.InterfaceToJSON(needDelStoreInfoThird))
	asyncParam := omnibus_vo2.AsyncProductParam{
		Data: needDelStoreInfoThird,
	}
	asyncTask := omnibus_po2.TaskListAsync{
		CreateId:         cast.ToString(in.Product.UpdatedBy),
		CreateName:       in.Product.UpdatedName,
		TaskContent:      enum.SyncProductDelTaskContent,
		OperationFileUrl: utils.JsonEncode(asyncParam),
		KeyStr:           "",
		ExtendedData:     enum.SyncTaskContentMap[enum.SyncProductDelTaskContent],
	}

	if err := asyncTask.CreateAsyncTask(s.Engine); err != nil {
		log.Error(logPrefix, "创建异步删除任务失败,err=", err.Error())
		return errors.New("创建异步删除任务失败")
	}
	return nil
}

// 同步第三方商品数据
func (s ProductService) handleSyncThirdPartyProducts(in vo.ChainProduct, logPrefix string) error {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	// 获取spu信息
	spuMap, _, err := new(product_po2.ProProductStoreSpu).QueryMap(session, product_po2.SpuQuery{
		ProductIds: []int{in.Product.Id},
		ChannelIds: []int{common.ChannelIdMT, common.ChannelIdELM},
	})
	if err != nil {
		log.Error(logPrefix, "获取spu信息失败,err=", err.Error())
		return errors.New("获取spu信息失败")
	}

	// 收集所有SPU信息
	spuInfo := omnibus_vo2.SpuSyncInfo{
		Data: make([]struct {
			StoreId   string `json:"store_id"`
			ProductId int    `json:"product_id"`
			ChannelId int    `json:"channel_id"`
		}, 0),
	}
	for _, spu := range spuMap {
		spuInfo.Data = append(spuInfo.Data, struct {
			StoreId   string `json:"store_id"`
			ProductId int    `json:"product_id"`
			ChannelId int    `json:"channel_id"`
		}{
			StoreId:   spu.StoreId,
			ProductId: spu.ProductId,
			ChannelId: spu.ChannelId,
		})
	}
	asyncTask := omnibus_po2.TaskListAsync{
		CreateId:         cast.ToString(in.Product.UpdatedBy),
		CreateName:       in.Product.UpdatedName,
		TaskContent:      enum.SyncProductEditTaskContent,
		OperationFileUrl: utils.JsonEncode(spuInfo),
		KeyStr:           "",
		ExtendedData:     enum.SyncTaskContentMap[enum.SyncProductEditTaskContent],
	}

	if err = asyncTask.CreateAsyncTask(s.Engine); err != nil {
		log.Error(logPrefix, "创建SPU批量同步异步任务失败，err=", err.Error())
		return errors.New("创建SPU批量同步异步任务失败")
	}
	return nil
}
