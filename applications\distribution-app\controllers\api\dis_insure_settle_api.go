package api

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	"eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"net/http"
)

// ListDisInsureSettle 获取保险分销结算列表
// @Summary 保险分销结算分页查询
// @Description 保险分销结算查询接口
// @Tags 小程序接口-保险分销结算
// @Accept  json
// @Produce  json
// @Param InsSettlePageReq body vo.InsureSettlePageApiReq true " "
// @Success 200 {object} vo.InsureSettlePageApiResp
// @Failure 400 {object} vo.InsureSettlePageApiResp
// @Router /api/insure-settle/page-list [GET]
func ListDisInsureSettle(w http.ResponseWriter, r *http.Request) {
	resp := vo.InsureSettlePageApiResp{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[vo.InsureSettlePageApiReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("分页查询商品操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询商品操作，参数解析失败：%s", err.Error())
	} else {
		service := services.DisInsureSettleService{}
		list, total, totalSales, err := service.InsureSettleApiPage(req)
		if err != nil {
			log.Error("分页查询商品操作失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("分页查询商品操作异常：%s", err.Error())
		} else {
			resp.BasePageHttpResponse.Code = 200
			resp.BasePageHttpResponse.Total = total
			resp.TotalSale = totalSales
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
