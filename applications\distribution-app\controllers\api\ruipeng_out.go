/*
瑞鹏体制外的数据
*/
package api

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
)

// @Summary 获取瑞鹏体制外医院列表@blky-v1.2
// @Description
// @Tags 小程序接口-瑞鹏体制外的数据
// @Accept json
// @Produce json
// @Param OutHospitalListReq query vo.OutHospitalListReq true " "
// @Success 200 {object} vo.OutHospitalListRes
// @Failure 400 {object} vo.OutHospitalListRes
// @Router /api/out/hospital-list [GET]
func OutHospitalList(w http.ResponseWriter, r *http.Request) {
	resp := vo.OutHospitalListRes{}
	resp.Code = 400

	req, err := utils.Bind[vo.OutHospitalListReq](r)
	if err != nil {
		log.Error("获取瑞鹏体制外医院，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取瑞鹏体制外医院，参数解析失败：%s", err.Error())
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}

	server := services.OutService{}
	data, err := server.OutHospitalList(req)
	if err != nil {
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		w.Write(bytes)
		return
	}
	resp.Data = data
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
