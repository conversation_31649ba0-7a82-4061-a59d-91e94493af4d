package distribution_po

import (
	"time"

	"xorm.io/xorm"
)

type DisInsuranceSettle struct {
	Id          int       `json:"id" xorm:"pk not null comment('主键') BIGINT 'id'"`
	State       int       `json:"state" xorm:"not null default 1 comment('结算状态：1-待结算，2-已结算') TINYINT(4) 'state'"`
	SettleTime  time.Time `json:"settle_time" xorm:"default 'null' comment('结算时间') DATETIME 'settle_time'"`
	OrderNo     string    `json:"order_no" xorm:"not null comment('订单编号') VARCHAR(255) 'order_no'"`
	PolicyNo    string    `json:"policy_no" xorm:"not null comment('保单编号') VARCHAR(36) 'policy_no'"`
	ProductCode string    `json:"product_code" xorm:"not null comment('产品编号') VARCHAR(64) 'product_code'"`
	DisId       int       `json:"dis_id" xorm:"not null default 0 comment('分销员id') INT 'dis_id'"`
	CreateTime  time.Time `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	UpdateTime  time.Time `json:"update_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') TIMESTAMP 'update_time'"`
}

// 获取保险分销订单的佣金、待结佣金和已结佣金字段
func FindInsSettCommission(db *xorm.Engine, where map[string]interface{}) (data []FindSettCommissionStru, err error) {
	session := db.NewSession()
	defer session.Close()
	s := `sum(a.dis_amount) as commission,
		a.dis_id,
		a.shop_id,
		b.shop_name,
		b.enterprise_id,
		c.enterprise_name`

	session = session.Table("eshop.dis_insurance_settle").Alias("a").Select(s).
		Join("left", "eshop.shop b", "a.shop_id = b.id").
		Join("left", "eshop.scrm_enterprise c", "b.enterprise_id = c.id").
		Where("a.dis_id>0").
		Where("a.shop_id>0")

	orgId, ok := where["orgId"]
	if ok {
		session = session.Where("a.org_id=?", orgId)
	}
	state, ok := where["state"]
	if ok {
		session = session.Where("a.state=?", state)
	}
	createTimeStart, ok := where["createTimeStart"]
	if ok {
		session = session.Where("a.create_time>=?", createTimeStart)
	}
	createTimeEnd, ok := where["createTimeEnd"]
	if ok {
		session = session.Where("a.create_time<=?", createTimeEnd)
	}

	settleTimeStart, ok := where["settleTimeStart"]
	if ok {
		session = session.Where("a.settle_time>=?", settleTimeStart)
	}
	settleTimeEnd, ok := where["settleTimeEnd"]
	if ok {
		session = session.Where("a.settle_time<=?", settleTimeEnd)
	}
	shopId, ok := where["shopId"]
	if ok {
		session = session.Where("shop_id=?", shopId)
	}

	disId, ok := where["disId"]
	if ok {
		session = session.Where("dis_id=?", disId)
	}

	groupBy, ok := where["groupBy"]
	if ok {
		session = session.GroupBy(groupBy.(string))
	}

	err = session.Find(&data)
	return
}
