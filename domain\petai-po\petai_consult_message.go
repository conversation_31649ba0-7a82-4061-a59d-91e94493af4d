package petai_po

import (
	"time"
)

// 小闻养宠助消息表 (注意： 只有消息类型为answer时，DataSource这个字段才会有值， 只有消息类型为question时，Intent这个字段才会有值 )
type PetaiConsultMessage struct {
	Id                    int       `json:"id"  xorm:"pk autoincr 'id' comment('主键')"` //自增id
	Uuid                  string    `json:"uuid"`                                      // 消息uuid,唯一标识该条消息
	ConsultConversationId int64     `json:"consult_conversation_id"`                   //会诊会话id即petai_consult_conversation.id
	Role                  string    `json:"role"`                                      //发送这条消息的实体:user-代表该条消息内容是用户发送的;assistant-代表该条消息内容是 Bot 发送的
	Type                  string    `json:"type"`                                      //消息类型:question-用户输入内容;answer-Bot返回给用户的消息内容，支持增量返回;function_call-Bot 对话过程中调用函数（function call）的中间结果;tool_response-调用工具(function call)后返回的结果;follow_up-如果在 Bot上配置打开了用户问题建议开关，则会返回推荐问题相关的回复内容;verbose-多 answer 场景下，服务端会返回一个 verbose 包，对应的 content 为 JSON 格式，content.msg_type =generate_answer_finish 代表全部 answer 回复完成。不支持在请求中作为入参。
	ContentType           string    `json:"content_type"`                              //消息内容的类型:text-文本；object_string-多模态内容，即文本和文件的组合、文本和图片的组合;card-卡片。此枚举值仅在接口响应中出现，不支持作为入参
	Content               string    `json:"content"`                                   //消息内容
	ReferenceContent      string    `json:"reference_content"`                         //引用内容
	ReferenceSame         int       `json:"reference_same"`                            //引用程度:1-完全引用
	MetaData              string    `json:"meta_data"`                                 //创建消息时的附加消息，获取消息时也会返回此附加消息
	Evaluate              int       `json:"evaluate"`                                  //消息评价：0-初始，1-赞，2-踩
	EvaluateTime          time.Time `json:"evaluate_time"`                             //消息评价时间
	Feedback              string    `json:"feedback"`                                  //用户反馈
	FeedbackType          int       `json:"feedback_type"`                             //反馈类别:1-没有帮助，2-信息有误，3-理解错误，4-违法有害，5-内容不完整 6-内容不专业，7-格式错误
	CreateTime            time.Time `json:"create_time" xorm:"created"`                //创建时间
	UpdateTime            time.Time `json:"update_time" xorm:"updated"`                //更新时间
	IsDeleted             int       `json:"is_deleted"`                                //消息是否被删除，0否，1是

}

func (m *PetaiConsultMessage) TableName() string {
	return "eshop.petai_consult_message"
}
