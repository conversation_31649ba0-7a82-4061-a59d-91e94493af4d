package manager

import (
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/services/omnibus-service/services"
	"net/http"

	"eShop/infra/utils"
	"eShop/view-model/omnibus-vo"
)

// 首页六宫格统计
// @Summary 首页六宫格统计
// @Description 获取首页六宫格统计数据
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param store_id body string true "门店ID"
// @Success 200 {object} omnibus_vo.StatisticsOverviewRes
// @Router /omnibus-app/manager/statistics/overview [post]
func GetStatisticsOverview(w http.ResponseWriter, r *http.Request) {
	var req omnibus_vo.StatisticsOverviewReq

	req, err := utils.Bind[omnibus_vo.StatisticsOverviewReq](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}
	data, err := services.GetStatisticsOverview(req.StoreId)
	if err != nil {
		response.InternalError(w, "获取统计数据失败:"+err.Error())
		return
	}
	response.SuccessWithData(w, data)
}

// 首页趋势图统计
// @Summary 首页趋势图统计
// @Description 获取首页趋势图统计数据
// @Tags 宠物连锁SAAS-管理后台v1.6.0
// @Accept json
// @Produce json
// @Param store_id body string true "门店ID"
// @Param start_date body string false "开始日期"
// @Param end_date body string false "结束日期"
// @Param type body int true "类型 1-储值卡销售 2-商品销售 3-服务销售 4-活体销售 5-次卡销售"
// @Success 200 {object} omnibus_vo.StatisticsTrendRes
// @Router /omnibus-app/manager/statistics/trend [post]
func GetStatisticsTrend(w http.ResponseWriter, r *http.Request) {
	var req omnibus_vo.StatisticsTrendReq
	req, err := utils.Bind[omnibus_vo.StatisticsTrendReq](r)
	if err != nil {
		log.Error("参数绑定失败:", err)
		response.BadRequest(w, "参数绑定失败:"+err.Error())
		return
	}
	data, err := services.GetStatisticsTrend(req.StoreId, req.StartDate, req.EndDate, req.Type)
	if err != nil {
		response.InternalError(w, "获取趋势图数据失败:"+err.Error())
		return
	}
	response.SuccessWithData(w, data)
}
