package utils

import (
	glog "eShop/infra/log"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"time"
)

var (
	orderEngine           *xorm.Engine
	analyticalOrderEngine *xorm.Engine
)

// NewDbConn 连接池，请勿关闭
func NeWDatacenterDbConn(dataSourceName ...string) *xorm.Engine {
	var err error

	if orderEngine != nil {
		if err = orderEngine.DB().Ping(); err == nil {
			return orderEngine
		}
		return orderEngine
	}

	var mySqlStr string
	if len(dataSourceName) == 1 {
		mySqlStr = dataSourceName[0]
	} else {
		mySqlStr = config.GetString("mysql.datacenter")
	}

	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	orderEngine, err = xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		glog.Error(err.Error())
	}

	//空闲关闭时间
	orderEngine.SetConnMaxLifetime(120 * time.Second)
	//最大空闲连接
	orderEngine.SetMaxIdleConns(10)
	//最大连接数
	orderEngine.SetMaxOpenConns(1000)

	orderEngine.SetTZLocation(time.Local)

	return orderEngine
}

// 分析型数据库 连接池，请勿关闭
func NeWOrderDbConn(dataSourceName ...string) *xorm.Engine {
	var err error

	if analyticalOrderEngine != nil {
		if err = analyticalOrderEngine.DB().Ping(); err == nil {
			return analyticalOrderEngine
		}
		return analyticalOrderEngine
	}

	var mySqlStr string
	if len(dataSourceName) == 1 {
		mySqlStr = dataSourceName[0]
	} else {
		mySqlStr = config.GetString("mysql.analytical.orderCenter")
	}
	//mySqlStr = "root:XjIrQepuHn7u^E8D@(172.30.3.8:2000)/dc_order"
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	analyticalOrderEngine, err = xorm.NewEngine("mysql", mySqlStr)
	if err != nil {
		glog.Error(err.Error())
	}

	//空闲关闭时间
	analyticalOrderEngine.SetConnMaxLifetime(120 * time.Second)
	//最大空闲连接
	analyticalOrderEngine.SetMaxIdleConns(10)
	//最大连接数
	analyticalOrderEngine.SetMaxOpenConns(1000)

	analyticalOrderEngine.SetTZLocation(time.Local)

	return analyticalOrderEngine
}
