package base_service

import (
	"eShop/services/common"
	"eShop/view-model/distribution-vo"
	"reflect"
	"testing"
)

func TestOrgService_OrgGet(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req distribution_vo.OrgPageData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    distribution_vo.OrgPageData
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "根据子龙编码获取Org_id"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := OrgService{
				BaseService: tt.fields.BaseService,
			}
			var req distribution_vo.OrgPageData
			req.ZlOrgId = "CWDJT"
			got, err := s.OrgGet(req)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrgGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("OrgGet() got = %v, want %v", got, tt.want)
			}
		})
	}
}
