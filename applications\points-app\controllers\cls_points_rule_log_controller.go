package controllers

import (
	po "eShop/domain/points-po"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"

	"github.com/go-chi/chi"
)

type ClsPointsRuleLogController struct {
	SuperController[int, po.ClsPointsRuleLog, vo.ClsPointsRuleLogSaveVO, vo.ClsPointsRuleLogUpdateVO, vo.ClsPointsRuleLogQueryVO, vo.ClsPointsRuleLogResultVO]
	ControllerHooks[int, po.ClsPointsRuleLog, vo.ClsPointsRuleLogSaveVO, vo.ClsPointsRuleLogUpdateVO, vo.ClsPointsRuleLogQueryVO, vo.ClsPointsRuleLogResultVO]
	service service.ClsPointsRuleLogService
}

func NewClsPointsRuleLogController() ClsPointsRuleLogController {
	return ClsPointsRuleLogController{
		NewSuperController(
			service.NewClsPointsRuleLogService(),
			&ClsPointsRuleLogController{},
		),
		NewControllerHooks[int, po.ClsPointsRuleLog, vo.ClsPointsRuleLogSaveVO, vo.ClsPointsRuleLogUpdateVO, vo.ClsPointsRuleLogQueryVO, vo.ClsPointsRuleLogResultVO](),
		service.NewClsPointsRuleLogService(),
	}
}

func (c ClsPointsRuleLogController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
}
