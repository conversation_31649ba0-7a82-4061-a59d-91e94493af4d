// 分销企业数据
package services

import (
	"eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"runtime"
	"time"
)

type StatsEnterpriseService struct {
	common.BaseService
}

func (s StatsEnterpriseService) GetStatsEntView(in distribution_vo.GetKanbanOverviewReq) (out distribution_vo.StatsEntView, err error) {
	s.Begin()
	defer s.Close()
	logPrefix := fmt.Sprintf("获取业务员数据和分销企业数据,入参：%s======", utils.InterfaceToJSON(in))
	log.Info(logPrefix)

	if len(in.StartDate) == 0 || len(in.EndDate) == 0 {
		log.Error(logPrefix, "开始日期和结束日期都不能为空")
		err = errors.New("开始日期和结束日期都不能为空")
		return
	}
	startDate, err := time.ParseInLocation(utils.DateLayout, in.StartDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "开始日期格式错误err=", err.Error())
		err = errors.New("开始日期格式错误")
		return
	}
	endDate, err := time.ParseInLocation(utils.DateLayout, in.EndDate, time.Local)
	if err != nil {
		log.Error(logPrefix, "结束日期格式错误err=", err.Error())
		err = errors.New("结束日期格式错误")
		return
	}
	// 获取 stats_enterprise_daily 数据
	var totalData, curData, lastData distribution_po.StatsEnterpriseDaily
	totalData, curData, lastData, err = GetStatsEnterpriseDaily(s.Engine, map[string]interface{}{"startDate": startDate.Format(utils.DateLayout), "endDate": endDate.Format(utils.DateLayout)})
	if err != nil {
		log.Error(logPrefix, "获取业务员数据和分销企业数据概览失败，err=", err.Error())
		err = errors.New("获取业务员数据和分销企业数据概览失败")
		return
	}

	out = OrgStatsEntView(totalData, curData, lastData)
	// 累计业务员 实时查
	CntSalesperson, err := distribution_po.CntSalesperson(s.Engine, map[string]interface{}{})
	out.TotalSalesman = CntSalesperson[0].Cnt
	// 累计分销员 实时查

	CntDistributor, err := distribution_po.CntDistributor(s.Engine, map[string]interface{}{"orgId": in.OrgId})
	out.TotalDistributor = CntDistributor[0].Cnt
	// 获取 业务员启用和停用数据
	out.TotalSalesmanOpen, out.TotalSalesmanStop, out.TotalSalesmanNewOpen, out.TotalSalesmanNewStop, err = GetSalespersonOpenStop(s.Engine, map[string]interface{}{"groupBy": "status", "startDate": startDate.Format(utils.DateLayout), "endDate": endDate.Format(utils.DateLayout)})
	if err != nil {
		log.Error(logPrefix, "获取 业务员启用和停用数据失败err=", err.Error())
		err = errors.New("获取 业务员启用和停用数据失败")
		return
	}

	// 获取 分销员启用和停用数据
	out.TotalDistributorOpen, out.TotalDistributorStop, out.TotalDistributorNewOpen, out.TotalDistributorNewStop, err = GetDistributorOpenStop(s.Engine, map[string]interface{}{"groupBy": "status", "orgId": in.OrgId, "startDate": startDate.Format(utils.DateLayout), "endDate": endDate.Format(utils.DateLayout)})
	if err != nil {
		log.Error(logPrefix, "获取 分销员启用和停用数据失败err=", err.Error())
		err = errors.New("获取 分销员启用和停用数据失败")
		return
	}
	return

}

// 跑历史数据每日的统计记录
func (s StatsEnterpriseService) StatsEnterpriseDailyRun(startTime, endTime string) {
	startDay, err := time.Parse(utils.DateLayout, startTime)
	if err != nil {
		return
	}
	endDay, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return
	}
	for i := startDay; i.Before(endDay) || i.Equal(endDay); i = i.AddDate(0, 0, 1) {
		// 跑一天的数据
		fmt.Println("处理日期范围-分销企业数据-日:", i.Format("2006-01-02"), "-", i.Format("2006-01-02"))
		s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
			StartDate: i.Format(time.DateOnly),
			EndDate:   i.Format(time.DateOnly),
		})

		// 检测是不是周末，跑一周的数据
		weekday := i.Weekday()
		if weekday == time.Sunday {
			startDate := i.AddDate(0, 0, -6)
			fmt.Println("处理日期范围-分销企业数据-周:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是月末，跑一月的数据
		tomorrow := i.AddDate(0, 0, 1)
		if tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(0, -1, 0)
			fmt.Println("处理日期范围-分销企业数据-月:", startDate.Format("2006-01-02"), "-", i.Format("2006-01-02"))
			s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}

		// 检测是不是年末，跑一年的数据
		if tomorrow.Month() == 1 && tomorrow.Day() == 1 {
			startDate := tomorrow.AddDate(-1, 0, 0)
			s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
				StartDate: startDate.Format(time.DateOnly),
				EndDate:   i.Format(time.DateOnly),
			})
		}
	}
}

func (s StatsEnterpriseService) StatsEnterpriseDailyDefalut() {
	logPrefix := "写入业务员和分销企业数据-daily===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsEnterpriseDailyDataLock, "daily")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	now := time.Now()
	startDate := now.AddDate(0, 0, -1)
	s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   startDate.Format("2006-01-02"),
	})
}

func (s StatsEnterpriseService) StatsEnterpriseWeeklyDefalut() {
	logPrefix := "写入业务员和分销企业数据-weekly===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsEnterpriseDailyDataLock, "weekly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	now := time.Now()
	startDate := now.AddDate(0, 0, -int(now.Weekday())-6)
	endDate := startDate.AddDate(0, 0, 6)
	s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s StatsEnterpriseService) StatsEnterpriseMonthlyDefalut() {
	logPrefix := "写入业务员和分销企业数据-monthly===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsEnterpriseDailyDataLock, "monthly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	now := time.Now()
	startDate := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, -1) // 加一个月，再减去一天
	s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

func (s StatsEnterpriseService) StatsEnterpriseYearlyDefalut() {
	logPrefix := "写入业务员和分销企业数据-yearly===="
	log.Infof("%s 定时任务开始-%s", logPrefix, time.Now().Format(utils.DateTimeLayout))
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("定时器cron-job异常信息捕获:", logPrefix, err)
			log.Error("定时器cron-job异常信息捕获：", logPrefix, err)
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER PWERROR]%s %v %s\n", logPrefix, err, stack[:length])

		}
	}()
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := fmt.Sprintf("%s-%s", cachekey.StatsEnterpriseDailyDataLock, "yearly")
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, lockKey)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	now := time.Now()
	startDate := time.Date(now.Year()-1, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := time.Date(now.Year()-1, 12, 31, 0, 0, 0, 0, time.Local)
	s.StatsEnterpriseDaily(distribution_vo.StatsEnterpriseDailyReq{
		StartDate: startDate.Format("2006-01-02"),
		EndDate:   endDate.Format("2006-01-02"),
	})
}

// 分销企业数据统计表stats_enterprise_daily写入数据
func (s StatsEnterpriseService) StatsEnterpriseDaily(req ...distribution_vo.StatsEnterpriseDailyReq) {
	logPrefix := "分销企业数据统计写入"
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	var StartDate, EndDate string
	if len(req) > 0 {
		StartDate = req[0].StartDate
		EndDate = req[0].EndDate
	}

	data := distribution_po.StatsEnterpriseDaily{
		StatDate: StartDate,
		EndDate:  EndDate,
	}
	// 新增分销企业
	sql := `select count(id) from eshop.shop  where org_id=3 and enterprise_id>0 and create_time>=? and create_time <= ?`
	if _, err := s.Engine.SQL(sql, StartDate, fmt.Sprintf("%s 23:59:59", StartDate)).Get(&data.TotalEnterprise); err != nil {
		log.Error(logPrefix, "处理新增分销企业数据失败!当前执行日期=", StartDate, "，发生错误=", err.Error())
		return
	}

	// 新增分销店铺
	sql = `select count(id) from eshop.shop  where org_id=3 and  is_setted_shop=1 and is_setted_time>=? and is_setted_time <= ?;`
	if _, err := s.Engine.SQL(sql, StartDate, fmt.Sprintf("%s 23:59:59", EndDate)).Get(&data.TotalShop); err != nil {
		log.Error(logPrefix, "处理新增分销店铺数据失败!当前执行日期=", StartDate, "，发生错误=", err.Error())
		return
	}

	// 新增成交企业
	startTimestamp := utils.Date2Timestamp(StartDate)
	endTimestamp := utils.Date2Timestamp(EndDate) + 84600
	sql = `select
		count(distinct b.shop_id)
	from
		upetmart.upet_orders a
	left join upetmart.upet_order_goods b on
		a.order_id = b.order_id
	where
		a.store_id = 3
		and a.is_dis = 1
		and a.payment_time>0
		and b.shop_id>0
		and a.add_time >=?
		and a.add_time < ?;`

	if _, err := s.Engine.SQL(sql, startTimestamp, endTimestamp).Get(&data.TotalTransEnterprise); err != nil {
		log.Error(logPrefix, "处理新增商订单成交企业数据失败!当前执行日期=", StartDate, "，发生错误=", err.Error())
		return
	}

	// 新增分销员
	sql = `select count(id) from eshop.dis_distributor  where org_id=3 and  create_time>=? and create_time <= ?;`
	if _, err := s.Engine.SQL(sql, StartDate, fmt.Sprintf("%s 23:59:59", EndDate)).Get(&data.TotalDistributor); err != nil {
		log.Error(logPrefix, "处理新增分销员数据失败!当前执行日期=", StartDate, "，发生错误=", err.Error())
		return
	}

	// 新增成交分销员
	sql = `select count(distinct b.dis_member_id) from
	upetmart.upet_orders a
	left join upetmart.upet_order_goods b on
	a.order_id = b.order_id
	where
	a.store_id = 3
	and a.is_dis = 1
	and b.dis_member_id >0
	and a.payment_time >0
	and a.add_time >= ? 
	and b.add_time < ?;`
	if _, err := s.Engine.SQL(sql, startTimestamp, endTimestamp).Get(&data.TotalTransDistributor); err != nil {
		log.Error(logPrefix, "处理新增成交分销员数据失败!当前执行日期=", StartDate, "，发生错误=", err.Error())
		return
	}

	// 累计业务员
	sql = `select count(*) from eshop.scrm_salesperson a where  a.create_time>=? and a.create_time < ?;`
	if _, err := s.Engine.SQL(sql, StartDate, fmt.Sprintf("%s 23:59:59", EndDate)).Get(&data.TotalSalesman); err != nil {
		log.Error(logPrefix, "处理累计业务员数据失败!当前执行日期=", StartDate, "，发生错误=", err.Error())
		return
	}

	// 判断该日期数据是否存在于stats_enterprise_daily表中， 存在则更新，不存在则插入
	info := distribution_po.StatsEnterpriseDaily{}
	exist, err := s.Session.Table("eshop.stats_enterprise_daily").Where("stat_date=? and end_date=?", StartDate, EndDate).Get(&info)
	if err != nil {
		log.Error(logPrefix, "获取分销企业数据失败err=", err.Error())
		return
	}
EXIST:
	if exist {

		cols := "total_enterprise,total_shop,total_trans_enterprise,total_distributor,total_trans_distributor,total_salesman"
		if affects, err := s.Session.Table("eshop.stats_enterprise_daily").Where("id =?", info.Id).Cols(cols).Update(&data); err != nil {
			log.Error(logPrefix, "更新分销企业数据失败err=", err.Error())
			return
		} else {
			log.Info(logPrefix, "更新分销企业数据成功，日期=", StartDate, ",影响条数=", affects)
		}
	} else {
		if affects, err := s.Session.Table("eshop.stats_enterprise_daily").Insert(&data); err != nil {
			exist = true

			log.Error(logPrefix, "写入分销企业数据失败err=", err.Error())
			goto EXIST
		} else {
			log.Info(logPrefix, "写入分销企业数据成功，日期=", StartDate, ",影响条数=", affects)
		}
	}
}
