package distribution_vo

import (
	viewmodel "eShop/view-model"
	"time"
)

type DisSettlement struct {
	Id              int       `json:"id" xorm:"pk autoincr not null comment('结算ID') INT 'id'"`                                //结算ID
	OrgId           int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`                                 //所属主体id
	ShopId          int       `json:"shop_id" xorm:"default 'null' comment('所属店铺') INT 'shop_id'"`                            //所属店铺
	Status          int       `json:"status" xorm:"default 0 comment('结算状态：0-默认,1-待结算，2-已结算') INT 'status'"`                  //结算状态：0-默认,1-待结算，2-已结算
	SettlementNo    string    `json:"settlement_no" xorm:"not null default '' comment('结算编号') VARCHAR(50) 'settlement_no'"`   //结算编号
	SettlementTime  time.Time `json:"settlement_time" xorm:"default 'null' comment('结算时间') DATETIME 'settlement_time'"`       //结算时间
	OrderNo         string    `json:"order_no" xorm:"default '' comment('订单编号') VARCHAR(50) 'order_no'"`                      //订单编号
	OrderTime       time.Time `json:"order_time" xorm:"default 'null' comment('下单时间') DATETIME 'order_time'"`                 //下单时间
	OrderFinishTime time.Time `json:"order_finish_time" xorm:"default 'null' comment('订单完成时间') DATETIME 'order_finish_time'"` //订单完成时间
	GoodsId         int       `json:"goods_id" xorm:"not null comment('商品id') INT 'goods_id'"`                                //商品id
	GoodsName       string    `json:"goods_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'goods_name'"`                 //商品名称
	PayAmount       int       `json:"pay_amount" xorm:"default 0 comment('支付金额(分)') INT 'pay_amount'"`                        //支付金额(分)
	RefundAmount    int       `json:"refund_amount" xorm:"default 0 comment('退款金额(分)') INT 'refund_amount'"`                  //退款金额(分)
	CommissionRate  int       `json:"commission_rate" xorm:"default 0 comment('佣金比例') INT 'commission_rate'"`                 //佣金比例
	Commission      int       `json:"commission" xorm:"default 0 comment('佣金(分)') INT 'commission'"`                          //佣金(分)
	DistributorId   int       `json:"distributor_id" xorm:"default 0 comment('分销员id') INT 'distributor_id'"`                  //分销员id

}

type DisSettlementView struct {
	Id              int     `json:"id" xorm:"pk autoincr not null comment('结算ID') INT 'id'"`                                //结算ID
	OrgId           int     `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`                                 //所属主体id
	ShopId          int     `json:"shop_id" xorm:"default 'null' comment('所属店铺') INT 'shop_id'"`                            //所属店铺
	Status          int     `json:"status" xorm:"default 0 comment('结算状态：0-默认,1-待结算，2-已结算') INT 'status'"`                  //结算状态：0-默认,1-待结算，2-已结算
	SettlementNo    string  `json:"settlement_no" xorm:"not null default '' comment('结算编号') VARCHAR(50) 'settlement_no'"`   //结算编号
	SettlementTime  string  `json:"settlement_time" xorm:"default 'null' comment('结算时间') DATETIME 'settlement_time'"`       //结算时间
	OrderNo         string  `json:"order_no" xorm:"default '' comment('订单编号') VARCHAR(50) 'order_no'"`                      //订单编号
	OrderTime       string  `json:"order_time" xorm:"default 'null' comment('下单时间') DATETIME 'order_time'"`                 //下单时间
	OrderFinishTime string  `json:"order_finish_time" xorm:"default 'null' comment('订单完成时间') DATETIME 'order_finish_time'"` //订单完成时间
	GoodsId         int     `json:"goods_id" xorm:"not null comment('商品id') INT 'goods_id'"`                                //商品id
	GoodsName       string  `json:"goods_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'goods_name'"`                 //商品名称
	PayAmount       int     `json:"pay_amount" xorm:"default 0 comment('支付金额(分)') INT 'pay_amount'"`                        //支付金额(分)
	RefundAmount    int     `json:"refund_amount" xorm:"default 0 comment('退款金额(分)') INT 'refund_amount'"`                  //退款金额(分)
	CommissionRate  float64 `json:"commission_rate" xorm:"default 0 comment('佣金比例') INT 'commission_rate'"`                 //佣金比例
	Commission      int     `json:"commission" xorm:"default 0 comment('佣金(分)') INT 'commission'"`                          //佣金(分)
	DistributorId   int     `json:"distributor_id" xorm:"default 0 comment('分销员id') INT 'distributor_id'"`                  //分销员id
	DisCommisAmount int     `json:"dis_commis_amount"`                                                                      // 扣除的佣金

	StatusText        string `json:"status_text"`         //结算状态描述
	DistributorName   string `json:"distributor_name"`    //分销员名称
	ShopName          string `json:"shop_name"`           //所属店铺名称
	EnterpriseName    string `json:"enterprise_name"`     //线下企业名称
	EnterpriseId      string `json:"enterprise_id"`       //线下企业id
	DisEnterpriseName string `json:"dis_enterprise_name"` //线下企业名称
	DisRole           int    `json:"dis_role"`            //分销员角色

}

type GetDisSettlementListReq struct {
	viewmodel.BasePageHttpRequest
	Status int `json:"status"`  //结算状态：0-默认,1-待结算，2-已结算
	OrgId  int `json:"org_id"`  //所属主体id
	ShopId int `json:"shop_id"` //所属分销店铺
	//查询条件的类型（enterprise_name=线下企业,settlement_no:结算编号、order_no:订单编号、goods_name：商品名称、distributor_name：分销员名称、distributor_mobile：分销员手机号、distributor_id：分销员id）
	WhereType string `json:"where_type"`
	//查询条件值
	Where string `json:"where"`

	WhereType2 string `json:"where_type2"` //查询条件的类型（settlement_time:结算时间、order_finish_time:结算时间）
	WhereStart string `json:"where_start"` //开始时间
	WhereEnd   string `json:"where_end"`   //结束时间
}
type GetDisSettlementListRes struct {
	viewmodel.BasePageHttpResponse
	Data []DisSettlementView `json:"data"`
}
