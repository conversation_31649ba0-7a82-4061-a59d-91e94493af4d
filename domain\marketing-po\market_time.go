package marketing_po

import (
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// MTimeCard 次卡信息表
type MTimeCardModel struct {
	Id               int       `json:"id" xorm:"pk autoincr  not null comment('唯一数据ID') BIGINT 'id'"`
	ChainId          int       `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId         int       `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	CardId           int       `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	Num              int       `json:"num" xorm:"not null default 0 comment('购买次数') INT 'num'"`
	GiftNum          int       `json:"gift_num" xorm:"not null default 0 comment('赠送次数') INT 'gift_num'"`
	IsLimit          bool      `json:"is_limit" xorm:"not null default 0 comment('是否限制使用次数：0.否，1是') BIT(1) 'is_limit'"`
	LimitNum         int       `json:"limit_num" xorm:"not null default 0 comment('限制次数') INT 'limit_num'"`
	LimitUnit        string    `json:"limit_unit" xorm:"not null default '' comment('使用次数限制单位：DAY日,WEEK周,MONTH月') VARCHAR(16) 'limit_unit'"`
	ApplyProductType string    `json:"apply_product_type" xorm:"not null default '' comment('适用商品类型  ALL:全部商品,SPEC_AVAILABLE:部分商品可用,SPEC_NOT_AVAILABLE:部分商品不可用') VARCHAR(16) 'apply_product_type'"`
	Status           string    `json:"status" xorm:"not null default '' comment('数据状态: ENABLE启用, DISABLE停用') VARCHAR(16) 'status'"`
	IsDeleted        bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识：0未删除,1已删除') BIT(1) 'is_deleted'"`
	CreatedBy        int       `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime      time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy        int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime      time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

// MTimeCard 次卡信息拓展表
type MTimeCard struct {
	MTimeCardModel `xorm:"extends"`
	CardName       string `json:"name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'name'"`
}

func (t MTimeCard) TableName() string {
	return "eshop_saas.m_time_card"
}

// MTimeCardOrder 次卡购买/续充订单表
type MTimeCardOrder struct {
	Id                int       `json:"id" xorm:"pk autoincr not null comment('主键') BIGINT 'id'"`
	TenantId          int       `json:"tenant_id" xorm:"not null default 0 comment('租户号') BIGINT 'tenant_id'"`
	CardInfoId        int       `json:"card_info_id" xorm:"not null default 0 comment('基础卡id;基础卡id') BIGINT 'card_info_id'"`
	CardId            int       `json:"card_id" xorm:"not null default 0 comment('卡id;卡id') BIGINT 'card_id'"`
	CardName          string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	CardType          string    `json:"card_type" xorm:"not null default '' comment('卡类型;卡类型: TIME_CARD;') VARCHAR(16) 'card_type'"`
	RecordId          int       `json:"record_id" xorm:"not null default 0 comment('卡记录id;卡id') BIGINT 'record_id'"`
	BizType           string    `json:"biz_type" xorm:"not null default '' comment('业务类型;购买;续充') VARCHAR(255) 'biz_type'"`
	OrderAmount       float64   `json:"order_amount" xorm:"not null default '0.0000' comment('订单金额') DECIMAL(18) 'order_amount'"`
	OrderId           int       `json:"order_id" xorm:"not null default 0 comment('关联订单id') BIGINT 'order_id'"`
	OrderNo           string    `json:"order_no" xorm:"not null default '' comment('关联订单号') VARCHAR(255) 'order_no'"`
	Status            string    `json:"status" xorm:"not null default '' comment('状态;进行中; 成功; 失败') VARCHAR(255) 'status'"`
	Num               int       `json:"num" xorm:"not null default 0 comment('购买次数') INT 'num'"`
	NumGift           int       `json:"num_gift" xorm:"not null default 0 comment('赠送次数') INT 'num_gift'"`
	CustomerId        int       `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	CustomerMobile    string    `json:"customer_mobile" xorm:"not null default '' comment('客户手机号') VARCHAR(255) 'customer_mobile'"`
	CustomerName      string    `json:"customer_name" xorm:"not null default '' comment('客户名称') VARCHAR(255) 'customer_name'"`
	SellerId          int64     `json:"seller_id" xorm:"not null default 0 comment('收银员id') BIGINT 'seller_id'"`
	SellerName        string    `json:"seller_name" xorm:"not null default '' comment('收银员名') VARCHAR(64) 'seller_name'"`
	Remark            string    `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(255) 'remark'"`
	RefundFlag        bool      `json:"refund_flag" xorm:"not null default 0 comment('是否退单') BIT(1) 'refund_flag'"`
	RefundAmount      float64   `json:"refund_amount" xorm:"not null default '0.0000' comment('退单金额') DECIMAL(18) 'refund_amount'"`
	RefundOrderId     int       `json:"refund_order_id" xorm:"not null default 0 comment('关联退单id') BIGINT 'refund_order_id'"`
	RefundCardNum     int       `json:"refund_card_num" xorm:"not null default 0 comment('卡退的次数') INT 'refund_card_num'"`
	RefundCardNumGift int       `json:"refund_card_num_gift" xorm:"not null default 0 comment('卡退的次数-赠送次数') INT 'refund_card_num_gift'"`
	RefundSellerId    int       `json:"refund_seller_id" xorm:"not null default 0 comment('收银员id') BIGINT 'refund_seller_id'"`
	IsDeleted         bool      `json:"is_deleted" xorm:"not null default 0 comment('是否删除') BIT(1) 'is_deleted'"`
	CreatedBy         int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime       time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy         int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime       time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

func (t MTimeCardOrder) TableName() string {
	return "eshop_saas.m_time_card_order"
}

// MTimeCardRecord 次卡记录
type MTimeCardRecord struct {
	Id             int       `json:"id" xorm:"pk autoincr not null comment('唯一数据ID') BIGINT 'id'"`
	ChainId        int       `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId       int       `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	CardId         int       `json:"card_id" xorm:"not null default 0 comment('卡ID') BIGINT 'card_id'"`
	CardNo         string    `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName       string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	ValidType      string    `json:"valid_type" xorm:"not null default '' comment('有效期类型: PERMANENT永久有效  VALID_DAYS办卡起多少天有效，FIXED_TIME指定时间内有效') VARCHAR(16) 'valid_type'"`
	EnableTime     time.Time `json:"enable_time" xorm:"not null default '0000-01-01 00:00:00' comment('启用时间') DATETIME 'enable_time'"`
	ExpirationTime time.Time `json:"expiration_time" xorm:"not null default '0000-01-01 00:00:00' comment('到期时间') DATETIME 'expiration_time'"`
	RefundTime     time.Time `json:"refund_time" xorm:"not null default '0000-01-01 00:00:00' comment('退款时间') DATETIME 'refund_time'"`
	TotalNum       int       `json:"total_num" xorm:"not null default 0 comment('总次数') INT 'total_num'"`
	BuyNum         int       `json:"buy_num" xorm:"not null default 0 comment('购买次数') INT 'buy_num'"`
	GiftNum        int       `json:"gift_num" xorm:"not null default 0 comment('赠送次数') INT 'gift_num'"`
	BuyingNum      int       `json:"buying_num" xorm:"not null default 0 comment('购买时模板的购买次数') INT 'buying_num'"`
	BuyingGiftNum  int       `json:"buying_gift_num" xorm:"not null default 0 comment('购买时模板的赠送次数') INT 'buying_gift_num'"`
	Balance        float64   `json:"balance" xorm:"not null default '0.0000' comment('余额') DECIMAL(18) 'balance'"`
	Price          float64   `json:"price" xorm:"not null default '0.0000' comment('售价') DECIMAL(18) 'price'"`
	RefundAmount   float64   `json:"refund_amount" xorm:"not null default '0.0000' comment('退款金额') DECIMAL(18) 'refund_amount'"`
	RefundReason   string    `json:"refund_reason" xorm:"not null default '' comment('退款原因') VARCHAR(100) 'refund_reason'"`
	RefundType     string    `json:"refund_type" xorm:"not null default '' comment('退款类型: RETURN_CARD.仅退卡,REFUND_CARD 退卡退款') VARCHAR(16) 'refund_type'"`
	GiftPackage    string    `json:"gift_package" xorm:"not null default '' comment('礼包') VARCHAR(200) 'gift_package'"`
	CustomerId     int       `json:"customer_id" xorm:"not null default 0 comment('客户id') BIGINT 'customer_id'"`
	CustomerName   string    `json:"customer_name" xorm:"not null default '' comment('客户名称') VARCHAR(50) 'customer_name'"`
	CustomerMobile string    `json:"customer_mobile" xorm:"not null default '' comment('手机号') VARCHAR(11) 'customer_mobile'"`
	EmployeeId     int       `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	Status         string    `json:"status" xorm:"not null default '' comment('数据状态:PIN_CARD.销卡，USE.使用中，FREEZE.冻结，NOT_USE不可用，INEFFECTIVE.未生效，IN_THE_CARD退卡中，RETIRED_CARD已退卡') VARCHAR(16) 'status'"`
	Version        int       `json:"version" xorm:"not null default 0 comment('版本号') INT 'version'"`
	IsDeleted      bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识: 0未删除,1已删除') BIT(1) 'is_deleted'"`
	CreatedBy      int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime    time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy      int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime    time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

func (t MTimeCardRecord) TableName() string {
	return "eshop_saas.m_time_card_record"
}

// MTimeCardUseRecord 次卡使用记录
type MTimeCardUseRecord struct {
	Id             int       `json:"id" xorm:"pk autoincr not null comment('唯一数据ID') BIGINT 'id'"`
	ChainId        int       `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId       int       `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	RecordId       int       `json:"record_id" xorm:"not null default 0 comment('卡记录id') BIGINT 'record_id'"`
	CardId         int       `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardNo         string    `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName       string    `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	OperateType    string    `json:"operate_type" xorm:"not null default 'CONSUMPTION' comment('操作类型:CONSUMPTION.消费，TOP_UP.充值，REFUND.退款，RECONCILIATION.调账，RETURN_CARD.退卡') VARCHAR(16) 'operate_type'"`
	Balance        float64   `json:"balance" xorm:"not null default '0.0000' comment('余额') DECIMAL(18) 'balance'"`
	BalanceChange  float64   `json:"balance_change" xorm:"not null default '0.0000' comment('余额变动') DECIMAL(18) 'balance_change'"`
	TotalChange    int       `json:"total_change" xorm:"not null default 0 comment('次数变动') INT 'total_change'"`
	CurrentChange  int       `json:"current_change" xorm:"not null default 0 comment('本次数变动') INT 'current_change'"`
	GiftChange     int       `json:"gift_change" xorm:"not null default 0 comment('赠送次数变动') INT 'gift_change'"`
	Num            int       `json:"num" xorm:"not null default 0 comment('剩余购买次数') INT 'num'"`
	GiftNum        int       `json:"gift_num" xorm:"not null default 0 comment('剩余赠送次数') INT 'gift_num'"`
	ReturnableGift int       `json:"returnable_gift" xorm:"not null default 0 comment('可退赠送次数') INT 'returnable_gift'"`
	ReturnableBuy  int       `json:"returnable_buy" xorm:"not null default 0 comment('可退购买次数') INT 'returnable_buy'"`
	OrderId        int       `json:"order_id" xorm:"not null default 0 comment('订单ID') BIGINT 'order_id'"`
	OrderNo        string    `json:"order_no" xorm:"not null default '' comment('订单编号') VARCHAR(32) 'order_no'"`
	CustomerId     int       `json:"customer_id" xorm:"not null default 0 comment('客户ID') BIGINT 'customer_id'"`
	EmployeeId     int       `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	GiftPackage    string    `json:"gift_package" xorm:"not null default '' comment('礼包') VARCHAR(200) 'gift_package'"`
	Remark         string    `json:"remark" xorm:"not null default '' comment('备注') VARCHAR(200) 'remark'"`
	Status         string    `json:"status" xorm:"not null default '' comment('数据状态') VARCHAR(16) 'status'"`
	IsDeleted      bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识:0未删除,1已删除') BIT(1) 'is_deleted'"`
	CreatedBy      int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime    time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy      int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime    time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

// MTimeCardUseExt 次卡使用扩展表
type MTimeCardUseExt struct {
	Id            int     `json:"id" xorm:"pk autoincr not null comment('唯一数据ID') BIGINT 'id'"`
	ChainId       int     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId      int     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	RecordId      int     `json:"record_id" xorm:"not null default 0 comment('卡记录id') BIGINT 'record_id'"`
	CardId        int     `json:"card_id" xorm:"not null default 0 comment('卡id') BIGINT 'card_id'"`
	CardNo        string  `json:"card_no" xorm:"not null default '' comment('卡号') VARCHAR(64) 'card_no'"`
	CardName      string  `json:"card_name" xorm:"not null default '' comment('卡名称') VARCHAR(100) 'card_name'"`
	Status        string  `json:"status" xorm:"not null default '' comment('数据状态') VARCHAR(16) 'status'"`
	CustomerId    int     `json:"customer_id" xorm:"not null default 0 comment('客户ID') BIGINT 'customer_id'"`
	EmployeeId    int     `json:"employee_id" xorm:"not null default 0 comment('员工id') BIGINT 'employee_id'"`
	TotalChange   int     `json:"total_change" xorm:"not null default 0 comment('次数变动') INT 'total_change'"`
	AllBalance    float64 `json:"all_balance" xorm:"not null default '0.0000' comment('总余额') DECIMAL(18) 'all_balance'"`
	Balance       float64 `json:"balance" xorm:"not null default '0.0000' comment('余额') DECIMAL(18) 'balance'"`
	BalanceChange float64 `json:"balance_change" xorm:"not null default '0.0000' comment('余额变动') DECIMAL(18) 'balance_change'"`
	CurrentChange int     `json:"current_change" xorm:"not null default 0 comment('本次数变动') INT 'current_change'"`
	GiftChange    int     `json:"gift_change" xorm:"not null default 0 comment('赠送次数变动') INT 'gift_change'"`
	Num           int     `json:"num" xorm:"not null default 0 comment('剩余购买次数') INT 'num'"`
	GiftNum       int     `json:"gift_num" xorm:"not null default 0 comment('剩余赠送次数') INT 'gift_num'"`
	BuyNum        int     `json:"buy_num" xorm:"not null default 0 comment('购买次数') INT 'buy_num'"`
	AllGiftNum    int     `json:"all_gift_num" xorm:"not null default 0 comment('总剩余赠送次数') INT 'all_gift_num'"`
	TotalNum      int     `json:"total_num" xorm:"not null default 0 comment('总次数') INT 'total_num'"`
	BuyingNum     int     `json:"buying_num" xorm:"not null default 0 comment('购买时模板的购买次数') INT 'buying_num'"`
	BuyingGiftNum int     `json:"buying_gift_num" xorm:"not null default 0 comment('购买时模板的赠送次数') INT 'buying_gift_num'"`
	Price         float64 `json:"price" xorm:"not null default '0.0000' comment('售价') DECIMAL(18) 'price'"`
	OperateType   string  `json:"operate_type" xorm:"not null default 'CONSUMPTION' comment('操作类型:CONSUMPTION.消费，TOP_UP.充值，REFUND.退款，RECONCILIATION.调账，RETURN_CARD.退卡') VARCHAR(16) 'operate_type'"`
}

func (t MTimeCardUseExt) TableName() string {
	return "eshop_saas.m_time_card_use_ext"
}

func (t MTimeCardUseRecord) TableName() string {
	return "eshop_saas.m_time_card_use_record"
}

// 操作类型
const (
	OperateTypeOpenCard       = "OPEN_CARD"      //开卡
	OperateTypeConsumption    = "CONSUMPTION"    // 消费
	OperateTypeTopUp          = "TOP_UP"         // 充值
	OperateTypeRefund         = "REFUND"         // 退款
	OperateTypeReconciliation = "RECONCILIATION" // 调账
	OperateTypeReturnCard     = "RETURN_CARD"    // 退卡
	OperateTypeCHARGEBACK     = "CHARGEBACK"     // 开卡退单
	OperateTypeRECYCLE        = "RECYCLE"        // 流水记录-退卡

	PIN_CARD     = "PIN_CARD"
	USE          = "USE"          // 使用中
	FREEZE       = "FREEZE"       // 冻结
	NOT_USE      = "NOT_USE"      // 不可用
	INEFFECTIVE  = "INEFFECTIVE"  // 未生效
	IN_THE_CARD  = "IN_THE_CARD"  // 退卡中
	RETIRED_CARD = "RETIRED_CARD" // 已退卡

)

// MApplyProduct 优惠券适用商品信息
type MApplyProduct struct {
	Id               int       `json:"id" xorm:"pk not null comment('唯一数据ID') BIGINT 'id'"`
	ChainId          int       `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId         int       `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	RefId            int       `json:"ref_id" xorm:"not null default 0 comment('关联ID') BIGINT 'ref_id'"`
	Type             string    `json:"type" xorm:"not null default '' comment('关联类型:APPLY_REF_TYPE: COUPON 优惠卷，TIMECARD 次卡，STORECARD 储值卡') VARCHAR(16) 'type'"`
	ApplyType        string    `json:"apply_type" xorm:"not null default '' comment('适用类型:PRODUCT商品,TYPE商品类型(商品表示具体某一个商品，商品类型表示某一个商品品类下的全部商品)') VARCHAR(16) 'apply_type'"`
	ProductType      string    `json:"product_type" xorm:"not null default '' comment('商品类型，GOODS实物，SER服务，ALIVE活体') VARCHAR(15) 'product_type'"`
	ProductRefId     int       `json:"product_ref_id" xorm:"not null default 0 comment('商品关联ID（商品sku_id，或者品类id）') BIGINT 'product_ref_id'"`
	ProductStatus    string    `json:"product_status" xorm:"not null default '' comment('商品状态: OK已上架,STOP已下架,DEL删除') VARCHAR(16) 'product_status'"`
	ProductIsSellOut bool      `json:"product_is_sell_out" xorm:"not null default 0 comment('商品是否售罄:0否,1是') BIT(1) 'product_is_sell_out'"`
	IsDeleted        bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识:0未删除,1已删除') BIT(1) 'is_deleted'"`
	CreatedBy        int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime      time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy        int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime      time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

func (t MApplyProduct) TableName() string {
	return "eshop_saas.m_apply_product"
}

type MCardInfo struct {
	Id               int64     `json:"id" xorm:"pk not null comment('唯一数据id') BIGINT 'id'"`
	ChainId          int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	TenantId         int64     `json:"tenant_id" xorm:"not null default 0 comment('店铺ID') BIGINT 'tenant_id'"`
	CardType         string    `json:"card_type" xorm:"not null default '' comment('卡类型 STORE_CARD 储值卡 TIME_CARD次卡') VARCHAR(16) 'card_type'"`
	Code             string    `json:"code" xorm:"not null default '' comment('编码') VARCHAR(64) 'code'"`
	Name             string    `json:"name" xorm:"not null default '' comment('卡名称') VARCHAR(32) 'name'"`
	Price            float64   `json:"price" xorm:"not null default '0.0000' comment('销售价格') DECIMAL(18) 'price'"`
	GiftPackage      string    `json:"gift_package" xorm:"not null default '' comment('权益') VARCHAR(200) 'gift_package'"`
	Restriction      string    `json:"restriction" xorm:"not null default '' comment('使用限制') VARCHAR(100) 'restriction'"`
	ValidType        string    `json:"valid_type" xorm:"not null default '' comment('有效期类型: PERMANENT永久有效  VALID_DAYS办卡起多少天有效，FIXED_TIME指定时间内有效') VARCHAR(16) 'valid_type'"`
	Validity         string    `json:"validity" xorm:"not null default '' comment('有效期拼接字符串') VARCHAR(32) 'validity'"`
	WithinDay        int       `json:"within_day" xorm:"not null default 0 comment('办卡起多少天有效') INT 'within_day'"`
	StartTime        time.Time `json:"start_time" xorm:"not null default '0000-01-01 00:00:00' comment('有效期开始时间') DATETIME 'start_time'"`
	EndTime          time.Time `json:"end_time" xorm:"not null default '0000-01-01 00:00:00' comment('有效期结束时间') DATETIME 'end_time'"`
	MemberNum        int       `json:"member_num" xorm:"not null default 0 comment('会员数') INT 'member_num'"`
	ArrivalType      string    `json:"arrival_type" xorm:"not null default '' comment('到账类型: IMMEDIATELY立即到账,DELAY延时到账,INSTALLMENT分期到账') VARCHAR(16) 'arrival_type'"`
	DelayNum         int       `json:"delay_num" xorm:"not null default 0 comment('延期时间') INT 'delay_num'"`
	DelayUnit        string    `json:"delay_unit" xorm:"not null default '' comment('延期单位: HOUR小时,DAY天') VARCHAR(16) 'delay_unit'"`
	InstallmentDay   int       `json:"installment_day" xorm:"not null default 0 comment('分期到账几号到账') INT 'installment_day'"`
	InstallmentMonth int       `json:"installment_month" xorm:"not null default 0 comment('分几月到账') INT 'installment_month'"`
	Remark           string    `json:"remark" xorm:"not null default '' comment('说明') VARCHAR(100) 'remark'"`
	Show             bool      `json:"show" xorm:"not null default 0 comment('是否展示模板') BIT(1) 'show'"`
	Recharge         bool      `json:"recharge" xorm:"not null default 0 comment('是否支持续充') BIT(1) 'recharge'"`
	Img              string    `json:"img" xorm:"default 'null' comment('选中的背景图地址') VARCHAR(200) 'img'"`
	IsDefault        bool      `json:"is_default" xorm:"not null default 0 comment('是否为默认创建') BIT(1) 'is_default'"`
	Status           string    `json:"status" xorm:"not null default 'ON_SELL' comment('数据状态: ON_SELL出售中, UN_SELL已停售') VARCHAR(16) 'status'"`
	IsDeleted        bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识') BIT(1) 'is_deleted'"`
	CreatedBy        int64     `json:"created_by" xorm:"not null default 0 comment('创建人') BIGINT 'created_by'"`
	CreatedTime      time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedBy        int64     `json:"updated_by" xorm:"not null default 0 comment('更新人') BIGINT 'updated_by'"`
	UpdatedTime      time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time' updated"`
}

func (t MCardInfo) TableName() string {
	return "eshop_saas.m_card_info"
}

// TimeCardQuery 次卡查询参数
type TimeCardQuery struct {
	StoreId     string `json:"store_id"`
	ChainId     int64  `json:"chain_id"`
	CustomerId  int    `json:"customer_id"`
	SkuIds      []int  `json:"sku_ids"`
	ProductType string `json:"product_type"`
	CategoryIds []int  `json:"category_ids"`
}

// TimeCardInfo 次卡信息出参
type TimeCardInfo struct {
	TimeCard      MTimeCard       `json:"time_card"`      // 次卡基础信息
	RemainNum     int             `json:"remain_num"`     // 剩余购买次数
	TotalNum      int             `json:"total_num"`      // 总次数(购买次数+赠送次数)
	GiftNum       int             `json:"gift_num"`       // 剩余赠送次数
	ApplyProducts []MApplyProduct `json:"apply_products"` // 适用商品列表
	CanUse        bool            `json:"can_use"`        // 是否可用(根据使用限制判断)
	UsedNum       int             `json:"used_num"`       // 当前限制周期内已使用次数(日/周/月)
	CardNo        string          `json:"card_no"`        // 次卡卡号
}

// GetTimeCardInfo 获取次卡信息
func (m *MTimeCard) GetTimeCardInfo(session *xorm.Session, query TimeCardQuery) (data []TimeCardInfo, err error) {
	if query.ChainId == 0 {
		return nil, errors.New("chain_id is required")
	}
	if query.StoreId == "" {
		return nil, errors.New("store_id is required")
	}

	// 联表查询次卡和卡信息
	var timeCards []MTimeCard

	err = session.Table("eshop_saas.m_time_card").
		Join("LEFT", "eshop_saas.m_card_info", "m_time_card.card_id = m_card_info.id").
		Where("eshop_saas.m_time_card.chain_id = ?", query.ChainId).
		Where("eshop_saas.m_time_card.tenant_id = ?", query.StoreId).
		Where("m_time_card.status = 'ON_SELL'").
		Where("eshop_saas.m_time_card.is_deleted = 0").
		Find(&timeCards)

	if err != nil {
		return nil, err
	}

	now := time.Now()
	for _, card := range timeCards {
		info := TimeCardInfo{
			TimeCard: card,
		}

		// 查询适用商品
		var products []MApplyProduct
		err = session.Table("eshop_saas.m_apply_product").
			Where("chain_id = ?", query.ChainId).
			Where("tenant_id = ?", query.StoreId).
			Where("ref_id = ?", card.CardId).
			Where("type = 'TIME_CARD'").
			Where("is_deleted = 0").
			Find(&products)
		if err != nil {
			return nil, err
		}

		// 过滤商品
		if len(query.SkuIds) > 0 {
			var filteredProducts []MApplyProduct
			for _, product := range products {
				for _, skuid := range query.SkuIds {
					if product.ProductRefId == skuid {
						filteredProducts = append(filteredProducts, product)
						break
					}
				}
			}
			products = filteredProducts
		}

		info.ApplyProducts = products

		// 查询次卡记录获取卡号
		var records []MTimeCardRecord
		err = session.Where("card_id = ?", card.CardId).
			Where("customer_id = ?", query.CustomerId).
			Where("is_deleted = 0").
			Find(&records)
		if err != nil {
			return nil, err
		}

		// 为每个记录创建一个TimeCardInfo
		var cardInfos []TimeCardInfo
		for _, record := range records {
			cardInfo := TimeCardInfo{
				TimeCard:      card,
				RemainNum:     record.BuyNum,
				TotalNum:      record.TotalNum,
				GiftNum:       record.GiftNum,
				CardNo:        record.CardNo,
				ApplyProducts: products, // 使用之前查询的适用商品
			}

			// 查询当前限制周期内已使用次数
			if card.IsLimit {
				var startTime string
				switch card.LimitUnit {
				case "DAY":
					startTime = now.Format("2006-01-02") + " 00:00:00"
				case "WEEK":
					offset := int(now.Weekday())
					if offset == 0 {
						offset = 7
					}
					weekStart := now.AddDate(0, 0, -offset+1)
					startTime = weekStart.Format("2006-01-02") + " 00:00:00"
				case "MONTH":
					startTime = now.Format("2006-01") + "-01 00:00:00"
				}

				var totalChange float64
				totalChange, err = session.Table("eshop_saas.m_time_card_use_record").
					Where("card_id = ?", card.Id).
					Where("customer_id = ?", query.CustomerId).
					Where("card_no = ?", record.CardNo). // 添加卡号条件
					Where("operate_type = 'CONSUMPTION'").
					Where("created_time >= ?", startTime).
					Where("created_time <= ?", now.Format("2006-01-02 15:04:05")).
					Where("is_deleted = 0").
					Sum(new(MTimeCardUseRecord), "total_change")

				if err != nil {
					return nil, err
				}

				cardInfo.UsedNum = int(totalChange)
				cardInfo.CanUse = cardInfo.UsedNum < card.LimitNum
			} else {
				cardInfo.CanUse = true
			}

			cardInfos = append(cardInfos, cardInfo)
		}

		// 将该卡的所有记录添加到结果中
		data = append(data, cardInfos...)
	}

	return data, nil
}

// UpdateTimeCardUse 更新次卡使用记录
func (m *MTimeCardRecord) UpdateTimeCardUse(session *xorm.Session, cardNo string, customerId int64, buyNumDeduct int, giftNumDeduct int, deductNum int, totalDeductAmount float64, version int) error {
	sql := `UPDATE eshop_saas.m_time_card_record SET 
		buy_num = buy_num - ?, 
		gift_num = gift_num - ?, 
		total_num = total_num - ?, 
		balance = balance - ?, 
		version = version + 1 
		WHERE card_no = ? AND customer_id = ? AND status = 'IN_USE' AND version = ?`

	result, err := session.Exec(sql,
		buyNumDeduct,
		giftNumDeduct,
		deductNum,
		totalDeductAmount,
		cardNo,
		customerId,
		version)

	if err != nil {
		return err
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if affected == 0 {
		return errors.New("次卡记录已被修改,请重试")
	}

	return nil
}

// GetTimeCardByCardNo 根据卡号获取次卡记录
func (m *MTimeCardRecord) GetTimeCardByCardNo(session *xorm.Session, cardNo string, customerId int64) (*MTimeCardRecord, error) {
	var record MTimeCardRecord
	exists, err := session.Where("card_no = ?", cardNo).
		Where("customer_id = ?", customerId).
		Where("status = 'IN_USE'").
		Where("is_deleted = 0").
		Get(&record)

	if err != nil {
		return nil, err
	}

	if !exists {
		return nil, errors.New("次卡不存在或已失效")
	}

	return &record, nil
}

// CreateTimeCardUseRecord 创建次卡使用记录
func (m *MTimeCardUseRecord) CreateTimeCardUseRecord(session *xorm.Session, record *MTimeCardUseRecord) error {
	if record == nil {
		return errors.New("次卡使用记录不能为空")
	}

	_, err := session.InsertOne(record)
	if err != nil {
		return fmt.Errorf("创建次卡使用记录失败: %v", err)
	}

	return nil
}

// UpdateTimeCardRecordStatus 更新次卡记录状态
func (m *MTimeCardRecord) UpdateTimeCardRecordStatus(session *xorm.Session, cardNo string) error {
	_, err := session.Where("card_no = ?", cardNo).Update(&MTimeCardRecord{
		RefundTime: time.Now(),
		RefundType: OperateTypeReturnCard,
		Status:     RETIRED_CARD,
	})
	return err
}

// GetCardInfo 获取次卡基础信息
func (m *MTimeCard) GetCardInfo(session *xorm.Session, chainId int64, tenantId int64, cardId int64) (*MCardInfo, error) {
	var cardInfo MCardInfo
	exists, err := session.Where("id = ?", cardId).
		Where("chain_id = ?", chainId).
		Where("tenant_id = ?", tenantId).
		Where("card_type = 'TIME_CARD'").
		Where("status = 'ON_SELL'").
		Where("is_deleted = 0").
		Get(&cardInfo)

	if err != nil {
		return nil, fmt.Errorf("查询次卡基础信息失败: %v", err)
	}

	if !exists {
		return nil, errors.New("次卡基础信息不存在或已下架")
	}

	return &cardInfo, nil
}

// RefundTimeCard 退还次卡使用记录
func (m *MTimeCardRecord) RefundTimeCard(session *xorm.Session, cardNo string, customerId int64, buyNumRefund int, giftNumRefund int, refundNum int, totalRefundAmount float64, version int) error {
	sql := `UPDATE eshop_saas.m_time_card_record SET 
		buy_num = buy_num + ?, 
		gift_num = gift_num + ?, 
		total_num = total_num + ?, 
		balance = balance + ?, 
		version = version + 1 
		WHERE card_no = ? AND customer_id = ? AND status = 'IN_USE' AND version = ?`

	result, err := session.Exec(sql,
		buyNumRefund,
		giftNumRefund,
		refundNum,
		totalRefundAmount,
		cardNo,
		customerId,
		version)

	if err != nil {
		return fmt.Errorf("更新次卡记录失败: %v", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}

	if affected == 0 {
		return errors.New("次卡记录不存在或版本已变更")
	}

	return nil
}
