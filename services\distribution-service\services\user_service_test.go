package services

import (
	po "eShop/domain/upetmart-po"
	"eShop/infra/log"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestUpetUserService_GetExtremeJoyUserList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req vo.UserPageReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []po.UserMemberView
		want1   int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				req: vo.UserPageReq{
					OrgId:      2,
					Mobile:     "",
					MemberName: "",
					PageIndex:  1,
					PageSize:   10,
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &UpetUserService{
				BaseService: tt.fields.BaseService,
			}
			got, got1, err := s.GetUserList(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtremeJoyUserList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExtremeJoyUserList() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetExtremeJoyUserList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestUpetUserService_GetUserDetail(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req vo.UserDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *vo.UserDetail
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				req: vo.UserDetailReq{
					MemberId: 2,
					OrgId:    2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log.Init()
			s := &UpetUserService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.GetUserDetail(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpetUserService.GetUserDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpetUserService.GetUserDetail() = %v, want %v", got, tt.want)
			}
		})
	}
}
