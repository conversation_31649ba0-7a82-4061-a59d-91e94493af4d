package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// XSecurityCode 防伪码-深圳利都
type XSecurityCode struct {
	Id         int       `json:"id" xorm:"pk autoincr not null 'id'"`            // 自增主键
	Code       string    `json:"code" xorm:"not null unique VARCHAR(50) 'code'"` // 防伪码
	CreateTime time.Time `json:"create_time" xorm:"not null 'create_time'"`      // 创建时间
}

// TableName 表名
func (x *XSecurityCode) TableName() string {
	return "blky.xsecurity_code"
}

// GetByCode 根据防伪码获取记录
func (x *XSecurityCode) GetByCode(session *xorm.Session, code string) (*XSecurityCode, error) {
	var securityCode XSecurityCode
	has, err := session.Where("code = ?", code).Get(&securityCode)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &securityCode, nil
}