package petai_vo

import viewmodel "eShop/view-model"

type UserAgreeAuthReq struct {
	UserInfoId   string `json:"user_info_id"`  //用户id (前端不用传)
	IsAuthorized int    `json:"is_authorized"` //用户是否授权使用阿闻数据，0-否，1-是，2-拒绝
}
type UserAgreeAuthResp struct {
	viewmodel.BaseHttpResponse
}

type UserInfo struct {
	UserInfoId string `json:"user_info_id"` //用户信息id
	UserAvatar string `json:"user_avatar"`  //用户头像
	UserName   string `json:"user_name"`    //用户姓名
	UserSex    int    `json:"user_sex"`     //用户性别，0-未知，1-男，2-女
	// 用户生日
	UserBirthday string `json:"user_birthday"`
	// 用户所在国家
	Country string `json:"country"`
	// 用户所在省份
	Province string `json:"province"`
	// 用户所在城市
	City string `json:"city"`
	// 用户所在区域
	Area string `json:"area"`
	// 用户所在国家编码
	CountryCode string `json:"country_code"`
	// 用户所在省份编码
	ProvinceCode string `json:"province_code"`
	// 用户所在城市编码
	CityCode string `json:"city_code"`
	// 用户所在区域编码
	AreaCode string `json:"area_code"`
	// 初次养宠时间
	FirstRaisesPet    string `json:"first_raises_pet"`
	UserMobile        string `json:"user_mobile"`         // 用户加*手机号码
	EncryptUserMobile string `json:"encrypt_user_mobile"` // 用户加密手机号码
	IsAuthorized      int    `json:"is_authorized"`       //用户是否授权使用阿闻数据，0-否，1-是，2-拒绝
	IsNewUser         bool   `json:"is_new_user"`         // 是否为新用户
}
