package services

import (
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"fmt"
	"sync"
	"testing"
)

func Test_callCozeWithToken(t *testing.T) {
	cache.CacheSources[cache_source.EShop] = cache.Address(config.Get("redis.PassAddr"))
	type args struct {
		rdb       cache.MemoryCache
		requestID int
		agentID   string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				rdb:       cache.NewMemberCache(cache.CacheSources[cache_source.EShop]),
				requestID: 1,
				agentID:   "agent1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a, err := tt.args.rdb.LPush("coze_tokens:"+tt.args.agentID, "1")
			fmt.Println(a, err)
			if err := utils.CallCozeWithToken(tt.args.rdb, tt.args.requestID, tt.args.agentID); (err != nil) != tt.wantErr {
				t.Errorf("callCozeWithToken() error = %v, wantErr %v", err, tt.wantErr)
			}

		})
	}
}

func Test_callCozeWithTokenMaxConcurrency(t *testing.T) {
	cache.CacheSources[cache_source.EShop] = cache.Address(config.Get("redis.PassAddr"))
	type args struct {
		rdb       cache.MemoryCache
		requestID int
		agentID   string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				rdb:       cache.NewMemberCache(cache.CacheSources[cache_source.EShop]),
				requestID: 1,
				agentID:   "agent1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 最大并发数为3
			maxConcurrent := 7
			// // 初始化令牌桶
			// for i := 0; i < maxConcurrent; i++ {
			// 	tt.args.rdb.LPush("coze_tokens:"+tt.args.agentID, "1")
			// }

			var wg sync.WaitGroup
			for i := 0; i < maxConcurrent+2; i++ {
				wg.Add(1)
				go func(id int) {
					defer wg.Done()
					err := utils.CallCozeWithToken(tt.args.rdb, id, tt.args.agentID)
					if err != nil {
						t.Errorf("callCozeWithToken() error = %v, id %v", err, id)
					}
				}(i)
			}
			wg.Wait()

		})
	}
}
