package utils

import (
	"fmt"
	"reflect"
	"strings"
	"time"
	"unicode"
)

// 查询操作符
const (
	TagEq      = "eq"      // 等于
	TagGt      = "gt"      // 大于
	TagGte     = "gte"     // 大于等于
	TagLt      = "lt"      // 小于
	TagLte     = "lte"     // 小于等于
	TagLike    = "like"    // 模糊查询
	TagBetween = "between" // 范围查询
	TagIn      = "in"      // IN查询
	TagNotIn   = "not_in"  // NOT IN查询
	TagIsNull  = "is_null" // IS NULL查询
)

// QueryTag 查询标签结构
type QueryTag struct {
	Column   string // 字段名
	Table    string // 表名（可选）
	Operator string // 操作符
}

// GetQueryCondition 获取查询条件
func GetQueryCondition(queryVO any) string {
	var conditions []string
	val := reflect.ValueOf(queryVO)
	typ := val.Type()

	// 处理指针类型
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
		typ = val.Type()
	}

	// 递归处理结构体字段，使用指针传递conditions
	processStruct(&conditions, val, typ)
	// log.Infof("Generated SQL conditions: %s", strings.Join(conditions, " AND "))
	return strings.Join(conditions, " AND ")
}

func processStruct(conditions *[]string, val reflect.Value, typ reflect.Type) {
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		fieldValue := val.Field(i)

		// 处理嵌套结构体
		if field.Anonymous {
			processStruct(conditions, fieldValue, field.Type)
			continue
		}

		// 获取并解析查询标签
		queryTag := field.Tag.Get("query")
		if queryTag == "" {
			continue
		}

		// 检查是否允许零值查询
		allowZero := strings.Contains(queryTag, ",allow_zero")

		// 获取字段值，如果为零且不允许零值查询，则跳过
		// 当包含allow_zero标签时，如果值为-1，也需要跳过字段，不拼接查询条件
		if fieldValue.IsZero() && !allowZero {
			continue
		}
		// 当包含allow_zero标签时，如果字段是整数类型且值为-1，则跳过字段，不拼接查询条件
		if allowZero && fieldValue.CanInt() && fieldValue.Int() == -1 {
			continue
		}

		// 解析查询标签，获取所有条件
		queryConditions := parseQueryTag(queryTag, field.Name)
		for _, qt := range queryConditions {
			// 构建完整的列名
			columnName := qt.Column
			if qt.Table != "" {
				columnName = qt.Table + "." + columnName
			}

			// 根据操作符类型构建查询条件
			var condition string
			switch qt.Operator {
			case TagLike:
				if fieldValue.Kind() == reflect.String {
					condition = fmt.Sprintf("%s LIKE '%%%s%%'", columnName, fieldValue.String())
				}
			case TagBetween:
				if fieldValue.Kind() == reflect.Slice && fieldValue.Len() == 2 {
					condition = fmt.Sprintf("%s BETWEEN %v AND %v", columnName,
						formatValue(fieldValue.Index(0)),
						formatValue(fieldValue.Index(1)))
				}
			case TagIn, TagNotIn:
				if fieldValue.Kind() == reflect.Slice {
					values := make([]string, fieldValue.Len())
					for i := 0; i < fieldValue.Len(); i++ {
						values[i] = fmt.Sprint(formatValue(fieldValue.Index(i)))
					}
					operator := "IN"
					if qt.Operator == TagNotIn {
						operator = "NOT IN"
					}
					condition = fmt.Sprintf("%s %s (%s)", columnName, operator, strings.Join(values, ","))
				}
			case TagIsNull:
				condition = fmt.Sprintf("%s IS NULL", columnName)
			default:
				switch fieldValue.Kind() {
				case reflect.String:
					condition = fmt.Sprintf("%s %s '%s'", columnName, getOperator(qt.Operator), fieldValue.String())
				case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
					condition = fmt.Sprintf("%s %s %d", columnName, getOperator(qt.Operator), fieldValue.Int())
				case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
					condition = fmt.Sprintf("%s %s %d", columnName, getOperator(qt.Operator), fieldValue.Uint())
				case reflect.Float32, reflect.Float64:
					condition = fmt.Sprintf("%s %s %f", columnName, getOperator(qt.Operator), fieldValue.Float())
				case reflect.Bool:
					condition = fmt.Sprintf("%s %s %t", columnName, getOperator(qt.Operator), fieldValue.Bool())
				case reflect.Struct:
					if t, ok := fieldValue.Interface().(time.Time); ok {
						condition = fmt.Sprintf("%s %s '%s'", columnName, getOperator(qt.Operator), t.Format("2006-01-02 15:04:05"))
					}
				}
			}

			if condition != "" {
				*conditions = append(*conditions, condition)
			}
		}
	}
}

// parseQueryTag 解析查询标签
// 支持格式：
// 1. "column:operator" -> [{Column: "column", Operator: "operator"}]
// 2. "table.column:operator" -> [{Table: "table", Column: "column", Operator: "operator"}]
// 3. "table.column1:operator1;table.column2:operator2" -> 多个条件
//
// allow_zero 标签说明:
// 用于指示当字段值为零值时是否参与查询条件拼接。
// 默认情况下，如果字段值为其类型的零值（例如，int 类型的 0，string 类型的 ""），该字段将不会被用于构建查询条件。
// 如果在 query 标签中添加了 ",allow_zero"，则即使字段值为零值，也会参与查询条件拼接。
// 特殊情况：当字段为整数类型且包含 ",allow_zero" 标签时，如果其值为 -1，则该字段仍会被跳过，不拼接查询条件。
// 示例:
// type UserQuery struct {
//     ID     int    `query:"id:eq"` // ID为0时跳过
//     Status int    `query:"status:eq,allow_zero"` // Status为0时参与查询，但为-1时跳过
//     Name   string `query:"name:like"` // Name为空字符串时跳过
// }
func parseQueryTag(tag string, fieldName string) []QueryTag {
	var conditions []QueryTag

	if tag == "" {
		return conditions
	}

	// 分割多个条件
	conditionParts := strings.Split(tag, ";")
	for _, part := range conditionParts {
		var condition QueryTag

		// 如果标签只包含操作符
		if !strings.Contains(part, ":") {
			condition.Operator = part
			condition.Column = camelToSnake(fieldName)
			conditions = append(conditions, condition)
			continue
		}

		// 解析 column:operator 或 table.column:operator 格式
		parts := strings.Split(part, ":")
		if len(parts) != 2 {
			continue
		}

		// 设置操作符，并去除可能存在的额外标志（如allow_zero）
		operatorAndFlags := strings.Split(parts[1], ",")
		condition.Operator = operatorAndFlags[0]

		// 解析表名和字段名
		columnParts := strings.Split(parts[0], ".")
		if len(columnParts) == 2 {
			condition.Table = columnParts[0]
			condition.Column = columnParts[1]
		} else {
			condition.Column = parts[0]
		}

		conditions = append(conditions, condition)
	}

	return conditions
}

// formatValue 格式化字段值
func formatValue(value reflect.Value) interface{} {
	switch value.Kind() {
	case reflect.String:
		return fmt.Sprintf("'%s'", value.String())
	case reflect.Struct:
		if t, ok := value.Interface().(time.Time); ok {
			return fmt.Sprintf("'%s'", t.Format("2006-01-02 15:04:05"))
		}
		return value.Interface()
	default:
		return value.Interface()
	}
}

// getOperator 获取SQL操作符
func getOperator(queryTag string) string {
	switch queryTag {
	case TagEq:
		return "="
	case TagGt:
		return ">"
	case TagGte:
		return ">="
	case TagLt:
		return "<"
	case TagLte:
		return "<="
	default:
		return "="
	}
}

// camelToSnake 将驼峰命名转换为下划线命名
func camelToSnake(s string) string {
	var result strings.Builder
	for i, r := range s {
		if unicode.IsUpper(r) {
			if i > 0 {
				result.WriteByte('_')
			}
			result.WriteRune(unicode.ToLower(r))
		} else {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// GetXormColumnName 从 xorm 标签中获取数据库列名
func GetXormColumnName(field reflect.StructField) string {
	xormTag := field.Tag.Get("xorm")
	if xormTag == "" {
		// 如果没有 xorm 标签，将字段名转换为下划线格式
		return camelToSnake(field.Name)
	}

	// 解析xorm标签，获取列名（去掉单引号）
	if strings.Contains(xormTag, "'") {
		parts := strings.Split(xormTag, "'")
		if len(parts) >= 2 {
			return parts[1]
		}
	}

	// 如果 xorm 标签存在但没有指定列名（没有用单引号包裹的情况）
	// 也将字段名转换为下划线格式
	return camelToSnake(field.Name)
}
