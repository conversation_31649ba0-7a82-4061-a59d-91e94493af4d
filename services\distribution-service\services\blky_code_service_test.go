package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	"eShop/view-model/distribution-vo"
	_ "github.com/go-sql-driver/mysql"
	"reflect"
	"testing"
)

func TestBlkyCodeService_DeductCommission(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.CommissionWithdrawReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				in: distribution_vo.CommissionWithdrawReq{
					DisCommisRate: 2,
					Code:          "1051448001",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := BlkyCodeService{
				BaseService: tt.fields.BaseService,
			}
			if _, err := s.DeductCommission(tt.args.in); (err != nil) != tt.wantErr {
				t.Errorf("DeductCommission() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBlkyCodeService_QueryCodeList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in distribution_vo.BlkyQueryCodeRequest
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   distribution_vo.BlkyCodeListData
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				in: distribution_vo.BlkyQueryCodeRequest{
					Code: "602118128442",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := BlkyCodeService{
				BaseService: tt.fields.BaseService,
			}
			if got := s.QueryCodeList(tt.args.in); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("QueryCodeList() = %v, want %v", got, tt.want)
			}
		})
	}
}
