package common

import (
	po "eShop/domain/omnibus-po"
	"eShop/infra/enum"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	vo "eShop/view-model/omnibus-vo"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
)

type AsyncCommService struct {
	BaseService
}

// 创建任务
func (h AsyncCommService) CreatSyncTask(r *http.Request, req vo.TaskListAsync) error {
	h.Begin()
	defer h.Close()

	session := h.Session

	//判断如果有相对的任务正在执行，则直接返回错误
	if req.KeyStr != "" {
		if isExist, err := session.Table("task_list_async").Where("task_content = ? and task_status in (1,2) and key_str = ?", req.TaskContent, req.KeyStr).Exist(); err != nil {
			return err
		} else if isExist {
			return errors.New("有相")
		}
	}

	task := po.TaskListAsync{}
	copier.Copy(&task, req)

	task.TaskStatus = enum.TaskStatusNotStart
	//先注释，测试第三方
	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		return err
	}
	task.ExtendedData = enum.SyncTaskContentMap[task.TaskContent]
	ipAddress := r.Header.Get("X-Forwarded-For")
	if ipAddress == "" {
		ipAddress = r.RemoteAddr
	}
	ipParts := strings.Split(ipAddress, ":")
	ip := ipParts[0]

	//task.CreateId = jwtInfo.UserNo
	//task.CreateMobile = jwtInfo.Mobile
	task.CreateIp = ip
	task.ChainId = cast.ToInt64(jwtInfo.ChainId)
	task.CreateId = jwtInfo.UserId
	task.CreateName = jwtInfo.UserName
	task.OrgId = cast.ToInt(r.Header.Get("org_id"))
	task.CreateName = jwtInfo.UserName

	_, err = session.Insert(&task)
	if err != nil {
		return err
	}
	return nil
}

func (h AsyncCommService) GetAsyncTaskList(in vo.GetTaskListRequest) ([]vo.TaskListAsync, int, error) {
	h.Begin()
	defer h.Close()

	//Engine := h.Engine

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	ssql := "select * from task_list_async "
	countsql := "select count(1) from task_list_async "
	whereSql := " where 1 = 1 "
	if in.CreateId != "" && in.Promoter == 0 {
		//whereSql += fmt.Sprintf(" and create_id = \"%s\" ", in.CreateId)
		whereSql += fmt.Sprintf(" and create_id =  '" + in.CreateId + "'")
	}

	if in.Promoter == 2 {
		whereSql += fmt.Sprintf("and create_id != '" + in.CreateId + "'")
	}

	if in.TaskStatus != 0 {
		whereSql += fmt.Sprintf(" and task_status = %d ", in.TaskStatus)
	}
	if in.Id != 0 {
		whereSql += fmt.Sprintf(" and id = %d ", in.Id)
	}
	if in.OrgId != 0 {
		whereSql += fmt.Sprintf(" and org_id = %d ", in.OrgId)
	}
	if in.ChainId != 0 {
		whereSql += fmt.Sprintf(" and chain_id = %d ", in.ChainId)
	}
	if in.ContentStr != "" {
		whereSql += fmt.Sprintf(" and task_content  in (%s)", in.ContentStr)
	}

	if in.Sort != "" {
		if in.Sort == "createTimeDesc" {
			whereSql += " order by create_time desc "
		}
		if in.Sort == "createTimeAsc" {
			whereSql += " order by create_time asc "
		}
		if in.Sort == "idDesc" {
			whereSql += " order by id desc "
		}
		if in.Sort == "idAsc" {
			whereSql += " order by id asc"
		}
	}
	countsql += whereSql
	//获取数据列表总条数
	var out []vo.TaskListAsync
	//分页
	if in.PageIndex > 0 && in.PageSize > 0 {
		whereSql += fmt.Sprintf(" LIMIT %d,%d ", (in.PageIndex-1)*in.PageSize, in.PageSize)
	}
	sqls := ssql + whereSql

	Count, err := h.Engine.SQL(countsql).Count(&out)
	err = h.Engine.SQL(sqls).Find(&out)
	if err != nil {
		log.Error(err)
		return nil, 0, err
	}

	return out, int(Count), nil
}
