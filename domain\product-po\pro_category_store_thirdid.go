package product_po

import "time"

type ProCategoryStoreThirdid struct {
	Id                 int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	ChannelId          int       `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-阿闻，2-美团，3-饿了么，4-京东到家)') INT 'channel_id'"`
	ChannelStoreId     string    `json:"channel_store_id" xorm:"not null comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
	CategoryId         string    `json:"category_id" xorm:"not null comment('第三方分类ID') VARCHAR(30) 'category_id'"`
	OriginalCategoryId int       `json:"original_category_id" xorm:"not null default 0 comment('对分类表ID') INT 'original_category_id'"`
	CreateDate         time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	UpdateDate         time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
}
