package petai_vo

import (
	petai_po "eShop/domain/petai-po"
	viewmodel "eShop/view-model"

	"github.com/coze-dev/coze-go"
)

type ConsultConversationCreateReq struct {
	PmOrderSn int64 `json:"pm_order_sn"` //互联网医院订单号

}

type PetInfoDetail struct {
	PetId           string
	PetName         string
	PetWeight       string
	PetKindofStr    string
	PetVarietyStr   string
	PetAge          string
	PetNeuteringStr string
	PetSexStr       string
}

type ConversationCreateReq struct {
	UserInfoId string `json:"user_info_id"` //eshop.user_info.user_info_id（前端不用存）
	PetInfoId  string `json:"pet_info_id"`  //宠物id
	PetName    string `json:"pet_name"`     //宠物名称
	PetKindof  int    `json:"pet_kindof"`   //宠物种类
	PetVariety int    `json:"pet_variety"`  //宠物品种
	Title      string `json:"title"`        //会话标题

}

type ConversationCreateRes struct {
	viewmodel.BaseHttpResponse
	Data *petai_po.PetaiConversation `json:"data"`
}
type ChatReq struct {
	UserInfoId               string      `json:"user_info_id"`                //eshopUserId用户id（前端不用传）
	Message                  ChatMessage `json:"message"`                     //消息列表
	ConversationId           int         `json:"conversation_id"`             // 会话id
	PetDesc                  string      `json:"pet_desc"`                    //宠物描述(前端无需传)
	Uuid                     string      `json:"uuid"`                        // 问题消息的uuid,唯一标识该条消息（前端无需传）
	BotType                  int         `json:"bot_type"`                    // 智能体类型：1-养宠助手火山 2-小闻模型 3-宠物自诊 4-宠物识别；5-健康建议（当用户选择了宠物自诊、宠物识别时传对应的值）
	CozeConversationId       string      `json:"coze_conversation_id"`        //coze中的会话id(前端无需传)
	CozeConversationCreateAt int         `json:"coze_conversation_create_at"` //coze会话创建时间(前端无需传)
	ReasoningContent         string      `json:"reasoning_content"`           // 推理内容(前端无需传)
	PetInfoId                string      `json:"pet_info_id"`                 //宠物id(前端无需传)

}

type ConsultChatReq struct {
	PmOrderSn string         `json:"pm_order_sn"` // 互联网医院订单号（必传）
	Message   []*ChatMessage `json:"message"`     //消息上下文列表

}
type ConsultChatRes struct {
	viewmodel.BaseHttpResponse
}

type ConsultChatReferenceContentReq struct {
	ConsultConversationId int64  `json:"consult_conversation_id" validate:"required" label:"问诊会话id"` // 问诊会话id （必传）
	MessageUuid           string `json:"message_uuid" validate:"required" label:"引用的消息uuid"`         // 引用的消息uuid,唯一标识该条消息（必传）
	ReferenceContent      string `json:"reference_content" validate:"required" label:"实际发送内容"`       // 实际发送内容（必传）
}
type ConsultChatReferenceContentRes struct {
	viewmodel.BaseHttpResponse
	Data string `json:"data"` // 参考内容
}

type ChatMessage struct {
	Role          string         `json:"role"`           //消息发送者：user-用户；assistant-智能体
	ContentType   string         `json:"content_type"`   //消息内容的类型:text-文本；object_string-多模态内容，即文本和文件的组合、文本和图片的组合;
	TextContent   string         `json:"text_content"`   //文本内容
	ObjectContent []ObjectString `json:"object_content"` //多模态内容
	MetaData      string         `json:"meta_data"`      // 元数据
}

type ObjectString struct {
	Type    string `json:"type"`     //多模态消息内容类型，支持设置为：text：文本类型。file：文件类型。image：图片类型。audio：音频类型。
	Text    string `json:"text"`     //文本内容
	FileId  string `json:"file_id"`  //文件、图片、音频内容的 ID。(说明：1必须是当前账号上传的文件 ID，上传方式可参考上传文件。2在 type 为 file、image 或 audio 时，file_id 和 file_url 应至少指定一个)
	FileUrl string `json:"file_url"` //文件、图片或语音文件的在线地址。必须是可公共访问的有效地址。在 type 为 file、image 或 audio 时，file_id 和 file_url 应至少指定一个。
}

type ChatRes struct {
	viewmodel.BaseHttpResponse
	Data ChatData `json:"data"`
}
type ChatData struct {
}

type ConversationDetailReq struct {
	UserInfoId     string `json:"user_info_id"`    //eshop.user_info.user_info_id
	ConversationId int    `json:"conversation_id"` // 会话id
	IsCozeData     bool   `json:"is_coze_data"`    // 是否调用coze接口获取数据
	PmOrderSn      int64  `json:"pm_order_sn"`     // 互联网医院订单号

}

type ConversationDetailForAdminReq struct {
	ConversationId int `json:"conversation_id"` // 会话id
	PageIndex      int `json:"page_index"`      // 页码
	PageSize       int `json:"page_size"`       // 每页数量

}
type ConversationDetailForAdminRes struct {
	viewmodel.BasePageHttpResponse
	Data ConversationDetailForAdmin `json:"data"`
}
type ConversationDetailForAdmin struct {
	ConversationDetail   petai_po.PetaiConversationExd        `json:"conversation_detail"`   // 会话详情
	MessageList          []petai_po.PetaiMessage              `json:"message_list"`          // 消息列表
	ConversationTransfer []petai_po.PetaiConversationTransfer `json:"conversation_transfer"` // 会话转互联网医生人工记录列表
}
type ConversationDetailRes struct {
	viewmodel.BaseHttpResponse
	Data *petai_po.PetaiConversation `json:"data"`
}

type ConversationMessageListReq struct {
	viewmodel.BasePageHttpRequest
	UserInfoId     string `json:"user_info_id"`    //eshop.user_info.user_info_id
	ConversationId int    `json:"conversation_id"` // 会话id
	IsCozeData     bool   `json:"is_coze_data"`    // 是否调用coze接口获取数据
	Order          string `json:"order"`           // 排序方式：desc倒序，asc升序（默认）
	DataFrom       int    `json:"data_from"`       // 数据来源：1-ai 2-好兽医互联网医院

}

type ConsultConversationMessageListReq struct {
	viewmodel.BasePageHttpRequest
	ConversationId int64 `json:"conversation_id"` // 会话id
}

type ConsultConversationMessageListRes struct {
	viewmodel.BasePageHttpResponse
	Data []*petai_po.PetaiConsultMessage `json:"data"`
}

type ConversationMessageListExtend struct {
	*petai_po.PetaiMessage

	DataAcquisition DataAcquisitionListByMessageUuidRes
}

type ConversationMessageListRes struct {
	// 状态码，200：请求成功，400：请求失败
	Code int `json:"code"`
	// 响应提示信息
	Message string `json:"message"`
	// 总记录大小
	Total               int                      `json:"total"`
	DataAcquisitionShow bool                     `json:"data_acquisition_show"` // 是否显示数据来源
	Data                []*petai_po.PetaiMessage `json:"data"`
}

type ConversationListReq struct {
	viewmodel.BasePageHttpRequest
	UserInfoId string `json:"user_info_id"` //eshop.user_info.user_info_id（前端不用存）
	PetInfoId  string `json:"pet_info_id"`  //宠物id
	BotType    int    `json:"bot_type"`     // 智能体类型：1-AI问答； 2-AI健康建议
	PetKindof  int    `json:"pet_kindof"`   //品种 -1未知 1000猫 1001狗 1002其他
	//PetName    string `json:"pet_name"`     //宠物名称
	// UserMobile         string `json:"user_mobile"`          // 用户手机号
	// ConversationId     int    `json:"conversation_id"`      // 会话id
	// CozeConversationId string `json:"coze_conversation_id"` // coze中的会话id
	// Title              string `json:"title"`                // 会话标题
	Query      string   `json:"query"`       // 搜索关键字:用户手机号/ID/宠物名称/cozeid/会话标题
	CreateTime []string `json:"create_time"` // 创建时间开始、创建时间结束
	UpdateTime []string `json:"update_time"` // 更新时间开始 、更新时间结束
	OrderBy    int      `json:"order_by"`    // 排序方式：0-按更新时间倒序; 1-创建时间倒序

}
type ConversationListRes struct {
	viewmodel.BasePageHttpResponse
	Data []*petai_po.PetaiConversation `json:"data"`
}

type CancelChatReq struct {
	UserInfoId         string `json:"user_info_id"`         //eshop.user_info.user_info_id
	ConversationId     int    `json:"conversation_id"`      // 会话id
	CozeConversationId string `json:"coze_conversation_id"` // coze中的会话id
	ChatId             string `json:"chat_id"`              // 对话id
}

type RetrieveFileReq struct {
	UserInfoId string `json:"user_info_id"` //eshop.user_info.user_info_id
	FileId     string `json:"file_id"`      // 文件id
}

type UploadFileRes struct {
	viewmodel.BaseHttpResponse
	Data FileInfo `json:"data"`
}
type FileInfo struct {
	FileId    string `json:"file_id"`
	FileBytes int    `json:"file_bytes"`
	FileName  string `json:"file_name"`
	CreatedAt int    `json:"created_at"`
}
type MedicalChatReq struct {
	QuestionUuid         string          `json:"question_uuid"`          // 消息uuid,唯一标识该条消息
	PetInfoId            string          `json:"pet_info_id"`            // 宠物id
	Text                 string          `json:"text"`                   // 问题描述
	ThreadId             string          `json:"thread_id"`              // 会话id
	PetName              string          `json:"pet_name"`               // 宠物名称
	PetKindofStr         string          `json:"pet_kindof_str"`         // 宠物种类
	PetVarietyStr        string          `json:"pet_variety_str"`        // 宠物品种
	PetSexStr            string          `json:"pet_sex_str"`            // 宠物性别
	PetAge               string          `json:"pet_age"`                // 宠物年龄
	PetWeight            string          `json:"pet_weight"`             // 宠物体重(单位：kg)
	PetNeuteringStr      string          `json:"pet_neutering_str"`      // 宠物绝育
	LastestVaccinateTime string          `json:"lastest_vaccinate_time"` // 最近一次疫苗时间
	LastestVaccinateName string          `json:"lastest_vaccinate_name"` // 最近一次疫苗名称
	LastestDewormingTime string          `json:"lastest_deworming_time"` // 最近一次驱虫时间
	LastestDewormingName string          `json:"lastest_deworming_name"` // 最近一次驱虫名称
	Messages             []*coze.Message `json:"messages"`
	PmOrderSn            int64           `json:"pm_order_sn"` // 互联网医院订单号
	Intent               string          `json:"intent"`      //意图直接定义为"医疗问题"， 若这个字段为空, 则志辉那边需要通过前置模型自己来识别是什么问题
}

// pet_name 宠物名称
// pet_kindof_str 品种
// pet_sex_str 性别：男；女
// pet_age 年龄
// pet_weight 体重（单位kg）
// pet_neutering_str 绝育状态 ：已绝育；未绝育
// lastest_vaccinate_time 最近一次免疫时间
// lastest_vaccinate_name 最近一次免疫产品名称
// lastest_deworming_time 最近一次驱虫时间
// lastest_deworming_name 最近一次驱虫产品名称
type MedicalStreamPostReq struct {
	UserInfoId     string          `json:"user_info_id"`    //eshop.user_info.user_info_id
	ConversationId int             `json:"conversation_id"` // 会话id
	Text           string          `json:"text"`            // 问题描述
	PetInfoId      string          `json:"pet_info_id"`     // 宠物id
	PetDetail      MedicalPetInfo  `json:"pet_detail"`      // 宠物详情
	Messages       []*coze.Message `json:"messages"`
}

type MedicalPetInfo struct {
	PetName              string `json:"pet_name"`               // 宠物名称
	PetKindofStr         string `json:"pet_kindof_str"`         // 宠物种类
	PetVarietyStr        string `json:"pet_variety_str"`        // 宠物品种
	PetSexStr            string `json:"pet_sex_str"`            // 宠物性别
	PetAge               string `json:"pet_age"`                // 宠物年龄
	PetWeight            string `json:"pet_weight"`             // 宠物体重(单位：kg)
	PetNeuteringStr      string `json:"pet_neutering_str"`      // 宠物绝育
	LastestVaccinateTime string `json:"lastest_vaccinate_time"` // 最近一次疫苗时间
	LastestVaccinateName string `json:"lastest_vaccinate_name"` // 最近一次疫苗名称
	LastestDewormingTime string `json:"lastest_deworming_time"` // 最近一次驱虫时间
	LastestDewormingName string `json:"lastest_deworming_name"` // 最近一次驱虫名称
	FullText             string `json:"full_text"`              // 完整文本
}

type PetInfoForMedicalReq struct {
	PetInfoId string `json:"pet_info_id"` //宠物id
}

type MedicalChatRes struct {
	Chunk          string             `json:"chunk"`           // 回答内容
	IsEnd          bool               `json:"is_end"`          // 是否结束
	Intent         string             `json:"intent"`          // 意图
	Confidence     float64            `json:"confidence"`      // 置信度
	ProcessingTime float64            `json:"processing_time"` // 处理时间
	Details        MedicalChatDetails `json:"details"`         // 详细信息
}

type MedicalChatDetails struct {
	Probabilities map[string]float64 `json:"probabilities"` // 概率
	ResponseType  string             `json:"response_type"` // 响应类型
}
type SaveMessageFormMedicalReq struct {
	UserInfoId       string `json:"user_info_id"`      //eshop.user_info.user_info_id
	ConversationId   int    `json:"conversation_id"`   // 会话id
	Question         string `json:"question"`          //问题
	Answer           string `json:"answer"`            // 答案
	Uuid             string `json:"uuid"`              // 消息uuid,唯一标识该条问题消息
	ReasoningContent string `json:"reasoning_content"` // 推理内容
}

type SaveConsultConversationMessageReq struct {
	ConsultConversationId int64  `json:"consult_conversation_id"` // 问诊会话id
	Question              string `json:"question"`                //问题
	Answer                string `json:"answer"`                  // 答案
	Uuid                  string `json:"uuid"`                    // 消息uuid,唯一标识该条问题消息
}

type EvaluateMessageReq struct {
	MessageId    string `json:"message_id"`    // 消息id
	Evaluate     int    `json:"evaluate"`      //消息评价：0-初始，1-赞，2-踩
	UserInfoId   string `json:"user_info_id"`  // 用户id(前端不用传)
	Feedback     string `json:"feedback"`      //反馈
	FeedbackType int    `json:"feedback_type"` //反馈类别:1-没有帮助，2-信息有误，3-理解错误，4-违法有害，5-内容不完整 6-内容不专业，7-格式错误

}

type TransferPetMedicalReq struct {
	ConversationId int    `json:"conversation_id"` // 会话id
	UserInfoId     string `json:"user_info_id"`    //eshop.user_info.user_info_id(前端不用传)
}

type EvaluateConversationReq struct {
	FeedbackDoctorCode string `json:"doctor_code"`   // 医生code
	FeedbackDoctorName string `json:"doctor_name"`   // 医生姓名
	PmOrderSn          int64  `json:"pm_order_sn"`   // 订单号
	Evaluate           int    `json:"evaluate"`      //消息评价：0-初始，1-赞，2-踩
	Feedback           string `json:"feedback"`      //反馈
	FeedbackType       int    `json:"feedback_type"` //反馈类别:1-没有帮助，2-信息有误，3-理解错误，4-违法有害，5-内容不完整 6-内容不专业，7-格式错误
}

type ConversationDataAcquisitionReq struct {
	ConversationId int `json:"conversation_id"` // 会话id
}
