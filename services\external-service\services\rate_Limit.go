package services

import (
	"github.com/go-redis/redis"
)

/**
基于redis的限流
*/

// Implements RedisClient for redis.Client
type redisClient struct {
	*redis.Client
}

func (c *redisClient) RateDel(key string) error {
	return c.Del(key).Err()
}
func (c *redisClient) RateEvalSha(sha1 string, keys []string, args ...interface{}) (interface{}, error) {
	return c.EvalSha(sha1, keys, args...).Result()
}
func (c *redisClient) RateScriptLoad(script string) (string, error) {
	return c.ScriptLoad(script).Result()
}

//var (
//	Prefix = "获取限流异常: "
//
//	// 美团删除商品 接口限流50次/source/秒
//	MtSkuDelete                = "retail.sku.delete"
//	limiterMtDeleteProductRate *ratelimiter.Limiter
//
//	// 美团删除商品 接口限流50次/source/秒
//	MtSkuBatchDelete                = "retailCat.batchdelete.catandretail"
//	limiterMtBatchDeleteProductRate *ratelimiter.Limiter
//
//	//ele批量删除商品接口  接口限流20次/source/秒
//	EleSkuDelete                = "ele.sku.delete"
//	limiterEleDeleteProductRate *ratelimiter.Limiter
//
//	//上传图片 me.ele.retail:picture.upload-3
//	//需要授权接口限流10次/source/秒
//	limiterElePictureUploadProductRate *ratelimiter.Limiter
//	ElePictureUpload                   = "ele.retail.picture.upload"
//
//	//UploadPictureRTF 公共上传富文本
//	//sku.uploadrtf me.ele.retail:sku.uploadrtf-3  //需要授权接口限流200次/source/秒
//	limiterEleEleSkuUpoadRtfRate *ratelimiter.Limiter
//	EleSkuUpoadRtf               = "me.ele.retail.sku.uploadrtf"
//)

//func init() {
//
//	//美团删除商品限流
//	limiterMtDeleteProductRate = ratelimiter.New(ratelimiter.Options{
//		Max:      50,
//		Duration: time.Second, // 每秒50
//		Client:   &redisClient{cache.GetRedisConn()},
//	})
//	//美团删除商品限流
//	limiterMtBatchDeleteProductRate = ratelimiter.New(ratelimiter.Options{
//		Max:      5,
//		Duration: time.Second, // 每秒5
//		Client:   &redisClient{cache.GetRedisConn()},
//	})
//
//	//饿了了么删除商品限流
//	limiterEleDeleteProductRate = ratelimiter.New(ratelimiter.Options{
//		Max:      20,
//		Duration: time.Second, // 每秒20
//		Client:   &redisClient{cache.GetRedisConn()},
//	})
//
//	limiterElePictureUploadProductRate = ratelimiter.New(ratelimiter.Options{
//		Max:      10,
//		Duration: time.Second, // 每秒10
//		Client:   &redisClient{cache.GetRedisConn()},
//	})
//
//	limiterEleEleSkuUpoadRtfRate = ratelimiter.New(ratelimiter.Options{
//		Max:      200,
//		Duration: time.Second, // 每秒10
//		Client:   &redisClient{cache.GetRedisConn()},
//	})
//}

// 较为重要的接口 15次没有获取到限流就直接退出
//func LimiterImportantRate(rate *ratelimiter.Limiter, key string) bool {
//	defer func() {
//		if err := recover(); err != nil {
//			log.Error(Prefix, " 限流key: ", key, " ", err)
//		}
//	}()
//
//	for i := 0; i < 15; i++ { // 循环15次获取
//		res, _ := rate.Get(key)
//		if res.Remaining > 0 {
//			return true
//		} else {
//			//fmt.Println("等待时间：", res.Duration.Seconds())
//			//fmt.Println("等待时间：", res.Reset.Sub(time.Now()).Seconds())
//			time.Sleep(res.Duration)
//		}
//	}
//
//	return false
//}

//// 一般接口限流
//func LimiterNoImportantRate(rate *ratelimiter.Limiter, key string) bool {
//	defer func() {
//		if err := recover(); err != nil {
//			log.Error(Prefix, " 限流key: ", key, " ", err)
//		}
//	}()
//
//	for i := 0; i < 5; i++ {
//		res, _ := rate.Get(key)
//		if res.Remaining > 0 {
//			return true
//		} else {
//			//fmt.Println("等待时间：", res.Duration.Seconds())
//			//fmt.Println("等待时间：", res.Reset.Sub(time.Now()).Seconds())
//			time.Sleep(res.Duration)
//		}
//	}
//
//	return false
//}
