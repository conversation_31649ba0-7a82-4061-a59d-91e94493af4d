package distribution_vo

import (
	"eShop/view-model"
	"time"
)

type DisDistributorFans struct {
	Id          int       `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`                                 //分销员粉丝关系表自增id
	DisId       int       `json:"dis_id" xorm:"not null comment('分销员id') INT 'dis_id'"`                                  //分销员id
	MemberId    int       `json:"member_id" xorm:"not null comment('客户户id') INT 'member_id'"`                            //客户户id
	OrgId       int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`                                //所属主体id
	ShopId      int       `json:"shop_id" xorm:"default 0 comment('分销店铺') INT 'shop_id'"`                                //分销店铺
	OrderAmount int       `json:"order_amount" xorm:"default 0 comment('订单金额(分)') INT 'order_amount'"`                   //订单金额(分)
	OrderNum    int       `json:"order_num" xorm:"default 0 comment('订单数') INT 'order_num'"`                             //订单数
	ExpireTime  time.Time `json:"expire_time" xorm:"default 'null' comment('过期时间') DATETIME 'expire_time'"`              //过期时间
	CreateTime  time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"` //创建时间
	UpdateTime  time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"` //更新时间
}
type DisDistributorFansView struct {
	Id             int    `json:"id" xorm:"pk autoincr not null comment('ID') INT 'id'"`                                 //分销员粉丝关系表自增id
	DisId          int    `json:"dis_id" xorm:"not null comment('分销员id') INT 'dis_id'"`                                  //分销员id
	MemberId       int    `json:"member_id" xorm:"not null comment('客户户id') INT 'member_id'"`                            //客户户id
	OrgId          int    `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`                                //所属主体id
	ShopId         int    `json:"shop_id" xorm:"default 0 comment('分销店铺') INT 'shop_id'"`                                //分销店铺
	OrderAmount    int    `json:"order_amount" xorm:"default 0 comment('订单金额(分)') INT 'order_amount'"`                   //订单金额(分)
	OrderNum       int    `json:"order_num" xorm:"default 0 comment('订单数') INT 'order_num'"`                             //订单数
	OrderPayAmount int    `json:"order_pay_amount" xorm:"default 0 comment('订单支付金额(分)') INT 'order_pay_amount'"`         //订单支付金额(分)
	OrderPayNum    int    `json:"order_pay_num" xorm:"default 0 comment('订单支付数') INT 'order_pay_num'"`                   //订单支付数
	ExpireTime     string `json:"expire_time" xorm:"default 'null' comment('过期时间') DATETIME 'expire_time'"`              //过期时间
	CreateTime     string `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"` //创建时间
	UpdateTime     string `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"` //更新时间
	MemberName     string `json:"member_name"`                                                                           //粉丝会员名
	MemberMobile   string `json:"member_mobile"`                                                                         //粉丝手机号码
	MemberAvatar   string `json:"member_avatar"`                                                                         //粉丝头像
	ScrmUserId     string `json:"scrm_user_id"`                                                                          //scrm用户id
}
type GetDisFansListReq struct {
	viewmodel.BasePageHttpRequest
	IsValid  int    `json:"is_valid"`  //0-全部，1-有效，2-全部
	Mobile   string `json:"mobile"`    //用户手机号
	MemberId int    `json:"member_id"` //分销员在电商表的upet_member表的member_id（前端不用传）
}

type GetDisFansListRes struct {
	viewmodel.BasePageHttpResponse
	Data []DisDistributorFansView `json:"data"`
}

type GetDisFansInfoRes struct {
	viewmodel.BasePageHttpResponse
	Data []DisDistributorFans `json:"data"`
}
