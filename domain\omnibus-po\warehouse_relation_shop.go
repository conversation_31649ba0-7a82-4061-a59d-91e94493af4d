package omnibus_po

import (
	"fmt"
	"time"

	"xorm.io/xorm"
)

// 仓库和门店的对应关系表
type WarehouseRelationShop struct {
	Id            int       `json:"id" xorm:"pk autoincr not null comment('自增id') INT 'id'"`
	ShopName      string    `json:"shop_name" xorm:"default 'null' comment('门店名称') VARCHAR(255) 'shop_name'"`
	ShopId        string    `json:"shop_id" xorm:"not null comment('门店id') VARCHAR(255) 'shop_id'"`
	WarehouseName string    `json:"warehouse_name" xorm:"default 'null' comment('仓库名称') VARCHAR(255) 'warehouse_name'"`
	WarehouseId   int       `json:"warehouse_id" xorm:"not null comment('仓库id') INT 'warehouse_id'"`
	ChannelId     int       `json:"channel_id" xorm:"not null comment('1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提') INT 'channel_id'"`
	CreateTime    time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
}

func (t WarehouseRelationShop) TableName() string {
	return "dc_dispatch.warehouse_relation_shop"
}

// 根据店铺ID获取仓库关系映射
type GetWarehouseMapInfoReq struct {
	ShopIds []string // 店铺ID列表
	ShopId  string   // 店铺id
}

// 根据店铺id , 获取该店铺各个渠道对应的仓库id
func (w *WarehouseRelationShop) GetWarehouseMapInfo(session *xorm.Session, req GetWarehouseMapInfoReq) (map[string]WarehouseRelationShop, error) {
	warehouseList := make([]WarehouseRelationShop, 0)
	warehouseMap := make(map[string]WarehouseRelationShop)

	if len(req.ShopIds) > 0 {
		session = session.In("shop_id", req.ShopIds)
	}
	if req.ShopId != "" {
		session.Where("shop_id=?", req.ShopId)
	}

	if err := session.Table(w.TableName()).Find(&warehouseList); err != nil {
		return nil, err
	}

	for _, warehouse := range warehouseList {
		key := fmt.Sprintf("%s_%d", warehouse.ShopId, warehouse.ChannelId)
		warehouseMap[key] = warehouse
	}

	return warehouseMap, nil
}
