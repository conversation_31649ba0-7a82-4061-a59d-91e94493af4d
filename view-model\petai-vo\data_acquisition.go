package petai_vo

import petai_po "eShop/domain/petai-po"

type DataAcquisitionAddReq struct {
	QuestionUuid string `json:"question_uuid"` // 32位的随机字符串(用于标识唯一一条消息，用于志辉那边健康数据采集时，定位到采集的数据来源于那条消息)
	ThreadId     string `json:"thread_id"`     // 会话id即petai_conversation.id
	PetInfoId    string `json:"pet_info_id"`   // user_pet_info.pet_info_id
	// 绝育相关字段（修改宠物表eshop.user_pet_info.pet_neutering）
	PetNeuteringStr string `json:"pet_neutering_str"` // 是否绝育：是（必需）

	// 疫苗相关字段(插入user_pet_vaccinate_record,且type=1)
	PetVaccinatedName     string `json:"pet_vaccinated_name"`      //疫苗产品名称 （必需）
	PetVaccinatedTime     string `json:"pet_vaccinated_time"`      // 疫苗接种时间（年-月-日）（必需）
	PetVaccinatedCategory string `json:"pet_vaccinated_category"`  //  疫苗类型（核心疫苗：200000088，狂犬疫苗：200000089，核心疫苗抗体检测：200000091,狂犬疫苗抗体检测:200000092,抗体检测：200000090） （非必需）
	PetVaccinatedNumberOf int    `json:"pet_vaccinated_number_of"` //疫苗次数： 0-未知，1-首次免疫，2-二次免疫，3-尾次免疫，4-年度免疫（非必需）
	PetVaccinatedHospital string `json:"pet_vaccinated_hospital"`  // 疫苗医院（非必需）

	// 驱虫相关字段(插入user_pet_vaccinate_record,且type=2)
	PetDewormingName     string `json:"pet_deworming_name"`     //驱虫产品名称（必需）
	PetDewormingTime     string `json:"pet_deworming_time"`     //驱虫时间（年-月-日）（必需）
	PetDewormingHospital string `json:"pet_deworming_hospital"` // 驱虫医院（非必需）

	// 口腔相关字段(插入user_pet_vaccinate_record,且type=3)
	PetTeethCleanName     string `json:"pet_teeth_clean_name"`     //洁牙产品名称（必需）
	PetTeethCleanTime     string `json:"pet_teeth_clean_time"`     //洁牙时间（年-月-日）（必需）
	PetTeethCleanHospital string `json:"pet_teeth_clean_hospital"` // 洁牙医院（非必需）

	// 体检相关字段(插入user_pet_vaccinate_record,且type=4)
	PetPhysicalExamName     string `json:"pet_physical_exam_name"`     //体检产品名称（必需）
	PetPhysicalExamTime     string `json:"pet_physical_exam_time"`     //体检时间（年-月-日）（必需）
	PetPhysicalExamHospital string `json:"pet_physical_exam_hospital"` //体检医院（非必需）

	// 洗护相关字段(插入user_pet_vaccinate_record,且type=5)
	PetCareName     string `json:"pet_care_name"`     //洗护产品名称（必需）
	PetCareTime     string `json:"pet_care_time"`     //洗护时间（年-月-日）（必需）
	PetCareHospital string `json:"pet_care_hospital"` //洗护医院（非必需）

	// 体况相关字段(插入user_pet_vaccinate_record,且type=6)
	PetBcsName  string `json:"pet_bcs_name"`  //体况评分（必需）
	PetBcsTime  string `json:"pet_bcs_time"`  //体况时间（年-月-日）（必需）
	PetBcsImage string `json:"pet_bcs_image"` //体况图片 （非必需）

	// 体重相关字段(插入user_pet_vaccinate_record,且type=8)
	PetWeightName string `json:"pet_weight_name"` //体重（必需）
	PetWeightTime string `json:"pet_weight_time"` //体重记录时间（年-月-日）（必需）

	// 病史相关字段(插入petai_med_record)
	PetDiseaseName             string `json:"pet_disease_name"`              //疾病名称（必需）
	PetDiseaseTime             string `json:"pet_disease_time"`              //疾病记录时间（年-月-日）（必需）
	PetDiseaseHospital         string `json:"pet_disease_hospital"`          //就诊医院（非必需）
	PetDiseaseTreatmentOutcome string `json:"pet_disease_treatment_outcome"` //治愈情况（必需）

	// 疾病自诊相关字段
	PetSelfDiagnosisImage  string `json:"pet_self_diagnosis_image"`  //自诊图片（必需）
	PetSelfDiagnosisTime   string `json:"pet_self_diagnosis_time"`   //疾病自诊时间（年-月-日）（必需）
	PetSelfDiagnosisResult string `json:"pet_self_diagnosis_result"` //自诊结果（必需）

	// 报告解读相关字段
	PetReportName     string `json:"pet_report_name"`     //报告名称（必需）
	PetReportTime     string `json:"pet_report_time"`     //报告时间（年-月-日）（必需）
	PetReportHospital string `json:"pet_report_hospital"` //就诊医院（必需）
	PetReportImage    string `json:"pet_report_image"`    //报告图片（必需）
	PetReportResult   string `json:"pet_report_result"`   //报告诊断（必需）
}

type DataAcquisitionListByMessageUuidReq struct {
	QuestionUuid []string `json:"question_uuid"` // 32位的随机字符串(用于标识唯一一条消息，用于志辉那边健康数据采集时，定位到采集的数据来源于那条消息)

}
type DataAcquisitionListByMessageUuidRes struct {
	PetVaccinateRecord []*petai_po.UserPetVaccinateRecord `json:"pet_vaccinate_record"` // 健康档案列表
	PetNeutering       int                                `json:"pet_neutering"`        // 绝育 -1未知,0未绝育,1已绝育
	PetInfoId          string                             `json:"pet_info_id"`          // 宠物信息id
}

type DataAcquisitionListReq struct {
	ConversationId int `json:"conversation_id"` // 会话id（必需）

}

type DataAcquisitionListRes struct {
	Data []*petai_po.UserPetVaccinateRecord `json:"data"`
}

type DataAcquisitionEditReq struct {
	Data []petai_po.UserPetVaccinateRecord `json:"data"`
}
