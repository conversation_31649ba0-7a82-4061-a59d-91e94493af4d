package controllers

// 店铺满减和
import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/marketing-service/services"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"

	marketing_vo "eShop/view-model/marketing-vo"

	"github.com/go-chi/chi/v5"
	"github.com/spf13/cast"
)

type ActivityController struct {
	service services.ActivityService
}

func NewActivityController(service services.ActivityService) *ActivityController {
	return &ActivityController{
		service: service,
	}
}

func (a *ActivityController) RegisterRoutes(r chi.Router) {
	r.Route("/marketing-app/manager/activity", func(r chi.Router) {
		// 营销活动列表
		r.Get("/list", a.GetActivityList)
		// 获取活动详情
		r.Get("/detail", a.GetActivityDetail)
		// 保存活动
		r.Post("/save", a.SaveActivity)
		// 编辑活动
		r.Post("/edit", a.EditActivity)
		// 结束活动
		r.Post("/end", a.EndActivity)
		// 获取正在参与特价活动的商品列表
		r.Get("/running-products", a.GetRunningActivityProducts)

	})

	r.Route("/marketing-app/api/activity", func(r chi.Router) {
		// 获取店铺当前有效的满减活动
		r.Get("/running-full-list", a.GetRunningFullReductionList)

	})
}

// @Summary 营销活动详情接口
// @Tags 后台接口-营销活动
// @Accept json
// @Produce json
// @Param GetActivityDetailReq query marketing_vo.GetActivityDetailReq true "营销活动详情请求"
// @Success 200 {object} marketing_vo.GetActivityDetailRes
// @Router /marketing-app/manager/activity/detail [GET]
func (a *ActivityController) GetActivityDetail(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.GetActivityDetailRes{}
	out.Code = 400

	req, err := utils.Bind[marketing_vo.GetActivityDetailReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	req.StoreId = cast.ToString(jwtInfo.TenantId)

	if out.Data, err = a.service.GetActivityDetail(&req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 营销活动列表接口
// @Tags 后台接口-营销活动
// @Accept json
// @Produce json
// @Param ActivityListReq query marketing_vo.ActivityListReq true "营销活动列表请求"
// @Success 200 {object} marketing_vo.ActivityListRes
// @Router /marketing-app/manager/activity/list [GET]
func (a *ActivityController) GetActivityList(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.ActivityListRes{}
	out.Code = 400
	out.Data.List = make([]marketing_vo.ActivityListData, 0)
	req, err := utils.Bind[marketing_vo.ActivityListReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	req.StoreId = cast.ToString(jwtInfo.TenantId)

	if out.Data.List, out.Total, err = a.service.GetActivityList(&req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	// 获取统计
	var waitGroup sync.WaitGroup
	waitGroup.Add(3)

	go func(req marketing_vo.ActivityListReq) {
		req.Status = 1
		_, out.Data.Stats.Pending, err = a.service.GetActivityList(&req)
		waitGroup.Done()
	}(req)

	go func(req marketing_vo.ActivityListReq) {
		req.Status = 2
		_, out.Data.Stats.Running, err = a.service.GetActivityList(&req)
		waitGroup.Done()
	}(req)

	go func(req marketing_vo.ActivityListReq) {
		req.Status = 3
		_, out.Data.Stats.Ended, err = a.service.GetActivityList(&req)
		waitGroup.Done()
	}(req)

	waitGroup.Wait()

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 获取店铺当前有效的满减活动列表
// @Tags API接口-营销活动
// @Accept json
// @Produce json
// @Param ActivityListReq query marketing_vo.ActivityListReq true "营销活动列表请求"
// @Success 200 {object} marketing_vo.ActivityListRes
// @Router /marketing-app/api/activity/running-full-list [GET]
func (a *ActivityController) GetRunningFullReductionList(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.ApiActivityListRes{}
	out.Code = 400
	out.Data = make([]marketing_vo.ActivityListData, 0)
	req, err := utils.Bind[marketing_vo.ActivityListReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	req.StoreId = jwtInfo.TenantId
	req.Status = 2 // 进行中
	req.Type = 1   // 满减活动
	if out.Data, out.Total, err = a.service.GetActivityList(&req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 获取正在参与特价活动的商品列表
// @Tags 后台接口-营销活动
// @Accept json
// @Produce json
// @Param RunningActivityProductsReq query marketing_vo.RunningActivityProductsReq true "获取正在参与特价活动的商品列表请求"
// @Success 200 {object} marketing_vo.RunningActivityProductsRes
// @Router /marketing-app/manager/activity/running-products [GET]
func (a *ActivityController) GetRunningActivityProducts(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.RunningActivityProductsRes{}
	out.Code = 400
	out.Data = make([]marketing_vo.RunningActivityProducts, 0)
	req, err := utils.Bind[marketing_vo.RunningActivityProductsReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	req.StoreId = cast.ToString(jwtInfo.TenantId)

	if out.Data, out.Total, err = a.service.GetRunningActivityProducts(&req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 保存满减活动或特价活动
// @Tags 后台接口-营销活动
// @Accept json
// @Produce json
// @Param SaveActivityReq body marketing_vo.SaveActivityReq true "保存满减活动或特价活动请求"
// @Success 200 {object} marketing_vo.SaveActivityRes
// @Router /marketing-app/manager/activity/save [POST]
func (a *ActivityController) SaveActivity(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.SaveActivityRes{}
	out.Code = 400
	req, err := utils.Bind[marketing_vo.SaveActivityReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	req.MarketingActivity.StoreId = cast.ToString(jwtInfo.TenantId)
	req.MarketingActivity.ChainId = cast.ToInt64(jwtInfo.ChainId)
	if err := a.service.SaveActivity(&req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)

}

// @Summary 编辑满减活动或特价活动
// @Tags 后台接口-营销活动
// @Accept json
// @Produce json
// @Param SaveActivityReq body marketing_vo.SaveActivityReq true "保存满减活动或特价活动请求"
// @Success 200 {object} marketing_vo.SaveActivityRes
// @Router /marketing-app/manager/activity/edit [POST]
func (a *ActivityController) EditActivity(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.SaveActivityRes{}
	out.Code = 400
	req, err := utils.Bind[marketing_vo.SaveActivityReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	req.MarketingActivity.StoreId = cast.ToString(jwtInfo.TenantId)
	req.MarketingActivity.ChainId = cast.ToInt64(jwtInfo.ChainId)
	if err := a.service.EditActivity(&req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)
}

// @Summary 结束营销活动
// @Tags 后台接口-营销活动
// @Accept json
// @Produce json
// @Param EndActivityReq body marketing_vo.EndActivityReq true "结束营销活动请求"
// @Success 200 {object} marketing_vo.EndActivityRes
// @Router /marketing-app/manager/activity/end [POST]
func (a *ActivityController) EndActivity(w http.ResponseWriter, r *http.Request) {
	out := marketing_vo.SaveActivityRes{}
	out.Code = 400
	req, err := utils.Bind[marketing_vo.EndActivityReq](r)
	if err != nil {
		out.Message = "解析参数错误" + err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	jwtInfo, err := jwtauth.GetOfflineJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		out.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	req.StoreId = cast.ToString(jwtInfo.TenantId)
	req.ChainId = cast.ToInt64(jwtInfo.ChainId)
	if err := a.service.EndActivity(&req); err != nil {
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		w.Write(out2)
		return
	}

	out.Code = 200
	out2, _ := json.Marshal(out)
	w.Write(out2)

}
