// Package marketing_po 参赛用户领域模型
package marketing_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

// PetContestant 参赛用户表领域模型
type PetContestant struct {
	// 主键
	Id int `xorm:"pk autoincr 'id'" json:"id"`
	// 用户ID
	ScrmUserId string `xorm:"scrm_user_id" json:"scrm_user_id"`
	// 用户昵称
	NickName string `xorm:"nick_name" json:"nick_name"`
	// 用户手机号带*
	Mobile string `xorm:"mobile" json:"mobile"`
	// 用户加密手机号
	EnMobile string `xorm:"en_mobile" json:"en_mobile"`
	// 首次创作时间
	FirstWorkTime time.Time `xorm:"first_work_time" json:"first_work_time"`
	// 已生成作品数量
	WorkCount int `xorm:"work_count" json:"work_count"`
	// 是否参与PK 0否 1是
	IsPk int `xorm:"is_pk" json:"is_pk"`
	// 参与PK的作品id
	PkWorkId string `xorm:"pk_work_id" json:"pk_work_id"`
	// 参与PK的时间
	PkTime time.Time `xorm:"pk_time" json:"pk_time"`
	// 创建时间
	CreateTime time.Time `xorm:"create_time created" json:"create_time"`
	// 更新时间
	UpdateTime time.Time `xorm:"update_time updated" json:"update_time"`
}

// TableName 返回表名
func (PetContestant) TableName() string {
	return "eshop.pet_contestant"
}

func (p *PetContestant) Create(session *xorm.Session, contestant *PetContestant) (err error) {
	if session == nil {
		return errors.New("session is nil")
	}
	if contestant == nil {
		return errors.New("contestant is nil")
	}
	_, err = session.Omit("pk_time").Insert(contestant)
	return err
}

func (p *PetContestant) GetByScrmId(session *xorm.Session, Scrmid string) (out PetContestant, exist bool, err error) {

	exist, err = session.Table("eshop.pet_contestant").Where("scrm_user_id=?", Scrmid).Get(&out)
	if err != nil {
		return
	}

	return
}
