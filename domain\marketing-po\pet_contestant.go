// Package marketing_po 参赛用户领域模型
package marketing_po

import (
	"time"
)

// PetContestant 参赛用户表领域模型
type PetContestant struct {
	// 主键
	Id int `xorm:"pk autoincr 'id'"`
	// 用户ID
	ScrmUserId string `xorm:"scrm_user_id"`
	// 用户昵称
	NickName string `xorm:"nick_name"`
	// 用户手机号带*
	Mobile string `xorm:"mobile"`
	// 用户加密手机号
	EnMobile string `xorm:"en_mobile"`
	// 用户注册时间
	RegisterTime time.Time `xorm:"register_time"`
	// 客户类型 0新客 1老客
	CustomerType int `xorm:"customer_type"`
	// 首次创作时间
	FirstWorkTime time.Time `xorm:"first_work_time"`
	// 已生成作品数量
	WorkCount int `xorm:"work_count"`
	// 是否参与PK 0否 1是
	IsPk int `xorm:"is_pk"`
	// 参与PK的作品id
	PkWorkId string `xorm:"pk_work_id"`
	// 参与PK的时间
	PkTime time.Time `xorm:"pk_time"`
	// 创建时间
	CreateTime time.Time `xorm:"create_time"`
	// 更新时间
	UpdateTime time.Time `xorm:"update_time"`
}

// TableName 返回表名
func (PetContestant) TableName() string {
	return "eshop.pet_contestant"
}

// 可根据需要添加领域方法，如判断是否新客/老客、是否参与PK等
func (p *PetContestant) IsNewCustomer() bool {
	return p.CustomerType == 0
}

func (p *PetContestant) IsParticipatingPK() bool {
	return p.IsPk == 1
}
