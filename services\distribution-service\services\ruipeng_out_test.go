package services

import (
	"testing"
)

func TestOutService_UpdateOutHospitalToEs(t *testing.T) {
	tests := []struct {
		name    string
		s       OutService
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			s:    OutService{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.s.UpdateOutHospitalToEs()
		})
	}
}

func TestCreateOutHospitalESIndex(t *testing.T) {
	type args struct {
		orgId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "aaa",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CreateOutHospitalESIndex(); (err != nil) != tt.wantErr {
				t.Errorf("CreateESIndex() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
