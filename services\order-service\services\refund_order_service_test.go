package services

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/services/common"
	order_vo "eShop/view-model/order-vo"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestRefundOrderService_ApplyRefund(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req     order_vo.RefundRequest
		jwtInfo *jwtauth.XCShopPayload
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test_apply_refund",
			args: args{
				req: order_vo.RefundRequest{
					OrderSn:      "9964128299085287",
					RefundType:   1, //1:原路退回 2:指定退回
					RefundMethod: 1,
					RefundReason: "原路退回",
					Type:         1, // 1:全部退款 2:部分退款
				},

				// 单个商品退款
				//req: order_vo.RefundRequest{
				//	OrderSn:      "9964128298151493",
				//	RefundType:   2,
				//	RefundMethod: 1,
				//	RefundReason: "部分退款",
				//	Type:         2,
				//	Products: []*order_vo.RefundProduct{
				//		{
				//			OrderProductId: 9425377,
				//			RefundNum:      1,
				//			RefundAmount:   200,
				//		},
				//	},
				//},
				jwtInfo: &jwtauth.XCShopPayload{
					UserId: "602678362255611904",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &RefundOrderService{
				BaseService: tt.fields.BaseService,
			}
			log.Init()
			if err := s.ApplyRefund(tt.args.req, tt.args.jwtInfo); (err != nil) != tt.wantErr {
				t.Errorf("ApplyRefund() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRefundOrderService_GetRefundReturnList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req order_vo.RefundReturnListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "test_get_refund_return_list",
			args: args{
				req: order_vo.RefundReturnListRequest{
					Page:     1,
					PageSize: 10,
					Status:   3, // 待处理
					ShopId:   "576534157590154085",
					Mobile:   "18142651711",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &RefundOrderService{
				BaseService: tt.fields.BaseService,
			}
			log.Init()
			got, _, err := s.GetRefundReturnList(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRefundReturnList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("GetRefundReturnList() got = %+v", got)
		})
	}
}

func TestRefundOrderService_ProcessReturn(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req     order_vo.ProcessReturnRequest
		jwtInfo *jwtauth.XCShopPayload
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "test_process_return",
			args: args{
				req: order_vo.ProcessReturnRequest{
					RefundSn: "50000136776",
					Products: []*order_vo.ProcessReturnProduct{
						{
							Id:              5397146,
							ActualReturnNum: 0,
							Remark:          "商品完好",
						},
					},
					Remark: "退货验收通过",
				},
				jwtInfo: &jwtauth.XCShopPayload{
					UserId:   "1",
					UserName: "admin",
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &RefundOrderService{
				BaseService: tt.fields.BaseService,
			}
			log.Init()
			if err := s.ProcessReturn(tt.args.req, tt.args.jwtInfo); (err != nil) != tt.wantErr {
				t.Errorf("ProcessReturn() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
