package omnibus_po

type Store_set struct {
	Shop_id       string `xorm:"not null pk comment('店铺ID') INT(11)"`
	Is_getorder   int32  `xorm:"not null default 0 comment('接单声音是否开启')"`
	Getordertype  int32  `xorm:"not null default 0 comment('提示音频率，1播放3次，2循环播放') "`
	Is_reminder   int32  `xorm:"not null default 0 comment('催单声音是否开启') "`
	Remindertype  int32  `xorm:"not null default 0 comment('提示音频率，1播放3次，2循环播放') "`
	Is_backorder  int32  `xorm:"not null default 0 comment('退单声音是否开启') "`
	Backordertype int32  `xorm:"not null default 0 comment('提示音频率，1播放3次，2循环播放') "`
	Is_aotu       int32  `xorm:"not null default 0 comment('自动接单声音是否开启') "`
	Aotutype      int32  `xorm:"not null default 0 comment('提示音频率，1播放3次，2循环播放')"`
	Is_ordercome  int32  `xorm:"not null default 0 comment('订单到达是否开启') "`
	Ordercometype int32  `xorm:"not null default 0 comment('提示音频率，1播放3次，2循环播放') "`
	Is_exception  int32  `xorm:"not null default 0 comment('配送异常声音是否开启') "`
	Exceptiontype int32  `xorm:"not null default 0 comment('提示音频率，1播放3次，2循环播放') "`
	Volume        int32  `xorm:"not null default 0 comment('音量') "`
	Is_aotu_order int32  `xorm:"not null default 0 comment('是否自动接单1：是，0：否') "`
	PrintExtent   int32  `xorm:"not null default 0 comment('打印长度mm) "`
}
