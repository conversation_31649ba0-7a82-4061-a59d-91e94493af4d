package services

import (
	dispatch_po "eShop/domain/dispatch-po"
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/proto/dac"
	"eShop/services/common"
	order_vo "eShop/view-model/order-vo"
	"fmt"
	"strings"

	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type DacService struct {
	common.BaseService
}

// 查询渠道的仓库设置信息
func (s DacService) GetChannelWarehouses(finance_code string) ([]order_vo.ChannelWarehouse, error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	sql := `
		select wrs.shop_id,
		wrs.shop_name,
	   w.code as warehouse_code,
	   w.name as warehouse_name,
	   w.id as warehouse_id,
	   w.category, 
	   wrs.channel_id
from dc_dispatch.warehouse_relation_shop wrs 
join dc_dispatch.warehouse w on wrs.warehouse_id  = w.id
where wrs.shop_id  = ? ;
`
	channelWarehouses := make([]order_vo.ChannelWarehouse, 0)
	err := session.SQL(sql, finance_code).Find(&channelWarehouses)
	if err != nil {
		return channelWarehouses, err
	}

	return channelWarehouses, nil
}

// ShopStoreGet 店铺基本信息获取
func (s DacService) ShopStoreGet(params *dac.ShopStoreGetRequest) (*dac.ShopStoreGetResponse, error) {
	logPrefix := fmt.Sprintf("ShopStoreGet 店铺基本信息获取,店铺财务编码%s", params.Finance_Code)
	log.Info(logPrefix, "入参为", utils.JsonEncode(params))
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	if params.Finance_Code == "" {
		return nil, status.Error(codes.Internal, "店铺财务ID不能为空")
	}

	var (
		has bool
		err error
	)
	if params.User_No != "" {
		has, err = session.Where("user_no=? and finance_code=?", params.User_No, params.Finance_Code).Get(&omnibus_po.StoreUserAuthority{})
		if err != nil {
			log.Error(logPrefix, "查询用户权限失败，", err.Error())
			return nil, status.Error(codes.Internal, "数据库查询失败："+err.Error())
		}
		if !has {
			log.Error(logPrefix, "没有查看该店铺信息权限")
			return nil, status.Error(codes.Internal, "没有查看该店铺信息权限")
		}
	}

	shop := new(omnibus_po.Store)
	has, err = session.Where("finance_code = ? ", params.Finance_Code).Get(shop)
	if err != nil {
		log.Error(logPrefix, "数据库查询失败，", err.Error())
		return nil, status.Error(codes.Internal, "数据库查询失败，"+err.Error())
	}
	if !has {
		log.Error(logPrefix, "门店信息不存在")
		return nil, status.Error(codes.Internal, params.Finance_Code+"，门店信息不存在")
	}

	out := &dac.ShopStoreGetResponse{
		Code: 200,
		Data: &dac.ShopStoreInfo{
			ShopId:          cast.ToString(shop.Id),
			ShopCode:        shop.StoreCode,
			ShopName:        shop.Name,
			ShopShortname:   shop.Shortname,
			Finance_Code:    shop.FinanceCode,
			Channel_Id:      params.Channel_Id,
			ZilongId:        shop.ZilongId,
			CustomCode:      shop.CustomCode,
			CustomerCode:    shop.CustomCode,
			ShopAddress:     shop.Province + shop.City + shop.Address,
			ShopDesc:        shop.Desc,
			PointX:          strings.Trim(shop.PointX, " "),
			PointY:          strings.Trim(shop.PointY, " "),
			ElmDelivery:     cast.ToInt32(shop.ElmDelivery),
			City:            shop.City,
			BigRegion:       shop.Bigregion,
			AppChannel:      int32(shop.AppChannel),
			DeliveryMethod:  int32(shop.DeliveryMethod),
			SellDrugs:       int32(shop.SellDrugs),
			DrugsChannelIds: shop.DrugsChannelIds,
			OrgId:           int32(shop.OrgId),
		},
	}

	var storeRelation []*omnibus_po.StoreRelation
	if err = session.Where("finance_code=?", params.Finance_Code).Find(&storeRelation); err != nil {
		log.Error(logPrefix, "数据库查询失败，", err.Error())
		return nil, status.Error(codes.Internal, "数据库查询失败，"+err.Error())
	}

	for _, v := range storeRelation {
		switch v.ChannelId {
		case common.ChannelIdMT:
			out.Data.ChannelStoreId = v.ChannelStoreId
		case common.ChannelIdELM:
			out.Data.EleId = v.ChannelStoreId
		case common.ChannelIdJD:
			out.Data.JddjId = v.ChannelStoreId
		}
	}
	//从北京的接口中拉去门店信息更新
	// saas-v1.0  如果主体是 宠物saas ， 不需要调北京接口
	if shop.AppChannel != cast.ToInt(config.GetString("eshop_store_app_channel")) {
		// todo 待移阿闻订单时补充
		// singleHospital, err := dataCenter.GetAcpHospitalInfoByFinanceCode([]string{params.Finance_Code})
		// if err == nil {
		// 	out.Data.ShopAddress = singleHospital.Data[0].Address
		// 	out.Data.ShopName = singleHospital.Data[0].ClinicName
		// }
	}

	// 查询仓库的label
	channelWarehouses, err := s.GetChannelWarehouses(params.Finance_Code)
	if err != nil {
		return out, err
	}

	warehouseMap := map[int]string{1: "电商仓", 3: "门店仓", 4: "前置仓", 5: "虚拟仓"}

	for i := range channelWarehouses {
		warehouse := channelWarehouses[i]
		label := fmt.Sprintf("从%s:%s(%s)取的库存", warehouseMap[warehouse.Category], warehouse.WarehouseName, warehouse.WarehouseCode)
		if warehouse.ChannelId == 1 { //阿闻
			out.Data.ZlLabel = label
		}
		if warehouse.ChannelId == 2 { // mt
			out.Data.MtLabel = label
		}
		if warehouse.ChannelId == 3 { // ele
			out.Data.ElmLabel = label
		}
		if warehouse.ChannelId == 4 { // jd
			out.Data.JdLabel = label
		}
		if warehouse.ChannelId == 9 { // 互联网医疗
			out.Data.YlLabel = label
		}
		if warehouse.ChannelId == 10 { // 竖屏外卖
			out.Data.SpLabel = label
		}
	}

	var warehouse []dispatch_po.Warehouse
	if err = session.SQL("SELECT DISTINCT a.* FROM dc_dispatch.warehouse a INNER JOIN dc_dispatch.`warehouse_relation_shop` b "+
		"ON a.id=b.warehouse_id WHERE b.shop_id=?", params.Finance_Code).
		Find(&warehouse); err != nil {
		log.Error(logPrefix, "查询仓库关联数据失败，", err.Error())
		return nil, status.Error(codes.Internal, "查询仓库关联数据失败："+err.Error())
	}

	if len(warehouse) > 0 {
		for _, v := range warehouse {
			if v.Status == 0 {
				continue
			}
			warehouseTmp := &dac.Warehouse{
				Code:         v.Code,
				Name:         v.Name,
				Address:      v.Address,
				Category:     int32(v.Category),
				CategoryText: warehouseMap[cast.ToInt(v.Category)],
			}
			out.Data.WarehouseList = append(out.Data.WarehouseList, warehouseTmp)
		}
	}

	return out, nil
}

// 消息创建修改读取状态
func (s DacService) MessageUpdate(in *dac.MessageUpdateRequest) (*dac.MessageUpdateResponse, error) {
	logPrefix := fmt.Sprintf("消息状态更改：%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	var out = new(dac.MessageUpdateResponse)
	out.Code = 200
	if len(in.MessageId) == 0 && len(in.OrderId) == 0 && len(in.MessageRelationshipId) == 0 {
		out.Code = 400
		out.Message = "参数不能为空"
		return out, nil
	}
	sql := "SELECT mr.* FROM datacenter.message m INNER JOIN datacenter.message_relationship mr ON m.id=mr.message_id WHERE 1=1  "
	var params []interface{}

	if len(in.MessageRelationshipId) > 0 { //阅览调用
		var model omnibus_po.MessageRelationship

		sql += " AND mr.`id`=? "
		params = append(params, in.MessageRelationshipId)

		session.SQL(sql, params...).Get(&model)
		model.IsRead = 2
		if len(model.Id) == 0 {
			out.Code = 401
			out.Message = "不存在此消息"
			return out, nil
		}
		_, err := session.Where("id=?", model.Id).Cols("is_read").Update(&model)
		if err != nil {
			out.Code = 500
			out.Message = "更新状态失败"
			log.Infof("消息更新状态失败%s", err.Error())
			return out, nil
		}
		out.Code = 200
		return out, nil
	} else if len(in.OrderId) > 0 { //自动取消调用
		if len(in.OrderId) > 0 {
			sql += " AND m.`order_id`=? "
			params = append(params, in.OrderId)
		}
		var list []omnibus_po.MessageRelationship
		session.SQL(sql, params...).Find(&list)
		for _, v := range list {
			v.IsRead = 2
			_, err := session.Where("id=?", v.Id).Cols("is_read").Update(&v)
			if err != nil {
				out.Code = 500
				out.Message = "更新状态失败，事务失败"
				log.Infof("消息更新状态失败%s", err.Error())
				return out, nil
			}
		}
	}
	out.Code = 200
	return out, nil
}
