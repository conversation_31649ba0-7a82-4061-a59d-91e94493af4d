package distribution_po

import "time"

type StatsSalespersonDaily struct {
	Id                       int       `json:"id" xorm:"pk autoincr not null comment('主键') BIGINT 'id'"`
	StatDate                 string    `json:"stat_date" xorm:"not null comment('日期') DATE 'stat_date'"`
	EndDate                  string    `json:"end_date" xorm:"not null comment('日期') DATE 'end_date'"`
	SalesmanId               int       `json:"salesman_id" xorm:"not null comment('业务员ID') BIGINT 'salesman_id'"`
	SalesmanName             string    `json:"salesman_name" xorm:"not null default '' comment('业务员姓名') VARCHAR(100) 'salesman_name'"`
	Organization             string    `json:"organization" xorm:"not null comment('所属组织') VARCHAR(100) 'organization'"`
	ServiceEnterpriseCount   int       `json:"service_enterprise_count" xorm:"not null default 0 comment('服务企业数') INT 'service_enterprise_count'"`
	ShopCount                int       `json:"shop_count" xorm:"not null default 0 comment('分销店铺数') INT 'shop_count'"`
	ServiceDistributorCount  int       `json:"service_distributor_count" xorm:"not null default 0 comment('服务分销员数') INT 'service_distributor_count'"`
	BossCount                int       `json:"boss_count" xorm:"not null default 0 comment('老板数') INT 'boss_count'"`
	ProductEnterpriseCount   int       `json:"product_enterprise_count" xorm:"not null default 0 comment('分销商品成交企业数') INT 'product_enterprise_count'"`
	ProductTransCount        int       `json:"product_trans_count" xorm:"not null default 0 comment('分销商品成交单数') INT 'product_trans_count'"`
	ProductTransAmount       int       `json:"product_trans_amount" xorm:"not null default 0 comment('分销商品成交金额(分)') INT 'product_trans_amount'"`
	ProductCommission        int       `json:"product_commission" xorm:"not null default 0 comment('分销商品佣金(分)') INT 'product_commission'"`
	InsuranceEnterpriseCount int       `json:"insurance_enterprise_count" xorm:"not null default 0 comment('分销保险成交企业数') INT 'insurance_enterprise_count'"`
	InsuranceTransCount      int       `json:"insurance_trans_count" xorm:"not null default 0 comment('分销保险成交单数') INT 'insurance_trans_count'"`
	InsuranceTransAmount     int       `json:"insurance_trans_amount" xorm:"not null default 0 comment('分销保险成交金额(分)') INT 'insurance_trans_amount'"`
	InsuranceCommission      int       `json:"insurance_commission" xorm:"not null default 0 comment('分销保险佣金(分)') INT 'insurance_commission'"`
	CreateTime               time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime               time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}
