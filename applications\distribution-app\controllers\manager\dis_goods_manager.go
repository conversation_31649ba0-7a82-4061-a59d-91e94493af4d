package manager

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	base_service "eShop/services/base-service"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// GoodsPage 分页查询
// @Summary 分页查询
// @Description 分销商品表查询接口
// @Tags 后台接口-分销商品
// @Accept  json
// @Produce  json
// @Param GoodsPageReq body distribution_vo.GoodsPageReq true " "
// @Success 200 {object} distribution_vo.GoodsPageResp
// @Failure 400 {object} distribution_vo.GoodsPageResp
// @Router /manager/goods/page-list [GET]
func GoodsPage(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.GoodsPageResp{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.GoodsPageReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("分页查询商品操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询商品操作，参数解析失败：%s", err.Error())
	} else {
		service := services.DisGoodsService{}
		list, total, err := service.GoodsPage(req)
		if err != nil {
			log.Error("分页查询商品操作失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("分页查询商品操作异常：%s", err.Error())
		} else {
			resp.BasePageHttpResponse.Code = 200
			resp.BasePageHttpResponse.Total = total
			resp.Data = list
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GoodsExport 导出分销商品
// @Summary 导出分销商品
// @Description 导出分销商品接口
// @Tags 后台接口-分销商品
// @Accept  json
// @Produce  json
// @Param GoodsPageReq body distribution_vo.GoodsPageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/goods/export [POST]
func GoodsExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.GoodsPageReq](r)
	req.OrgId = cast.ToInt(r.Header.Get("org_id"))
	if err != nil {
		log.Error("导出分销商品，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("导出分销商品，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = 9
		err := s.CreatTask(r, task)
		if err != nil {
			log.Error("导出分销商品：err=" + err.Error())
			resp.Message = fmt.Sprintf("导出分销商品：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GoodsGlobalSetting 全局佣金设置
// @Summary 全局佣金设置
// @Description 全局佣金设置接口
// @Tags 后台接口-分销商品
// @Accept  json
// @Produce  json
// @Param GoodsGlobalSetReq body distribution_vo.GoodsGlobalSetReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/goods/global-setting [POST]
func GoodsGlobalSetting(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.GoodsGlobalSetReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("全局佣金设置操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("全局佣金设置操作，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("全局佣金设置操作参数校验异常：%s", vErr)
		} else {
			service := services.DisGoodsService{}
			err = service.GoodsGlobalSetting(req)
			if err != nil {
				log.Error("全局佣金设置失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("全局佣金设置操作异常：%s", err.Error())
			} else {
				resp.Code = 200
				jwtInfo, err := jwtauth.GetJwtInfo(r)
				if err != nil || len(jwtInfo.UserNo) == 0 {
					log.Error("全局佣金设置，记录打印日志失败，未获取到登录人信息：err=" + err.Error())
				}
				log.Info("GoodsGlobalSetting 设置商品全局默认佣金比例rate=", req.DisCommisRate, "，设置信息人userno=", jwtInfo.UserNo)
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GetGoodsGlobalSetting 查询全局佣金设置
// @Summary 查询全局佣金设置
// @Description 全局佣金设置查询接口
// @Tags 后台接口-分销商品
// @Accept  json
// @Produce  json
// @Success 200 {object} distribution_vo.GlobalCommisSettingResp
// @Failure 400 {object} distribution_vo.GlobalCommisSettingResp
// @Router /manager/goods/global-setting-get [POST]
func GetGoodsGlobalSetting(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.GlobalCommisSettingResp{}

	orgId := r.Header.Get("org_id")
	resp.Code = 200
	service := services.DisGoodsService{}
	resp.GlobalCommisRate = service.GetGoodsGlobalSetting(cast.ToInt(orgId))

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// CommissionSetting 佣金设置
// @Summary 佣金设置
// @Description 佣金设置接口
// @Tags 后台接口-分销商品
// @Accept  json
// @Produce  json
// @Param GoodsCommissionSetReq body distribution_vo.GoodsCommissionSetReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/goods/commission-setting [POST]
func CommissionSetting(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.GoodsCommissionSetReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Error("佣金设置操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("佣金设置操作，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("佣金设置操作参数校验异常：%s", vErr)
		} else {
			service := services.DisGoodsService{}
			err = service.CommissionSetting(req)
			if err != nil {
				log.Error("佣金设置失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("佣金设置操作异常：%s", err.Error())
			} else {
				resp.Code = 200

				//添加设置佣金的操作日志
				go func(r *http.Request, req distribution_vo.GoodsCommissionSetReq) {
					var description string
					if req.IsDis == 0 {
						description = "取消商品分销"
					} else {
						if req.IsDefaultCommisRate == 1 {
							req.DisCommisRate = service.GetGoodsGlobalSetting(req.OrgId)
						}
						description = fmt.Sprintf("设置商品分销佣金为“%.2f%%”", req.DisCommisRate)
					}

					operateLogService := base_service.OperateLogService{}
					operateLogService.Add(r, distribution_vo.OperateLogReq{
						ModuleType:  base_service.ModuleDisGoods,
						Type:        base_service.DisGoodsCommisSet,
						FromId:      cast.ToString(req.GoodsId),
						Description: description,
					})
				}(r, req)
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// GoodsDisWrite 分销商品-推广素材
// @Summary 分销商品-推广素材 @fuma-v1.1
// @Description 分销商品-推广素材
// @Tags 后台接口-分销商品
// @Accept  json
// @Produce  json
// @Param GoodsDisWriteReq body distribution_vo.GoodsDisWriteReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/goods/dis-write [POST]
func GoodsDisWrite(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}
	logPrefix := "分销商品推广素材编辑"
	req, err := utils.Bind[distribution_vo.GoodsDisWriteReq](r)
	orgId := r.Header.Get("org_id")
	req.OrgId = cast.ToInt(orgId)
	if err != nil {
		log.Errorf("%s，参数解析失败：err=%s", logPrefix, err.Error())
		resp.Message = fmt.Sprintf("%s，参数解析失败：%s", logPrefix, err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("%s参数校验异常：%s", logPrefix, vErr)
		} else {
			service := services.DisGoodsService{}
			err = service.GoodsDisWrite(req)
			if err != nil {
				log.Errorf("%s失败：err=%s", logPrefix, err.Error())
				resp.Message = fmt.Sprintf("%s操作异常：%s", logPrefix, err.Error())
			} else {
				resp.Code = 200

			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}
