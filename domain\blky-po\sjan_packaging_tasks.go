package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SjanPackagingTask 北京世纪安诺包装任务表领域模型
type SjanPackagingTask struct {
	// 自增主键
	Id int `xorm:"pk autoincr not null 'id'" json:"id"`
	// 任务单号
	BillNo string `xorm:"not null unique VARCHAR(50) 'bill_no'" json:"bill_no"`
	// 任务开始时间
	InDate time.Time `xorm:"not null datetime 'in_date'" json:"in_date"`
	// 产品编号
	PNo string `xorm:"not null VARCHAR(50) 'p_no'" json:"p_no"`
	// 产品名称
	Pname string `xorm:"not null VARCHAR(100) 'pname'" json:"pname"`
	// 包装数量
	UnitNum int `xorm:"not null 'unit_num'" json:"unit_num"`
	// 创建时间
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"`
}

// TableName 表名
func (s *SjanPackagingTask) TableName() string {
	return "blky.sjan_packaging_tasks"
}

// GetByBillNo 根据任务单号获取包装任务
func (s *SjanPackagingTask) GetByBillNo(session *xorm.Session, billNo string) (*SjanPackagingTask, error) {
	var task SjanPackagingTask
	has, err := session.Where("bill_no = ?", billNo).Get(&task)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &task, nil
}

// GetByPNo 根据产品编号获取包装任务列表
func (s *SjanPackagingTask) GetByPNo(session *xorm.Session, pNo string) ([]SjanPackagingTask, error) {
	var tasks []SjanPackagingTask
	err := session.Where("p_no = ?", pNo).Find(&tasks)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}
