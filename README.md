# eShop

线上零售Saas版

## swag 提示

第一次执行swag init前， 请先执行：go install github.com/swaggo/swag/cmd/swag@latest

必须在eshop目录下 执行swag init命令

swag 里面出现的结构体，统一用eshop\contracts\view-model这个目录下的，否则无法生成swag

## 规范

命名规范

    包名：仅支持小写字母、数字。

    文件名：仅支持小写字母、下划线、数字

表的归属原则：PO在哪个服务，那个服务就有写入权限。

请求参数 字段名 和 接口返回字段名：统一用 小写和下划线。

数据库表字段 如果用整型来做枚举， 统一从1开始。 （例如：提现状态：dis_withdraw.status：0-默认值，1-待审核，2-已打款，3-审核驳回，理由是： 管理后台如果按状态查， 不传，则是查全部状态）
