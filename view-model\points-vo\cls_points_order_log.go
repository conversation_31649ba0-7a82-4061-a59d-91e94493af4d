package points_vo

import (
	po "eShop/domain/points-po"
)

// ClsPointsOrderLogSaveVO 用于创建订单日志的视图对象
type ClsPointsOrderLogSaveVO struct {
	SuperSaveVO[po.ClsPointsOrderLog]
	OrderId  int    `json:"order_id" validate:"required"`
	Status   int    `json:"status" validate:"required"`
	Operator string `json:"operator"`
}

// ClsPointsOrderLogUpdateVO 用于更新订单日志的视图对象 (通常日志不更新，或有限更新)
type ClsPointsOrderLogUpdateVO struct {
	SuperUpdateVO[po.ClsPointsOrderLog]
	Id int `json:"id" validate:"required"`
}

// ClsPointsOrderLogQueryVO 用于查询订单日志的视图对象
type ClsPointsOrderLogQueryVO struct {
	SuperQueryVO[po.ClsPointsOrderLog]
	OrderId int `json:"order_id" query:"order_id:eq"`
}

// ClsPointsOrderLogResultVO 用于返回订单日志结果的视图对象
type ClsPointsOrderLogResultVO struct {
	SuperResultVO[ClsPointsOrderLogResultVO]
	Id        int    `json:"id"`
	OrderId   int    `json:"order_id"`
	Status    int    `json:"status"`
	Operator  string `json:"operator,omitempty"`
	CreatedAt string `json:"created_at"`
}
