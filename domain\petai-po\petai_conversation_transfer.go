package petai_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"errors"
	"time"

	"xorm.io/xorm"
)

type PetaiConversationTransfer struct {
	Id                 int       `json:"id"  xorm:"pk autoincr 'id' comment('主键')"` //自增id
	ConversationId     int       `json:"conversation_id"`                           //会话id即petai_conversation.id
	PmOrderSn          int64     `json:"pm_order_sn"`                               //互联网医院问诊订单号
	Evaluate           int       `json:"evaluate"`                                  //消息评价：0-初始，1-赞，2-踩
	EvaluateTime       time.Time `json:"evaluate_time"`                             //消息评价时间
	Feedback           string    `json:"feedback"`                                  //用户反馈
	FeedbackType       int       `json:"feedback_type"`                             //反馈类别:1-没有帮助，2-信息有误，3-理解错误，4-违法有害，5-内容不完整 6-内容不专业，7-格式错误
	FeedbackDoctorName string    `json:"feedback_doctor_name"`                      //反馈医生名称
	FeedbackDoctorCode string    `json:"feedback_doctor_code"`                      //反馈医生编号
	CreateTime         time.Time `json:"create_time" xorm:"created"`                //创建时间
	UpdateTime         time.Time `json:"update_time" xorm:"updated"`                //更新时间

}

func (m *PetaiConversationTransfer) TableName() string {
	return "eshop.petai_conversation_transfer"
}

type GetConversationTransfersReq struct {
	ConversationId int `json:"conversation_id"` //会话id即petai_conversation.id
}

// 获取转接互联网医生人工记录列表
func (m *PetaiConversationTransfer) GetConversationTransfers(session *xorm.Session, in GetConversationTransfersReq) (out []PetaiConversationTransfer, err error) {
	logPrefix := "====获取转接互联网医生人工记录列表GetConversationTransfers,入参:" + utils.JsonEncode(in)
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if in.ConversationId == 0 {
		err = errors.New("会话id为空")
		return
	}
	out = make([]PetaiConversationTransfer, 0)
	if err = session.Where("conversation_id=?", in.ConversationId).OrderBy("id asc").Find(&out); err != nil {
		log.Error(logPrefix+",err:", err)
		return
	}
	return
}
