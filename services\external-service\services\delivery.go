package services

import (
	"bytes"
	glog "eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/view-model/external-vo/dto"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
)

type Delivery struct {
	common.BaseService
}

// 发送配送，用主单号
func (s *Delivery) PushDelivery(orderSn string) error {
	model := dto.DeliveryOrder{}
	model.OrderSn = orderSn
	str, _ := json.Marshal(model)
	url := "http://127.0.0.1:11001/boss/noAuth/order/push-delivery"
	ret, err := HttpPostTo(url, str)
	glog.Info("ESHOP 发配送返回", string(ret), err)
	return nil
}

func HttpPostTo(url string, bytesData []byte) ([]byte, error) {
	//跳过证书验证
	//tr := &http.Transport{
	//	TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	//	DialContext: (&net.Dialer{
	//		Timeout: 10 * time.Second,
	//	}).DialContext,
	//}

	reader := bytes.NewReader(bytesData)
	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		return nil, err
	}
	//request.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
	request.Header.Set("Content-Type", "application/json;charset=utf-8")

	//client := &http.Client{Transport: tr}
	resp, err := utils.HttpTransportClientTimeout.Do(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, errors.New(resp.Status)
	}
	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	} else {
	}
	return respBytes, nil
}
