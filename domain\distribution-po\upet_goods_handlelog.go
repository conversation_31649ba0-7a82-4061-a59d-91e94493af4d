package distribution_po

import "time"

type UpetGoodsHandlelog struct {
	GhId       int       `json:"gh_id" xorm:"pk autoincr not null comment('索引ID') INT 'gh_id'"`
	GhGoodIds  string    `json:"gh_good_ids" xorm:"comment('商品skuid') MEDIUMTEXT 'gh_good_ids'"`
	GhNotes    string    `json:"gh_notes" xorm:"not null default '' comment('操作说明') VARCHAR(50) 'gh_notes'"`
	GhAddtime  int64     `json:"gh_addtime" xorm:"not null default 0 comment('添加时间') INT 'gh_addtime'"`
	GhState    int       `json:"gh_state" xorm:"not null default 0 comment('处理状态0默认1已处理') TINYINT(1) 'gh_state'"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	StoreId    int       `json:"store_id" xorm:"not null default 1 comment('店铺id') INT 'store_id'"`
	ShopId     int       `json:"shop_id" xorm:"not null default 0 comment('分销店铺id') INT 'shop_id'"`
}
