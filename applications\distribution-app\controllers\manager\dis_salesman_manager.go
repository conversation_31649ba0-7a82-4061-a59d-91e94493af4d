package manager

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// @Summary 业务员详情接口
// @Description
// @Tags 后台接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body distribution_vo.DisSalesmanStopReq true " "
// @Success 200 {object} distribution_vo.DisSalesmanRes
// @Failure 400 {object} distribution_vo.DisSalesmanRes
// @Router /manager/salesman/get [POST]
func SaleManGet(writer http.ResponseWriter, request *http.Request) {
	//这里只对request的参数做检查,不涉及任何业务逻辑。
	//所有逻辑放在服务层

	resp := distribution_vo.DisSalesmanRes{}
	resp.Code = 400

	server := services.DisSalesmanService{}
	req, err := utils.Bind[distribution_vo.DisSalesmanStopReq](request)

	if err != nil {
		log.Error("获取业务员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取业务员列表，参数解析失败：%s", err.Error())
	} else {

		vErr := validate.Validate(req, "upd")
		if vErr != nil {
			resp.Message = fmt.Sprintf("获取业务员参数校验异常：%s", vErr)
		} else {
			data, err := server.Get(req)
			if err != nil {
				log.Error("获取业务员失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("获取业务员异常：%s", err.Error())
			} else {
				resp.Code = 200
				resp.Data = data
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员列表接口
// @Description
// @Tags 后台接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body distribution_vo.DisSalesmanListReq true " "
// @Success 200 {object} distribution_vo.DisSalesmanListRes
// @Failure 400 {object} distribution_vo.DisSalesmanListRes
// @Router /manager/salesman/list [POST]
func SaleManList(writer http.ResponseWriter, request *http.Request) {
	//这里只对request的参数做检查,不涉及任何业务逻辑。
	//所有逻辑放在服务层

	resp := distribution_vo.DisSalesmanListRes{}
	resp.Code = 400

	server := services.DisSalesmanService{}
	req, err := utils.Bind[distribution_vo.DisSalesmanListReq](request)
	if req.OrgId == 0 {
		req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	}

	if err != nil {
		log.Error("获取业务员列表，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取业务员列表，参数解析失败：" + err.Error())
	} else {
		data, total, err := server.GetList(req)
		if err != nil {
			log.Error("获取业务员列表失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取业务员列表异常：" + err.Error())
		} else {
			resp.Code = 200
			resp.Total = total
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员添加
// @Description
// @Tags 后台接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body distribution_vo.DisSalesman true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/salesman/add [POST]
func SaleManAdd(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	server := services.DisSalesmanService{}
	req, err := utils.Bind[distribution_vo.DisSalesman](request)
	if req.OrgId == 0 {
		req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	}

	//校验手机格式
	if !utils.IsValidPhoneNumber(req.Mobile) {
		resp.Message = "手机号格式不正确"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	if err != nil {
		log.Error("添加业务员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("添加业务员，参数解析失败：" + err.Error())
	} else {
		vErr := validate.Validate(req, "add")
		if vErr != nil {
			resp.Message = fmt.Sprintf("业务员添加参数校验异常：%s", vErr)
		} else {
			err = server.SaleManAdd(req)
			if err != nil {
				log.Error("添加业务员失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("添加业务员异常：" + err.Error())
			} else {
				resp.Code = 200
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员编辑
// @Description
// @Tags 后台接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body distribution_vo.DisSalesman true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/salesman/edit [POST]
func SaleManEdit(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	//server := services.DisSalesmanService{}
	//req, err := utils.Bind[distribution_vo.DisSalesman](request)
	//if err != nil {
	//	log.Error("编辑业务员，参数解析失败：err=", err.Error())
	//	resp.Message = fmt.Sprintf("编辑业务员，参数解析失败：" + err.Error())
	//} else {
	//	vErr := validate.Validate(req, "upd")
	//	if vErr != nil {
	//		resp.Message = fmt.Sprintf("业务员编辑参数校验异常：%s", vErr)
	//	} else {
	//		err = server.SaleManEdit(req)
	//		if err != nil {
	//			log.Error("编辑业务员失败：err=" + err.Error())
	//			resp.Message = fmt.Sprintf("添加业务员异常：" + err.Error())
	//		} else {
	//			resp.Code = 200
	//			go func(id int, r *http.Request) {
	//				operateService := base_service.OperateLogService{}
	//				Operatelog := distribution_vo.OperateLogReq{}
	//				Operatelog.ModuleType = base_service.ModuleSalesman
	//				Operatelog.Type = base_service.SalesmanEdit
	//				Operatelog.FromId = id
	//				Operatelog.Description = "编辑业务员信息"
	//				operateService.Add(r, Operatelog)
	//			}(req.Id, request)
	//		}
	//	}
	//}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员停用启用
// @Description
// @Tags 后台接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body distribution_vo.DisSalesmanStopReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/salesman/stop [POST]
func SaleManStopOrStar(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	//server := services.DisSalesmanService{}
	//req, err := utils.Bind[distribution_vo.DisSalesmanStopReq](request)
	//if err != nil {
	//	log.Error("业务员停用启用，参数解析失败：err=", err.Error())
	//	resp.Message = fmt.Sprintf("业务员停用启用，参数解析失败：" + err.Error())
	//} else {
	//
	//	vErr := validate.Validate(req, "upd")
	//	if vErr != nil {
	//		resp.Message = fmt.Sprintf("业务员停用启用参数校验异常：%s", vErr)
	//		bytes, _ := json.Marshal(resp)
	//		writer.Write(bytes)
	//	}
	//
	//	err = server.SaleManStopOrStar(req)
	//	if err != nil {
	//		log.Error("业务员停用启用：err=" + err.Error())
	//		resp.Message = fmt.Sprintf("业务员停用启用一次：" + err.Error())
	//	} else {
	//		resp.Code = 200
	//		go func(model distribution_vo.DisSalesmanStopReq, r *http.Request) {
	//			operateService := base_service.OperateLogService{}
	//			Operatelog := distribution_vo.OperateLogReq{}
	//			desc := "业务员被"
	//			Operatelog.Type = base_service.SalesmanStop
	//			if model.Status == 2 {
	//				Operatelog.Type = base_service.SalesmanStart
	//				desc += "启用"
	//			} else {
	//				desc += "停用"
	//			}
	//			Operatelog.ModuleType = base_service.ModuleSalesman
	//			Operatelog.FromId = model.Id
	//			Operatelog.Description = ""
	//			operateService.Add(r, Operatelog)
	//		}(req, request)
	//	}
	//}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 业务员导出接口
// @Description
// @Tags 后台接口-业务员
// @Accept  json
// @Produce  json
// @Param SaleMan body distribution_vo.DisSalesmanListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/salesman/export [POST]
func SaleManExport(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.DisSalesmanListReq](request)
	req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	if err != nil {
		log.Error("导出业务员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		s := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(req)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(request.Header.Get("org_id"))
		task.TaskContent = 2
		err := s.CreatTask(request, task)
		if err != nil {
			log.Error("导出业务员：err=" + err.Error())
			resp.Message = fmt.Sprintf("导出业务员：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
