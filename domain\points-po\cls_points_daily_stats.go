package points_po

import "time"

// ClsPointsDailyStats 对应 cls_points_daily_stats 表的持久化对象
type ClsPointsDailyStats struct {
	SuperEntity[int] `xorm:"extends"` // 主键ID, CreatedAt, UpdatedAt
	StatDate         time.Time        `xorm:"'stat_date' DATE notnull unique(idx_stat_date)" json:"statDate"` // 统计日期
	IssueTotal       int              `xorm:"'issue_total' notnull default 0" json:"issueTotal"`
	IssueBlky        int              `xorm:"'issue_blky' notnull default 0" json:"issueBlky"`
	IssueSzld        int              `xorm:"'issue_szld' notnull default 0" json:"issueSzld"`
	ConsumeTotal     int              `xorm:"'consume_total' notnull default 0" json:"consumeTotal"`
	ConsumeBlky      int              `xorm:"'consume_blky' notnull default 0" json:"consumeBlky"`
	ConsumeSzld      int              `xorm:"'consume_szld' notnull default 0" json:"consumeSzld"`
	ExpiredTotal     int              `xorm:"'expired_total' notnull default 0" json:"expiredTotal"`
	ExpiredBlky      int              `xorm:"'expired_blky' notnull default 0" json:"expiredBlky"`
	ExpiredSzld      int              `xorm:"'expired_szld' notnull default 0" json:"expiredSzld"`
}

// TableName 指定数据库中的表名
func (e ClsPointsDailyStats) TableName() string {
	return "cls_points_daily_stats"
}

// AsPointer 返回实体对象的指针，用于 SuperService 泛型约束
func (e ClsPointsDailyStats) AsPointer() any {
	return &e
}
