package controllers

import (
	"eShop/services/inventory-service/voucher"

	"github.com/go-chi/chi/v5"
)

// VoucherController 库存库存单据控制器
type VoucherController struct {
	service voucher.VoucherService
}

func NewVoucherController(service voucher.VoucherService) *VoucherController {
	return &VoucherController{
		service: service,
	}
}

func (c VoucherController) RegisterRoutes(r chi.Router) {
	//采购申请单
	r.Route("/inventory-app/voucher/purchase/request", func(r chi.Router) {
		r.Post("/save", c.RequestSave)    //保存申请的，参数id>0为修改，<=0 为新增；下面几个save都是一样的
		r.Get("/page", c.RequestPage)     //列表页
		r.Get("/detail", c.RequestDetail) // 单据详情页
		r.Post("/cancel", c.RequestCancel) // 取消单据
		r.Post("/manual-complete", c.RequestManualComplete) // 手动完成单据
		r.Post("/save-logistics-info", c.RequestSaveLogisticsInfo) // 保存物流信息
	})

	//采购入库单
	r.Route("/inventory-app/voucher/purchase/inbound", func(r chi.Router) {
		r.Post("/save", c.InboundSave)
		r.Get("/page", c.InboundPage)
		r.Get("/detail", c.InboundDetail)
	})

	//采购退货申请单
	r.Route("/inventory-app/voucher/purchase/return", func(r chi.Router) {
		r.Post("/save", c.ReturnSave)    //保存申请的，参数id>0为修改，<=0 为新增；下面几个save都是一样的
		r.Get("/page", c.ReturnPage)     //列表页
		r.Get("/detail", c.ReturnDetail) // 单据详情页
		r.Post("/cancel", c.ReturnCancel) // 取消单据
		r.Post("/save-logistics-info", c.ReturnSaveLogisticsInfo) // 保存物流信息
	})
	//采购退货出库单
	r.Route("/inventory-app/voucher/purchase/outbound", func(r chi.Router) {
		r.Post("/save", c.OutboundSave)
		r.Get("/page", c.OutboundPage)
		r.Get("/detail", c.OutboundDetail)
	})

}
