package common

import (
	"eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"fmt"
	"github.com/spf13/cast"
	"strings"
	"time"
	"xorm.io/xorm"
)

// GetChannelInfoByStoreId 获取渠道信息
func GetChannelInfoByStoreId(db *xorm.Engine, financeCode string, channelId int) (string, int, bool) {
	redisConn := cache.GetRedisConn()
	key := fmt.Sprintf("%s:%s:%d", "ChannelStoreKey", financeCode, channelId)
	channelStoreVal := redisConn.Get(key).Val()
	if channelStoreVal == "" {
		storeChannel := omnibus_po.StoreChannel{}
		if _, err := db.Table("datacenter.store").Alias("a").
			Select("a.finance_code, a.app_channel,b.channel_id,b.channel_store_id").
			Join("left", "datacenter.store_relation b", "a.finance_code =  b.finance_code").
			Where(" a.finance_code =? and b.channel_id =?", financeCode, channelId).Get(&storeChannel); err != nil {
			log.Errorf("获取渠道配置信息异常：请求参数:%s,%d,error:%s", financeCode, channelId, err.Error())
			return "", 0, false
		}
		if storeChannel.ChannelStoreId == "" || storeChannel.AppChannel == 0 {
			return "", 0, false
		}
		redisConn.Set(key, fmt.Sprintf("%s|%d", storeChannel.ChannelStoreId, storeChannel.AppChannel), 60*time.Second)

		return storeChannel.ChannelStoreId, storeChannel.AppChannel, true
	} else {
		channelStoreArr := strings.Split(channelStoreVal, "|")
		return channelStoreArr[0], cast.ToInt(channelStoreArr[1]), true
	}
}
