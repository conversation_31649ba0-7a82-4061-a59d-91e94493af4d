package marketing_po

import "time"

// MarketingGift 赠品信息
type MarketingGift struct {
    Id           int       `xorm:"pk autoincr 'id'" json:"id"`                 // 唯一数据ID
    ChainId      int64     `xorm:"'chain_id'" json:"chain_id"`                 // 连锁ID
    StoreId      string    `xorm:"'store_id'" json:"store_id"`                 // 店铺ID
    Name         string    `xorm:"'name'" json:"name"`                         // 赠品名称
    ProductId    int       `xorm:"'product_id'" json:"product_id"`             // 商品ID
    SkuId        int       `xorm:"'sku_id'" json:"sku_id"`                     // 商品SKU_ID
    IsPermanent  int       `xorm:"'is_permanent'" json:"is_permanent"`         // 是否长期有效：0-否 1-是
    EnableTime   *string `xorm:"'enable_time'" json:"enable_time"`           // 有效期开始时间
    ExpireTime   *string `xorm:"'expire_time'" json:"expire_time"`           // 有效期结束时间
    PersonLimit  int       `xorm:"'person_limit'" json:"person_limit"`         // 每人限领次数
    TotalCount   int       `xorm:"'total_count'" json:"total_count"`           // 总数量
    RemainCount  int       `xorm:"'remain_count'" json:"remain_count"`         // 剩余数量
    ReceivedCount int      `xorm:"'received_count'" json:"received_count"`     // 领取数量
    UsedCount    int       `xorm:"'used_count'" json:"used_count"`             // 已使用数量
    Status       int       `xorm:"'status'" json:"status"`                     // 数据状态：1-未开始 2-赠送中 3-已结束
    IsDeleted    int       `xorm:"'is_deleted'" json:"is_deleted"`             // 删除标识：0-未删除 1-已删除
    CreatedTime  time.Time `xorm:"created 'created_time'" json:"created_time"` // 创建时间
    UpdatedTime  time.Time `xorm:"updated 'updated_time'" json:"updated_time"` // 更新时间
}

func (MarketingGift) TableName() string {
    return "eshop.marketing_gift"
} 