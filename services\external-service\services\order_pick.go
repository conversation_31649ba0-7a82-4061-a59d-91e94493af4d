package services

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/view-model/external-vo/dto"
	"encoding/json"
	"fmt"
)

type OrderPick struct {
	common.BaseService
}

// 通知饿了么拣货完成 或通知美团拣货完成
func (s *OrderPick) PushOrderPick(in dto.OrderPick) error {
	logPrefix := fmt.Sprintf("通知饿了么拣货完成 或通知美团拣货完成，入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)
	if in.ChannelId == common.ChannelElmId {
		str, _ := json.Marshal(in)
		url := "http://127.0.0.1:11001/boss/noAuth/order/elm-pick-complete"
		ret, err := HttpPostTo(url, str)
		log.Info(logPrefix, "ESHOP 发拣货完成返回", string(ret), err)
	}
	if in.ChannelId == common.ChannelMtId {
		str, _ := json.Marshal(in)
		url := "http://127.0.0.1:11001/boss/noAuth/order/mt-pick-complete"
		ret, err := HttpPostTo(url, str)
		log.Info(logPrefix, "ESHOP 发拣货完成返回", string(ret), err)
	}
	return nil
}
