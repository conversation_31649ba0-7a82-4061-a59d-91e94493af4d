package inventory

import baseVO "eShop/view-model/inventory-vo"

type InventoryChange struct {
	WarehouseId         int                   `json:"warehouse_id"`          // 仓库ID
	ItemType            int                   `json:"item_type"`             // 单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	ItemRefId           int                   `json:"item_ref_id"`           // 出入库关联的单据id
	ItemRefNo           string                `json:"item_ref_no"`           // 出入库关联的单据编号
	ItemRefType         int                   `json:"item_ref_type"`         // 出入库关联的单据类型，1.无，2.订单，3.入库单，4.盘点单，5.退款单
	Operator            string                `json:"operator"`              // 操作人
	SkuInventoryChanges []SkuInventoryChanges `json:"sku_inventory_changes"` // sku变更
	Remark              string                `json:"remark"`                // 备注
}

type SkuInventoryChanges struct {
	SkuId       int    `json:"sku_id"`       // SKU ID
	ProductName string `json:"product_name"` // 商品名称
	Price       int    `json:"price"`        // 商品价格(分)
	ChangeNum   int    `json:"change_num"`   // 变更数量, 出入库都为正数
}

type InventoryChangeCalc struct {
	ChangeNum          int `json:"change_num"`           // 变更数量
	CurrentCostPrice   int `json:"current_cost_price"`   // 当前变更时商品平均成本价
	ChangeAmount       int `json:"change_amount"`        // 当前变更金额
	TotalNumBefore     int `json:"total_num_before"`     // 变更前总库存
	FreezeNumBefore    int `json:"freeze_num_before"`    // 变更前锁定库存
	AvailableNumBefore int `json:"available_num_before"` // 变更前可用库存
	TotalAmountBefore  int `json:"total_amount_before"`  // 变更前总成本
	TotalNumAfter      int `json:"total_num_after"`      // 变更后总库存
	FreezeNumAfter     int `json:"freeze_num_after"`     // 变更后锁定库存
	AvailableNumAfter  int `json:"available_num_after"`  // 变更后可用库存
	TotalAmountAfter   int `json:"total_amount_after"`   // 变更后总成本
	AvgCostPriceAfter  int `json:"avg_cost_price_after"` // 变更后单价
}

type InventoryInitCommand struct {
	ChainId     int64                        `json:"chain_id"`     // 连锁id
	StoreId     string                       `json:"store_id"`     // 门店id
	WarehouseId int                          `json:"warehouse_id"` // 仓库id
	BoundType   int                          `json:"bound_type"`   // 出入库类型：1. 入库，2. 出库
	Details     []InventoryInitDetailCommand `json:"details"`      // 库存详情
	Operator    string                       `json:"operator"`     // 操作人
}

type InventoryInitDetailCommand struct {
	ProductId           int    `json:"product_id"`            // 商品id
	SkuId               int    `json:"sku_id"`                // sku id
	ProductName         string `json:"product_name"`          // 商品名称
	ProductType         int    `json:"product_type"`          // 商品类型：1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体
	BarCode             string `json:"bar_code"`              // 条形码
	ProductCategoryPath string `json:"product_category_path"` // 商品分类路径
	AvgCostPrice        int    `json:"avg_cost_price"`        // 平均成本价
}

type InventoryPageRequest struct {
	ChainId           int64  `json:"chain_id"`            // 连锁id
	StoreId           string `json:"store_id"`            // 门店id
	WarehouseId       int    `json:"warehouse_id"`        // 仓库id
	ProductType       int    `json:"product_type"`        // 商品类型：1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体
	Query             string `json:"query"`               // 查询条件
	IsOos             int    `json:"is_oos"`              // 是否无库存：1-无库存，2-有库存
	ProductTypes      []int  `json:"product_types"`       // 商品类型列表: 1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体
	ProductCategoryId int    `json:"product_category_id"` // 商品分类id
	FilterNoneFlow    int    `json:"filter_none_flow"`    // 过滤无入库记录商品
	baseVO.PageRequest
	baseVO.SortRequest
}

type InventoryResponse struct {
	Id                  int                    `json:"id"`                    // 主键
	ChainId             int64                  `json:"chain_id"`              // 连锁id
	StoreId             string                 `json:"store_id"`              // 门店id
	WarehouseId         int                    `json:"warehouse_id"`          // 仓库id
	WarehouseName       string                 `json:"warehouse_name"`        // 仓库名称
	ProductId           int                    `json:"product_id"`            // 商品id
	SkuId               int                    `json:"sku_id"`                // sku id
	ProductName         string                 `json:"product_name"`          // 商品名称
	ProductType         string                 `json:"product_type"`          // 商品类型
	BarCode             string                 `json:"bar_code"`              // 条形码
	ProductCategoryPath string                 `json:"product_category_path"` // 商品分类路径
	AvgCostPrice        int                    `json:"avg_cost_price"`        // 平均成本价
	TotalAmount         int                    `json:"total_amount"`          // 总成本
	TotalNum            int                    `json:"total_num"`             // 总库存
	FreezeNum           int                    `json:"freeze_num"`            // 锁定库存
	AvailableNum        int                    `json:"available_num"`         // 可用库存
	AvgDailySales       int                    `json:"avg_daily_sales"`       // 平均日销量
	TurnoverDays        int                    `json:"turnover_days"`         // 库存流转天数
	IsWaning            bool                   `json:"is_waning"`             // 是否提醒
	ProductSku          ProductSkuInfoResponse `json:"product_sku" xorm:"-"`  // 商品信息
}

type ProductSkuInfoResponse struct {
	SkuId            int    `json:"sku_id"`             // sku id
	SkuCode          string `json:"sku_code"`           // sku商家编码，默认生成，也可商家自定义
	BarCode          string `json:"bar_code"`           // 条形码
	CanSellNum       int    `json:"can_sell_num"`       // 起售个数
	SellPrice        int    `json:"sell_price"`         // 零售价
	MarketPrice      int    `json:"market_price"`       // 市场价
	BasicPrice       int    `json:"basic_price"`        // 成本价
	Weight           int    `json:"weight"`             // 重量
	BirthDate        string `json:"birth_date"`         // 出生日期,精确到月
	ServeTimeLength  int    `json:"serve_time_length"`  // 字典-服务时长, 必须5分钟-24小时之间
	ProductSpecs     string `json:"product_specs"`      // 规格信息
	IsDiscount       bool   `json:"is_discount"`        // 是否打折：0-否 1-是
	IsSellOut        bool   `json:"is_sell_out"`        // 是否售罄，0否,1是
	Status           string `json:"status"`             // 状态 ，OK已上架，  STOP已下架，DEL删除
	CategoryId       int    `json:"category_id"`        // 商品分类,对应 t_product_category表
	CategoryName     string `json:"category_name"`      // 目录名称
	IdPath           string `json:"id_path"`            // 目录path
	CategoryNamePath string `json:"category_name_path"` // 目录名path
	SkuInventoryNum  int    `json:"sku_inventory_num"`  // 商品sku库存
	CreatedTime      string `json:"created_time"`       // 创建时间
	ChainId          int64  `json:"chain_id"`           // 连锁ID
	ProductId        int    `json:"product_id"`         // 商品主表id
	ProductName      string `json:"product_name"`       // 商品名称
	Img              string `json:"img"`                // 商品图片地址
	BrandId          int    `json:"brand_id"`           // 品牌ID,关联品牌表
	BrandName        string `json:"brand_name"`         // 品牌名称
	SupplierId       int    `json:"supplier_id"`        // 供应商ID,关联供应商表id
	SupplierName     string `json:"supplier_name"`      // 供应商名称
	StoreUnitKey     string `json:"store_unit_key"`     // 库存单位key
	StoreUnit        string `json:"store_unit"`         // 库存单位
	ProductType      int    `json:"product_type"`       // 商品类型，GOODS实物，SER服务，ALIVE活体
	ProductSpecsType string `json:"product_specs_type"` // 规格类型 NO无规格 ,ONE单规格,MANY多规格
	LocationCode     string `json:"location_code"`      // 库位名称
}

// 缺货预警请求结构体
// 仓库类型：3-门店仓，4-加盟仓
// 商品分类用pro_product.category_id_offline
// Query为商品名称或条码
// Page/Size为分页
type InventoryWarningRequest struct {
	StoreId           string `json:"store_id"`            // 门店ID
	WarehouseCategory int    `json:"warehouse_category"`  // 仓库类型 3-门店仓 4-加盟仓
	ProductCategoryId int64  `json:"product_category_id"` // 商品分类（pro_product.category_id_offline）
	Query             string `json:"query"`               // 商品名称或条码
	Page              int    `json:"page"`
	Size              int    `json:"size"`
}

type InventoryWarningResponse struct {
	Id                  int    `json:"id"`
	ChainId             int64  `json:"chain_id"`
	StoreId             string `json:"store_id"`
	WarehouseId         int    `json:"warehouse_id"`
	WarehouseName       string `json:"warehouse_name"`
	ProductId           int    `json:"product_id"`
	SkuId               int    `json:"sku_id"`
	ProductName         string `json:"product_name"`
	BarCode             string `json:"bar_code"`
	ProductSpecs        string `json:"product_specs"`         // 规格
	LocationCode        string `json:"location_code"`         // 库位
	ProductCategoryPath string `json:"product_category_path"` // 产品分类（category_nav）
	StoreUnit           string `json:"store_unit"`            // 单位
	AvgCostPrice        int    `json:"avg_cost_price"`        // 平均成本价
	AvailableNum        int    `json:"available_num"`         // 系统库存
	Category            int    `json:"category"`              // 3门店仓 4 加盟仓
	Images              string `json:"images"`                //图片
}

// 库存预计的消息
type MessageCreateRequest struct {
	ShopId     string `json:"shop_id"`
	MemberMain string `json:"member_main"`
	Content    string `json:"content"`
	//消息类别 -1 批量上架结果通知 1 接单通知 2 拣货订单 3退款 5配送异常 6库存预警
	MessageType int32 `json:"message_type"`
}

// 库存预计的消息 Content 内容
type OrderMessage struct {
	//消息类别 -1 批量上架结果通知 1 接单通知 2 拣货订单 3退款 5配送异常 6库存预警
	MessageType int    `json:"MessageType"`
	Msg         string `json:"Msg"`
	FinanceCode string `json:"FinanceCode"`
	WarehouseId int    `json:"WarehouseId"`
	SkuId       int    `json:"SkuId"`
	BarCode     string `json:"BarCode"`
}
