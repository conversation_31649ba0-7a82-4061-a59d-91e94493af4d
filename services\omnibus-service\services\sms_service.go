package services

import (
	"context"
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/infra/utils"
	"eShop/services/common"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"errors"
	"fmt"
	"strings"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	"github.com/golang/glog"
	"github.com/limitedlee/microservice/common/config"

	"encoding/json"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type SmsService struct {
	common.BaseService
}

// 短信模板参数结构体
type (
	// 验证码参数
	SmsVerifyCodeParams struct {
		Code string `json:"code"`
	}

	// 消费通知参数
	SmsConsumeParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 支付金额
		PayAmount string `json:"payAmount"`
		// 余额
		BalanceAmount string `json:"balanceAmount"`
	}

	// 退款通知参数
	SmsRefundParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 退款金额
		RefundAmount string `json:"refundAmount"`
		// 余额
		BalanceAmount string `json:"balanceAmount"`
	}

	// 充值金额通知参数
	SmsRechargeParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 充值金额
		ChangeAmount string `json:"changeAmount"`
		// 余额
		BalanceAmount string `json:"balanceAmount"`
	}

	// 充值次数通知参数
	SmsRechargeCountParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 充值金额
		ChangeAmount string `json:"changeAmount"`
		// 次数
		TotalNum string `json:"totalNum"`
	}

	// 储值卡到期提醒参数
	SmsStoreCardExpireParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 激活时间
		ActivateTime string `json:"activateTime"`
		// 储值卡名称
		StoreCardName string `json:"storeCardName"`
		// 到期时间
		ExpireTime string `json:"expireTime"`
		// 余额
		BalanceAmount string `json:"balanceAmount"`
	}

	// 次卡到期提醒参数
	SmsTimeCardExpireParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 激活时间
		ActivateTime string `json:"activateTime"`
		// 次卡名称
		TimeCardName string `json:"timeCardName"`
		// 到期时间
		ExpireTime string `json:"expireTime"`
		// 次数
		TotalNum string `json:"totalNum"`
	}

	// 赠送优惠券通知参数
	SmsCouponGiftParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 优惠券额度
		Threshold string `json:"threshold"`
		//优惠金额/折扣
		Discount string `json:"discount"`
	}

	// 优惠券到期提醒参数
	SmsCouponExpireParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 优惠券类型
		CouponType string `json:"couponType"`
		// 内容
		Content string `json:"content"`
		// 到期时间
		ExpireTime string `json:"expireTime"`
	}

	// 预约服务通知参数
	SmsReservationParams struct {
		// 店铺名称
		TenantName      string `json:"tenantName"`
		ReservationTime string `json:"reservationTime"`
	}

	// 寄养到期通知参数
	SmsPetHostingParams struct {
		// 店铺名称
		TenantName string `json:"tenantName"`
		// 宠物名称
		PetName string `json:"petName"`
		// 结束时间
		EndTime string `json:"endTime"`
	}
)

// SmsLengthResult 短信长度计算结果
type SmsLengthResult struct {
	// 完整内容
	Content string
	// 总字数
	TotalLength int
	// 短信条数
	SmsCount int
	// 每条短信的字数
	Segments []int
}

// GetSmsConfigs 获取短信配置并转换为视图模型
func (s *SmsService) GetSmsConfigs(ctx context.Context, storeId string) ([]omnibus_vo.SmsConfigItem, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()
	log.Infof("test sms")
	// 获取领域模型数据
	smsConfig := &omnibus_po.SmsConfig{}
	configs, err := smsConfig.GetByStoreId(session, storeId)

	// 如果没有配置，初始化默认配置
	if err == nil && len(configs) == 0 {
		if err := smsConfig.InitDefaultConfigs(session, storeId); err != nil {
			log.Error("初始化短信配置失败:", err)
			return nil, err
		}

		// 重新获取配置
		configs, err = smsConfig.GetByStoreId(session, storeId)
	}

	if err != nil {
		return nil, err
	}

	// 转换为视图模型
	items := make([]omnibus_vo.SmsConfigItem, 0, len(configs))
	for _, config := range configs {
		isSetDate := false
		if config.ConfigType == "store_card_expire" || config.ConfigType == "time_card_expire" || config.ConfigType == "coupon_expire" {
			isSetDate = true
		}
		items = append(items, omnibus_vo.SmsConfigItem{
			Id:           config.Id,
			ConfigType:   config.ConfigType,
			ConfigName:   config.ConfigName,
			TemplateCode: config.TemplateCode,
			IsEnabled:    config.IsEnabled,
			IsSetDate:    isSetDate,
			ExpireDays:   config.ExpireDays,
		})
	}

	return items, nil
}

// BatchSaveSmsConfigs 从请求模型转换并批量保存短信配置
func (s *SmsService) BatchSaveSmsConfigs(ctx context.Context, req omnibus_vo.SmsConfigRequest) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 处理配置项
	for _, item := range req.Data {
		config := &omnibus_po.SmsConfig{
			StoreId:      req.StoreId,
			ConfigType:   item.ConfigType,
			ConfigName:   item.ConfigName,
			IsEnabled:    item.IsEnabled,
			ExpireDays:   item.ExpireDays,
			TemplateCode: item.TemplateCode,
		}

		if item.Id > 0 {
			config.Id = item.Id
		}

		if err := config.Save(session); err != nil {
			session.Rollback()
			return err
		}
	}

	return session.Commit()
}

// QuerySmsOrders 查询短信订单
func (s *SmsService) QuerySmsOrders(ctx context.Context, req omnibus_vo.SmsOrderQueryRequest) (*omnibus_vo.SmsOrderResponse, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 构建查询条件
	query := session.Table("eshop.sms_order_info")
	if req.ChainId != "" {
		query = query.And("chain_id = ?", req.ChainId)
	}
	if req.StoreId != "" {
		query = query.And("store_id = ? or store_name like ?", req.StoreId, "%"+req.StoreId+"%")
	}
	if req.OrderStatus > 0 {
		query = query.And("order_status = ?", req.OrderStatus)
	}
	if req.StartTime != "" {
		query = query.And("order_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.And("order_time <= ?", req.EndTime)
	}
	if req.PayStartTime != "" {
		query = query.And("pay_time >= ?", req.PayStartTime)
	}
	if req.PayEndTime != "" {
		query = query.And("pay_time <= ?", req.PayEndTime)
	}

	//// 获取总数
	//total, err := query.Count(new(omnibus_po.SmsOrderInfo))
	//if err != nil {
	//	return nil, err
	//}

	// 分页查询
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.PageIndex - 1) * req.PageSize

	orders := make([]omnibus_po.SmsOrderInfo, 0)
	total, err := query.Limit(req.PageSize, offset).OrderBy("id desc").FindAndCount(&orders)
	if err != nil {
		return nil, err
	}

	// 转换为视图模型
	response := &omnibus_vo.SmsOrderResponse{}
	response.Total = cast.ToInt(total)
	response.Data.OrderList = make([]omnibus_vo.SmsOrderDetailData, 0, len(orders))

	// 收集所有需要查询的连锁ID
	chainIds := make([]int64, 0)
	chainIdMap := make(map[int64]bool)
	for _, record := range orders {
		if record.ChainId > 0 && !chainIdMap[record.ChainId] {
			chainIds = append(chainIds, record.ChainId)
			chainIdMap[record.ChainId] = true
		}
	}

	// 查询连锁名称映射
	chainMap, err := s.GetChainNames(session, chainIds)
	if err != nil {
		log.Error("获取连锁名称映射失败:", err)
	}

	for _, order := range orders {
		// 获取连锁名称
		chainName := ""
		if name, ok := chainMap[order.ChainId]; ok {
			chainName = name
		}
		response.Data.OrderList = append(response.Data.OrderList, omnibus_vo.SmsOrderDetailData{
			Id:           order.Id,
			ChainId:      order.ChainId,
			StoreId:      order.StoreId,
			AmountNum:    order.AmountNum,
			PayAmount:    order.PayAmount,
			OrderTime:    order.OrderTime,
			PayTime:      order.PayTime,
			Operator:     order.Operator,
			OrderStatus:  order.OrderStatus,
			UsedCount:    order.UsedCount,
			RefundAmount: order.RefundAmount,
			StoreName:    order.StoreName,
			ChainName:    chainName,
			RefundCount:  order.RefundCount,
			Remark:       order.Remark,
			CreateTime:   order.CreateTime,
		})
	}
	if req.Source == 0 {

		redis := cache.GetRedisConn()
		countKey := omnibus_po.StoreSmsCountKey + req.StoreId
		count, _ := redis.Get(countKey).Int64()
		response.Data.Statistics.SurplusCount = cast.ToInt(count)

		totalAmount, err := session.Table("eshop.sms_order_info").
			Where("store_id = ? AND order_status = 1", req.StoreId).
			Sum(omnibus_po.SmsOrderInfo{}, "amount_num")
		if err != nil {
			log.Error("统计订单总量失败:", err)
		}
		response.Data.Statistics.SuccessCount = cast.ToInt(totalAmount)
	}
	//response.Data.Statistics.SuccessCount=

	return response, nil
}

// QuerySmsSendRecords 查询短信发送记录
func (s *SmsService) QuerySmsSendRecords(ctx context.Context, req omnibus_vo.SmsSendRecordQueryRequest) (*omnibus_vo.SmsSendRecordResponse, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 构建查询条件
	query := session.Table("eshop.sms_send_record")
	if req.ChainId != "" {
		query = query.And("chain_id = ?", req.ChainId)
	}
	if req.StoreId != "" {
		query = query.And("store_id = ? or store_name like ?", req.StoreId, "%"+req.StoreId+"%")
	}
	if req.Mobile != "" {
		query = query.And("en_mobile = ?", utils.MobileEncrypt(req.Mobile))
	}
	if req.SmsType > 0 {
		query = query.And("sms_type = ?", req.SmsType)
	}
	if req.StartTime != "" {
		query = query.And("send_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.And("send_time <= ?", req.EndTime)
	}

	//// 获取总数
	//total, err := query.Count(new(omnibus_po.SmsSendRecord))
	//if err != nil {
	//	return nil, err
	//}

	// 分页查询
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.PageIndex - 1) * req.PageSize

	records := make([]omnibus_po.SmsSendRecord, 0)
	total, err := query.Limit(req.PageSize, offset).OrderBy("id desc").FindAndCount(&records)
	if err != nil {
		return nil, err
	}

	// 转换为视图模型
	response := &omnibus_vo.SmsSendRecordResponse{}
	response.Total = cast.ToInt(total)
	response.Data.Records = make([]omnibus_vo.SmsSendRecordItem, 0, len(records))
	// 收集所有需要查询的连锁ID
	chainIds := make([]int64, 0)
	chainIdMap := make(map[int64]bool)
	for _, record := range records {
		if record.ChainId > 0 && !chainIdMap[record.ChainId] {
			chainIds = append(chainIds, record.ChainId)
			chainIdMap[record.ChainId] = true
		}
	}

	// 查询连锁名称映射
	chainMap, err := s.GetChainNames(session, chainIds)
	if err != nil {
		log.Error("获取连锁名称映射失败:", err)
	}
	for _, record := range records {
		// 获取连锁名称
		chainName := ""
		if name, ok := chainMap[record.ChainId]; ok {
			chainName = name
		}
		response.Data.Records = append(response.Data.Records, omnibus_vo.SmsSendRecordItem{
			Id:         record.Id,
			Mobile:     record.Mobile,
			SmsType:    record.SmsType,
			Content:    record.Content,
			SendStatus: record.SendStatus,
			SendTime:   record.SendTime,
			RetryCount: record.RetryCount,
			StoreName:  record.StoreName,
			EnMobile:   record.EnMobile,
			ChainName:  chainName,
		})
	}
	if req.Source == 0 {

		redis := cache.GetRedisConn()
		countKey := omnibus_po.StoreSmsCountKey + req.StoreId
		count, _ := redis.Get(countKey).Int64()
		response.Data.Statistics.SurplusCount = cast.ToInt(count)

	}

	return response, nil
}

// GetChainNames 获取连锁名称映射
func (s *SmsService) GetChainNames(session *xorm.Session, chainIds []int64) (map[int64]string, error) {
	chainMap := make(map[int64]string)
	if len(chainIds) == 0 {
		return chainMap, nil
	}

	var chainInfos []struct {
		ChainId   int64  `xorm:"id"`
		ChainName string `xorm:"name"`
	}

	// 从eshop_saas.t_chain表查询连锁名称
	err := session.Table("eshop_saas.t_chain").In("id", chainIds).Cols("id", "name").
		Find(&chainInfos)

	if err != nil {
		log.Error("查询连锁信息失败:", err)
		return chainMap, err
	}

	for _, info := range chainInfos {
		chainMap[info.ChainId] = info.ChainName
	}

	return chainMap, nil
}

// GetSmsRefundList 获取退单短信列表
func (s *SmsService) GetSmsRefundList(ctx context.Context, req omnibus_vo.SmsRefundListRequest) ([]omnibus_vo.SmsRefundData, int64, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	record := &omnibus_po.SmsRefundRecord{}
	records, total, err := record.GetList(
		session,
		req.ChainId,
		req.StoreId,
		req.StoreName,
		req.RefundSn,
		req.OrderSn,
		req.RefundStatus,
		req.StartApplyTime,
		req.EndApplyTime,
		req.StartRefundTime,
		req.EndRefundTime,
		(req.PageIndex-1)*req.PageSize,
		req.PageSize,
	)

	if err != nil {
		return nil, 0, err
	}

	// 收集所有需要查询的连锁ID
	chainIds := make([]int64, 0)
	chainIdMap := make(map[int64]bool)
	for _, record := range records {
		if record.ChainId > 0 && !chainIdMap[record.ChainId] {
			chainIds = append(chainIds, record.ChainId)
			chainIdMap[record.ChainId] = true
		}
	}

	// 查询连锁名称映射
	chainMap, err := s.GetChainNames(session, chainIds)
	if err != nil {
		log.Error("获取连锁名称映射失败:", err)
	}

	data := make([]omnibus_vo.SmsRefundData, 0, len(records))
	for _, record := range records {
		applyTimeStr := record.ApplyTime.Format("2006-01-02 15:04:05")
		refundTimeStr := ""
		if !record.RefundTime.IsZero() {
			refundTimeStr = record.RefundTime.Format("2006-01-02 15:04:05")
		}

		// 获取连锁名称
		chainName := ""
		if name, ok := chainMap[record.ChainId]; ok {
			chainName = name
		}

		data = append(data, omnibus_vo.SmsRefundData{
			Id:           record.Id,
			ChainName:    chainName,
			StoreId:      record.StoreId,
			StoreName:    record.StoreName,
			OrderSn:      record.OrderSn,
			RefundCount:  record.RefundCount,
			RefundAmount: record.RefundAmount,
			ApplyTime:    applyTimeStr,
			RefundTime:   refundTimeStr,
			Operator:     record.Operator,
			RefundStatus: record.RefundStatus,
			Remark:       record.Remark,
			Images:       record.Images,
			CreatedTime:  record.CreatedTime.Format("2006-01-02 15:04:05"),
		})
	}

	return data, total, nil
}

// GetSmsRefundDetail 获取退款订单详情
func (s *SmsService) GetSmsRefundDetail(ctx context.Context, id int64) (*omnibus_vo.SmsRefundData, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 查询退款记录基本信息
	record := &omnibus_po.SmsRefundRecord{}
	refundRecord, err := record.GetById(session, id)
	if err != nil {
		return nil, fmt.Errorf("查询退款记录失败: %v", err)
	}

	if refundRecord == nil {
		return nil, fmt.Errorf("退款记录不存在")
	}

	// 2. 查询连锁名称
	var chainName string
	if refundRecord.ChainId > 0 {
		chainMap, err := s.GetChainNames(session, []int64{refundRecord.ChainId})
		if err != nil {
			log.Error("获取连锁名称映射失败:", err)
		} else if name, ok := chainMap[refundRecord.ChainId]; ok {
			chainName = name
		}
	}

	// 3. 查询原订单详情
	var order omnibus_po.SmsOrderInfo
	has, err := session.ID(refundRecord.OrderSn).Get(&order)
	if err != nil {
		log.Error("获取订单信息失败:", err)
	}

	// 4. 构建退款状态描述
	var refundStatusDesc string
	switch refundRecord.RefundStatus {
	case 1:
		refundStatusDesc = "待审核"
	case 2:
		refundStatusDesc = "退款成功"
	case 3:
		refundStatusDesc = "退款拒绝"
	case 4:
		refundStatusDesc = "退款取消"
	default:
		refundStatusDesc = "未知状态"
	}

	// 5. 格式化时间
	applyTimeStr := refundRecord.ApplyTime.Format("2006-01-02 15:04:05")
	refundTimeStr := ""
	if !refundRecord.RefundTime.IsZero() {
		refundTimeStr = refundRecord.RefundTime.Format("2006-01-02 15:04:05")
	}

	// 6. 计算退款单价(元/条)
	// unitPrice := 0.0
	// if refundRecord.RefundCount > 0 {
	// 	unitPrice = float64(refundRecord.RefundAmount) / float64(refundRecord.RefundCount) / 100.0
	// }

	// 7. 构建退款记录详情数据
	refundData := &omnibus_vo.SmsRefundData{
		// 基本信息
		Id:        refundRecord.Id,
		ChainId:   refundRecord.ChainId,
		ChainName: chainName,
		StoreId:   refundRecord.StoreId,
		StoreName: refundRecord.StoreName,
		OrderSn:   refundRecord.OrderSn,

		// 退款信息
		RefundCount:  refundRecord.RefundCount,
		RefundAmount: refundRecord.RefundAmount,
		// RefundUnitPrice:  fmt.Sprintf("%.2f", unitPrice),
		RefundStatus:     refundRecord.RefundStatus,
		RefundStatusDesc: refundStatusDesc,

		// 时间信息
		ApplyTime:   applyTimeStr,
		RefundTime:  refundTimeStr,
		CreatedTime: refundRecord.CreatedTime.Format("2006-01-02 15:04:05"),

		// 操作信息
		Operator:    refundRecord.Operator,
		AuditorName: refundRecord.Operator, // 使用操作人作为审核人
		AuditReason: refundRecord.AuditReason,
		AuditTime:   refundTimeStr, // 使用退款时间作为审核时间
		Remark:      refundRecord.Remark,
		Images:      refundRecord.Images,
	}

	// 8. 添加原订单信息(如果存在)
	if has {
		refundData.OrderInfo = &omnibus_vo.SmsOrderBriefInfo{
			OrderId:     order.Id,
			AmountNum:   order.AmountNum,
			PayAmount:   order.PayAmount,
			UsedCount:   order.UsedCount,
			RefundCount: order.RefundCount,
			CreateTime:  order.CreateTime.Format("2006-01-02 15:04:05"),
			OrderStatus: order.OrderStatus,
			RemainCount: order.AmountNum - order.UsedCount - order.RefundCount,
		}

		// 计算订单单价(元/条)
		if order.AmountNum > 0 {
			refundData.OrderInfo.UnitPrice = fmt.Sprintf("%.2f", float64(order.PayAmount)/float64(order.AmountNum)/100.0)
		}
	}

	// 计算退款比例
	if has && order.AmountNum > 0 {
		refundData.RefundRatio = fmt.Sprintf("%.2f%%", float64(refundRecord.RefundCount)/float64(order.AmountNum)*100)
	}

	return refundData, nil
}

// GetSmsRefundDetailByRefundSn 根据退款单号获取退款订单详情
func (s *SmsService) GetSmsRefundDetailByRefundSn(ctx context.Context, refundSn string) (*omnibus_vo.SmsRefundData, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 1. 查询退款记录基本信息
	record := &omnibus_po.SmsRefundRecord{}
	refundRecord, err := record.GetByRefundSn(session, refundSn)
	if err != nil {
		return nil, fmt.Errorf("查询退款记录失败: %v", err)
	}

	if refundRecord == nil {
		return nil, fmt.Errorf("退款记录不存在")
	}

	// 2. 查询连锁名称
	var chainName string
	if refundRecord.ChainId > 0 {
		chainMap, err := s.GetChainNames(session, []int64{refundRecord.ChainId})
		if err != nil {
			log.Error("获取连锁名称映射失败:", err)
		} else if name, ok := chainMap[refundRecord.ChainId]; ok {
			chainName = name
		}
	}

	// 3. 查询原订单详情
	var order omnibus_po.SmsOrderInfo
	has, err := session.ID(refundRecord.OrderSn).Get(&order)
	if err != nil {
		log.Error("获取订单信息失败:", err)
	}

	// 4. 构建退款状态描述
	var refundStatusDesc string
	switch refundRecord.RefundStatus {
	case 1:
		refundStatusDesc = "待审核"
	case 2:
		refundStatusDesc = "退款成功"
	case 3:
		refundStatusDesc = "退款拒绝"
	case 4:
		refundStatusDesc = "退款取消"
	default:
		refundStatusDesc = "未知状态"
	}

	// 5. 格式化时间
	applyTimeStr := refundRecord.ApplyTime.Format("2006-01-02 15:04:05")
	refundTimeStr := ""
	if !refundRecord.RefundTime.IsZero() {
		refundTimeStr = refundRecord.RefundTime.Format("2006-01-02 15:04:05")
	}

	// 6. 计算退款单价(元/条)
	unitPrice := 0.0
	if refundRecord.RefundCount > 0 {
		unitPrice = float64(refundRecord.RefundAmount) / float64(refundRecord.RefundCount) / 100.0
	}

	// 7. 构建退款记录详情数据
	refundData := &omnibus_vo.SmsRefundData{
		// 基本信息
		Id:        refundRecord.Id,
		ChainId:   refundRecord.ChainId,
		ChainName: chainName,
		StoreId:   refundRecord.StoreId,
		StoreName: refundRecord.StoreName,
		RefundSn:  refundSn,
		OrderSn:   refundRecord.OrderSn,

		// 退款信息
		RefundCount:      refundRecord.RefundCount,
		RefundAmount:     refundRecord.RefundAmount,
		RefundUnitPrice:  fmt.Sprintf("%.2f", unitPrice),
		RefundStatus:     refundRecord.RefundStatus,
		RefundStatusDesc: refundStatusDesc,

		// 时间信息
		ApplyTime:   applyTimeStr,
		RefundTime:  refundTimeStr,
		CreatedTime: refundRecord.CreatedTime.Format("2006-01-02 15:04:05"),

		// 操作信息
		Operator:    refundRecord.Operator,
		AuditorName: refundRecord.Operator, // 使用操作人作为审核人
		AuditReason: refundRecord.AuditReason,
		AuditTime:   refundTimeStr, // 使用退款时间作为审核时间
		Remark:      refundRecord.Remark,
		Images:      refundRecord.Images,
	}

	// 8. 添加原订单信息(如果存在)
	if has {
		refundData.OrderInfo = &omnibus_vo.SmsOrderBriefInfo{
			OrderId:     order.Id,
			AmountNum:   order.AmountNum,
			PayAmount:   order.PayAmount,
			UsedCount:   order.UsedCount,
			RefundCount: order.RefundCount,
			CreateTime:  order.CreateTime.Format("2006-01-02 15:04:05"),
			OrderStatus: order.OrderStatus,
			RemainCount: order.AmountNum - order.UsedCount - order.RefundCount,
		}

		// 计算订单单价(元/条)
		if order.AmountNum > 0 {
			refundData.OrderInfo.UnitPrice = fmt.Sprintf("%.2f", float64(order.PayAmount)/float64(order.AmountNum)/100.0)
		}
	}

	// 计算退款比例
	if has && order.AmountNum > 0 {
		refundData.RefundRatio = fmt.Sprintf("%.2f%%", float64(refundRecord.RefundCount)/float64(order.AmountNum)*100)
	}

	return refundData, nil
}

// AuditSmsRefund 审核短信退款
func (s *SmsService) AuditSmsRefund(req omnibus_vo.SmsRefundAuditRequest, sendSmsRequestession *xorm.Session) error {
	// 如果传入了session，使用传入的session
	var session *xorm.Session

	if sendSmsRequestession != nil {
		session = sendSmsRequestession
	} else {
		s.Begin()
		defer s.Close()

		session = s.Engine.NewSession()
		defer session.Close()
	}

	// 如果需要开始新事务
	if err := session.Begin(); err != nil {
		return err
	}

	// 获取退款记录
	record := &omnibus_po.SmsRefundRecord{}
	refundRecord, err := record.GetById(session, req.Id)
	if err != nil {
		session.Rollback()
		return err
	}

	if refundRecord == nil {
		session.Rollback()
		return fmt.Errorf("退款记录不存在")
	}

	// 更新退款状态
	if err := record.UpdateStatus(session, req.Id, req.RefundStatus, req.Operator, req.Remark, req.AuditReason); err != nil {
		session.Rollback()
		return err
	}

	// 如果退款审核通过，需要更新订单的退款信息
	if req.RefundStatus == 2 { // 退款成功
		// 查询原订单信息
		var smsOrder omnibus_po.SmsOrderInfo
		has, err := session.Where("id = ?", refundRecord.OrderSn).Get(&smsOrder)
		if err != nil {
			session.Rollback()
			return err
		}
		if !has {
			session.Rollback()
			return fmt.Errorf("原订单不存在")
		}

		// 更新订单的退款金额和退款数量
		smsOrderObj := &omnibus_po.SmsOrderInfo{}
		if err := smsOrderObj.UpdateSmsOrderRefundInfo(session, refundRecord.OrderSn, smsOrder.RefundAmount+refundRecord.RefundAmount, smsOrder.RefundCount+refundRecord.RefundCount); err != nil {
			session.Rollback()
			return err
		}
	}
	//如果是审批拒绝，加回退款数量
	if req.RefundStatus == 2 {
		redis := cache.GetRedisConn()
		// 2. 检查并扣减店铺短信余额
		countKey := omnibus_po.StoreSmsCountKey + record.StoreId
		redis.IncrBy(countKey, int64(record.RefundCount))
	}

	// 如果是当前方法开启的事务，则需要提交
	return session.Commit()
}

// CreateSmsOrder 创建短信订单
func (s *SmsService) CreateSmsOrder(ctx context.Context, req omnibus_vo.SmsOrderCreateRequest) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	var financeCodes []string
	financeCodes = append(financeCodes, req.StoreId)
	storemap, err := new(omnibus_po.Store).GetStoreInfoByFinanceCode(session, financeCodes)
	if err != nil {
		return fmt.Errorf("获取店铺信息失败")
	}

	// 构建订单对象
	smsOrder := &omnibus_po.SmsOrderInfo{
		ChainId:    cast.ToInt64(req.ChainId),
		StoreId:    req.StoreId,
		AmountNum:  req.AmountNum,
		PayAmount:  req.PayAmount,
		Operator:   req.Operator,
		OperatorId: req.OperatorId,
		PayImage:   req.PayImage,
		Remark:     req.Remark,
		StoreName:  storemap[req.StoreId].Name,
		UnitPrice:  5,
	}

	// 创建订单
	if err := smsOrder.CreateSmsOrder(session); err != nil {
		session.Rollback()
		return err
	}

	//充值成功后，添加短信条数
	redis := cache.GetRedisConn()
	countKey := omnibus_po.StoreSmsCountKey + req.StoreId
	_, err = redis.IncrBy(countKey, int64(smsOrder.AmountNum)).Result()
	if err != nil {
		session.Rollback()
		return fmt.Errorf("添加次数失败")
	}

	return session.Commit()
}

// SendMessage 发送短信并记录
func (s *SmsService) SendMessage(req omnibus_vo.SmsSendMessageRequest) (err error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	defer func() {
		if err != nil {
			log.Error("发送短信失败:", req, err.Error())
		}
	}()
	redis := cache.GetRedisConn()
	countKey := ""
	// 1. 计算短信条数
	result, err := s.CalculateSmsLength(req.TemplateCode, req.TemplateParam)
	if err != nil {
		err = fmt.Errorf("计算短信长度失败: %v", err)
		return err
	}
	record := &omnibus_po.SmsSendRecord{
		StoreId:    req.StoreId,
		Mobile:     req.Mobile,
		Content:    result.Content,
		RetryCount: result.SmsCount, // 记录实际发送条数
		SendStatus: 3,               // 待发送
		SmsType:    1,
	}
	session.Begin()
	if req.TemplateCode != omnibus_po.SmsTemplateVerifyCode {

		if req.TemplateCode != omnibus_po.SmsTemplateStoreCardPayVerify {
			// 1. 查询短信配置
			config := &omnibus_po.SmsConfig{}
			if enabled, err := config.IsSmsEnabled(session, req.StoreId, req.TemplateCode); err != nil {
				return fmt.Errorf("查询短信配置失败: %v", err)
			} else if !enabled {
				return nil
			}
		}

		// 2. 检查并扣减店铺短信余额
		countKey = omnibus_po.StoreSmsCountKey + req.StoreId
		count, err := redis.DecrBy(countKey, int64(result.SmsCount)).Result()
		if err != nil {
			err = fmt.Errorf("扣减短信余额失败: %v", err)
			return err
		}

		// 如果扣减后小于0,说明余额不足,回滚并返回错误
		if count < 0 {
			// 扣减失败,补回扣减的数量
			redis.IncrBy(countKey, int64(result.SmsCount))
			err = fmt.Errorf("短信余额不足,当前余额:%d,需要发送:%d条", count+int64(result.SmsCount), result.SmsCount)
			return err
		}

		var financeCodes []string
		financeCodes = append(financeCodes, req.StoreId)
		storemap, err := new(omnibus_po.Store).GetStoreInfoByFinanceCode(session, financeCodes)
		if err != nil {
			err = fmt.Errorf("获取店铺信息失败: %v", err)
			return err
		}
		// 3. 创建发送记录
		record = &omnibus_po.SmsSendRecord{
			StoreId:    req.StoreId,
			Mobile:     req.Mobile,
			Content:    result.Content,
			RetryCount: result.SmsCount, // 记录实际发送条数
			SendStatus: 3,               // 待发送
			StoreName:  storemap[req.StoreId].Name,
			ChainId:    storemap[req.StoreId].ChainId,
			SmsType:    1,
		}
		if req.TemplateCode == omnibus_po.SmsTemplateCouponExpire || req.TemplateCode == omnibus_po.SmsTemplateCouponFullReduction || req.TemplateCode == omnibus_po.SmsTemplateCouponDiscount {
			record.SmsType = 3
		}
		if req.TemplateCode == omnibus_po.SmsTemplateStoreCardPayVerify {
			record.SmsType = 2
		}

		if err = record.CreateSmsSendRecord(session); err != nil {
			// 创建记录失败,回滚短信余额
			session.Rollback()
			redis.IncrBy(countKey, int64(result.SmsCount))
			err = fmt.Errorf("创建发送记录失败: %v", err)
			return err
		}
	}
	// 4. 发送短信
	smsRes, err := sendAliyunSms(req.Mobile, omnibus_po.SmsSignName, req.TemplateCode, req.TemplateParam)
	if err != nil {
		session.Rollback()
		if req.TemplateCode != omnibus_po.SmsTemplateVerifyCode {
			// 发送失败,回滚短信余额
			redis.IncrBy(countKey, int64(result.SmsCount))
		}
		err = fmt.Errorf("发送短信失败: %v", err)
		return err
	}
	if *smsRes.Body.Code != "OK" {
		session.Rollback()
		if req.TemplateCode != omnibus_po.SmsTemplateVerifyCode {
			// 发送失败,回滚短信余额
			redis.IncrBy(countKey, int64(result.SmsCount))
		}
		err = fmt.Errorf("发送短信失败: %s", *smsRes.Body.Message)
		return err
	}
	if req.TemplateCode != omnibus_po.SmsTemplateVerifyCode {
		// 5. 更新发送成功的biz_id
		record.BizId = *smsRes.Body.BizId // 发送成功
		if _, err := session.ID(record.Id).Cols("biz_id").Update(record); err != nil {
			glog.Error("更新BizId失败", req)
			return fmt.Errorf("更新BizId失败: %v", err)
		}
	}

	// 6. 提交事务
	return session.Commit()
}

// CreateSmsSendRecord 创建短信发送记录
func (s *SmsService) CreateSmsSendRecord(ctx context.Context, req omnibus_vo.SmsSendRecordCreateRequest) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 构建发送记录对象
	sendRecord := &omnibus_po.SmsSendRecord{
		ChainId:    req.ChainId,
		StoreId:    req.StoreId,
		Mobile:     req.Mobile,
		SmsType:    req.SmsType,
		Content:    req.Content,
		RetryCount: req.RetryCount,
	}

	// 创建发送记录
	if err := sendRecord.CreateSmsSendRecord(session); err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

// GetSmsOrderDetail 获取短信订单详情
func (s *SmsService) GetSmsOrderDetail(ctx context.Context, orderId int64) (*omnibus_vo.SmsOrderDetailResponse, error) {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	// 查询订单信息
	var order omnibus_po.SmsOrderInfo
	has, err := session.ID(orderId).Get(&order)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("订单不存在")
	}

	// 查询连锁名称
	var chainName string
	if order.ChainId > 0 {
		chainMap, err := s.GetChainNames(session, []int64{order.ChainId})
		if err != nil {
			log.Error("获取连锁名称映射失败:", err)
		} else if name, ok := chainMap[order.ChainId]; ok {
			chainName = name
		}
	}

	// 查询店铺名称
	storeName := ""
	if order.StoreId != "" {
		var financeCodes []string
		financeCodes = append(financeCodes, order.StoreId)
		storemap, err := new(omnibus_po.Store).GetStoreInfoByFinanceCode(session, financeCodes)
		if err != nil {
			log.Error("获取店铺名称失败:", err)
		} else {
			storeName = storemap[order.StoreId].Name
		}
	}

	// 构建响应
	response := &omnibus_vo.SmsOrderDetailResponse{
		Data: omnibus_vo.SmsOrderDetailData{
			Id:           order.Id,
			ChainId:      order.ChainId,
			ChainName:    chainName,
			StoreId:      order.StoreId,
			StoreName:    storeName,
			AmountNum:    order.AmountNum,
			PayAmount:    order.PayAmount,
			OrderTime:    order.OrderTime,
			PayTime:      order.PayTime,
			PayImage:     order.PayImage,
			Operator:     order.Operator,
			OrderStatus:  order.OrderStatus,
			UsedCount:    order.UsedCount,
			RefundAmount: order.RefundAmount,
			RefundCount:  order.RefundCount,
			Remark:       order.Remark,
			CreateTime:   order.CreateTime,
			UnitPrice:    order.UnitPrice,
		},
	}

	return response, nil
}

// 创建阿里云短信发送客户端
func newAliyunSmsClient() (*dysmsapi20170525.Client, error) {
	conf := &openapi.Config{}
	SmsAccessKeyId := config.GetString("NewSmsAccessKeyId")
	SmsAccessKeySecret := config.GetString("NewSmsAccessKeySecret")
	conf.SetAccessKeyId(SmsAccessKeyId)
	conf.SetAccessKeySecret(SmsAccessKeySecret)
	return dysmsapi20170525.NewClient(conf)
}

// 发送阿里云短信
func sendAliyunSms(phoneNumbers, signName, templateCode, templateParam string) (*dysmsapi20170525.SendSmsResponse, error) {
	client, err := newAliyunSmsClient()
	if err != nil {
		log.Error("创建阿里云短信客户端失败，error: ", err.Error())
		return nil, err
	}
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
	sendSmsRequest.SetPhoneNumbers(phoneNumbers)
	sendSmsRequest.SetSignName(signName)
	sendSmsRequest.SetTemplateCode(templateCode)
	sendSmsRequest.SetTemplateParam(templateParam)
	re, err := client.SendSms(sendSmsRequest)
	if err != nil {
		log.Error("发送短信失败，error: ", err.Error())
		return nil, err
	}
	if re.Body == nil {
		log.Error("发送短信失败，respBody: ", re.Body)
		return nil, errors.New("发送短信失败 body nil")
	}
	return re, err
}

// 发送验证码短信
// 短信模板："SMS_241066101" 账号验证短信模板
func (s *SmsService) SendCode(Mobile string) error {
	templateCode := "SMS_241066101"
	redis := cache.GetRedisConn()
	defer redis.Close()
	//获取次数限制当天内10次
	checkCountKey := omnibus_po.RpMillionsVerifyCountKey + Mobile
	todayRemainSecond := utils.TodayRemainSecond()
	intSecond := time.Duration(todayRemainSecond)
	setOk := redis.SetNX(checkCountKey, 1, time.Second*intSecond)
	var count int64 = 1
	if !setOk.Val() {
		redis.Incr(checkCountKey)
		count = cast.ToInt64(redis.Get(checkCountKey).Val())
		if count >= 10 {
			return errors.New("频繁获取验证码，24小时后再尝试")
		}
	}

	// 发送验证码
	code := utils.GenValidateCode(6)
	templateParam := fmt.Sprintf(`{"code":"%s"}`, code)
	re, err := sendAliyunSms(Mobile, omnibus_po.SmsSignName, templateCode, templateParam)
	if err != nil {
		glog.Error("发送短信失败err", err)

		return errors.New("发送短信失败err:" + err.Error())
	}
	if *(re.Body.Code) != "OK" {
		mes := ""
		if *(re.Body.Code) == "isv.BUSINESS_LIMIT_CONTROL" {
			mes = "您发送的太频繁，请稍后再发！"
		} else {
			mes = "发送失败！"
			glog.Error("发送短信失败，", *(re.Body.Code), "，err：", *(re.Body.Message), "，templateParam：", templateParam)
		}
		return errors.New(mes)
	}
	glog.Infof("宠物SAAS 发送短信验证码成功:%s %v", Mobile, *(re.Body.Message))
	codeKey := omnibus_po.RpMillionsVerifyCodeKey + Mobile
	redis.Set(codeKey, code, time.Duration(20)*time.Minute)
	redis.Incr(checkCountKey) //次数加1

	return nil
}

// GetSmsTemplateContent 获取短信模板内容
func GetSmsTemplateContent(templateCode string) string {
	return omnibus_po.TemplateContentMap[templateCode]
}

// CalculateSmsLength 计算短信内容长度和条数
// 规则:
// 1. 简体中文、字母、数字、标点符号都按1个字计算
// 2. <=70字按1条计算
// 3. >70字时按67字/条分隔计算
func (s *SmsService) CalculateSmsLength(templateCode string, params string) (*SmsLengthResult, error) {
	// 获取模板内容
	template := GetSmsTemplateContent(templateCode)
	if template == "" {
		return nil, fmt.Errorf("未找到模板内容")
	}

	// 将参数转换为map
	paramsMap := make(map[string]string)
	if err := json.Unmarshal([]byte(params), &paramsMap); err != nil {
		return nil, err
	}

	// 替换模板中的变量
	content := "【" + omnibus_po.SmsSignName + "】" + template
	for key, value := range paramsMap {
		placeholder := "${" + key + "}"
		content = strings.ReplaceAll(content, placeholder, value)
	}

	// 计算总字数(按照UTF-8编码计算)
	totalLength := len([]rune(content))

	result := &SmsLengthResult{
		Content:     content,
		TotalLength: totalLength,
	}

	// 计算短信条数
	if totalLength <= 70 {
		// 70字以内按1条计算
		result.SmsCount = 1
		result.Segments = []int{totalLength}
	} else {
		// 超过70字按67字/条分隔
		segments := make([]int, 0)
		remainLength := totalLength

		for remainLength > 0 {
			if remainLength > 67 {
				segments = append(segments, 67)
				remainLength -= 67
			} else {
				segments = append(segments, remainLength)
				remainLength = 0
			}
		}

		result.SmsCount = len(segments)
		result.Segments = segments
	}

	return result, nil
}

// HandleSmsReport 处理短信回执
func (s *SmsService) HandleSmsReport(ctx context.Context, reports []omnibus_vo.SmsReportRequest) error {
	s.Begin()
	defer s.Close()

	//session := s.Engine.NewSession()
	//defer session.Close()
	//
	//if err := session.Begin(); err != nil {
	//	return err
	//}
	log.Info("收到短信回执:", reports)
	for _, report := range reports {
		session := s.Engine.NewSession()
		// 根据BizId查询发送记录
		record := new(omnibus_po.SmsSendRecord)
		has, err := session.Where("biz_id = ? and send_status=3", report.BizId).Get(record)
		if err != nil {
			session.Close()
			log.Error("查询发送记录失败:", err)
			continue
		}
		if !has {
			session.Close()
			log.Error("未找到对应的发送记录:", report.BizId)
			continue
		}
		redis := cache.GetRedisConn()
		////加锁保证同一时间只有一个任务在执行
		if !redis.SetNX(report.BizId, time.Now().Unix(), 10*time.Second).Val() {
			glog.Info(report.BizId + "task is already running")
			continue
		}
		if err := session.Begin(); err != nil {
			session.Close()
			redis.Del(report.BizId)
			return err
		}
		// 更新发送状态
		record.SendStatus = getSendStatus(report.Success)
		record.ErrMes = report.ErrMsg
		if _, err := session.ID(record.Id).Cols("send_status,err_mes").Update(record); err != nil {
			log.Error("更新发送状态失败:", err)
			session.Rollback()
			session.Close()
			redis.Del(report.BizId)
			return err
		}

		// 处理发送结果
		if report.Success {
			// 发送成功,更新订单使用次数
			if err := s.updateOrderUsedCount(session, record.StoreId, record.RetryCount); err != nil {
				log.Error("更新订单使用次数失败:", err)
				session.Rollback()
				session.Close()
				redis.Del(report.BizId)
				return err
			}
		} else {

			// 发送失败,回退短信条数

			countKey := omnibus_po.StoreSmsCountKey + record.StoreId
			log.Error(omnibus_po.StoreSmsCountKey+"回退短信条数", record.RetryCount, record.BizId)
			if _, err := redis.IncrBy(countKey, int64(record.RetryCount)).Result(); err != nil {
				session.Rollback()
				session.Close()
				redis.Del(report.BizId)
				log.Error("回退短信条数失败:", err)
				return err
			}
		}
		session.Commit()
		session.Close()
		redis.Del(report.BizId)

	}

	return nil
}

// updateOrderUsedCount 更新订单使用次数
func (s *SmsService) updateOrderUsedCount(session *xorm.Session, storeId string, usedCount int) error {
	// 查询该店铺下所有可用的短信订单(按时间先后顺序)
	var orders []omnibus_po.SmsOrderInfo
	err := session.Where("store_id = ? AND order_status = 1", storeId).
		OrderBy("order_time ASC").
		Find(&orders)
	if err != nil {
		return err
	}

	remainCount := usedCount
	for _, order := range orders {
		// 计算当前订单可用次数
		availableCount := order.AmountNum - order.UsedCount - order.RefundCount
		if availableCount <= 0 {
			continue
		}

		// 计算本次使用次数
		useCount := remainCount
		if useCount > availableCount {
			useCount = availableCount
		}

		// 更新订单使用次数
		_, err := session.ID(order.Id).
			Incr("used_count", useCount).
			Update(new(omnibus_po.SmsOrderInfo))
		if err != nil {
			return err
		}

		// 更新剩余需要处理的次数
		remainCount -= useCount
		if remainCount <= 0 {
			break
		}
	}

	// 如果还有剩余未处理的次数,记录错误日志
	if remainCount > 0 {
		log.Error("店铺短信余额不足,storeId:", storeId, "未处理次数:", remainCount)
	}

	return nil
}

// getSendStatus 根据发送结果获取状态码
func getSendStatus(success bool) int {
	if success {
		return 1 // 发送成功
	}
	return 2 // 发送失败
}

// SendVerifyCode 发送验证码
func (s *SmsService) SendVerifyCode(ctx context.Context, req omnibus_vo.SendVerifyCodeRequest) error {
	// 生成6位随机验证码
	code := utils.GenValidateCode(6)

	// 根据验证码类型选择模板
	var templateCode string
	switch req.Type {
	case 1: // 登录验证码
		templateCode = omnibus_po.SmsTemplateVerifyCode
	case 2: // 支付验证码
		templateCode = omnibus_po.SmsTemplateStoreCardPayVerify
	default:
		return fmt.Errorf("不支持的验证码类型")
	}

	// 构造短信参数
	params := map[string]string{
		"code": code,
	}
	paramsJson, _ := json.Marshal(params)

	// 发送短信
	if req.Type == 2 { // 支付验证码需要扣减店铺短信条数
		if err := s.SendMessage(omnibus_vo.SmsSendMessageRequest{
			StoreId:       req.StoreId,
			Mobile:        req.Mobile,
			TemplateCode:  templateCode,
			TemplateParam: string(paramsJson),
		}); err != nil {
			return err
		}
	} else { // 登录验证码不扣减店铺短信条数
		if _, err := sendAliyunSms(req.Mobile, omnibus_po.SmsSignName, templateCode, string(paramsJson)); err != nil {
			return err
		}
	}

	// 将验证码存入Redis
	redis := cache.GetRedisConn()
	key := fmt.Sprintf("%s%s:%d", omnibus_po.RpMillionsVerifyCodeKey, req.Mobile, req.Type)
	if err := redis.Set(key, code, time.Minute*5).Err(); err != nil {
		return err
	}

	return nil
}

// VerifyCode 验证验证码
func (s *SmsService) VerifyCode(ctx context.Context, req omnibus_vo.VerifyCodeRequest) error {
	redis := cache.GetRedisConn()
	key := fmt.Sprintf("%s%s:%d", omnibus_po.RpMillionsVerifyCodeKey, req.Mobile, req.Type)

	isOpen := cast.ToInt32(config.GetString("is_open_master_key"))
	if req.Code == "688123" && isOpen == 1 {
		return nil
	}
	// 获取存储的验证码
	code, err := redis.Get(key).Result()
	if err != nil {
		return fmt.Errorf("验证码已过期")
	}

	// 验证码比对
	if code != req.Code {
		return fmt.Errorf("验证码错误")
	}

	// 验证通过后删除验证码
	redis.Del(key)

	return nil
}

// ApplySmsRefund 申请短信订单退款
func (s *SmsService) ApplySmsRefund(ctx context.Context, req omnibus_vo.SmsRefundApplyRequest) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 1. 查询订单信息
	order := new(omnibus_po.SmsOrderInfo)
	has, err := session.ID(req.OrderId).Get(order)
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if !has {
		return fmt.Errorf("订单不存在")
	}

	// 2. 校验退款条件
	if order.OrderStatus != 1 {
		return fmt.Errorf("订单状态不允许退款")
	}
	redis := cache.GetRedisConn()
	// 2. 检查并扣减店铺短信余额
	countKey := omnibus_po.StoreSmsCountKey + order.StoreId
	count, err := redis.DecrBy(countKey, int64(req.RefundCount)).Result()
	if err != nil {
		return fmt.Errorf("扣减短信余额失败: %v", err)
	}

	// 如果扣减后小于0,说明余额不足,回滚并返回错误
	if count < 0 {
		// 扣减失败,补回扣减的数量
		redis.IncrBy(countKey, int64(req.RefundCount))
		return fmt.Errorf("短信余额不足,当前余额:%d,需要退款:%d条", count+int64(req.RefundCount), req.RefundCount)
	}

	availableCount := order.AmountNum - order.UsedCount - order.RefundCount
	if availableCount < req.RefundCount {
		// 扣减失败,补回扣减的数量
		redis.IncrBy(countKey, int64(req.RefundCount))
		return fmt.Errorf("可退款条数不足,剩余可退:%d,申请退款:%d", availableCount, req.RefundCount)
	}

	// 4. 创建退款记录
	record := &omnibus_po.SmsRefundRecord{
		ChainId:      order.ChainId,
		StoreId:      order.StoreId,
		StoreName:    order.StoreName,
		OrderSn:      order.Id, // 使用订单ID作为订单号
		RefundCount:  req.RefundCount,
		RefundAmount: req.RefundAmount,
		ApplyTime:    time.Now(),
		RefundStatus: 1, // 待审核
		Remark:       req.Remark,
		Images:       req.Images, // 直接使用逗号分隔的图片字符串
		Operator:     req.Operator,
		//AuditReason:  "自动审核通过",
	}

	if err := record.Create(session); err != nil {
		// 扣减失败,补回扣减的数量
		redis.IncrBy(countKey, int64(req.RefundCount))
		return fmt.Errorf("创建退款记录失败: %v", err)
	}
	auditReq := omnibus_vo.SmsRefundAuditRequest{
		Id:           int64(record.Id),
		RefundStatus: 2, // 自动审核通过
		Operator:     req.Operator,
		Remark:       req.Remark,
		AuditReason:  "自动审核",
	}

	//调用审核方法
	if err := s.AuditSmsRefund(auditReq, session); err != nil {
		session.Rollback()
		// 扣减失败,补回扣减的数量
		redis.IncrBy(countKey, int64(req.RefundCount))
		return fmt.Errorf("审核退款失败: %v", err)
	}
	session.Commit()
	return nil
}

// ReadAllMessages 一键已读所有消息
func (s *SmsService) ReadAllMessages(ctx context.Context, memberMain string) error {
	s.Begin()
	defer s.Close()

	session := s.Engine.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 更新所有未读消息为已读状态
	_, err := session.Table("datacenter.message_relationship").
		Where("member_main = ? AND is_read = 1", memberMain).
		Update(map[string]interface{}{
			"is_read": 2, // 2表示已读
		})

	if err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

func (s *SmsService) TaskDo(ctx context.Context, req omnibus_vo.TaskDoRequest) error {

	switch req.Type {
	case 1:
		s.NotifyExpireCoupons()
	case 2:
		s.NotifyExpireCards(1)
	case 3:
		s.NotifyExpireCards(2)

	}
	return nil
}
