package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	_ "github.com/go-sql-driver/mysql"
	"testing"
	"xorm.io/xorm"
)

func TestStoreProductService_GetChannelInfoByStoreId(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		financeCode string
		channelId   int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
		want1  int
		want2  bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				financeCode: "530219708465609002",
				channelId:   2,
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &StoreProductService{
				BaseService: tt.fields.BaseService,
			}
			s.Begin()
			defer s.Close()
			got, got1, got2 := GetChannelInfoByStoreId(tt.fields.BaseService.Engine, tt.args.financeCode, tt.args.channelId)
			if got != tt.want {
				t.Errorf("GetChannelInfoByStoreId() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetChannelInfoByStoreId() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("GetChannelInfoByStoreId() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}

func TestGetChannelInfoByStoreId(t *testing.T) {
	type args struct {
		db          *xorm.Engine
		financeCode string
		channelId   int
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 int
		want2 bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				financeCode: "530219708465609002",
				channelId:   2,
			},
			want:  "test",
			want1: 2,
			want2: true,
		},
	}

	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := common.BaseService{}
			s.Begin()
			defer s.Close()
			got, got1, got2 := GetChannelInfoByStoreId(s.Engine, tt.args.financeCode, tt.args.channelId)
			if got != tt.want {
				t.Errorf("GetChannelInfoByStoreId() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetChannelInfoByStoreId() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("GetChannelInfoByStoreId() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}
