package aliexpress

import (
	"testing"
)

func TestGet(t *testing.T) {
	type args struct {
		code     string
		shipCode string
		phone    string
	}
	tests := []struct {
		name    string
		args    args
		wantRes *Response
		wantErr bool
	}{
		{
			args: args{
				code:     "DEPPON",
				shipCode: "DPK366023635970",
				phone:    "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRes, err := Info(tt.args.code, tt.args.shipCode, tt.args.phone)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && gotRes.Status != "0" {
				t.Errorf("Get() gotRes = %v, want %v", gotRes, tt.wantRes)
			}
		})
	}
}
