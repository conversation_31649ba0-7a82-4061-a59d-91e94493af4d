package external_po

import (
	"eShop/view-model/product-vo"

	"github.com/shopspring/decimal"
)

type PromotionTimeDiscount struct {
	Id                int    `xorm:"pk autoincr"`
	Uuid              string `xorm:"UUID not null default '''' comment('uuid') CHAR(36)"`
	UserType          int    `xorm:"UserType"`
	DisountType       int    `xorm:"DisountType"`
	DiscountValue     int    `xorm:"DiscountValue"`
	LimitCountByOrder int    `xorm:"LimitCountByOrder"`
	LimitCountByStock int    `xorm:"LimitCountByStock"`
}

func (model *PromotionTimeDiscount) TableName() string {
	return "datacenter.promotion_timediscount"
}

func (model *PromotionTimeDiscount) ToDto(promotionId int) *product_vo.PromotionTimeDiscountDto {
	var dto = new(product_vo.PromotionTimeDiscountDto)
	dto.PromotionId = int32(promotionId)
	dto.UserType = int32(model.UserType)
	dto.DisountType = int32(model.DisountType)
	discountValueDeci := decimal.NewFromInt(int64(model.DiscountValue)).Div(decimal.NewFromInt(100))
	dto.DiscountValue, _ = discountValueDeci.Float64()
	dto.LimitCountByOrder = int32(model.LimitCountByOrder)
	dto.LimitCountByStock = int32(model.LimitCountByStock)
	return dto
}

func (model *PromotionTimeDiscount) ToPromotionCalcDto(promotionId int) *product_vo.PromotionCalcDto {
	var calc = new(product_vo.PromotionCalcDto)
	calc.PromotionType = 2
	calc.PromotionTitle = "限时折扣"
	calc.PromotionId = int32(promotionId)
	return calc
}
