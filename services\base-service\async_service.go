package base_service

import (
	po "eShop/domain/omnibus-po"
	"eShop/infra/cache"
	enum "eShop/infra/enum"
	"eShop/infra/log"
	mq "eShop/infra/mq"
	"eShop/infra/utils"
	"eShop/services/common"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/services/product-service/async"
	viewmode "eShop/view-model"
	vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"github.com/streadway/amqp"
)

type AsyncService struct {
	common.BaseService
}
type SyncInterface interface {
	OperationFunc(parJson string, org_id int) (*viewmode.ImportResult, error)
}

func NewSyncMode(taskContent int) (oet SyncInterface) {
	switch taskContent {
	//添加商品
	case enum.SyncProductAddTaskContent:
		oet = &async.ProductAddService{}
	case enum.SyncProductEditTaskContent:
		oet = &async.ProductEditService{}
	case enum.SyncProductDelTaskContent:
		oet = &async.ProductDelService{}
	case enum.SyncCategoryTaskContent:
		oet = &async.CategoryAsync{}
	case enum.SyncXkucunCodeDateTaskContent:
		oet = &async.Xkucun{}
	case enum.SyncPetArtworkTaskContent:
		oet = &async.PetArtworkService{}
	default:
		log.Info("异步任务类型有误")
	}

	return
}

func (h AsyncService) TaskInit() {

	for _, x := range enum.InitQueue {
		go func(TaskContent int) {
			for {
				mq.Consume(enum.AsyncQueue+cast.ToString(TaskContent), enum.AsyncQueue+cast.ToString(TaskContent), "eshop", h.EshopSyncTask)
				time.Sleep(10 * time.Second)
			}
		}(x)
	}

}

func (h AsyncService) InsertMq() {

	lock := "async:task:add:lock"
	fmt.Println("InsertMq")
	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lock, time.Minute*10)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", "InsertMq", lock)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), lock)

	h.Begin()
	defer h.Close()

	session := h.Engine
	var taskList []vo.TaskListAsync
	// 这里排除贵族裂变活动-AI生成贵族宠物图任务
	err := session.SQL("select * from eshop.task_list_async where is_push=0  and do_count<=5  order by create_time asc limit 10").Find(&taskList)
	if err != nil {
		log.Error("获取异步任务报错" + err.Error())
		return
	}
	for _, x := range taskList {
		bt, _ := json.Marshal(x)
		if ok := mq.PublishRabbitMQ(enum.AsyncQueue+cast.ToString(x.TaskContent), string(bt), "eshop"); !ok {
			log.Error("mq推送失败，", err, "，", string(bt))
			// 失败后更新任务 task_status = 4
			session.Exec("update eshop.task_list_async set task_status=4,err_mes=?,do_count=do_count+1 where id=", "mq推送失败："+err.Error(), x.Id)
		}
		//丢到MQ成功就改成进行中
		_, err := session.Exec("update eshop.task_list_async set is_push=1,err_mes='' where id= ?", x.Id)
		fmt.Println("InsertMq()", err)
	}
}

func (h AsyncService) EshopSyncTask(d amqp.Delivery) (response string, err error) {

	logPrefix := "处理异步任务===="
	log.Info(logPrefix, "入参：", string(d.Body))
	h.Begin()
	defer h.Close()
	model := new(po.TaskListAsync)
	if err := json.Unmarshal(d.Body, model); err != nil {
		log.Error(err)
		return err.Error(), nil
	}
	logPrefix = fmt.Sprintf("%s-任务ID:%d,%s,%s", logPrefix, model.Id, model.CreateId, model.CreateName)
	log.Info(logPrefix, "开始处理异步任务")

	//文件下载链接
	var url string
	var success_num int
	var fail_num int
	var err_mes string
	var task_detail string
	//var petArtWorkData marketing_po.PetArtwork
	// //贵族裂变活动 - 异步任务生成贵族宠物图时，需要获取作品数据
	// if model.TaskContent == enum.SyncPetArtworkTaskContent {
	// 	if err = json.Unmarshal([]byte(model.OperationFileUrl), &petArtWorkData); err != nil {
	// 		log.Error(logPrefix, "解析参数失败", err)
	// 		return err.Error(), nil
	// 	}
	// }

	defer func() {
		log.Info(logPrefix, "异步任务结束")
		if err == nil {
			model.TaskStatus = enum.TaskStatusFinished
		} else {
			model.TaskStatus = enum.TaskStatusing
		}
		model.ResulteFileUrl = url
		model.SuccessNum = success_num
		model.FailNum = fail_num
		model.TaskDetail = task_detail
		model.ErrMes = err_mes
		//查询一下任务的次数
		modelitem := new(po.TaskListAsync)
		_, err1 := h.Engine.Where("id=?", model.Id).Get(modelitem)
		if err1 != nil {
			log.Error(logPrefix, model.Id, ", 查询异步任务出错, ", err1.Error())
		}
		model.DoCount = modelitem.DoCount

		if err != nil && model.DoCount == 5 {
			model.TaskStatus = enum.TaskStatusFailed
			// //贵族裂变活动 - 如果失败，则更新作品AI图生成状态
			// if model.TaskContent == enum.SyncPetArtworkTaskContent {
			// 	_, err = h.Engine.Table("eshop.pet_artwork").Where("id=?", petArtWorkData.Id).Cols("deal_status").Update(map[string]interface{}{
			// 		"deal_status": marketing_po.DealStatus_Failed,
			// 	})
			// 	if err != nil {
			// 		log.Error(logPrefix, "作品id:", petArtWorkData.Id, "更新作品AI图生成状态失败, ", err)
			// 	}
			// }
		} else {
			model.DoCount = modelitem.DoCount + 1
		}

		h.UpdateTask(model)
		//如果还是进行中，说明错误次数没到5次，继续重试
		if model.TaskStatus == enum.TaskStatusing {
			d.Nack(false, true)
		} else { //成功，或者失败超过5次了。就直接确认消息了
			d.Ack(false)
		}
	}()

	//修改任务状态
	h.StartTask(model)
	oet := NewSyncMode(model.TaskContent)

	out, err := h.OperationSync(model.OperationFileUrl, model.OrgId, oet.OperationFunc)
	log.Infof("%s-异步任务 内容执行完成,结果为:%s,err:%v", logPrefix, utils.JsonEncode(out), err)
	if err != nil {
		log.Error(logPrefix, "异步任务出错, ", err)
		err = errors.New("异步任务执行出错：, " + err.Error())
		task_detail = "异步任务执行出错" + err.Error()
		err_mes = err.Error()
		return "", err
	}
	if out.Code != 200 {
		log.Error(logPrefix, "异步任务出错, ", out.Message)
		err = errors.New("异步任务执行出错：, " + out.Message)
		task_detail = "异步任务执行出错" + out.Message
		err_mes = out.Message
		return "", err
	}

	url = out.QiniuUrl
	success_num = int(out.SuccessNum)
	fail_num = int(out.FailNum)
	if out.Message == "" {
		task_detail = "操作成功:" + cast.ToString(success_num) + " 操作失败:" + cast.ToString(fail_num)
	} else {
		task_detail = out.Message
	}

	return "success", nil
}

// 更新任务状态为 进行中
func (h AsyncService) StartTask(task *po.TaskListAsync) {
	log.Info("任务详情", task)
	h.Begin()
	defer h.Close()

	db := h.Engine
	_, err := db.Exec("update task_list_async set task_status=2 where id =?", task.Id)
	if err != nil {
		log.Info("开始任务状态报错", task, err.Error())
	}
}

func (h AsyncService) UpdateTask(task *po.TaskListAsync) {
	log.Info("任务详情", task)
	h.Begin()
	defer h.Close()

	db := h.Engine
	_, err := db.Where("id =?", task.Id).Update(task)
	if err != nil {
		log.Info("更新任务状态报错", task, err.Error())
	}
}

type OperationSyncFunc func(parJson string, org_id int) (*viewmode.ImportResult, error)

func (h AsyncService) OperationSync(parJson string, org_id int, operation OperationSyncFunc) (*viewmode.ImportResult, error) {
	out := new(viewmode.ImportResult)
	out.Code = 400
	out, err := operation(parJson, org_id)
	if err != nil {
		out.Message = err.Error()
		return out, nil
	}
	if out.Code != 200 {
		return out, nil
	}
	out.Code = 200
	return out, nil
}
