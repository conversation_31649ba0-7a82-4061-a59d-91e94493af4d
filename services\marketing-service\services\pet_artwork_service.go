package services

import (
	"context"
	po "eShop/domain/marketing-po"
	omnibus_po2 "eShop/domain/omnibus-po"
	"eShop/infra/cache"
	"eShop/infra/converter"
	"eShop/infra/enum"
	"eShop/infra/errors"
	"eShop/infra/log"
	"eShop/infra/transaction"
	"eShop/infra/utils"
	"eShop/services/common"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/marketing-vo"
	common_errors "errors"
	"fmt"
	"io"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/coze-dev/coze-go"
	"github.com/spf13/cast"
	"xorm.io/xorm"
)

// petArtworkService 作品服务实现
type PetArtworkService struct {
	common.BaseService
	tm transaction.TransactionManager
}

func NewPetArtworkService() PetArtworkService {
	return PetArtworkService{
		tm: transaction.NewTransactionManager(),
	}
}

// 生成极宠家 作品编号
func GeneratePetArtworkCode() string {
	logPrefix := "生成极宠家 作品编号===="
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	petArtworkCodeSli := redisConn.Get(string(cache_source.EShop), "petArtworkCode")
	log.Info(logPrefix, "petArtworkCodeSli:", petArtworkCodeSli)
	if len(petArtworkCodeSli) == 0 {
		petArtworkCode := fmt.Sprintf("GZ%06d", 1)
		redisConn.Save(string(cache_source.EShop), "petArtworkCode", petArtworkCode, 0)
		return petArtworkCode
	} else {
		petArtworkCode := petArtworkCodeSli[0].(string)
		tmp := strings.TrimPrefix(petArtworkCode, "GZ")
		petArtworkCodeInt, err := strconv.ParseInt(tmp, 10, 64) // 十进制，64位整数
		if err != nil {
			log.Error(logPrefix, "petArtworkCodeInt转换失败，err=", err.Error())
			return ""
		}
		petArtworkCode = fmt.Sprintf("GZ%06d", petArtworkCodeInt+1)
		redisConn.Save(string(cache_source.EShop), "petArtworkCode", petArtworkCode, 0)
		log.Info(logPrefix, "petArtworkCode:", petArtworkCode)
		return petArtworkCode
	}
}

// todo 获取随机3张参考图片
func GetRandom3RefImg() (string, string, string) {
	// 设置随机种子确保每次运行结果不同
	rand.Seed(time.Now().UnixNano())

	RefImgSli := []string{
		"https://file.vetscloud.com/646e586916c70b91afb180f10bb2ffa0.png",
		"https://file.vetscloud.com/1c5f928886b0242f43abc54e5933ae13.png",
		"https://file.vetscloud.com/269f41c446020bcd2e5b9ee624edbe1c.png",
		"https://file.vetscloud.com/e797da99de0d84451555abcb069c21fb.png",
		"https://file.vetscloud.com/ac9274478861ff3c795ae5761b8c11b7.png",
		"https://file.vetscloud.com/ac9274478861ff3c795ae5761b8c11b7.png",
		"https://file.vetscloud.com/1b6ab0157910e001783ed14eb8ea6150.png",
		"https://file.vetscloud.com/e7b62d79ab2d07fcf661811878d3959d.png",
		"https://file.vetscloud.com/ec7da5ccdfbb6211d25c8130421fa031.png",
		"https://file.vetscloud.com/27e05202ade5d97aac7c2ea5d327d939.png",
		"https://file.vetscloud.com/2fc2ac81696f179eedf09b7d776e14c2.png",
		"https://file.vetscloud.com/2fc2ac81696f179eedf09b7d776e14c2.png",
	}

	// 随机打乱切片
	rand.Shuffle(len(RefImgSli), func(i, j int) {
		RefImgSli[i], RefImgSli[j] = RefImgSli[j], RefImgSli[i]
	})

	// 取前3个元素
	selected := RefImgSli[:3]
	return selected[0], selected[1], selected[2]
}

// Create 创建作品
func (s PetArtworkService) Create(cmd vo.PetArtworkCreateReq) (out *po.PetArtwork, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("贵族裂变活动-创作作品,scrmUserId:%s====", cmd.ScrmUserId)

	petrtworkList, hasPk, err := new(po.PetArtwork).FindByUserId(session, cmd.ScrmUserId)
	if err != nil {
		log.Error(logPrefix, "查询作品失败，err=", err.Error())
		err = errors.New("服务内部异常")
		return
	}
	if hasPk {
		err = errors.New("您已有参与PK的作品，不能再次创建作品")
		return
	}
	if len(petrtworkList) == 10 {
		err = errors.New("生成次数已达上限：10次，无法再生成新的图片")
		return
	}

	refImg1, refImg2, refImg3 := GetRandom3RefImg() //获取随机3张参考图片
	out = new(po.PetArtwork)

	out, err = s.Chat(vo.GuizuPetCozeChatReq{
		UserInfoId: cmd.ScrmUserId,
		BotType:    common.GuizuPetBloodCozeBotId,
		PetName:    cmd.PetName,
		PetPhoto:   cmd.PetPhoto,
		PetGender:  cmd.PetGender,
		RefImg1:    refImg1,
		RefImg2:    refImg2,
		RefImg3:    refImg3,
	})
	if err != nil {
		log.Error(logPrefix, "获取coze对话结果失败，err=", err.Error())
		return
	}

	petrtwork := new(po.PetArtwork)
	petrtwork.ScrmUserId = cmd.ScrmUserId
	petrtwork.WorkCode = GeneratePetArtworkCode() //生成作品编号
	petrtwork.PetName = cmd.PetName
	petrtwork.PetGender = cmd.PetGender
	petrtwork.PetPhoto = cmd.PetPhoto
	petrtwork.RefImg1 = refImg1
	petrtwork.RefImg2 = refImg2
	petrtwork.RefImg3 = refImg3
	petrtwork.NoblePetImg = out.NoblePetImg
	petrtwork.PetBreed = out.PetBreed
	petrtwork.BloodOrigin = out.BloodOrigin
	petrtwork.BloodRatio = out.BloodRatio
	petrtwork.GeneAttribute = out.GeneAttribute
	petrtwork.NobleTitleAward = out.NobleTitleAward
	petrtwork.NobleDescription = out.NobleDescription
	petrtwork.NobleScore = out.NobleScore
	petrtwork.NobleScoreDescription = out.NobleScoreDescription

	// 创建作品
	err = new(po.PetArtwork).Create(session, petrtwork)
	if err != nil {
		log.Error(logPrefix, "创建作品失败，err=", err.Error())
		return
	}
	go func() {

		var e error
		//第一次创建作品，发放5元无门槛优惠券
		if len(petrtworkList) == 0 {
			// prize := po.PetPrize{
			// 	UserId:        cmd.ScrmUserId,
			// 	NickName:      cmd.ScrmUserName,
			// 	Mobile:        utils.AddStar(cmd.Mobile),
			// 	EnMobile:      utils.MobileEncrypt(cmd.Mobile),
			// 	PrizeType:     3,
			// 	WorkCode:      petrtwork.WorkCode,
			// 	PrizeCount:    0,
			// 	ReceiveStatus: 1,
			// 	PrizeContent:  "5元无门槛券",
			// 	CreateTime:    time.Now(),
			// 	UpdateTime:    time.Now(),
			// }

			// _, e = session.Table(po.PetPrize{}.TableName()).Insert(&prize)
			// if e != nil {
			// 	log.Error(logPrefix, "插入我的奖品-5元无门槛优惠券失败,err=", e.Error())
			// } else {
				petActivityService := PetActivityService{}
				if _, e = petActivityService.ReceiveVoucherByType(cmd.ScrmUserId, 5, 3); e != nil {
					log.Error(logPrefix, "发放5元无门槛优惠券失败,err=", e.Error())
				}
			// }
		}

		// 插入异步任务
		asyncTask := omnibus_po2.TaskListAsync{
			CreateId:         cmd.ScrmUserId,
			CreateName:       cmd.ScrmUserName,
			TaskContent:      enum.SyncPetArtworkTaskContent,
			OperationFileUrl: utils.JsonEncode(petrtwork),
			KeyStr:           "",
			ExtendedData:     enum.SyncTaskContentMap[enum.SyncPetArtworkTaskContent],
		}

		if e = asyncTask.CreateAsyncTask2(session); e != nil {
			log.Error(logPrefix, "创建贵族裂变活动 - AI生成贵族宠物图任务(极宠家)异步任务失败，err=", e.Error())
		}

	}()
	out.Id = petrtwork.Id
	return out, nil

}

func (s PetArtworkService) Chat(in vo.GuizuPetCozeChatReq) (petrtwork *po.PetArtwork, err error) {
	logPrefix := fmt.Sprintf("贵族裂变活动-coze对话====%s,%s", in.UserInfoId, in.BotType)
	log.Info(logPrefix, "入参", utils.JsonEncode(in))
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()
	cozeService := common.NewCozeService()
	cozeCli, _ := cozeService.NewCozeAPI(in.UserInfoId, common.GuizuPetCozeType, in.BotType)
	stream := true
	autoSaveHistory := false
	message := make([]*coze.Message, 0)
	content := make([]*coze.MessageObjectString, 0)
	if in.BotType == cast.ToString(common.GuizuPetBloodCozeBotId) {
		if in.PetName == "" {
			err = errors.New("宠物名称不能为空")
			return
		}
		if in.PetPhoto == "" {
			err = errors.New("宠物照片不能为空")
			return
		}
		petGender := "未知"
		if in.PetGender == 1 {
			petGender = "公"
		} else if in.PetGender == 2 {
			petGender = "母"
		}

		content = append(content, coze.NewTextMessageObject(fmt.Sprintf("宠物名称：%s", in.PetName)))
		content = append(content, coze.NewTextMessageObject(fmt.Sprintf("宠物性别：%s", petGender)))
		content = append(content, coze.NewImageMessageObjectByURL(in.PetPhoto))
		message = append(message, coze.BuildUserQuestionObjects(content, nil))

	} else if in.BotType == common.GuizuPetImageCozeBotId {
		if in.PetPhoto == "" || in.RefImg1 == "" || in.RefImg2 == "" || in.RefImg3 == "" {
			err = errors.New("宠物照片、参考图不能为空")
			return
		}
		content = append(content, coze.NewImageMessageObjectByURL(in.PetPhoto))
		content = append(content, coze.NewImageMessageObjectByURL(in.RefImg1))
		content = append(content, coze.NewImageMessageObjectByURL(in.RefImg2))
		content = append(content, coze.NewImageMessageObjectByURL(in.RefImg3))
		message = append(message, coze.BuildUserQuestionObjects(content, nil))

	} else {
		err = errors.New("入参错误")
		return
	}

	//  贵族宠物图，宠物品种，血统起源，血统占比，基因属性，贵族气息描述，荣获贵族封号，贵族气质评分，气质评分的解释
	petrtwork = new(po.PetArtwork)

	req := &coze.CreateChatsReq{
		ConversationID:  "",
		BotID:           cozeService.GetBotId(in.BotType),
		UserID:          in.UserInfoId,
		Stream:          &stream,
		AutoSaveHistory: &autoSaveHistory,
		Messages:        message,
	}
	chatMessage := new(coze.Message)
	if in.BotType == cast.ToString(common.GuizuPetBloodCozeBotId) {
		resp, e := cozeCli.Chat.Stream(ctx, req)
		if e != nil {
			log.Error(logPrefix, "对话失败,错误信息为", e.Error())
			return
		}
		log.Infof("%s|发起coze对话|请求参数为:%s|响应信息为:%s", logPrefix, utils.JsonEncode(req), utils.JsonEncode(resp))
		defer resp.Close()

		for {
			event, err := resp.Recv()
			if common_errors.Is(err, io.EOF) {
				log.Error(logPrefix, "Stream finished")
				fmt.Println("Stream finished")
				break
			}
			if err != nil {
				log.Error(logPrefix, "接收数据发生错误:", err.Error())
				fmt.Println("接收数据发生错误:", err.Error())
				break
			}

			fmt.Printf("event:%s\n", utils.JsonEncode(event))
			log.Infof("%s|event:%s|%s\n", logPrefix, event.Event, utils.JsonEncode(event))
			if event.Event == coze.ChatEventConversationMessageCompleted { //完整对话消息
				if event.Message.Type == coze.MessageTypeAnswer {
					//消息返回的内容
					chatMessage = event.Message
				}
			}

		}
	}

	// 贵族裂变活动 - coze智能体 宠物贵族血统鉴定师 解析结果
	if in.BotType == cast.ToString(common.GuizuPetBloodCozeBotId) {
		if chatMessage.ContentType != coze.MessageContentTypeText {
			err = errors.New("coze智能体返回消息类型错误")
			return
		}

		petrtwork.PetBreed = utils.ExtractValue(chatMessage.Content, "宠物品种：", "\n")
		if petrtwork.PetBreed == "" {
			petrtwork.PetBreed = utils.ExtractBetween(chatMessage.Content, "宠物品种\n", "\n")
		}
		petrtwork.BloodOrigin = utils.ExtractValue(chatMessage.Content, "血统起源：", "\n")
		petrtwork.BloodRatio = utils.ExtractValue(chatMessage.Content, "血统占比：", "\n")
		petrtwork.GeneAttribute = utils.ExtractValue(chatMessage.Content, "基因属性：", "\n")
		petrtwork.NobleTitleAward = utils.ExtractBetween(chatMessage.Content, "贵族封号：[", "]")
		petrtwork.NobleDescription = utils.ExtractBetween(chatMessage.Content, "贵族气息描述\n", "\n")
		petrtwork.NobleScore = cast.ToInt(utils.ExtractValue(chatMessage.Content, "贵族气质评分：", "\n"))
		petrtwork.NobleScoreDescription = utils.ExtractValue(chatMessage.Content, "评分理由：", "\n")
	} else if in.BotType == common.GuizuPetImageCozeBotId {
		// // 查找https开头的部分
		// start := strings.Index(chatMessage.Content, "https")
		// if start == -1 {
		// 	err = errors.New("未找到图片地址")
		// 	return
		// }
		// var ossUrl string
		// // 提取https开头的完整URL todo 将coze图片地址保存到本地
		// petrtwork.NoblePetImg = strings.TrimSpace(chatMessage.Content[start:])
		// ossUrl, err = utils.UpImageToOss(petrtwork.NoblePetImg)
		// if err != nil {
		// 	log.Error(logPrefix, "上传图片到oss失败,err=", err.Error())
		// } else {
		// 	petrtwork.NoblePetImg = ossUrl
		// }

		petrtwork.NoblePetImg = "https://s.coze.cn/t/okh5bCOg8PM/"
		// petrtwork.PetBreed = "拉布拉多"
		// petrtwork.BloodOrigin = "美国加州贵族猫裔"
		// petrtwork.BloodRatio = "检测到「95% 美国加州贵族猫裔」血脉"
		// petrtwork.GeneAttribute = "亲和力 + 80%，优雅度 + 75%"
		// petrtwork.NobleTitleAward = "星辰柔语・月纱领主"
		// petrtwork.NobleDescription = "这只布偶猫浑身散发着优雅高贵的气息，蓬松柔软的毛发如云朵般洁白，脸部的灰色花纹好似精心勾勒的妆容，衬托出它的与众不同。湛蓝如宝石的双眼澄澈明净，仰头凝视的姿态尽显灵动与温婉，仿佛一位不食人间烟火的贵族淑女，静静地散发着温柔而典雅的魅力，每一处细节都彰显着布偶猫特有的甜美与高贵，让人忍不住为之倾倒。"
		// petrtwork.NobleScore = cast.ToInt(940)
		// petrtwork.NobleScoreDescription = "宠物神情（350）：蓝眼清澈灵动，表情温柔恬静，尽显优雅神态；宠物身姿（250）：坐姿挺拔端正，体态优美，展现出良好的优雅姿态；毛发光泽（200）：毛发蓬松顺滑，富有光泽，质感极佳；宠物名字（90）：“小布是只喵”简洁可爱，富有特色；衣着装扮（50）：无复杂配饰，保持自然状态，尽显猫咪本身的纯净之美。"
	} else {
		err = errors.New("入参错误")
		return
	}

	return
}

func (s PetArtworkService) ArtworkTest(in vo.PetArtworkTestReq) (err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	logPrefix := fmt.Sprintf("====贵族裂变活动-AI生成贵族宠物图任务,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	var rdb = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	// 5. 同步工具
	var (
		wg sync.WaitGroup
		//results = make(chan string, 100) // 按顺序收集结果
		//resultMutex sync.Mutex
	)

	// 模拟请求
	for i := 0; i < 30; i++ {
		wg.Add(1)
		go func(requestID int) {
			defer wg.Done()

			// 使用原子操作获取下一个智能体
			var agentID string = "7526851960020992046"
			// agentID, err = utils.GetNextAgentAtomic(rdb)

			// if err != nil {
			// 	log.Errorf("Request %d failed to get agent: %v", requestID, err)
			// 	return
			// }
			// 安全地收集结果
			// resultMutex.Lock()
			// results <- fmt.Sprintf("请求 %d 分配到智能体: %s", requestID, agentID)
			// resultMutex.Unlock()
			if err := utils.CallCozeWithToken(rdb, requestID, agentID); err != nil {

			}
		}(i)
	}
	wg.Wait()
	//close(results)

	// 8. 按顺序打印结果
	// fmt.Println("==== 智能体分配结果 ====")
	// for result := range results {
	// 	fmt.Println(result)
	// }
	time.Sleep(4 * time.Minute)
	return

}

// Update 更新作品
func (s PetArtworkService) Update(session *xorm.Session, cmd vo.PetArtworkUpdateReq) error {
	return s.tm.Required(session, func(tx *xorm.Session) error {
		artwork, err := new(po.PetArtwork).GetByID(tx, cmd.Id)
		if err != nil {
			return err
		}
		if artwork.Id == 0 {
			return errors.NewBadRequest("作品不存在")
		}

		updEntity, err := converter.DeepConvert[po.PetArtwork](cmd)
		if err != nil {
			return err
		}
		updEntity.Id = artwork.Id
		return new(po.PetArtwork).Update(tx, updEntity)
	})
}

// Page 作品分页查询
func (s PetArtworkService) Page(session *xorm.Session, query vo.PetArtworkPageReq) ([]vo.PetArtworkResp, int64, error) {
	var resps []vo.PetArtworkResp
	var total int64
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error

		tx.Select("pet_artwork.*, DENSE_RANK() OVER (ORDER BY pet_artwork.vote_count DESC, pet_artwork.pk_time ASC) AS vote_count_rank, pet_prize.prize_content").
			Table("pet_artwork").
			Join("LEFT", "pet_prize", "pet_prize.work_code = pet_artwork.work_code AND pet_prize.prize_type=1")

		conditions := utils.GetQueryCondition(query)
		if conditions != "" {
			tx.Where(conditions)
		}
		if query.IsMiniApp == 1 {
			tx.OrderBy("vote_count_rank asc")
		}

		if query.PageIndex > 0 && query.PageSize > 0 {
			tx.Limit(query.PageSize, (query.PageIndex-1)*query.PageSize)
		}

		total, err = tx.Table("pet_artwork").FindAndCount(&resps)
		if err != nil {
			return err
		}
		return nil
	})
	return resps, total, err
}

// Detail 作品详情查询
func (s PetArtworkService) Detail(session *xorm.Session, query vo.PetArtworkDetailReq) (vo.PetArtworkResp, error) {
	var resp vo.PetArtworkResp
	err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
		var err error
		tx.Select("*, DENSE_RANK() OVER (ORDER BY vote_count DESC, pk_time ASC) AS vote_count_rank").
			Table("pet_artwork")

		conditions := utils.GetQueryCondition(query)
		if conditions != "" {
			tx.Where(conditions)
		}
		has, err := tx.Limit(1).Get(&resp)
		if err != nil {
			return err
		}

		// 计算气质评分排名
		if has {
			nobleScore := resp.NobleScore

			// 获取宠物排名和总数
			scoreList := make([]int, 0)

			sql := `SELECT MAX(noble_score) AS noble_score 
			FROM pet_artwork 
			GROUP BY scrm_user_id, pet_name, pet_breed, pet_gender 
			ORDER BY noble_score DESC;
			`

			err := tx.SQL(sql).Find(&scoreList)
			if err != nil {
				return err
			}

			totalPets := len(scoreList)
			resp.PetCount = totalPets
			if len(scoreList) > 0 {
				rank := 1 // 默认排名为总数+1
				for i, score := range scoreList {
					if nobleScore <= score {
						rank = i + 1
					} else {
						break
					}
				}

				// 计算超过百分比
				percentage := (1 - float64(rank)/float64(totalPets)) * 100
				// 去掉小数部分，保留整数
				resp.NobleRankPercent = int(percentage)
			}
		}

		return nil
	})
	return resp, err
}
