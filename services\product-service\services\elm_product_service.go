package services

import (
	product_po "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/proto"
	external "eShop/services/external-service/services"
	"eShop/services/omnibus-service/services"
	omnibus_vo "eShop/view-model/omnibus-vo"
	vo "eShop/view-model/product-vo"
	"errors"
	"fmt"
	"strings"

	"github.com/spf13/cast"
)

// AllowedPhotoAttrs 允许上传图片的属性名列表
type AllowedPhotoAttrs struct {
	Names map[string]bool
}

// NewAllowedPhotoAttrs 创建允许上传图片的属性配置
func NewAllowedPhotoAttrs(names []string) *AllowedPhotoAttrs {
	attrs := &AllowedPhotoAttrs{
		Names: make(map[string]bool),
	}
	for _, name := range names {
		attrs.Names[name] = true
	}
	return attrs
}

// UploadPictureToElm 上传图片到饿了么平台
func (s *StoreProductServiceData) UploadPictureToElm(picUrl string, appChannel int32) (string, error) {
	var pic proto.UploadPictureRequest
	pic.Url = picUrl
	pic.AppChannel = appChannel

	res, err := new(external.ElmProductService).UploadPicture(&pic)
	if err != nil {
		return "", err
	}
	if res.Code != 200 {
		return "", errors.New(res.Error)
	}
	return res.Data, nil
}

// Photos 处理商品图片
func (s *StoreProductServiceData) Photos(AppChannel int32, StoreProduct *product_po.ProProductStoreAppChannel) (PhotosArr []*proto.SkuPhotos) {
	if len(StoreProduct.Pic) == 0 {
		log.Errorf("图片不能为空")
		return
	}

	photoList := strings.Split(StoreProduct.Pic, ",")
	for picK, picV := range photoList {
		if strings.TrimSpace(picV) == "" {
			continue
		}

		uploadedUrl, err := s.UploadPictureToElm(picV, AppChannel)
		if err != nil {
			log.Errorf("饿了么商品图片上传失败，全部门店，productId：%d，ERR：%v, pic: %s",
				StoreProduct.ProductId, err.Error(), picV)
			continue
		}

		photo := proto.SkuPhotos{
			IsMaster: 0,
			Url:      uploadedUrl,
		}
		if picK == 0 {
			photo.IsMaster = 1
		}
		PhotosArr = append(PhotosArr, &photo)
	}
	return
}

// generateSkuSpecs 生成多规格组合
func generateSkuSpecs(skus []product_po.ProProductStoreInfoExt, attrs []product_po.ProProductChannelAttr, ChannelStoreId string, productId int) []*proto.SkuSpec {
	var saleAttrs []product_po.ProProductChannelAttr
	for _, attr := range attrs {
		if attr.AttrSaleProp {
			saleAttrs = append(saleAttrs, attr)
		}
	}
	//查询库存
	stockRes := new(services.InventoryService).QueryStock(omnibus_vo.QueryStockReq{
		ShopId:     ChannelStoreId,
		ProductIds: []int64{cast.ToInt64(productId)},
	})
	if stockRes.Code != 200 {
		log.Errorf("查询库存失败，门店：%s,商品：%d,库存：%s", ChannelStoreId, productId, stockRes.Message)
		stockRes.Stock = make(map[int64]omnibus_vo.VInventory)
	}
	var skuSpecs []*proto.SkuSpec
	for _, sku := range skus {
		skuSpec := &proto.SkuSpec{
			//ProductSpecId:   cast.ToInt64(sku.SkuId),
			SkuSpecCustomId: cast.ToString(sku.SkuId),
			Upc:             sku.SkuUpc,
			SalePrice:       cast.ToInt32(sku.RetailPrice),
			LeftNum:         cast.ToInt64(stockRes.Stock[cast.ToInt64(sku.SkuId)].AvailableNum),
			Weight:          cast.ToInt32(sku.WeightForUnit * 1000),
			Order:           1,
			SpecProperty:    make([]*proto.SpecProperty, 0),
		}

		// 添加SKU自身的规格属性
		skuSpec.SpecProperty = append(skuSpec.SpecProperty, &proto.SpecProperty{
			PropId:    168606316, // 饿了么默认规格的属性id值，写死
			PropText:  "规格",
			ValueId:   cast.ToInt64(sku.Id),
			ValueText: sku.ProductSpecs,
		})
		skuSpecs = append(skuSpecs, skuSpec)
	}
	return skuSpecs
}

// generateCatProperties 生成商品属性
func generateCatProperties(storeProduct *product_po.ProProductStoreAppChannel) []*proto.CatProperty {
	var catProps []*proto.CatProperty

	// 把sku的规格属性拼接，添加到cat_property
	var skuSpecs string
	for _, sku := range storeProduct.Skus {
		skuSpecs += sku.ProductSpecs + ","
	}
	catProps = append(catProps, &proto.CatProperty{
		PropId:    168606316,
		PropText:  "规格",
		ValueId:   0,
		ValueText: skuSpecs,
	})

	for _, v := range storeProduct.Attr {
		if v.AttrSaleProp == true || v.AttrName == "规格" {
			continue
		}
		catProps = append(catProps, &proto.CatProperty{
			PropId:    cast.ToInt64(v.AttrId),
			PropText:  v.AttrName,
			ValueId:   cast.ToInt64(v.AttrValueId),
			ValueText: v.AttrValue,
		})
	}
	return catProps
}

func (s *StoreProductServiceData) UpdateElmProduct(storeProduct *product_po.ProProductStoreAppChannel) error {
	logPrefix := "铺品商品，饿了么创建/更新商品，请求=="

	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}

	if storeProduct.CategoryName == "" {
		return errors.New("店内分类为必填项")
	}
	if len(storeProduct.Name) == 0 {
		return errors.New("商品名称为必填项")
	}
	if len(storeProduct.Attr) == 0 {
		return errors.New("商品属性为必填项")
	}

	var req = &proto.UpdateElmShopSkuRequest{}
	if s.Params.Type == 1 {
		req.LeftNum = -1 //商品库存
		req.Status = 0
	} else {
		req.SkuId = cast.ToString(storeProduct.ProductId) //创建不传，更新要传
		req.UpdateField = "bar_code,name,selling_point,pic,content_pc,weight_for_unit,cat3_id,cat_property,sku_spec,category_id"
	}
	req.ShopId = ChannelStoreId
	req.AppChannel = cast.ToInt32(AppChannel)
	if storeProduct.ContentPc != "" {
		req.Desc = storeProduct.ContentPc
	}
	req.Photos = s.Photos(req.AppChannel, storeProduct)
	req.Name = storeProduct.Name
	req.Cat3Id = cast.ToInt32(storeProduct.CategoryThirdId)
	req.CategoryName = storeProduct.CategoryName
	req.Summary = storeProduct.SellingPoint
	// 添加cat_property参数(商品属性)
	req.CatProperty = generateCatProperties(storeProduct)
	// 多规格商品可销售属性组合
	req.SkuSpec = generateSkuSpecs(storeProduct.Skus, storeProduct.Attr, ChannelStoreId, storeProduct.ProductId)
	req.CustomSkuId = cast.ToString(storeProduct.ProductId)

	res, err := new(external.ElmProductService).UpdateElmShopSku(req)
	if err != nil {
		log.Errorf("%s接口异常：,params:%s,error:%s", logPrefix, utils.JsonEncode(storeProduct), err.Error())
		return errors.New(err.Error())
	}
	if res.Code != 200 {
		log.Errorf("%s失败,params：%s,error:%s", logPrefix, utils.JsonEncode(storeProduct), res.Error)
		return errors.New(res.Error)
	}

	return nil
}

func (s *StoreProductServiceData) OnlineElmShopSkuOne(storeProduct *product_po.ProProductStoreAppChannel, syncType int) (err error) {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}
	if ChannelStoreId == "" {
		return errors.New("门店信息未设置")
	}

	req := &proto.UpdateElmShopSkuPriceRequest{
		ShopId:      ChannelStoreId,
		CustomSkuId: cast.ToString(storeProduct.ProductId),
		AppChannel:  cast.ToInt32(AppChannel),
	}
	res := new(proto.ELMBaseResponse)
	if syncType == 1 {
		res, err = new(external.ElmProductService).OnlineElmShopSkuOne(req)
	} else if syncType == 2 {
		res, err = new(external.ElmProductService).OfflineElmShopSkuOne(req)
	}
	if err != nil {
		log.Errorf("上下架商品，饿了么请求接口异常：,params:%s,error:%s", utils.JsonEncode(storeProduct), err.Error())
		return errors.New(err.Error())
	}
	if res.Code != 200 {
		log.Errorf("上下架商品，饿了么失败,params：%s,error:%s", utils.JsonEncode(storeProduct), res.Error)
		return errors.New(res.Error)
	}
	return nil
}

func (s *StoreProductServiceData) DeleteElmShopSku(storeProduct *product_po.ProProductStoreAppChannel) (err error) {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		log.Infof("删除商品，饿了么门店信息未设置:id:%d,product_id:%d,store_id:%s,channel_id:%d", storeProduct.Id, storeProduct.ProductId, storeProduct.StoreId, storeProduct.ChannelId)
		return nil
	}
	req := &proto.UpdateElmShopSkuPriceRequest{
		ShopId:      ChannelStoreId,
		CustomSkuId: cast.ToString(storeProduct.ProductId),
		AppChannel:  cast.ToInt32(AppChannel),
	}
	res, err := new(external.ElmProductService).DeleteElmShopSku(req)
	if err != nil {
		log.Errorf("删除商品，饿了么请求接口异常：,params:%s,error:%s", utils.JsonEncode(storeProduct), err.Error())
		return errors.New(err.Error())
	} else if res.Code != 200 {
		log.Errorf("删除商品，饿了么失败,params：%s,error:%s", utils.JsonEncode(storeProduct), utils.JsonEncode(res))
		return errors.New(res.Error)
	}
	return nil
}

func (s *StoreProductService) UpdateElmShopCategory(StoreId string, ChannelId int, req *vo.UpdateElmShopCategoryRequest) (err error) {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, StoreId, ChannelId)
	if !state {
		return errors.New("门店信息未设置")

	}
	req.ShopId = cast.ToString(ChannelStoreId)
	req.AppChannel = cast.ToInt32(AppChannel)
	res, err := new(external.ElmProductService).UpdateElmShopCategory(req)
	log.Infof("同步商品分类，饿了么请求接口：,params:%s,返回结果：%s", utils.JsonEncode(req), utils.JsonEncode(res))
	if err != nil {
		log.Errorf("更新商品分类，饿了么请求接口异常：,params:%s,error:%s", utils.JsonEncode(req), err.Error())
		return errors.New(err.Error())
	} else if res.Code != 200 {
		log.Errorf("更新商品分类，饿了么失败,params：%s,error:%s", utils.JsonEncode(req), utils.JsonEncode(res))
		return errors.New(res.Error)
	}
	return nil
}

func (s *StoreProductServiceData) SkuPriceUpdateOne(storeProduct *product_po.ProProductStoreAppChannel) (err error) {
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}
	//格式为"sku_id:销售价格(必选),市场价格(可选)"，示例："15397158021:100,110", "15397158021:100"；3.单位：分，范围：1~99999900；4.只支持一个sku_id
	skuidPrice := fmt.Sprintf("%v:%s", storeProduct.Skus[0].Id, cast.ToString(storeProduct.Skus[0].RetailPrice))
	req := &proto.SkuPriceUpdateOneRequest{
		ShopId:     ChannelStoreId,
		SkuidPrice: skuidPrice,
		AppChannel: cast.ToInt32(AppChannel),
	}
	res, err := new(external.ElmProductService).SkuPriceUpdateOne(req)
	if err != nil {
		log.Errorf("调价商品，饿了么请求接口异常,params:%s,error:%s", utils.JsonEncode(req), err.Error())
		return errors.New(err.Error())
	}
	if res.Code != 200 {
		log.Errorf("调价商品，饿了么失败,params：%s,error:%s", utils.JsonEncode(req), res.Error)
		return errors.New(res.Error)
	}
	return nil
}

// UpdateElmSkuPrice 更新饿了么商品价格
func (s *StoreProductServiceData) UpdateElmSkuSpecPrice(storeProduct *product_po.ProProductStoreAppChannel) error {
	// 获取门店信息
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}

	// 构建规格价格列表
	var skuPrices []proto.SkuSpecPriceSingleDto
	for _, sku := range storeProduct.Skus {
		skuPrice := proto.SkuSpecPriceSingleDto{
			SkuSpecCustomId: cast.ToString(sku.SkuId),
			SalePrice:       cast.ToInt64(sku.RetailPrice),
		}
		skuPrices = append(skuPrices, skuPrice)
	}

	// 构建请求体
	req := &proto.UpdateSkuPriceRequest{
		ShopId:           ChannelStoreId,
		CustomSkuId:      cast.ToString(storeProduct.ProductId), // 使用第一个SKU的ID作为商品ID
		SkuSpecPriceList: skuPrices,
		AppChannel:       cast.ToInt32(AppChannel),
	}

	// 调用饿了么接口
	res, err := new(external.ElmProductService).UpdateSpecSkuPrice(req)
	if err != nil {
		log.Errorf("更新价格失败，饿了么请求接口异常：params:%s, error:%s",
			utils.JsonEncode(req), err.Error())
		return err
	}

	if res.Code != 200 {
		log.Errorf("更新价格失败，饿了么返回错误：params:%s, error:%s",
			utils.JsonEncode(req), res.Error)
		return errors.New(res.Error)
	}

	return nil
}

// UpdateElmSkuStock 更新饿了么商品库存
func (s *StoreProductServiceData) UpdateElmSkuSpecStock(storeProduct *product_po.ProProductStoreAppChannel) error {
	// 获取门店信息
	ChannelStoreId, AppChannel, state := GetChannelInfoByStoreId(s.Engine, storeProduct.StoreId, storeProduct.ChannelId)
	if !state {
		return errors.New("门店信息未设置")
	}
	//查询库存
	stockRes := new(services.InventoryService).QueryStock(omnibus_vo.QueryStockReq{
		ShopId:     storeProduct.StoreId,
		ProductIds: []int64{cast.ToInt64(storeProduct.ProductId)},
	})
	if stockRes.Code != 200 {
		log.Errorf("查询库存失败，门店：%s,商品：%d,库存：%s", ChannelStoreId, storeProduct.ProductId, stockRes.Message)
		return errors.New(stockRes.Message)
	}
	// 构建规格库存列表
	var skuStocks []proto.SkuSpecStockSingleDto
	for _, sku := range storeProduct.Skus {
		skuStock := proto.SkuSpecStockSingleDto{
			SpecCustomId: cast.ToString(sku.SkuId),
			LeftNum:      cast.ToInt64(stockRes.Stock[cast.ToInt64(sku.SkuId)].AvailableNum),
		}
		skuStocks = append(skuStocks, skuStock)
	}

	// 构建请求体
	req := &proto.UpdateSkuStockRequest{
		ShopId:        ChannelStoreId,
		CustomSkuId:   cast.ToString(storeProduct.ProductId),
		SpecStockList: skuStocks,
		AppChannel:    cast.ToInt32(AppChannel),
	}

	// 调用饿了么接口
	res, err := new(external.ElmProductService).UpdateSkuSpecStock(req)
	if err != nil {
		log.Errorf("更新库存失败，饿了么请求接口异常：params:%s, error:%s",
			utils.JsonEncode(req), err.Error())
		return err
	}

	if res.Code != 200 {
		log.Errorf("更新库存失败，饿了么返回错误：params:%s, error:%s", utils.JsonEncode(req), res.Error)
		return errors.New(res.Error)
	}

	return nil
}
