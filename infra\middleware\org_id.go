package middleware

import (
	"eShop/infra/log"
	base_service "eShop/services/base-service"
	"eShop/view-model/distribution-vo"
	"github.com/spf13/cast"
	"net/http"
	"strings"
)

var (
	mapResult = make(map[string]string)
)

func SetOrgId(handle http.Handler) http.Handler {
	hfn := func(w http.ResponseWriter, r *http.Request) {
		//	orgId := r.Header.Get("org_id")
		// 请求头中没有org_id，再通过ACP编码去获取

		if strings.Contains(r.URL.Path, "/manager") {
			structOuterCode := r.Header.Get("structOuterCode")
			if _, has := mapResult[structOuterCode]; has {
				r.Header.Set("org_id", mapResult[structOuterCode])
			} else {
				server := base_service.OrgService{}
				var req distribution_vo.OrgPageData
				req.ZlOrgId = structOuterCode
				out, err := server.OrgGet(req)
				if out.Id == 0 {
					out.Id = 1
				}
				if err != nil {
					log.Error("查询主体ID出错", err)
					//出错了默认给1
					r.Header.Set("org_id", "1")
				}
				mapResult[structOuterCode] = cast.ToString(out.Id)
				r.Header.Set("org_id", mapResult[structOuterCode])
			}

		}
		handle.ServeHTTP(w, r)
	}
	return http.HandlerFunc(hfn)
}
