package points_repo

import (
	po "eShop/domain/points-po"
	"eShop/infra/errors"
	vo "eShop/view-model/points-vo"

	"xorm.io/xorm"
)

type ClsPointsDailyStatsRepo struct {
	SuperRepo[po.ClsPointsDailyStats]
}

func NewClsPointsDailyStatsRepo() ClsPointsDailyStatsRepo {
	return ClsPointsDailyStatsRepo{
		NewSuperRepo[po.ClsPointsDailyStats](),
	}
}

// SaveOrUpdate
func (r ClsPointsDailyStatsRepo) SaveOrUpdate(session *xorm.Session, stats po.ClsPointsDailyStats) error {
	// 检查记录是否存在
	exists, err := session.Where("stat_date = ?", stats.StatDate.Format("2006-01-02")).Exist(&po.ClsPointsDailyStats{})
	if err != nil {
		return errors.NewBadRequest("查询每日统计记录失败：" + err.Error())
	}

	if exists {
		// 如果记录存在，执行更新操作
		_, err = session.
			Table("cls_points_daily_stats").
			Where("stat_date = ?", stats.StatDate.Format("2006-01-02")).
			Incr("consume_total", stats.ConsumeTotal).
			Incr("consume_blky", stats.ConsumeBlky).
			Incr("consume_szld", stats.ConsumeSzld).
			Incr("issue_total", stats.IssueTotal).
			Incr("issue_blky", stats.IssueBlky).
			Incr("issue_szld", stats.IssueSzld).
			Incr("expired_total", stats.ExpiredTotal).
			Incr("expired_blky", stats.ExpiredBlky).
			Incr("expired_szld", stats.ExpiredSzld).
			Update(po.ClsPointsDailyStats{})
	} else {
		// 如果记录不存在，执行插入操作
		_, err = session.Insert(&stats)
	}

	if err != nil {
		return errors.NewBadRequest("保存或更新每日统计数据失败：" + err.Error())
	}

	return nil
}

func (r ClsPointsDailyStatsRepo) StatAll(session *xorm.Session) (vo.ClsPointsStatVO, error) {
	var vo vo.ClsPointsStatVO
	session.Select("SUM(issue_total) AS total_issue_points, SUM(consume_total) AS total_consume_points, SUM(expired_total) AS total_expired_points").Table("cls_points_daily_stats").Get(&vo)
	return vo, nil
}
