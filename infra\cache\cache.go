package cache

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
	"github.com/google/uuid"
)

type Source string

// Address redis://password@localhost:6380?ssl=true&db=1
type Address string

type MemoryCache struct {
	currentCache *redis.Client
	Items        map[Source]*redis.Client
}

var redisClient *redis.Client

var CacheSources = make(map[Source]Address)

// Default 如果当前配置了多个缓存，并不是第一个
func (cache MemoryCache) Default() *redis.Client {
	var currentCache *redis.Client
	if len(cache.Items) == 0 {
		panic(errors.New("未指定任何缓存的链接地址！"))
	}
	for _, value := range cache.Items {
		currentCache = value
		break
	}
	return currentCache
}

func NewMemberCache(addr Address) MemoryCache {
	var mc MemoryCache
	mc.currentCache = connectRedis(addr)
	return mc
}

func (cache MemoryCache) InitCache(sources map[Source]Address) {
	for k, v := range sources {
		cache.Items[k] = connectRedis(v)
	}
}

func (cache MemoryCache) Get(appName string, keys ...string) []interface{} {
	if cache.currentCache == nil {
		panic(errors.New("未指定缓存对象，请初始化MemoryCache对象或在程序启动中调InitCache方法"))
	}

	if len(keys) == 0 {
		panic(errors.New("未指定key"))
	}

	var result []interface{}

	if len(keys) == 1 {
		key := fmt.Sprintf("%s:%s", appName, keys[0])
		val, err := cache.currentCache.Get(key).Result()
		if err != nil && err.Error() != "redis: nil" {
			panic(err)
		}
		result = append(result, val)
	}

	//用redis管道
	if len(keys) > 1 {
		pipe := cache.currentCache.Pipeline()

		for _, v := range keys {
			key := fmt.Sprintf("%s:%s", appName, v)
			pipe.Get(key)
		}
		cmder, err := pipe.Exec()

		if err != nil && err.Error() != "redis: nil" {
			return nil
		}

		for _, v := range cmder {
			//result=append(result,v.)
			var value = v.(*redis.StringCmd).Val()
			if value != "redis: nil" {
				result = append(result, value)
			} else {
				result = append(result, nil)
			}
		}
	}

	return result
}

func (cache MemoryCache) Save(appName string, key string, value interface{}, expiration time.Duration) {
	checkParam(cache.currentCache, key)
	keyName := fmt.Sprintf("%s:%s", appName, key)
	cache.currentCache.Set(keyName, value, expiration)
}

func (cache MemoryCache) TrySave(appName string, key string, value interface{}, expiration time.Duration) bool {
	checkParam(cache.currentCache, key)
	keyName := fmt.Sprintf("%s:%s", appName, key)
	return cache.currentCache.SetNX(keyName, value, expiration).Val()
}

func (cache MemoryCache) Delete(appName string, keys ...string) {
	if cache.currentCache == nil {
		panic(errors.New("未指定缓存对象，请初始化MemoryCache对象或在程序启动中调InitCache方法"))
	}

	if len(keys) == 0 {
		panic(errors.New("未指定key"))
	}

	if len(keys) == 1 {
		key := fmt.Sprintf("%s:%s", appName, keys[0])
		_, err := cache.currentCache.Del(key).Result()
		if err != nil && err.Error() != "redis: nil" {
			panic(err)
		}

	}

	//用redis管道
	if len(keys) > 1 {
		pipe := cache.currentCache.Pipeline()

		for _, v := range keys {
			key := fmt.Sprintf("%s:%s", appName, v)
			pipe.Del(key)
		}
		_, err := pipe.Exec()

		if err != nil && err.Error() != "redis: nil" {
			panic(err)
		}

	}
}

// TryLock 尝试加锁，当参数expiration为0时，默认为1分钟
func (cache MemoryCache) TryLock(appName string, key string, expiration time.Duration) bool {
	checkParam(cache.currentCache, key)
	keyName := fmt.Sprintf("%s:%s", appName, key)

	lockValue := uuid.New().String() // 生成唯一锁值

	if expiration == 0 {
		expiration = time.Duration(60) * time.Second
	}

	ok, err := cache.currentCache.SetNX(keyName, lockValue, expiration).Result()
	if err != nil {
		return false // 加锁失败
	}

	return ok // 加锁成功，返回锁值
}

func (cache MemoryCache) Unlock(appName string, key string) {
	checkParam(cache.currentCache, key)
	keyName := fmt.Sprintf("%s:%s", appName, key)
	cache.currentCache.Del(keyName).Val()
}

func (cache MemoryCache) AtomicIncr(appName string, key string) {
	checkParam(cache.currentCache, key)
	keyName := fmt.Sprintf("%s:%s", appName, key)
	cache.currentCache.Incr(keyName).Val()
}

func (cache MemoryCache) AtomicDecr(appName string, key string) {
	checkParam(cache.currentCache, key)
	keyName := fmt.Sprintf("%s:%s", appName, key)
	cache.currentCache.Decr(keyName).Val()
}
func (cache MemoryCache) HashGet(appName string, key string, field string) string {
	checkParam(cache.currentCache, key, field)
	keyName := fmt.Sprintf("%s:%s", appName, key)
	return cache.currentCache.HGet(keyName, field).Val()
}

func (cache MemoryCache) ScriptLoad(script string) (string, error) {
	return cache.currentCache.ScriptLoad(script).Result()
}

func (cache MemoryCache) Del(key string) (int64, error) {
	return cache.currentCache.Del(key).Result()
}

func (cache MemoryCache) RPush(key string, values ...interface{}) (int64, error) {
	return cache.currentCache.RPush(key, values...).Result()
}

func (cache MemoryCache) LPush(key string, values ...interface{}) (int64, error) {
	return cache.currentCache.LPush(key, values...).Result()
}

func (cache MemoryCache) Set(key string, value interface{}, expiration time.Duration) (string, error) {
	return cache.currentCache.Set(key, value, expiration).Result()
}

func (cache MemoryCache) LRange(key string, start int64, stop int64) ([]string, error) {
	return cache.currentCache.LRange(key, start, stop).Result()
}

func (cache MemoryCache) Exists(key string) (int64, error) {
	return cache.currentCache.Exists(key).Result()
}

func (cache MemoryCache) LLen(key string) (int64, error) {
	return cache.currentCache.LLen(key).Result()
}
func (cache MemoryCache) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	return cache.currentCache.SetNX(key, value, expiration).Result()
}

func (cache MemoryCache) Pipeline() redis.Pipeliner {
	return cache.currentCache.Pipeline()
}

func (cache MemoryCache) EvalSha(sha1 string, keys []string, args ...interface{}) (interface{}, error) {
	return cache.currentCache.EvalSha(sha1, keys, args...).Result()
}
func (cache MemoryCache) Eval(script string, keys []string, args ...interface{}) (interface{}, error) {
	return cache.currentCache.Eval(script, keys, args...).Result()
}

func (cache MemoryCache) BRPop(timeout time.Duration, key string) ([]string, error) {
	return cache.currentCache.BRPop(timeout, key).Result()
}

// connectRedis     格式：redis://password@localhost:6380?ssl=true&db=1
func connectRedis(addr Address) *redis.Client {

	s1 := strings.Split(string(addr), ":")              //add redis://qlwhIPO82@#KDFQAwe@**********:6979?ssl=true&db=1\
	s2 := strings.Split(s1[1], "@")                     //s1[0] redis://qlwhIPO82@#KDFQAwe@**********
	s3 := strings.Split(s1[2], "?")                     //s1[1] 6979?ssl=true&db=1
	s31 := strings.Split(strings.ToLower(s3[1]), "db=") //s3[1] ssl=true&db=1
	s11 := strings.Split(s1[1], "//")

	pwdStr := strings.Replace(s11[1], fmt.Sprintf("@%s", s2[len(s2)-1]), "", -1)
	ip := s2[len(s2)-1]
	port := s3[0]
	dbStr := s31[1]

	if redisClient != nil {
		stats := redisClient.PoolStats()
		fmt.Printf("Hits=%d Misses=%d Timeouts=%d TotalConns=%d IdleConns=%d StaleConns=%d\n",
			stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
		return redisClient
	}
	DB, _ := strconv.Atoi(dbStr)
	address := fmt.Sprintf("%s:%s", ip, port)

	redisClient = redis.NewClient(&redis.Options{
		//连接信息
		Network:  "tcp",   //网络类型，tcp or unix，默认tcp
		Addr:     address, //主机名+冒号+端口，默认localhost:6379
		Password: pwdStr,  //密码
		DB:       DB,      // redis数据库index

		//连接池容量及闲置连接数量
		PoolSize:     200, // 连接池最大socket连接数，默认为4倍CPU数， 4 * runtime.NumCPU
		MinIdleConns: 10,  //在启动阶段创建指定数量的Idle连接，并长期维持idle状态的连接数不少于指定数量；。

		//超时
		DialTimeout:  5 * time.Second, //连接建立超时时间，默认5秒。
		ReadTimeout:  3 * time.Second, //读超时，默认3秒， -1表示取消读超时
		WriteTimeout: 3 * time.Second, //写超时，默认等于读超时
		PoolTimeout:  4 * time.Second, //当所有连接都处在繁忙状态时，客户端等待可用连接的最大等待时长，默认为读超时+1秒。

		//闲置连接检查包括IdleTimeout，MaxConnAge
		IdleCheckFrequency: 5 * time.Second,  //闲置连接检查的周期，默认为1分钟，-1表示不做周期性检查，只在客户端获取连接时对闲置连接进行处理。
		IdleTimeout:        10 * time.Second, //闲置超时，默认5分钟，-1表示取消闲置超时检查
		MaxConnAge:         0 * time.Second,  //连接存活时长，从创建开始计时，超过指定时长则关闭连接，默认为0，即不关闭存活时长较长的连接

		//命令执行失败时的重试策略
		MaxRetries:      1,                      // 命令执行失败时，最多重试多少次，默认为0即不重试
		MinRetryBackoff: 8 * time.Millisecond,   //每次计算重试间隔时间的下限，默认8毫秒，-1表示取消间隔
		MaxRetryBackoff: 512 * time.Millisecond, //每次计算重试间隔时间的上限，默认512毫秒，-1表示取消间隔

		////可自定义连接函数
		//Dialer: func() (net.Conn, error) {
		//	netDialer := &net.Dialer{
		//		Timeout:   5 * time.Second,
		//		KeepAlive: 5 * time.Minute,
		//	}
		//	return netDialer.Dial("tcp", address)
		//},

		//钩子函数
		OnConnect: func(conn *redis.Conn) error { //仅当客户端执行命令时需要从连接池获取连接时，如果连接池需要新建连接时则会调用此钩子函数
			fmt.Printf("conn=%v\n", conn)
			return nil
		},
	})
	//defer gClient.Close()
	return redisClient
}

func checkParam(client *redis.Client, key ...string) {
	if client == nil {
		panic(errors.New("未指定缓存对象，请初始化MemoryCache对象或在程序启动中调InitCache方法"))
	}

	if len(key) == 0 {
		panic(errors.New("未指定key"))
	}
}
