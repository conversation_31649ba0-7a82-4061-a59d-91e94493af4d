package services

import (
	"eShop/services/common"
	"eShop/view-model/distribution-vo"
	"testing"
)

func TestJobStatsDisService_StatsDisOrderDailyData(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req []distribution_vo.StatsShopDistributorDailyReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "tesst",
			args: args{
				req: []distribution_vo.StatsShopDistributorDailyReq{
					{StartDate: "2024-05-01", EndDate: "2024-06-02"},
				}},
			fields: fields{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &JobStatsDisService{
				BaseService: tt.fields.BaseService,
			}
			s.StatsDisOrderDailyData(tt.args.req...)
		})
	}
}

func TestJobStatsDisService_StatsDisOrderDaliyDataRun(t *testing.T) {
	//s := JobStatsDisService{}
	//s.StatsDisOrderDaliyDataRun("2024-04-01", "2024-07-14")
}
