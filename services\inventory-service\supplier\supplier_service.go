package supplier

import (
	"context"
	po "eShop/domain/inventory-po/supplier"
	"eShop/infra/converter"
	jwt "eShop/infra/jwtauth"
	"eShop/infra/pkg/util/cache"
	"eShop/services/common"
	vo "eShop/view-model/inventory-vo/supplier"
	"fmt"
	"time"
)

type SupplierService struct {
	common.BaseService
}

// NewService 创建供应商服务
func NewService() *SupplierService {
	return &SupplierService{}
}

// Create 创建供应商
func (s *SupplierService) Create(ctx context.Context, cmd vo.CreateCommand) error {
	s.Begin()
	session := s.Session
	defer s.Close()

	no, err := s.GenerateNo()	
	if err != nil {
		return err
	}

	sup := po.Supplier{
		ChainId:         jwt.CtxGet[int64](ctx, "ChainId"),
		Code:            no,
		Name:            cmd.Name,
		Address:         cmd.Address,
		Type:            cmd.Type,
		UnifiedSocialId: cmd.UnifiedSocialID,
		PurchasePeriod:  cmd.PurchasePeriod,
		DueDays:         cmd.DueDays,
		ContactPerson:   cmd.ContactPerson,
		ContactMethod:   cmd.ContactMethod,
		BankAccount:     cmd.BankAccount,
		BankName:        cmd.BankName,
		AccountName:     cmd.AccountName,
		Source:          cmd.Source,
		Url:             cmd.Url,
		Status:          1,
		CreatedOperator: jwt.CtxGet[string](ctx, "UserId"),
		CreatedTime: time.Now(),
		UpdatedOperator: jwt.CtxGet[string](ctx, "UserId"),
		UpdatedTime: time.Now(),
	}

	err = sup.Create(ctx, session)
	if err != nil {
		return err
	}

	// 创建供应商 无需事件，没有下文
	/*
	event := vo.CreateCommand{
		//ID:        sup.ID,
		//ChainID:   sup.ChainID,
		//TenantID:  sup.TenantID,
		Name: sup.Name,
		//CreatedBy: sup.CreatedBy,
		//CreatedAt: sup.CreatedTime,
	}
	fmt.Println(event)
	//return s.EventStore.SaveEvent(event)
	*/

	return nil
}

// Update 更新供应商
func (s *SupplierService) Update(ctx context.Context, cmd vo.UpdateCommand) error {
	s.Begin()
	session := s.Session
	defer s.Close()

	sup := po.Supplier{}
	sup.ChainId=       jwt.CtxGet[int64](ctx, "ChainId")
	err := sup.GetByID(ctx, session, cmd.ID)
	if err != nil {
		return err
	}

	sup.Name=            cmd.Name
	sup.Address=         cmd.Address
	sup.Type=            cmd.Type
	sup.UnifiedSocialId= cmd.UnifiedSocialID
	sup.PurchasePeriod=  cmd.PurchasePeriod
	sup.DueDays=         cmd.DueDays
	sup.ContactPerson=   cmd.ContactPerson
	sup.ContactMethod=   cmd.ContactMethod
	sup.BankAccount=     cmd.BankAccount
	sup.BankName=        cmd.BankName
	sup.AccountName=     cmd.AccountName
	sup.Source=          cmd.Source
	sup.Url=             cmd.Url
	sup.UpdatedOperator= jwt.CtxGet[string](ctx, "UserId")
	sup.UpdatedTime= time.Now()

	err = sup.Update(ctx, session)
	if err != nil {
		return err
	}

	return nil
}



// Enable 启用供应商
func (s *SupplierService) Enable(ctx context.Context, id int) error {
	s.Begin()
	session := s.Session
	defer s.Close()

	sup := po.Supplier{}
	sup.ChainId=       jwt.CtxGet[int64](ctx, "ChainId")
	err := sup.GetByID(ctx, session, id)
	if err != nil {
		return err
	}

	if sup.Status == 1 {
		return nil
	}

	sup.Status = 1
	sup.UpdatedOperator = jwt.CtxGet[string](ctx, "UserId")
	sup.UpdatedTime = time.Now()

	err = sup.Update(ctx, session)
	if err != nil {
		return err
	}

	return nil
}

// Disable 禁用供应商
func (s *SupplierService) Disable(ctx context.Context, id int) error {
	s.Begin()
	session := s.Session
	defer s.Close()

	sup := po.Supplier{}
	sup.ChainId=       jwt.CtxGet[int64](ctx, "ChainId")
	err := sup.GetByID(ctx, session, id)
	if err != nil {
		return err
	}

	if sup.Status == 0 {
		return nil
	}

	sup.Status = 0
	sup.UpdatedOperator = jwt.CtxGet[string](ctx, "UserId")
	sup.UpdatedTime = time.Now()

	err = sup.Update(ctx, session)
	if err != nil {
		return err
	}

	return nil
}

// Query 查询供应商列表
func (s *SupplierService) Query(ctx context.Context, params vo.QueryParams) ([]vo.Supplier, int, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	var page []vo.Supplier
	var paramItems []interface{}

	var countSql = `select count(*) as total`
	var selectSql = `select * `
	var fromSql=` from inventory_suppliers where 1=1`

	var whereSql = ` and chain_id = ?`
	paramItems = append(paramItems, jwt.CtxGet[int64](ctx, "ChainId"))
	if params.SupplierInfo != "" {
		whereSql += ` and( id=? or  name like ?)  `
		paramItems = append(paramItems,params.SupplierInfo, "%"+params.SupplierInfo+"%")
	}
	if params.Type != "" {
		whereSql += ` and type = ?`
		paramItems = append(paramItems,params.Type)
	}
	if params.Source>0{
		whereSql += ` and source = ?`
		paramItems = append(paramItems,params.Source)
	}
	if params.Status>0{
		whereSql += ` and status = ?`
		paramItems = append(paramItems,params.Status)
	}

	var num int64
	session.SQL(countSql + fromSql + whereSql,paramItems...).Get(&num)

	offset := (params.PageIndex - 1) * params.PageSize
	whereSql = whereSql + ` limit ?,?`
	paramItems = append(paramItems, offset, params.PageSize)

	err := session.SQL(selectSql+fromSql+whereSql, paramItems...).Find(&page)
	if err != nil {
		panic(err)
	}

	result, err := converter.ConvertSlice[vo.Supplier](page)
	if err != nil {
		return nil, 0, err
	}

	return result, int(num), nil
}

// Get 获取供应商
func (s *SupplierService) Get(ctx context.Context, id int) (po.Supplier, error) {
	s.Begin()
	session := s.Session
	defer s.Close()

	sup := po.Supplier{}
	sup.ChainId=       jwt.CtxGet[int64](ctx, "ChainId")
	err := sup.GetByID(ctx, session, id)
	if err != nil {
		return sup, err
	}

	return sup, nil
}

// GenerateVoucherNo	根据单据类型生成单据号
func (s SupplierService) GenerateNo() (string, error) {
	redisConn := cache.GetRedisConn()
	key := "eShop:SupplierNo"
	val, err := redisConn.Exists(key).Result()
	if err == nil && val <= 0 {
		redisConn.Set(key, 1, -1)
	}
	if err == nil && val > 0 {
		val, _ = redisConn.Incr(key).Result()
	}

	return fmt.Sprintf("%s%08d", "GYS", val), nil
}
