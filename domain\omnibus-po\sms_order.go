package omnibus_po

import (
	"eShop/infra/utils"
	"time"

	"xorm.io/xorm"
)

// SmsOrderInfo 短信订单信息
type SmsOrderInfo struct {
	Id        int64  `json:"id" xorm:"pk autoincr 'id'"`
	ChainId   int64  `json:"chain_id" xorm:"not null BIGINT 'chain_id'"`
	StoreId   string `json:"store_id" xorm:"not null VARCHAR(64) 'store_id'"`
	StoreName string `json:"store_name" xorm:"not null VARCHAR(64) 'store_name'"`
	//充值条数
	AmountNum int `json:"amount_num" xorm:"not null INT 'amount_num'"`
	//pay_amount
	PayAmount int `json:"pay_amount" xorm:"not null INT 'pay_amount'"`
	//下单时间
	OrderTime time.Time `json:"order_time" xorm:"not null DATETIME 'order_time'"`
	//支付时间
	PayTime    time.Time `json:"pay_time" xorm:"DATETIME 'pay_time'"`
	PayImage   string    `json:"pay_image" xorm:"VARCHAR(500) 'pay_image'"`
	Operator   string    `json:"operator" xorm:"not null VARCHAR(64) 'operator'"`
	OperatorId int64     `json:"operator_id" xorm:"not null BIGINT 'operator_id'"`
	//订单状态(1支付成功、2支付失败、3支付中)
	OrderStatus int `json:"order_status" xorm:"not null INT 'order_status'"`
	//使用次数
	UsedCount int `json:"used_count" xorm:"INT 'used_count'"`
	//refund_amount
	RefundAmount int `json:"refund_amount" xorm:"INT 'refund_amount'"`
	//退款次数
	RefundCount int       `json:"refund_count" xorm:"INT 'refund_count'"`
	Remark      string    `json:"remark" xorm:"VARCHAR(255) 'remark'"`
	UnitPrice   int       `json:"unit_price" xorm:"INT 'unit_price'"`
	CreateTime  time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime  time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

// SmsSendRecord 短信发送记录
type SmsSendRecord struct {
	Id         int64     `json:"id" xorm:"pk autoincr 'id'"`
	ChainId    int64     `json:"chain_id" xorm:"not null BIGINT 'chain_id'"`
	StoreId    string    `json:"store_id" xorm:"not null VARCHAR(64) 'store_id'"`
	StoreName  string    `json:"store_name" xorm:"not null VARCHAR(64) 'store_name'"`
	Mobile     string    `json:"mobile" xorm:"not null VARCHAR(30) 'mobile'"`
	EnMobile   string    `json:"en_mobile" xorm:"not null VARCHAR(50) 'en_mobile'"`
	SmsType    int       `json:"sms_type" xorm:"not null INT 'sms_type'"`
	Content    string    `json:"content" xorm:"not null TEXT 'content'"`
	ErrMes     string    `json:"err_mes" xorm:"not null TEXT 'err_mes'"`
	SendStatus int       `json:"send_status" xorm:"not null INT 'send_status'"`
	SendTime   time.Time `json:"send_time" xorm:"not null DATETIME 'send_time'"`
	//回执ID
	BizId string `json:"biz_id" xorm:"not null VARCHAR(100) 'biz_id'"`
	//短信条数
	RetryCount int       `json:"retry_count" xorm:"not null INT 'retry_count'"`
	CreateTime time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

func (s *SmsOrderInfo) TableName() string {
	return "eshop.sms_order_info"
}

func (s *SmsSendRecord) TableName() string {
	return "eshop.sms_send_record"
}

// CreateSmsOrder 创建短信订单
func (s *SmsOrderInfo) CreateSmsOrder(session *xorm.Session) error {
	// 生成订单号
	//s.OrderNo = utils.GenerateNo("SMS")
	// 设置默认值
	s.OrderStatus = 1 // 支付成功
	s.OrderTime = time.Now()
	s.UsedCount = 0
	s.RefundAmount = 0
	s.RefundCount = 0
	s.PayTime = time.Now().Add(time.Second * 2)
	// 插入数据
	_, err := session.Insert(s)
	return err
}

// UpdateSmsOrderRefundInfo 根据退款记录修改短信订单退款信息
func (s *SmsOrderInfo) UpdateSmsOrderRefundInfo(session *xorm.Session, orderNo int64, refundAmount int, refundCount int) error {
	// 更新订单的退款金额和退款数量
	updateFields := map[string]interface{}{
		"refund_amount": refundAmount,
		"refund_count":  refundCount,
	}

	_, err := session.Table(s.TableName()).Where("id = ?", orderNo).Update(updateFields)
	return err
}

// CreateSmsSendRecord 创建短信发送记录
func (s *SmsSendRecord) CreateSmsSendRecord(session *xorm.Session) error {
	// 设置默认值
	s.SendStatus = 3 // 待发送
	s.SendTime = time.Now()
	if s.RetryCount == 0 {
		s.RetryCount = 1
	}

	// 加密手机号
	s.EnMobile = utils.MobileEncrypt(s.Mobile)
	s.Mobile = utils.AddStar(s.Mobile)

	// 插入数据
	_, err := session.Table(s.TableName()).Insert(s)
	return err
}
