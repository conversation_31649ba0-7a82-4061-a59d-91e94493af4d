package common

import (
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/utils"
	"encoding/json"
	"errors"
	"fmt"
)

func FindPetCaseDetailFromZL(regId, orgId string) (out PetCaseDetail, err error) {
	logPrefix := fmt.Sprintf("===查询子系统宠物病例详情,regId为%s,orgId为%s", regId, orgId)
	log.Info(logPrefix)

	//增加body的返回数据
	bjZLUrl := config.Get("bj_zl_url")
	url := fmt.Sprintf("%s/api/nethospital/med-record/med-record-info?reg_id=%s&orgid=%s", bjZLUrl, regId, orgId)
	bjZLHeaders := utils.GetBJZLRequestHeader()
	res, err := utils.HttpGet(url, "", bjZLHeaders)
	log.Info(logPrefix, "请求北京宠物病例详情接口参数：", url, ";返回结果：", string(res))
	if err != nil {
		log.Error(logPrefix, "请求北京宠物病例详情接口失败，参数：", url, "；错误信息：", err)
		err = errors.New("查询病例详情失败:" + err.Error())
		return
	}
	//接收子龙返回的结果
	zlList := new(ZLPetCaseDetail)
	err = json.Unmarshal(res, &zlList)
	if err != nil {
		log.Error(logPrefix, "请求北京宠物病例详情接口返回的结果解析失败，错误信息：", err)
		zlErrorRes := new(ZLErrorResponse)
		err = json.Unmarshal(res, &zlErrorRes)
		if zlErrorRes.Code != 0 {
			err = errors.New("查询病例详情失败:" + zlErrorRes.Msg)
		}
		return
	}
	if zlList.Code == 0 {
		out = zlList.Data
	}
	return
}

func FindPetCaseListFromZL(petId string, pageIndex, pageSize int) (out *ZLPetCaseList, err error) {
	logPrefix := fmt.Sprintf("===查询子系统宠物病例列表,petId为%s", petId)
	log.Info(logPrefix)
	out = new(ZLPetCaseList)
	if pageSize == 0 {
		pageSize = 9999
	}
	if pageIndex == 0 {
		pageIndex = 1
	}
	bjZLUrl := config.Get("bj_zl_url")
	url := fmt.Sprintf("%s/api/nethospital/med-record/med-record-list?pet_id=%s&page=%d&page_size=%d", bjZLUrl, petId, pageIndex, pageSize)
	bjZLHeaders := utils.GetBJZLRequestHeader()
	res, err := utils.HttpGet(url, "", bjZLHeaders)
	log.Info(logPrefix, "请求北京宠物病例接口参数：", url, ";返回结果：", string(res))
	if err != nil {
		log.Error(logPrefix, "请求北京宠物病例接口失败，参数：", url, "；错误信息：", err)
		return out, err
	}
	err = json.Unmarshal(res, &out)
	if err != nil {
		log.Error(logPrefix, "请求北京宠物病例接口返回的结果解析失败，错误信息：", err)
		zlErrorRes := new(ZLErrorResponse)
		err = json.Unmarshal(res, &zlErrorRes)
		if zlErrorRes.Code != 0 {
			return out, errors.New(zlErrorRes.Msg)
		}
		return out, err
	}
	if out.Code != 0 {
		return out, errors.New(out.Msg)
	}
	return out, nil
}
