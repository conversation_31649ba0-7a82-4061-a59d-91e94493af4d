package voucher

// VoucherInfo 单据基本信息
type VoucherInfo struct {
	// 单据基本信息
    Id               int       `json:"id" xorm:"id"`
    CurrentVoucherNo string    `json:"current_voucher_no" xorm:"current_voucher_no"`
    ParentVoucherNo  string    `json:"parent_voucher_no" xorm:"parent_voucher_no"`
    ChildVoucherNo   string    `json:"child_voucher_no" xorm:"child_voucher_no"`
	ChildId          int       `json:"child_id" xorm:"child_id"`
    ChainId         int64     `json:"chain_id" xorm:"chain_id"`
    
    // 店铺信息
    StoreId         string    `json:"store_id" xorm:"store_id"`
    StoreName       string    `json:"store_name" xorm:"store_name"`
    StoreContactMethod string `json:"store_contact_method" xorm:"store_contact_method"`
    
    // 仓库信息
    WarehouseId     int       `json:"warehouse_id" xorm:"warehouse_id"`
    WarehouseName   string    `json:"warehouse_name" xorm:"warehouse_name"`
    WarehouseAddress string `json:"warehouse_address" xorm:"warehouse_address"`
    // 供应商信息
    SupplierId      int       `json:"supplier_id" xorm:"supplier_id"`
    SupplierName    string    `json:"supplier_name" xorm:"supplier_name"`
    SupplierContactPerson string `json:"supplier_contact_person" xorm:"supplier_contact_person"`
    SupplierContactMethod string `json:"supplier_contact_method" xorm:"supplier_contact_method"`
    SupplierAddress     string `json:"supplier_address" xorm:"supplier_address"`
    // 单据信息
    Name            string    `json:"name" xorm:"name"`
    VoucherType     int8      `json:"voucher_type" xorm:"voucher_type"`
    Status          int8      `json:"status" xorm:"status"`
    SourceType      int8      `json:"source_type" xorm:"source_type"`
    ProfitStatus    int8      `json:"profit_status" xorm:"profit_status"`
    ChangeNum       int       `json:"change_num" xorm:"change_num"`
    ChangeAmount    int       `json:"change_amount" xorm:"change_amount"`
    Remark          string    `json:"remark" xorm:"remark"`
    PurchaseTime    string    `json:"purchase_time" xorm:"purchase_time"`
	// 物流信息
	LogisticsNo string `json:"logistics_no" xorm:"logistics_no"`
	LogisticsCompany string `json:"logistics_company" xorm:"logistics_company"`
    DeliveryTime    string    `json:"delivery_time" xorm:"delivery_time"`
    ReturnTime      string    `json:"return_time" xorm:"return_time"`
    IsDeleted       int8      `json:"is_deleted" xorm:"is_deleted"`
    Operator        string    `json:"operator" xorm:"operator"`
    VoucherTime     string    `json:"voucher_time" xorm:"voucher_time"`
    CreatedTime     string    `json:"created_time" xorm:"created_time"`
    UpdatedTime     string    `json:"updated_time" xorm:"updated_time"`
}

type ProductInfo struct {
	// 商品信息
    SkuId           int       `json:"sku_id" xorm:"sku_id"`
	// 商品ID
    ProductId       int       `json:"product_id" xorm:"product_id"`
	// 商品名称
    ProductName     string    `json:"product_name" xorm:"product_name"`
	// 条形码
    BarCode         string    `json:"bar_code" xorm:"bar_code"`
	// 库位编码
	LocationCode	string    `json:"location_code" xorm:"location_code"`
	// 商品规格
    ProductSpecs    string    `json:"product_specs" xorm:"product_specs"`
	// 商品单位
    StoreUnit       string    `json:"store_unit" xorm:"store_unit"`
	// 商品重量
    WeightForUnit   float64   `json:"weight_for_unit" xorm:"weight_for_unit"`
	// 税率
    TaxRate         float64   `json:"tax_rate" xorm:"tax_rate"`
	// 单价
    Price           int    `json:"price" xorm:"price"`
	// 图片
	ProductImg string `json:"product_img" xorm:"product_img"`
	// 原因
	Reason string `json:"reason" xorm:"reason"`
	// 期望数量
    ExpectQuantity    int    `json:"expect_quantity" xorm:"expect_quantity"`
	// 期望金额
    ExpectAmount int    `json:"expect_amount" xorm:"expect_amount"`
	// 实际数量
    ActualQuantity    int    `json:"actual_quantity" xorm:"actual_quantity"`
	// 实际金额
    ActualAmount int    `json:"actual_amount" xorm:"actual_amount"`
	// 库存总数
	InventoryTotal int `json:"inventory_total" xorm:"inventory_total"`
	// 库存冻结数
	InventoryFreeze int `json:"inventory_freeze" xorm:"inventory_freeze"`
	// 库存可用数
	InventoryAvailable int `json:"inventory_available" xorm:"inventory_available"`
}

type VoucherRequestResponse struct {
	VoucherInfo VoucherInfo `json:"voucher_info" xorm:"voucher_info"`
	ProductInfo []ProductInfo `json:"product_info" xorm:"product_info"`
}

type VoucherRequestSaveLogisticsInfoCommand struct {
	// 单据ID
	Id int `json:"id" xorm:"id"`
	// 单据ID
	LogisticsNo string `json:"logistics_no" xorm:"logistics_no"`
	// 物流公司
	LogisticsCompany string `json:"logistics_company" xorm:"logistics_company"`
	// 物流信息
    DeliveryTime    string    `json:"delivery_time" xorm:"delivery_time"`
}
