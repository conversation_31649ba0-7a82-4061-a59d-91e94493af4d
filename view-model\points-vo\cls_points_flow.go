package points_vo

import (
	po "eShop/domain/points-po"
	task_type "eShop/infra/enum"
	"time"
)

// ClsPointsFlowSaveVO 用于创建积分流水的视图对象
type ClsPointsFlowSaveVO struct {
	SuperSaveVO[po.ClsPointsFlow]
	DisId           int       `json:"dis_id" validate:"required"`
	EnterpriseId    int       `json:"enterprise_id" validate:"required"`
	Type            int       `json:"type" validate:"required"`
	BizType         int       `json:"biz_type" validate:"required"` // 业务类型:1-开单商品，2-分销商品，3-积分兑换，4-商品退货积分扣减，5-积分到期自动扣减，6-积分赠送收入，7-积分赠送支出，8-获赠积分到期
	Status          int       `json:"status" validate:"required"`
	Points          int       `json:"points" validate:"required,ne=0"`
	RemainingPoints int       `json:"remaining_points"`
	ExpireTime      time.Time `json:"expire_time,omitempty"`
	BillOrderNo     string    `json:"bill_order_no,omitempty"`
	BillGoodsCode   string    `json:"bill_goods_code,omitempty"`
	PointsOrderId   int       `json:"points_order_id,omitempty"`
	PointsBlky      int       `json:"points_blky,omitempty"`
	PointsSzld      int       `json:"points_szld,omitempty"`
	Remark          string    `json:"remark,omitempty"`
	Operator        string    `json:"operator,omitempty"`
	StaffNo         string    `json:"staff_no,omitempty"`
	OccurTime       time.Time `json:"occur_time" validate:"required"`
}

// ClsPointsFlowUpdateVO 用于更新积分流水的视图对象
type ClsPointsFlowUpdateVO struct {
	SuperUpdateVO[po.ClsPointsFlow]
	Id              int    `json:"id" validate:"required"`
	Status          int    `json:"status,omitempty"`
	RemainingPoints int    `json:"remaining_points,omitempty"`
	Remark          string `json:"remark,omitempty"`
	Operator        string `json:"operator,omitempty"`
}

// ClsPointsFlowQueryVO 用于查询积分流水的视图对象
type ClsPointsFlowQueryVO struct {
	SuperQueryVO[po.ClsPointsFlow]
	DisId           int    `json:"dis_id" query:"cls_points_flow.dis_id:eq"`                // 分销商ID
	EnterpriseId    int    `json:"enterprise_id"  query:"cls_points_flow.enterprise_id:eq"` // 企业id
	DisName         string `json:"dis_name" query:"dis_distributor.real_name:like"`         // 分销员姓名
	Mobile          string `json:"mobile" query:"dis_distributor.encrypt_mobile:eq"`        // 分销员手机号
	EnterpriseName  string `json:"enterprise_name"`
	Type            int    `json:"type" query:"cls_points_flow.type:eq"`                      // 积分类型：1-发放 2-消耗
	OccurTimeStart  string `json:"occur_time_start" query:"cls_points_flow.occur_time:gte"`   // 发生时间-开始
	OccurTimeEnd    string `json:"occur_time_end" query:"cls_points_flow.occur_time:lte"`     // 发生时间-结束
	InSystem        int    `json:"in_system"`                                                 // 是否系统 1-体系内 0-体系外
	OperateType     int    `json:"operate_type"`                                              // 操作类型 1-全局发放 2-全局消耗 3-分销员 4-企业
	BizType         []int  `json:"biz_type" query:"cls_points_flow.biz_type:in"`              // 业务类型:1-开单商品，2-分销商品，3-积分兑换，4-商品退货积分扣减，5-积分到期自动扣减，6-积分赠送收入，7-积分赠送支出，8-获赠积分到期
	Status          []int  `json:"status" query:"cls_points_flow.status:in"`                  // 积分状态:1-待使用，2-部分使用，3-已使用，4-已过期
	ExpireTimeStart string `json:"expire_time_start" query:"cls_points_flow.expire_time:gte"` // 过期时间-开始
	ExpireTimeEnd   string `json:"expire_time_end" query:"cls_points_flow.expire_time:lte"`   // 过期时间-结束
	CreatedAtStart  string `json:"created_at_start" query:"cls_points_flow.created_at:gte"`   // 创建时间-开始
}

func (v ClsPointsFlowQueryVO) GetExportType() int {
	return task_type.ClsPointsFlowExport
}

// ClsPointsFlowResultVO 用于返回积分流水结果的视图对象
type ClsPointsFlowResultVO struct {
	SuperResultVO[ClsPointsFlowResultVO] `xorm:"extends"`
	// 积分流水基本信息
	Id            int    `json:"id"`              // 主键ID
	DisId         int    `json:"dis_id"`          // 分销员ID
	EnterpriseId  int    `json:"enterprise_id"`   // 企业ID
	Type          int    `json:"type"`            // 类型:1-积分进账，2-积分出账
	BizType       int    `json:"biz_type"`        // 业务类型:1-开单商品，2-分销商品，3-积分兑换，4-商品退货积分扣减，5-积分到期自动扣减，6-积分赠送收入，7-积分赠送支出，8-获赠积分到期
	Status        int    `json:"status"`          // 状态:1-待使用，2-部分使用，3-已使用，4-已过期
	Points        int    `json:"points"`          // 积分值
	ExpireTime    string `json:"expire_time"`     // 积分到期时间
	BillOrderNo   string `json:"bill_order_no"`   // 开单单号
	BillGoodsCode string `json:"bill_goods_code"` // 开单商品编码
	PointsOrderId int    `json:"points_order_id"` // 积分兑换订单id
	PointsBlky    int    `json:"points_blky"`     // 积分值(北京百林康源)
	PointsSzld    int    `json:"points_szld"`     // 积分值(深圳利都)
	Operator      string `json:"operator"`        // 操作人
	OccurTime     string `json:"occur_time"`      // 发生时间
	Region        string `json:"region"`          // 区域
	Remark        string `json:"remark"`          // 备注

	// 积分订单相关信息
	OrderNo        string `json:"order_no"`         // 积分订单编号
	OrderGoodsName string `json:"order_goods_name"` // 积分订单商品名称
	CostPrice      int    `json:"cost_price"`       // 积分订单商品成本价

	// 开单商品相关信息
	BillGoodsName string `json:"bill_goods_name"` // 开单商品名称
	BrandId       int    `json:"brand_id"`        // 品牌ID
	BrandName     string `json:"brand_name"`      // 品牌名称

	// 分销商相关信息
	DisName       string `json:"dis_name"`       // 分销商名称
	InSystem      int    `json:"in_system"`      // 是否系统内：1-体系内 0-体系外
	DisRole       int    `json:"dis_role"`       // 分销商角色 0-初始值 1-老板 2-店员 3-医生
	HospitalName  string `json:"hospital_name"`  // 医院名称
	Mobile        string `json:"mobile"`         // 手机号
	EncryptMobile string `json:"encrypt_mobile"` // 加密手机号

	// 企业信息
	EnterpriseName string `json:"enterprise_name"` // 企业名称
}

// CostFlow 积分消耗流水查询结果
type CostFlow struct {
	Id              int    `json:"id"`
	DisId           int    `json:"dis_id"`
	EnterpriseId    int    `json:"enterprise_id"`
	Points          int    `json:"points"`
	RemainingPoints int    `json:"remaining_points"`
	TotalSum        int    `json:"total_sum"`
	Operator        string `json:"operator"`
	Region          string `json:"region"`
	StaffNo         string `json:"staff_no"`
	PointsBlky      int    `json:"points_blky"`
	PointsSzld      int    `json:"points_szld"`
}

type SalesInVO struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}

// 定义工作任务结构
type SalesTask struct {
	OperateUserId  string
	Sales          []po.ZlDoctorSaleGoods
	Operator       SalesOperatorVO
	GoodsSourceMap map[string]int
	RuleMap        map[string][]po.ClsPointsRule
	OccurTime      time.Time
	ExpireTime     time.Time
}

type SalesOperatorVO struct {
	StaffNo          string `json:"staff_no"`
	DisId            int    `json:"dis_id"`
	EnterpriseId     int    `json:"enterprise_id"`
	SocialCreditCode string `json:"social_credit_code"`
	RealName         string `json:"real_name"`
	Mobile           string `json:"mobile"`
}

// ClsPointsFlowStatVO 积分流水统计视图对象
type ClsPointsFlowStatVO struct {
	// 当前积分统计
	Current int `json:"current"` // 当前积分剩余总数

	// 累计积分统计
	TotalGet    int `json:"total_get"`    // 用户累计获取总数
	TotalUse    int `json:"total_use"`    // 用户累计消耗总数
	TotalExpire int `json:"total_expire"` // 用户累计失效总数

	// 今日积分统计
	TodayGet int `json:"today_get"` // 今日获取总数
	TodayUse int `json:"today_use"` // 今日消耗总数
}
