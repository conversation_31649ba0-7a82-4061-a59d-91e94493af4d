package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SjanOutboundRecord 北京世纪安诺出库记录表领域模型
type SjanOutboundRecord struct {
	// 自增主键
	Id int `xorm:"pk autoincr not null 'id'" json:"id"`
	// 出库单号
	BillNo string `xorm:"not null unique VARCHAR(50) 'bill_no'" json:"bill_no"`
	// 出库单建立时间
	InDate time.Time `xorm:"not null datetime 'in_date'" json:"in_date"`
	// 代理编号
	CuNo string `xorm:"not null VARCHAR(50) 'cu_no'" json:"cu_no"`
	// 创建时间
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"`
}

// TableName 表名
func (s *SjanOutboundRecord) TableName() string {
	return "blky.sjan_outbound_records"
}

// GetByBillNo 根据出库单号获取出库记录
func (s *SjanOutboundRecord) GetByBillNo(session *xorm.Session, billNo string) (*SjanOutboundRecord, error) {
	var record SjanOutboundRecord
	has, err := session.Where("bill_no = ?", billNo).Get(&record)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &record, nil
}

// GetByCuNo 根据代理编号获取出库记录列表
func (s *SjanOutboundRecord) GetByCuNo(session *xorm.Session, cuNo string) ([]SjanOutboundRecord, error) {
	var records []SjanOutboundRecord
	err := session.Where("cu_no = ?", cuNo).Find(&records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// GetByTimeRange 根据时间范围查询出库记录
func (s *SjanOutboundRecord) GetByTimeRange(session *xorm.Session, startTime, endTime time.Time) ([]SjanOutboundRecord, error) {
	var records []SjanOutboundRecord
	err := session.Where("in_date BETWEEN ? AND ?", startTime, endTime).Find(&records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// AddOutboundRecord 添加出库记录
func (s *SjanOutboundRecord) AddOutboundRecord(session *xorm.Session) (int64, error) {
	return session.Insert(s)
}
