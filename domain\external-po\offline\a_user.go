package offline

import "time"

type User struct {
	Id                    string    `gorm:"primaryKey;not null" json:"id"`              // Id
	Username              string    `gorm:"unique;not null;default:''" json:"username"` // 用户名;大小写数字下划线
	NickName              string    `gorm:"not null;default:''" json:"nick_name"`       // 昵称
	Mobile                string    `gorm:"unique;not null;default:''" json:"mobile"`   // 手机;1开头11位纯数字
	WxUnionId             string    `json:"wx_union_id"`                                // 微信UnionId
	WxOpenId              string    `json:"wx_open_id"`                                 // 微信OpenId
	DdOpenId              string    `json:"dd_open_id"`                                 // 钉钉OpenId
	Sex                   string    `gorm:"default:'M'" json:"sex"`                     // 性别;[1-男 2-女 3-未知]
	State                 bool      `gorm:"default:true;not null" json:"state"`         // 状态;[0-禁用 1-启用]
	PasswordErrorLastTime time.Time `json:"password_error_last_time"`                   // 输错密码时间
	PasswordErrorNum      int       `gorm:"default:0" json:"password_error_num"`        // 密码错误次数
	PasswordExpireTime    time.Time `json:"password_expire_time"`                       // 密码过期时间
	Password              string    `gorm:"not null;default:''" json:"password"`        // 密码
	Salt                  string    `gorm:"not null;default:''" json:"salt"`            // 密码盐
	LastLoginTime         time.Time `json:"last_login_time"`                            // 最后登录时间
	IsDeleted             bool      `gorm:"default:false;not null" json:"is_deleted"`   // 删除状态;0-未删除 1-已删除
	CreatedBy             int64     `gorm:"default:0" json:"created_by"`                // 创建人id
	CreatedTime           time.Time `json:"created_time"`                               // 创建时间
	UpdatedBy             int64     `gorm:"default:0" json:"updated_by"`                // 更新人id
	UpdatedTime           time.Time `json:"updated_time"`                               // 更新时间
}
