package distribution_vo

import viewmodel "eShop/view-model"

type BlkyProductPageReq struct {
	//起始物流码
	StartLogisticsCode string `json:"start_logistics_code"`
	//结束物流码
	EndLogisticsCode string `json:"end_logistics_code"`
	//起始时间
	StartTime string `json:"start_time"`
	//结束时间
	EndTime string `json:"end_time"`
	//商品名称
	ProductName string `json:"product_name"`
	//类型  1：北京百林康源   2：深圳利都
	DataType int `json:"data_type"`
	//起始序号
	StartSn string `json:"start_sn"`
	//结束序号
	EndSn string `json:"end_sn"`
	viewmodel.BasePageHttpRequest
}

type BlkyProductPageResp struct {
	viewmodel.BasePageHttpResponse
	//列表数据
	Data []BlkyProductPageData `json:"data"`
}

type BlkyProductPageData struct {
	//主键
	Id int `json:"id"`
	//物流码
	Swlm string `json:"swlm"`
	//防伪码
	Sfwm string `json:"sfwm"`
	//生成日期
	Dregtime string `json:"dregtime"`
	//商品名称
	Sspmc string `json:"sspmc"`
	//是否分销:0-未使用，1-已使用
	State int `json:"state"`
	//类型  1：北京百林康源   2：深圳利都
	DataType int `json:"data_type"`
	//序列号
	Sn string `json:"sn"`
}

type BlkyProductPageRes struct {
	viewmodel.BasePageHttpResponse
	//列表数据
	Data []BlkyProductData `json:"data"`
}

type BlkyProductData struct {
	//主键
	Id int `json:"id"`
	//物流码
	Swlm string `json:"swlm"`
	//防伪码
	Sfwm string `json:"sfwm"`
	//生成日期
	Dregtime string `json:"dregtime"`
	//商品名称
	Sspmc string `json:"sspmc"`
	//是否分销:0-未使用，1-已使用
	State int `json:"state"`
	//类型  1：北京百林康源   2：深圳利都
	DataType int `json:"data_type"`
	//序列号
	Sn string `json:"sn"`
	//商品名称
	ProductName string `json:"product_name"`
}
