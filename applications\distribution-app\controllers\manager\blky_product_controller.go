package manager

import (
	bytes2 "bytes"
	"eShop/infra/enum"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	base_service "eShop/services/base-service"
	"eShop/services/common"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

// BlkyProductPage 百林康源商品列表查询
// @Summary 百林康源商品列表查询
// @Description 百林康源商品列表查询接口
// @Tags 后台接口-百林康源
// @Accept  json
// @Produce  json
// @Param BlkyProductPageReq body distribution_vo.BlkyProductPageReq true " "
// @Success 200 {object} distribution_vo.BlkyProductPageResp
// @Failure 400 {object} distribution_vo.BlkyProductPageResp
// @Router /manager/blky/product/page-list [GET]
func BlkyProductPage(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.BlkyProductPageRes{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.BlkyProductPageReq](r)
	if err != nil {
		log.Error("百林康源商品列表查询，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("百林康源商品列表查询，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			resp.Message = fmt.Sprintf("百林康源商品列表查询，操作参数校验异常：%s", vErr)
		} else {
			service := services.BlkyProductService{}
			list, total, err := service.BlkyProductPage(req)
			if err != nil {
				log.Error("百林康源商品列表查询失败：err=" + err.Error())
				resp.Message = fmt.Sprintf("百林康源商品列表查询异常：%s", err.Error())
			} else {
				resp.BasePageHttpResponse.Code = 200
				resp.BasePageHttpResponse.Total = total
				resp.Data = list
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// BlkyProductExport 导出百林康源商品
// @Summary 导出百林康源商品
// @Description 导出百林康源商品接口
// @Tags 后台接口-百林康源
// @Accept  json
// @Produce  json
// @Param BlkyProductPageReq body distribution_vo.BlkyProductPageReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/blky/product/export [POST]
func BlkyProductExport(w http.ResponseWriter, r *http.Request) {
	resp := viewmodel.BaseHttpResponse{
		Code: 400,
	}

	req, err := utils.Bind[distribution_vo.BlkyProductPageReq](r)
	if err != nil {
		log.Error("导出百林康源商品，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("导出百林康源商品，参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "export")
		if vErr != nil {
			resp.Message = fmt.Sprintf("导出百林康源商品，参数校验异常：%s", vErr)
		} else {
			s := common.TaskListService{}

			taskContent := enum.TaskContentBlkyProductExport
			if req.DataType == disdistributor.XkucunCompanySzld {
				taskContent = enum.TaskContentSzldProductExport
			}

			// 检查是否有正在进行的百林康源导出任务，有则不让导出，设计同步sqlserver的重复数据问题
			_, count, _ := s.GetTaskList(distribution_vo.GetTaskListRequest{
				ContentStr: cast.ToString(taskContent),
				TaskStatus: 1,
			})
			if count > 0 {
				resp.Message = "存在正在执行的导出任务，请稍候重试"
			} else {
				var task distribution_vo.TaskList
				par, _ := json.Marshal(req)
				task.OperationFileUrl = string(par)
				task.OrgId = cast.ToInt(r.Header.Get("org_id"))
				task.TaskContent = cast.ToInt8(taskContent)
				err := s.CreatTask(r, task)
				if err != nil {
					log.Error("导出百林康源商品：err=" + err.Error())
					resp.Message = fmt.Sprintf("导出百林康源商品：%s", err.Error())
				} else {
					resp.Code = 200
				}
			}
		}
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

// BlkyQueryCode
// @Summary 百林康源物流码/箱码查询 @cls_v1.2
// @Tags 后台接口-百林康源
// @Accept plain
// @Produce json
// @Param BlkyQueryCodeRequest body distribution_vo.BlkyQueryCodeRequest true " "
// @Success 200 {object} distribution_vo.BlkyCodeListRes
// @Failure 400 {object} distribution_vo.BlkyCodeListRes
// @Router /manager/blky/query-code [get]
func BlkyQueryCode(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.BlkyCodeListRes{}
	resp.Code = 400
	req, err := utils.Bind[distribution_vo.BlkyQueryCodeRequest](r)
	if err != nil {
		resp.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
		out2, _ := json.Marshal(resp)
		w.Write(out2)
		return
	}

	service := services.BlkyCodeService{}

	resp.Data = service.QueryCodeList(req)
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	//添加操作日志
	if resp.Data.IsExist {
		go func(r *http.Request, code string) {
			new(base_service.OperateLogService).Add(r, distribution_vo.OperateLogReq{
				ModuleType:  base_service.ModuleCode,
				Type:        base_service.Code1Type,
				FromId:      code,
				Description: fmt.Sprintf("查询物流码%s", code),
			})
		}(r, req.Code)
	}

	if req.IsExport == 1 && len(resp.Data.UnuseData) > 0 {
		f := excelize.NewFile()
		writer, _ := f.NewStreamWriter("Sheet1")
		_ = writer.SetRow("A1", []interface{}{
			"物流码",
		})

		for i := 0; i < len(resp.Data.UnuseData); i++ {
			v := resp.Data.UnuseData[i]

			writer.SetRow("A"+strconv.Itoa(i+2), []interface{}{
				cast.ToString(v.Swlm),
			})
		}
		writer.Flush()
		var buff bytes2.Buffer

		if err = f.Write(&buff); err != nil {
			w.Write([]byte("导出文件失败"))
			return
		}
		fileName := "操作日志列表导出 -" + time.Now().Format("20060102150405") + ".xlsx"
		w.Header().Set(echo.HeaderContentType, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		w.Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
		w.Write(buff.Bytes())
		return
	}
	w.Write(bytes)
}

// DeductCommission
// @Summary 扣除佣金 @cls_v1.2
// @Tags 后台接口-百林康源
// @Accept plain
// @Produce json
// @Param CommissionWithdrawReq body distribution_vo.CommissionWithdrawReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/blky/deduct_commission [post]
func DeductCommission(writer http.ResponseWriter, r *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	if req, err := utils.Bind[distribution_vo.CommissionWithdrawReq](r); err != nil {
		out.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			out.Message = fmt.Sprintf("参数请求失败：%s", vErr)
		} else {
			Description, err := new(services.BlkyCodeService).DeductCommission(req)
			if err != nil {
				out.Message = err.Error()
				out2, _ := json.Marshal(out)
				writer.Write(out2)
				return
			}
			//添加操作日志
			go func(r *http.Request, code string) {
				new(base_service.OperateLogService).Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleCode,
					Type:        base_service.Code2Type,
					FromId:      code,
					Description: Description,
				})
			}(r, req.Code)
			out.Code = 200
		}
	}
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 物流码恢复
// @Summary 物流码恢复 @cls_v1.2
// @Tags 后台接口-百林康源
// @Accept plain
// @Produce json
// @Param BlkyQueryCodeRequest body distribution_vo.BlkyQueryCodeRequest true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/blky/restore_code [Post]
func RestoreCode(writer http.ResponseWriter, r *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	if req, err := utils.Bind[distribution_vo.BlkyQueryCodeRequest](r); err != nil {
		out.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
	} else {
		Description, err := new(services.BlkyCodeService).RestoreCode(req)
		if err != nil {
			out.Message = err.Error()
		} else {
			//添加操作日志
			go func(r *http.Request, code string) {
				operateLogService := base_service.OperateLogService{}
				operateLogService.Add(r, distribution_vo.OperateLogReq{
					ModuleType:  base_service.ModuleCode,
					Type:        base_service.Code3Type,
					FromId:      code,
					Description: Description,
				})
			}(r, req.Code)
			out.Code = 200
		}
	}
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}

// 删除结算管理记录
// DeductCommission
// @Summary 删除结算管理记录
// @Tags 后台接口-百林康源
// @Accept plain
// @Produce json
// @Param DelDataReq body distribution_vo.DelDataReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/blky/del_data [post]
func DelTestData(writer http.ResponseWriter, r *http.Request) {
	out := viewmodel.BaseHttpResponse{Code: 400}
	if req, err := utils.Bind[distribution_vo.DelDataReq](r); err != nil {
		out.Message = fmt.Sprintf("参数解析失败：%s", err.Error())
	} else {
		vErr := validate.Validate(req, "")
		if vErr != nil {
			out.Message = fmt.Sprintf("参数请求失败：%s", vErr)
		} else {
			err = new(services.BlkyCodeService).DelTestData(req)
			if err != nil {
				out.Message = err.Error()
				out2, _ := json.Marshal(out)
				writer.Write(out2)
				return
			}
			out.Code = 200
		}
	}
	out2, _ := json.Marshal(out)
	writer.Write(out2)
}
