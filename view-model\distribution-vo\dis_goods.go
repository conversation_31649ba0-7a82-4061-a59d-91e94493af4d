package distribution_vo

import (
	"eShop/view-model"
)

type GoodsPageReq struct {
	//是否分销:0-否，1-是（默认-1）
	IsDis int `json:"is_dis"`
	//店铺id
	ShopId int `json:"shop_id"`
	//商品分类id
	GcId int `json:"gc_id"`
	//查询条件
	Where string `json:"where"`
	//查询条件的类型（goods_name=商品名称，spu=商品spu，sku=商品sku goods_state=1上架 2下架）
	WhereType string `json:"where_type"`
	//主体:1-阿闻，2-极宠家，3-福码购
	OrgId int `json:"org_id"`
	//0-all 1-上架，2-下架
	GoodsState int `json:"goods_state"`
	viewmodel.BasePageHttpRequest
}

type GoodsPageResp struct {
	viewmodel.BasePageHttpResponse
	Data []GoodsPageData `json:"data"`
}

type GoodsPageData struct {
	StoreId int `json:"store_id"`
	//商品id
	Id int `json:"id"`
	//spu
	GoodsCommonid int `json:"goods_commonid"`
	//sku
	GoodsId int `json:"goods_id"`
	//商品名称
	GoodsName string `json:"goods_name"`
	//商品规格
	GoodsSpec string `json:"goods_spec"`
	// 商品货号（主体为百林康源时，存储百林sku）
	GoodsSerial string `json:"goods_serial"`
	//所属店铺id
	ShopId int `json:"shop_id"`
	//所属店铺名称
	ShopName string `json:"shop_name"`
	//分类id
	GcId int `json:"gc_id"`
	//商品分类名称
	GcName string `json:"gc_name"`
	//商品价格（元）
	GoodsPrice float64 `json:"goods_price"`
	//是否分销
	IsDis int `json:"is_dis"`
	//是否为默认佣金设置：0-否，1-是
	IsDefaultCommisRate int `json:"is_default_commis_rate"`
	// 日常佣金比例,
	DisNormalCommisRate float64 `json:"dis_normal_commis_rate"`
	//佣金比例
	DisCommisRate float32 `json:"dis_commis_rate"`
	//更新时间
	UpdateTime string `json:"update_time"`
	// 素材推广
	DisWrite string `json:"dis_write"`
	// 分销类型
	DisType int `json:"dis_type"` //1分享连接，2扫码 3 自己扫码自己 5 自主访问下单
	//上下架状态
	GoodsState int `json:"goods_state"` //商品状态 0下架，1正常
}

type GoodsGlobalSetReq struct {
	//主体id
	OrgId int `json:"org_id"`
	//佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	// 日常佣金
	DisNormalCommisRate float64 `json:"dis_normal_commis_rate"`
}

type GoodsCommissionSetReq struct {
	//主体id
	OrgId int `json:"org_id"`
	//商品id
	GoodsId int `json:"goods_id" validate:"required"`
	//是否分销：0-否，1-是
	IsDis int `json:"is_dis"`
	//是否为默认佣金设置：0-否，1-是
	IsDefaultCommisRate int `json:"is_default_commis_rate"`
	//佣金比例
	DisCommisRate float64 `json:"dis_commis_rate"`
	//推广素材
	DisWrite string `json:"dis_write"`
	// 编辑时间
	GoodsEdittime int `json:"goods_edittime"`
	// 日常佣金
	DisNormalCommisRate float64 `json:"dis_normal_commis_rate"`
}

type GoodsDisWriteReq struct {
	//主体id
	OrgId int `json:"org_id"`
	//商品id
	GoodsId int `json:"goods_id" validate:"required"`
	//推广素材
	DisWrite string `json:"dis_write"`
}
type GlobalCommisSettingResp struct {
	viewmodel.BaseHttpResponse
	GlobalCommisRate float64 `json:"global_commis_rate"`
}
