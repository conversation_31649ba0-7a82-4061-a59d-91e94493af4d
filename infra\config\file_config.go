package config

import (
	"net"
)

var LocalSetting FileSetting

type FileSetting struct {
	Grpc struct {
		Appid   string
		Address string
		//LogAddress string
	}

	LocalIP string
}

func GetCurrentIP() string {
	conn, err := net.Dial("tcp", "www.rvet.cn:80")
	if err != nil {
		panic(err)
	}
	defer conn.Close()

	// 获取本机IP地址
	localAddr := conn.LocalAddr().(*net.TCPAddr)
	localIP := localAddr.IP

	return localIP.String()
}
