package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	"eShop/view-model/product-vo"
	"net/http"
	"testing"
)

func TestProductService_ProductToEs(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		in product_vo.ProductEs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "商品同步ES"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := ProductService{
				BaseService: tt.fields.BaseService,
			}
			var in product_vo.ProductEs
			in.Type = 1
			in.StoreId = "10000007"
			log.Init()
			if err := s.ProductToEs(in); (err != nil) != tt.wantErr {
				t.Errorf("ProductToEs() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestProductService_GenerateBarCode(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
		Request     *http.Request
	}
	type args struct {
		chainId int64
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantCode string
		wantErr  bool
	}{
		// TODO: Add test cases.
		{
			name: "aaa",
			args: args{
				chainId: 1,
			},
			fields: fields{
				BaseService: common.BaseService{},
				Request:     &http.Request{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log.Init()
			s := ProductService{
				BaseService: common.BaseService{},
			}
			gotCode, err := s.GenerateBarCode(tt.args.chainId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateBarCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotCode != tt.wantCode {
				t.Errorf("GenerateBarCode() gotCode = %v, want %v", gotCode, tt.wantCode)
			}
		})
	}
}
