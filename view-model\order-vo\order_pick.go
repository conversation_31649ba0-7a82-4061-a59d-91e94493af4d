package order_vo

type PickingTaskStatReq struct {
	PickupUserId int64  `json:"pickup_user_id"` // 拣货人id(前端无需传)
	ShopId       string `json:"shop_id"`        // 店铺id(前端无需传)

}
type PickingTaskStatRes struct {
	// 待领取数量
	WaitPickupCount int64 `json:"wait_pickup_count"`
	// 待拣货数量
	WaitPickingCount int64 `json:"wait_picking_count"`
	// 已完成数量
	CompletedCount int64 `json:"completed_count"`
}

// 拣货任务列表请求参数
type GetOrderListReq struct {
	ShopId             string `json:"shop_id"`              // 门店id
	OrderSn            string `json:"order_sn"`             // 订单编号
	Type               int    `json:"type"`                 // 列表类型：1待领取 2待拣货 3已完成
	PickupUserId       int64  `json:"pickup_user_id"`       // 拣货人id
	PickupCode         string `json:"pickup_code"`          // 取货码也就是流水号
	PickingTimeStart   string `json:"picking_time_start"`   // 拣货开始时间
	PickingTimeEnd     string `json:"picking_time_end"`     // 拣货结束时间
	PageIndex          int    `json:"page_index"`           // 页码
	PageSize           int    `json:"page_size"`            // 每页大小
	ShowAvailableStock int    `json:"show_available_stock"` // 是否展示可用库存0否1是
	IsStats            bool   `json:"is_stats"`             // 是否统计
	QueryKey           string `json:"query_key"`            // 查询关键字:订单编号、子订单号、外部订单号（美饿有）、手机号查询
}

// 拣货任务列表响应参数
type GetOrderListRes struct {
	ChannelId        int            `json:"channel_id"`         // 渠道id,订单来源 1-阿闻到家 2-美团 3-饿了么 4-京东到家 5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗 10-自提 100线下门店	OrderSn          string         `json:"order_sn"`           // 订单编号
	ShopId           string         `json:"shop_id"`            // 店铺id
	OrderSn          string         `json:"order_sn"`           // 订单编号
	OldOrderSn       string         `json:"old_order_sn"`       // 渠道订单号
	ChildOrderSn     string         `json:"child_order_sn"`     // 子订单编号
	OrderStatus      int            `json:"order_status"`       // 订单状态：0已取消,10(默认)未付款,20已付款,30已完成
	OrderStatusChild int            `json:"order_status_child"` // 子状态：20101（默认）未接单,20102已接单,20103配送中,20104已送达,20105已取货,20106已完成,20107已取消;10201(电商默认，以下状态电商专用)未付款,20201待发货,20202全部发货,20203确认收货,20204部分发货,20205已取消;30100已取消,30101待核销,30102部分核销,30103已完成(已核销)',
	WarehouseId      int            `json:"warehouse_id"`       // 仓库id
	WarehouseCode    string         `json:"warehouse_code"`     // 仓库代码
	WarehouseName    string         `json:"warehouse_name"`     // 仓库名称
	ReceiverName     string         `json:"receiver_name"`      // 收件人
	ReceiverState    string         `json:"receiver_state"`     // 收件省
	ReceiverCity     string         `json:"receiver_city"`      // 收件市
	ReceiverDistrict string         `json:"receiver_district"`  // 收件区
	ReceiverAddress  string         `json:"receiver_address"`   // 收件地址
	ReceiverPhone    string         `json:"receiver_phone"`     // 收件电话
	EnReceiverPhone  string         `json:"en_receiver_phone"`  // 加密收件电话
	ReceiverMobile   string         `json:"receiver_mobile"`    // 收件手机
	EnReceiverMobile string         `json:"en_receiver_mobile"` // 加密收件手机号
	BuyerMemo        string         `json:"buyer_memo"`         // 买家留言
	SellerMemo       string         `json:"seller_memo"`        // 卖家留言
	PickingTime      string         `json:"picking_time"`       // 拣货时间
	IsPicking        int            `json:"is_picking"`         // 是否已拣货
	PickupCode       string         `json:"pickup_code"`        // 取货码也就是流水号
	PickupUserId     int64          `json:"pickup_user_id"`     // 拣货人id
	PickupUserName   string         `json:"pickup_user_name"`   // 拣货人姓名
	CreateTime       string         `json:"create_time"`        // 创建时间
	OrderProduct     []OrderProduct `json:"order_product"`      // 订单商品
}

// 领取拣货任务请求参数
type ReceivePickingTaskReq struct {
	OrderSn        string `json:"order_sn"`         // 订单编号
	ShopId         string `json:"shop_id"`          // 门店ID
	PickupUserId   int64  `json:"pickup_user_id"`   // 拣货人ID
	PickupUserName string `json:"pickup_user_name"` // 拣货人姓名
}

// CompletePickingTaskReq 完成拣货任务请求
type CompletePickingTaskReq struct {
	OrderSn      string `json:"order_sn" binding:"required"` // 订单号（备注： 这里传进来的是父订单的订单号）
	SkuIds       []int  `json:"sku_ids" binding:"required"`  // 商品行ID数组,支持批量拣货
	ShopId       string `json:"-"`                           // 店铺ID,从JWT获取
	PickupUserId int64  `json:"-"`                           // 拣货人ID,从JWT获取
}
