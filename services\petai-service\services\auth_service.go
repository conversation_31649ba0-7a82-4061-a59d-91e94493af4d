package services

import (
	omnibus_po "eShop/domain/omnibus-po"
	petai_po "eShop/domain/petai-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	petai_vo "eShop/view-model/petai-vo"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"
)

// NewCozeService 创建coze服务
func NewAuthService() *AuthService {
	return &AuthService{}
}

type AuthService struct {
	common.BaseService
	Request *http.Request
	JwtInfo *jwtauth.JwtInfo
}

// 用户同意授权阿闻数据到小闻养宠助手
func (s *AuthService) UserAgreeAuth(in petai_vo.UserAgreeAuthReq) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()
	logPrefix := fmt.Sprintf("====用户同意授权数据,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	// 更新用户授权状态
	if _, err = session.Table("eshop.user_info").Where("user_info_id =?", in.UserInfoId).Update(map[string]interface{}{
		"is_authorized": in.IsAuthorized,
	}); err != nil {
		session.Rollback()
		log.Error(logPrefix + "更新用户授权状态失败,err:" + err.Error())
		err = errors.New("更新用户授权状态失败")
		return
	}
	// 不是授权则返回
	if in.IsAuthorized != petai_po.IsAuthorizedYes {
		session.Commit()
		return
	}
	if err = new(petai_po.EshopUserInfo).AuthData(session, petai_po.AuthDataReq{
		UserInfoId: in.UserInfoId,
	}); err != nil {
		session.Rollback()
		log.Error(logPrefix + "授权scrm用户数据失败,err:" + err.Error())
		err = errors.New("授权用户数据失败")
		return
	}

	// 授权宠物数据
	if err = new(petai_po.UserPetInfo).AuthData(session, in.UserInfoId); err != nil {
		session.Rollback()
		log.Error(logPrefix + "授权scrm用户宠物数据失败,err:" + err.Error())
		err = errors.New("授权用户宠物数据失败")
		return
	}

	// 授权scrm驱虫免疫数据
	if err = new(petai_po.UserPetVaccinateRecord).AuthData(session, in.UserInfoId); err != nil {
		session.Rollback()
		log.Error(logPrefix, "授权驱虫免疫数据,err:"+err.Error())
		err = errors.New("授权驱虫免疫数据失败")
		return
	}
	session.Commit()
	go func() {
		if err = new(petai_po.UserPetVaccinateRecord).AuthDataMedicalHistory(session, in.UserInfoId); err != nil {
			session.Rollback()
			log.Error(logPrefix, "授权宠物病例数据失败,err:"+err.Error())
			err = errors.New("授权宠物病例数据失败")
			return
		}
	}()

	log.Info(logPrefix + "授权数据成功")

	return

}

// 同步scrm新增数据到 小闻养宠助手
func (s *AuthService) SyncScrmData(userInfoId string) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()
	logPrefix := fmt.Sprintf("====同步scrm新增数据,入参:%s", userInfoId)
	log.Info(logPrefix)
	// 查询用户信息
	eshopUserInfo, err := new(petai_po.EshopUserInfo).GetUserInfoByUserId(session, userInfoId)
	if err != nil {
		session.Rollback()
		log.Error(logPrefix + fmt.Sprintf("查询eshop用户信息失败,err:%s", err.Error()))
		err = errors.New("查询用户信息失败")
		return
	}
	if eshopUserInfo == nil || len(eshopUserInfo.UserInfoId) == 0 {
		session.Rollback()
		log.Error(logPrefix + "eshop用户信息不存在")
		err = errors.New("eshop用户信息不存在")
		return
	}

	if eshopUserInfo.IsAuthorized != 1 {
		session.Rollback()
		log.Error(logPrefix + "用户未授权")
		err = errors.New("用户未授权")
		return
	}

	// 授权宠物数据
	if err = new(petai_po.UserPetInfo).AuthData(session, userInfoId); err != nil {
		session.Rollback()
		log.Error(logPrefix + "同步scrm用户宠物数据失败,err:" + err.Error())
		err = errors.New("同步用户宠物数据失败")
		return
	}

	// 授权scrm驱虫免疫数据
	if err = new(petai_po.UserPetVaccinateRecord).AuthData(session, userInfoId); err != nil {
		session.Rollback()
		log.Error(logPrefix, "同步驱虫免疫数据,err:"+err.Error())
		err = errors.New("同步驱虫免疫数据失败")
		return
	}
	session.Commit()
	go func() {
		if err = new(petai_po.UserPetVaccinateRecord).AuthDataMedicalHistory(session, userInfoId); err != nil {
			log.Error(logPrefix, "授权宠物病例数据失败,err:"+err.Error())
			err = errors.New("授权宠物病例数据失败")
			return
		}
	}()

	log.Info(logPrefix + "同步数据成功")

	return

}

// 小闻养宠助手用户信息
func (s *AuthService) UserInfo(userInfoId string) (out petai_vo.UserInfo, err error) {

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("====小闻养宠助手用户信息,入参:%s", userInfoId)
	log.Info(logPrefix)

	sel := `user_info_id,
			user_avatar,
			user_name,
			user_sex,
			user_birthday,
			country,
			province,
			city,
			area,
			first_raises_pet,
			is_authorized,
			user_mobile,
			encrypt_user_mobile,
			country_code,
			province_code,
			city_code,
			area_code
			`
	exist, err := session.Table("eshop.user_info").Select(sel).Where("user_info_id=?", userInfoId).Get(&out)
	if err != nil {
		log.Error(logPrefix + "查询用户信息失败,err:" + err.Error())
		err = errors.New("查询用户信息失败")
		return
	}
	if !exist {
		log.Error(logPrefix + "用户信息不存在")
		err = errors.New("用户信息不存在")
		return
	}

	//查询scrm是否有这个用户， 如果没有， 则为新用户
	scrmUserInfo, err := new(omnibus_po.TScrmUserInfo).GetUserByMobile(session, out.EncryptUserMobile)
	if err != nil {
		log.Error(logPrefix, "获取数据失败,err=", err.Error())
		err = errors.New("获取数据失败")
	}

	if scrmUserInfo == nil || len(scrmUserInfo.UserId) == 0 {
		out.IsNewUser = true
	}
	if len(out.UserBirthday) > 0 {
		out.UserBirthday = strings.Split(out.UserBirthday, " ")[0]
	}
	if len(out.FirstRaisesPet) > 0 {
		out.FirstRaisesPet = strings.Split(out.FirstRaisesPet, " ")[0]
	}

	return

}

func (s *AuthService) EditUserInfo(in petai_vo.UserInfo) (err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("====编辑小闻养宠助手用户信息,入参:%s", utils.JsonEncode(in))
	log.Info(logPrefix)

	cols := `id,
			user_info_id,
			user_avatar,
			user_name,
			user_sex,
			user_birthday,
			country,
			province,
			city,
			area,
			first_raises_pet,
			country_code,
			province_code,
			city_code,
			area_code
		`

	userInfo := petai_po.EshopUserInfo{
		UserAvatar:   in.UserAvatar,
		UserName:     in.UserName,
		UserSex:      in.UserSex,
		Country:      in.Country,
		Province:     in.Province,
		City:         in.City,
		Area:         in.Area,
		CountryCode:  in.CountryCode,
		ProvinceCode: in.ProvinceCode,
		CityCode:     in.CityCode,
		AreaCode:     in.AreaCode,
	}
	if len(in.UserBirthday) > 0 {
		userInfo.UserBirthday, _ = time.ParseInLocation(utils.DateLayout, in.UserBirthday, time.Local)
	}
	if len(in.FirstRaisesPet) > 0 {
		userInfo.FirstRaisesPet, _ = time.ParseInLocation(utils.DateLayout, in.FirstRaisesPet, time.Local)
	}

	_, err = session.Table("eshop.user_info").Cols(cols).Where("user_info_id=?", in.UserInfoId).Update(&userInfo)
	if err != nil {
		log.Error(logPrefix + "编辑用户信息失败,err:" + err.Error())
		err = errors.New("编辑用户信息失败")
		return
	}

	return

}
