package iobound

import (
	"context"
	inventoryPO "eShop/domain/inventory-po/inventory"
	"eShop/infra/errors"
	inventoryVO "eShop/view-model/inventory-vo/inventory"
	vo "eShop/view-model/inventory-vo/iobound"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// IoBoundDetail 商品出入库详情
type IoBoundDetail struct {
	Id                  int       `json:"id" xorm:"pk autoincr 'id'"`                                      // 主键id
	ChainId             int64     `json:"chain_id"`                                                        // 连锁id
	StoreId             string    `json:"store_id" xorm:"varchar(50) default '0'"`                         // 门店的主键
	WarehouseId         int       `json:"warehouse_id"`                                                    // 仓库id
	BoundId             int       `json:"bound_id"`                                                        // 出入库id
	BoundNo             string    `json:"bound_no" xorm:"varchar(32) default ''"`                          // 出入库单号
	BoundType           int       `json:"bound_type" xorm:"default 0"`                                     // 出库/入库类型
	ItemType            int       `json:"item_type" xorm:"default 0"`                                      // 单据类型
	ItemDetailRefId     string    `json:"item_detail_ref_id" xorm:"varchar(50) default ''"`                // 出入库关联的单据详情id
	ProductId           int       `json:"product_id" xorm:"default 0"`                                     // 商品id
	SkuId               int       `json:"sku_id" xorm:"default 0"`                                         // sku id
	ProductName         string    `json:"product_name" xorm:"varchar(255) COLLATE utf8mb4_bin default ''"` // 商品名称
	ProductType         int       `json:"product_type" xorm:"default 0"`                                   // 商品类型
	BarCode             string    `json:"bar_code" xorm:"varchar(64) default ''"`                          // 条形码
	ProductCategoryPath string    `json:"product_category_path" xorm:"varchar(255) default ''"`            // 商品分类路径
	AvgCostPrice        int       `json:"avg_cost_price" xorm:"default 0"`                                 // 出入库前平均成本价(分)
	IoPrice             int       `json:"io_price" xorm:"default 0"`                                       // 出入库单价(分)
	IoCount             int       `json:"io_count" xorm:"default 0"`                                       // 出入库数量
	IoAmount            int       `json:"io_amount" xorm:"default 0"`                                      // 出入库小计(分)
	RealIoAmount        int       `json:"real_io_amount" xorm:"default 0"`                                 // 实际出入库小计(分)
	TotalNumAfter       int       `json:"total_num_after" xorm:"default 0"`                                // 变更后总库存
	FreezeNumAfter      int       `json:"freeze_num_after" xorm:"default 0"`                               // 变更后锁定库存
	AvailableNumAfter   int       `json:"available_num_after" xorm:"default 0"`                            // 变更后可用库存
	TotalAmountAfter    int       `json:"total_amount_after" xorm:"default 0"`                             // 变更后总成本(分)
	Operator            string    `json:"operator" xorm:"varchar(255) COLLATE utf8mb4_bin default ''"`     // 操作人
	IsDeleted           bool      `json:"is_deleted" xorm:"default 0"`                                     // 删除标识:0未删除,1已删除
	CreatedTime         time.Time `json:"created_time" xorm:"default CURRENT_TIMESTAMP not null"`          // 创建时间
	UpdatedTime         time.Time `json:"updated_time" xorm:"default CURRENT_TIMESTAMP not null"`          // 修改时间
}

func (s IoBoundDetail) TableName() string {
	return "inventory_in_out_bound_detail"
}

func (s IoBoundDetail) BatchCreate(ctx context.Context, session *xorm.Session, ioBoundDetails []IoBoundDetail) error {
	_, err := session.Context(ctx).InsertMulti(ioBoundDetails)
	if err != nil {
		return errors.Wrap(err, "创建出入库详情记录失败")
	}
	return nil
}

// 计算出入库单价
func (s IoBoundDetail) CalcSellPrice(inventory inventoryPO.Inventory, ioPrice int, itemType int) (int, error) {
	//单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	switch itemType {
	case 1, 3, 4, 5, 7, 9, 11:
		return 0, nil
	case 6:
		return ioPrice, nil
	case 2, 8, 10, 12:
		return inventory.AvgCostPrice, nil
	}
	return 0, errors.New("不支持的出入库方式")
}

func (s IoBoundDetail) CalcIoBoundPrice(inventory inventoryPO.Inventory, detail vo.IoBoundDetailCreateCommand, itemType int) (int, error) {
	//单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	switch itemType {
	case 1, 9, 11: // 采购入库、其他入库、初始化入库
		return detail.IoPrice, nil
	case 2, 3, 4, 6, 7, 8, 10, 12: // 采购退货出库、取消锁定库存、锁定库存、销售出库、盘盈入库、盘亏出库、其他出库、自用出库
		return inventory.AvgCostPrice, nil
	case 5: // 销售退货入库
		if detail.IoPrice == 0 {
			return inventory.AvgCostPrice, nil
		}
		return detail.IoPrice, nil
	default:
		return 0, errors.New("不支持的出入库方式")
	}
}

func (s IoBoundDetail) CalcInventoryChange(itemType int, inventory inventoryPO.Inventory, change inventoryVO.SkuInventoryChanges) (inventoryVO.InventoryChangeCalc, error) {
	//单据类型: 1. 采购入库, 2. 采购退货出库, 3. 取消锁定库存, 4. 锁定库存, 5. 销售退货入库, 6. 销售出库, 7. 盘盈入库, 8. 盘亏出库, 9. 其它入库, 10. 其它出库, 11. 初始化入库, 12.自用出库
	switch itemType {
	case 1, 5, 7, 9, 11: // 采购入库、销售退货入库、盘盈入库、其他入库、初始化入库
		return commonInCalc(inventory, change)
	case 3: // 取消锁定库存
		return unfreezeInCalc(inventory, change)
	case 4: // 锁定库存
		return freezeOutCalc(inventory, change)
	case 6: // 销售出库
		return orderOutCalc(inventory, change)
	case 2, 8, 10, 12: // 采购退货出库、盘亏出库、其他出库、自用出库
		return commonOutCalc(inventory, change)
	default:
		return inventoryVO.InventoryChangeCalc{}, errors.New("不支持的出入库方式")
	}
}

func commonInCalc(inventory inventoryPO.Inventory, change inventoryVO.SkuInventoryChanges) (inventoryVO.InventoryChangeCalc, error) {
	changeNum := change.ChangeNum
	changeAmount := change.Price * changeNum

	totalNumAfter := inventory.TotalNum + changeNum
	freezeNumAfter := inventory.FreezeNum
	availableNumAfter := inventory.AvailableNum + changeNum
	totalAmountAfter := inventory.TotalAmount + changeAmount
	avgCostPriceAfter := inventory.AvgCostPrice

	if totalNumAfter != 0 {
		avgCostPriceAfter = totalAmountAfter / totalNumAfter
	}

	return genCalcResult(inventory, changeNum, changeAmount, totalNumAfter, freezeNumAfter, availableNumAfter, totalAmountAfter, avgCostPriceAfter)
}

func unfreezeInCalc(inventory inventoryPO.Inventory, change inventoryVO.SkuInventoryChanges) (inventoryVO.InventoryChangeCalc, error) {
	changeNum := change.ChangeNum
	changeAmount := 0

	totalNumAfter := inventory.TotalNum
	freezeNumAfter := inventory.FreezeNum - changeNum
	availableNumAfter := inventory.AvailableNum + changeNum
	totalAmountAfter := inventory.TotalAmount
	avgCostPriceAfter := inventory.AvgCostPrice

	return genCalcResult(inventory, changeNum, changeAmount, totalNumAfter, freezeNumAfter, availableNumAfter, totalAmountAfter, avgCostPriceAfter)
}

func freezeOutCalc(inventory inventoryPO.Inventory, change inventoryVO.SkuInventoryChanges) (inventoryVO.InventoryChangeCalc, error) {
	changeNum := change.ChangeNum
	changeAmount := 0

	totalNumAfter := inventory.TotalNum
	freezeNumAfter := inventory.FreezeNum + changeNum
	availableNumAfter := inventory.AvailableNum - changeNum
	totalAmountAfter := inventory.TotalAmount
	avgCostPriceAfter := inventory.AvgCostPrice

	return genCalcResult(inventory, changeNum, changeAmount, totalNumAfter, freezeNumAfter, availableNumAfter, totalAmountAfter, avgCostPriceAfter)
}

func orderOutCalc(inventory inventoryPO.Inventory, change inventoryVO.SkuInventoryChanges) (inventoryVO.InventoryChangeCalc, error) {
	changeNum := change.ChangeNum
	changeAmount := change.Price * changeNum

	totalNumAfter := inventory.TotalNum - changeNum
	freezeNumAfter := inventory.FreezeNum - changeNum
	availableNumAfter := inventory.AvailableNum
	totalAmountAfter := inventory.TotalAmount - changeAmount
	avgCostPriceAfter := inventory.AvgCostPrice

	return genCalcResult(inventory, changeNum, changeAmount, totalNumAfter, freezeNumAfter, availableNumAfter, totalAmountAfter, avgCostPriceAfter)
}

func commonOutCalc(inventory inventoryPO.Inventory, change inventoryVO.SkuInventoryChanges) (inventoryVO.InventoryChangeCalc, error) {
	changeNum := change.ChangeNum
	changeAmount := change.Price * changeNum

	totalNumAfter := inventory.TotalNum - changeNum
	freezeNumAfter := inventory.FreezeNum
	availableNumAfter := inventory.AvailableNum - changeNum
	totalAmountAfter := inventory.TotalAmount - changeAmount
	avgCostPriceAfter := inventory.AvgCostPrice

	return genCalcResult(inventory, changeNum, changeAmount, totalNumAfter, freezeNumAfter, availableNumAfter, totalAmountAfter, avgCostPriceAfter)
}

func genCalcResult(inventory inventoryPO.Inventory, changeNum int, changeAmount int, totalNumAfter int, freezeNumAfter int, availableNumAfter int, totalAmountAfter int, avgCostPriceAfter int) (inventoryVO.InventoryChangeCalc, error) {
	// 添加调试日志
	fmt.Printf("genCalcResult input freezeNumAfter: %d\n", freezeNumAfter)

	result := inventoryVO.InventoryChangeCalc{
		ChangeNum:          changeNum,
		CurrentCostPrice:   inventory.AvgCostPrice,
		ChangeAmount:       changeAmount,
		TotalNumBefore:     inventory.TotalNum,
		FreezeNumBefore:    inventory.FreezeNum,
		AvailableNumBefore: inventory.AvailableNum,
		TotalAmountBefore:  inventory.TotalAmount,
		TotalNumAfter:      totalNumAfter,
		FreezeNumAfter:     freezeNumAfter,
		AvailableNumAfter:  availableNumAfter,
		TotalAmountAfter:   totalAmountAfter,
		AvgCostPriceAfter:  avgCostPriceAfter,
	}

	// 添加调试日志
	fmt.Printf("genCalcResult output FreezeNumAfter: %d\n", result.FreezeNumAfter)

	return result, nil
}

func (s IoBoundDetail) ListByBoundId(ctx context.Context, session *xorm.Session, id int) ([]IoBoundDetail, error) {
	var ioBoundDetails []IoBoundDetail
	err := session.Context(ctx).Where("bound_id = ? AND is_deleted = 0", id).Find(&ioBoundDetails)
	if err != nil {
		return nil, errors.Wrap(err, "查询出入库详情失败")
	}
	return ioBoundDetails, nil
}

// buildPageQuery 构建分页查询条件
func buildPageQuery(ctx context.Context, session *xorm.Session, req vo.IoBoundDetailPageRequest) *xorm.Session {
	query := session.Context(ctx).Table("inventory_in_out_bound_detail").
		Alias("d").
		Join("LEFT", "inventory_in_out_bound iob", "iob.id = d.bound_id").
		Join("LEFT", "pro_product p", "d.product_id = p.id").
		Join("LEFT", "dc_dispatch.warehouse w", "d.warehouse_id = w.id").
		Where("d.warehouse_id = ?", req.WarehouseId)

	if req.BoundId > 0 {
		query = query.And("d.bound_id = ?", req.BoundId)
	}
	if len(req.BoundNo) > 0 {
		query = query.And("d.bound_no LIKE ?", "%"+req.BoundNo+"%")
	}
	if req.BoundType > 0 {
		query = query.And("d.bound_type = ?", req.BoundType)
	}
	if req.ItemType > 0 {
		query = query.And("d.item_type = ?", req.ItemType)
	}
	if len(req.ItemDetailRefId) > 0 {
		query = query.And("d.item_detail_ref_id = ?", req.ItemDetailRefId)
	}
	if req.ProductId > 0 {
		query = query.And("d.product_id = ?", req.ProductId)
	}
	if req.SkuId > 0 {
		query = query.And("d.sku_id = ?", req.SkuId)
	}
	if len(req.Query) > 0 {
		query = query.And("(d.product_name LIKE ? OR d.bar_code LIKE ? OR iob.item_ref_no LIKE ?)",
			"%"+req.Query+"%", "%"+req.Query+"%", "%"+req.Query+"%")
	}
	if len(req.Operator) > 0 {
		query = query.And("d.operator = ?", req.Operator)
	}
	if req.ProductCategoryId > 0 {
		query = query.And("p.category_id = ?", req.ProductCategoryId)
	}
	if len(req.ProductCategoryIds) > 0 {
		query = query.In("p.category_id", req.ProductCategoryIds)
	}
	if len(req.TimeStart) > 0 {
		query = query.And("d.created_time >= ?", req.TimeStart)
	}
	if len(req.TimeEnd) > 0 {
		query = query.And("d.created_time <= ?", req.TimeEnd)
	}

	query = query.And("d.is_deleted = ?", 0)

	return query
}

func (s IoBoundDetail) Page(ctx context.Context, session *xorm.Session, req vo.IoBoundDetailPageRequest) ([]vo.IoBoundDetailResponse, int64, error) {
	// 1. 构建查询条件
	query := buildPageQuery(ctx, session, req)

	// 2. 使用FindAndCount查询总数和分页数据
	var details []vo.IoBoundDetailResponse
	if len(req.Sort) > 0 {
		query = query.OrderBy(req.Sort + " " + req.Order)
	}

	total, err := query.
		Select("d.*,iob.item_ref_id,iob.item_ref_no,iob.item_ref_type,w.name as warehouse_name").
		Limit(req.Size, (req.Current-1)*req.Size).
		FindAndCount(&details)
	if err != nil {
		return nil, 0, errors.Wrap(err, "分页查询出入库明细失败")
	}

	return details, total, nil
}

func (s IoBoundDetail) Summary(ctx context.Context, session *xorm.Session, req vo.IoBoundDetailPageRequest) (int, error) {
	query := buildPageQuery(ctx, session, req)

	var totalAmount int
	_, err := query.Select("SUM(d.io_amount) as total_amount").Get(&totalAmount)
	if err != nil {
		return 0, errors.Wrap(err, "查询出入库明细失败")
	}
	return totalAmount, nil
}

func (s IoBoundDetail) ListByRefDetailIds(ctx context.Context, session *xorm.Session, itemType int, refDetailIds []string) ([]IoBoundDetail, error) {
	var ioBoundDetails []IoBoundDetail
	err := session.Context(ctx).Where("item_type = ?", itemType).In("item_detail_ref_id", refDetailIds).Find(&ioBoundDetails)
	if err != nil {
		return nil, errors.Wrap(err, "查询出入库明细失败")
	}
	return ioBoundDetails, nil
}
