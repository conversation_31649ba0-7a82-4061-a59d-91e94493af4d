package points_repo

import (
	po "eShop/domain/points-po"
	"eShop/infra/utils"
	vo "eShop/view-model/points-vo"

	"xorm.io/xorm"
)

type ClsPointsRuleRepo struct {
	SuperRepo[po.ClsPointsRule]
}

func NewClsPointsRuleRepo() ClsPointsRuleRepo {
	return ClsPointsRuleRepo{
		NewSuperRepo[po.ClsPointsRule](),
	}
}

// Page 分页查询积分规则和商品信息
func (r ClsPointsRuleRepo) Page(session *xorm.Session, queryVO vo.ClsPointsRuleQueryVO) ([]vo.MultiRuleResultVO, int64, error) {
	// 构建基础查询
	session = session.Table("cls_points_rule").
		Join("LEFT", "upetmart.upet_goods", "cls_points_rule.goods_code = upet_goods.goods_serial AND upet_goods.store_id=4")

	// 添加查询条件
	if condition := utils.GetQueryCondition(queryVO); condition != "" {
		session = session.Where(condition)
	}

	// 查询所需字段，使用条件聚合
	session = session.Select(`
		GROUP_CONCAT(cls_points_rule.id) as id,
		upet_goods.goods_commonid as spu_id,
		upet_goods.goods_id as sku_id,
		cls_points_rule.goods_code,
		upet_goods.goods_name,
		upet_goods.goods_marketprice as market_price,
		upet_goods.goods_price as price,
		cls_points_rule.status as status,
		GROUP_CONCAT(if(cls_points_rule.points > 0, cls_points_rule.points, '')) AS points,
		GROUP_CONCAT(cls_points_rule.region) as region
	`).GroupBy("cls_points_rule.goods_code")

	// 执行查询
	var results []vo.MultiRuleResultVO
	offset := queryVO.Size * (queryVO.Current - 1)
	total, err := session.Limit(queryVO.Size, offset).FindAndCount(&results)
	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}
