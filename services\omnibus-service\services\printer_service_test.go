package services

import (
	"eShop/services/common"
	"testing"
)

func TestPrinterService_PrintCashierTicket(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		orderSn string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "打印"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PrinterService{
				BaseService: tt.fields.BaseService,
			}
			if err := s.PrintCashierTicket("9964128299152378"); (err != nil) != tt.wantErr {
				t.Errorf("PrintCashierTicket() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
