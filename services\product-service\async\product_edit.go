package async

import (
	po "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	proproductstoreinfo "eShop/services/product-service/enum/pro-product-store-info"
	product_service "eShop/services/product-service/services"
	viewmode "eShop/view-model"
	omnibus_vo "eShop/view-model/omnibus-vo"
	vo "eShop/view-model/product-vo"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/cast"
)

type ProductEditService struct {
	common.BaseService
}

// mq 调试参数格式
// {"id":1312,"task_content":2,"task_status":3,"task_detail":"","operation_file_url":"{\\"data\\":[{\\"product_id\\":111010,\\"store_id\\":\\"530094312600386773\\",\\"channel_id\\":2},{\\"product_id\\":111010,\\"store_id\\":\\"530094312600386773\\",\\"channel_id\\":3}]}","request_header":"","resulte_file_url":"","create_id":"530094312600386773","create_time":"2024-09-04T16:49:53.85765+08:00","create_name":"","create_mobile":"","create_ip":"127.0.0.1","ip_location":"","success_num":0,"fail_num":0,"extended_data":"","context_data":"530094312600386773","org_id":6,"do_count":0,"err_mes":"","update_time":"2024-09-04T16:49:53.85765+08:00","operation_type":1}
// parJson  异步任务的参数JSON数据
func (h ProductEditService) OperationFunc(parJson string, org_id int) (out *viewmode.ImportResult, err error) {
	logPrefix := "同步编辑商品到第三方===="
	log.Info(logPrefix, "入参：", parJson)
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()
	
	out = new(viewmode.ImportResult)
	//200 表示处理正常，会确认MQ，
	out.Code = 200
	in := omnibus_vo.SpuSyncInfo{}
	if err = json.Unmarshal([]byte(parJson), &in); err != nil {
		out.Message = "解析参数失败"
		return
	}

	// 获取商品信息
	productIdsMap := make(map[int]int, 0)
	productIds := make([]int, 0)
	for _, v := range in.Data {
		if _, ok := productIdsMap[v.ProductId]; !ok {
			productIdsMap[v.ProductId] = 1
			productIds = append(productIds, v.ProductId)
		}
	}

	chainProductMap, err := product_service.GetChainProductData(session, productIds)
	if err != nil {
		log.Error(logPrefix, "获取连锁商品信息失败，err=", err.Error())
		out.Message = "获取连锁商品信息失败"
		return
	}

	// 记录操作结果
	channelSucMap := make(map[int]int)              //map[渠道id]成功个数
	channelFailMap := make(map[int]int)             //map[渠道id]失败个数
	channelMap := make(map[int]int)                 // 本次处理了几个渠道
	resultMap := make(map[string]ProductResultStru) //map[渠道id_店铺id_商品id_skuid]
	for _, v := range in.Data {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, v.StoreId, v.ProductId)
		channelMap[v.ChannelId] = 0
		result := ProductResultStru{
			ChannelName: common.ChannelIdMap[v.ChannelId],
			ChannelId:   v.ChannelId,
			ProductId:   v.ProductId,
			SkuId:       0,
			StoreId:     v.StoreId,
			ProductName: "",
			OperateName: "同步编辑商品到" + common.ChannelIdMap[v.ChannelId],
			SucOrFail:   "失败",
		}
		info, ok := chainProductMap[v.ProductId]
		if !ok {
			channelFailMap[v.ChannelId]++
			out.FailNum++
			result.ErrorMsg = "商品信息不存在"
			resultMap[k] = result
			continue
		}
		result.ProductName = info.Product.Name
		where := map[string]interface{}{
			"storeId":   v.StoreId,
			"productId": v.ProductId,
			"channelId": v.ChannelId,
		}

		switch v.ChannelId {
		case common.ChannelIdMT, common.ChannelIdELM:
			productService := product_service.StoreProductService{}
			if _, err = productService.BatchStoreProduct(vo.BatchStoreProductReq{
				ProductIds: cast.ToString(v.ProductId),
				Type:       proproductstoreinfo.ProductTypeUpdate,
				ChannelIds: cast.ToString(v.ChannelId),
				TenantId:   v.StoreId,
			}); err != nil {
				channelFailMap[v.ChannelId]++
				out.FailNum++
				result.ErrorMsg = err.Error()
				resultMap[k] = result
				// 更新铺品表数据
				updateData := po.ProProductStoreInfo{SyncError: err.Error(), Status: proproductstoreinfo.StatusUpdateFailed}
				if err = updateData.UpdateProductStoreInfo(h.Engine, where, "sync_error,status"); err != nil {
					log.Error(logPrefix, "更新铺品表失败,数据："+utils.InterfaceToJSON(where)+",err=", err.Error())
				}
				continue
			}
			channelSucMap[v.ChannelId]++
			out.SuccessNum++
			result.SucOrFail = "成功"
			resultMap[k] = result
			updateData := po.ProProductStoreInfo{Status: proproductstoreinfo.StatusNormal}
			if err = updateData.UpdateProductStoreInfo(h.Engine, where, "status"); err != nil {
				log.Error(logPrefix, "更新铺品表失败,数据："+utils.InterfaceToJSON(where)+",err=", err.Error())
			}
		default:
			log.Error(logPrefix, "未知的渠道,数据：", utils.InterfaceToJSON(v))
		}

	}
	log.Info(logPrefix, "操作结果：", utils.InterfaceToJSON(resultMap))
	fileName := fmt.Sprintf("同步编辑商品到第三方(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	excelObject, _ := NewExcelObject(fileName)
	out.QiniuUrl, _ = excelObject.WriteExcelAndUpload(resultMap)
	message := ""
	for k := range channelMap {
		message = fmt.Sprintf("%s|%s渠道，处理成功商品数：%d,处理失败商品数：%d|", message, common.ChannelIdMap[k], channelSucMap[k], channelFailMap[k])
	}
	out.Message = message
	out.Code = 200
	return out, nil
}
