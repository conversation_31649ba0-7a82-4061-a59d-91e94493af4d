package services

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/services/common"
)

type StatsShopDistributorDailyService struct {
	common.BaseService
}

func (s StatsShopDistributorDailyService) InsertAndGet(disId int, daily po.StatsShopDistributorDaily) (po.StatsShopDistributorDaily, error) {
	existDaily := po.StatsShopDistributorDaily{}
	startDate := daily.StatDate
	endDate := daily.EndDate
	if len(startDate) == 0 || len(endDate) == 0 {
		log.Info("StatsShopDistributorDailyService InsertAndGet，参数异常")
		return existDaily, nil
	}
	isDis := 0
	if disId != 0 {
		isDis = 1
	}

	s.Begin()
	defer s.Close()
	session := s.Session

	_, err := session.Select("*").Table("stats_shop_distributor_daily").
		Where("dis_id=? AND `type`=? AND stat_date=? AND end_date=?", disId, daily.Type, startDate, endDate).
		Get(&existDaily)
	if err != nil {
		log.Error("StatsShopDistributorDailyService InsertAndGet，查询异常", err)
		return existDaily, err
	}

	// 如果不存在，则需要统计以下字段（企业编码、企业名称、分销店铺id、分销员id、分销店铺名称），并创建一条新的记录
	if existDaily.Id == 0 {
		// 根据enterpriseCode查询企业信息
		enterpriseId := daily.EnterpriseId
		var enterpriseName string
		_, err = session.Select("enterprise_name").Table("scrm_enterprise").Where("id=?", enterpriseId).Get(&enterpriseName)
		if err != nil {
			log.Error("StatsShopDistributorDailyService InsertAndGet，查询企业信息异常", err)
			return existDaily, err
		}

		// 根据shopId查询分销店铺信息
		shopId := daily.ShopId
		var shopName string
		_, err = session.Select("shop_name").Table("shop").Where("id=?", shopId).Get(&shopName)
		if err != nil {
			log.Error("StatsShopDistributorDailyService InsertAndGet，查询企业信息异常", err)
			return existDaily, err
		}

		existDaily = po.StatsShopDistributorDaily{
			StatDate:       startDate,
			EndDate:        endDate,
			EnterpriseId:   enterpriseId,
			EnterpriseName: enterpriseName,
			ShopId:         shopId,
			DisId:          daily.DisId,
			ShopName:       shopName,
			Type:           daily.Type,
			IsDis:          isDis,
		}
		session.Insert(&existDaily)
	}

	return existDaily, nil
}
