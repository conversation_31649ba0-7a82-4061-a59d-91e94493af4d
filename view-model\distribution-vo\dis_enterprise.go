package distribution_vo

// 企业信息表
type DisEnterprise struct {
	Id               int    `json:"id" xorm:"pk autoincr not null comment('自增id') BIGINT 'id'"`
	OrgId            int    `json:"org_id" xorm:"not null default '0' comment('所属主体id') INT 'org_id'"`
	ScrmEnterpriseId int    `json:"scrm_enterprise_id" xorm:"not null default '0' comment('是R1企业表的scrm_enterprise.id') BIGINT 'scrm_enterprise_id'"`
	EnterpriseName   string `json:"enterprise_name" xorm:"not null default '' comment('企业名称') VARCHAR(128) 'enterprise_name'"`
	Province         string `json:"province" xorm:"not null default '' comment('省') VARCHAR(255) 'province'"`
	City             string `json:"city" xorm:"not null default '' comment('市') VARCHAR(255) 'city'"`
	District         string `json:"district" xorm:"not null default '' comment('区') VARCHAR(255) 'district'"`
	Address          string `json:"address" xorm:"not null default '' comment('详细地址') VARCHAR(255) 'address'"`
	Phone            string `json:"phone" xorm:"not null default '' comment('手机号') VARCHAR(20) 'phone'"`
	EncryptPhone     string `json:"encrypt_phone" xorm:"not null default '' comment('加密手机号') VARCHAR(20) 'encrypt_phone'"`
	SocialCreditCode string `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	DataSource       int    `json:"data_source" xorm:"not null default '1' comment('来源：1、自建   2 云订货') TINYINT 'data_source'"`
	CreateTime       string `json:"create_time" xorm:"not null default CURRENT_TIMESTAMP COMMENT '创建时间'"`
	UpdateTime       string `json:"update_time" xorm:"not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'"`
}
