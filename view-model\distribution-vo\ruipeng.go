package distribution_vo

import (
	"eShop/view-model"
)

type RPHospitalListReq struct {
	City string `json:"city"`
}
type RPHospitalListRes struct {
	viewmodel.BaseHttpResponse
	Data []Hospital `json:"data"`
}
type Hospital struct {
	HospitalName string `json:"hospital_name"` //医院名称
}

type AllHospitalListReq struct {
	City      string `json:"city"`
	Name      string `json:"name"`
	PageSize  int    `json:"pageSize"`
	PageIndex int    `json:"pageIndex"`
}

type AllHospitalListRes struct {
	viewmodel.BasePageHttpResponse
	Data []Hospital `json:"data"`
}
