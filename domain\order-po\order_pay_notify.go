package order_po

import (
	"eShop/infra/log"
	"errors"
	"time"

	"xorm.io/xorm"
)

// 小程序支付回调通知数据实体
type OrderPayNotify struct {
	OrderSn    string    `xorm:"not null pk comment('订单SN') VARCHAR(36)"`
	PaySn      string    `xorm:"default 'NULL' comment('支付单号') VARCHAR(36)"`
	PayMode    int32     `xorm:"not null default 0 comment('1支付宝  2微信 3美团 4其他') TINYINT(4)"`
	PayTime    time.Time `xorm:"default 'NULL' comment('支付时间') DATETIME"`
	PayAmount  int32     `xorm:"not null default 0 comment('实际支付金额') INT(11)"`
	DealNum    int32     `xorm:"not null default 0 comment('处理次数') INT(11)"`
	DealStatus int32     `xorm:"not null default 0 comment('处理状态') TINYINT(4)"`
	DealMsg    string    `xorm:"default 'NULL' comment('处理提示') VARCHAR(255)"`
	CreateTime time.Time `xorm:"default 'NULL' comment('创建时间') DATETIME created"`
	UpdateTime time.Time `xorm:"default 'NULL' comment('更新时间') DATETIME updated"`
	Source     int32     `xorm:"-"`
}

func (p OrderPayNotify) TableName() string {
	return "dc_order.order_pay_notify"
}

// 根据订单号获取TradeNo
func (p *OrderPayNotify) GetTradeNoByOrderSn(session *xorm.Session, orderSn string) (string, error) {
	orderPayNotify := &OrderPayNotify{}
	has, err := session.Where("order_sn = ?", orderSn).Get(orderPayNotify)
	if err != nil {
		log.Errorf("根据订单号获取TradeNo失败, err: %v", err)
		return "", err
	}
	if !has {
		return "", errors.New("获取支付流水号订单号失败")
	}
	return orderPayNotify.PaySn, nil
}
