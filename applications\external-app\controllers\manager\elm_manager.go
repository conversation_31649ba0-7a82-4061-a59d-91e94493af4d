package manager

import (
	"eShop/services/external-service/services"
	"eShop/view-model/external-vo/dto"
	"encoding/json"
	"net/http"
)

// @Summary 获取饿了么商品属性列表
// @Description 获取饿了么商品属性列表
// @Tags 饿了么-商品属性
// @Accept json
// @Produce json
// @Param shop_id query string true "店铺ID"
// @Param category_id query string false "分类ID"
// @Success 200 {object} dto.ElmCategoryPropertyListResponse
// @Failure 400 {object} dto.ElmCategoryPropertyListResponse
// @Router /external-app/manager/elm/property/list [get]
func GetElmCategoryPropertyList(w http.ResponseWriter, r *http.Request) {
	req := &dto.ElmCategoryPropertyListRequest{
		ShopId:     r.URL.Query().Get("shop_id"),
		CategoryId: r.URL.Query().Get("category_id"),
	}
	resp := dto.ElmCategoryPropertyListResponse{
		Code:    200,
		Message: "success",
	}

	service := services.ElmProductService{}

	out, err := service.GetElmCategoryPropertyList(req)
	if err != nil {
		resp.Code = 400
		resp.Message = err.Error()
	}
	resp.Data = out.Data
	json.NewEncoder(w).Encode(resp)
}
