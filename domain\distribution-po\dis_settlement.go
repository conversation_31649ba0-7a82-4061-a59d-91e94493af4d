package distribution_po

import (
	"eShop/infra/log"
	"time"

	"xorm.io/xorm"
)

type DisSettlement struct {
	Id              int       `json:"id" xorm:"pk autoincr not null comment('结算ID') INT 'id'"`
	OrgId           int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	ShopId          int       `json:"shop_id" xorm:"default 'null' comment('所属店铺') INT 'shop_id'"`
	Status          int       `json:"status" xorm:"default 0 comment('结算状态：0-默认,1-待结算，2-已结算') INT 'status'"`
	SettlementNo    string    `json:"settlement_no" xorm:"not null default '' comment('结算编号') VARCHAR(50) 'settlement_no'"`
	SettlementTime  time.Time `json:"settlement_time" xorm:"default 'null' comment('结算时间') DATETIME 'settlement_time'"`
	OrderNo         string    `json:"order_no" xorm:"default '' comment('订单编号') VARCHAR(50) 'order_no'"`
	OrderTime       time.Time `json:"order_time" xorm:"default 'null' comment('下单时间') DATETIME 'order_time'"`
	OrderFinishTime time.Time `json:"order_finish_time" xorm:"default 'null' comment('订单完成时间') DATETIME 'order_finish_time'"`
	GoodsId         int       `json:"goods_id" xorm:"not null comment('商品id') INT 'goods_id'"`
	GoodsName       string    `json:"goods_name" xorm:"default '' comment('商品名称') VARCHAR(255) 'goods_name'"`
	PayAmount       int       `json:"pay_amount" xorm:"default 0 comment('支付金额(分)') INT 'pay_amount'"`
	RefundAmount    int       `json:"refund_amount" xorm:"default 0 comment('退款金额(分)') INT 'refund_amount'"`
	CommissionRate  float64   `json:"commission_rate" xorm:"not null default '0.00' comment('分销佣金比例(示例：佣金比例是5%，这里存5)') DECIMAL(4) 'commission_rate'"`
	Commission      int       `json:"commission" xorm:"default 0 comment('佣金(分)') INT 'commission'"`
	DistributorId   int       `json:"distributor_id" xorm:"default 0 comment('分销员id') INT 'distributor_id'"`
	CreateTime      time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime      time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}
type FindSettCommissionStru struct {
	Commission     int    `json:"commission"`
	DisId          int    `json:"dis_id"`
	ShopId         int    `json:"shop_id"`
	ShopName       string `json:"shop_name"`
	EnterpriseName string `json:"enterprise_name"`
	EnterpriseId   int64  `json:"enterprise_id"`
}

// 获取商品分销订单的佣金、待结佣金和已结佣金字段
func FindSettCommission(db *xorm.Engine, where map[string]interface{}) (data []FindSettCommissionStru, err error) {
	log.Infof("FindSettCommission开始执行，查询条件: %+v", where)

	session := db.NewSession()
	defer session.Close()
	s := `sum(a.commission) as commission,
		a.distributor_id as dis_id,
		a.shop_id,
		b.shop_name,
		b.enterprise_id,
		c.enterprise_name`

	session = session.Table("eshop.dis_settlement").Alias("a").Select(s).
		Join("left", "eshop.shop b", "a.shop_id = b.id").
		Join("left", "eshop.scrm_enterprise c", "b.enterprise_id = c.id").
		Where("a.distributor_id>0").
		Where("a.shop_id>0")

	orgId, ok := where["orgId"]
	if ok {
		session = session.Where("a.org_id=?", orgId)
		log.Infof("添加筛选条件: orgId=%v", orgId)
	}
	status, ok := where["status"]
	if ok {
		session = session.Where("a.status = ?", status)
		log.Infof("添加筛选条件: status=%v", status)
	}
	createTimeStart, ok := where["createTimeStart"]
	if ok {
		session = session.Where("DATE(a.create_time) >= ?", createTimeStart)
		log.Infof("添加筛选条件: createTimeStart=%v", createTimeStart)
	}
	createTimeEnd, ok := where["createTimeEnd"]
	if ok {
		session = session.Where("DATE(a.create_time) <= ?", createTimeEnd)
		log.Infof("添加筛选条件: createTimeEnd=%v", createTimeEnd)
	}

	settlementTimeStart, ok := where["settlementTimeStart"]
	if ok {
		session = session.Where("DATE(a.settlement_time) >= ?", settlementTimeStart)
		log.Infof("添加筛选条件: settlementTimeStart=%v", settlementTimeStart)
	}
	settlementTimeEnd, ok := where["settlementTimeEnd"]
	if ok {
		session = session.Where("DATE(a.settlement_time) <= ?", settlementTimeEnd)
		log.Infof("添加筛选条件: settlementTimeEnd=%v", settlementTimeEnd)
	}

	shopId, ok := where["shopId"]
	if ok {
		session = session.Where("shop_id=?", shopId)
		log.Infof("添加筛选条件: shopId=%v", shopId)
	}

	disId, ok := where["disId"]
	if ok {
		session = session.Where("distributor_id=?", disId)
		log.Infof("添加筛选条件: disId=%v", disId)
	}
	groupBy, ok := where["groupBy"]
	if ok {
		session = session.GroupBy(groupBy.(string))
		log.Infof("添加分组条件: groupBy=%v", groupBy)
	}

	// 获取最终SQL语句
	sql, params := session.LastSQL()
	log.Infof("执行SQL: %s, 参数: %+v", sql, params)

	err = session.Find(&data)
	if err != nil {
		log.Errorf("FindSettCommission查询失败: %v", err)
		return
	}

	log.Infof("FindSettCommission查询成功，返回数据条数: %d", len(data))
	return
}
