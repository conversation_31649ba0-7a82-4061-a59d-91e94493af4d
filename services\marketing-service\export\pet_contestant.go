package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/marketing-service/services"
	vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

// PetContestantTask 参赛用户导出任务
type PetContestantTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.PetContestantListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 导出数据
func (e PetContestantTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.PetContestantListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	e.ExportParams.PageSize = 10000
	e.ExportParams.IsExport = false // 避免重复触发导出

	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	e.SetSheetName()
	client := new(service.PetActivityService)

	k := 0
	for {
		ret, total, err := client.ListPetContestants(e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)

			// 根据表格中的字段顺序设置行数据
			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].ScrmUserId,   // 用户ID
				ret[i].NickName,     // 用户昵称
				ret[i].Mobile,       // 用户手机号
				ret[i].RegisterTime, // 注册时间
				func() string {
					switch ret[i].CustomerType {
					case 0:
						return "新客"
					case 1:
						return "老客"
					default:
						return fmt.Sprintf("%d", ret[i].CustomerType)
					}
				}(), // 客户类型
				ret[i].FirstWorkTime, // 首次创建作品时间
				ret[i].WorkCount,     // 已生成作品数量
				func() string {
					switch ret[i].IsPk {
					case 0:
						return "否"
					case 1:
						return "是"
					default:
						return fmt.Sprintf("%d", ret[i].IsPk)
					}
				}(), // 是否参与PK
				ret[i].PkWorkId, // 当前参与PK的作品id
				ret[i].PkTime,   // 参与PK的时间
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) || k >= total {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

// SetSheetName 设置表头
func (e PetContestantTask) SetSheetName(args ...interface{}) {
	// 根据表格中的字段设置表头
	nameList := []interface{}{
		"用户ID", "用户昵称", "用户手机号", "注册时间", "客户类型", "首次创建作品时间", "已生成作品数量", "是否参与PK", "当前参与PK的作品id", "参与PK的时间",
	}
	_ = e.writer.SetRow("A1", nameList)
}

// GenerateDownUrl 生成下载链接
func (e PetContestantTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("参赛用户列表导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

// OperationFunc 操作函数（如需要）
func (e PetContestantTask) OperationFunc(row []string, orgId int) string {
	return ""
}
