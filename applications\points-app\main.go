package main

import (
	"context"
	"eShop/applications/points-app/controllers"
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/middleware"
	"eShop/infra/security"
	"eShop/infra/tracing"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/BurntSushi/toml"
	"github.com/go-chi/chi"
	httpSwagger "github.com/swaggo/http-swagger"
)

func main() {
	// 读取配置文件
	toml.DecodeFile("appsetting.toml", &config.LocalSetting)

	// 获取本机地址
	config.LocalSetting.LocalIP = config.GetCurrentIP()

	// 初始化证书
	security.InitPEM("rsa_1024_priv.pem", "rsa_1024_pub.pem", "rsa_1024_pub.pem")

	//解决时区问题
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	// 初始化缓存
	cache.CacheSources[cache_source.EShop] = cache.Address(config.Get("redis.PassAddr"))

	// 初始化日志
	log.Init()

	fmt.Println("points-app 启动成功! 端口 8157")
	server := &http.Server{Addr: "0.0.0.0:8157", Handler: service()}

	_, closer, err := tracing.InitJaeger()
	if err != nil {
		panic(err)
	}

	// Server run context
	serverCtx, serverStopCtx := context.WithCancel(context.Background())

	// Listen for syscall signals for process to interrupt/quit
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	go func() {
		<-sig

		// Shutdown signal with grace period of 30 seconds
		shutdownCtx, _ := context.WithTimeout(serverCtx, 30*time.Second)

		go func() {
			<-shutdownCtx.Done()
			closer.Close()

			if errors.Is(shutdownCtx.Err(), context.DeadlineExceeded) {
				log.Fatal("graceful shutdown timed out.. forcing exit.")
			}
		}()

		// Trigger graceful shutdown
		err := server.Shutdown(shutdownCtx)
		if err != nil {
			log.Fatal(err)
		}
		serverStopCtx()
	}()

	//初始化
	utils.InitClient()

	// Run the server
	err = server.ListenAndServe()
	if err != nil && !errors.Is(err, http.ErrServerClosed) {
		log.Fatal(err)
	}

	// Wait for server context to be stopped
	<-serverCtx.Done()
}

func service() http.Handler {
	// 使用 chi 框架
	r := chi.NewRouter()

	r.Use(middleware.WithLogger)  //用中间件去处理日志问题。未完待续
	r.Use(middleware.WithTracing) //基于jaeger的open tracing

	// jwtAuth
	jwtauth.JwtInit()
	r.Use(jwtauth.Verify(jwtauth.TokenFromHeader))
	r.Use(middleware.SetOrgId) //给所有后台请求，添加主体标识org_id

	// 初始化api
	controllers.InitAllApi(r)

	r.Mount("/swagger", httpSwagger.WrapHandler)
	return r
}
