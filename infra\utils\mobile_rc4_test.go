package utils

import "testing"

// func TestMobileEncrypt(t *testing.T) {
// 	type args struct {
// 		mobile string
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want string
// 	}{
// 		// TODO: Add test cases.
// 		{
// 			name: "手机号加密",
// 			args: args{
// 				mobile: "430502198912480505",
// 			},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			InitMobileRC4()
// 			if got := MobileEncrypt(tt.args.mobile); got != tt.want {
// 				t.Errorf("MobileEncrypt() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

func TestMobileDecrypt(t *testing.T) {
	type args struct {
		ciphertext string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "手机号解密",
			args: args{
				ciphertext: "IqlGXoRcRXntobY=",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//if got := MobileDecrypt(tt.args.ciphertext); got != tt.want {
			//	t.Errorf("MobileDecrypt() = %v, want %v", got, tt.want)
			//}
		})
	}
}

func TestIsValidIdCard(t *testing.T) {
	got := ValidateIDCard("110102196205245791")
	t.Logf("%v", got)
}

func TestValidateBankCard(t *testing.T) {
	got := ValidateBankCard("6212264000071812073")
	t.Logf("%v", got)
}

func TestCreditCodeValid(t *testing.T) {
	//got := ValidateUSCC("91440300306110974N")
	got := ValidateUSCC("9144030008463566XL")
	t.Logf("%v", got)
}
