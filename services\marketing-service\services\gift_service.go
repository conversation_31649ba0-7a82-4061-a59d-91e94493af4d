package services

import (
	marketing_po "eShop/domain/marketing-po"
	product_po "eShop/domain/product-po"
	"eShop/infra/log"
	"eShop/infra/pkg/util/cache"
	"eShop/services/common"
	marketing_vo "eShop/view-model/marketing-vo"
	"errors"
	"fmt"
	"time"
)

type GiftService struct {
	common.BaseService
}

// calculateGiftStatus 计算赠品状态
func calculateGiftStatus(isPermanent int, enableTime, expireTime *string, status int) int {
	// 如果状态已经是已结束，直接返回
	if status == 3 {
		return 3 // 已结束
	}

	if isPermanent == 1 {
		return 2 // 永久赠品默认进行中
	}

	now := time.Now().Format("2006-01-02 15:04:05")
	if now < *enableTime {
		return 1 // 未开始
	} else if now > *expireTime {
		return 3 // 已结束
	}
	return 2 // 进行中
}

// GetGiftList 获取赠品列表
func (s *GiftService) GetGiftList(param *marketing_vo.GiftListReq) (out []marketing_vo.GiftListItem, Total int, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 构建查询条件
	tableName := marketing_po.MarketingGift{}.TableName()
	query := session.Table(tableName+" g").
		Join("LEFT", "eshop.pro_sku ps", "g.sku_id = ps.id").
		Join("LEFT", "eshop.pro_product pp", "g.product_id = pp.id").
		Join("LEFT", "eshop.pro_product_store_info psi", "g.sku_id = psi.sku_id AND g.store_id = psi.store_id and psi.channel_id=?", common.ChannelIdOfflineShop)

	// 选择字段
	query = query.Select("g.*, ps.product_specs, pp.name as product_name, psi.retail_price, pp.pic")

	if param.Name != "" {
		query = query.Where("g.name LIKE ?", "%"+param.Name+"%")
	}
	if param.Status != "" {
		query = query.Where("g.status = ?", param.Status)
	}
	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}

	// 分页查询
	total, err := query.Where("g.store_id = ? AND g.is_deleted = 0", param.StoreId).
		Limit(param.PageSize, param.PageSize*(param.PageIndex-1)).
		Desc("g.created_time").
		FindAndCount(&out)
	if err != nil {
		log.Error("查询赠品列表失败，err=", err.Error())
		return
	}

	// 计算每个赠品的实时状态
	for i := range out {
		out[i].Status = calculateGiftStatus(out[i].IsPermanent, out[i].EnableTime, out[i].ExpireTime, out[i].Status)
	}
	return out, int(total), nil
}

// GetGiftList 获取生效的赠品
func (s *GiftService) GiftList(param *marketing_vo.GiftListReq) (out []marketing_po.MarketingGift, err error) {
	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()

	// 构建查询条件
	tableName := marketing_po.MarketingGift{}.TableName()
	query := session.Table(tableName)
	// 分页查询
	query = query.Where("store_id = ? AND is_deleted = 0 AND status != 3", param.StoreId).
		Where("(is_permanent = 1 OR (enable_time <= NOW() AND expire_time >= NOW()))")

	if len(param.SkuIds) > 0 {
		query = query.In("sku_id", param.SkuIds)
	}

	err = query.Desc("created_time").Find(&out)
	if err != nil {
		log.Error("查询赠品列表失败，err=", err.Error())
		return
	}
	return
}

// SaveGift 保存赠品
func (s *GiftService) SaveGift(param *marketing_vo.SaveGiftReq) error {
	logPrefix := fmt.Sprintf("保存赠品====，入参：%+v", param)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()

	redisConn := cache.GetRedisConn()
	lockKey := fmt.Sprintf("lock:save_gift_%s", param.StoreId)
	lock, err := redisConn.SetNX(lockKey, 1, 10*time.Second).Result()
	if err != nil {
		return err
	}
	if !lock {
		return errors.New("系统繁忙，请稍后再试")
	}
	defer redisConn.Del(lockKey)

	session := s.Session
	// 参数校验
	if param.IsPermanent == 0 {
		if param.EnableTime == "" || param.ExpireTime == "" {
			return errors.New("非永久赠品必须设置开始和结束时间")
		}
		// 检查时间格式和有效性
		if param.EnableTime >= param.ExpireTime {
			return errors.New("开始时间必须小于结束时间")
		}
	}

	// 1. 检查商品是否存在
	var product product_po.ProProductStoreInfo
	exists, err := session.Table("pro_product_store_info psi").
		Join("LEFT", "pro_sku ps", "psi.sku_id = ps.id").
		Where("psi.sku_id = ? AND psi.store_id = ? AND ps.is_del = 0",
			param.SkuId, param.StoreId).
		Get(&product)
	if err != nil {
		return fmt.Errorf("查询商品信息失败: %v", err)
	}
	if !exists {
		return errors.New("商品不存在")
	}
	if product.ProductType != 1 {
		return errors.New("商品类型必须是实物商品")
	}

	// 2. 检查时间段内是否存在相同商品的赠品
	var existingGift marketing_po.MarketingGift
	baseWhereStr := "sku_id = ? AND store_id = ? AND is_deleted = 0"
	if param.Id > 0 {
		baseWhereStr += fmt.Sprintf(" AND id != %d", param.Id) // 排除自己
	}

	// 先检查是否存在永久赠品
	exists, err = session.Where(baseWhereStr+" AND is_permanent = 1 and status != 3",
		param.SkuId, param.StoreId).Get(&existingGift)
	if err != nil {
		return fmt.Errorf("检查永久赠品失败: %v", err)
	}
	if exists {
		return errors.New("该商品已存在其它赠品活动中")
	}

	// 再检查是否存在有效期内的临时赠品
	if param.IsPermanent == 0 {
		whereStr := baseWhereStr + " AND enable_time <= ? AND expire_time >= ? and status != 3"
		exists, err = session.Where(whereStr,
			param.SkuId, param.StoreId,
			param.ExpireTime, param.EnableTime).Get(&existingGift)
		if err != nil {
			return fmt.Errorf("检查临时赠品失败: %v", err)
		}
		if exists {
			return errors.New("该商品已存在其它赠品活动中")
		}
	}

	// 2. 组装赠品数据
	gift := &marketing_po.MarketingGift{
		ChainId:     param.ChainId,
		StoreId:     param.StoreId,
		Name:        param.Name,
		ProductId:   param.ProductId,
		SkuId:       param.SkuId,
		IsPermanent: param.IsPermanent,
		PersonLimit: param.PersonLimit,
		RemainCount: 1, //todo 这里要改成库存
		TotalCount:  1,
		Status:      1, // 默认未开始
		EnableTime:  nil,
		ExpireTime:  nil,
	}

	if param.IsPermanent == 0 {
		gift.EnableTime = &param.EnableTime
		gift.ExpireTime = &param.ExpireTime
	}

	// 计算赠品状态
	gift.Status = calculateGiftStatus(gift.IsPermanent, gift.EnableTime, gift.ExpireTime, gift.Status)

	// 3. 新增或更新赠品
	if param.Id > 0 {
		// 更新
		gift.Id = param.Id
		cols := []string{"chain_id", "store_id", "name", "product_id", "sku_id",
			"is_permanent", "person_limit", "remain_count", "status"}

		// 根据 IsPermanent 决定是否更新时间字段
		if param.IsPermanent == 0 {
			cols = append(cols, "enable_time", "expire_time")
		} else {
			// 当设置为永久时，清空时间字段
			gift.EnableTime = nil
			gift.ExpireTime = nil
			cols = append(cols, "enable_time", "expire_time")
		}

		if _, err := session.ID(param.Id).Cols(cols...).Update(gift); err != nil {
			session.Rollback()
			log.Error(logPrefix, "更新赠品失败，err=", err.Error())
			return errors.New("更新赠品失败")
		}
	} else {
		// 新增
		if _, err = session.Insert(gift); err != nil {
			session.Rollback()
			log.Error(logPrefix, "新增赠品失败，err=", err.Error())
			return errors.New("新增赠品失败")
		}
	}
	session.Commit()
	return nil
}

// DeleteGift 删除赠品
func (s *GiftService) DeleteGift(id int64, storeId string) error {
	logPrefix := fmt.Sprintf("删除赠品====，id：%d", id)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()
	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	// 1. 检查赠品状态
	var gift marketing_po.MarketingGift
	exists, err := session.Where("id = ? AND store_id = ? AND is_deleted = 0",
		id, storeId).Get(&gift)
	if err != nil {
		return fmt.Errorf("查询赠品信息失败: %v", err)
	}
	if !exists {
		return errors.New("赠品不存在")
	}

	// 2. 更新赠品为已删除状态
	gift.IsDeleted = 1
	if _, err := session.Where("id = ? AND store_id = ?",
		id, storeId).Cols("is_deleted").Update(&gift); err != nil {
		session.Rollback()
		log.Error(logPrefix, "删除赠品失败，err=", err.Error())
		return errors.New("删除赠品失败")
	}

	return session.Commit()
}

// EndGift 结束赠品活动
func (s *GiftService) EndGift(id int64, storeId string) error {
	logPrefix := fmt.Sprintf("结束赠品活动====，id：%d", id)
	log.Info(logPrefix)

	s.Begin()
	defer s.Close()

	redisConn := cache.GetRedisConn()
	lockKey := fmt.Sprintf("lock:end_gift_%d_%s", id, storeId)
	lock, err := redisConn.SetNX(lockKey, 1, 10*time.Second).Result()
	if err != nil {
		return err
	}
	if !lock {
		return errors.New("系统繁忙，请稍后再试")
	}
	defer redisConn.Del(lockKey)

	session := s.Engine.NewSession()
	defer session.Close()
	session.Begin()

	var gift marketing_po.MarketingGift
	exists, err := session.Where("id = ? AND store_id = ? AND is_deleted = 0",
		id, storeId).Get(&gift)
	if err != nil {
		return fmt.Errorf("查询赠品信息失败: %v", err)
	}
	if !exists {
		return errors.New("赠品不存在")
	}

	if gift.Status == 3 {
		return errors.New("该赠品活动已结束")
	}

	gift.Status = 3 // 已结束
	if _, err := session.Where("id = ? AND store_id = ?",
		id, storeId).Cols("status").Update(&gift); err != nil {
		session.Rollback()
		log.Error(logPrefix, "结束赠品活动失败，err=", err.Error())
		return errors.New("结束赠品活动失败")
	}

	return session.Commit()
}

// GetGiftDetail 获取赠品详情
func (s *GiftService) GetGiftDetail(param *marketing_vo.GiftDetailReq) (*marketing_vo.GiftDetailResp, error) {
	logPrefix := fmt.Sprintf("获取赠品详情====，入参：%+v", param)
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	// 1. 查询赠品基本信息
	var gift marketing_po.MarketingGift
	exists, err := s.Engine.Where("id = ? AND store_id = ? AND is_deleted = 0", param.Id, param.StoreId).Get(&gift)
	if err != nil {
		return nil, fmt.Errorf("查询赠品信息失败: %v", err)
	}
	if !exists {
		return nil, errors.New("赠品不存在")
	}

	// 2. 查询商品信息
	var productInfo struct {
		ProductId    int     `xorm:"product_id"`
		SkuId        int     `xorm:"sku_id"`
		ProductName  string  `xorm:"product_name"`
		Barcode      string  `xorm:"bar_code"`
		ProductSpecs string  `xorm:"product_specs"`
		RetailPrice  float64 `xorm:"retail_price"`
		Stock        int     `xorm:"stock"`
	}

	exists, err = s.Engine.Table("pro_product_store_info psp").
		Join("LEFT", "pro_product pp", "psp.product_id = pp.id").
		Join("LEFT", "pro_sku ps", "psp.sku_id = ps.id").
		Select("psp.sku_id, psp.product_id, "+
			"pp.name as product_name, psp.bar_code, "+
			"ps.product_specs, psp.retail_price").
		Where("psp.sku_id = ? AND psp.store_id = ? AND psp.channel_id=?",
			gift.SkuId, gift.StoreId, common.ChannelIdOfflineShop).
		Get(&productInfo)

	if err != nil {
		return nil, fmt.Errorf("查询商品信息失败: %v", err)
	}
	if !exists {
		return nil, errors.New("商品不存在")
	}

	// 3. 组装返回数据
	resp := &marketing_vo.GiftDetailResp{
		Id:          gift.Id,
		ChainId:     gift.ChainId,
		StoreId:     gift.StoreId,
		Name:        gift.Name,
		ProductId:   gift.ProductId,
		SkuId:       gift.SkuId,
		IsPermanent: gift.IsPermanent,
		EnableTime:  gift.EnableTime,
		ExpireTime:  gift.ExpireTime,
		PersonLimit: gift.PersonLimit,
		TotalCount:  gift.TotalCount,
		UsedCount:   gift.UsedCount,
	}

	// 4. 设置商品信息
	resp.ProductInfo.ProductName = productInfo.ProductName
	resp.ProductInfo.Barcode = productInfo.Barcode
	resp.ProductInfo.ProductSpecs = productInfo.ProductSpecs
	resp.ProductInfo.RetailPrice = productInfo.RetailPrice
	resp.ProductInfo.Stock = productInfo.Stock

	// 5. 计算赠品状态
	resp.Status = calculateGiftStatus(gift.IsPermanent, gift.EnableTime, gift.ExpireTime, gift.Status)

	return resp, nil
}
