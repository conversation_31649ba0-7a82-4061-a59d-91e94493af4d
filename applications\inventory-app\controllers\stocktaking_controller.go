package controllers

import (
	jwt "eShop/infra/jwtauth"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/inventory-service/voucher"
	baseVO "eShop/view-model/inventory-vo"
	vo "eShop/view-model/inventory-vo/voucher"
	"github.com/go-chi/chi/v5"
	"net/http"
)

// StocktakingController 库存盘点控制器
type StocktakingController struct {
	service voucher.StocktakingService
}

func NewStocktakingController(service voucher.StocktakingService) *StocktakingController {
	return &StocktakingController{
		service: service,
	}
}

func (c StocktakingController) RegisterRoutes(r chi.Router) {
	r.Route("/inventory-app/stocktaking", func(r chi.Router) {
		r.Post("/page", c.Page)
		r.Get("/detail", c.Detail)
		r.Post("/summary", c.Summary)
		r.Post("/operate", c.Operate)
		r.Post("/create", c.Create)
	})

	r.Route("/inventory-app/stocktaking-detail", func(r chi.Router) {
		r.Post("/save-batch", c.BatchCreate)
		r.Post("/update", c.DetailUpdate)
		r.Post("/page-for-statistics", c.PageForStatistics)
		r.Post("/page", c.DetailPage)
		r.Get("/detail", c.DetailDetail)
		r.Post("/delete", c.DetailDelete)
	})

}

// Page 库存盘点分页
// @Summary 库存盘点分页
// @Description 获取库存盘点分页数据
// @Tags 库存盘点
// @Accept json
// @Produce json
// @Param page body vo.VoucherPageRequest true "分页请求"
// @Success 200 {object} response.Response[[]vo.VoucherResponse] "成功获取库存单据分页数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking/page [post]
func (c StocktakingController) Page(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	if len(cmd.TenantId) == 0 {
		cmd.TenantId = jwt.CtxGet[string](r.Context(), "TenantId")
	}
	page, total, err := c.service.Page(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// Detail 库存盘点详情
// @Summary 库存盘点详情
// @Description 获取库存盘点数据
// @Tags 库存盘点
// @Accept json
// @Produce json
// @Param cmd body baseVO.IdRequest true "库存单据详情请求"
// @Success 200 {object} response.Response[vo.VoucherDetailResponse] "成功获取库存单据详情数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking/detail [get]
func (c StocktakingController) Detail(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[baseVO.IdRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	detail, err := c.service.Detail(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, detail)
}

// Summary 库存盘点汇总
// @Summary 库存盘点汇总
// @Description 获取库存盘点汇总数据
// @Tags 库存盘点
// @Accept json
// @Produce json
// @Param page body vo.VoucherPageRequest true "分页请求"
// @Success 200 {object} response.Response[int] "成功获取库存单据汇总数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking/summary [post]
func (c StocktakingController) Summary(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	if len(cmd.TenantId) == 0 {
		cmd.TenantId = jwt.CtxGet[string](r.Context(), "TenantId")
	}
	summary, err := c.service.Summary(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, summary)
}

// Create 创建库存盘点
// @Summary 创建库存盘点
// @Description 创建库存盘点
// @Tags 库存盘点
// @Accept json
// @Produce json
// @Param cmd body vo.VoucherCreateCommand true "创建库存单据请求"
// @Success 200 {object} response.BaseResp "成功创建库存单据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking/create [post]
func (c StocktakingController) Create(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherCreateCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	cmd.ChainId = jwt.CtxGet[int64](r.Context(), "ChainId")
	cmd.StoreId = jwt.CtxGet[string](r.Context(), "TenantId")
	cmd.Operator = jwt.CtxGet[string](r.Context(), "UserName")
	id, err := c.service.Create(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, id)
}

// Operate 操作库存盘点
// @Summary 操作库存盘点
// @Description 操作库存盘点
// @Tags 库存盘点
// @Accept json
// @Produce json
// @Param cmd body vo.VoucherUpdateCommand true "操作库存单据请求"
// @Success 200 {object} response.BaseResp "成功操作库存单据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking/operate [post]
func (c StocktakingController) Operate(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherUpdateCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	operateResult, err := c.service.Operate(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, operateResult)
}

// BatchCreate 批量创建库存盘点详情
// @Summary 批量创建库存盘点详情
// @Description 批量创建库存盘点详情
// @Tags 库存盘点详情
// @Accept json
// @Produce json
// @Param cmd body vo.VoucherDetailBatchCreateCommand true "批量创建库存单据详情请求"
// @Success 200 {object} response.BaseResp "成功批量创建库存单据详情"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking-detail/save-batch [post]
func (c StocktakingController) BatchCreate(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherDetailBatchCreateCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	err = c.service.BatchCreate(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// DetailUpdate 批量更新库存盘点详情
// @Summary 批量更新库存盘点详情
// @Description 批量更新库存盘点详情
// @Tags 库存盘点详情
// @Accept json
// @Produce json
// @Param cmd body vo.VoucherDetailUpdateCommand true "批量更新库存单据详情请求"
// @Success 200 {object} response.BaseResp "成功批量更新库存单据详情"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking-detail/update [post]
func (c StocktakingController) DetailUpdate(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherDetailUpdateCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	err = c.service.DetailUpdate(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// PageForStatistics 库存盘点详情统计
// @Summary 库存盘点详情统计
// @Description 获取库存盘点详情统计数据
// @Tags 库存盘点详情
// @Accept json
// @Produce json
// @Param cmd body vo.VoucherDetailPageRequest true "库存单据详情统计请求"
// @Success 200 {object} response.Response[vo.DetailStatisticsResponse] "成功获取库存单据详情统计数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking-detail/page-for-statistics [post]
func (c StocktakingController) PageForStatistics(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherDetailPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	statistics, err := c.service.PageForStatistics(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, statistics)
}

// DetailPage 库存盘点详情分页
// @Summary 库存盘点详情分页
// @Description 获取库存盘点详情分页数据
// @Tags 库存盘点详情
// @Accept json
// @Produce json
// @Param cmd body vo.VoucherDetailPageRequest true "库存单据详情分页请求"
// @Success 200 {object} response.Response[[]vo.VoucherDetailResponse] "成功获取库存单据详情分页数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking-detail/page [post]
func (c StocktakingController) DetailPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.VoucherDetailPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	cmd.TenantId = jwt.CtxGet[string](r.Context(), "TenantId")
	page, total, err := c.service.DetailPage(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// DetailDetail 库存盘点详情
// @Summary 库存盘点详情
// @Description 获取库存盘点详情
// @Tags 库存盘点详情
// @Accept json
// @Produce json
// @Param cmd body baseVO.IdRequest true "库存单据详情请求"
// @Success 200 {object} response.Response[vo.VoucherDetailResponse] "成功获取库存单据详情"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking-detail/detail [get]
func (c StocktakingController) DetailDetail(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[baseVO.IdRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	detail, err := c.service.DetailDetail(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, detail)
}

// DetailDelete 删除库存盘点详情
// @Summary 删除库存盘点详情
// @Description 删除库存盘点详情
// @Tags 库存盘点详情
// @Accept json
// @Produce json
// @Param cmd body baseVO.IdRequest true "删除库存单据详情请求"
// @Success 200 {object} response.BaseResp "成功删除库存单据详情"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/stocktaking-detail/delete [post]
func (c StocktakingController) DetailDelete(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[baseVO.IdRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	err = c.service.DetailDelete(r.Context(), cmd.Id)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}
