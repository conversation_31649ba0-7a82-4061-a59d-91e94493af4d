package order_po

import (
	"time"

	"xorm.io/xorm"
)

type TimeCardOrder struct {
	// 主键
	Id int64 `json:"id" xorm:"pk autoincr 'id' comment('主键')"`
	// 租户号
	TenantId int64 `json:"tenant_id" xorm:"not null default 0 'tenant_id' comment('租户号')"`
	// 基础卡id;基础卡id
	CardInfoId int64 `json:"card_info_id" xorm:"not null default 0 'card_info_id' comment('基础卡id;基础卡id')"`
	// 卡id;卡id
	CardId int64 `json:"card_id" xorm:"not null default 0 'card_id' comment('卡id;卡id')"`
	// 卡名称
	CardName string `json:"card_name" xorm:"not null default '' varchar(100) 'card_name' comment('卡名称')"`
	// 卡类型;卡类型: TIME_CARD;
	CardType string `json:"card_type" xorm:"not null default '' varchar(16) 'card_type' comment('卡类型;卡类型: TIME_CARD;')"`
	// 卡记录id;卡id
	RecordId int64 `json:"record_id" xorm:"not null default 0 'record_id' comment('卡记录id;卡id')"`
	// 业务类型;购买;续充
	BizType string `json:"biz_type" xorm:"not null default '' varchar(255) 'biz_type' comment('业务类型;购买;续充')"`
	// 订单金额
	OrderAmount float64 `json:"order_amount" xorm:"not null default 0.0000 decimal(18,4) 'order_amount' comment('订单金额')"`
	// 关联订单id
	OrderId int64 `json:"order_id" xorm:"not null default 0 'order_id' comment('关联订单id')"`
	// 关联订单号
	OrderNo string `json:"order_no" xorm:"not null default '' varchar(255) 'order_no' comment('关联订单号')"`
	// 状态;进行中; 成功; 失败
	Status string `json:"status" xorm:"not null default '' varchar(255) 'status' comment('状态;进行中; 成功; 失败')"`
	// 购买次数
	Num int `json:"num" xorm:"not null default 0 'num' comment('购买次数')"`
	// 赠送次数
	NumGift int `json:"num_gift" xorm:"not null default 0 'num_gift' comment('赠送次数')"`
	// 客户id
	CustomerId int64 `json:"customer_id" xorm:"not null default 0 'customer_id' comment('客户id')"`
	// 客户手机号
	CustomerMobile string `json:"customer_mobile" xorm:"not null default '' varchar(255) 'customer_mobile' comment('客户手机号')"`
	// 客户名称
	CustomerName string `json:"customer_name" xorm:"not null default '' varchar(255) 'customer_name' comment('客户名称')"`
	// 收银员id
	SellerId int64 `json:"seller_id" xorm:"not null default 0 'seller_id' comment('收银员id')"`
	// 收银员名
	SellerName string `json:"seller_name" xorm:"not null default '' varchar(64) 'seller_name' comment('收银员名')"`
	// 备注
	Remark string `json:"remark" xorm:"not null default '' varchar(255) 'remark' comment('备注')"`
	// 是否退单
	RefundFlag bool `json:"refund_flag" xorm:"not null default false 'refund_flag' comment('是否退单')"`
	// 退单金额
	RefundAmount float64 `json:"refund_amount" xorm:"not null default 0.0000 decimal(18,4) 'refund_amount' comment('退单金额')"`
	// 关联退单id
	RefundOrderId int64 `json:"refund_order_id" xorm:"not null default 0 'refund_order_id' comment('关联退单id')"`
	// 卡退的次数
	RefundCardNum int `json:"refund_card_num" xorm:"not null default 0 'refund_card_num' comment('卡退的次数')"`
	// 卡退的次数-赠送次数
	RefundCardNumGift int `json:"refund_card_num_gift" xorm:"not null default 0 'refund_card_num_gift' comment('卡退的次数-赠送次数')"`
	// 收银员id
	RefundSellerId int64 `json:"refund_seller_id" xorm:"not null default 0 'refund_seller_id' comment('收银员id')"`
	// 是否删除
	IsDeleted bool `json:"is_deleted" xorm:"not null default 0 'is_deleted' comment('是否删除')"`
	// 创建人
	CreatedBy int64 `json:"created_by" xorm:"not null default 0 'created_by' comment('创建人')"`
	// 创建时间
	CreatedTime time.Time `json:"created_time" xorm:"not null default CURRENT_TIMESTAMP created 'created_time' comment('创建时间')"`
	// 更新人
	UpdatedBy int64 `json:"updated_by" xorm:"not null default 0 'updated_by' comment('更新人')"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time" xorm:"not null default CURRENT_TIMESTAMP updated 'updated_time' comment('更新时间')"`
}

func (m *TimeCardOrder) TableName() string {
	return "eshop_saas.m_time_card_order"
}

type TimeCardOrderRequest struct {
	OrderSn string `json:"order_sn"`
}

// GetTimeCardOrderByOrderSn 根据订单编号获取次卡订单信息
func (m *TimeCardOrder) GetTimeCardOrderByOrderSn(session *xorm.Session, orderSn string) (*TimeCardOrder, error) {
	var order TimeCardOrder
	has, err := session.Where("order_no = ? AND is_deleted = ?", orderSn, 0).Get(&order)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &order, nil
}
