package validate

import (
	"errors"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zhTranslations "github.com/go-playground/validator/v10/translations/zh"
)

var (
	validate *validator.Validate
	trans    ut.Translator
	once     sync.Once
)

// initValidator 初始化验证器和翻译器
func initValidator() {
	once.Do(func() {
		// 初始化验证器
		validate = validator.New(validator.WithRequiredStructEnabled())
		validate.RegisterValidation("chinese4", validateChineseMax4)

		// 初始化翻译器
		zh := zh.New()
		uni := ut.New(zh, zh)
		trans, _ = uni.GetTranslator("zh")

		// 注册默认的中文翻译
		zhTranslations.RegisterDefaultTranslations(validate, trans)

		// 注册自定义翻译
		validate.RegisterTranslation("chinese4", trans,
			// 注册翻译
			func(ut ut.Translator) error {
				return ut.Add("chinese4", "{0}只能输入1-4个中文字符", true)
			},
			// 翻译函数
			func(ut ut.Translator, fe validator.FieldError) string {
				t, _ := ut.T("chinese4", fe.Field())
				return t
			},
		)
	})
}

// Validate 自定义校验方法，用于处理同一struct在不同接口中，需要遵循不同的校验规则
// 使用方式：struct的字段上标签： `validate: "add:required"` 代表只有add规则是校验必填 对应调用则为：Validate(s, "add)
func Validate(s interface{}, rule string) error {
	// 确保验证器已初始化
	initValidator()
	outMap := make(map[string]string)
	out := ""
	// 定义实体内容
	var data = make(map[string]interface{})

	// 定义校验规则
	var rules = make(map[string]interface{})

	// 递归，解析出参数的数据以及定义中的tag
	recursiveStruct(s, rule, &data, &rules)

	// 调用validator包的校验方法，进行校验
	errs := validate.ValidateMap(data, rules)
	if len(errs) > 0 {
		for field, err := range errs {
			if validationErrors, ok := err.(validator.ValidationErrors); ok {
				for _, e := range validationErrors {
					fieldName := getFieldLabel(s, field, rule) // 获取字段的中文标签
					outMap[field] = strings.Replace(e.Translate(trans), e.Field(), fieldName, 1)
				}
			}
		}
		for _, v := range outMap {
			out += v + ";"
		}
		return errors.New(out)
	}
	return nil
}

// 递归解析参数内容
func recursiveStruct(s interface{}, rule string, data *map[string]interface{}, rules *map[string]interface{}) {
	t := reflect.TypeOf(s)
	v := reflect.ValueOf(s)
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)
		typ := field.Type

		// 获取并填入校验条件
		vTag := field.Tag.Get("validate")
		fieldName := field.Name
		if len(vTag) == 0 {
			continue
		}

		if typ == reflect.TypeOf(time.Time{}) {
			fieldName := field.Name
			(*data)[fieldName] = value.Interface().(time.Time)
			tag := getVTag(vTag, rule)
			(*rules)[fieldName] = tag
		} else if typ.Kind() == reflect.Struct {
			// 递归调用
			recursiveStruct(value, rule, data, rules)
		} else {
			(*data)[fieldName] = value.Interface()
			tag := getVTag(vTag, rule)
			(*rules)[fieldName] = tag
		}
	}
}

// 解析tag内容，支持以下格式：validate:"xxx"|validate:"rule:xxx;rule1:xxx|..." rule/rule1代表自定义的规则名称，xxx代表校验条件，支持validator框架自带条件
func getVTag(vTag string, rule string) interface{} {
	// rule:xxx;rule1:xxxx ==> slice["rule:xxx", "rule1:xxx"]
	elems := strings.Split(vTag, ";")
	ruleTagMap := make(map[string]string)
	for _, elem := range elems {
		// rule:xxx ==> map["rule", "xxx"]
		keyVal := strings.Split(elem, ":")
		if len(keyVal) == 1 {
			// 调用没有指定rule，则将""视为一种规则
			ruleTagMap[""] = keyVal[0]
		} else if len(keyVal) == 2 {
			ruleTagMap[keyVal[0]] = keyVal[1]
		}
	}

	//
	if len(ruleTagMap) == 0 {
		return vTag
	}
	if tag, has := ruleTagMap[rule]; has {
		return tag
	} else {
		return ""
	}
}

// 新增获取字段中文标签的函数
func getFieldLabel(s interface{}, fieldName string, rule string) string {
	t := reflect.TypeOf(s)
	if field, ok := t.FieldByName(fieldName); ok {
		// 优先使用 label 标签，如果没有则尝试使用 json 标签
		label := field.Tag.Get("label")
		if label != "" {
			tag := getVTag(label, rule)
			return tag.(string)
		}
		json := field.Tag.Get("json")
		if json != "" {
			// 如果json标签包含逗号，只取第一部分
			return strings.Split(json, ",")[0]
		}
		return fieldName
	}
	return fieldName
}

// validateChineseMax4 验证只能输入中文且最多4个字
func validateChineseMax4(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return true // 如果是空值则通过验证
	}

	count := 0
	for _, r := range []rune(value) {
		// 判断是否为中文字符
		if r < 0x4E00 || r > 0x9FFF {
			return false // 不是中文字符，验证失败
		}
		count++
		if count > 4 {
			return false // 超过4个字，验证失败
		}
	}
	return true
}
