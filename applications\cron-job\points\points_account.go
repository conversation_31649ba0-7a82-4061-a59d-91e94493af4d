package points

import (
	"eShop/infra/log"
	service "eShop/services/points-service"
	cachekey "eShop/services/points-service/enum"
)

// 积分即将过期提醒，每月月底前7天
func PointsExpireAlert() {
	lock(cachekey.PointsExpireAlert, func() {
		s := service.NewClsPointsAccountService()
		err := s.PointsExpireAlert(nil)
		if err != nil {
			log.Error("积分即将过期提醒失败", err)
		}
	})
}

// 赠送积分即将过期提醒，提前3天
func GivenPointsExpireAlert() {
	lock(cachekey.GivenPointsExpireAlert, func() {
		s := service.NewClsPointsAccountService()
		err := s.GivenPointsExpireAlert(nil)
		if err != nil {
			log.Error("赠送积分即将过期提醒失败", err)
		}
	})
}

// 注册宠利扫员工帐号
func RegisterClsWorker() {
	lock(cachekey.RegisterClsWorker, func() {
		s := service.NewClsPointsAccountService()
		err := s.RegisterClsWorker(nil, "")
		if err != nil {
			log.Error("", err)
		}
	})
}
