package petai_po

import (
	"eShop/infra/log"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

const (
	CollectedTypeHealthFile = 1 // 健康档案
	CollectedTypePetInfo    = 2 // 宠物信息
)

// 消息扩展表 (注意：记录采集到的数据来源于哪条消息)
type PetaiMessageExtend struct {
	Id             int       `json:"id"  xorm:"pk autoincr 'id' comment('主键')"` //自增id
	ConversationId int       `json:"conversation_id"`                           //会话id即petai_conversation.id
	MessageUuid    string    `json:"message_uuid"`                              // 消息uuid ,petai_message.uuid
	CollectedType  int       `json:"collected_type"`                            // 消息被采集为的数据类型：1-健康档案 2-病史记录 3-资料库 4-宠物信息
	CorrelationId  string    `json:"correlation_id"`                            //collected_type=1时，对应user_pet_vaccinate_record.id;collected_type=2时，对应petai_med_record.id;collected_type=3时，user_pet_info.pet_info_id
	CreateTime     time.Time `json:"create_time" xorm:"created"`                //创建时间
	UpdateTime     time.Time `json:"update_time" xorm:"updated"`                //更新时间

}

func (s *PetaiMessageExtend) TableName() string {
	return "eshop.petai_message_extend"
}

func (s *PetaiMessageExtend) Insert(session *xorm.Session) (err error) {
	logPrefix := fmt.Sprintf("====新增消息扩展表,消息uuid为%s", s.MessageUuid)
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if s == nil {
		err = errors.New("消息扩展表对象为空")
		return
	}
	if s.MessageUuid == "" {
		err = errors.New("消息uuid不能为空")
		return
	}
	if s.CollectedType == 0 {
		err = errors.New("消息被采集为的数据类型不能为空")
		return
	}
	if s.CorrelationId == "" {
		err = errors.New("消息被采集为的数据id不能为空")
		return
	}
	if _, err = session.Insert(s); err != nil {
		log.Error(logPrefix + "新增消息扩展表失败,err:" + err.Error())
		return
	}
	return
}

// // 根据会话id获取健康记录
// func (s *PetaiMessageExtend) GetByConversationId(session *xorm.Session, conversationId int) (out []*PetaiMessageExtend, err error) {
// 	if session == nil {
// 		err = errors.New("session is nil")
// 		return
// 	}

// 	if conversationId == 0 {
// 		err = errors.New("conversationId is 0")
// 		return
// 	}
// 	out = make([]*PetaiMessageExtend, 0)
// 	if err = session.Table("eshop.petai_message_extend").Where("conversation_id=?", conversationId).Find(&out); err != nil {
// 		log.Error("根据会话id获取消息采集扩展失败,err:" + err.Error())
// 		return
// 	}
// 	return
// }

// // 根据会话id获取消息扩展map
// func (s *PetaiMessageExtend) GetMessageExtendMap(session *xorm.Session, conversationId int) (out map[int]*PetaiMessageExtend, err error) {
// 	if session == nil {
// 		err = errors.New("session is nil")
// 		return
// 	}

// 	if conversationId == 0 {
// 		err = errors.New("conversationId is 0")
// 		return
// 	}
// 	out = make(map[int]*PetaiMessageExtend)
// 	list, err := s.GetByConversationId(session, conversationId)
// 	if err != nil {
// 		log.Error("根据会话id获取消息扩展失败,err:" + err.Error())
// 		return
// 	}
// 	for _, item := range list {
// 		out[item.CollectedType] = item
// 	}
// 	return
// }
