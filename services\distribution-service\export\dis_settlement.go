package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type DisSettlementTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.GetDisSettlementListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// 结算导出
func (e *DisSettlementTask) DataExport(taskParams string) (success_num int, fail_num int, err error) {
	e.ExportParams = new(vo.GetDisSettlementListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	fail_num = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName(e.ExportParams.OrgId)
	client := services.DisSettlementService{}

	k := 0
	for {

		ret, _, err := client.GetDisSettlementList(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++

			axis := fmt.Sprintf("A%d", k+1)
			//结算状态 结算编号 结算时间	订单编号	下单时间	订单完成时间	支付金额(元)	退款金额(元)	商品名称 商品skuId 电商店铺	佣金比例	分销佣金(元)	分销员ID	分销员姓名  线下企业ID 绑定线下企业
			d := []interface{}{
				ret[i].StatusText,
				ret[i].SettlementNo,
				ret[i].SettlementTime,
				ret[i].OrderNo,
				ret[i].OrderTime,
				ret[i].OrderFinishTime,
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].PayAmount)),
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].RefundAmount)),
				ret[i].GoodsName,
				ret[i].GoodsId,
				enum.OrgMap[ret[i].OrgId],
				ret[i].CommissionRate,
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].Commission)),
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].DisCommisAmount)),
				ret[i].DistributorId,
				ret[i].DistributorName,
			}
			if e.ExportParams.OrgId == enum.OrgId {
				d = append(d, []string{ret[i].EnterpriseId,
					ret[i].EnterpriseName})
			}
			_ = e.writer.SetRow(axis, d)
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	success_num = k
	_ = e.writer.Flush()
	return

}

// 结算导出头
func (e *DisSettlementTask) SetSheetName(args ...interface{}) {

	nameList := []interface{}{
		"结算状态", "结算编码", "结算时间", "订单编号", "下单时间", "订单完成时间", "支付金额(元)", "退款金额(元)", "商品名称", "商品skuId", "电商店铺", "佣金比例", "分销佣金(元)", "分销员ID", "分销员姓名", "线下企业ID", "绑定线下企业",
	}
	if len(args) > 0 && args[0] == enum.BLKYOrgId {
		nameList = []interface{}{
			"结算状态", "结算编码", "结算时间", "订单编号", "下单时间", "订单完成时间", "支付金额(元)", "退款金额(元)", "商品名称", "商品skuId", "电商店铺", "佣金比例", "分销佣金(元)", "扣除佣金(元)", "分销员ID", "分销员姓名",
		}
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e DisSettlementTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("结算导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (h DisSettlementTask) OperationFunc(row []string, org_id int) (msg string) {
	return ""
}
