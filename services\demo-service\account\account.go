package account

import (
	"eShop/domain/demo-po/account"
	viewmodel "eShop/view-model"
)

// CreateCommand 创建账户命令
type CreateCommand struct {
	ID      string
	Balance float64
}

// TransferCommand 转账命令
type TransferCommand struct {
	FromID string
	ToID   string
	Amount float64
}

// WithdrawCommand 提现命令
type WithdrawCommand struct {
	ID     string
	Amount float64
}

// DepositCommand 存款命令
type DepositCommand struct {
	ID     string
	Amount float64
}

// QueryParams 查询参数
type QueryParams struct {
	viewmodel.BasePageHttpResponse
	Status     string
	StartTime  int64
	PageNumber int
	PageSize   int
}

// QueryResult 查询结果
type QueryResult struct {
	Items []*account.Account `json:"items"`
	Total int64              `json:"total"`
}
