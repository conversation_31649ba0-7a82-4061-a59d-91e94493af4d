 
 package base_service

import (
	po "eShop/domain/distribution-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
)

type OrgService struct {
	common.BaseService
}

func (s OrgService) OrgPage(req vo.OrgPageReq) ([]vo.OrgPageData, int, error) {
	s.<PERSON>gin()
	defer s.Close()

	session := s.Session
	var list []vo.OrgPageData

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	session.Table("datacenter.organization_info")
	if len(req.OrgName) > 0 {
		session.And("org_name LIKE ?", "%"+req.OrgName+"%")
	}
	if req.IsUse > -1 {
		session.And("is_use = ?", req.IsUse)
	}

	limit := req.PageSize
	start := (req.PageIndex - 1) * req.PageSize
	total, err := session.Limit(limit, start).FindAndCount(&list)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return nil, 0, err
	}

	return list, cast.ToInt(total), nil
}

func (s OrgService) OrgAdd(req vo.OrgAddReq) (int, error) {
	org := po.OrganizationInfo{}
	copier.Copy(&org, req)
	org.IsUse = 1 //默认启用
	now := time.Now()
	org.CreateTime = now
	org.UpdateTime = now

	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Engine.NewSession()
	session.Begin()

	session.Table("datacenter.organization_info")
	_, err := session.Insert(&org)
	if err != nil {
		log.Error("新增组织，插入数据库异常：e=" + err.Error())
		return 0, errors.New("新增组织，插入数据库异常：e=" + err.Error())
	}

	// 创建组织，
	err = utils.CreateESIndex(cast.ToString(org.Id))
	if err != nil {
		log.Error("新增组织，创建ES索引异常：e=", err.Error())
		session.Rollback()
		return 0, errors.New("新增组织，创建ES索引异常：e=" + err.Error())
	}

	session.Commit()
	return org.Id, nil
}

func (s OrgService) OrgEdit(req vo.OrgEditReq) error {
	org := po.OrganizationInfo{}
	copier.Copy(&org, req)
	org.UpdateTime = time.Now()

	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	session.Table("datacenter.organization_info")
	_, err := session.Update(&org, &po.OrganizationInfo{Id: org.Id})
	if err != nil {
		log.Error("新增组织，插入数据库异常：e=" + err.Error())
		return errors.New("新增组织，插入数据库异常：e=" + err.Error())
	}

	return nil
}

func (s OrgService) OrgIsUse(req vo.OrgIsUseReq) error {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session

	_, err := session.Exec("UPDATE datacenter.organization_info SET is_use=?,update_time=NOW() WHERE id=?", req.IsUse, req.Id)
	if err != nil {
		log.Error("启用、停用组织，修改数据库异常：e=" + err.Error())
		return errors.New("启用、停用组织异常：e=" + err.Error())
	}

	return nil
}

func (s OrgService) OrgGet(req vo.OrgPageData) (vo.OrgPageData, error) {
	s.Begin()
	defer s.Close()
	// 将子上下文传入Session
	session := s.Session
	out := vo.OrgPageData{}
	_, err := session.Table("datacenter.organization_info").Where("zl_org_id=?", req.ZlOrgId).Get(&out)
	if err != nil {
		log.Error("查询组织对应关系失败：e=" + err.Error())
		return out, errors.New("启用、停用组织异常：e=" + err.Error())
	}
	return out, nil
}

func (s OrgService) OrgDetail(id int) (vo.OrgDetailData, error) {
	s.Begin()
	defer s.Close()

	session := s.Session
	var data vo.OrgDetailData

	session.Table("datacenter.organization_info")
	_, err := session.Where("id=?", id).Get(&data)
	if err != nil {
		log.Error("查询分销员列表失败：err=", err.Error())
		return data, err
	}

	return data, nil
}
