package inventory

import (
	"context"
	"eShop/infra/errors"
	vo "eShop/view-model/inventory-vo/inventory"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"

	"xorm.io/xorm"
)

// Inventory 库存
type Inventory struct {
	Id                  int       `json:"id" xorm:"pk autoincr 'id'"` // 主键id
	ChainId             int64     `json:"chain_id"`                   // 连锁id
	StoreId             string    `json:"store_id"`                   // 门店的主键
	WarehouseId         int       `json:"warehouse_id"`               // 仓库id
	ProductId           int       `json:"product_id"`                 // 商品id
	SkuId               int       `json:"sku_id"`                     // sku id
	ProductName         string    `json:"product_name"`               // 商品名称
	ProductType         int       `json:"product_type"`               // 商品类型
	BarCode             string    `json:"bar_code"`                   // 条形码
	ProductCategoryPath string    `json:"product_category_path"`      // 商品分类路径
	AvgCostPrice        int       `json:"avg_cost_price"`             // 平均成本价(分)
	TotalAmount         int       `json:"total_amount"`               // 总成本(分)
	TotalNum            int       `json:"total_num"`                  // 总库存
	FreezeNum           int       `json:"freeze_num"`                 // 锁定库存
	AvailableNum        int       `json:"available_num"`              // 可用库存
	AvgDailySales       int       `json:"avg_daily_sales"`            // 平均日销量(分)
	IsDeleted           int       `json:"is_deleted"`                 // 删除标识:0未删除,1已删除
	CreatedTime         time.Time `json:"created_time"`               // 创建时间
	UpdatedTime         time.Time `json:"updated_time"`               // 修改时间
}

func (i Inventory) ListBySkuIds(ctx context.Context, session *xorm.Session, warehouseId int, skuIds []int) ([]Inventory, error) {
	var inventories []Inventory
	err := session.Context(ctx).
		Table("inventory").
		And("warehouse_id = ?", warehouseId).
		In("sku_id", skuIds).
		And("is_deleted = 0").
		Find(&inventories)

	if err != nil {
		return nil, errors.Wrap(err, "查询库存失败")
	}
	return inventories, nil
}

func (i Inventory) BatchUpdate(ctx context.Context, session *xorm.Session, inventories []Inventory) error {
	// 遍历
	for _, inventory := range inventories {
		inventory.UpdatedTime = time.Now()
		_, err := session.Context(ctx).Table("inventory").ID(inventory.Id).Cols("avg_cost_price,total_amount,total_num,freeze_num,available_num,avg_daily_sales,updated_time").Update(inventory)
		if err != nil {
			session.Rollback()
			return errors.Wrap(err, "更新库存失败")
		}
	}

	return nil
}

func (i Inventory) BatchCreate(ctx context.Context, session *xorm.Session, cmd vo.InventoryInitCommand) ([]Inventory, error) {
	if len(cmd.Details) == 0 {
		return nil, errors.NewBadRequest("初始化库存商品不能为空")
	}

	// 1. 获取所有cmd.Details中的所有skuIds过滤0且去重
	skuIds := lo.Uniq(lo.Filter(lo.Map(cmd.Details, func(change vo.InventoryInitDetailCommand, _ int) int {
		return change.SkuId
	}), func(skuId int, _ int) bool {
		return skuId != 0
	}))

	if len(skuIds) != len(cmd.Details) {
		return nil, errors.NewBadRequest("请勿保存相同skuId的商品库存信息")
	}

	// 2. 查询已存在的库存记录
	exists, err := i.ListBySkuIds(ctx, session, cmd.WarehouseId, skuIds)
	if err != nil {
		return nil, err
	}

	// 4. 准备需要新增的记录
	inventories := make([]Inventory, 0)
	noProduct := make([]string, 0)
	for _, detail := range cmd.Details {
		// 查询skuId对应的商品信息
		//product, err := i.GetProductBySkuId(ctx, session, detail.SkuId)
		//if err != nil || product.SkuId == 0 {
		//	// 商品不存在，跳过
		//	noProduct = append(noProduct, detail.ProductName)
		//	continue
		//}
		// 如果detail.skuId在exist中存在，则跳过
		if lo.ContainsBy(exists, func(inventory Inventory) bool {
			return inventory.SkuId == detail.SkuId
		}) {
			continue
		}

		inventory := Inventory{
			ChainId:             cmd.ChainId,
			StoreId:             cmd.StoreId,
			WarehouseId:         cmd.WarehouseId,
			ProductId:           detail.ProductId,
			SkuId:               detail.SkuId,
			ProductName:         detail.ProductName,
			ProductType:         detail.ProductType,
			BarCode:             detail.BarCode,
			ProductCategoryPath: detail.ProductCategoryPath,
			AvgCostPrice:        detail.AvgCostPrice,
			TotalAmount:         0,
			TotalNum:            0,
			FreezeNum:           0,
			AvailableNum:        0,
			AvgDailySales:       0,
			IsDeleted:           0,
			CreatedTime:         time.Now(),
			UpdatedTime:         time.Now(),
		}
		inventories = append(inventories, inventory)
	}

	if len(noProduct) > 0 {
		return nil, errors.NewBadRequest(fmt.Sprintf("商品:%s不存在，请先创建商品", strings.Join(noProduct, ",")))
	}

	// 5. 批量插入新记录
	if len(inventories) > 0 {
		_, err = session.InsertMulti(&inventories)
		if err != nil {
			return nil, errors.Wrap(err, "批量初始化商品库存失败")
		}
	}

	return inventories, nil
}

func (i Inventory) Page(ctx context.Context, session *xorm.Session, cmd vo.InventoryPageRequest) ([]vo.InventoryResponse, int64, error) {
	var inventories []vo.InventoryResponse

	query := session.Table("inventory").Alias("i").
		Join("left", "pro_sku s", "s.id = i.sku_id").
		Join("left", "pro_product p", "p.id = s.product_id").
		Join("left", "dc_dispatch.warehouse w", "w.id = i.warehouse_id").
		Where("i.warehouse_id = ?", cmd.WarehouseId)

	if cmd.ProductCategoryId > 0 {
		query = query.And("p.category_id = ?", cmd.ProductCategoryId)
	}

	if cmd.Query != "" {
		query = query.And("p.name LIKE ? OR s.bar_code LIKE ?", "%"+cmd.Query+"%", "%"+cmd.Query+"%")
	}

	if cmd.IsOos == 1 {
		query = query.And("i.available_num = 0")
	} else if cmd.IsOos == 2 {
		query = query.And("i.available_num > 0")
	}
	query = query.And("i.is_deleted = 0")

	query.Select(`i.*,w.name as warehouse_name`)
	if len(cmd.Sort) > 0 {
		query = query.OrderBy("i." + cmd.Sort + " " + cmd.Order)
	}

	offset := (cmd.Current - 1) * cmd.Size
	total, err := query.Limit(cmd.Size, offset).FindAndCount(&inventories)
	if err != nil {
		return nil, 0, errors.Wrap(err, "查询库存失败")
	}

	return inventories, total, nil
}

type GetSkuInventoryByStoreReq struct {
	SkuId       int
	SkuIds      []int
	StoreId     string
	WarehouseId int
}

// GetSkuInventoryByStore 获取某个店铺里，在某个仓库里，某个sku的库存数量
// out[店铺id_仓库id_skuid]库存信息
// out1[店铺id_仓库id_productid]该商品所有sku的总库存之和
// out2[店铺id_仓库id_productid]该商品所有sku的锁定库存之和
// out3[店铺id_仓库id_productid]该商品所有sku的可用库存之和
func (i Inventory) GetSkuInventoryByStore(ctx context.Context, session *xorm.Session, cmd GetSkuInventoryByStoreReq) (out map[string]*Inventory, out1 map[string]int, out2 map[string]int, out3 map[string]int, err error) {
	list := make([]*Inventory, 0)
	session = session.Table("eshop.inventory")
	if cmd.SkuId > 0 {
		session = session.Where("sku_id=?", cmd.SkuId)
	}
	if len(cmd.SkuIds) > 0 {
		session = session.In("sku_id", cmd.SkuIds)
	}

	if cmd.StoreId != "" {
		session = session.Where("store_id=?", cmd.StoreId)
	}
	if cmd.WarehouseId > 0 {
		session = session.Where("warehouse_id=?", cmd.WarehouseId)
	}
	if err = session.Find(&list); err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "查询数据库失败")
	}

	out = make(map[string]*Inventory, 0)
	out1 = make(map[string]int)
	out2 = make(map[string]int)
	out3 = make(map[string]int)
	for _, v := range list {
		out[fmt.Sprintf("%s_%d_%d", v.StoreId, v.WarehouseId, v.SkuId)] = v
		out1[fmt.Sprintf("%s_%d_%d", v.StoreId, v.WarehouseId, v.ProductId)] += v.TotalNum
		out2[fmt.Sprintf("%s_%d_%d", v.StoreId, v.WarehouseId, v.ProductId)] += v.FreezeNum
		out3[fmt.Sprintf("%s_%d_%d", v.StoreId, v.WarehouseId, v.ProductId)] += v.AvailableNum

	}
	return out, out1, out2, out3, nil
}

func (i Inventory) GetProductBySkuId(ctx context.Context, session *xorm.Session, storeId string, skuId int) (vo.ProductSkuInfoResponse, error) {
	var productSku vo.ProductSkuInfoResponse
	_, err := session.Context(ctx).
		Table("pro_sku").Alias("s").
		Join("left", "pro_product p", "p.id = s.product_id").
		Join("left", "inventory i", "i.sku_id = s.id").
		Join("left", "pro_product_store_info ppsi", "ppsi.store_id = i.store_id AND ppsi.sku_id = s.id AND ppsi.channel_id=100").
		Join("LEFT", "eshop.inventory_location il", "il.warehouse_id = i.warehouse_id AND il.sku_id=s.id").
		Select(`
			s.id as sku_id,
			s.sku_code,
			s.bar_code,
			s.store_price AS market_price,
			ppsi.retail_price as sell_price,
			s.basic_price,
			s.weight_for_unit as weight,
			s.store_unit_key,
			s.store_unit,
			s.product_specs,
			s.product_specs as product_specs_type,
			s.create_date as created_time,
			p.id as product_id,
			p.chain_id,
			p.name as product_name,
			p.category_id,
			p.category_name,
			p.category_nav as category_name_path,
			p.pic as img,
			p.is_discount,
			p.product_type,
			p.brand_id,
			p.brand_name,
			p.supplier_id,
			p.supplier_name,
			COALESCE(i.available_num, 0) as can_sell_num,
			COALESCE(i.total_num, 0) as sku_inventory_num,
			il.code AS location_code
		`).
		Where("i.store_id = ? AND s.id = ?", storeId, skuId).
		Get(&productSku)

	if err != nil {
		return vo.ProductSkuInfoResponse{}, errors.Wrap(err, "查询商品sku失败")
	}
	return productSku, nil
}

// 缺货预警分页查询（字段补全）
func (i Inventory) WarningPage(ctx context.Context, session *xorm.Session, req vo.InventoryWarningRequest, warningQty int) ([]vo.InventoryWarningResponse, int64, error) {
	var inventories []vo.InventoryWarningResponse

	query := session.Table("eshop.inventory").Alias("i").
		Join("left", "eshop.pro_sku s", "s.id = i.sku_id").
		Join("left", "eshop.pro_product p", "p.id = s.product_id").
		Join("left", "dc_dispatch.warehouse w", "w.id = i.warehouse_id").
		Join("left", "eshop.inventory_location il", "il.warehouse_id = i.warehouse_id AND il.sku_id = i.sku_id").
		Where("i.is_deleted = 0").
		And("i.available_num < ?", warningQty)

	if req.StoreId != "" {
		query = query.And("i.store_id = ?", req.StoreId)
	}
	if req.WarehouseCategory > 0 {
		query = query.And("w.category = ?", req.WarehouseCategory)
	}
	if req.ProductCategoryId > 0 {
		query = query.And("p.category_id_offline = ?", req.ProductCategoryId)
	}
	if req.Query != "" {
		query = query.And("(p.name LIKE ? OR s.bar_code LIKE ?)", "%"+req.Query+"%", "%"+req.Query+"%")
	}

	query.Select(`
		i.id,
		i.chain_id,
		i.store_id,
		i.warehouse_id,
		w.name as warehouse_name,
		i.product_id,
		i.sku_id,
		p.name as product_name,
		s.bar_code,
		s.product_specs,
		il.code as location_code,
		p.category_nav as product_category_path,
		s.store_unit,
		i.avg_cost_price,
		i.available_num,
p.pic as images,
w.category
	`)
	offset := (req.Page - 1) * req.Size
	total, err := query.Limit(req.Size, offset).FindAndCount(&inventories)
	if err != nil {
		return nil, 0, err
	}
	return inventories, total, nil
}
