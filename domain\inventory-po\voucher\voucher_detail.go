package voucher

import (
	"context"
	"eShop/infra/errors"
	baseVO "eShop/view-model/inventory-vo"
	vo "eShop/view-model/inventory-vo/voucher"
	"time"

	"xorm.io/xorm"
)

type VoucherDetail struct {
	Id               int       `json:"id" xorm:"'id' pk autoincr comment('主键id')"`
	ChainId          int64     `json:"chain_id" xorm:"'chain_id' comment('连锁id')"`
	StoreId          string    `json:"store_id" xorm:"'store_id' default('0') comment('门店id')"`
	WarehouseId      int       `json:"warehouse_id" xorm:"'warehouse_id' comment('仓库id')"`
	VoucherId        int       `json:"voucher_id" xorm:"'voucher_id' comment('单据id')"`
	ProductId        int       `json:"product_id" xorm:"'product_id' comment('商品id')"`
	SkuId            int       `json:"sku_id" xorm:"'sku_id' comment('sku id')"`
	Price            int       `json:"price" xorm:"'price' default(0) comment('单价(分)')"`
	Quantity         int       `json:"quantity" xorm:"'quantity' default(0) comment('计划数量')"`
	TotalNum         int       `json:"total_num" xorm:"'total_num' default(0) comment('当前系统库存')"`
	TotalNumSnapshot int       `json:"total_num_snapshot" xorm:"'total_num_snapshot' default(0) comment('总库存数量快照')"`
	Amount           int       `json:"amount" xorm:"'amount' default(0) comment('计划金额小计(分)')"`
	ActualNum        int       `json:"actual_num" xorm:"'actual_num' default(0) comment('实际数量')"`
	ActualAmount     int       `json:"actual_amount" xorm:"'actual_amount' default(0) comment('实际金额小计(分)')"`
	TotalNumBefore   int       `json:"total_num_before" xorm:"'total_num_before' default(0) comment('操作前库存')"`
	TotalNumAfter    int       `json:"total_num_after" xorm:"'total_num_after' default(0) comment('操作后库存')"`
	ProfitStatus     int       `json:"profit_status" xorm:"'profit_status' default(0) comment('盈亏状态: 0-无盈亏, 1-盈利, 2-亏损, 3-平账')"`
	ChangeNum        int       `json:"change_num" xorm:"'change_num' default(0) comment('变更数量')"`
	ChangeAmount     int       `json:"change_amount" xorm:"'change_amount' default(0) comment('变更金额(分)')"`
	AvgCostPrice     int       `json:"avg_cost_price" xorm:"'avg_cost_price' default(0) comment('当前平均成本价')"`
	Reason           string    `json:"reason" xorm:"'reason' default('') comment('原因(退货原因/盘点差异原因)')"`
	Remark           string    `json:"remark" xorm:"'remark' default('') comment('备注')"`
	IsDeleted        int       `json:"is_deleted" xorm:"'is_deleted' default(0) comment('删除标识:0未删除,1已删除')"`
	CreatedTime      time.Time `json:"created_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time' created"`
	UpdatedTime      time.Time `json:"updated_time" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'updated_time' updated"`
}

// TableName 表名
func (p VoucherDetail) TableName() string {
	return "inventory_voucher_detail"
}

func (p VoucherDetail) Insert(ctx context.Context, session *xorm.Session) error {
	_, err := session.Context(ctx).Insert(p)
	if err != nil {
		return errors.Wrap(err, "创建库存单据详情失败")
	}
	return nil
}

// Update 更新库存单据详情
func (p VoucherDetail) Update(ctx context.Context, session *xorm.Session) error {
	_, err := session.Context(ctx).ID(p.Id).AllCols().Update(p)
	if err != nil {
		return errors.Wrap(err, "更新库存单据详情失败")
	}
	return nil
}

// Delete 删除库存单据详情（软删除）
func (p VoucherDetail) Delete(ctx context.Context, session *xorm.Session) error {
	p.IsDeleted = 1
	_, err := session.Context(ctx).ID(p.Id).Cols("is_deleted", "updated_time").Update(p)
	if err != nil {
		return errors.Wrap(err, "删除库存单据详情失败")
	}
	return nil
}

// GetById 根据ID获取库存单据详情
func (p VoucherDetail) GetById(ctx context.Context, session *xorm.Session, id int) (VoucherDetail, error) {
	var details VoucherDetail
	_, err := session.Context(ctx).Where("id = ?", id).Get(&details)
	if err != nil {
		return details, errors.Wrap(err, "查询库存单据详情失败")
	}
	return details, nil
}

// Query 查询库存单据详情列表
func (p VoucherDetail) Query(ctx context.Context, session *xorm.Session) ([]VoucherDetail, error) {
	var details []VoucherDetail
	err := session.Context(ctx).Where("is_deleted = 0").Find(&details)
	if err != nil {
		return nil, errors.Wrap(err, "查询库存单据详情列表失败")
	}
	return details, nil
}

func (p VoucherDetail) CountBySkuId(ctx context.Context, session *xorm.Session, voucherId int, skuIds []int) (int64, error) {
	return session.Context(ctx).Table("inventory_voucher_detail").
		Where("voucher_id = ? AND is_deleted=0", voucherId).In("sku_id", skuIds).
		Count()
}

func (p VoucherDetail) BatchCreate(ctx context.Context, session *xorm.Session, details []VoucherDetail) error {
	if len(details) > 0 {
		_, err := session.Context(ctx).InsertMulti(details)
		if err != nil {
			return errors.Wrap(err, "创建库存单据详情失败")
		}
	}
	return nil
}

func (p VoucherDetail) PageForStatistics(ctx context.Context, session *xorm.Session, voucherId int) (vo.DetailStatisticsResponse, error) {
	var response vo.DetailStatisticsResponse

	_, err := session.Context(ctx).Table("inventory_voucher_detail").
		Where("voucher_id = ? AND is_deleted=0", voucherId).
		Select("COUNT(1) AS total_num,COUNT(IF(actual_num = -1, null, 1)) as done_total_num,COUNT(IF(actual_num != -1, null, 1)) as undone_total_num").
		Get(&response)
	if err != nil {
		return response, errors.Wrap(err, "统计库存单据详情失败")
	}

	return response, nil
}

func (p VoucherDetail) Page(ctx context.Context, session *xorm.Session, cmd vo.VoucherDetailPageRequest) ([]vo.VoucherDetailResponse, int64, error) {
	var voucherDetails []vo.VoucherDetailResponse

	// 构建查询条件
	query := session.Context(ctx).Table("inventory_voucher_detail").Alias("ivd").
		Join("LEFT", "inventory_voucher iv", "iv.id = ivd.voucher_id").
		Join("LEFT", "dc_dispatch.warehouse w", "w.id = ivd.warehouse_id").
		Join("LEFT", "pro_sku ps", "ps.id = ivd.sku_id").
		Where("ivd.store_id=? AND iv.voucher_type=3 AND ivd.is_deleted=0", cmd.TenantId)

	// 添加时间查询条件
	if cmd.WarehouseId > 0 {
		query = query.And("ivd.warehouse_id = ?", cmd.WarehouseId)
	}
	// 单据id
	if cmd.VoucherId > 0 {
		query = query.And("ivd.voucher_id = ?", cmd.VoucherId)
	}
	// 盈亏状态
	if cmd.ProfitStatus > 0 {
		query = query.And("ivd.profit_status = ?", cmd.ProfitStatus)
	}
	// 商品条形码
	if len(cmd.BarCode) > 0 {
		query = query.And("ps.bar_code LIKE ?", "%"+cmd.BarCode+"%")
	}

	query.Select("ivd.*,ivd.total_num,ivd.actual_num,w.name as warehouse_name")

	// 执行分页查询
	offset := (cmd.Current - 1) * cmd.Size
	total, err := query.Limit(cmd.Size, offset).FindAndCount(&voucherDetails)
	if err != nil {
		return nil, 0, errors.Wrap(err, "分页查询盘点记录失败")
	}

	return voucherDetails, total, nil
}

func (p VoucherDetail) Detail(ctx context.Context, session *xorm.Session, cmd baseVO.IdRequest) (vo.VoucherDetailResponse, error) {
	var voucherDetail vo.VoucherDetailResponse

	// 构建查询条件
	query := session.Context(ctx).Table("inventory_voucher_detail").Alias("ivd").
		Where("ivd.id=?", cmd.Id)

	_, err := query.Select("ivd.*,ivd.quantity AS total_num,ivd.actual_num").Get(&voucherDetail)
	if err != nil {
		return voucherDetail, errors.Wrap(err, "查询盘点明细记录失败")
	}

	return voucherDetail, nil
}

func (p VoucherDetail) DetailDelete(ctx context.Context, session *xorm.Session, id int) error {
	p.IsDeleted = 1
	p.UpdatedTime = time.Now()
	_, err := session.Context(ctx).Exec("UPDATE inventory_voucher_detail SET is_deleted=1, updated_time=? WHERE id=?", time.Now(), id)
	if err != nil {
		return errors.Wrap(err, "删除库存单据详情失败")
	}
	return nil
}

func (p VoucherDetail) DeleteByVoucherId(ctx context.Context, session *xorm.Session, voucherId int) error {
	p.IsDeleted = 1
	p.UpdatedTime = time.Now()
	_, err := session.Context(ctx).Table("inventory_voucher_detail").
		Where("voucher_id = ?", voucherId).Cols("is_deleted", "updated_time").
		Update(p)
	if err != nil {
		return errors.Wrap(err, "删除指定单据下的出入库单据明细失败")
	}
	return nil
}

func (p VoucherDetail) ListByVoucherId(ctx context.Context, session *xorm.Session, voucherId int) ([]VoucherDetail, error) {
	var details []VoucherDetail
	err := session.Context(ctx).Table("inventory_voucher_detail").
		Where("voucher_id = ?", voucherId).And("is_deleted = 0").
		Find(&details)
	if err != nil {
		return details, errors.Wrap(err, "查询指定单据下的出入库单据明细失败")
	}
	return details, nil
}

func (p VoucherDetail) CountInit(ctx context.Context, session *xorm.Session, voucherId int) (int64, error) {
	count, err := session.Context(ctx).Table("inventory_voucher_detail").
		Where("voucher_id = ?", voucherId).And("is_deleted=0").And("actual_num = -1").Count()
	if err != nil {
		return 0, errors.Wrap(err, "查询出入库单据明细的初始化数据数量失败")
	}

	return count, nil
}

func (p VoucherDetail) ListChangeNumDetail(ctx context.Context, session *xorm.Session, voucherId int) ([]vo.StocktakingDetailChangeNumResultVO, error) {
	var changeNumDetails []vo.StocktakingDetailChangeNumResultVO

	// 构建查询条件
	query := session.Context(ctx).Table("inventory_voucher_detail").Alias("ivd").
		Join("LEFT", "inventory i", "i.warehouse_id=ivd.warehouse_id AND i.sku_id=ivd.sku_id").
		Join("LEFT", "pro_product pp", "pp.id=ivd.product_id").
		Where("ivd.voucher_id = ? AND ivd.is_deleted = 0 AND ivd.total_num_snapshot != i.total_num", voucherId)

	// 执行查询
	err := query.Select("ivd.id, ivd.product_id, ivd.sku_id, ivd.total_num_snapshot, ivd.actual_num, i.total_num, pp.name AS product_name, pp.pic AS product_img").Find(&changeNumDetails)
	if err != nil {
		return nil, errors.Wrap(err, "查询库存单据详情变更数量失败")
	}

	return changeNumDetails, nil
}

func (p VoucherDetail) UpdateSnapShot(ctx context.Context, session *xorm.Session, voucherId int) error {
	// 构建更新语句
	_, err := session.Exec("UPDATE inventory_voucher_detail ivd "+
		"LEFT JOIN inventory i ON i.warehouse_id=ivd.warehouse_id AND i.sku_id=ivd.sku_id "+
		"SET ivd.total_num_snapshot = i.total_num "+
		"WHERE ivd.voucher_id = ? AND ivd.is_deleted = 0 AND i.is_deleted = 0", voucherId)
	if err != nil {
		return errors.Wrap(err, "更新库存单据详情快照失败")
	}
	return nil
}

func (p VoucherDetail) BatchUpdateById(ctx context.Context, session *xorm.Session, details []VoucherDetail) error {
	if len(details) == 0 {
		return nil
	}

	for _, detail := range details {
		_, err := session.Context(ctx).ID(detail.Id).Cols(
			"quantity", "total_num", "total_num_snapshot", "amount", "actual_num", "actual_amount",
			"total_num_before", "total_num_after", "profit_status", "change_num", "change_amount",
			"avg_cost_price", "reason", "remark", "is_deleted", "updated_time",
		).Update(detail)
		if err != nil {
			return errors.Wrap(err, "批量更新库存单据详情失败")
		}
	}

	return nil
}
