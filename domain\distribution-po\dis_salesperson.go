package distribution_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

type DisSalesman struct {
	Id            int       `json:"id" xorm:"pk autoincr not null comment('业务员ID') INT 'id'"`
	Name          string    `json:"name" xorm:"default '' comment('业务员姓名') VARCHAR(50) 'name'"`
	Mobile        string    `json:"mobile" xorm:"default '' comment('业务员手机号') VARCHAR(50) 'mobile'"`
	EncryptMobile string    `json:"encrypt_mobile" xorm:"default '' comment('加密手机号') VARCHAR(20) 'encrypt_mobile'"`
	EmployeeNo    string    `json:"employee_no" xorm:"default '' comment('员工编号') VARCHAR(50) 'employee_no'"`
	OrgId         int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	RegionId      int       `json:"region_id" xorm:"default 0 comment('所属组织id') INT 'region_id'"`
	ShopId        int       `json:"shop_id" xorm:"default 0 comment('所属店铺id') INT 'shop_id'"`
	BarCode       string    `json:"bar_code" xorm:"default 'null' comment('二维码URL') VARCHAR(100) 'bar_code'"`
	PromoterNum   int       `json:"promoter_num" xorm:"default 0 comment('推广分销员数量') INT 'promoter_num'"`
	Status        int       `json:"status" xorm:"default 2 comment('状态：1-禁用，2-启用') INT 'status'"`
	CreateTime    time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'create_time' created"`
	UpdateTime    time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' DATETIME 'update_time' updated"`
}

// 我们这边的补充业务员表
type DisSalesperson struct {
	SalespersonId int64     `json:"salesperson_id" xorm:"pk not null comment('scrm_salesperson.id字段(供应链scrm.scrm_salesperson表)') BIGINT 'salesperson_id'"`
	OrgId         int       `json:"org_id" xorm:"not null default 0 comment('所属主体id') INT 'org_id'"`
	BarCode       string    `json:"bar_code" xorm:"not null default '' comment('二维码URL') VARCHAR(127) 'bar_code'"`
	TotalOrderNum int       `json:"total_order_num" xorm:"not null default 0 comment('累计分销单数') INT 'total_order_num'"`
	TotalSales    int       `json:"total_sales" xorm:"not null default 0 comment('累计分销销售额') INT 'total_sales'"`
	TuokeDisNum   int       `json:"tuoke_dis_num" xorm:"not null default 0 comment('拓客分销员数量') INT 'tuoke_dis_num'"`
	ServiceDisNum int       `json:"service_dis_num" xorm:"not null default 0 comment('服务分销员数量') INT 'service_dis_num'"`
	ServiceEntNum int       `json:"service_ent_num" xorm:"not null default 0 comment('服务企业数') INT 'service_ent_num'"`
	CreateTime    time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime    time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

// 供应链的原始表
type ScrmSalesperson struct {
	Id           int64     `json:"id" xorm:"pk not null BIGINT 'id'"`
	OrgCode      string    `json:"org_code" xorm:"not null comment('组织编码') VARCHAR(20) 'org_code'"`
	OrgName      string    `json:"org_name" xorm:"not null comment('组织名称') VARCHAR(20) 'org_name'"`
	Code         string    `json:"code" xorm:"default 'null' comment('业务员编码') VARCHAR(20) 'code'"`
	Name         string    `json:"name" xorm:"not null comment('业务员名称') VARCHAR(100) 'name'"`
	Phone        string    `json:"phone" xorm:"not null comment('手机号') VARCHAR(13) 'phone'"`
	Status       int8      `json:"status" xorm:"not null comment('状态（0停用 1启用）') TINYINT 'status'"`
	UserId       int       `json:"user_id" xorm:"default 'null' comment('系统用户id') BIGINT 'user_id'"`
	CreateTime   time.Time `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
	CreateUser   string    `json:"create_user" xorm:"not null default '' comment('创建人') VARCHAR(20) 'create_user'"`
	CreateUserId int       `json:"create_user_id" xorm:"not null comment('创建人id') BIGINT 'create_user_id'"`
	UpdateTime   time.Time `json:"update_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time'"`
	UpdateUser   string    `json:"update_user" xorm:"not null default '' comment('更新人') VARCHAR(20) 'update_user'"`
	UpdateUserId int       `json:"update_user_id" xorm:"not null comment('更新人id') BIGINT 'update_user_id'"`
	OrgId        int       `json:"org_id" xorm:"not null default 0 comment('所属主体id') INT 'org_id'"`
}

type CntStru struct {
	Cnt    int `json:"cnt"`
	Status int `json:"status"`
}

// 计算条数
func CntSalesperson(db *xorm.Engine, where map[string]interface{}) (out []CntStru, err error) {
	session := db.NewSession()
	defer session.Close()
	out = make([]CntStru, 0)
	session.Table("eshop.scrm_salesperson").Select("count(*) as cnt,status")
	status, ok := where["status"]
	if ok {
		session = session.Where("status=?", status)
	}
	createTimeS, ok := where["createTimeStart"]
	if ok {
		session = session.Where("create_time>=?", createTimeS)
	}
	createTimeE, ok := where["createTimeEnd"]
	if ok {
		session = session.Where("create_time<=?", createTimeE)
	}
	groupBy, ok := where["groupBy"]
	if ok {
		session = session.GroupBy(groupBy.(string))
	}
	err = session.Find(&out)
	return
}

//ScrmSalesperson的拓展上加BarCode的字段

type ScrmSalespersonExt struct {
	ScrmSalesperson `xorm:"extends"`
	BarCode         string `json:"bar_code" xorm:"not null default '' comment('二维码URL') VARCHAR(127) 'bar_code'"`
}

// GetDistributorEnterpriseList 获取分销员关联的企业列表
func GetDistributorEnterpriseList(db *xorm.Engine, orgId int, memberId int) ([]struct {
	Id             int    `xorm:"id"`              // 分销员ID
	OrgId          int    `xorm:"org_id"`          // 主体ID
	ShopId         int    `xorm:"shop_id"`         // 店铺ID
	IsDefault      int8   `xorm:"is_default"`      // 是否默认
	EnterpriseName string `xorm:"enterprise_name"` // 企业名称
}, error) {
	var result []struct {
		Id             int    `xorm:"id"`
		OrgId          int    `xorm:"org_id"`
		ShopId         int    `xorm:"shop_id"`
		IsDefault      int8   `xorm:"is_default"`
		EnterpriseName string `xorm:"enterprise_name"`
	}

	err := db.Table("eshop.dis_distributor").Alias("d").
		Join("LEFT", []string{"eshop.shop", "s"}, "d.shop_id = s.id AND d.org_id = s.org_id").
		Join("LEFT", []string{"eshop.scrm_enterprise", "e"}, "s.enterprise_id = e.id").
		Select("d.id, d.org_id, d.shop_id, d.is_default, e.enterprise_name").
		Where("d.org_id = ? AND d.member_id = ?", orgId, memberId).
		Find(&result)

	return result, err
}

type GetSalespersonInfoReq struct {
	Name string `json:"name"`
}

// 根据业务员名称 查询业务员信息
func (s *ScrmSalesperson) GetSalesperson(session *xorm.Session, req GetSalespersonInfoReq) (out ScrmSalesperson, err error) {
	has, err := session.Table("eshop.scrm_salesperson").Where("name = ?", req.Name).Cols("id", "name", "phone").Get(&out)
	if err != nil {
		return ScrmSalesperson{}, err
	}
	if !has {
		return ScrmSalesperson{}, errors.New("业务员不存在")
	}
	return out, nil
}

// 根据业务员名称 ，获取业务员id
func (s *ScrmSalesperson) GetSalespersonId(session *xorm.Session, req GetSalespersonInfoReq) (int64, error) {
	out, err := s.GetSalesperson(session, req)
	if err != nil {
		return 0, err
	}
	return out.Id, nil
}
