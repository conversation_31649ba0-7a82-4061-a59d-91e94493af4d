package account

import (
	"eShop/domain/demo-po/account"
	"eShop/infra/log"
	"encoding/json"
)

// HandleEvent 处理事件
func (s *AccountService) HandleEvent(data []byte) error {
	var event account.AccountCreatedEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return err
	}

	switch event.EventType {
	case account.EventTypeAccountCreated:
		return s.handleAccountCreated(data)
	case account.EventTypeAccountCredited:
		return s.handleAccountCredited(data)
	case account.EventTypeAccountDebited:
		return s.handleAccountDebited(data)
	case account.EventTypeAccountTransferred:
		return s.handleAccountTransferred(data)
	default:
		log.Infof("Unknown event type: %s", event.EventType)
		return nil
	}
}

func (s *AccountService) handleAccountCreated(data []byte) error {
	var event account.AccountCreatedEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return err
	}

	log.Infof("Account created: ID=%s, Balance=%.2f",
		event.AggregateID, event.Balance)
	return nil
}

func (s *AccountService) handleAccountCredited(data []byte) error {
	var event account.AccountCreditedEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return err
	}

	log.Infof("Account credited: ID=%s, Amount=%.2f, Balance=%.2f",
		event.AggregateID, event.Amount, event.Balance)
	return nil
}

func (s *AccountService) handleAccountDebited(data []byte) error {
	var event account.AccountDebitedEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return err
	}

	log.Infof("Account debited: ID=%s, Amount=%.2f, Balance=%.2f",
		event.AggregateID, event.Amount, event.Balance)
	return nil
}

func (s *AccountService) handleAccountTransferred(data []byte) error {
	var event account.AccountTransferredEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return err
	}

	log.Infof("Account transferred: FromID=%s, ToID=%s, Amount=%.2f",
		event.FromID, event.ToID, event.Amount)
	return nil
}
