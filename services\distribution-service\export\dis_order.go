package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	upetorders "eShop/services/distribution-service/enum/upet-orders"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type DisOrderTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.GetDisOrderListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

func (e *DisOrderTask) DataExport(taskParams string) (success_num int, fail_num int, err error) {
	e.ExportParams = new(vo.GetDisOrderListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	fail_num = 0
	e.ExportParams.PageIndex = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 10000

	//使用流式写入，会更节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	//设置表头
	e.SetSheetName(e.ExportParams.OrgId)
	client := services.DisOrderService{}

	k := 0
	for {
		ret, _, err := client.GetDisOrderList(*e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1

		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)

			// 下单方式
			disType := ""
			if ret[i].DisType == 1 {
				disType = "分销链接"
			} else if ret[i].DisType == 2 || ret[i].DisType == 3 {
				disType = "分销海报"
			} else if ret[i].DisType == 5 {
				disType = "粉丝关系自主"
			}

			//"订单编号","下单时间","订单完成时间",	"订单状态","订单金额(元)", "运费金额(元)", "订单退款金额(元)", "电商店铺", "订单分销金额(元)", "买家ID", "买家账号", "买家手机号", "分销员ID", "分销员姓名", "绑定线下企业","企业编码","业务员ID/姓名",
			d := []interface{}{
				cast.ToString(ret[i].OrderSn),
				ret[i].OrderAddTime,
				ret[i].OrderFinishTime,
				upetorders.StateMap[ret[i].OrderState],
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].OrderAmount)),
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].ShippingFee)),
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].RefundAmount)),
				enum.OrgMap[ret[i].StoreId],
				fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].OrderDisAmount)),
				ret[i].BuyerId,
				ret[i].BuyerName,
				ret[i].BuyerPhone,
				ret[i].DistributorId,
				ret[i].DistributorName,
				ret[i].EnterpriseName,
				fmt.Sprintf("%d", ret[i].EnterpriseId),
				ret[i].SalespersonIdName,
				disType,
				ret[i].TuokeSalespersonIdName,
			}

			if e.ExportParams.OrgId == enum.BLKYOrgId {
				//ret[i].SwlmStatus  物流码状态 1 已使用 2 已恢复
				SwlmStatusText := ""
				if ret[i].SwlmStatus == 1 {
					SwlmStatusText = "已使用"
				} else if ret[i].SwlmStatus == 0 {
					SwlmStatusText = "已恢复"
				}

				d = []interface{}{
					cast.ToString(ret[i].OrderSn),
					ret[i].OrderAddTime,
					ret[i].OrderFinishTime,
					upetorders.StateMap[ret[i].OrderState],
					fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].OrderAmount)),
					enum.OrgMap[ret[i].StoreId],
					fmt.Sprintf("%.2f", utils.Fen2Yuan(ret[i].OrderDisAmount)),
					ret[i].DistributorId,
					ret[i].DistributorName,
					SwlmStatusText,
				}
			}
			_ = e.writer.SetRow(axis, d)
		}
		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}
	success_num = k
	_ = e.writer.Flush()
	return

}

// 分销订单导出头
func (e *DisOrderTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"订单编号", "下单时间", "订单完成时间", "订单状态", "订单金额(元)", "运费金额(元)", "订单退款金额(元)", "电商店铺", "订单分销金额(元)", "买家ID", "买家账号", "买家手机号", "分销员ID", "分销员姓名", "绑定线下企业", "企业编码", "业务员ID/姓名", "下单方式", "注册业务员",
	}

	if len(args) > 0 && args[0] == enum.BLKYOrgId {
		nameList = []interface{}{
			"订单编号", "下单时间", "订单完成时间", "订单状态", "订单金额(元)", "电商店铺", "订单分销金额(元)", "分销员ID", "分销员姓名", "物流码状态",
		}
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e DisOrderTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("分销订单导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (h DisOrderTask) OperationFunc(row []string, org_id int) (msg string) {
	return ""
}
