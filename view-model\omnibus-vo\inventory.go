package omnibus_vo

import "time"

type QueryStockReq struct {
	// 商品sku
	SkuIds []int64 `protobuf:"varint,1,rep,packed,name=sku_ids,json=skuIds,proto3" json:"sku_ids"`
	// 商品id
	ProductIds []int64 `protobuf:"varint,2,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids"`
	// 门店财务编码，channel_id = 5 时不用传
	ShopId string `protobuf:"bytes,3,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
}

type QueryStockRes struct {
	// 200 成功  400 失败
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 库存，map，key为skuid，value为库存
	Stock map[int64]VInventory `json:"stock"`
}

// 库存结构体
type VInventory struct {
	SkuId int64 `json:"sku_id"`
	//可用库存
	AvailableNum int32 `json:"available_num"`
	// 库位
	LocationCode string `json:"location_code"`
}

// 库存结构体
type VIdRelation struct {
	SkuId     int64 `json:"sku_id" xorm:"not null comment('sku id') BIGINT 'sku_id'"`
	ProductId int64 `json:"product_id" xorm:"not null comment('商品id') BIGINT 'product_id'"`
	BaseSkuId int64 `json:"base_sku_id" xorm:"not null comment('base_sku_id') BIGINT 'base_sku_id'"`
}

type ProProductStoreInfo struct {
	// 主键
	Id int `json:"id" xorm:"pk autoincr not null INT 'id'"`

	// 商品ID
	ProductId int `json:"product_id" xorm:"default 0 comment('商品ID') INT 'product_id'"`

	// 门店的主键
	StoreId string `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`

	// 渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)
	ChannelId int `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)') INT 'channel_id'"`

	// 分类id
	ChannelCategoryId string `json:"channel_category_id" xorm:"default '' comment('分类id') VARCHAR(50) 'channel_category_id'"`

	// 上下架状态（1-上架，0-下架）
	UpDownState int `json:"up_down_state" xorm:"default 0 comment('上下架状态（1-上架，0-下架）') INT 'up_down_state'"`

	// 1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败
	Status int `json:"status" xorm:"default 0 comment('1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败 ') INT 'status'"`

	// 建议价格/零售价
	RetailPrice int `json:"retail_price" xorm:"default 0 comment('建议价格/零售价') INT 'retail_price'"`

	// 0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功
	IsDistribution int `json:"is_distribution" xorm:"default 0 comment('0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功') INT 'is_distribution'"`

	// 铺品或者上架报错信息
	SyncError string `json:"sync_error" xorm:"not null comment('铺品或者上架报错信息') VARCHAR(500) 'sync_error'"`

	// 第三方回写的商品ID
	ProductThirdId string `json:"product_third_id" xorm:"default '' comment('第三方回写的商品ID') VARCHAR(50) 'product_third_id'"`
	// 第三方回写的skuID
	SkuThirdId string `json:"sku_third_id" xorm:"default '' comment('第三方回写的skuID') VARCHAR(50) 'sku_third_id'"`

	// 商品添加日期
	CreateDate time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`

	// 商品最后更新日期
	UpdateDate time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
}

type FoodData struct {
	// app_food_code(必填项),APP方商品id
	App_food_code string `json:"app_food_code" form:"app_food_code" query:"app_food_code"`
	// skus(必填项)，商品sku信息集合的json数组
	Skus []Skus `json:"skus" form:"skus" query:"skus"`
}

// 同步库存给第三方的结构体
type Skus struct {
	// sku_id(必填项)，是sku唯一标识码
	Sku_id string `json:"sku_id" form:"sku_id" query:"sku_id"`
	// stock(必填项)，为sku的库存，传非负整数，若传"*"表示库存无限
	Stock string `json:"stock" form:"stock" query:"stock"`
}

// 同步库存给第三方的结构体
type RetailSkuStock struct {
	// APP方门店id--美团id
	App_poi_code string `json:"app_poi_code" form:"app_poi_code" query:"app_poi_code"`
	// 商品sku库存集合的json数据
	Food_data []FoodData `json:"food_data" form:"food_data" query:"food_data"`
}

type SyncStockData struct {
	// 商品ID
	ProductId int `json:"product_id" xorm:"default 0 comment('商品ID') INT 'product_id'"`

	// 门店的主键
	StoreId string `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`

	// 渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)
	ChannelId int `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)') INT 'channel_id'"`

	// sku_id(必填项)，是sku唯一标识码
	SkuId int64 `json:"sku_id" form:"sku_id" query:"sku_id"`
	//第三方skuid
	SkuThirdId int64 `json:"sku_third_id" form:"sku_third_id" query:"sku_third_id"`
}
