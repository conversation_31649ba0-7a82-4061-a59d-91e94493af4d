package base

// StoreRelation 基础结构定义
type StoreRelation struct {
    Id             int    `json:"id" xorm:"pk autoincr not null INT 'id'"`
    FinanceCode    string `json:"finance_code" xorm:"default 'null' comment('财务编码') VARCHAR(50) 'finance_code'"`
    ChannelId      int    `json:"channel_id" xorm:"not null default 0 comment('渠道id') INT 'channel_id'"`
    ChannelStoreId string `json:"channel_store_id" xorm:"default 'null' comment('渠道门店id') VARCHAR(50) 'channel_store_id'"`
    CustomCode     string `json:"custom_code" xorm:"default 'null' comment('全渠道往来单位') VARCHAR(255) 'custom_code'"`
    IsCreate       int    `json:"is_create" xorm:"default 'null' comment('是否创建了配送门店') INT 'is_create'"`
} 