package controllers

import (
	po "eShop/domain/points-po"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"net/http"

	"github.com/go-chi/chi"
)

type ClsPointsRuleController struct {
	SuperController[int, po.ClsPointsRule, vo.ClsPointsRuleSaveVO, vo.ClsPointsRuleUpdateVO, vo.ClsPointsRuleQueryVO, vo.ClsPointsRuleResultVO]
	ControllerHooks[int, po.ClsPointsRule, vo.ClsPointsRuleSaveVO, vo.ClsPointsRuleUpdateVO, vo.ClsPointsRuleQueryVO, vo.ClsPointsRuleResultVO]
	service service.ClsPointsRuleService
}

func NewClsPointsRuleController() ClsPointsRuleController {
	return ClsPointsRuleController{
		NewSuperController(
			service.NewClsPointsRuleService(),
			&ClsPointsRuleController{},
		),
		NewControllerHooks[int, po.ClsPointsRule, vo.ClsPointsRuleSaveVO, vo.ClsPointsRuleUpdateVO, vo.ClsPointsRuleQueryVO, vo.ClsPointsRuleResultVO](),
		service.NewClsPointsRuleService(),
	}
}

func (c ClsPointsRuleController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
	r.Post("/batch-create", c.BatchCreate)
	r.Post("/batch-update", c.BatchUpdate)
	r.Post("/page", c.MultiPage)
	r.Get("/get-by-code", c.GetByCode)
}

// @Summary 批量创建积分规则
// @Description 批量创建多条积分规则记录
// @Tags 积分规则管理
// @Accept json
// @Produce json
// @Param data body vo.ClsPointsRuleSaveVO true "积分规则创建参数"
// @Success 200 {object} response.BaseResp "成功创建积分规则"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /points-app/cls-points-rule/batch-create [post]
func (c ClsPointsRuleController) BatchCreate(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	vo, err := utils.Bind[vo.ClsPointsRuleBatchSaveVO](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 执行创建操作
	result, err := c.service.BatchCreate(nil, vo)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

// @Summary 批量更新积分规则
// @Description 批量更新多条积分规则记录
// @Tags 积分规则管理
// @Accept json
// @Produce json
// @Param data body vo.ClsPointsRuleUpdateVO true "积分规则更新参数"
// @Success 200 {object} response.BaseResp "成功更新积分规则"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /points-app/cls-points-rule/batch-update [post]
func (c ClsPointsRuleController) BatchUpdate(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	vo, err := utils.Bind[vo.ClsPointsRuleUpdateVO](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	vo.Operator = jwtInfo.Name

	// 执行更新操作
	result, err := c.service.BatchUpdate(nil, vo)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}

// @Summary 分页查询积分规则
// @Description 分页查询积分规则
// @Tags 积分规则管理
// @Accept json
// @Produce json
// @Param queryVO query vo.ClsPointsRuleQueryVO true "查询参数"
// @Success 200 {object} response.BaseResp "成功获取积分规则"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /points-app/cls-points-rule/multi-page [get]
func (c ClsPointsRuleController) MultiPage(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	queryVO, err := utils.Bind[vo.ClsPointsRuleQueryVO](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 执行分页查询操作
	result, total, err := c.service.MultiPage(nil, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithPage(w, result, int(total))
}

// @Summary 根据商品编码获取积分规则
// @Description 根据商品编码获取积分规则
// @Tags 积分规则管理
// @Accept json
// @Produce json
// @Param queryVO query vo.ClsPointsRuleQueryVO true "查询参数"
// @Success 200 {object} response.BaseResp "成功获取积分规则"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /points-app/cls-points-rule/get-by-code [get]
func (c ClsPointsRuleController) GetByCode(w http.ResponseWriter, r *http.Request) {
	// 绑定请求参数
	queryVO, err := utils.Bind[vo.ClsPointsRuleQueryVO](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 执行获取积分规则操作
	result, err := c.service.GetByCode(nil, queryVO)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 返回结果
	response.SuccessWithData(w, result)
}
