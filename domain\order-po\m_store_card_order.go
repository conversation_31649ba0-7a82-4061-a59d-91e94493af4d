package order_po

import (
	"time"

	"xorm.io/xorm"
)

// CREATE TABLE `m_store_card_order` (
//
//		`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
//		`tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户号',
//		`card_info_id` bigint NOT NULL DEFAULT '0' COMMENT '基础卡id',
//		`card_id` bigint NOT NULL DEFAULT '0' COMMENT '卡id',
//		`card_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡名称',
//		`card_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卡类型',
//		`record_id` bigint NOT NULL DEFAULT '0' COMMENT '卡记录id;卡id',
//		`biz_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
//		`order_amount` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '订单金额',
//		`order_id` bigint NOT NULL DEFAULT '0' COMMENT '订单id',
//		`order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
//		`status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '进行中' COMMENT '状态',
//		`amount` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '购买储值卡额度',
//		`amount_gift` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '赠送额度',
//		`customer_id` bigint NOT NULL DEFAULT '0' COMMENT '客户id',
//		`customer_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户手机号',
//		`customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户名称',
//		`seller_id` bigint NOT NULL DEFAULT '0' COMMENT '收银员id',
//		`seller_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收银员名',
//		`remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
//		`refund_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否退单',
//		`refund_amount` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '退单金额',
//		`refund_order_id` bigint NOT NULL DEFAULT '0' COMMENT '关联退单id',
//		`refund_card_amount` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '卡退的额度',
//		`refund_card_amount_gift` decimal(18,4) NOT NULL DEFAULT '0.0000' COMMENT '卡退的额度-赠送金',
//		`refund_seller_id` bigint NOT NULL DEFAULT '0' COMMENT '退单收银员id',
//		`is_deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
//		`created_by` bigint NOT NULL DEFAULT '0' COMMENT '创建人',
//		`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
//		`updated_by` bigint NOT NULL DEFAULT '0' COMMENT '更新人',
//		`updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
//		PRIMARY KEY (`id`)
//	  ) ENGINE=InnoDB AUTO_INCREMENT=587213232994560350 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='储值卡购买/续充订单表'
type StoreCardOrder struct {
	// 主键
	Id int64 `json:"id" xorm:"pk autoincr 'id' comment('主键')"`
	// 租户号
	TenantId int64 `json:"tenant_id" xorm:"not null default 0 'tenant_id' comment('租户号')"`
	// 基础卡id
	CardInfoId int64 `json:"card_info_id" xorm:"not null default 0 'card_info_id' comment('基础卡id')"`
	// 卡id
	CardId int64 `json:"card_id" xorm:"not null default 0 'card_id' comment('卡id')"`
	// 卡名称
	CardName string `json:"card_name" xorm:"not null default '' varchar(100) 'card_name' comment('卡名称')"`
	// 卡类型
	CardType string `json:"card_type" xorm:"not null default '' varchar(16) 'card_type' comment('卡类型')"`
	// 卡记录id
	RecordId int64 `json:"record_id" xorm:"not null default 0 'record_id' comment('卡记录id;卡id')"`
	// 业务类型
	BizType string `json:"biz_type" xorm:"not null default '' varchar(255) 'biz_type' comment('业务类型')"`
	// 订单金额
	OrderAmount float64 `json:"order_amount" xorm:"not null default 0.0000 decimal(18,4) 'order_amount' comment('订单金额')"`
	// 订单id
	OrderId int64 `json:"order_id" xorm:"not null default 0 'order_id' comment('订单id')"`
	// 订单号
	OrderNo string `json:"order_no" xorm:"not null default '' varchar(255) 'order_no' comment('订单号')"`
	// 状态
	Status string `json:"status" xorm:"not null default '进行中' varchar(255) 'status' comment('状态')"`
	// 购买储值卡额度
	Amount float64 `json:"amount" xorm:"not null default 0.0000 decimal(18,4) 'amount' comment('购买储值卡额度')"`
	// 赠送额度
	AmountGift float64 `json:"amount_gift" xorm:"not null default 0.0000 decimal(18,4) 'amount_gift' comment('赠送额度')"`
	// 客户id
	CustomerId int64 `json:"customer_id" xorm:"not null default 0 'customer_id' comment('客户id')"`
	// 客户手机号
	CustomerMobile string `json:"customer_mobile" xorm:"not null default '' varchar(255) 'customer_mobile' comment('客户手机号')"`
	// 客户名称
	CustomerName string `json:"customer_name" xorm:"not null default '' varchar(255) 'customer_name' comment('客户名称')"`
	// 收银员id
	SellerId int64 `json:"seller_id" xorm:"not null default 0 'seller_id' comment('收银员id')"`
	// 收银员名
	SellerName string `json:"seller_name" xorm:"not null default '' varchar(64) 'seller_name' comment('收银员名')"`
	// 备注
	Remark string `json:"remark" xorm:"not null default '' varchar(255) 'remark' comment('备注')"`
	// 是否退单
	RefundFlag bool `json:"refund_flag" xorm:"not null default 0 'refund_flag' comment('是否退单')"`
	// 退单金额
	RefundAmount float64 `json:"refund_amount" xorm:"not null default 0.0000 decimal(18,4) 'refund_amount' comment('退单金额')"`
	// 关联退单id
	RefundOrderId int64 `json:"refund_order_id" xorm:"not null default 0 'refund_order_id' comment('关联退单id')"`
	// 卡退的额度
	RefundCardAmount float64 `json:"refund_card_amount" xorm:"not null default 0.0000 decimal(18,4) 'refund_card_amount' comment('卡退的额度')"`
	// 卡退的额度-赠送金
	RefundCardAmountGift float64 `json:"refund_card_amount_gift" xorm:"not null default 0.0000 decimal(18,4) 'refund_card_amount_gift' comment('卡退的额度-赠送金')"`
	// 退单收银员id
	RefundSellerId int64 `json:"refund_seller_id" xorm:"not null default 0 'refund_seller_id' comment('退单收银员id')"`
	// 是否删除
	IsDeleted bool `json:"is_deleted" xorm:"not null default 0 'is_deleted' comment('是否删除')"`
	// 创建人
	CreatedBy int64 `json:"created_by" xorm:"not null default 0 'created_by' comment('创建人')"`
	// 创建时间
	CreatedTime time.Time `json:"created_time" xorm:"not null default CURRENT_TIMESTAMP created 'created_time' comment('创建时间')"`
	// 更新人
	UpdatedBy int64 `json:"updated_by" xorm:"not null default 0 'updated_by' comment('更新人')"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time" xorm:"not null default CURRENT_TIMESTAMP updated 'updated_time' comment('更新时间')"`
}

func (m *StoreCardOrder) TableName() string {
	return "eshop_saas.m_store_card_order"
}

type StoreCardOrderRequest struct {
	OrderSn string `json:"order_sn"`
}

// GetStoreCardOrderByOrderSn 根据订单编号获取储值卡订单信息
func (m *StoreCardOrder) GetStoreCardOrderByOrderSn(session *xorm.Session, orderSn string) (*StoreCardOrder, error) {
	var order StoreCardOrder
	has, err := session.Select("*").Where("order_no = ? AND is_deleted = ?", orderSn, 0).Get(&order)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &order, nil
}

// 卡信息结构
type CardInfo struct {
	// 有效期类型
	Validity string `json:"validity" xorm:"validity"`
	// 使用范围
	DiscountRange string `json:"discount_range" xorm:"discount_range"`
	// 启用时间
	EnableTime time.Time `json:"enable_time" xorm:"enable_time"`
	// 过期时间
	ExpirationTime time.Time `json:"expiration_time" xorm:"expiration_time"`
}

// GetStoreCardValidity 获取储值卡有效期和使用范围
func (m *StoreCardOrder) GetStoreCardValidity(session *xorm.Session, orderSn string) (*CardInfo, error) {
	var result CardInfo

	has, err := session.SQL(`SELECT cr.valid_type as validity, c.discount_range, 
							 cr.enable_time, cr.expiration_time
							FROM eshop_saas.m_store_card_use_record csr 
							LEFT JOIN eshop_saas.m_store_card_record cr ON csr.card_no = cr.card_no 
							LEFT JOIN eshop_saas.m_store_card c ON csr.card_id = c.card_id 
							WHERE csr.order_no = ?`, orderSn).Get(&result)

	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return &result, nil
}

// GetTimeCardValidity 获取次卡有效期和使用范围
func (m *StoreCardOrder) GetTimeCardValidity(session *xorm.Session, orderSn string) (*CardInfo, error) {
	var result CardInfo

	has, err := session.SQL(`SELECT cr.valid_type as validity, c.apply_product_type as discount_range, 
							 cr.enable_time, cr.expiration_time
							FROM eshop_saas.m_time_card_use_record csr 
							LEFT JOIN eshop_saas.m_time_card_record cr ON csr.card_no = cr.card_no 
							LEFT JOIN eshop_saas.m_time_card c ON csr.card_id = c.card_id 
							WHERE csr.order_no = ?`, orderSn).Get(&result)

	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return &result, nil
}
