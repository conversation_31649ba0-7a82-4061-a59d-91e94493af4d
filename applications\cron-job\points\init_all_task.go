package points

// 积分-开单数据转积分进账
import (
	"eShop/infra/cache"
	"eShop/infra/log"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"errors"
	"time"

	"github.com/robfig/cron/v3"
)

func InitTask() {
	c := cron.New(cron.WithSeconds())

	// 开单记录转积分进账 每天早上8点执行
	c.AddFunc("0 0 8 * * *", SalesIn)

	// 积分过期 每天早上1点执行
	c.AddFunc("0 0 1 * * *", PointsExpire)

	// 积分兑换订单自动完成 每天早上3点执行
	c.AddFunc("0 0 3 * * *", FinishOrder)

	// 积分即将过期提醒，每月月底前7天（每月24-31号的2点执行）
	c.AddFunc("0 0 2 L-6 * *", PointsExpireAlert)

	// 积分即将过期提醒，每天早上2点执行
	c.AddFunc("0 0 2 * * *", GivenPointsExpireAlert)

	// 注册宠利扫员工帐号，每天早上6点执行
	c.AddFunc("0 0 6 * * *", RegisterClsWorker)

	c.Start()
}

// 分布式锁，执行fn期间持有锁，结束后校验唯一标识再释放
func lock(lockKey string, fn func()) error {
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey, time.Minute*120)
	if !setNxReslt {
		log.Errorf("CronJob Points InitTask分布式锁(%s)获取失败", lockKey)
		return errors.New("分布式锁(%s)获取失败")
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey)
	fn()
	return nil
}
