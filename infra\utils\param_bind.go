package utils

import (
	"eShop/infra/log"
	"eShop/infra/utils/validate"
	"encoding/json"
	"errors"
	"net/http"
	"reflect"
	"strings"

	"github.com/gorilla/schema"
)

func Bind[T any](r *http.Request, rule ...string) (T, error) {
	var t T
	method := r.Method
	contentType := r.Header.Get("Content-Type")
	switch method {
	case "GET", "DELETE":
		t, _ = handleGet[T](r)
	case "POST", "PUT":
		if strings.Contains(contentType, "application/json") {
			t, _ = handlePostJson[T](r)
		} else if strings.Contains(contentType, "application/x-www-form-urlencoded") {
			t, _ = handlePostForm[T](r)
		} else if strings.Contains(contentType, "multipart/form-data") {
			t, _ = handlePostMultiForm[T](r)
		} else {
			return t, errors.New("不支持的请求方式，请另外拓展")
		}
	default:
		return t, errors.New("不支持的请求方式，body中没有找到参数信息")
	}
	validateRule := ""
	if len(rule) > 0 {
		validateRule = rule[0]
	}
	err := validate.Validate(t, validateRule)
	if err != nil {
		return t, errors.New("参数校验失败:" + err.Error())
	}
	return t, nil
}

func handlePostForm[T any](r *http.Request) (T, error) {
	var t T
	if err := r.ParseForm(); err != nil {
		log.Error("Error occurred while parsing form: ", err.Error())
		return t, errors.New("POST-Form请求参数解析异常，err=" + err.Error())
	}

	val := reflect.ValueOf(&t).Elem()
	typ := val.Type()

	// !struct
	if typ.Kind() != reflect.Struct {
		return t, errors.New("binding element must be a struct")
	}

	decoder := schema.NewDecoder()
	decoder.SetAliasTag("json")
	err := decoder.Decode(&t, r.PostForm)
	if err != nil {
		return t, errors.New("Get请求参数解析错误：err=" + err.Error())
	}

	return t, nil
}

func handlePostMultiForm[T any](r *http.Request) (T, error) {
	var t T
	if err := r.ParseMultipartForm(32 << 20); err != nil {
		log.Error("Error occurred while parsing form:", err.Error())
		return t, errors.New("POST-Form请求参数解析异常，err=" + err.Error())
	}

	val := reflect.ValueOf(&t).Elem()
	typ := val.Type()

	// !struct
	if typ.Kind() != reflect.Struct {
		return t, errors.New("binding element must be a struct")
	}

	decoder := schema.NewDecoder()
	decoder.SetAliasTag("json")
	err := decoder.Decode(&t, r.PostForm)
	if err != nil {
		return t, errors.New("Get请求参数解析错误：err=" + err.Error())
	}

	return t, nil
}

func handlePostJson[T any](r *http.Request) (T, error) {
	var t T
	val := reflect.ValueOf(&t).Elem()
	typ := val.Type()
	tt := typ.Kind()
	// !struct
	if tt != reflect.Struct && tt != reflect.Slice {
		return t, errors.New("binding element must be a struct")
	}

	err := json.NewDecoder(r.Body).Decode(&t)
	if err != nil {
		return t, errors.New("POST-Json请求参数解析异常，err=" + err.Error())
	}
	return t, nil
}

func handleGet[T any](r *http.Request) (T, error) {
	var t T
	var query = r.URL.Query()
	if query == nil || len(query) == 0 {
		return t, nil
	}

	decoder := schema.NewDecoder()
	// 忽略struct中没有定义的参数
	decoder.IgnoreUnknownKeys(true)
	decoder.SetAliasTag("json")
	err := decoder.Decode(&t, query)
	if err != nil {
		return t, errors.New("Get请求参数解析错误：err=" + err.Error())
	}

	return t, nil
}
