package location

import (
	"context"
	"eShop/infra/errors"
	vo "eShop/view-model/inventory-vo/location"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// Location 库位信息

type Location struct {
	Id          int       `json:"id" xorm:"pk autoincr not null comment('主键ID') INT 'id'"`
	ChainId     int64     `json:"chain_id" xorm:"not null comment('连锁ID') BIGINT 'chain_id'"`
	StoreId     string    `json:"store_id" xorm:"default 0 comment('门店的主键') VARCHAR(50) 'store_id'"`
	WarehouseId int       `json:"warehouse_id" xorm:"not null comment('仓库ID') INT 'warehouse_id'"`
	Code        string    `json:"code" xorm:"not null comment('库位编码') VARCHAR(64) 'code'"`
	Name        string    `json:"name" xorm:"not null comment('库位名称') VARCHAR(128) 'name'"`
	ProductId   int       `json:"product_id" xorm:"not null default 0 comment('商品id') INT 'product_id'"`
	SkuId       int       `json:"sku_id" xorm:"not null default 0 comment('sku id') INT 'sku_id'"`
	CreatedTime time.Time `json:"created_time" xorm:"not null comment('创建时间') DATETIME 'created_time' created"`
	UpdatedTime time.Time `json:"updated_time" xorm:"not null comment('修改时间') DATETIME 'updated_time' updated"`
}

// TableName 表名
func (s *Location) TableName() string {
	return "eshop.inventory_location"
}

// Create 创建库位
func (l Location) Create(ctx context.Context, session *xorm.Session) error {
	_, err := session.Context(ctx).Insert(l)
	if err != nil {
		return errors.Wrap(err, "创建库位失败")
	}
	return nil
}

// Update 更新库位
func (l Location) Update(ctx context.Context, session *xorm.Session, fields ...string) error {
	session = session.Context(ctx).ID(l.Id)
	if len(fields) > 0 {
		session = session.Cols(fields...)
	}
	_, err := session.Update(l)
	if err != nil {
		return errors.Wrap(err, "更新库位失败")
	}
	return nil
}

// Delete 删除库位
func (l *Location) Delete(ctx context.Context, session *xorm.Session) error {
	_, err := session.Context(ctx).Exec("DELETE FROM inventory_location WHERE id=?", l.Id)
	if err != nil {
		return errors.Wrap(err, "删除库位失败")
	}

	return nil
}

// GetByID 根据ID获取库位
func (l *Location) GetByID(ctx context.Context, session *xorm.Session, id int) error {
	has, err := session.Context(ctx).ID(id).Get(l)
	if err != nil {
		return errors.Wrap(err, "获取库位失败")
	}
	if !has {
		return errors.New("库位不存在")
	}
	return nil
}

// List 查询库位列表
func (l Location) List(ctx context.Context, session *xorm.Session, params vo.QueryParams) ([]vo.Location, int64, error) {
	var locations []vo.Location

	// 构建查询条件
	query := session.Table("inventory_location").Alias("l").
		Join("left", "pro_sku s", "s.id = l.sku_id").
		Join("left", "pro_product p", "p.id = s.product_id").
		Where("l.warehouse_id = ?", params.WarehouseID)
	if params.Code != "" {
		query = query.And("l.code = ?", params.Code)
	}
	if params.Name != "" {
		query = query.And("l.name LIKE ?", "%"+params.Name+"%")
	}
	if params.ProductInfo != "" {
		query = query.And("p.name LIKE ? OR s.bar_code LIKE ? OR s.id = ? OR p.id = ?",
			"%"+params.ProductInfo+"%", "%"+params.ProductInfo+"%", params.ProductInfo, params.ProductInfo)
	}
	query = query.Select(`l.*,
								p.id as product_id,s.id as sku_id,
								p.name as product_name,
								p.pic as product_image,
								concat(s.product_specs,' ',s.store_unit)   as product_spec,
								s.bar_code`)

	// 获取分页数据
	offset := (params.Page - 1) * params.PageSize
	total, err := query.Limit(params.PageSize, offset).FindAndCount(&locations)
	if err != nil {
		return nil, 0, errors.Wrap(err, "查询库位列表失败")
	}

	return locations, total, nil
}

// BindProduct 绑定商品
func (l Location) BindProduct(ctx context.Context, session *xorm.Session, skuId int) error {
	_, err := session.Exec("UPDATE inventory_location il "+
		"LEFT JOIN pro_sku ps ON ps.id=? "+
		"SET il.sku_id=?,il.product_id=ps.product_id,updated_time=NOW() "+
		"WHERE il.id=?", skuId, skuId, l.Id)
	if err != nil {
		return err
	}
	return nil
}

// UnbindProduct 解绑商品
func (l Location) UnbindProduct(ctx context.Context, session *xorm.Session) error {
	//这里的解绑没有达到预期，我希望的是置字段为Null，但实际赋值为0
	updEntity := &Location{
		Id:          l.Id,
		SkuId:       0,
		ProductId:   0,
		UpdatedTime: time.Now(),
	}
	err := updEntity.Update(ctx, session, "sku_id,product_id,update_time")
	if err != nil {
		return err
	}
	return nil
}

// QueryParams 查询参数
type QueryParams struct {
	Code string `json:"code"` // 库位编码
	Name string `json:"name"` // 库位名称
	Page int    `json:"page"` // 页码
	Size int    `json:"size"` // 每页大小
}

// GetLocationByCodeReq 根据库位码和店铺ID获取库位信息请求参数
type GetLocationReq struct {
	LocationCode string `json:"location_code"` // 库位编码
	StoreId      string `json:"store_id"`      // 店铺ID
	SkuId        int    `json:"sku_id"`        // skuid
	WarehouseId  int    `json:"warehouse_id"`
}

// GetLocation 根据库位码和店铺ID获取库位信息
func (l *Location) GetLocation(session *xorm.Session, req GetLocationReq) (out *Location, exist bool, err error) {
	out = new(Location)
	session = session.Table(l.TableName())
	if req.LocationCode != "" {
		session = session.Where("code=?", req.LocationCode)
	}
	if req.StoreId != "" {
		session = session.Where("store_id=?", req.StoreId)
	}
	if req.SkuId > 0 {
		session = session.Where("sku_id=?", req.SkuId)
	}
	if req.WarehouseId > 0 {
		session = session.Where("warehouse_id=?", req.WarehouseId)
	}
	exist, err = session.Get(out)
	if err != nil {
		return
	}
	return
}

type GetLocationListReq struct {
	LocationCode string `json:"location_code"` // 库位编码
	StoreId      string `json:"store_id"`      // 店铺ID
	SkuId        int    `json:"sku_id"`        // skuid
	SkuIds       []int  `json:"sku_ids"`       // skuids
	WarehouseId  int    `json:"warehouse_id"`
}

// GetLocationList 获取库位信息列表
func (l *Location) GetLocationList(session *xorm.Session, req GetLocationListReq) (out map[string]*Location, err error) {
	out = make(map[string]*Location)
	data, err := l.ListLocations(session, req)
	if err != nil {
		return out, err
	}
	for _, v := range data {
		out[fmt.Sprintf("%s_%d_%d", v.StoreId, v.WarehouseId, v.SkuId)] = v
	}
	return
}

func (l *Location) ListLocations(session *xorm.Session, req GetLocationListReq) ([]*Location, error) {
	data := make([]*Location, 0)
	session = session.Table(l.TableName())
	if req.LocationCode != "" {
		session = session.Where("code=?", req.LocationCode)
	}
	if req.StoreId != "" {
		session = session.Where("store_id=?", req.StoreId)
	}
	if req.SkuId > 0 {
		session = session.Where("sku_id=?", req.SkuId)
	}
	if len(req.SkuIds) > 0 {
		session = session.In("sku_id", req.SkuIds)
	}
	if req.WarehouseId > 0 {
		session = session.Where("warehouse_id=?", req.WarehouseId)
	}
	if err := session.Find(&data); err != nil {
		return nil, nil
	}
	return data, nil
}
