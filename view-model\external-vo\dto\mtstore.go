package dto

type MtStoreData struct {
	Data []MtStore `json:"data"`
}

type MtStore struct {
	IsOnline           int32   `json:"is_online"`
	Utime              int32   `json:"utime"`
	Phone              string  `json:"phone"`
	PicURL             string  `json:"pic_url"`
	OpenLevel          int32   `json:"open_level"`
	Name               string  `json:"name"`
	PreBookMinDays     int32   `json:"pre_book_min_days"`
	Longitude          float32 `json:"longitude"`
	AppPoiCode         string  `json:"app_poi_code"`
	PromotionInfo      string  `json:"promotion_info"`
	ShippingFee        float32 `json:"shipping_fee"`
	Ctime              int32   `json:"ctime"`
	PreBook            int32   `json:"pre_book"`
	InvoiceSupport     int32   `json:"invoice_support"`
	ShippingTime       string  `json:"shipping_time"`
	Address            string  `json:"address"`
	PreBookMaxDays     int32   `json:"pre_book_max_days"`
	InvoiceMinPrice    int32   `json:"invoice_min_price"`
	TimeSelect         int32   `json:"time_select"`
	Latitude           float32 `json:"latitude"`
	InvoiceDescription string  `json:"invoice_description"`
	ThirdTagName       string  `json:"third_tag_name"`
	LogisticsCodes     string  `json:"logistics_codes"`
}

type MtStoreSaveResponse struct {
	ResultCode int32       `json:"result_code"`
	Error      []Errorlist `json:"error_list"`
}

type Errorlist struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}

type MtStoreOpenCloseResponse struct {
	Data  string `json:"data"`
	Error struct {
		Code int32  `json:"code"`
		Msg  string `json:"msg"`
	} `json:"error"`
}

type MtDeleteDto struct {
	//APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
	AppPoiCode string `json:"app_poi_code"`
	//原app_food_code字段）APP方商品id：(1)商家中台系统中商品的编码，即商家系统商品spu_code值。(2)字段信息限定长度不超过128个字符。(3)当商品为标品时，在此商品下非标品sku未删净的情况下，不允许删除标品sku。(4)如此商品目前只有一个sku，删除sku时即会彻底此商品。
	AppSpuCode string `json:"app_spu_code"`
	//商品sku唯一标识码:(1)字段信息限定长度不超过40个字符；(2)如此sku是当前商品唯一sku，删除此sku时即会彻底商品。(3)当商品为标品时，在此商品下非标品sku未删净的情况下，不允许删除标品sku。
	SkuId string `json:"sku_id"`
	//当商品目前只有一个sku时，删除此sku即会彻底此商品；而如果此商品为其分类下的最后一个商品，通过本参数选择当商品被删除的同时是否删除此分类，取值范围：1-删除分类；2-保留分类。 如未传本参数，则默认为1，表示删除分类。
	IsDeleteRetailCat int `json:"is_delete_retail_cat"`
}

type MtBatchDeleteDto struct {
	//APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
	AppPoiCode string `json:"app_poi_code"`
	//原app_food_code字段）APP方商品id：(1)商家中台系统中商品的编码，即商家系统商品spu_code值。(2)字段信息限定长度不超过128个字符。(3)当商品为标品时，在此商品下非标品sku未删净的情况下，不允许删除标品sku。(4)如此商品目前只有一个sku，删除sku时即会彻底此商品。
	AppSpuCodes string `json:"app_spu_codes"`
}

type MtDeliveryListRes struct {
	SuccessList []struct {
		Area                string  `json:"area"`
		LogisticsCode       string  `json:"logistics_code"`
		Type                int     `json:"type"`
		ShippingPeriodName  string  `json:"shipping_period_name"`
		GroupKey            string  `json:"groupKey"`
		ShippingFee         float64 `json:"shipping_fee"`
		Valid               int     `json:"valid"`
		MtShippingID        int     `json:"mt_shipping_id"`
		MinPrice            float64 `json:"min_price"`
		TimeRange           string  `json:"time_range"`
		AppShippingCode     string  `json:"app_shipping_code"`
		LogisticsType       string  `json:"logistics_type"`
		WeightMarkupFactors []struct {
			MarkupNum float64 `json:"markupNum"`
			Weight    float64 `json:"weight"`
			Step      float64 `json:"step"`
		} `json:"weight_markup_factors,omitempty"`
		TimeMarkupFactors []struct {
			MarkupNum float64 `json:"markupNum"`
			TimeRange string  `json:"timeRange"`
		} `json:"time_markup_factors,omitempty"`
		DistanceMarkupFactors []struct {
			Distance  float64 `json:"distance"`
			MarkupNum float64 `json:"markupNum"`
		} `json:"distance_markup_factors,omitempty"`
	} `json:"success_list"`
	ResultCode int `json:"result_code"`
}

type AutoGenerated []struct {
	X int `json:"x"`
	Y int `json:"y"`
}

type MtStoreBaseResponse struct {
	Data       string `json:"data"`
	ResultCode int32  `json:"result_code"`
}
