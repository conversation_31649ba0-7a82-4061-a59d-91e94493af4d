package api

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/infra/utils/validate"
	"eShop/services/distribution-service/enum"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

//分销员手机端

// @Summary 获取分销员信息 @blky-v1.0 @cls-v1.0
// @Description
// @Tags 小程序接口-分销员
// @Accept plain
// @Produce plain
// @Param mobile query string false "手机号"
// @Param org_id query int false "主体ID"
// @Param shop_id query int false "店铺ID"
// @Success 200 {object} vo.DistributorResp
// @Failure 400 {object} vo.DistributorResp
// @Router /api/distributor/get [get]
func DistributorGet(writer http.ResponseWriter, request *http.Request) {
	resp := vo.DistributorResp{}
	resp.Code = 400

	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.DistributorReq](request)
	if err != nil {
		log.Error("获取分销员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取分销员，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	if req.OrgId == 0 {
		req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	}

	req.Mobile = jwtInfo.Mobile
	req.MemberId = jwtInfo.Memberid
	req.ScrmUserId = jwtInfo.Scrmid
	if req.OrgId == enum.OrgId {
		resp.Data, err = server.DistributorGet(req)
	} else if req.OrgId == enum.BLKYOrgId {
		resp.Data, err = server.BLKYDistributorGet(req)
	} else {
		log.Error("获取分销员，未知主体， ", req.OrgId)
		resp.Message = fmt.Sprintf("未知主体:%d", req.OrgId)
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	if err != nil {
		log.Error("获取分销员：err=" + err.Error())
		resp.Message = fmt.Sprintf("获取分销员：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)

}

// @Summary 分销员入驻 @cls_v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param DisDistributorAddReq body vo.DisDistributorAddReq true " "
// @Success 200 {object} vo.DisDistributorAddResp
// @Failure 400 {object} vo.DisDistributorAddResp
// @Router /api/distributor/insert [POST]
func DistributorInsert(writer http.ResponseWriter, request *http.Request) {
	resp := vo.DisDistributorAddResp{}
	resp.Code = 400

	server := services.DistributorManageService{}
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	orgId := cast.ToInt(request.Header.Get("org_id"))

	req, err := utils.Bind[vo.DisDistributorAddReq](request)
	if err != nil {
		log.Error("分销员入住，err=", err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	if req.OrgId == 0 {
		req.OrgId = orgId
	}
	req.MemberId = jwtInfo.Memberid
	req.Mobile = jwtInfo.Mobile
	logPrefix := fmt.Sprintf("分销员入驻,用户主体id:%d,MemberId:%d，手机号:%s", req.OrgId, req.MemberId, req.Mobile)
	log.Info(logPrefix, "入参：", utils.InterfaceToJSON(req))

	if req.OrgId == enum.OrgId { // 润合云店 分销员入住
		err = server.DistributorInsert(req)
	} else if req.OrgId == enum.BLKYOrgId && req.OperationType == 0 { // 百林康源医生身份入驻
		//if utils.IsProEnv() {
		resp.Message = "该功能已停用"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
		//}
		// 数据验证
		vErr := validate.Validate(req, cast.ToString(req.OrgId))
		if vErr != nil {
			log.Error(logPrefix, "分销员入驻数据校验异常：", vErr)
			resp.Message = fmt.Sprintf("分销员入驻数据校验异常：%s", vErr)
			bytes, _ := json.Marshal(resp)
			writer.Write(bytes)
			return
		}
		resp.Data, err = server.BLKYDistributorInsert(req)
	} else if req.OrgId == enum.BLKYOrgId && req.OperationType == 1 { //宠利扫分销员入驻
		if req.DisRole != 1 && req.DisRole != 2 { // 老板身份入驻
			log.Error(logPrefix, "分销员入驻数据校验异常：", "分销员角色异常")
			resp.Message = "分销员角色异常"
			bytes, _ := json.Marshal(resp)
			writer.Write(bytes)
			return
		}
		// 数据校验规则， 主体id_分销员角色
		vRule := "boss"
		if req.DisRole == 2 {
			vRule = "worker"
		}
		vErr := validate.Validate(req, fmt.Sprintf("%d_%s", req.OrgId, vRule))
		if vErr != nil {
			log.Error(logPrefix, "分销员入驻数据校验异常：", vErr)
			resp.Message = fmt.Sprintf("分销员入驻数据校验异常：%s", vErr)
			bytes, _ := json.Marshal(resp)
			writer.Write(bytes)
			return
		}
		if req.DisRole == 1 && req.BossDisId == 0 { // 老板身份入驻
			resp.Data, err = server.CLSBossDistributorInsert(req)
		} else if req.DisRole == 2 && req.BossDisId > 0 { // 店员身份入驻
			resp.Data, err = server.CLSBeWorkerDistributor(req) // 店员身份入驻
		} else {
			err = errors.New("分销员角色异常")
		}

	}

	if err != nil {
		log.Error(logPrefix, "分销员入驻失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)

}

// @Summary ocr识别营业执照上的社会信用代码和企业名称 @cls_v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param OcrpPredictionSocialCodeReq body vo.OcrpPredictionSocialCodeReq true " "
// @Success 200 {object} vo.OcrpPredictionSocialCodeRes
// @Failure 400 {object} vo.OcrpPredictionSocialCodeRes
// @Router /api/distributor/ocrp-prediction-social-code [Post]
func OcrpPredictionSocialCode(writer http.ResponseWriter, request *http.Request) {
	resp := vo.OcrpPredictionSocialCodeRes{}
	resp.Code = 400

	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.OcrpPredictionSocialCodeReq](request)
	if err != nil {
		log.Error("ocr识别营业执照上的社会信用代码和企业名称，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("ocr识别营业执照上的社会信用代码和企业名称，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	resp.Data, err = server.OcrpPredictionSocialCode(req)
	if err != nil {
		log.Error("ocr识别营业执照上的社会信用代码和企业名称，失败：err=", err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)

}

// @Summary 根据社会信用代码查询企业名称 @cls_v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param EnterpriseBySocialCodeReq body vo.EnterpriseBySocialCodeReq true " "
// @Failure 400 {object} vo.EnterpriseBySocialCodeRes
// @Router /api/distributor/enterprise-by-social-code [Post]
func EnterpriseBySocialCode(writer http.ResponseWriter, request *http.Request) {
	resp := vo.EnterpriseBySocialCodeRes{}
	resp.Code = 400

	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.EnterpriseBySocialCodeReq](request)
	if err != nil {
		log.Error("根据社会信用代码查询企业名称，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("根据社会信用代码查询企业名称，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	resp.Data, err = server.EnterpriseBySocialCode(req)
	if err != nil {
		log.Error("根据社会信用代码查询企业名称，失败：err=", err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 根据社会信用代码和法人手机号，判断该企业是否被其他分销员绑定 @cls_v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param CheckEnterpriseBindedReq body vo.CheckEnterpriseBindedReq true " "
// @Success 200 {object} vo.CheckEnterpriseBindedRes
// @Failure 400 {object} vo.CheckEnterpriseBindedRes
// @Router /api/distributor/check-enterprise-binded [Post]
func CheckEnterpriseBinded(writer http.ResponseWriter, request *http.Request) {
	resp := vo.CheckEnterpriseBindedRes{}
	resp.Code = 400

	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.CheckEnterpriseBindedReq](request)
	if err != nil {
		log.Error("根据社会信用代码和法人手机号，判断该企业是否被其他分销员绑定，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	resp.Data, err = server.CheckEnterpriseBinded(req)
	//被绑定
	if err != nil {
		log.Error("根据社会信用代码和法人手机号，判断该企业是否被其他分销员绑定，失败：err=", err.Error())
		resp.Message = err.Error()

	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 修改分销员信息 @cls_v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param BaseUserRequest body vo.DisEditReq true " "
// @Success 200 {object} vo.DisDistributorAddResp
// @Failure 400 {object} vo.DisDistributorAddResp
// @Router /api/distributor/edit [Post]
func DistributorEdit(writer http.ResponseWriter, request *http.Request) {
	resp := vo.DisDistributorAddResp{}
	resp.Code = 400

	server := services.DistributorManageService{}
	orgId := cast.ToInt(request.Header.Get("org_id"))
	req, err := utils.Bind[vo.DisEditReq](request)
	if err != nil {
		log.Error("分销员修改信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分销员修改信息，参数解析失败：" + err.Error())
	} else {
		if req.OrgId == 0 {
			req.OrgId = orgId
		}
		if req.EditType == 3 {
			vErr := validate.Validate(req, cast.ToString(req.OrgId))
			if vErr != nil {
				resp.Message = fmt.Sprintf("分销员修改信息数校验异常：%s", vErr)
				bytes, _ := json.Marshal(resp)
				writer.Write(bytes)
				return
			}
		} else {
			//req.Mobile = jwtInfo.Mobile
			vErr := validate.Validate(req, "upd")
			if vErr != nil {
				resp.Message = fmt.Sprintf("分销员修改信息数校验异常：%s", vErr)
				bytes, _ := json.Marshal(resp)
				writer.Write(bytes)
				return
			}
		}

		resp.Data, err = server.DistributorEdit(req)
		if err != nil {
			log.Error("分销员修改信息，失败：err=" + err.Error())
			if req.EditType == 3 {
				resp.Message = err.Error()
			} else {
				resp.Message = fmt.Sprintf("分销员修改信息，异常：" + err.Error())
			}

		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 宠利扫老板分销员修改信息 @cls_v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param CLSDistributorEditReq body vo.CLSDistributorEditReq true " "
// @Success 200 {object} vo.CLSDistributorEditResp
// @Failure 400 {object} vo.CLSDistributorEditResp
// @Router /api/distributor/cls-edit [Post]
func CLSDistributorEdit(writer http.ResponseWriter, request *http.Request) {
	resp := vo.CLSDistributorEditResp{}
	resp.Code = 400

	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.CLSDistributorEditReq](request)
	if err != nil {
		log.Error("宠利扫分销员修改信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("宠利扫分销员修改信息，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	if len(req.SocialCreditCode) == 0 || len(req.EnterpriseName) == 0 {
		resp.Message = "请选择企业"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	req.MemberId = jwtInfo.Memberid
	err = server.CLSDistributorEdit(req)
	if err != nil {
		log.Error("宠利扫分销员修改信息，失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 老板邀请注册店员二维码@cls-v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param BossInviteDistributorReq body vo.BossInviteDistributorReq true " "
// @Success 200 {object} vo.BossInviteDistributorRes
// @Failure 400 {object} vo.BossInviteDistributorRes
// @Router /api/distributor/boss-invite-distributor [Post]
func BossInviteDistributor(writer http.ResponseWriter, request *http.Request) {
	resp := vo.BossInviteDistributorRes{}
	resp.Code = 400
	server := services.DistributorManageService{}
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req := vo.BossInviteDistributorReq{
		MemberId:   jwtInfo.Memberid,
		ScrmUserId: jwtInfo.Scrmid,
		Mobile:     jwtInfo.Mobile,
		OrgId:      cast.ToInt(request.Header.Get("org_id")),
	}
	resp.Data, err = server.BossInviteDistributor(req)
	if err != nil {
		log.Error("老板邀请注册店员二维码，失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 检查老板邀请码是否有效@cls-v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param CheckBossInviteCodeReq body vo.CheckBossInviteCodeReq true " "
// @Success 200 {object} vo.CheckBossInviteCodeRes
// @Failure 400 {object} vo.CheckBossInviteCodeRes
// @Router /api/distributor/check-boss-invite-code [Post]
func CheckBossInviteCode(writer http.ResponseWriter, request *http.Request) {
	resp := vo.CheckBossInviteCodeRes{}
	resp.Code = 400
	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.CheckBossInviteCodeReq](request)
	if err != nil {
		log.Error("检查老板邀请码是否有效，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("检查老板邀请码是否有效，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Data, err = server.CheckBossInviteCode(req)
	if err != nil {
		log.Error("检查老板邀请码是否有效，失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 将店员分销员移出企业@cls-v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param RemoveDistributorReq body vo.RemoveDistributorReq true " "
// @Success 200 {object} vo.RemoveDistributorRes
// @Failure 400 {object} vo.RemoveDistributorRes
// @Router /api/distributor/remove-distributor [Post]
func RemoveDistributor(writer http.ResponseWriter, request *http.Request) {
	resp := vo.RemoveDistributorRes{}
	resp.Code = 400
	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.RemoveDistributorReq](request)
	if err != nil {
		log.Error("将店员分销员移出企业，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("将店员分销员移出企业，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.ScrmUserId = jwtInfo.Scrmid
	req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	err = server.RemoveDistributor(req)
	if err != nil {
		log.Error("将店员分销员移出企业，失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 店员自己解绑所在企业 @cls-v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param SelfRemoveEnterpriseReq body vo.SelfRemoveEnterpriseReq true " "
// @Success 200 {object} vo.SelfRemoveEnterpriseRes
// @Failure 400 {object} vo.SelfRemoveEnterpriseRes
// @Router /api/distributor/self-remove-enterprise [Post]
func SelfRemoveEnterprise(writer http.ResponseWriter, request *http.Request) {
	resp := vo.SelfRemoveEnterpriseRes{}
	resp.BaseHttpResponse.Code = 400
	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.SelfRemoveEnterpriseReq](request)
	if err != nil {
		log.Error("店员自己解绑所在企业，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("店员自己解绑所在企业，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.MemberId = jwtInfo.Memberid
	req.ScrmUserId = jwtInfo.Scrmid
	req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	err = server.SelfRemoveEnterprise(req)
	if err != nil {
		log.Error("店员自己解绑所在企业，失败：err=", err.Error())
		resp.Message = err.Error()
	} else {
		resp.Code = 200
	}
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 分享海报
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param DisShareReq body vo.DisShareReq true " "
// @Success 200 {object} vo.DisShareRes
// @Failure 400 {object} vo.DisShareRes
// @Router /api/distributor/share [Post]
func SharePosters(writer http.ResponseWriter, request *http.Request) {
	resp := vo.DisShareRes{}
	resp.Code = 400
	server := services.DistributorManageService{}
	req, err := utils.Bind[vo.DisShareReq](request)
	if err != nil {
		log.Error("分销员分享海报，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分销员分享海报，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	orgId := cast.ToInt(request.Header.Get("org_id"))
	req.OrgId = orgId
	org := cast.ToInt(request.Header.Get("org"))
	if org != 0 {
		req.OrgId = org
	}

	log.Info("打印分享参数：", req)
	if err != nil {
		log.Error("分销员修改信息，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分销员修改信息，参数解析失败：" + err.Error())
	} else {
		url, err := server.SharePosters(req)
		if err != nil {
			log.Error("分销员修改信息，失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("分销员修改信息异常：" + err.Error())
		} else {
			resp.Code = 200
			resp.Url = url
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 分销店铺里的所有分销员信息列表@fuma-v1.3
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Success 200 {object} vo.GetShopDistributorsRes
// @Failure 400 {object} vo.GetShopDistributorsRes
// @Router /api/distributor/shop-distributors [Get]
func GetShopDistributors(writer http.ResponseWriter, request *http.Request) {
	out := vo.GetShopDistributorsRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))

	// 从jwt里获取
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Errorf("获取分销订单列表-鉴权失败-错误为%s", err.Error())
		out.Message = "鉴权失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	param := vo.GetShopDistributorsReq{
		OrgId:      orgId,
		MemberId:   jwtInfo.Memberid,
		ScrmUserId: jwtInfo.Scrmid,
	}
	server := services.DistributorManageService{}
	if out.Data, err = server.GetShopDistributors(param); err != nil {
		log.Errorf("获取分销店铺的店员列表失败-错误为%s", err.Error())
		out.Message = err.Error()
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}

// @Summary 获取分销员关联的企业列表
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param org_id query int true "主体ID"
// @Success 200 {object} []vo.DistributorEnterpriseInfo
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/distributor/enterprises [get]
func GetDistributorEnterpriseList(writer http.ResponseWriter, request *http.Request) {
	type Response struct {
		viewmodel.BaseHttpResponse
		Data  []vo.DistributorEnterpriseInfo `json:"data"`
		Total int                            `json:"total"`
	}
	resp := Response{BaseHttpResponse: viewmodel.BaseHttpResponse{Code: 400}}

	// 从header获取org_id
	orgId := cast.ToInt(request.Header.Get("org_id"))
	if orgId == 0 {
		resp.Message = "主体ID不能为空"
		json.NewEncoder(writer).Encode(resp)
		return
	}

	// 从jwt获取会员信息
	jwtInfo, err := jwtauth.GetJwtInfo(request)
	if err != nil {
		log.Error("获取登录信息失败: err", err.Error())
		resp.Message = "获取登录信息失败"
		json.NewEncoder(writer).Encode(resp)
		return
	}

	req := vo.DistributorEnterpriseListReq{
		OrgId:    orgId,
		MemberId: jwtInfo.Memberid,
	}

	server := services.DistributorManageService{}
	data, err := server.GetDistributorEnterpriseList(req)
	if err != nil {
		log.Error("获取分销员关联企业列表失败：err=", err.Error())
		resp.Message = err.Error()
		json.NewEncoder(writer).Encode(resp)
		return
	}

	resp.Data = data
	resp.Total = len(data)
	resp.Code = 200
	json.NewEncoder(writer).Encode(resp)
}

// @Summary 根据手机号获取云订货企业信息@cls-v1.0.0
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param mobile query string true "手机号"
// @Success 200 {object} vo.GetEnterpriseInfoRes
// @Failure 400 {object} vo.GetEnterpriseInfoRes
// @Router /api/distributor/enterprises-by-phone [get]
func GetEnterprisesByPhone(writer http.ResponseWriter, request *http.Request) {
	resp := vo.GetEnterpriseInfoRes{BaseHttpResponse: viewmodel.BaseHttpResponse{Code: 400}}

	req, err := utils.Bind[vo.GetEnterprisesByPhoneReq](request)
	if err != nil {
		log.Error("根据手机号获取云订货企业信息，参数解析失败：err=", err.Error())
		resp.Message = "参数解析失败：" + err.Error()
		json.NewEncoder(writer).Encode(resp)
		return
	}

	server := services.DistributorManageService{}
	resp.Data, err = server.GetEnterprisesByPhone(req.Mobile)
	if err != nil {
		log.Error("根据手机号获取云订货企业信息，失败：err=", err.Error())
		resp.Message = err.Error()
		json.NewEncoder(writer).Encode(resp)
		return
	}

	resp.Code = 200
	json.NewEncoder(writer).Encode(resp)
}

// @Summary 更新分销员默认企业
// @Description
// @Tags 小程序接口-分销员
// @Accept json
// @Produce json
// @Param UpdateDefaultEnterpriseReq body vo.UpdateDefaultEnterpriseReq true "请求参数"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /api/distributor/enterprise/default [post]
func UpdateDefaultEnterprise(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{Code: 400}

	req, err := utils.Bind[vo.UpdateDefaultEnterpriseReq](request)
	if err != nil {
		log.Error("更新默认企业，参数解析失败：err=", err.Error())
		resp.Message = "参数解析失败：" + err.Error()
		json.NewEncoder(writer).Encode(resp)
		return
	}

	server := services.DistributorManageService{}
	if err := server.UpdateDefaultEnterprise(req); err != nil {
		log.Error("更新默认企业失败：err=", err.Error())
		resp.Message = err.Error()
		json.NewEncoder(writer).Encode(resp)
		return
	}

	resp.Code = 200
	json.NewEncoder(writer).Encode(resp)
}
