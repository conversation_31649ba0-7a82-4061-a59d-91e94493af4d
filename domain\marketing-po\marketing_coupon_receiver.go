package marketing_po

import (
	"eShop/infra/utils"
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"

	"xorm.io/xorm"
)

// 短信通知状态
const (
	SmsNotifyStatusUnsent = 0 // 未发送
	SmsNotifyStatusSent   = 1 // 已发送
)

// MarketingCouponReceiver 优惠券领取记录
type MarketingCouponReceiver struct {
	Id int64 `xorm:"pk 'id'" json:"id"` // 唯一数据ID
	// 连锁ID
	ChainId int64 `xorm:"'chain_id' default 0" json:"chain_id"`
	// 店铺ID
	StoreId string `xorm:"'store_id' varchar(127) notnull default '0'" json:"store_id"`
	// 优惠券ID
	CouponId int `xorm:"'coupon_id' default 0" json:"coupon_id"`
	// 优惠券编码
	Code string `xorm:"'code' varchar(32) default ''" json:"code"`
	// 会员ID
	CustomerId int64 `xorm:"'customer_id' default 0" json:"customer_id"`
	// 会员姓名
	CustomerName string `xorm:"'customer_name' varchar(50) default ''" json:"customer_name"`
	// 会员手机号
	CustomerMobile string `xorm:"'customer_mobile' varchar(11) default ''" json:"customer_mobile"`
	// 加密会员手机号
	EncryCustomerMobile string `xorm:"'encry_customer_mobile' varchar(50) default ''" json:"encry_customer_mobile"`
	// 领取来源
	Source int `xorm:"'source' tinyint default 0" json:"source"`
	// 生效时间
	EnableTime time.Time `xorm:"'enable_time' datetime null" json:"enable_time"`
	// 失效时间
	ExpireTime time.Time `xorm:"'expire_time' datetime null" json:"expire_time"`
	// 订单ID
	OrderId int `xorm:"'order_id' default 0" json:"order_id"`
	// 订单编号
	OrderNo string `xorm:"'order_no' varchar(32) default ''" json:"order_no"`
	// 数据状态:  0-全部 1-未使用,2-已使用,3-已失效
	Status int `xorm:"'status' tinyint default 0" json:"status"`
	// 是否手动停止0-否 1-是
	IsStop int `xorm:"'is_stop' tinyint default 0" json:"is_stop"`
	// 删除标识:0未删除,1已删除
	IsDeleted int `xorm:"'is_deleted' bit default 0" json:"is_deleted"`
	// 短信通知状态 1-已发送 0-未发送
	SmsNotifyStatus int `xorm:"'sms_notify_status' tinyint default 0" json:"sms_notify_status"`
	// 创建人
	CreatedBy int64 `xorm:"'created_by' default 0" json:"created_by"`
	// 创建时间
	CreatedTime time.Time `xorm:"created 'created_time'" json:"created_time"`
	// 更新人
	UpdatedBy int64 `xorm:"'updated_by' default 0" json:"updated_by"`
	// 更新时间
	UpdatedTime time.Time `xorm:"updated 'updated_time'" json:"updated_time"`
}

// TableName 表名
func (MarketingCouponReceiver) TableName() string {
	return "marketing_coupon_receiver"
}

// Stop 停止优惠券领取记录
func (r *MarketingCouponReceiver) Stop(session *xorm.Session, id, couponId int64, storeId string, customerId string) error {
	// 1. 构建基础查询条件
	query := session.Table(r.TableName()).
		Where("coupon_id = ? AND store_id = ? AND status = 1 AND is_deleted = 0", couponId, storeId)

	// 2. 如果指定了客户ID,则只停止该客户的优惠券
	if customerId != "" {
		query = query.And("customer_id = ?", customerId)
	}

	if id != 0 {
		query = query.And("id = ?", id)
	}

	// 3. 更新状态为已停止
	affected, err := query.Update(map[string]interface{}{
		"status":       StatusExpired, // 已停止
		"is_stop":      1,
		"updated_time": time.Now(),
	})
	if err != nil {
		return fmt.Errorf("停止用券失败: %v", err)
	}

	// 4. 如果没有更新任何记录,说明没有可停止的优惠券
	if affected == 0 {
		if customerId != "" {
			return fmt.Errorf("客户[%s]没有可停止的优惠券", customerId)
		}
		return errors.New("没有可停止的优惠券")
	}

	return nil
}

// CreateReceiveRecord 创建领取记录
func CreateReceiveRecord(session *xorm.Session, coupon *MarketingCoupon, customer *Customer, customerId string, source int) error {
	now := time.Now()
	enableTime, expireTime := coupon.CalculateTime()

	receiver := &MarketingCouponReceiver{
		ChainId:             coupon.ChainId,
		StoreId:             coupon.StoreId,
		CouponId:            coupon.Id,
		Code:                utils.GenerateBarCode(),
		CustomerId:          cast.ToInt64(customerId),
		CustomerName:        customer.Name,
		CustomerMobile:      utils.AddStar(customer.Phone),
		EncryCustomerMobile: utils.MobileEncrypt(customer.Phone),
		Source:              source,
		EnableTime:          enableTime,
		ExpireTime:          expireTime,
		Status:              StatusUnused,
		CreatedTime:         now,
		UpdatedTime:         now,
	}

	_, err := session.Insert(receiver)
	return err
}

// UseMarketingCoupon 使用优惠券
func (r *MarketingCouponReceiver) UseMarketingCoupon(session *xorm.Session, code string, customerId int64, orderId int, orderNo string) error {
	if code == "" {
		return errors.New("优惠券编码不能为空")
	}

	// 更新优惠券状态为已使用
	result, err := session.Exec(`UPDATE marketing_coupon_receiver 
		SET status = 2,  
			order_id = ?, 
			order_no = ?,
			updated_time = NOW() 
		WHERE code = ? 
		AND customer_id = ? 
		AND status = 1 
		AND is_deleted = 0 
		AND is_stop = 0
		AND NOW() BETWEEN enable_time AND expire_time`,
		orderId,
		orderNo,
		code,
		customerId)

	if err != nil {
		return fmt.Errorf("更新优惠券状态失败: %v", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if affected == 0 {
		return errors.New("优惠券不可用或已被使用")
	}

	return nil
}
