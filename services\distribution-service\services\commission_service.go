package services

import (
	distribution_po "eShop/domain/distribution-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/enum"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	disWithdraw "eShop/services/distribution-service/enum/dis-withdraw"
	diswithdrawrecord "eShop/services/distribution-service/enum/dis-withdraw-record"
	distribution_vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"time"

	idvalidator "github.com/guanguans/id-validator"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

type DiscommissionService struct {
	common.BaseService
}

func (h *DiscommissionService) GetDisCommissionInfo(in distribution_vo.GetDisCommissionInfoReq) (out distribution_vo.GetDisCommissionList, total int, err error) {

	logPrefix := fmt.Sprintf("我的佣金-分销员会员id=%d", in.MemberId)

	h.Begin()
	defer h.Close()
	session := h.Session

	exists, err := session.Table("dis_distributor").Where("org_id=?", in.OrgId).Where("member_id=?", in.MemberId).Get(&out.Dis)
	if err != nil {
		log.Errorf("%s-获取分销员的提现信息失败-错误为%s", logPrefix, err.Error())
		err = errors.New("获取分销员提现信息失败")
		return
	}
	if !exists {
		log.Errorf("%s-未找到分销员数据", logPrefix)
		err = errors.New("未找到分销数据")
		return
	}

	exists, err = session.Table("dis_distributor_total").Where("org_id=?", in.OrgId).Where("dis_id=?", out.Dis.Id).Get(&out.DisTotal)
	if err != nil {
		log.Errorf("%s-获取分销员的统计提现信息失败-错误为%s", logPrefix, err.Error())
		err = errors.New("获取分销员统计提现信息失败")
		return
	}
	if !exists {
		log.Errorf("%s-未找到分销员统计数据", logPrefix)
		err = errors.New("未找到分销统计数据")
		return
	}

	out.Wid = make([]distribution_vo.DisWithdrawView, 0)
	selectStr := `a.*,
				(case
					when a.status = 1 then '未打款'
					when a.status = 2 then '已打款'
					when a.status = 3 then '已拒绝'
					else ''
				end ) as status_text,pre_tax_amount-after_tax_amount as tax`
	count, err := session.Table("dis_withdraw").Alias("a").Select(selectStr).
		Where("distributor_id=?", out.Dis.Id).
		OrderBy("create_time desc").
		Limit((in.PageSize), int(in.PageSize*(in.PageIndex-1))).
		FindAndCount(&out.Wid)
	if err != nil {
		log.Errorf("%s-获取提现记录失败-错误为%s", logPrefix, err.Error())
		err = errors.New("获取提现记录失败")
		return
	}
	total = int(count)

	return
}

// 获取分销店铺 累计可提现佣金、可提现佣金、预计税后金额、提现中金额 、已提现金额字段
func (h *DiscommissionService) GetShopCommInfo(in distribution_vo.GetDisCommissionInfoReq) (out distribution_vo.ShopCommInfo, err error) {

	logPrefix := fmt.Sprintf("我的店铺佣金-分销员会员id=%d", in.MemberId)
	log.Info(logPrefix, "入参:", utils.InterfaceToJSON(in))
	h.Begin()
	defer h.Close()
	session := h.Session

	exists, err := session.Table("dis_distributor").Where("org_id=?", in.OrgId).Where("member_id=?", in.MemberId).Get(&out.Dis)
	if err != nil {
		log.Errorf("%s-获取分销员的信息失败-错误为%s", logPrefix, err.Error())
		err = errors.New("获取分销员的信息失败")
		return
	}
	if !exists {
		log.Errorf("%s-未找到分销员数据", logPrefix)
		err = errors.New("未找到分销数据")
		return
	}
	// 润合云店 或者 宠利扫分销员
	shopId := 0
	if in.OrgId == enum.OrgId || (in.OrgId == enum.BLKYOrgId && out.Dis.DisRole != disdistributor.DisRoleDoctor && !in.IsShowBLKYDoctorOrder) {
		exists, err = session.Table("eshop.shop").Where("org_id=?", in.OrgId).Where("id=?", out.Dis.ShopId).Get(&out.ShopComm)
		if err != nil {
			log.Errorf("%s-获取分销店铺提现信息失败-错误为%s", logPrefix, err.Error())
			err = errors.New("获取分销店铺提现信息失败")
			return
		}
		if !exists {
			log.Errorf("%s-未找到分销店铺统计数据", logPrefix)
			err = errors.New("未找到分销店铺统计数据")
			return
		}
		shopId = int(out.ShopComm.Id)
		if out.Detail, err = h.GetShopCommList(distribution_vo.GetShopCommListReq{
			OrgId:  in.OrgId,
			ShopId: shopId,
		}); err != nil {
			log.Errorf("%s-获取分销店铺佣金明细", logPrefix)
			err = errors.New("获取分销店铺佣金明细")
			return
		}
	} else {
		// 个人分销员 统计提现信息，店铺id一定是为0的
		exists, err = session.Table("dis_distributor_total").Where("org_id=?", in.OrgId).Where("dis_id=?", out.Dis.Id).Where("shop_id=0").Get(&out.DisTotal)
		if err != nil {
			log.Errorf("%s-获取分销员的统计提现信息失败-错误为%s", logPrefix, err.Error())
			err = errors.New("获取分销员统计提现信息失败")
			return
		}
		if !exists {
			log.Errorf("%s-未找到分销员统计数据", logPrefix)
			err = errors.New("未找到分销统计数据")
			return
		}
	}

	withServer := DisWithdrawService{}
	if out.WithdrawList, _, err = withServer.GetSimpleWithList(distribution_vo.GetSimpleWithListReq{
		OrgId:         in.OrgId,
		ShopId:        shopId,
		DistributorId: out.Dis.Id,
	}); err != nil {
		log.Errorf("%s-获取提现记录失败", logPrefix)
		err = errors.New("获取提现记录失败")
		return
	}

	if in.OrgId == enum.BLKYOrgId {
		out.Dis.Name = out.Dis.RealName
	}
	out.WithdrawTaxRate = utils.WithdrawTaxRate(in.OrgId)

	return
}

// 获取店铺佣金明细
func (h *DiscommissionService) GetShopCommList(in distribution_vo.GetShopCommListReq) (out []distribution_vo.GetShopCommListRes, err error) {

	logPrefix := fmt.Sprintf("店铺佣金明细-shop_id=%d", in.ShopId)
	log.Info(logPrefix)

	h.Begin()
	defer h.Close()
	out = make([]distribution_vo.GetShopCommListRes, 0)
	session := h.Session
	if err := session.Table("eshop.dis_distributor_total").Alias("a").
		Select(`a.settled_commission,a.unsettled_commission,a.ins_settled_commission,a.ins_unsettled_commission,b.name as distributor_name,b.dis_role`).
		Join("left", "eshop.dis_distributor b", "a.dis_id=b.id").
		Where("a.shop_id=?", in.ShopId).Where("a.org_id=?", in.OrgId).Find(&out); err != nil {
		log.Errorf("%s 获取店铺佣金明细失败-错误为%s", logPrefix, err.Error())
		return nil, errors.New("获取佣金明细失败")
	}

	return
}

// 宠利扫的老板提现 入参校验
// 返回本次提现申请订单 总金额、需要退还的金额
func (h *DiscommissionService) CLSCommissionApplyValidate(in distribution_vo.DisCommissionApplyReq) (orderAmount, needReturnAmount, operateType int, cps []distribution_vo.DistributorSwlmCpsListRes, withdrawOrder []distribution_vo.WithdrawOrderItem, err error) {
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()

	var (
		withdrawSuccessOrder  = make([]string, 0) // 已经提现成功的订单
		deductCommissionOrder = make([]string, 0) // 扣除佣金订单(订单未提现过的情况下 ，操作了扣除佣金)
		orderNoSli            = make([]int64, 0)  // 订单号
		orderNoSliStr         = make([]string, 0) // 订单号
		logPrefix             = fmt.Sprintf("宠利扫老板佣金提现申请数据校验|分销员会员id=%d,orgId=%d", in.MemberId, in.OrgId)
		allOrderCnt           int
		AllOrderSn            = make([]int64, 0) // 全选的订单号
		refundOrderSnSlice    = make([]int64, 0) // 退款订单号
	)

	log.Infof("%s 入参=%s", logPrefix, utils.InterfaceToJSON(in))
	orderNoSliStr = strings.Split(in.OrderNo, ",")
	orderNoSli = utils.StringSliceToInt64Slice(orderNoSliStr)

	if in.ShopId == 0 {
		err = errors.New("店铺id不能为0")
		return
	}
	if in.IsApplyAllOrder && in.MaxOrderId == 0 {
		err = errors.New("最大订单id不能为0")
		return
	}

	cacheK := fmt.Sprintf(cachekey.CLSShopWithdrawOrder, in.OrgId, in.ShopId, in.DisId)
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	// 如果是全选，则需要获取所有订单
	if in.IsApplyAllOrder {

		orderSnSlice := mCache.Get(string(cache_source.EShop), cacheK)
		if len(orderSnSlice) > 0 {
			orderSnSliceStr := orderSnSlice[0].(string)
			orderSnSlice := strings.Split(orderSnSliceStr, ",")
			for _, v := range orderSnSlice {
				if cast.ToInt64(v) > 0 {
					AllOrderSn = append(AllOrderSn, cast.ToInt64(v))
				}
			}
		} else {
			log.Errorf("%s 全选的订单号不能为空,allOrderSn=%v", logPrefix, AllOrderSn)
			err = errors.New("全选的订单号不能为空")
			return

		}
	}
	//选中本页订单
	if !in.IsApplyAllOrder && len(orderNoSli) > 0 {

		// //判断该订单是否已经提现成功过
		if withdrawSuccessOrder, err = new(distribution_po.DisWithdrawOrder).GetSuccessWithdrawOrder(session, in.ShopId, orderNoSli); err != nil {
			log.Errorf("%s 获取提现成功订单失败-错误为%s", logPrefix, err.Error())
			err = errors.New("获取提现成功订单失败")
			return
		} else if len(withdrawSuccessOrder) > 0 {
			log.Errorf("订单号%s已提现成功", strings.Join(withdrawSuccessOrder, ","))
			operateType = 1
			return
		}

		//判断该订单是否已经自平账
		if deductCommissionOrder, err = new(distribution_po.DisDistributorSwlmCps).BanlancedSwlmCpsList(session, in.ShopId, orderNoSli); err != nil {
			log.Errorf("%s 获取扣除佣金订单失败-错误为%s", logPrefix, err.Error())
			err = errors.New("获取扣除佣金订单失败")
			return
		} else if len(deductCommissionOrder) > 0 {
			log.Errorf("订单号%s已扣除佣金并恢复物流码", strings.Join(deductCommissionOrder, ","))
			operateType = 1
			return
		}

		withdrawOrder, _, orderAmount, _, err = new(distribution_po.DisWithdrawOrder).GetWithdrawOrderList(session, distribution_po.GetWithdrawOrderListReq{
			OrgId:   in.OrgId,
			ShopId:  in.ShopId,
			OrderSn: orderNoSli,
		})

	} else if in.IsApplyAllOrder && len(orderNoSli) > 0 { // 代表要排除本页订单

		withdrawOrder, allOrderCnt, orderAmount, _, err = new(distribution_po.DisWithdrawOrder).GetWithdrawOrderList(session, distribution_po.GetWithdrawOrderListReq{
			OrgId:          in.OrgId,
			ShopId:         in.ShopId,
			ExcludeOrderSn: orderNoSli,
			MaxOrderId:     in.MaxOrderId,
			OrderSn:        AllOrderSn,
		})
		if allOrderCnt+len(orderNoSli) != in.AllOrderCnt {
			log.Errorf("您的当前可提现订单发生变化，请重新进入")
			operateType = 1
			return
		}

	} else if in.IsApplyAllOrder && len(orderNoSli) == 0 { //全部全选

		withdrawOrder, allOrderCnt, orderAmount, _, err = new(distribution_po.DisWithdrawOrder).GetWithdrawOrderList(session, distribution_po.GetWithdrawOrderListReq{
			OrgId:      in.OrgId,
			ShopId:     in.ShopId,
			MaxOrderId: in.MaxOrderId,
			OrderSn:    AllOrderSn,
		})
		if allOrderCnt != in.AllOrderCnt {
			log.Errorf("您的当前可提现订单发生变化，请重新进入")
			operateType = 1
			return
		}
	} else {
		err = errors.New("参数错误")
		return
	}

	if orderAmount <= 0 {
		err = errors.New("本次提现申请订单总金额不能为0")
		return
	}
	cacheK2 := fmt.Sprintf(cachekey.CLSShopWithdrawRefundOrder, in.OrgId, in.ShopId, in.DisId)
	refundOrderSnStr := mCache.Get(string(cache_source.EShop), cacheK2)
	if len(refundOrderSnStr) > 0 {
		refundOrderSnStr := refundOrderSnStr[0].(string)
		refundOrderSnSliceStr := strings.Split(refundOrderSnStr, ",")
		for _, v := range refundOrderSnSliceStr {
			if cast.ToInt64(v) > 0 {
				refundOrderSnSlice = append(refundOrderSnSlice, cast.ToInt64(v))
			}
		}
	}

	if cps, err = new(distribution_po.DisDistributorSwlmCps).DistributorSwlmCpsList(session, distribution_po.DistributorSwlmCpsListReq{
		ShopId:      in.ShopId,
		Status:      distribution_po.StatusNotDeduct,
		MustOrderSn: refundOrderSnSlice,
	}); err != nil {
		log.Errorf("%s 获取欠款订单列表失败-错误为%s", logPrefix, err.Error())
		err = errors.New("获取欠款订单列表失败")
		return
	}

	for _, v := range cps {
		needReturnAmount += v.Amount
	}

	if needReturnAmount > 0 && needReturnAmount > orderAmount {
		err = errors.New("本次提现申请订单总金额小于需要退还的金额")
		return
	}

	return
}

// 佣金提现 分销员校验
func (h *DiscommissionService) CommisApplyDisValidate(in distribution_vo.DisCommissionApplyReq) (d distribution_po.DisDistributor, updateCols []string, err error) {
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()

	updateCols = make([]string, 0)
	logPrefix := fmt.Sprintf("佣金提现-分销员校验|分销员会员id=%d,orgId=%d", in.MemberId, in.OrgId)
	log.Info(logPrefix, "入参为", utils.InterfaceToJSON(in))

	// 获取分销员信息
	if exists, e := session.Table("eshop.dis_distributor").Where("member_id=?", in.MemberId).Where("org_id=?", in.OrgId).Get(&d); e != nil {
		log.Errorf("%s 获取分销员信息失败-错误为%s", logPrefix, err.Error())
		err = errors.New("获取分销员信息失败")
		return
	} else if !exists {
		log.Errorf("%s 未找到分销员信息", logPrefix)
		err = errors.New("未找到分销员信息")
		return
	}

	if d.DisRole == disdistributor.DisRoleBoss && d.ShopId == 0 {
		err = errors.New("分销员没有绑定店铺")
		return
	}
	if d.OrgId != in.OrgId && in.OrgId != enum.BLKYOrgId {
		err = errors.New("不是宠利扫的提现")
		return
	}

	log.Info(logPrefix, "分销员信息：", utils.InterfaceToJSON(d))

	//  只有老板角色可以提现
	if d.DisRole != disdistributor.DisRoleBoss && d.DisRole != disdistributor.DisRoleDoctor {
		err = errors.New("只有老板和医生角色可以提现")
		return
	}

	if in.BankAccount == "" && d.BankAccount == "" {
		err = errors.New("请填写收款信息")
		return
	}
	if in.WithdrawIdCard == "" && d.WithdrawIdCard == "" {
		err = errors.New("请填写收款人的身份证号码")
		return
	}

	// 收款信息 入参校验
	if in.WithdrawIdCard != "" && in.WithdrawIdCard != d.WithdrawIdCard {
		if !idvalidator.IsValid(in.WithdrawIdCard, false) {
			err = errors.New("无效的身份证号")
			return
		}
		updateCols = append(updateCols, "withdraw_id_card", "encrypt_with_idcard")
		d.WithdrawIdCard = utils.AddStar(in.WithdrawIdCard)
		d.EncryptWithIdcard = utils.MobileEncrypt(in.WithdrawIdCard)
	}
	if in.AccountName != "" && in.AccountName != d.AccountName {
		if len([]rune(in.AccountName)) < 2 || len([]rune(in.AccountName)) > 20 {
			err = errors.New("收款人长度是2-20")
			return
		}
		updateCols = append(updateCols, "account_name")
		d.AccountName = in.AccountName
	}
	if in.BankName != "" && in.BankName != d.BankName {
		if len([]rune(in.BankName)) < 2 || len([]rune(in.BankName)) > 20 {
			err = errors.New("无效的银行名称")
			return
		}
		updateCols = append(updateCols, "bank_name")
		d.BankName = in.BankName
	}

	if in.BankAccount != "" && in.BankAccount != d.BankAccount {
		if !utils.IsBankCardValid(in.BankAccount) {
			err = errors.New("无效的收款账号")
			return
		}
		updateCols = append(updateCols, "bank_account", "encrypt_bank_account")
		d.BankAccount = utils.AddStar(in.BankAccount)
		d.EncryptBankAccount = utils.MobileEncrypt(in.BankAccount)
	}
	if in.BankBranch != "" && in.BankBranch != d.BankBranch {
		updateCols = append(updateCols, "bank_branch")
		d.BankBranch = in.BankBranch
	}
	//判断手机号
	if in.BankMobile != "" && in.BankMobile != d.BankMobile {
		if !utils.IsMobile(in.BankMobile) {
			err = errors.New("无效的手机号")
			return
		}
		updateCols = append(updateCols, "bank_mobile", "bank_encrypt_mobile")
		d.BankMobile = utils.AddStar(in.BankMobile)
		d.BankEncryptMobile = utils.MobileEncrypt(in.BankMobile)
	}

	if len(updateCols) == 0 && (d.AccountName == "" || d.EncryptWithIdcard == "" || d.WithdrawIdCard == "" || d.BankAccount == "" || d.EncryptBankAccount == "" || d.BankName == "" || d.BankBranch == "") {
		err = errors.New("请填写正确的收款人、收款身份证号、收款账号、收款银行、收款支行")
		return
	}
	return

}

// 宠利扫的老板提现
// 操作类型：当c端用户停在可提现订单页时， 有订单A, 当订单A在后台管理扣除佣金并恢复物流码时 返回1， 前端根据这个值去刷新可提现订单页
func (h *DiscommissionService) CLSCommissionApply(in distribution_vo.DisCommissionApplyReq) (operateType int, err error) {
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()

	logPrefix := fmt.Sprintf("宠利扫老板佣金提现申请|分销员会员id=%d,orgId=%d", in.MemberId, in.OrgId)
	log.Infof("%s 入参=%s", logPrefix, utils.InterfaceToJSON(in))

	var (
		d                distribution_po.DisDistributor                           //分销员信息
		updateCols       []string                                                 //分销员信息 需要更新的字段
		needUpdateMap                                   = make(map[string]string) //需要更新的map
		withdrawFlag     int                            = enum.WithdrawFlagShop   //提现标志
		orderAmount      int                                                      //本次提现申请订单 总金额
		needReturnAmount int                                                      //需要退还的金额
		amount           int                                                      //本次提现申请金额
		cps              []distribution_vo.DistributorSwlmCpsListRes
		withdrawOrder    []distribution_vo.WithdrawOrderItem
	)
	defer func() {
		if err := recover(); err != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER-提现申请] %v %s\n", err, stack[:length])
			err = errors.New("提现申请失败PANIC")
		}
	}()

	// 获取并校验分销员信息
	if d, updateCols, err = h.CommisApplyDisValidate(in); err != nil {
		return
	}

	// 校验提现订单数据
	in.ShopId = d.ShopId
	in.DisId = d.Id
	if orderAmount, needReturnAmount, operateType, cps, withdrawOrder, err = h.CLSCommissionApplyValidate(in); err != nil {
		return
	} else if operateType == 1 {
		return
	}
	amount = orderAmount - needReturnAmount

	//第一步： redis加锁，防止并发（目前仅支持老板提现，加锁只需加到企业上， 防止企业更换老板的情况）
	cacheK := fmt.Sprintf(cachekey.ShopWithdrawAmountUpdate, d.OrgId, d.ShopId)
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cacheK, time.Minute*10)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, cacheK)
		err = errors.New("正在处理提现申请，请稍后再试")
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cacheK)

	//判断可提现金额是否足够
	session.Begin()

	// 查询入参提现订单总金额是否大于 已欠账金额

	// 获取 提现税率
	rate := utils.WithdrawTaxRate(in.OrgId)
	// 计算所需缴纳税金额（浮点型计算，避免精度丢失）
	// 税后金额：税前金额-税前金额*（1-1/1.08），直接取小数点后两位，不进行四舍五入
	decimalValue := decimal.NewFromFloat(cast.ToFloat64(amount)) // 税前金额
	one := decimal.NewFromFloat(1)
	rateDecimal := decimal.NewFromFloat(cast.ToFloat64(rate))
	taxRate := one.Sub(one.Div(one.Add(rateDecimal)))  // 计算1-1/(1+rate)
	taxAmount := decimalValue.Mul(taxRate).Truncate(2) // 税额，直接截取2位小数
	AfterTaxAmount := decimalValue.Sub(taxAmount).IntPart()

	w := distribution_po.DisWithdraw{
		Id:                 0,
		OrgId:              d.OrgId,
		ShopId:             in.ShopId,
		DisRole:            d.DisRole,
		WithdrawNo:         utils.GenerateNo(enum.WithdrawNoPrefix),
		Status:             disWithdraw.StatusUncheck,
		DistributorId:      d.Id,
		PreTaxAmount:       cast.ToInt(amount),
		AfterTaxAmount:     cast.ToInt(AfterTaxAmount),
		BankName:           d.BankName,
		BankAccount:        d.BankAccount,
		EncryptBankAccount: d.EncryptBankAccount,
		AccountName:        d.AccountName,
		IdCard:             d.WithdrawIdCard,
		EncryptIdCard:      d.EncryptWithIdcard,
		BankBranch:         d.BankBranch,
		CreateTime:         time.Now(),
		UpdateTime:         time.Now(),
		BankEncryptMobile:  d.BankEncryptMobile,
		BankMobile:         d.BankMobile,
	}

	// 第二步: 插入一条提现信息
	if _, err = session.Insert(&w); err != nil {
		log.Errorf("%s-插入提现数据失败-错误为%s", logPrefix, err.Error())
		session.Rollback()
		err = errors.New("插入提现数据失败")
		return
	}

	// 第三步：更新分销员的 银行取款信息
	if len(updateCols) > 0 {
		if _, err = session.Cols(updateCols...).ID(d.Id).Update(d); err != nil {
			log.Errorf("%s-修改分销员可提现金额和提现中金额失败-错误为%s", logPrefix, err.Error())
			session.Rollback()
			err = errors.New("提现申请失败")
			return
		}
	}

	// 第四步： eshop.dis_withdraw_order 插入数据
	insertWithdrawOrder := make([]*distribution_po.DisWithdrawOrder, 0)
	for _, v := range withdrawOrder {
		insertWithdrawOrder = append(insertWithdrawOrder, &distribution_po.DisWithdrawOrder{
			WithdrawNo: w.WithdrawNo,
			OrderSn:    cast.ToInt64(v.OrderSn),
			GoodsId:    v.GoodsId,
			OrgType:    v.OrgType,
			SwlmCpsId:  0,
		})
	}
	cpsOrderSnSli := make([]int64, 0)
	for _, v := range cps {
		cpsOrderSnSli = append(cpsOrderSnSli, cast.ToInt64(v.OrderSn))
		insertWithdrawOrder = append(insertWithdrawOrder, &distribution_po.DisWithdrawOrder{
			WithdrawNo: w.WithdrawNo,
			OrderSn:    cast.ToInt64(v.OrderSn),
			GoodsId:    v.GoodsId,
			OrgType:    v.OrgType,
			SwlmCpsId:  v.SwlmCpsId,
		})
	}
	if _, err = session.Insert(insertWithdrawOrder); err != nil {
		log.Errorf("%s-批量插入提现订单-错误为%s", logPrefix, err.Error())
		session.Rollback()
		err = errors.New("提现申请失败")
		return
	}
	// 第五步：修改eshop.dis_distributor_swlm_cps状态
	if len(cpsOrderSnSli) > 0 {
		if _, err = session.Table("eshop.dis_distributor_swlm_cps").In("order_sn", cpsOrderSnSli).Update(map[string]interface{}{"status": distribution_po.StatusDeducting, "update_time": time.Now()}); err != nil {
			log.Errorf("%s-修改逆向订单状态失败-错误为%s", logPrefix, err.Error())
			session.Rollback()
			err = errors.New("提现申请失败")
			return
		}
	}
	// 第六步： 批量插入 dis_withdraw_record 数据
	insertWithdrawRecord := make([]*distribution_po.DisWithdrawRecord, 0)
	for _, v := range withdrawOrder {
		needUpdateMap[fmt.Sprintf("%d:%d:%d", w.OrgId, w.ShopId, v.DisId)] = ""
		insertWithdrawRecord = append(insertWithdrawRecord, &distribution_po.DisWithdrawRecord{
			OrgId:         w.OrgId,
			ShopId:        w.ShopId,
			DisId:         v.DisId,
			WithdrawDisId: w.DistributorId,
			Type:          diswithdrawrecord.TypeApply,
			ThirdId:       cast.ToInt64(w.Id),
			WaitWithdraw:  -v.Amount,
			WithdrawApply: v.Amount,
		})
	}

	// 申请中， 也平账， 等拒绝时，则再次插入一个扣减的记录
	for _, v := range cps {
		needUpdateMap[fmt.Sprintf("%d:%d:%d", w.OrgId, w.ShopId, v.DisId)] = ""
		insertWithdrawRecord = append(insertWithdrawRecord, &distribution_po.DisWithdrawRecord{
			OrgId:         w.OrgId,
			ShopId:        w.ShopId,
			DisId:         v.DisId,
			WithdrawDisId: w.DistributorId,
			Type:          diswithdrawrecord.TypeBalance,
			ThirdId:       cast.ToInt64(w.Id),
			WaitWithdraw:  v.Amount,
		})
	}

	// 第七步： 修改 dis_distributor_total.wait_withdraw , withdraw_apply,withdraw_success字段 以及shop.wait_withdraw withdraw_apply,withdraw_success字段

	if _, err = session.Insert(insertWithdrawRecord); err != nil {
		log.Errorf("%s-批量插入提现记录-错误为%s", logPrefix, err.Error())
		session.Rollback()
		err = errors.New("提现申请失败")
		return
	}

	session.Commit()
	//修改 dis_distributor_total.wait_withdraw , withdraw_apply字段 以及shop.wait_withdraw withdraw_apply
	go h.WithdrawCommUpdate(needUpdateMap, withdrawFlag)
	return
}
func (h *DiscommissionService) DisCommissionApply(in distribution_vo.DisCommissionApplyReq) (err error) {
	logPrefix := fmt.Sprintf("提现申请|分销员会员id=%d,orgId=%d", in.MemberId, in.OrgId)
	log.Infof("%s 入参=%s", logPrefix, utils.InterfaceToJSON(in))

	defer func() {
		if err := recover(); err != nil {
			stack := make([]byte, 4<<10) //4KB
			length := runtime.Stack(stack, false)
			log.Errorf("[PANIC RECOVER-提现申请] %v %s\n", err, stack[:length])
			err = errors.New("提现申请失败PANIC")
		}
	}()

	// 获取分销员信息
	var (
		d             distribution_po.DisDistributor
		updateCols    []string
		needUpdateMap = make(map[string]string)
		shopId        int
	)
	h.Begin()
	defer h.Close()

	// 获取分销员信息
	if d, updateCols, err = h.CommisApplyDisValidate(in); err != nil {
		return err
	}

	shopId = d.ShopId
	// 提现维度： shop代表提现店铺金额  myself代表分销员个人
	withdrawFlag := enum.WithdrawFlagShop
	if in.OrgId == enum.BLKYOrgId && d.DisRole == distribution_po.DisRoleDoctor {
		withdrawFlag = enum.WithdrawFlagSelf
	} else if in.OrgId == enum.BLKYOrgId && d.DisRole != distribution_po.DisRoleDoctor && in.IsWithdrawBLKYDoctorMoney {
		// 百林康源的医生角色， 后来入驻了企业， 想要提现曾经作为百林康源医生角色的可提现金额
		withdrawFlag = enum.WithdrawFlagSelf
		shopId = 0
	}
	log.Info(logPrefix, "分销员信息：", utils.InterfaceToJSON(d))

	//  只有老板角色可以提现
	if withdrawFlag == enum.WithdrawFlagShop {
		if d.DisRole != disdistributor.DisRoleBoss {
			return errors.New("只有老板才可提现")
		}
	}

	// 宠利扫的老板提现， 不走这里！因为宠利扫的老板提现，是根据分销订单来提现的。
	if in.OrgId == enum.BLKYOrgId && d.DisRole == distribution_po.DisRoleBoss {
		return errors.New("宠利扫的老板提现， 不走这里！因为宠利扫的老板提现，是根据分销订单来提现的。")
	}

	if in.Amount <= 0 {
		return errors.New("提现金额必须大于0")
	}

	//第一步： redis加锁，防止并发（目前仅支持老板提现，加锁只需加到企业上， 防止企业更换老板的情况）
	cacheK := fmt.Sprintf(cachekey.ShopWithdrawAmountUpdate, d.OrgId, d.ShopId)
	if withdrawFlag == enum.WithdrawFlagSelf {
		cacheK = fmt.Sprintf(cachekey.WithdrawAmountUpdate, d.OrgId, d.ShopId, d.Id)
	}
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cacheK, time.Minute*10)
	if !setNxReslt {
		log.Errorf("%s-设置redis锁(%s)失败", logPrefix, cacheK)
		return errors.New("正在处理提现申请，请稍后再试")
	}
	defer mCache.Delete(string(cache_source.EShop), cacheK)

	//判断可提现金额是否足够
	session := h.Engine.NewSession()
	defer session.Close()
	session.Begin()
	where := distribution_po.DisWithdrawRecord{ShopId: shopId, OrgId: d.OrgId}
	if withdrawFlag == enum.WithdrawFlagSelf {
		where.DisId = d.Id
	}
	if waitWithdraw, err := session.Table("eshop.dis_withdraw_record").SumInt(&where, "wait_withdraw"); err != nil {
		log.Errorf("%s-查询可提现金额失败：%v", logPrefix, err.Error())
		session.Rollback()
		return errors.New("查询可提现金额失败")
	} else {
		if in.Amount > cast.ToInt(waitWithdraw) {
			log.Errorf("%s-可提现金额不足(%d)", logPrefix, waitWithdraw)
			session.Rollback()
			return errors.New("可提现金额不足")
		}
	}
	// 获取 提现税率
	rate := utils.WithdrawTaxRate(in.OrgId)
	// 计算所需缴纳税金额（浮点型计算，避免精度丢失）
	// decimalValue := decimal.NewFromFloat(cast.ToFloat64(in.Amount))                    //提现金额
	// decimalValue = decimalValue.Mul(decimal.NewFromFloat(rate)).Round(0)               //需要缴纳的税的金额
	// AfterTaxAmount := decimal.NewFromInt(int64(in.Amount)).Sub(decimalValue).IntPart() //税后金额
	// 税后金额：税前金额-税前金额*（1-1/1.08），直接取小数点后两位，不进行四舍五入
	decimalValue := decimal.NewFromFloat(cast.ToFloat64(in.Amount)) // 税前金额
	one := decimal.NewFromFloat(1)
	rateDecimal := decimal.NewFromFloat(cast.ToFloat64(rate))
	taxRate := one.Sub(one.Div(one.Add(rateDecimal)))  // 计算1-1/(1+rate)
	taxAmount := decimalValue.Mul(taxRate).Truncate(2) // 税额，直接截取2位小数
	AfterTaxAmount := decimalValue.Sub(taxAmount).IntPart()

	w := distribution_po.DisWithdraw{
		Id:                 0,
		OrgId:              d.OrgId,
		ShopId:             shopId,
		DisRole:            d.DisRole,
		WithdrawNo:         utils.GenerateNo(enum.WithdrawNoPrefix),
		Status:             disWithdraw.StatusUncheck,
		DistributorId:      d.Id,
		PreTaxAmount:       in.Amount,
		AfterTaxAmount:     cast.ToInt(AfterTaxAmount),
		BankName:           d.BankName,
		BankAccount:        d.BankAccount,
		EncryptBankAccount: d.EncryptBankAccount,
		AccountName:        d.AccountName,
		IdCard:             d.WithdrawIdCard,
		EncryptIdCard:      d.EncryptWithIdcard,
		BankBranch:         d.BankBranch,
		CreateTime:         time.Now(),
		UpdateTime:         time.Now(),
		BankEncryptMobile:  d.BankEncryptMobile,
		BankMobile:         d.BankMobile,
	}

	// 第二步: 插入一条提现信息
	if _, err = session.Insert(&w); err != nil {
		log.Errorf("%s-插入提现数据失败-错误为%s", logPrefix, err.Error())
		session.Rollback()
		return errors.New("插入提现数据失败")
	}

	// 第三步：更新分销员的 银行取款信息
	if len(updateCols) > 0 {
		if _, err := session.Cols(updateCols...).ID(d.Id).Update(d); err != nil {
			log.Errorf("%s-修改分销员可提现金额和提现中金额失败-错误为%s", logPrefix, err.Error())
			session.Rollback()
			return errors.New("提现申请失败")
		}
	}

	// 第四步： 修改金额

	list := make([]*struct {
		DisId        int
		WaitWithdraw int // 可提现金额
		Amount       int // 提现金额
	}, 0)
	sql := `select sum(wait_withdraw) as wait_withdraw,dis_id from dis_withdraw_record where org_id=? and shop_id=? `
	if withdrawFlag == enum.WithdrawFlagSelf {
		sql = fmt.Sprintf("%s and dis_id=%d", sql, d.Id)
	}

	if err := session.SQL(fmt.Sprintf("%s group by org_id,shop_id,dis_id order by dis_id asc", sql), w.OrgId, w.ShopId).Find(&list); err != nil {
		log.Errorf("%s-获取该企业下的各分销员可用余额失败-错误为%s", logPrefix, err.Error())
		session.Rollback()
		return errors.New("获取可提现余额失败")
	}

	// 老板提现的金额 利用 先进先出的原则 分摊到各分销员
	for _, v := range list {
		if v.WaitWithdraw == 0 {
			continue
		}
		x := in.Amount - v.WaitWithdraw
		if x > 0 {
			v.Amount = v.WaitWithdraw
			in.Amount -= v.WaitWithdraw
		} else {
			v.Amount = in.Amount
			break
		}
	}
	insertSli := make([]*distribution_po.DisWithdrawRecord, 0)
	for _, v := range list {
		if v.Amount == 0 {
			continue
		}
		needUpdateMap[fmt.Sprintf("%d:%d:%d", d.OrgId, d.ShopId, v.DisId)] = ""
		insertSli = append(insertSli, &distribution_po.DisWithdrawRecord{
			OrgId:         w.OrgId,
			ShopId:        w.ShopId,
			DisId:         v.DisId, //  计算出具体提到谁的身上
			WithdrawDisId: w.DistributorId,
			Type:          diswithdrawrecord.TypeApply,
			ThirdId:       cast.ToInt64(w.Id),
			WaitWithdraw:  -v.Amount,
			WithdrawApply: v.Amount,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		})

	}
	if _, err := session.Insert(insertSli); err != nil {
		log.Errorf("%s-批量插入提现记录-错误为%s", logPrefix, err.Error())
		session.Rollback()
		return errors.New("提现申请失败")
	}

	session.Commit()
	//修改 dis_distributor_total.wait_withdraw , withdraw_apply字段 以及shop.wait_withdraw withdraw_apply
	go h.WithdrawCommUpdate(needUpdateMap, withdrawFlag)
	return
}

// 获取 提现税率
// func (h *DiscommissionService) WithdrawTaxRate(orgId int) string {
// 	taxRate := config.Get(fmt.Sprintf(enum.WithdrawTaxRate, orgId))
// 	log.Info(fmt.Sprintf("提现税率-orgId=%d,taxRate=%s", orgId, taxRate))
// 	if taxRate == "" {
// 		taxRate = enum.CommissionTaxRate
// 	}

// 	return taxRate
// }

// 修改 dis_distributor_total和shop表wait_withdraw,withdraw_apply,withdraw_success
// map[key]value 里的key是orgId:shopId:distributorId。 value是是指要更新的列（wait_withdraw,withdraw_apply,withdraw_success）
// needUpdateMap[]
// withdrawFlag 提现维度： 1-代表店铺维度  2-代表分销员个人维度
func (h *DiscommissionService) WithdrawCommUpdate(needUpdateMap map[string]string, withdrawFlag int, orgId ...int) {
	h.Begin()
	defer h.Close()
	logPrefix := "可提现金额和提现中金额和提现成功金额的写入"
	//log.Info(logPrefix, "入参：", utils.InterfaceToJSON(needUpdateMap))
	// 匿名函数 tableName为eshop.dis_distributor_total或eshop.shop
	f := func(tableName string, param []int) error {
		if tableName != "eshop.dis_distributor_total" && tableName != "eshop.shop" {
			return errors.New("tableName参数错误")
		}
		if len(param) != 3 && len(param) != 2 {
			return errors.New("参数错误")
		}
		sql := fmt.Sprintf(`select sum(wait_withdraw) as wait_withdraw,sum(withdraw_apply) as withdraw_apply,sum(withdraw_success) as withdraw_success from eshop.dis_withdraw_record where org_id=%d and shop_id=%d`, param[0], param[1])
		if tableName == "eshop.dis_distributor_total" {
			sql = fmt.Sprintf("%s and dis_id=%d", sql, param[2])
		}
		tmp := struct {
			WaitWithdraw    int
			WithdrawApply   int
			WithdrawSuccess int
		}{}

		if _, err := h.Session.SQL(sql).Get(&tmp); err != nil {
			log.Errorf("%s-获取分销员提现金额失败-错误为%s", logPrefix, err.Error())
			return err
		}
		session := h.Session.Table(tableName).Cols("wait_withdraw,withdraw_apply,withdraw_success")
		if tableName == "eshop.dis_distributor_total" {
			session = session.Where("org_id=?", param[0]).Where("shop_id=?", param[1]).Where("dis_id=?", param[2])
		} else if tableName == "eshop.shop" {
			session = session.Where("id=?", param[1]).Where("org_id=?", param[0])
		} else {
			return errors.New("tableName参数错误")
		}
		if _, err := session.Update(map[string]interface{}{
			"wait_withdraw":    tmp.WaitWithdraw,
			"withdraw_apply":   tmp.WithdrawApply,
			"withdraw_success": tmp.WithdrawSuccess,
		}); err != nil {
			log.Errorf("%s-更新分销员提现金额失败-错误为%s", logPrefix, err.Error())
			return err
		}
		return nil
	}

	shopMap := make(map[string]int)
	if len(needUpdateMap) > 0 {
		for k := range needUpdateMap {
			ksli := strings.Split(k, ":")
			if withdrawFlag == enum.WithdrawFlagShop {
				if _, ok := shopMap[fmt.Sprintf("%s:%s", ksli[0], ksli[1])]; !ok {
					shopMap[fmt.Sprintf("%s:%s", ksli[0], ksli[1])] = 1
					f("eshop.shop", []int{cast.ToInt(ksli[0]), cast.ToInt(ksli[1])})
				}
			}

			f("eshop.dis_distributor_total", []int{cast.ToInt(ksli[0]), cast.ToInt(ksli[1]), cast.ToInt(ksli[2])})
		}
	} else {
		//如果没有传入值，则全部统计一遍
		recordList := make([]*distribution_po.DisWithdrawRecord, 0)
		sql := `select org_id,shop_id,dis_id from eshop.dis_withdraw_record `
		if len(orgId) > 0 {
			sql = fmt.Sprintf("%s where org_id =%d", sql, orgId[0])
		}
		sql = fmt.Sprintf("%s group by org_id,shop_id,dis_id", sql)
		if err := h.Session.SQL(sql).Find(&recordList); err != nil {
			log.Errorf("%s-获取提现记录失败-错误为%s", logPrefix, err.Error())
			return
		}
		for _, v := range recordList {
			if withdrawFlag == enum.WithdrawFlagShop {
				if _, ok := shopMap[fmt.Sprintf("%d:%d", v.OrgId, v.ShopId)]; !ok {
					shopMap[fmt.Sprintf("%d:%d", v.OrgId, v.ShopId)] = 1
					f("eshop.shop", []int{v.OrgId, v.ShopId})
				}
			}

			f("eshop.dis_distributor_total", []int{v.OrgId, v.ShopId, v.DisId})
		}

	}

}
