package services

import (
	"context"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/olivere/elastic/v7"
)

type OutService struct {
	common.BaseService
}

// 体制外医院导入到es，方便快速搜索
var IndexScrmLeadsAutonavi = "scrm_leads_autonavi"

// 获取瑞鹏体制外的医院列表
func (s OutService) OutHospitalList(req vo.OutHospitalListReq) (out []vo.S, err error) {
	logPrefix := fmt.Sprintf("查询瑞鹏体制外的医院列表：入参：%s", utils.InterfaceToJSON(req))
	log.Info(logPrefix)
	s.Begin()
	defer s.Close()
	log.Info("获取瑞鹏体制外医院列表,入参：", utils.InterfaceToJSON(req))
	//session := s.Session
	name := strings.Trim(req.HospitalName, " ")
	if name == "" {
		return nil, errors.New("请输入医院名称")
	}

	out = make([]vo.S, 0)

	// 创建查询

	query := elastic.NewBoolQuery().
		Must(elastic.NewWildcardQuery("city", fmt.Sprintf("%s*", req.CityName))).
		Must(elastic.NewWildcardQuery("shop", fmt.Sprintf("*%s*", name)))

	es := utils.NewEsClient()

	// 执行搜索
	searchResult, err := es.Search().
		Index(IndexScrmLeadsAutonavi).
		Query(query).
		Size(10000).
		Do(context.Background())
	if err != nil {
		log.Error(logPrefix, "查询es失败，err=", err.Error())
		return nil, errors.New("查询数据失败")
	}

	// 处理搜索结果
	for _, hit := range searchResult.Hits.Hits {
		tmp := vo.ScrmLeadsAutonavi{}
		if err := json.Unmarshal([]byte(hit.Source), &tmp); err != nil {
			log.Error(logPrefix, "解析数据失败，err=", err.Error())
			return nil, errors.New("解析数据失败")
		}
		out = append(out, vo.S{Shop: tmp.Shop})

	}

	return out, nil

}

func (s OutService) UpdateOutHospitalToEs() {

	s.Begin()
	defer s.Close()
	logPrefix := "将瑞鹏体制外医院列表写入es"
	log.Info(logPrefix)
	data := make([]*vo.ScrmLeadsAutonavi, 0)
	if err := s.Session.Table("eshop.scrm_leads_autonavi").Find(&data); err != nil {
		log.Error(logPrefix, "获取数据失败：err=", err.Error())
		return
	}
	esclient := utils.NewEsClient()
	if exists, err := utils.IndexExists(esclient, IndexScrmLeadsAutonavi); err != nil {
		log.Error(logPrefix, "判断索引是否已经存在发生错误，err=", err.Error())
		return
	} else if exists {

		_, err := esclient.DeleteIndex(IndexScrmLeadsAutonavi).Do(context.Background())
		if err != nil {
			log.Error(logPrefix, "Error deleting index:", err.Error())
			return
		}
		log.Info(logPrefix, "删除索引成功")
	}

	bulkservice := esclient.Bulk()

	for _, v := range data {
		// esRes, err := bp.Index().Index(IndexScrmLeadsAutonavi).Id(fmt.Sprintf("IndexScrmLeadsAutonavi-%d", v.Id)).BodyJson(v).Do(context.Background())
		// if err != nil {
		// 	log.Error(logPrefix, "插入数据到es失败，err=", err.Error())
		// 	return
		// }
		// log.Info(logPrefix, "同步数据：", utils.InterfaceToJSON(esRes))
		req := elastic.NewBulkIndexRequest().Index(IndexScrmLeadsAutonavi).Id(fmt.Sprintf("IndexScrmLeadsAutonavi-%d", v.Id)).Doc(v)
		bulkservice.Add(req)
	}
	_, err := bulkservice.Do(context.Background())
	if err != nil {
		log.Error(logPrefix, "插入数据到es失败，err=", err.Error())
		return
	}
}

func CreateOutHospitalESIndex() error {
	logPrefix := "创建IndexScrmLeadsAutonavi索引"
	client := utils.NewEsClient()

	exists, err := utils.IndexExists(client, IndexScrmLeadsAutonavi)
	if err != nil {
		log.Fatalf("%s Error checking if index exists: %v", logPrefix, err)
	}

	//如果索引已经存在了。就不创建了
	if exists {
		return nil
	}

	settings := `{
	"mappings": {
		"properties": {
			"address": {
				"type": "text"
			},
			"city": {
				"type": "text"
			},
			"create_time": {
				"type": "text"
			},
			"district": {
				"type": "text"
			},
			"id": {
				"type": "long"
			},
			"json_str": {
				"type": "text"
			},
			"latitude": {
				"type": "text"
			},
			"longitude": {
				"type": "text"
			},
			"province": {
				"type": "text"
			},
			"shop": {
				"type": "text"
			},
			"tel": {
				"type": "text"
			},
			"un_key": {
				"type": "text"
			},
			"update_time": {
				"type": "text"
			}
		}

	}
}`

	err = utils.CreateIndexWithMappingAndSettings(client, IndexScrmLeadsAutonavi, settings)
	if err != nil {
		log.Error(logPrefix, "Error creating index with mapping and settings: ", err.Error())
		return err
	}

	return nil

}
