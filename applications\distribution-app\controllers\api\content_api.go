package api

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"net/http"
)

// @Summary 获取内容标签列表
// @Description
// @Tags 小程序接口-获取内容信息
// @Accept json
// @Produce json
// @Param TagListReq query vo.TagListReq true " "
// @Success 200 {object} vo.TagListRes
// @Failure 400 {object} vo.TagListRes
// @Router /api/centent/tag-list [Post]
func TagList(writer http.ResponseWriter, request *http.Request) {
	resp := vo.TagListRes{}
	resp.Code = 200

	resp.Data = append(resp.Data, vo.TagInfo{Name: "肠胃", Img: "https://file.vetscloud.com/eabaa7fa6e8225584a7739b3ff6e00cf.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "皮肤", Img: "https://file.vetscloud.com/56104a31be1f094bf1bc339c7de0adf7.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "泌尿", Img: "https://file.vetscloud.com/0058906a33be703fb61b2ecd84c4415b.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "肾脏", Img: "https://file.vetscloud.com/00b7cc37f272f1d8142a4e55c8902997.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "肝胆", Img: "https://file.vetscloud.com/fd4212fb5b3829822861e5721e7483dd.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "心脏", Img: "https://file.vetscloud.com/895430298978712f350bf8de6a69a325.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "胰腺", Img: "https://file.vetscloud.com/55dbca67df5fbd8181a9913916acb621.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "关节", Img: "https://file.vetscloud.com/e566bf04daf9986cf57d2666bb4425e8.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "骨骼", Img: "https://file.vetscloud.com/0ec6f53fb718ee86331d65f1c305171d.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "呼吸系统", Img: "https://file.vetscloud.com/bb4326d0c43a87312e6ac8175129632b.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "繁育", Img: "https://file.vetscloud.com/01936f1313417ceaec640dbc454200bb.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "过敏", Img: "https://file.vetscloud.com/1387bffdcf17629f5b130779edcd3e63.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "免疫", Img: "https://file.vetscloud.com/249d4b0ace053d6268de0befc6b8a729.png"})
	resp.Data = append(resp.Data, vo.TagInfo{Name: "综合营养", Img: "https://file.vetscloud.com/f243fc012ded59ab3eb721c3d6751482.png"})

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 获取文章列表
// @Description
// @Tags 小程序接口-获取内容信息
// @Accept json
// @Produce json
// @Param ArticleListReq query vo.ArticleListReq true " "
// @Success 200 {object} vo.ArticleListRes
// @Failure 400 {object} vo.ArticleListRes
// @Router /api/centent/article-list [Post]
func ArticleList(writer http.ResponseWriter, request *http.Request) {
	resp := vo.ArticleListRes{}
	resp.Code = 400

	server := services.BlkyContentService{}
	req, err := utils.Bind[vo.ArticleListReq](request)
	if req.OrgId == 0 {
		req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	}

	if err != nil {
		log.Error("获取业务员列表，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取业务员列表，参数解析失败：" + err.Error())
	} else {
		data, err := server.ArticleList(req)
		if err != nil {
			log.Error("获取文章列表失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取文章列表失败：" + err.Error())
		} else {
			resp.Code = 200
			resp.Data = data
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// @Summary 搜索文章列表
// @Description 搜索文章列表接口
// @Tags 小程序接口-获取内容信息
// @Accept json
// @Produce json
// @Param SearchArticleListReq query vo.SearchArticleListReq true " "
// @Success 200 {object} vo.SearchArticleListRes
// @Failure 400 {object} vo.SearchArticleListRes
// @Router /api/centent/search_article-list [Post]
func SearchArticleList(writer http.ResponseWriter, request *http.Request) {
	resp := vo.SearchArticleListRes{}
	resp.Code = 400

	server := services.BlkyContentService{}
	req, err := utils.Bind[vo.SearchArticleListReq](request)
	if req.OrgId == 0 {
		req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	}

	if err != nil {
		log.Error("获取业务员列表，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取业务员列表，参数解析失败：" + err.Error())
	} else {
		resp.Data, resp.Total, err = server.SearchArticleList(req)
		if err != nil {
			log.Error("获取文章列表失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取文章列表失败：" + err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
