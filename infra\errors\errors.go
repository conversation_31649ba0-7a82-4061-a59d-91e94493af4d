package errors

import "fmt"

type Error struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func (e *Error) Error() string {
	return e.Message
}

func New(message string) *Error {
	return &Error{
		Code:    400,
		Message: message,
	}
}

func NewBadRequest(message string) *Error {
	return &Error{
		Code:    400,
		Message: message,
	}
}

func NewUnauthorized(message string) *Error {
	return &Error{
		Code:    401,
		Message: message,
	}
}

func NewForbidden(message string) *Error {
	return &Error{
		Code:    403,
		Message: message,
	}
}

func NewNotFound(message string) *Error {
	return &Error{
		Code:    404,
		Message: message,
	}
}

func NewInternalError(message string) *Error {
	return &Error{
		Code:    500,
		Message: message,
	}
}

func Wrap(err error, message string) *Error {
	if err == nil {
		return nil
	}

	if e, ok := err.(*Error); ok {
		return &Error{
			Code:    e.Code,
			Message: fmt.Sprintf("%s: %s", message, e.Message),
		}
	}

	return &Error{
		Code:    500,
		Message: fmt.Sprintf("%s: %s", message, err.Error()),
	}
}
