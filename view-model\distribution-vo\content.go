package distribution_vo

import (
	"eShop/view-model"
)

type TagListRes struct {
	viewmodel.BaseHttpResponse
	Data []TagInfo `json:"data"`
}

type TagInfo struct {
	Name string `json:"name"`
	Img  string `json:"img"`
}

type TagListReq struct {
}

type ArticleListRes struct {
	viewmodel.BaseHttpResponse
	Data []ContentInfo `json:"data"`
}

type ArticleListReq struct {
	//标签
	Tags string `json:"tags"`
	//关键字
	Keyword string `json:"keyword"`
	OrgId   int    `json:"org_id"`
}

type ContentInfo struct {
	Id    int    `json:"id"`
	Title string `json:"title"`
	//0:标签  1:文章 2:商品
	Type int    `json:"type"`
	Img  string `json:"img"`
	//相关描述
	Content   string        `json:"content"`
	Childrens []ContentInfo `json:"childrens"`
	//更新时间
	UpdatedAt string `json:"updated_at"`
}

type SearchArticleListReq struct {
	//1:搜索  2：推荐
	ArticleType int `json:"article_type"`
	//关键字
	Keyword string `json:"keyword"`
	OrgId   int    `json:"org_id"`
	//页码
	PageIndex int `json:"page_index"`
	//页大小
	PageSize int `json:"page_size"`
}

type SearchArticleListRes struct {
	viewmodel.BasePageHttpResponse
	Data []SearchContentInfo `json:"data"`
}

type SearchContentInfo struct {
	Id    int    `json:"id"`
	Title string `json:"title"`
	Img   string `json:"img"`
	//0:标签  1:文章 2:商品
	Type int `json:"type"`
	// 相关描述
	Content string `json:"content"`
	//更新时间
	UpdatedAt string `json:"updated_at"`
}
