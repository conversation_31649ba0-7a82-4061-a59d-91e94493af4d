package distribution

//数据统计 定时任务
import (
	"eShop/services/distribution-service/services"

	"github.com/robfig/cron/v3"
)

func InitStatsTask() {
	c := cron.New(cron.WithSeconds())
	//分销企业数据统计
	statsEnterpriseService := services.StatsEnterpriseService{}
	// 说明：每天凌晨1点执行一次
	c.AddFunc("0 0 1 * * *", statsEnterpriseService.StatsEnterpriseDailyDefalut)
	//说明：每周一凌晨1点执行一次
	c.AddFunc("0 0 1 * * 1", statsEnterpriseService.StatsEnterpriseWeeklyDefalut)
	//说明：每月第一个日子的凌晨1点执行一次
	c.AddFunc("0 0 1 1 * *", statsEnterpriseService.StatsEnterpriseMonthlyDefalut)
	c.AddFunc("0 0 1 1 1 *", statsEnterpriseService.StatsEnterpriseYearlyDefalut)

	//分销店铺数据统计 分销员的佣金、待结佣金和已结佣金数据
	statsShopDisService := services.StatsShopService{}
	c.AddFunc("0 0 1 * * *", statsShopDisService.StatsShopDistributorDailyDefault)
	c.AddFunc("0 0 1 * * 1", statsShopDisService.StatsShopDistributorWeeklyDefault)
	c.AddFunc("0 0 1 1 * *", statsShopDisService.StatsShopDistributorMonthlyDefault)
	c.AddFunc("0 0 1 1 1 *", statsShopDisService.StatsShopDistributorYearlyDefault)

	//保险订单统计数据
	insureOrderStatService := services.InsureOrderStatService{}
	c.AddFunc("0 0 4 * * *", insureOrderStatService.InsureOrderDailyStatCronJob)
	c.AddFunc("0 0 3 * * 1", insureOrderStatService.InsureOrderWeekStatCronJob)
	c.AddFunc("0 0 2 1 * *", insureOrderStatService.InsureOrderMonthStatCronJob)
	c.AddFunc("0 0 1 1 1 *", insureOrderStatService.InsureOrderYearStatCronJob)

	//商品订单统计
	jobStatsService := services.JobStatsService{}
	//商品订单分销员统计
	jobStatsDisService := services.JobStatsDisService{}
	//业务员分销业绩统计
	jobStatsSalespersonService := services.JobStatsSalespersonService{}
	c.AddFunc("0 0 1 * * *", jobStatsService.StatsOrderDaliyData)
	c.AddFunc("0 0 1 * * 1", jobStatsService.StatsOrderWeeklyData)
	c.AddFunc("0 0 1 1 * *", jobStatsService.StatsOrderMonthlyData)
	c.AddFunc("0 0 1 1 1 *", jobStatsService.StatsOrderYearlyData)

	c.AddFunc("0 0 1 * * *", jobStatsDisService.StatsDisOrderDaliyData)
	c.AddFunc("0 0 1 * * 1", jobStatsDisService.StatsDisOrderWeeklyData)
	c.AddFunc("0 0 1 1 * *", jobStatsDisService.StatsDisOrderMonthlyData)
	c.AddFunc("0 0 1 1 1 *", jobStatsDisService.StatsDisOrderYearlyData)

	c.AddFunc("0 0 1 * * *", jobStatsSalespersonService.StatsSalespersonOrderDaliyData)
	c.AddFunc("0 0 1 * * 1", jobStatsSalespersonService.StatsSalespersonOrderWeeklyData)
	c.AddFunc("0 0 1 1 * *", jobStatsSalespersonService.StatsSalespersonOrderMontylyData)
	c.AddFunc("0 0 1 1 1 *", jobStatsSalespersonService.StatsSalespersonOrderYearlyData)

	c.Start()
}
