package export

import (
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"
	"testing"

	"github.com/xuri/excelize/v2"
)

func TestShopTask_DataExport(t *testing.T) {
	type fields struct {
		F            *excelize.File
		SheetName    string
		ExportParams *vo.DisShopListReq
		writer       *excelize.StreamWriter
		BaseService  common.BaseService
	}
	type args struct {
		taskParams string
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		wantSuccessNum int
		wantFailNum    int
		wantErr        bool
	}{
		// TODO: Add test cases.
		{
			name: "TestShopTask_DataExport",
			args: args{
				taskParams: "{\"status\":1,\"org_id\":3,\"shop_id\":0,\"is_setted_shop\":0,\"where\":\"\",\"where_type\":0,\"content\":\"\",\"enterprise_name\":\"\",\"enterprise_id\":\"\",\"page_index\":0,\"page_size\":0}",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := ShopTask{
				F:            tt.fields.F,
				SheetName:    tt.fields.SheetName,
				ExportParams: tt.fields.ExportParams,
				writer:       tt.fields.writer,
				BaseService:  tt.fields.BaseService,
			}
			gotSuccessNum, gotFailNum, err := e.DataExport(tt.args.taskParams)
			if (err != nil) != tt.wantErr {
				t.Errorf("DataExport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotSuccessNum != tt.wantSuccessNum {
				t.Errorf("DataExport() gotSuccessNum = %v, want %v", gotSuccessNum, tt.wantSuccessNum)
			}
			if gotFailNum != tt.wantFailNum {
				t.Errorf("DataExport() gotFailNum = %v, want %v", gotFailNum, tt.wantFailNum)
			}
		})
	}
}
