package omnibus_po

import "time"

type TProductBrand struct {
	Id          int64     `json:"id" xorm:"pk not null comment('ID') BIGINT 'id'"`
	ChainId     int64     `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	BrandCode   string    `json:"brand_code" xorm:"not null default '' comment('品牌编码') VARCHAR(64) 'brand_code'"`
	BrandName   string    `json:"brand_name" xorm:"default '' comment('品牌名称') VARCHAR(64) 'brand_name'"`
	BrandLogo   string    `json:"brand_logo" xorm:"not null default '' comment('品牌logo') VARCHAR(255) 'brand_logo'"`
	BrandRemark string    `json:"brand_remark" xorm:"not null default '' comment('品牌介绍') VARCHAR(255) 'brand_remark'"`
	UsedNum     int       `json:"used_num" xorm:"default 0 comment('使用商品数') INT 'used_num'"`
	IsDeleted   bool      `json:"is_deleted" xorm:"not null default 0 comment('删除标识:0未删除,1已删除') BIT(1) 'is_deleted'"`
	CreatedBy   int64     `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	CreatedTime time.Time `json:"created_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'created_time'"`
	UpdatedBy   int64     `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	UpdatedTime time.Time `json:"updated_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'updated_time'"`
}
