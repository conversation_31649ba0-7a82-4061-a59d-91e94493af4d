package order_vo

type ChannelWarehouse struct {
	ShopId        string `json:"shop_id"`        //门店id
	ShopName      string `json:"shop_name"`      //门店名称
	WarehouseId   string `json:"warehouse_id"`   //仓库id
	WarehouseName string `json:"warehouse_name"` //仓库名称
	WarehouseCode string `json:"warehouse_code"` //仓库编号、门店仓财务编码
	Category      int    `json:"category"`       //仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓，5-前置仓虚拟仓)
	ChannelId     int    `json:"channel_id"`     //1、阿闻渠道-外卖，2、美团，3、饿了么，4、京东到家，9、互联网医院，10、阿闻渠道-自提
	//SellDrugs     int    `json:"sell_drugs"`     //是否有资质售药 v6.27.1
}
