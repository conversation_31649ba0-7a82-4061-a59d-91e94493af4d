package product_po

import "time"

type XLogisticsCode struct {
	Iid        int       `xorm:"pk autoincr" json:"iid"`
	Swlm       string    `xorm:"varchar(50) 'swlm' notnull comment('商品物流码')" json:"swlm"`
	Sspmc      string    `xorm:"varchar(200) 'sspmc' notnull" json:"sspmc"`
	Spici      string    `xorm:"varchar(50) 'spici' null" json:"spici"`
	Sdxtm      string    `xorm:"varchar(50) 'sdxtm' null comment('箱码')" json:"sdxtm"`
	Dregtime   time.Time `xorm:"default(CURRENT_TIMESTAMP) notnull comment('注册时间')" json:"dregtime"`
	Drktime    time.Time `xorm:"null comment('入库时间')" json:"drktime"`
	Dcktime    time.Time `xorm:"null comment('出库时间')" json:"dcktime"`
	Izt        int       `xorm:"default(0) notnull" json:"izt"`
	Saddman    string    `xorm:"varchar(50) 'saddman' null" json:"saddman"`
	Sfwm       string    `xorm:"varchar(50) 'sfwm' null" json:"sfwm"`
	Sddbh      string    `xorm:"varchar(50) 'sddbh' null" json:"sddbh"`
	Dbdatetime time.Time `xorm:"null comment('绑定时间')" json:"dbdatetime"`
	Sn         string    `xorm:"varchar(32) 'sn' notnull default('') comment('序列号')" json:"sn"`
}
