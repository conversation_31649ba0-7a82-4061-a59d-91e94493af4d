package utils

import (
	"bytes"
	"eShop/infra/config"
	glog "eShop/infra/log"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"
)

// 授权中心客户端
var authCenterClient *AuthCenterClient

func InitClient() {
	authCenterClient = NewAuthCenterClient()
}

func GetQrImage(path string, sid string, orgId int, typeUrl int, sourceType int) (string, error) {

	if authCenterClient == nil {
		InitClient()
	}
	glog.Info("打印参数111 url:", authCenterClient.url, " appId:", authCenterClient.appId, " secretCode:", authCenterClient.secretCode, " orgId:", orgId)
	// 请求授权中心获取accessToken
	token, err := authCenterClient.GetAccessToken(orgId, sourceType)
	if err != nil {
		glog.Errorf("获取微信accessToken异常,err:%+v", err)
		return "", err
	}

	// 请求微信生成小程序码
	EnvVersion := "trial"
	//env := os.Getenv("ASPNETCORE_ENVIRONMENT")
	//if env == "sit" || env == "sit1" || env == "sit2" || env == "staging" || env == "uat" || env == "dev" {
	//	EnvVersion = "trial"
	//}
	//加配置，配置来控制
	isOnline := config.Get("isOnline")
	//EnvVersion = "trial"
	if isOnline == "1" {
		EnvVersion = "release"
	}
	glog.Info("isOnline 获取到配置：", isOnline, EnvVersion)
	//EnvVersion = "trial"
	var wxacode = WxacodeRequest{
		Scene:      sid,
		Page:       path,
		CheckPath:  false,
		EnvVersion: EnvVersion,
	}
	codeUrl := fmt.Sprintf("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s", token)
	codeByte, _ := json.Marshal(wxacode)
	glog.Info(codeUrl, string(codeByte))

	byteBuffer, _ := HttpPost(codeUrl, codeByte, "")
	var wxacodeErrResponse WxacodeErrResponse
	codeErr := json.Unmarshal(byteBuffer, &wxacodeErrResponse)
	if codeErr == nil {
		glog.Errorf("请求生成微信小程序码异常,codeUrl:%s,resp:%s", codeUrl, byteBuffer)
		return "", nil
	}

	if typeUrl == 1 {
		encodeString := base64.StdEncoding.EncodeToString(byteBuffer)
		return encodeString, nil
	}

	//os.WriteFile("temp.jpg", byteBuffer, os.ModePerm)
	t := time.Now()
	datetime := t.Format("20060102")
	uf := &UploadFile{
		Name:   "/files/" + datetime + "/" + GetGuid32() + ".jpg",
		Reader: bytes.NewReader(byteBuffer),
	}
	url, err := uf.ToQiNiu()
	if err != nil {
		glog.Info("获取微信二维码失败:", err.Error())
		return "", err
	}

	return url, nil

}

type WxacodeRequest struct {
	Access_token string `json:"access_token,omitempty"` //	是	接口调用凭证
	Scene        string `json:"scene,omitempty"`        //	是	最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
	Page         string `json:"page,omitempty"`         //主页	否	必须是已经发布的小程序存在的页面（否则报错），例如 pages/index/index, 根路径前不要填加 /,不能携带参数（参数请放在scene字段里），如果不填写这个字段，默认跳主页面
	CheckPath    bool   `json:"check_path"`             //默认是true，检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面（否则报错）；为 false 时允许小程序未发布或者 page 不存在， 但page 有数量上限（60000个）请勿滥用。
	EnvVersion   string `json:"env_version"`            //要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
	Width        int32  `json:"width,omitempty"`        //430	否	二维码的宽度，单位 px，最小 280px，最大 1280px
	Auto_color   bool   `json:"auto_color,omitempty"`   //	false	否	自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false
	Line_color   string `json:"line_color,omitempty"`   //{"r":0,"g":0,"b":0}	否	auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"} 十进制表示
	Is_hyaline   bool   `json:"is_hyaline,omitempty"`   //false	否	是否需要透明底色，为 true 时，生成透明底色的小程序
}

type WxacodeLimitedRequest struct {
	Access_token string `json:"access_token,omitempty"` //	是	接口调用凭证
	Path         string `json:"path,omitempty"`         //主页	否	必须是已经发布的小程序存在的页面（否则报错），例如 pages/index/index, 根路径前不要填加 /,不能携带参数（参数请放在scene字段里），如果不填写这个字段，默认跳主页面
	Width        int32  `json:"width,omitempty"`        //430	否	二维码的宽度，单位 px，最小 280px，最大 1280px
}

type WxacodeErrResponse struct {
	Errcode int32  //	错误码
	Errmsg  string //	错误信息
}
