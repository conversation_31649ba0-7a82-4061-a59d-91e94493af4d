package services

import (
	"context"
	"eShop/infra/log"
	"eShop/services/common"
	omnibus_vo "eShop/view-model/omnibus-vo"
	"reflect"
	"testing"
	"time"
)

func TestCommissionService_CreateCommissionSetup(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		ctx context.Context
		req omnibus_vo.CreateCommissionSetupReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *omnibus_vo.CommissionSetupResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "创建提成设置",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				ctx: context.Background(),
				req: omnibus_vo.CreateCommissionSetupReq{
					SetupName: "测试提成设置",
					Details: []*omnibus_vo.CommissionDetailItem{
						{
							CommissionType: 1,                                     // 商品提成
							CalcType:       1,                                     // 按比例计算
							CommissionRate: 10,                                    // 10%提成比例
							ScopeType:      1,                                     // 全部商品
							Products:       []*omnibus_vo.CommissionProductItem{}, // 全部商品时无需指定具体商品
						},
						{
							CommissionType: 2, // 服务提成
							CalcType:       2, // 按固定金额计算
							CommissionRate: 5, // 5元固定提成
							ScopeType:      2, // 指定服务
							Products: []*omnibus_vo.CommissionProductItem{
								{
									SkuId:       1234567890,
									ProductName: "洗澡服务",
									ProductType: 2,
								},
								{
									SkuId:       1234567891,
									ProductName: "美容服务",
									ProductType: 2,
								},
							},
						},
						{
							CommissionType: 3, // 寄养提成
							CalcType:       1, // 按比例计算
							CommissionRate: 8, // 8%提成比例
							ScopeType:      1, // 全部寄养
							Products:       []*omnibus_vo.CommissionProductItem{},
						},
						{
							CommissionType: 4,  // 活体提成
							CalcType:       1,  // 按比例计算
							CommissionRate: 15, // 15%提成比例
							ScopeType:      2,  // 指定活体
							Products: []*omnibus_vo.CommissionProductItem{
								{
									SkuId:       1234567890,
									ProductName: "金毛幼犬",
									ProductType: 4,
								},
								{
									SkuId:       1234567891,
									ProductName: "布偶猫",
									ProductType: 4,
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CommissionService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.CreateCommissionSetup(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateCommissionSetup() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateCommissionSetup() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCommissionService_AssignOrderCommission(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req *omnibus_vo.AssignOrderCommissionReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "分配订单业绩",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				req: &omnibus_vo.AssignOrderCommissionReq{
					OrderItems: []omnibus_vo.OrderItemCommission{
						{
							OrderId:       1,
							OrderNo:       "25656565234567890",
							SkuId:         1,
							ProductName:   "TEST",
							ProductType:   1,
							SalesAmount:   100.00,
							ActualAmount:  90.00,
							OrderTime:     time.Now(),
							StoreId:       577549921650660693,
							Channel:       1,
							AssignedEmpId: 606307459296918539,
						},
					},
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &CommissionService{
				BaseService: tt.fields.BaseService,
			}
			got, err := s.AssignOrderCommission(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AssignOrderCommission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AssignOrderCommission() got = %v, want %v", got, tt.want)
			}
		})
	}
}
