package voucher

import (
	"context"
	po "eShop/domain/inventory-po/voucher"
	jwt "eShop/infra/jwtauth"
	iService "eShop/services/inventory-service/iobound"
	ivo "eShop/view-model/inventory-vo/iobound"
	vo "eShop/view-model/inventory-vo/voucher"
	"fmt"
	"strconv"
	"time"

	"xorm.io/xorm"
)

// SaveOutbound 保存采购出库单
func (s VoucherService) SaveOutbound(ctx context.Context, cmd vo.VoucherSaveCommand) (voucherId int, err error) {
	s.Begin()
	session := s.Engine.NewSession()
	session.Begin()
	defer func() {
		if r := recover(); r != nil {
			session.Rollback()
			err = fmt.Errorf("panic occurred: %v", r)
		}
		session.Close()
		s.Close()
	}()

	var returnVoucherId = cmd.Pid
	var outboundVoucherId int
	
	//将当前单据详情转换为map
	//map里存放的是当前提交的数据
	outboundItems := make(map[int]vo.ProductDetail)
	for _, detail := range cmd.Details {
		outboundItems[detail.SkuId] = detail
	}

	//先根据pid获取采购单详情，从采购单中的child_voucher_no来判断是不是已经有入库单了，
	//如果child_voucher_no为空，则说明没有入库单，则需要创建入库单
	//如果child_voucher_no不为空，则说明已经有入库单，则需要更新入库单
	returnVoucher, err := s.ReturnDetail(ctx, cmd.Pid)
	if err != nil {
		panic(err)
	}

	var voucher po.Voucher
	if returnVoucher.VoucherInfo.ChildVoucherNo != "" {
		//获取当前单据详情
		outboundVoucher, err := s.OutboundDetail(ctx, returnVoucher.VoucherInfo.ChildId)
		outboundVoucherId = returnVoucher.VoucherInfo.ChildId
		if err != nil {
			panic(err)
		}

		var qualifyQuantity = 0
		for _, detail := range outboundVoucher.ProductInfo {
			//如果当前单据详情中的实际数量+当前提交的数据中的实际数量小于当前单据详情中的期望数量，则说明未全部入库
			_, has := outboundItems[detail.SkuId]
			if !has {
				break
			}
			if outboundItems[detail.SkuId].Quantity+detail.ActualQuantity >= detail.ExpectQuantity {
				qualifyQuantity++
			}
		}
		if qualifyQuantity == len(outboundVoucher.ProductInfo) {
			voucher.Status = 4
		} else {
			voucher.Status = 3
		}

		var voucherDetail po.VoucherDetail
		outboundVoucherDetails, err := voucherDetail.ListByVoucherId(ctx, session, returnVoucher.VoucherInfo.ChildId)
		if err != nil {
			panic(err)
		}

		// 更新出库单据详情
		for _, detail := range outboundVoucherDetails {
			var newDetail = detail
			newDetail.ActualNum = detail.ActualNum + outboundItems[detail.SkuId].Quantity
			newDetail.ActualAmount = (detail.ActualNum + outboundItems[detail.SkuId].Quantity) * detail.Price
			_, err := session.ID(detail.Id).Cols("actual_num", "actual_amount").Update(&newDetail)
			if err != nil {
				panic(err)
			}
		}
	} else {
		newVoucherNo, err := s.GenerateVoucherNo(cmd.VoucherType)
		if err != nil {
			panic(err)
		}
		var isDone = true
		for _, detail := range returnVoucher.ProductInfo {
			//如果当前单据详情中的skuId在提交的数据中不存在，则说明未全部入库
			_, has := outboundItems[detail.SkuId]
			if !has {
				isDone = false
				break
			}

			//如果当前单据详情中的实际数量+当前提交的数据中的实际数量不等于当前单据详情中的期望数量，则说明未全部入库
			if outboundItems[detail.SkuId].Quantity != detail.ExpectQuantity {
				isDone = false
				break
			}
		}
		//如果isDone为true，则说明全部入库
		if !isDone {
			voucher.Status = 3
		} else {
			voucher.Status = 4
		}

		//主单据的数据从上级单据中获取
		voucher.ChainId = returnVoucher.VoucherInfo.ChainId
		voucher.StoreId = returnVoucher.VoucherInfo.StoreId
		voucher.WarehouseId = returnVoucher.VoucherInfo.WarehouseId
		voucher.SupplierId = returnVoucher.VoucherInfo.SupplierId
		voucher.VoucherNo = newVoucherNo
		voucher.Name = cmd.Name
		voucher.VoucherType = vo.VoucherTypeMap[cmd.VoucherType]
		voucher.ChangeNum = 0
		voucher.ChangeAmount = 0
		voucher.ProfitStatus = 0
		voucher.Remark = returnVoucher.VoucherInfo.Remark
		voucher.IsDeleted = 0
		voucher.SourceType = cmd.SourceType
		voucher.DeliveryTime = returnVoucher.VoucherInfo.DeliveryTime
		voucher.PurchaseTime = returnVoucher.VoucherInfo.PurchaseTime
		voucher.Operator = jwt.CtxGet[string](ctx, "UserName")
		voucher.VoucherTime = time.Now()
		voucher.CreatedTime = time.Now()
		voucher.UpdatedTime = time.Now()
		voucher.Pid = returnVoucher.VoucherInfo.Id

		voucherId, err = voucher.Insert(ctx, session)
		if err != nil {
			panic(err)
		}
		returnVoucher.VoucherInfo.ChildId=voucherId
		returnVoucher.VoucherInfo.ChildVoucherNo=newVoucherNo
		// 保存单据详情

		returnProductMap := make(map[int]vo.ProductInfo)
		for _, detail := range returnVoucher.ProductInfo {
			returnProductMap[detail.SkuId] = detail
		}

		var voucherDetails []po.VoucherDetail
		for _, detail := range cmd.Details {
			voucherDetail := po.VoucherDetail{
				ChainId:        returnVoucher.VoucherInfo.ChainId,
				StoreId:        returnVoucher.VoucherInfo.StoreId,
				WarehouseId:    returnVoucher.VoucherInfo.WarehouseId,
				VoucherId:      voucherId,
				ProductId:      detail.ProductId,
				SkuId:          detail.SkuId,
				Price:          returnProductMap[detail.SkuId].Price,
				Quantity:       returnProductMap[detail.SkuId].ExpectQuantity,
				Amount:         returnProductMap[detail.SkuId].Price * returnProductMap[detail.SkuId].ExpectQuantity,
				ActualNum:      detail.Quantity,                                        //采购单不写入实际数量
				ActualAmount:   returnProductMap[detail.SkuId].Price * detail.Quantity, //采购单不写入实际金额
				TotalNumBefore: 0,                                                      //需要确认这个两个字段是否有实际意义，如有需从库存中心拿实时库存
				TotalNumAfter:  0,
				ProfitStatus:   0,
				ChangeNum:      detail.Quantity,
				ChangeAmount:   detail.Quantity * returnProductMap[detail.SkuId].Price,
				AvgCostPrice:   0,
				Reason:         detail.Reason,
				Remark:         cmd.Remark,
				IsDeleted:      0,
				CreatedTime:    time.Now(),
				UpdatedTime:    time.Now(),
			}

			voucherDetails = append(voucherDetails, voucherDetail)
		}

		session.Insert(voucherDetails)
	}

	//更新上级单据的实际数量和实际金额
	var voucherDetail po.VoucherDetail
	parentVoucher,err:=voucherDetail.ListByVoucherId(ctx,session,returnVoucher.VoucherInfo.Id)
	if err != nil {
		panic(err)
	}

	for _,detail:=range parentVoucher{
		var newDetail=detail
		newDetail.ActualNum = detail.ActualNum + outboundItems[detail.SkuId].Quantity
		newDetail.ActualAmount = (detail.ActualNum + outboundItems[detail.SkuId].Quantity) * detail.Price
		_, err := session.ID(detail.Id).Cols("actual_num", "actual_amount").Update(&newDetail)
		if err != nil {
			panic(err)
		}
	}

	//更新上级单据的状态
	_, err = session.ID(returnVoucherId).Cols("status").Update(&voucher)
	if err != nil {
		panic(err)
	}
	//更新当前单据的状态
	_, err = session.ID(outboundVoucherId).Cols("status").Update(&voucher)
	if err != nil {
		panic(err)
	}

	//更新出入库单据
	err = s.ChangeOutBound(ctx,session,cmd,returnVoucher)
	if err != nil {
		panic(err)
	}

	session.Commit()
	err = nil

	return voucherId, err
}

// OutboundDetail 获取出库单据详情
func (s VoucherService) OutboundDetail(ctx context.Context, id int) (vo.VoucherRequestResponse, error) {
	s.Begin()
	session := s.Session
	defer func() {
		if r := recover(); r != nil {
			return
		}
		session.Close()
		s.Close()
	}()
	var response vo.VoucherRequestResponse

	_, err := session.SQL(`
	select
    iv.id,
    iv.voucher_no as current_voucher_no,
    piv.voucher_no as parent_voucher_no,
    civ.voucher_no as child_voucher_no,
    iv.chain_id,
    iv.store_id,
    s.name as store_name,
    sbs.mobile as store_contact_method,
    iv.warehouse_id,
    w.name as warehouse_name,
	w.address as warehouse_address,
    iv.supplier_id,
    ins.name as supplier_name,
    ins.contact_person as supplier_contact_person,
    ins.contact_method as supplier_contact_method,
	ins.address as supplier_address,
    iv.name,
    iv.voucher_type,
    iv.status,
    iv.source_type,
    iv.profit_status,
    iv.change_num,
    iv.change_amount,
    iv.remark,
	iv.logistics_no,
	iv.logistics_company,
    iv.is_deleted,
    iv.purchase_time,
    iv.delivery_time,
    iv.return_time,
    iv.voucher_time,
    iv.operator,
    iv.created_time,
    iv.updated_time
from
    eshop.inventory_voucher  iv
        left join eshop.inventory_suppliers ins on iv.supplier_id =ins.id
        left join datacenter.store s on s.id=iv.store_id
        left join datacenter.store_business_setup sbs on sbs.finance_code=s.finance_code
        left join dc_dispatch.warehouse w  on iv.warehouse_id=w.id
        left join eshop.inventory_voucher piv on piv.pid=iv.id
        left join eshop.inventory_voucher civ on iv.pid=civ.id
where iv.id=?
	`, id).Get(&response.VoucherInfo)

	err = session.SQL(`
select
    iv.id,
    vd.sku_id,
    vd.product_id,
    pp.name as product_name,
    ps.bar_code,
	il.code as location_code,
    ps.product_specs,
    ps.store_unit,
    ps.weight_for_unit,
    ps.tax_rate,
    vd.price,
	pp.pic as product_img,
	vd.reason,
    vd.quantity as  expect_quantity,
    vd.amount as  expect_amount,
    vd.actual_num as  actual_quantity,
    vd.actual_amount as  actual_amount,
    ei.total_num as inventory_total,
    ei.freeze_num as inventory_freeze,
    ei.available_num as inventory_available
from
    eshop.inventory_voucher  iv
        left join eshop.inventory_voucher_detail vd on iv.id=vd.voucher_id
        LEFT JOIN eshop.pro_sku ps ON vd.sku_id=ps.id
        LEFT JOIN eshop.pro_product pp ON vd.product_id=pp.id
		left join eshop.inventory_location il on iv.warehouse_id=il.warehouse_id and il.product_id=vd.product_id and il.sku_id=vd.sku_id
		left join eshop.inventory ei on vd.sku_id=ei.sku_id and vd.warehouse_id=ei.warehouse_id
where iv.id=?`, id).Find(&response.ProductInfo)

	if err != nil {
		panic(err)
	}
	return response, nil
}


// ChangeIOBound 更新出入库单据
func (s VoucherService) ChangeOutBound(ctx context.Context,session *xorm.Session,cmd vo.VoucherSaveCommand,requestVoucher vo.VoucherRequestResponse) error {
	createParams := ivo.IoBoundCreateCommand{}
	createParams.ChainId = requestVoucher.VoucherInfo.ChainId
	createParams.StoreId = requestVoucher.VoucherInfo.StoreId
	createParams.WarehouseId = requestVoucher.VoucherInfo.WarehouseId
	createParams.ItemType=2
	createParams.ItemRefId=requestVoucher.VoucherInfo.ChildId
	createParams.ItemRefNo=requestVoucher.VoucherInfo.ChildVoucherNo
	createParams.ItemRefType=2
	createParams.Operator=jwt.CtxGet[string](ctx,"UserName")
	createParams.Remark=requestVoucher.VoucherInfo.Remark
	createParams.OccurrenceTime=time.Now()

	productMap := make(map[int]vo.ProductInfo)
	for _,detail:=range requestVoucher.ProductInfo{
		productMap[detail.SkuId]=detail
	}

	createParams.Details=make([]ivo.IoBoundDetailCreateCommand,len(cmd.Details))
	for i,detail:=range cmd.Details{
		createDetail:=ivo.IoBoundDetailCreateCommand{
			BoundType:2,
			ItemType:2,
			ItemDetailRefId:strconv.Itoa(requestVoucher.VoucherInfo.ChildId) ,
			SkuId:detail.SkuId,
			ProductName:productMap[detail.SkuId].ProductName,
			IoPrice: productMap[detail.SkuId].Price,
			IoCount:detail.Quantity,
			RealIoAmount: productMap[detail.SkuId].Price*detail.Quantity,
		}
		createParams.Details[i]=createDetail
	}

	_, err := iService.IoBoundService{}.Create(ctx,session,createParams)
	if err != nil {
		return err
	}

	return nil
}
