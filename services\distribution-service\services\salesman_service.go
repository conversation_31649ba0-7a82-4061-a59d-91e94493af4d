package services

import (
	po "eShop/domain/distribution-po"
	external_po "eShop/domain/external-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	distribution_vo "eShop/view-model/distribution-vo"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/spf13/cast"
)

type DisSalesmanService struct {
	common.BaseService
}

// 获取业务员列表接口 测试过
func (h *DisSalesmanService) Get(in distribution_vo.DisSalesmanStopReq) (distribution_vo.DisSalesmanView, error) {

	h.Begin()
	defer h.Close()
	var out distribution_vo.DisSalesmanView

	nowMon := time.Now().Format("2006-01")
	LastMon := time.Now().AddDate(0, -1, 0).Format("2006-01")

	// 将子上下文传入Session
	session := h.Session
	session.Table("scrm_salesperson").Alias("a").
		Join("left", "dis_salesperson b", "b.salesperson_id = a.id ").
		Join("left", "dis_salesperson_total c", "b.org_id=c.org_id and c.salesperson_id = a.id and c.`year_month`='"+nowMon+"'").
		Join("left", "dis_salesperson_total d", "b.org_id=d.org_id and d.salesperson_id = a.id and d.`year_month`='"+LastMon+"'").
		Select("a.*,b.*,c.total_order_pay_num as now_order_num,c.total_pay_sales as now_sales ,d.total_order_pay_num as last_order_num,d.total_pay_sales as last_sales")
	if len(in.Id) > 0 {
		session.And("a.id = ?", cast.ToInt64(in.Id))
	} else if in.Mobile != "" {
		session.And("a.phone=?", in.Mobile)
	}
	session.Where("status =1")
	session.OrderBy("a.id asc")
	_, err := session.Get(&out)

	// 根据业务员id查询对应的拓客分销员
	var tuokeDisNum int
	_, err = session.Select("COUNT(DISTINCT(id))").Table("dis_distributor").Where("tuoke_salesperson_id=?", out.Id).Get(&tuokeDisNum)
	if err == nil && tuokeDisNum > 0 {
		out.TuokeDisNum = tuokeDisNum
	}

	if err != nil {
		return out, err
	}
	return out, nil
}

// 获取业务员列表接口 测试过
func (h *DisSalesmanService) GetList(in distribution_vo.DisSalesmanListReq) ([]distribution_vo.DisSalesmanView, int, error) {

	h.Begin()
	defer h.Close()
	var out []distribution_vo.DisSalesmanView
	// 将子上下文传入Session
	session := h.Session

	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	if in.PageSize < 1 {
		in.PageSize = 10
	}

	session.Table("scrm_salesperson").Alias("a").
		Join("left", "dis_salesperson b", "b.salesperson_id = a.id ").
		//Where("b.org_id=?", in.OrgId)
		Where("1=1")

	if in.Status != -1 {
		session.And("a.status=?", in.Status)
	}
	if in.OrgId != 0 {
		session.And("b.org_id=?", in.OrgId)
	}
	if len(in.OrgName) > 0 {
		session.And("a.org_name LIKE ?", "%"+in.OrgName+"%")
	}
	//加密的手机号
	if len(in.Where) > 0 {
		//查询条件的类型（name=业务员名称，mobile=手机，employee_no=员工编号，Id=业务员ID）
		switch in.WhereType {
		case "":
		case "name":
			session.And("a."+in.WhereType+" like ?", "%"+in.Where+"%")
		case "mobile":
			session.And("a.phone=?", in.Where)
		case "id":
			session.And("a."+in.WhereType+"=?", in.Where)
		case "employee_no":
			session.And("a.code=?", in.Where)
		case "all":
			session.And("a.name like ? or phone=? or code=?", "%"+in.Where+"%", in.Where, in.Where)
		}
	}

	session.OrderBy("a.id asc")

	count, err := session.Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
		Select("a.*,b.*,(case when b.org_id = 3 then '润合云店' when b.org_id = 4 then '百林康源' else '' end) as shop_name").
		FindAndCount(&out)

	if err != nil {

		return out, 0, err
	}
	return out, cast.ToInt(count), nil
}

// 添加业务员  测试过
func (h *DisSalesmanService) SaleManAdd(in distribution_vo.DisSalesman) error {
	//h.Begin()
	//defer h.Close()
	//
	//// 将子上下文传入Session
	//session := h.Engine.NewSession()
	//
	////先判断手机号是否有重复的
	//var item po.DisSalesman
	////加星星的手机号
	//StarMobile := utils.AddStar(in.Mobile)
	////加密的手机号
	//EncryptMobile := utils.MobileEncrypt(in.Mobile)
	//Ishave, err := session.SQL("select 1 from dis_salesman where encrypt_mobile=? or employee_no=?", EncryptMobile, in.EmployeeNo).Get(&item)
	//if err != nil {
	//	return err
	//}
	//if Ishave {
	//
	//	return errors.New("手机号或员工编号重复")
	//}
	//
	//DisSalesman := po.DisSalesman{}
	//copier.Copy(&DisSalesman, in)
	//DisSalesman.Status = 2
	//DisSalesman.Mobile = StarMobile
	//DisSalesman.EncryptMobile = EncryptMobile
	//
	//session.Begin()
	//_, err = session.Insert(&DisSalesman)
	////插入成功后生成二维码
	//url, err := utils.GetQrImage("app/distribution/register", "id="+cast.ToString(DisSalesman.Id), 0)
	//if err != nil {
	//	session.Rollback()
	//	log.Error("创建业务员生成小程序码失败", err.Error())
	//	return errors.New("生成二维码失败：" + cast.ToString(DisSalesman.Id) + " " + err.Error())
	//}
	//DisSalesman.BarCode = url
	//_, err = session.Where("id=?", DisSalesman.Id).Cols("bar_code").Update(&DisSalesman)
	////插入成功后生成二维码
	//if err != nil {
	//	session.Rollback()
	//	log.Error("修改业务员二维码失败", err.Error())
	//	return errors.New("修改业务员二维码失败:" + url + "  " + err.Error())
	//}
	//
	//session.Commit()
	return nil
}

// 编辑业务员 测试过
func (h *DisSalesmanService) SaleManEdit(in distribution_vo.DisSalesman) error {
	//
	//h.Begin()
	//defer h.Close()
	//
	//// 将子上下文传入Session
	//session := h.Session
	//
	////先判断手机号是否有重复的
	//
	//var item po.DisSalesman
	//StarMobile := ""
	//EncryptMobile := ""
	//updateStr := "name,employee_no,region_id,shop_id"
	//Ishave, err := session.SQL("select 1 from dis_salesman where employee_no=? and id!=?", in.EmployeeNo, in.Id).Get(&item)
	//if err != nil {
	//	return err
	//}
	//if Ishave {
	//	return errors.New("当前员工编号已存在")
	//}
	//if !strings.ContainsAny(in.Mobile, "****") {
	//
	//	//加星星的手机号
	//	StarMobile = utils.AddStar(in.Mobile)
	//	//加密的手机号
	//	EncryptMobile = utils.MobileEncrypt(in.Mobile)
	//	Ishave, err = session.SQL("select 1 from dis_salesman where encrypt_mobile=? and id!=?", EncryptMobile, in.Id).Get(&item)
	//	if err != nil {
	//		return err
	//	}
	//	if Ishave {
	//		return errors.New("当前手机号已存在")
	//	}
	//	//如果修改了手机号，就要去修改
	//	updateStr += ",mobile,encrypt_mobile"
	//}
	//DisSalesman := po.DisSalesman{}
	//copier.Copy(&DisSalesman, in)
	//DisSalesman.Mobile = StarMobile
	//DisSalesman.EncryptMobile = EncryptMobile
	//
	//_, err = session.Where("id=?", DisSalesman.Id).Cols(updateStr).Update(&DisSalesman)
	//
	//if err != nil {
	//	return err
	//}
	return nil
}

// 停用启用  测试过
func (h *DisSalesmanService) SaleManStopOrStar(in distribution_vo.DisSalesmanStopReq) error {

	//h.Begin()
	//defer h.Close()
	//
	//// 将子上下文传入Session
	//session := h.Session
	//
	//DisSalesman := po.DisSalesman{}
	//copier.Copy(&DisSalesman, in)
	//
	//_, err := session.Where("id=?", DisSalesman.Id).Cols("status").Update(&DisSalesman)
	//if err != nil {
	//	return err
	//}

	return nil
}

// 修改业务员  推广分销员数量+1  ,有分销员入住成功就需要调用
func (h *DisSalesmanService) SaleManAddNum(in distribution_vo.DisSalesman) error {

	_, err := h.Session.Exec("update dis_salesperson set tuoke_dis_num=tuoke_dis_num+1 where salesperson_id=?", cast.ToInt64(in.Id))
	if err != nil {
		return err
	}
	return nil
}

// 跑业务员补充数据，二维码，服务企业数，服务分销员数量
func (h *DisSalesmanService) SetSaleManData() {

	h.Begin()
	defer h.Close()

	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.SetSaleManDataLock, time.Minute*60)
	if !setNxReslt {
		log.Errorf("%s-设置redis(%s)锁失败", "SetSaleManDataTask", cachekey.SetSaleManDataLock)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.SetSaleManDataLock)

	log.Error("SetSaleManDataTask 获取到锁处理数据")
	//先查询出所以已经插入了扩展表的数据进行更新
	var upList []po.ScrmSalespersonExt
	err := h.Engine.Table("scrm_salesperson").Alias("a").Join("inner", "dis_salesperson b", "a.id = b.salesperson_id").
		Select("a.*,b.bar_code").Find(&upList)
	if err != nil {
		log.Error("SetSaleManDataTask 查询业务员数据出错:", err.Error())
		return
	}

	for _, x := range upList {
		//统计服务企业数
		var service_ent_num = 0
		_, err = h.Engine.SQL("select count(1) from scrm_enterprise_salesperson_bind a inner join shop b on a.enterprise_id=b.enterprise_id  where a.salesperson_id=?", x.Id).Get(&service_ent_num)
		if err != nil {
			log.Error("SetSaleManDataTask 业务员ID："+cast.ToString(x.Id)+" 查询统计服务企业数出错:", err.Error())
			continue
		}

		var service_dis_num = 0
		_, err = h.Engine.SQL(`SELECT COUNT(1) FROM dis_distributor a 
								INNER JOIN shop b  ON a.shop_id=b.id
								INNER JOIN scrm_enterprise_salesperson_bind c ON b.enterprise_id=c.enterprise_id 
								WHERE c.salesperson_id=?`, x.Id).Get(&service_dis_num)
		if err != nil {
			log.Error("SetSaleManDataTask 业务员ID："+cast.ToString(x.Id)+" 查询统计服务企业数出错:", err.Error())
			continue
		}
		//修改统计数据
		_, err = h.Engine.Exec("update dis_salesperson set service_ent_num=?,service_dis_num=? where salesperson_id=?", service_ent_num, service_dis_num, x.Id)
		if err != nil {
			log.Error("SetSaleManDataTask 业务员ID："+cast.ToString(x.Id)+" 修改数据出错:", err.Error())
		}

		if x.BarCode == "" {
			//生成二维码
			url, err := utils.GetQrImage("app/distribution/register", "id="+cast.ToString(x.Id), 3, 0, 0)
			if err != nil {
				log.Error("SetSaleManDataTask 业务员生成小程序码出错:", x.Id, err.Error())
				continue
			}
			if url == "" {
				log.Error("SetSaleManDataTask 业务员生成小程序码上传出错:", x.Id)
				continue
			}
			_, err = h.Engine.Exec("update dis_salesperson set bar_code=? where salesperson_id=?", url, x.Id)
			if err != nil {
				log.Error("SetSaleManDataTask 业务员ID："+cast.ToString(x.Id)+" 修改二维码数据出错:", err.Error())
			}
		}
	}

}

// 添加业务员补充数据，二维码
func (h *DisSalesmanService) SetSaleManBarCode() {

	h.Begin()
	defer h.Close()

	//第一步： redis加锁，防止并发
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	setNxReslt := mCache.TryLock(string(cache_source.EShop), cachekey.SetSaleManBarCodeLock, time.Minute*60)
	if !setNxReslt {
		log.Errorf("%s-设置redis(%s)锁失败", "SetSaleManBarCodeTask", cachekey.SetSaleManBarCodeLock)
		return
	}
	defer mCache.Delete(string(cache_source.EShop), cachekey.SetSaleManBarCodeLock)

	log.Info("获取到锁", "SetSaleManBarCodeTask", cachekey.SetSaleManBarCodeLock)
	//查询所有不存在的数据，进行插入
	var addList []po.ScrmSalesperson
	err := h.Engine.Table("scrm_salesperson").Alias("a").Join("left", "dis_salesperson b", "a.id = b.salesperson_id").
		Where("b.salesperson_id is null").
		Select("a.*").Find(&addList)
	if err != nil {
		log.Error("SetSaleManBarCodeTask 查询业务员数据出错:", err.Error())
		return
	}
	for _, x := range addList {

		model := po.DisSalesperson{
			OrgId:         3,
			SalespersonId: x.Id,
		}
		//生成二维码
		url, err := utils.GetQrImage("app/distribution/register", "id="+cast.ToString(x.Id), 3, 0, 0)
		if err != nil {
			log.Error("SetSaleManBarCodeTask 业务员生成小程序码出错:", x.Id, err.Error())
			continue
		}
		if url == "" {
			log.Error("SetSaleManBarCodeTask 业务员生成小程序码上传出错:", x.Id)
			continue
		}
		model.BarCode = url
		_, err = h.Engine.Insert(model)
		if err != nil {
			log.Error("SetSaleManBarCodeTask 插入业务员补充数据出错:", x.Id, err.Error())
		}

	}
}

func (h *DisSalesmanService) SyncTuokeDisNum(ids ...string) error {
	if len(ids) == 0 {
		return nil
	}

	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	session.Begin()

	for _, id := range ids {
		_, err := session.Exec("UPDATE dis_salesperson SET tuoke_dis_num=("+
			"SELECT COUNT(DISTINCT(id)) FROM dis_distributor WHERE tuoke_salesperson_id=?"+
			") WHERE salesperson_id=?;", id, id)

		if err != nil {
			session.Rollback()
			return err
		}
	}

	session.Commit()
	return nil
}

// 每天同步shr，设置分销员是体系内还是体系外
func (h *DisSalesmanService) SetdistributorInSystem() {
	engine := h.DbDmMdm()
	defer engine.Close()

	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()

	var list []*distribution_vo.SyncDistributor
	if err := session.Table("eshop.dis_distributor").Alias("a").Select("a.id,a.dis_role,a.org_id,a.encrypt_mobile,a.veterinary_code,a.in_system,c.social_credit_code").
		Join("left", "eshop.shop b", "a.shop_id = b.id").
		Join("left", "eshop.dis_enterprise c", "b.enterprise_id = c.scrm_enterprise_id and a.org_id = c.org_id").
		Where("a.org_id = 4 and a.status = 1").Find(&list); err != nil {
		log.Error("SetdistributorInSystem 查询分销员数据失败:", err.Error())
		return
	} else {
		for _, item := range list {
			var inSystem int8 = 0
			// 如果是医生身份 ，则判断兽医资格证编号是否在体系内。 如果是店员或者老板 ，则判断企业社会信用代码是否在体系内。
			if item.DisRole == disdistributor.DisRoleDoctor {
				mobile := utils.MobileDecrypt(item.EncryptMobile)

				where := fmt.Sprintf("status = 2 and (mobile = '%s'", mobile)
				if item.VeterinaryCode != "" {
					where += fmt.Sprintf(" or vet_id_number = '%s'", item.VeterinaryCode)
				}
				where += ")"
				if isExist, err := engine.Table("dm_mdm.shr_t_staff_info").Where(where).Exist(); err != nil {
					log.Error("SetdistributorInSystem 查询vet_code失败:", err.Error())
					return
				} else if isExist {
					inSystem = 1
				}
			} else {
				// 如果分销员有所属企业， 则判断企业社会信用代码是否在体系内
				if item.SocialCreditCode != "" {
					// 判断 社会信用代码
					inSystem, err = new(external_po.VCoBusiness).GetBySocialReditCode(engine, item.SocialCreditCode)
					if err != nil {
						log.Error("SetdistributorInSystem 查询公司工商信息失败，err=", err.Error())
						return
					}
				}

			}
			if inSystem != item.InSystem {
				_, err := session.Exec("update eshop.dis_distributor set in_system=? where id=?", inSystem, item.Id)
				if err != nil {
					log.Error("SetdistributorInSystem 更新分销员数据失败:", err.Error())
				}
			}

		}
	}

	return
}
