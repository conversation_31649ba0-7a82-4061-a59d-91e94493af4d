package log

import (
	"context"
	"eShop/infra/config"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/robfig/cron/v3"
)

var (
	buffer        = make(chan <PERSON>yslog, 65535)
	lock          sync.RWMutex
	esIndexPrefix = "syslogs"

	esUrl   = config.Get("log.elasticsearch")
	esUname = config.Get("log.elasticsearch.LoginName")
	esPwd   = config.Get("log.elasticsearch.Password")

	BATCH_INSERT_SIZE = 100

	mobileHidden *regexp.Regexp
)

func Init() {
	mobileHidden, _ = regexp.Compile(`\d{11,}`)

	go func() {
		task := cron.New()
		if _, err := task.AddFunc("@every 5s", func() {
			lock.Lock()
			defer lock.Unlock()

			flush()

		}); err != nil {
			log.Println(err)
		} else {
			task.Run()
		}
	}()
}

func NewClient() *elastic.Client {
	maxRetries := 3

	for i := 0; i < maxRetries; i++ {
		client, err := elastic.NewClient(
			elastic.SetSniff(false),
			elastic.SetURL(esUrl),
			elastic.SetBasicAuth(esUname, esPwd),
		)
		if err == nil {
			return client
		}
		log.Printf("尝试连接ES失败,重试第%d次: %v", i+1, err)
		time.Sleep(time.Second * time.Duration(i+1)) // 递增重试间隔
	}

	log.Printf("连接ES失败,已重试%d次", maxRetries)
	return nil
}

func flush() {

	size := BATCH_INSERT_SIZE
	if size > len(buffer) {
		size = len(buffer)
	}

	if len(buffer) > 0 {
		client := NewClient()
		if client == nil {
			log.Printf("ES client creation failed, buffered logs size: %d\n", len(buffer))
			return
		}
		defer client.Stop()

		var logBuilder strings.Builder
		var bulks []elastic.BulkableRequest
		bulkRequest := client.Bulk()

		indexName := fmt.Sprintf("%s-%s-%s", esIndexPrefix, config.LocalSetting.Grpc.Appid, time.Now().Format("20060102"))

		for i := 0; i < size; i++ {
			obj := <-buffer
			bulks = append(bulks, elastic.NewBulkIndexRequest().Index(indexName).Doc(obj))

			objStr, _ := json.Marshal(obj)
			logBuilder.WriteString(string(objStr) + "\r\n")
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if len(bulks) > 0 {
			if _, err := bulkRequest.Add(bulks...).Do(ctx); err != nil {
				log.Printf("Failed to send logs to ES: %v\n", err)
			}
		}

		log.Printf("Processed logs: %d, Buffer remaining: %d\n", size, len(buffer))
		log.Println(logBuilder.String())
	}
}

func setLog(ctx context.Context, logText, level string) {
	_, fileName, lineNum, _ := runtime.Caller(3)

	var requestId = ""

	var log Syslog
	log.Appid = config.LocalSetting.Grpc.Appid
	log.Createdatetime = time.Now()
	log.EsIndexPrefix = esIndexPrefix
	log.Level = level
	log.Message = replaceMobile(logText)
	log.Logger = fmt.Sprintf("%s  line:%d", fileName, lineNum)
	log.RequestId = requestId
	log.Ip = config.LocalSetting.LocalIP

	buffer <- log
}

func info(ctx context.Context, logText string) {
	setLog(ctx, logText, "INFO")
}

func warn(ctx context.Context, logText string) {
	setLog(ctx, logText, "WARN")
}

func error(ctx context.Context, logText string) {
	setLog(ctx, logText, "ERROR")
}

func fatal(ctx context.Context, logText string) {
	setLog(ctx, logText, "FATAL")
}

func replaceMobile(message string) string {
	mobiles := mobileHidden.FindAllString(message, -1)
	for _, v := range mobiles {
		if len(v) == 11 && strings.HasPrefix(v, "1") {
			message = strings.ReplaceAll(message, v, v[:3]+"****"+v[7:])
		}
	}
	return message
}
