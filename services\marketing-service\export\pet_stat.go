package export

import (
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/marketing-service/services"
	vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

// PetStatTask 数据监测统计导出任务
type PetStatTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.PetStatListReq
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 导出数据
func (e PetStatTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.PetStatListReq)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}
	failNum = 0
	e.ExportParams.PageIndex = 1
	e.ExportParams.PageSize = 10000
	e.ExportParams.IsExport = false // 避免重复触发导出

	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	e.SetSheetName()
	client := new(service.PetActivityService)

	k := 0
	for {
		ret, total, err := client.ListPetStats(e.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}
		e.ExportParams.PageIndex += 1
		for i := 0; i < len(ret); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)

			// 根据表格中的字段顺序设置行数据
			_ = e.writer.SetRow(axis, []interface{}{
				ret[i].DaliyDate, // 日期
				ret[i].Metric1,   // 阿闻首页弹窗点击uv
				ret[i].Metric2,   // 阿闻首页banner点击uv
				ret[i].Metric3,   // 阿闻首页浮标点击uv
				ret[i].Metric4,   // 小闻首页广告点击uv
				ret[i].Metric5,   // 贵族首页弹窗点击uv
				ret[i].Metric6,   // 贵族首页banner点击uv
				ret[i].Metric7,   // 贵族活动主页访问的pv
				ret[i].Metric8,   // 贵族活动主页访问uv
				ret[i].Metric9,   // 作品助力页面访问pv
				ret[i].Metric10,  // 作品助力页面访问uv
				ret[i].Metric11,  // 生成贵族创作图的用户数
				ret[i].Metric12,  // 分享的用户数
				ret[i].Metric13,  // 贵族宠物图分享总次数
				ret[i].Metric14,  // 好友助力总次数
				ret[i].Metric15,  // 达到5票的用户数
				ret[i].Metric16,  // 达到25票的用户数
			})
		}
		if len(ret) < int(e.ExportParams.PageSize) || k >= total {
			break
		}
	}
	successNum = k
	_ = e.writer.Flush()
	return
}

// SetSheetName 设置表头
func (e PetStatTask) SetSheetName(args ...interface{}) {
	// 根据表格中的字段设置表头
	nameList := []interface{}{
		"日期",
		"阿闻首页弹窗点击uv",
		"阿闻首页banner点击uv",
		"阿闻首页浮标点击uv",
		"小闻首页广告点击uv",
		"贵族首页弹窗点击uv",
		"贵族首页banner点击uv",
		"贵族活动主页访问的pv",
		"贵族活动主页访问uv",
		"作品助力页面访问pv",
		"作品助力页面访问uv",
		"生成贵族创作图的用户数",
		"分享的用户数",
		"贵族宠物图分享总次数",
		"好友助力总次数",
		"达到5票的用户数",
		"达到25票的用户数",
	}
	_ = e.writer.SetRow("A1", nameList)
}

// GenerateDownUrl 生成下载链接
func (e PetStatTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("数据监测统计导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

// OperationFunc 操作函数（如需要）
func (e PetStatTask) OperationFunc(row []string, orgId int) string {
	return ""
}
