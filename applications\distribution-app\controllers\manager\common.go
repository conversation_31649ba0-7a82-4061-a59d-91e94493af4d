package manager

import (
	"bytes"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	service "eShop/services/base-service"
	"eShop/services/common"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/xuri/excelize/v2"

	"github.com/spf13/cast"
)

type CommonManager struct {
	OperateLogService service.OperateLogService
}

//这个里面放共用的接口处理，比如导入，导出，日志查看，日志添加等

// GetTaskList
// @Summary 获取任务列表
// @Description
// @Tags 共用中心
// @Accept plain
// @Produce plain
// @Param GetTaskListRequest query distribution_vo.GetTaskListRequest true "获取任务列表请求"
// @Success 200 {object} distribution_vo.TaskListRes
// @Failure 400 {object} distribution_vo.TaskListRes
// @Router /manager/comm/task-list [get]
func GetTaskList(writer http.ResponseWriter, request *http.Request) {
	resp := distribution_vo.TaskListRes{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.GetTaskListRequest](request)
	if err != nil {
		log.Error("获取任务出错，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(request.Header.Get("org_id"))
		}
		jwtInfo, err := jwtauth.GetJwtInfo(request)
		if err != nil {
			log.Error("获取登录信息失败: err", err.Error())
			resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		}
		req.CreateId = jwtInfo.UserNo
		ret, count, err := new(common.TaskListService).GetTaskList(req)
		if err != nil {
			log.Error("获取任务出错：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取任务出错：%s", err.Error())
		} else {
			resp.Code = 200
			resp.Total = count
			resp.Data = ret
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// TaskImport
// @Summary 导入接口
// @Description
// @Tags 共用中心
// @Accept plain
// @Produce plain
// @Accept plain
// @Produce plain
// @Param task_content formData int true "任务内容:1:业务员导入 2:业务员导出 3:分销订单导出 4:分销订单佣金结算导出 5-提现导出 6-导入打款记录(提现导入) 7分销员导出 8分销商品导入 9分销商品导出 10-保单分销结算导出 17-saas门店商品导出， 18 -saas连锁商品导出， 19-saas连锁商品导入 100-百林康源物流码导入 101-百林康源防伪码查询导出 102-百林康源防伪码查询导入"
// @Param operation_file_url formData string true "操作文件路径"
// @Param context_data formData string true "任务名称扩展字段:物流码导入类型（1-箱码 2-单个物流码）"
// @Param org_type formData int true "组织类型: 1-北京百林康源 2-深圳利都"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/comm/task-import [POST]
func TaskImport(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.TaskList](request)
	if err != nil {
		log.Error("创建任务，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(request.Header.Get("org_id"))
		}
		s := common.TaskListService{}
		req.OperationType = 1
		err := s.CreatTask(request, req)
		if err != nil {
			log.Error("创建任务：err=" + err.Error())
			resp.Message = fmt.Sprintf("创建任务：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// OperateLogPage 分页查询操作日志列表
// @Summary 分页查询操作日志列表
// @Description 分页查询操作日志列表接口
// @Tags 后台接口-共用中心
// @Accept  json
// @Produce  json
// @Param OperateLogPageReq body distribution_vo.OperateLogPageReq true " "
// @Success 200 {object} distribution_vo.OperateLogPageResp
// @Failure 400 {object} distribution_vo.OperateLogPageResp
// @Router /manager/comm/operate-log-page [GET]
func OperateLogPage(w http.ResponseWriter, r *http.Request) {
	resp := distribution_vo.OperateLogPageResp{
		BasePageHttpResponse: viewmodel.BasePageHttpResponse{
			Code: 400,
		},
	}

	req, err := utils.Bind[distribution_vo.OperateLogPageReq](r)

	if err != nil {
		log.Error("分页查询组织操作，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		list, total, err := new(service.OperateLogService).OperateLogPage(req)
		if err != nil {
			log.Error("分页查询组织操作失败：err=" + err.Error())
			resp.Message = fmt.Sprintf("分页查询组织操作异常：%s", err.Error())
		} else {
			resp.BasePageHttpResponse.Code = 200
			resp.BasePageHttpResponse.Total = total
			resp.Data = list
		}
	}
	if req.IsExport == 1 {
		f := excelize.NewFile()
		writer, _ := f.NewStreamWriter("Sheet1")
		_ = writer.SetRow("A1", []interface{}{
			"操作时间", "操作类型", "操作详情", "操作人",
		})

		for i := 0; i < len(resp.Data); i++ {
			v := resp.Data[i]
			var (
				w           distribution_vo.CommissionWithdraw
				moduleText  string
				Description string
			)
			//操作类型: module_type=5(1-查询物流码 2-扣除佣金 3-恢复物流码)
			if v.Type == 1 {
				moduleText = "查询物流码"
				Description = v.Description
			} else if v.Type == 2 {
				moduleText = "扣除佣金"
				err = json.Unmarshal([]byte(v.Description), &w)
				if err != nil {
					log.Error("json转map失败：err=" + err.Error())
				}
				Description = fmt.Sprintf("对分销员%s，扣除分销佣金%2.f元，原始分销订单:%s，原始物流码:%s,原始扫码登记手机号:%s", w.Name, utils.Fen2Yuan(w.Commission), w.OrderSn, w.LogisticsCode, w.Mobile)

			} else if v.Type == 3 {
				moduleText = "恢复物流码"
				err = json.Unmarshal([]byte(v.Description), &w)
				if err != nil {
					log.Error("json转map失败：err=" + err.Error())
				}
				Description = fmt.Sprintf("恢复物流码:%s，原始扫码登记手机号:%s，原始分销订单:%s", w.LogisticsCode, w.Mobile, w.OrderSn)
			}

			writer.SetRow("A"+strconv.Itoa(i+2), []interface{}{
				cast.ToString(v.CreateTime),
				moduleText,
				Description,
				v.UserName,
			})
		}
		writer.Flush()
		var buff bytes.Buffer
		if err = f.Write(&buff); err != nil {
			w.Write([]byte("导出文件失败"))
			return
		}
		fileName := "操作日志列表导出 -" + time.Now().Format("20060102150405") + ".xlsx"
		w.Header().Set(echo.HeaderContentType, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		w.Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
		w.Write(buff.Bytes())
		return
	}

	bytes, _ := json.Marshal(resp)
	w.Write(bytes)
}

//	获取所属组织列表（对应我们这边的区域）
//
// @Summary 获取所属组织列表（对应我们这边的区域）
// @Description
// @Tags 共用中心
// @Accept  json
// @Produce  json
// @Success 200 {object} distribution_vo.BaseOrganizationRes
// @Failure 400 {object} distribution_vo.BaseOrganizationRes
// @Router /manager/comm/region-list [GET]
func BaseRegionList(writer http.ResponseWriter, request *http.Request) {

	//测试过
	resp := distribution_vo.BaseOrganizationRes{}
	resp.Code = 400

	server := service.RegionService{}

	data, err := server.RegionList()
	if err != nil {
		log.Error("获取组织列表：err=" + err.Error())
		resp.Message = fmt.Sprintf("获取组织列表异常：" + err.Error())
	} else {
		resp.Code = 200
		resp.Data = data
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)

}
