package utils

import (
	"testing"
	"time"
)

func TestPublishRabbitMQV2(t *testing.T) {
	type args struct {
		routeKey   string
		exchange   string
		content    string
		amqpParams []interface{}
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "发送可自定义参数消息",
			args: args{
				routeKey:   "order.nopay",
				exchange:   "ordercenter",
				content:    "4000000000679758",
				amqpParams: []interface{}{int64(1 * time.Minute / time.Millisecond)},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := PublishRabbitMQV2(tt.args.routeKey, tt.args.exchange, tt.args.content, tt.args.amqpParams...); err != nil {
				t.Errorf("PublishRabbitMQV2() error = %v", err)
			}
		})
	}
}
