package distribution_po

import (
	"eShop/infra/utils"
	"eShop/services/distribution-service/enum"
	disdistributor "eShop/services/distribution-service/enum/dis-distributor"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

const (
	DisRoleInit   = 0
	DisRoleBoss   = 1
	DisRoleStaff  = 2
	DisRoleDoctor = 3
)

type DisDistributor struct {
	Id                     int       `json:"id" xorm:"pk autoincr not null comment('分销员ID') INT 'id'"`
	OrgId                  int       `json:"org_id" xorm:"default 0 comment('所属主体id') INT 'org_id'"`
	ShopId                 int       `json:"shop_id" xorm:"default 0 comment('分销店铺id') INT 'shop_id'"`
	MemberId               int       `json:"member_id" xorm:"not null comment('分销员用户id') INT 'member_id'"`
	SocialCreditCode       string    `json:"social_credit_code" xorm:"default '' comment('统一社会信用代码') VARCHAR(50) 'social_credit_code'"`
	SocialCodeImage        string    `json:"social_code_image" xorm:"default '' comment('统一社会信用代码图片') VARCHAR(255) 'social_code_image'"`
	EncryptIdCard          string    `json:"encrypt_id_card" xorm:"not null default '' comment('加密身份证号码') VARCHAR(100) 'encrypt_id_card'"`
	IdCard                 string    `json:"id_card" xorm:"not null default '' comment('身份证号码') VARCHAR(100) 'id_card'"`
	IdcardFront            string    `json:"idcard_front" xorm:"not null default '' comment('分销员身份证正面') VARCHAR(255) 'idcard_front'"`
	IdcardReverse          string    `json:"idcard_reverse" xorm:"not null default '' comment('分销员身份证反面') VARCHAR(255) 'idcard_reverse'"`
	DisRole                int       `json:"dis_role" xorm:"not null default 0 comment('分销员角色 0-初始值 1-老板 2-店员 3-医生') INT 'dis_role'"`
	Status                 int8      `json:"status" xorm:"default 0 comment('0-初始值   1-启用 2-禁用（已清退） 3-未启用') TINYINT(1) 'status'"`
	Name                   string    `json:"name" xorm:"default '' comment('分销员姓名') VARCHAR(50) 'name'"`
	Mobile                 string    `json:"mobile" xorm:"default '' comment('分销员手机号加*(可修改，有可能跟登录的手机号不一致)') VARCHAR(50) 'mobile'"`
	EncryptMobile          string    `json:"encrypt_mobile" xorm:"default '' comment('加密手机号') VARCHAR(20) 'encrypt_mobile'"`
	BankName               string    `json:"bank_name" xorm:"default '' comment('收款银行') VARCHAR(255) 'bank_name'"`
	BankAccount            string    `json:"bank_account" xorm:"default '' comment('收款账号') VARCHAR(255) 'bank_account'"`
	EncryptBankAccount     string    `json:"encrypt_bank_account" xorm:"default '' comment('加密收款账号') VARCHAR(100) 'encrypt_bank_account'"`
	AccountName            string    `json:"account_name" xorm:"default '' comment('开户姓名') VARCHAR(50) 'account_name'"`
	EncryptWithIdcard      string    `json:"encrypt_with_idcard" xorm:"not null default '' comment('用于提现的加密身份证号码') VARCHAR(100) 'encrypt_with_idcard'"`
	WithdrawIdCard         string    `json:"withdraw_id_card" xorm:"not null default '' comment('用于提现的身份证号码') VARCHAR(100) 'withdraw_id_card'"`
	BankBranch             string    `json:"bank_branch" xorm:"default '' comment('收款支行') VARCHAR(255) 'bank_branch'"`
	BarCode                string    `json:"bar_code" xorm:"default 'null' comment('分销员二维码') VARCHAR(200) 'bar_code'"`
	HeadImage              string    `json:"head_image" xorm:"default 'null' comment('分销员头像') VARCHAR(200) 'head_image'"`
	TuokeSalespersonId     int64     `json:"tuoke_salesperson_id" xorm:"not null default 0 comment('拓客业务员id') BIGINT 'tuoke_salesperson_id'"`
	OrderNum               int       `json:"order_num" xorm:"default 0 comment('分销单数') INT 'order_num'"`
	TotalSales             int       `json:"total_sales" xorm:"default 0 comment('分销销售额(分)') INT 'total_sales'"`
	SettledCommission      int       `json:"settled_commission" xorm:"default 0 comment('已结佣金(分)') INT 'settled_commission'"`
	UnsettledCommission    int       `json:"unsettled_commission" xorm:"not null default 0 comment('未结佣金(分)') INT 'unsettled_commission'"`
	InsSettledCommission   int       `json:"ins_settled_commission" xorm:"default 0 comment('保险已结佣金(分)') INT 'ins_settled_commission'"`
	InsUnsettledCommission int       `json:"ins_unsettled_commission" xorm:"not null default 0 comment('保险未结佣金(分)') INT 'ins_unsettled_commission'"`
	TotalCustomer          int       `json:"total_customer" xorm:"default 0 comment('累计客户数') INT 'total_customer'"`
	OrderPayNum            int       `json:"order_pay_num" xorm:"default 0 comment('已支付分销单数') INT 'order_pay_num'"`
	TotalPaySales          int       `json:"total_pay_sales" xorm:"default 0 comment('已支付分销销售额(分)') INT 'total_pay_sales'"`
	InsOrderPayNum         int       `json:"ins_order_pay_num" xorm:"default 0 comment('已支付保险分销单数') INT 'ins_order_pay_num'"`
	InsTotalPaySales       int       `json:"ins_total_pay_sales" xorm:"default 0 comment('已支付保险分销销售额(分)') INT 'ins_total_pay_sales'"`
	ApproveState           int8      `json:"approve_state" xorm:"default 0 comment('认证状态： 0-初始值， 1-待审核 2-审核通过 3-审核失败') TINYINT 'approve_state'"`
	ApproveTime            time.Time `json:"approve_time" xorm:"default 'null' comment('审核时间') DATETIME 'approve_time'"`
	Reason                 string    `json:"reason" xorm:"default '' comment('审核拒绝的原因') VARCHAR(255) 'reason'"`
	CreateTime             time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time' created"`
	UpdateTime             time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
	RealName               string    `json:"real_name" xorm:"default '' comment('分销员真实姓名') VARCHAR(50) 'real_name'"`
	BankMobile             string    `json:"bank_mobile" xorm:"default '' comment('收款人手机号加*') VARCHAR(50) 'bank_mobile'"`
	BankEncryptMobile      string    `json:"bank_encrypt_mobile" xorm:"default '' comment('收款人加密手机号') VARCHAR(20) 'bank_encrypt_mobile'"`
	VeterinaryCode         string    `json:"veterinary_code" xorm:"default '' comment('兽医资格证编号') VARCHAR(100) 'veterinary_code'"`
	InSystem               int8      `json:"in_system" xorm:"default 0 comment('类型   1：体系内   0：体系外') TINYINT 'in_system'"`
	SourceType             int8      `json:"source_type" xorm:"default 0 comment('来源类型：0-润合云店，1-润合SAAS') TINYINT 'source_type'"`
}

// 计算条数
func CntDistributor(db *xorm.Engine, where map[string]interface{}) (out []CntStru, err error) {
	session := db.NewSession()
	defer session.Close()
	out = make([]CntStru, 0)
	session = session.Table("eshop.dis_distributor").Select("count(*) as cnt,status")
	status, ok := where["status"]
	if ok {
		session = session.Where("status=?", status)
	}
	orgId, ok := where["orgId"]
	if ok {
		session = session.Where("org_id=?", orgId)
	}
	createTimeS, ok := where["createTimeStart"]
	if ok {
		session = session.Where("create_time>=?", createTimeS)
	}
	createTimeE, ok := where["createTimeEnd"]
	if ok {
		session = session.Where("create_time<=?", createTimeE)
	}
	groupBy, ok := where["groupBy"]
	if ok {
		session = session.GroupBy(groupBy.(string))
	}
	err = session.Find(&out)
	return
}

// 获取分销员信息
func GetDisInfo(db *xorm.Engine, where map[string]interface{}, field ...string) (out DisDistributor, err error) {
	session := db.NewSession()
	defer session.Close()
	sel := "*"
	if len(field) > 0 {
		sel = field[0]
	}
	session = session.Table("eshop.dis_distributor").Select(sel)
	if memberId, ok := where["memberId"]; ok {
		session = session.Where("member_id=?", memberId)
	}
	if encryptMobile, ok := where["encryptMobile"]; ok {
		session = session.Where("encrypt_mobile=?", encryptMobile)
	}
	if id, ok := where["id"]; ok {
		session = session.Where("id=?", id)
	}
	if shopId, ok := where["shopId"]; ok && shopId.(int) > 0 {
		session = session.Where("shop_id=?", shopId)
	} else {
		session = session.OrderBy("is_default desc,create_time desc")
	}
	if orgId, ok := where["orgId"]; ok {
		session = session.Where("org_id=?", orgId)
	}
	disRole, ok := where["disRole"]
	if ok {
		session = session.Where("dis_role=?", disRole)
	}

	if _, err = session.Get(&out); err != nil {
		return
	}
	return
}

type EditDisReq struct {
	Id     int `json:"id"`
	ShopId int `json:"shop_id"`
	OrgId  int `json:"org_id"`
}

// 更新分销员店铺id为0
// 指定字段更新
// update使用结构体
func (d *DisDistributor) EditDis(session *xorm.Session, where EditDisReq, update map[string]interface{}, field ...string) error {
	if session == nil {
		return errors.New("session不能为nil")
	}
	if where.Id > 0 {
		session = session.Where("id=?", where.Id)
	}
	if d == nil {
		return errors.New("更新数据不能为nil")
	}
	if where.ShopId > 0 {
		session = session.Where("shop_id=?", where.ShopId)
	}
	if where.OrgId > 0 {
		session = session.Where("org_id=?", where.OrgId)
	}
	list := make([]DisDistributor, 0)
	err := session.Table("eshop.dis_distributor").Find(&list)
	if err != nil {
		return err
	}
	if len(list) == 0 {
		return errors.New("分销员信息不存在")
	}
	if len(list) > 1 {
		return errors.New("分销员信息不唯一")
	}
	if len(field) > 0 {
		session = session.Cols(field...)
	}
	_, err = session.Table("eshop.dis_distributor").Where("id=?", list[0].Id).Update(update)
	if err != nil {
		return err
	}

	return nil
}

// 从企业中移除分销员
func (d *DisDistributor) RemoveFromEnterprise(session *xorm.Session, disId int, orgId int) (out DisDistributor, err error) {
	if session == nil {
		return out, errors.New("session不能为nil")
	}
	if disId == 0 {
		return out, errors.New("分销员id不能为0")
	}
	if orgId == 0 {
		return out, errors.New("主体id不能为0")
	}

	d.EditDis(session, EditDisReq{
		Id:    disId,
		OrgId: orgId,
	}, map[string]interface{}{
		"shop_id":            0,
		"approve_state":      0,
		"status":             3,
		"social_credit_code": "",
		"social_code_image":  "",
		"approve_time":       nil,
		"update_time":        time.Now(),
	})
	if err != nil {
		return out, err
	}
	return out, nil
}

// 获取分销员员信息
type GetDisInfoReq struct {
	MemberId   int    `json:"member_id"`
	ScrmUserId string `json:"scrm_user_id"`
	OrgId      int    `json:"org_id"`
}

// 获取分销员信息
func (d *DisDistributor) GetDisInfoByMemberIdOrScrmUserId(session *xorm.Session, in GetDisInfoReq) (out DisDistributor, err error) {
	if session == nil {
		return out, errors.New("session不能为nil")
	}
	if in.OrgId == 0 {
		return out, errors.New("主体id不能为0")
	}
	if in.MemberId == 0 && in.ScrmUserId == "" {
		return out, errors.New("会员id和scrm用户id不能同时为0")
	}
	member := 0
	//用SCRMID去查
	if in.MemberId == 0 {
		_, err := session.SQL("select member_id from upetmart.upet_member where scrm_user_id=?", in.ScrmUserId).Get(&member)
		if err != nil {
			return out, err
		}
	}
	if member > 0 {
		in.MemberId = member
	}
	session = session.Table("eshop.dis_distributor").Select("*").Where("member_id=?", in.MemberId).Where("org_id=?", in.OrgId)
	if _, err := session.Get(&out); err != nil {
		return out, err
	}
	return out, nil
}

// 检查老板分销员有效性
func CheckBossDisValid(dis DisDistributor) (err error) {
	if dis.Id == 0 {
		err = errors.New("分销员信息不存在")
		return
	}
	if dis.Status != disdistributor.StatusValid {
		err = errors.New("分销员状态不合法")
		return
	}
	if dis.ApproveState != disdistributor.ApproveStatePass {
		err = errors.New("分销员认证状态不合法")
		return
	}
	return
}

func GetDisList(db *xorm.Engine, where map[string]interface{}, field ...string) (out []DisDistributor, total int, err error) {
	session := db.NewSession()
	defer session.Close()
	out = make([]DisDistributor, 0)
	sel := "*"
	if len(field) > 0 {
		sel = field[0]
	}
	session = session.Table("eshop.dis_distributor").Select(sel)
	if memberId, ok := where["memberId"]; ok {
		session = session.Where("member_id=?", memberId)
	}
	if id, ok := where["id"]; ok {
		session = session.Where("id=?", id)
	}
	if ids, ok := where["ids"]; ok {
		session = session.In("id", ids)
	}
	if shopId, ok := where["shopId"]; ok && shopId.(int) > 0 {
		session = session.Where("shop_id=?", shopId)
	}

	pageSize, ok1 := where["pageSize"]
	pageIndex, ok2 := where["pageIndex"]
	if ok1 && ok2 {
		ps := pageSize.(int)
		pi := pageIndex.(int)
		session = session.Limit((ps), int(ps*(pi-1)))
	}

	tot, err := session.FindAndCount(&out)
	if err != nil {
		return
	}
	total = int(tot)
	return
}

// 校验老板信息是否正常
func (d *DisDistributor) CheckBossDisInfo(session *xorm.Session, bossDisId int, orgId int) (err error) {
	if session == nil {
		return errors.New("session不能为nil")
	}
	if bossDisId == 0 {
		err = errors.New("老板id不能为0")
		return
	}
	if orgId == 0 {
		err = errors.New("主体id不能为0")
		return
	}
	if d == nil {
		err = errors.New("分销员信息不能为nil")
		return
	}
	if _, err = session.Table("eshop.dis_distributor").Where("id=?", bossDisId).Where("org_id=?", orgId).Get(d); err != nil {
		return errors.New("查询老板信息失败" + err.Error())
	}
	if d.Id == 0 {
		err = errors.New("老板信息不存在")
		return
	}
	if d.DisRole != disdistributor.DisRoleBoss {
		err = errors.New("老板信息不合法")
		return
	}
	if d.Status != disdistributor.StatusValid {
		err = errors.New("老板信息状态不合法")
		return
	}
	if d.ApproveState != disdistributor.ApproveStatePass {
		err = errors.New("老板信息认证状态不合法")
		return
	}
	return nil
}

// CheckSocialCreditCodeUsed 检查社会信用代码是否被其他人使用(充血模型)
func (d *DisDistributor) CheckSocialCreditCodeUsed(session *xorm.Session, socialCreditCode string, mobile string) (string, error) {
	distributors := make([]DisDistributor, 0)
	// 查询是否存在使用该社会信用代码的分销员
	err := session.Table("eshop.dis_distributor").
		Select("id,org_id,encrypt_mobile,mobile").
		Where("org_id = 4 and dis_role=1 and approve_state in (1,2) and social_credit_code = ?", socialCreditCode).
		Find(&distributors)
	if err != nil {
		return "", fmt.Errorf("查询分销员信息失败: %v", err)
	}

	// 检查每个使用该社会信用代码的分销员
	for _, distributor := range distributors {
		checkMobile := utils.MobileDecrypt(distributor.EncryptMobile)
		if mobile != checkMobile {
			return distributor.Mobile, fmt.Errorf("该企业已被其他手机 %s 绑定。请使用该手机号登录，如非本人手机号绑定了您的营业执照，请联系客服或者业务员操作解绑", distributor.Mobile)
		}
	}

	return "", nil
}

// UpdateDefaultEnterprise 更新默认企业(充血模型)
func (d *DisDistributor) UpdateDefaultEnterprise(session *xorm.Session, disId int) error {
	// 先获取分销员信息
	distributor := new(DisDistributor)
	exist, err := session.Table("eshop.dis_distributor").
		Where("id = ?", disId).
		Get(distributor)
	if err != nil {
		return fmt.Errorf("查询分销员信息失败: %v", err)
	}
	if !exist {
		return errors.New("分销员不存在")
	}

	// 将该会员的所有分销员记录设置为非默认
	_, err = session.Table("eshop.dis_distributor").
		Where("member_id = ?", distributor.MemberId).
		Update(map[string]interface{}{
			"is_default":  0,
			"update_time": time.Now(),
		})
	if err != nil {
		return fmt.Errorf("重置默认状态失败: %v", err)
	}

	// 将当前分销员设置为默认
	_, err = session.Table("eshop.dis_distributor").
		Where("id = ?", disId).
		Update(map[string]interface{}{
			"is_default":  1,
			"update_time": time.Now(),
		})
	if err != nil {
		return fmt.Errorf("设置默认企业失败: %v", err)
	}

	return nil
}

// GetDistributorEnterpriseList 获取分销员关联的企业列表(充血模型)
func (d *DisDistributor) GetDistributorEnterpriseList(db *xorm.Engine, orgId int, memberId int) ([]struct {
	Id             int    `xorm:"id"`              // 分销员ID
	OrgId          int    `xorm:"org_id"`          // 主体ID
	ShopId         int    `xorm:"shop_id"`         // 店铺ID
	IsDefault      int8   `xorm:"is_default"`      // 是否默认
	EnterpriseName string `xorm:"enterprise_name"` // 企业名称
}, error) {
	var result []struct {
		Id             int    `xorm:"id"`
		OrgId          int    `xorm:"org_id"`
		ShopId         int    `xorm:"shop_id"`
		IsDefault      int8   `xorm:"is_default"`
		EnterpriseName string `xorm:"enterprise_name"`
	}

	err := db.Table("eshop.dis_distributor").Alias("d").
		Join("LEFT", []string{"eshop.shop", "s"}, "d.shop_id = s.id AND d.org_id = s.org_id").
		Join("LEFT", []string{"eshop.scrm_enterprise", "e"}, "s.enterprise_id = e.id").
		Select("d.id, d.org_id, d.shop_id, d.is_default, e.enterprise_name").
		Where("d.org_id = ? AND d.member_id = ?", orgId, memberId).
		Find(&result)

	return result, err
}

// 获取分销员的企业信息
func (d *DisDistributor) GetDistributorEnterpriseInfo(session *xorm.Session, disId int) (out DisEnterprise, err error) {
	if session == nil {
		return out, errors.New("session不能为nil")
	}
	if disId == 0 {
		return out, errors.New("分销员ID不能为0")
	}

	_, err = session.Table("eshop.dis_distributor").Alias("a").
		Join("LEFT", "eshop.shop b", "a.shop_id = b.id AND a.org_id = b.org_id").
		Join("LEFT", "eshop.dis_enterprise c", "b.enterprise_id = c.scrm_enterprise_id").
		Select("c.enterprise_name,c.province,c.city,c.district,c.address").
		Where("a.org_id=?", enum.BLKYOrgId).
		Where("a.id = ?", disId).Get(&out)
	if err != nil {
		return out, fmt.Errorf("查询分销员企业信息失败: %v", err)
	}

	return out, nil
}

func (d *DisDistributor) UpdateOrCreate(session *xorm.Session) (err error) {
	if session == nil {
		return errors.New("session不能为nil")
	}
	if d.Id > 0 {
		_, err = session.Table("eshop.dis_distributor").Where("id = ?", d.Id).Update(d)
	} else {
		_, err = session.Table("eshop.dis_distributor").Insert(d)
	}
	if err != nil {
		return fmt.Errorf("更新或创建分销员信息失败: %v", err)
	}
	return nil
}
