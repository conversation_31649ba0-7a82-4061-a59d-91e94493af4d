package product_vo

import (
	marketing_po "eShop/domain/marketing-po"
	po "eShop/domain/product-po"
	viewmodel "eShop/view-model"
)

type GetChannelProductSnapshotBySpuOrSkuRequest struct {
	ChannelId   int32  `json:"channel_id"`
	FinanceCode string `json:"finance_code"`
	ProductId   []int  `json:"product_id"`
	SkuId       []int  `json:"sku_id"`
}

type FindStoreProductListReq struct {
	viewmodel.BasePageHttpRequest
	ChainId           string `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`               //连锁ID
	StoreId           string `json:"store_id"`                                                                           //门店id
	ChannelId         int    `json:"channel_id"`                                                                         //售卖渠道 1-微信小程序 2-美团 3-饿了么 4-京东到家 100-线下门店
	Status            int    `json:"status"`                                                                             //商品状态：1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败 7删除失败
	Query             string `json:"query"`                                                                              //搜索条件：商品名称/条码/productid/skuid name,bar_code,product_id,skuid
	Type              int    `json:"type"`                                                                               // 0-全部 1-已上架 2-已下架  3-未铺品 4-有库存 5-有库存&未上架
	CategoryId        int    `json:"category_id" xorm:"default 'null' comment('分类id，线上') INT 'category_id'"`             //前端分类
	CategoryIdOffline int    `json:"category_id_offline" xorm:"not null comment('商品后台分类') BIGINT 'category_id_offline'"` //后端分类
	CategoryLevel     int    `json:"category_level"`                                                                     //分类等级 ： 1级分类就传1， 2级分类就传2
	IsStats           bool   `json:"is_stats"`                                                                           //是否求出统计
	IsOnlyCount       bool   `json:"is_only_count"`                                                                      // 是否只求出数量即可
	DataType          int    `json:"data_type"`                                                                          // 数据类型：1店内数据 2新零售数据
	SkuIds            string `json:"sku_ids"`
	LocationCode      string `json:"location_code"`      //库位码
	IsShowSingleSku   bool   `json:"is_show_single_sku"` //是否只展示精确搜索出的sku
	//是否导出
	Exprot      int `json:"exprot"`
	WarehouseId int `json:"warehouse_id"` //仓库id

}
type StoreProductListApiReq struct {
	viewmodel.BasePageHttpRequest
	ChannelId  int    `json:"channel_id"`                                                          // 渠道id
	ChainId    int64  `json:"chain_id"`                                                            //连锁id(前端无需传)
	StoreId    string `json:"store_id"`                                                            //门店id(前端无需传)
	ProductId  int    `json:"product_id"`                                                          // 商品id
	Query      string `json:"query"`                                                               //搜索条件：商品名称/条码/id (name,bar_code,product_id,sku_id)
	CategoryId int    `json:"category_id" xorm:"default 'null' comment('分类id') INT 'category_id'"` //前端分类
	HasStock   bool   `json:"has_stock"`                                                           // true：只查询有库存的商品
}
type StoreSkuDetailReq struct {
	ChainId int64  `json:"chain_id"` //连锁id(前端无需传)
	StoreId string `json:"store_id"` //门店id(前端无需传)
	SkuIds  []int  `json:"sku_ids"`  //商品skuid
}
type StoreSkuDetailRes struct {
	viewmodel.BaseHttpResponse
	Data []SkuDetail `json:"data"`
}

type RunningMarketingSkuReq struct {
	ChainId            int64  // 连锁id
	StoreId            string // 门店id
	Type               int    //关联类型：1-优惠券 2-次卡 3-储值卡 4-满减 5-特价（如果type等于0， 则查出正在参与优惠券和满减和特价这三个活动的商品列表）
	SkuIds             []int  // 查询这些sku参与的活动
	CategoryIdOfflines []int
}
type RunningMarketingSkuRes struct {
	CouponActivity       []CouponActivity `json:"coupon_activity"`         // 当前店铺所有可用的优惠券列表
	Activity             []Activity       `json:"activity"`                //当前店铺所有可用的满减活动和特价活动
	SkuIdYes             []int            `json:"sku_id_yes"`              //可用sku列表（所有活动可用的商品sku列表）
	SkuIdNo              []int            `json:"sku_id_no"`               //不可用sku列表（所有活动不可用的商品sku列表）
	CategoryIdOfflineYes []int            `json:"category_id_offline_yes"` // 适用的商品分类（所有活动可用的分类id,目前只有满减活动可用设置指定商品分类）
	ApplyAllProduct      bool             `json:"apply_all_product"`       // 是否适用全部商品
}

type GetSkuPromotionReq struct {
}
type GetSkuPromotionRes struct {
	MarketingActivity []marketing_po.MarketingActivity `json:"full_reduction"`   // 满减活动和特价活动
	MarketingCoupon   []marketing_po.MarketingCoupon   `json:"marketing_coupon"` // 优惠券
	PromotionInvolved []PromotionInvolved              `json:"promotion_involved"`
}

// 参与的活动类型
type PromotionInvolved struct {
	PromotionType int    `json:"promotion_type"` //活动类型 1-优惠券 2-次卡 3-储值卡 4-满减 5-特价
	PromotionName string `json:"promotion_name"` // 活动名称
}
type CouponActivity struct {
	ActivityInfo marketing_po.MarketingCoupon
	SkuIdYes     []int
	SkuIdNo      []int
}

type Activity struct {
	ActivityInfo         marketing_po.MarketingActivity
	SkuIdYes             []int `json:"sku_id_yes"`              //可用sku列表
	CategoryIdOfflineYes []int `json:"category_id_offline_yes"` // 可用的商品分类
}
type StoreProductListApiRes struct {
	viewmodel.BasePageHttpResponse
	List []*StoreProductListApiData `json:"list"` // 列表数据
}

type FindStoreProductListRes struct {
	viewmodel.BasePageHttpResponse
	Data struct {
		List  []FindStoreProductList `json:"list"`  // 列表数据
		Stats StoreProductStats      `json:"stats"` // 统计字段
	} `json:"data"`
}
type FindStoreSkuListRes struct {
	viewmodel.BasePageHttpResponse
	Data []FindStoreProductList `json:"data"` // 列表数据
}
type StoreProductStats struct {
	TotalCnt        int64 `json:"total_cnt"`          //全部
	UpCnt           int64 `json:"up_cnt"`             //已上架
	DownCnt         int64 `json:"down_cnt"`           //已下架
	UnLaunchCnt     int64 `json:"un_launch_cnt"`      //未铺品
	StockCnt        int64 `json:"stock_cnt"`          //有库存
	StockAndDownCnt int64 `json:"stock_and_down_cnt"` //有库存&未上架

}

type FindStoreProductList struct {
	ProductId         int    `json:"product_id"`                                                                               //商品id
	Pic               string `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"`                        //商品图片（多图）
	Name              string `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`                           //商品名称
	ContentPc         string `json:"content_pc"`                                                                               // 商品详情
	CategoryId        int    `json:"category_id" xorm:"default 'null' comment('商品前台分类') BIGINT 'category_id'"`                 // 商品前台分类
	CategoryIdOffline int    `json:"category_id_offline" xorm:"default 'null' comment('商品后台分类') BIGINT 'category_id_offline'"` // 商品后台分类
	// 后台分类路径（父分类名称>子分类名称）
	CategoryNav string `json:"category_nav" xorm:"default 'null' comment('后台分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁') VARCHAR(500) 'category_nav'"` //分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁
	// 前台分类路径（父分类名称>子分类名称）
	CategoryNavOnline string `json:"category_nav_online" xorm:"default 'null' comment('前端分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁') VARCHAR(500) 'category_nav2'"` //分类别名，多层级的组合起来显示，如：猫站>猫咪专区（实物商品）>生活日用>口腔清洁
	// 商品类型 1-实物商品 2-虚拟商品 3-组合商品 4-服务 5-活体
	ProductType          int    `json:"product_type"`
	MtCategoryThirdId    string `json:"mt_category_third_id"`
	MtCategoryThirdName  string `json:"mt_category_third_name"`
	ElmCategoryThirdId   string `json:"elm_category_third_id"`
	ElmCategoryThirdName string `json:"elm_category_third_name"`

	UpdateDate            string      `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"` //商品最后更新日期
	NewSellStr            string      `json:"new_sell_str" xorm:"not null default '' comment('新渠道信息（1-小程序，2-美团，3-饿了么，4-京东到家）,字符串拼接') VARCHAR(100) 'new_sell_str'"`
	NewSell               int         `json:"new_sell"`
	Sku                   []SkuExtend `json:"sku"` //连锁商品sku信息
	BrandName             string      `json:"brand_name" xorm:"default '' comment('品牌名称,只商品编辑取这个字段') VARCHAR(64) 'brand_name'"`
	SupplierName          string      `json:"supplier_name" xorm:"default '' comment('供应商名称') VARCHAR(64) 'supplier_name'"`
	InventoryTotalNum     int         `json:"inventory_total_num"`     //总库存
	InventoryFreezeNum    int         `json:"inventory_freeze_num"`    //锁定库存
	InventoryAvailableNum int         `json:"inventory_available_num"` //可用库存
	ServiceDuration       int         `json:"service_duration"`        //服务时长
	WarehouseId           int         `json:"warehouse_id"`            //仓库id
	LocationCode          string      `json:"location_code"`           // 库位码
}

type FindStoreSkuListReq struct {
	ChannelId         int    `json:"channel_id"`          // 渠道id
	StoreId           string `json:"store_id"`            // 门店id
	SkuIds            []int  `json:"sku_ids"`             // skuId
	SkuidsNo          []int  `json:"skuids_no"`           // 排除指定sku信息
	ProductId         int    `json:"product_id"`          // 商品id
	ProductName       string `json:"product_name"`        // 商品名称
	ProductType       int    `json:"product_type"`        // 商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)
	CategoryId        int    `json:"category_id"`         // 分类id
	CategoryIds       []int  `json:"categoryIds"`         // 分类ids
	CategoryIdOffline []int  `json:"category_id_offline"` // 后台分类
	UpDownState       int    `json:"up_down_state"`       // 上下架状态（1-上架，0-下架）
	BarCode           string `json:"bar_code"`            // 条码
	PageIndex         int    `json:"page_index"`          // 页码
	PageSize          int    `json:"page_size"`           // 每页数量
	KeyQuery          string `json:"key_query"`           // 查询条件：商品名称和条形码（适用实物和服务和活体）
	KeyQuery2         string `json:"key_query2"`          // 查询条件：商品名称， 条码， 商品id，skuid（仅适用实体商品）
	HasStock          bool   `json:"has_stock"`           // true：只查询有库存的商品

}

type FindStoreInOutListRes struct {
	viewmodel.BasePageHttpResponse
	Data []FindStoreInOutList `json:"data"` // 列表数据
}
type FindStoreInOutListReq struct {
	ChainId           int64  `json:"chain_id"`            // 连锁id(前端无需传)
	WarehouseId       int    `json:"warehouse_id"`        // 仓库id
	StoreId           string `json:"store_id"`            // 店铺id(前端无需传)
	ProductName       string `json:"product_name"`        // 商品名称
	BarCode           string `json:"bar_code"`            // 条码
	CategoryIdOffline int    `json:"category_id_offline"` //商品后台分类的末级分类id(只传末级分类id就好)
	PageIndex         int    `json:"page_index"`          // 页码
	PageSize          int    `json:"page_size"`           // 每页数量
	QueryKey          string `json:"query_key"`           //关键字查询： 商品名称或条形码或商品id或skuid或库位
}
type FindStoreInOutList struct {
	ProductId         int    `json:"product_id"`         //商品id
	SkuId             int    `json:"sku_id"`             // skuid
	Name              string `json:"name"`               //商品名称
	Pic               string `json:"pic"`                //商品图片
	BarCode           string `json:"bar_code"`           //条形码
	ProductSpecs      string `json:"product_specs"`      // 商品规格
	AvailableNum      int    `json:"available_num"`      //可用库存
	AvgCostPrice      int    `json:"avg_cost_price"`     //平均成本价(单位分)
	WarehouseName     string `json:"warehouse_name"`     //仓库名称
	WarehouseId       int    `json:"warehouse_id"`       //仓库id
	ChannelId         int    `json:"channel_id"`         // 渠道id
	WarehouseCategory int    `json:"warehouse_category"` // 仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓，5-前置仓虚拟仓)
	LocationCode      string `json:"location_code"`      // 库位编码

}

type StoreProductSku struct {
	ProductId         int    `json:"product_id"`          // 商品id
	ProductType       int    `json:"product_type"`        // 商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)
	Name              string `json:"name"`                // 商品名称
	Pic               string `json:"pic"`                 // 商品图片
	NewSell           int    `json:"new_sell"`            // 是否新零售
	CategoryNav       string `json:"category_nav"`        // 分类路径
	CategoryId        int    `json:"category_id"`         // 分类id
	CategoryIdOffline int    `json:"category_id_offline"` // 分类id
	ContentPc         string `json:"content_pc"`          //商品详情
	ChannelId         int    `json:"channel_id"`          // 渠道id
	ProductThirdId    string `json:"product_third_id"`    // 第三方商品id
	ChannelCategoryId int    `json:"channel_category_id"` // 渠道分类id
	SkuIds            string `json:"sku_ids"`             // skuIds
	BarCode           string `json:"bar_code"`            // 条码

}

type FindStoreProductListBySkuIdRes struct {
	viewmodel.BasePageHttpResponse
	Data struct {
		List []FindStoreProductList `json:"list"` // 列表数据
	} `json:"data"`
}
type SkuExtend struct {
	BaseSku     po.ProSku     `json:"base_sku"`
	ChannelInfo []ChannelInfo `json:"channel_info"` // 渠道商品
}

type StoreProductListApiData struct {
	ProductId         int                 `json:"product_id"`                                                        //商品id
	Pic               string              `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"` //商品图片（多图）
	Name              string              `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`    //商品名称
	Skus              []SkuDetail         `json:"skus"`                                                              //商品sku信息
	ContentPc         string              `json:"content_pc"`                                                        // 商品详情
	PromotionInvolved []PromotionInvolved `json:"promotion_involved"`                                                // 参与的活动类型
}
type SkuDetail struct {
	ProductId         int                              `json:"product_id"`                                                        //商品id
	Pic               string                           `json:"pic" xorm:"default 'null' comment('商品图片（多图）') VARCHAR(1000) 'pic'"` //商品图片（多图）
	Name              string                           `json:"name" xorm:"default 'null' comment('商品名称') VARCHAR(255) 'name'"`    //商品名称
	ContentPc         string                           `json:"content_pc"`                                                        // 商品详情
	SkuId             int                              `json:"sku_id"`                                                            //skuid
	RetailPrice       int                              `json:"retail_price"`                                                      // 零售价（单位分）
	Price             int                              `json:"price"`                                                             // 价格（单位分）
	BarCode           string                           `json:"bar_code"`                                                          // 条码
	ProductSpecs      string                           `json:"product_specs"`                                                     // 规格
	Stock             int                              `json:"stock"`                                                             //库存
	CategoryId        int                              `json:"category_id"`                                                       // 前台分类id
	CategoryIdOffline int                              `json:"category_id_offline"`                                               // 后台分类id
	PromotionInvolved []PromotionInvolved              `json:"promotion_involved"`                                                // 参与的活动类型
	MarketingActivity []marketing_po.MarketingActivity `json:"marketing_activity"`                                                // 满减活动和特价活动
	MarketingCoupon   []marketing_po.MarketingCoupon   `json:"marketing_coupon"`                                                  // 优惠券
}

type PromotionDetail struct {
	Id                             int32  `json:"id"`                              //优惠活动id
	Type                           int32  `json:"type"`                            //优惠活动类型，1-商品特价 2-店铺满减 1满减 2限时折扣 3满减运费 4会员折扣
	ReachMoney                     string `json:"reach_money"`                     //满足金额
	ReduceMoney                    string `json:"reduce_money"`                    //减免金额
	TimeDiscountEndDate            string `json:"timeDiscount_endDate"`            //限时折扣截止时间
	TimeDiscountType               int    `json:"timeDiscount_type"`               //限时折扣类型 0 折扣 1 固定价格
	TimeDiscountValue              int    `json:"timeDiscount_value"`              //限时折扣类型值 0 折扣率 1 固定价格值,分
	TimeDiscountLimitCountByOrder  int    `json:"timeDiscount_limitCountByOrder"`  //限时折扣每单可以下单数量  0 不限制  非0限制数量
	TimeDiscountLimitCountByStock  int    `json:"timeDiscount_limitCountByStock"`  //限时折扣可以下单数量库存限制 0 不限制  非0限制数量
	TimeDiscountRemainCountByStock int    `json:"timeDiscount_remainCountByStock"` //限时折扣当天可以下单的库存数量
	TimeDiscountMaxSkuCountByOrder int    `json:"timeDiscount_maxSkuCountByOrder"` //限时折扣每单可以下单sku数量

}
type ChannelInfo struct {
	ChannelId int `json:"channel_id"` //售卖渠道:渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)

	Desc string `json:"desc"` // 描述 示例： 上架或下架或未铺品或不可售卖

	RetailPrice int `json:"retail_price" xorm:"default 0 comment('建议价格/零售价') INT 'retail_price'"` // 建议价格/零售价（单位分）

	SyncError string `json:"sync_error" xorm:"not null comment('铺品或者上架报错信息') VARCHAR(500) 'sync_error'"` // 铺品或者上架报错信息(金叹号里的内容)
	// 1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败
	Status int `json:"status" xorm:"default 0 comment('1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败 ') INT 'status'"`

	CategoryThirdName string `json:"category_third_name" xorm:"default 'null' comment('第三方类目名称') INT 'category_third_name'"`
	CategoryThirdId   string `json:"category_third_id" xorm:"default 'null' comment('第三方类目id') INT 'category_third_id'"`
	ProductThirdId    string `json:"product_third_id"` //第三方回写的商品ID
	SkuThirdId        string `json:"sku_third_id"`
	//渠道配置第三方店铺id
	ChannelStoreId string `json:"channel_store_id"`
	ChildCateName  string `json:"child_cate_name" xorm:"default '' comment('子分类名称') VARCHAR(50) 'child_cate_name'"`
	ParentCateName string `json:"parent_cate_name" xorm:"default '' comment('父分类名称') VARCHAR(50) 'parent_cate_name'"`
}

type GetStoreProductRes struct {
	viewmodel.BaseHttpResponse
	Data StoreProduct `json:"data"`
}

type GetStoreProductReq struct {
	ProductId int `json:"product_id"` // 连锁库商品ID
	//门店ID(直接获取token)
	StoreId string `json:"store_id"`
}

type StoreProductUpDistinctReq struct {
	// 渠道id
	ChannelId int32 `json:"channel_id"`
	// 财务编码
	FinanceCode []string `json:"finance_code"`
	// 商品id
	ProductId []int32 `json:"product_id"`
	// skuId
	SkuId []int32 `json:"sku_id"`
	// 商品名称
	Name      string `json:"name"`
	PageIndex int32  `json:"page_index"`
	PageSize  int32  `json:"page_size"`
}

type StoreProductUpDistinctRes struct {
	Code    int32            `json:"code"`
	Message string           `json:"message"`
	Data    []StoreUpProduct `json:"data"`
	Total   int32            `json:"total"`
}

type StoreUpProduct struct {
	// 商品id
	ProductId int32 `json:"product_id"`
	// skuId
	SkuId int32 `json:"sku_id"`
	// 商品名称
	Name string `json:"name"`
}

// 商品按价格统计
type ProductCountByPriceReq struct {
	// 渠道id
	ChannelId int32 `json:"channel_id"`
	// 财务编码
	FinanceCode []string `json:"finance_code"`
	// 商品sku_id
	SkuId []int32 `json:"sku_id"`
}

type ProductCountByPriceRes struct {
	Code    int32                 `json:"code"`
	Message string                `json:"message"`
	Data    []ProductCountByPrice `json:"data"`
}

type ProductCountByPrice struct {
	// 商品id
	ProductId int32 `json:"product_id"`
	// skuId
	SkuId int32 `json:"sku_id"`
	// 商品名称
	Name string `json:"name"`
	// 售价
	MarketPrice int32 `json:"market_price"`
	// 关联门店数量
	Count int32 `json:"count"`
	// 随机一家门店名称
	ShopName string `json:"shop_name"`
}

type ExportStoreProductList struct {
	StoreId   string `json:"store_id"`
	ProductId int    `json:"product_id"`
	// 商品名称
	Name string `json:"name"`
	//
	BarCode string `json:"bar_code"`
	// 商品一级分类
	ProductFirstCategory string `json:"product_first_category"`
	// 商品二级分类
	ProductSecondCategory string `json:"product_second_category"`
	// 品牌
	BrandName string `json:"brand_name"`
	// 供应商
	SupplierName string `json:"supplier_name"`
	// 规格
	ProductSpecs string `json:"product_specs"`
	// 库存单位
	StoreUnit string `json:"store_unit"`
	// 零售价
	RetailPrice int `json:"retail_price"`
	// 市场价
	MarketPrice int `json:"market_price"`
	// 重量
	WeightForUnit float64 `json:"weight_for_unit"`
	// 平台货号
	SkuCode string `json:"sku_code"`
	// 新零售销售
	ChannelNameStr string `json:"channel_name_str"`
	// 店铺一级分类
	StoreCategoryOne string `json:"store_category_one"`
	// 店铺二级分类
	StoreCategoryTwo string `json:"store_category_two"`
	// 美团商品类目
	MtCategory string `json:"mt_category"`
	// 饿了么商品类目
	ElmCategory string `json:"elm_category"`
	// 京东到家商品类目
	JdCategory string `json:"jd_category"`
	// 上下架状态（1-上架，0-下架）
	UpDownState int `json:"up_down_state"`
	// 1正常 2操作中 3铺品失败 4上架失败 5下架失败 6更新失败
	Status int `json:"status"`
	// 0未铺品，1已铺品 ：指的是在第三方门店创建商品是否成功
	IsDistribution int `json:"is_distribution"`

	NewSellStr string `json:"new_sell_str"`
}

// 根据店铺Id查询满减活动
type PromotionQueryByShopIdRequest struct {
	//店铺Id
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 是否返回活动满减详细信息
	ShowDetail bool `protobuf:"varint,3,opt,name=showDetail,proto3" json:"showDetail"`
	// 活动类型 1 满减 2 限时折扣
	Types int32 `protobuf:"varint,6,opt,name=types,proto3" json:"types"`
	//分页信息--当前页
	PageIndex int32 `protobuf:"varint,7,opt,name=pageIndex,proto3" json:"pageIndex"`
	//分页信息--每页个数
	PageSize int32 `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize"`
	// 商品ids
	ProductIds []int32 `json:"product_id"`
}

// 通过sku查询商品是否参与满减活动 数据响应
type PromotionQueryBySkuIdsResponse struct {
	viewmodel.BasePageHttpRequest
	// 关联关系
	Data []*PromotionSkuDto `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
}

type PromotionSku struct {
	Id        int32  `json:"id"`         // 活动id
	Uuid      string `json:"uuid"`       // 活动uuid
	SkuId     string `json:"sku_id"`     // 商品skuid
	Types     int32  `json:"types"`      //活动类型 1 满减活动 2 限时折扣 3 满减运费 12 赠险
	Title     string `json:"title"`      // 活动标题
	ProductId int    `json:"product_id"` // 商品id
	Pic       string `json:"pic"`        //商品图片
	Name      string `json:"name"`       //商品名称
	//	JsonData    string `json:"json_data"`
}

// 促销活动与SKU关联关系
type PromotionSkuDto struct {
	// 类型 1 满减 2 限时折扣 3 满减运费 4会员折扣
	Types int32 `protobuf:"varint,1,opt,name=types,proto3" json:"types"`
	// 促销活动名称
	PromotionName string `protobuf:"bytes,2,opt,name=promotionName,proto3" json:"promotionName"`
	// sku信息
	SkuId string `protobuf:"bytes,3,opt,name=skuId,proto3" json:"skuId"`
	// 促销活动明细信息
	ReduceList []*PromotionReduceDto `protobuf:"bytes,4,rep,name=reduceList,proto3" json:"reduceList"`
	// 限时折扣
	TimeDiscount *PromotionSkuTimeDiscountDto `protobuf:"bytes,6,opt,name=timeDiscount,proto3" json:"timeDiscount"`
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,7,opt,name=promotionId,proto3" json:"promotionId"`
	//商品信息
	ProductId int `json:"product_id"` // 商品id

}

// 活动享受的满减优惠dto
type PromotionReduceDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,2,opt,name=promotionId,proto3" json:"promotionId"`
	// 最小金额
	ReachMoney float64 `protobuf:"fixed64,16,opt,name=reachMoney,proto3" json:"reachMoney"`
	// 减免金额
	ReduceMoney float64 `protobuf:"fixed64,17,opt,name=reduceMoney,proto3" json:"reduceMoney"`
}

// 限时折扣
type PromotionSkuTimeDiscountDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotionId,proto3" json:"promotionId"`
	// 截止日期
	EndDate string `protobuf:"bytes,2,opt,name=endDate,proto3" json:"endDate"`
	// 折扣类型  0 按折扣 固定活动价格
	DisountType int32 `protobuf:"varint,3,opt,name=disountType,proto3" json:"disountType"`
	// 为 0 时代表折扣 为 1 代表固定价格
	DiscountValue int32 `protobuf:"varint,4,opt,name=discountValue,proto3" json:"discountValue"`
	// 单限购 0 不限制, 非0  限制多少数量
	LimitCountByOrder int32 `protobuf:"varint,5,opt,name=limitCountByOrder,proto3" json:"limitCountByOrder"`
	// 当日限购 0 不限制,非0 限时库存数量
	LimitCountByStock int32 `protobuf:"varint,6,opt,name=limitCountByStock,proto3" json:"limitCountByStock"`
}

type PromotionVipDiscountDto struct {
	// 折扣或价格
	DiscountValue int32 `protobuf:"varint,2,opt,name=discountValue,proto3" json:"discountValue"`
}

// 限时折扣配置
type PromotionTimeDiscountDto struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,7,opt,name=promotionId,proto3" json:"promotionId"`
	// 用户类型 0 全部 1 新客户
	UserType int32 `protobuf:"varint,2,opt,name=UserType,proto3" json:"UserType"`
	// 折扣类型  0 按折扣 固定活动价格
	DisountType int32 `protobuf:"varint,3,opt,name=DisountType,proto3" json:"DisountType"`
	// 为 0 时代表折扣 为 1 代表固定价格 (统一传浮点数)
	DiscountValue float64 `protobuf:"fixed64,4,opt,name=DiscountValue,proto3" json:"DiscountValue"`
	// 单限购 0 不限制, 非0  限制多少数量
	LimitCountByOrder int32 `protobuf:"varint,5,opt,name=LimitCountByOrder,proto3" json:"LimitCountByOrder"`
	// 当日限购 0 不限制,非0 限时库存数量
	LimitCountByStock int32 `protobuf:"varint,6,opt,name=LimitCountByStock,proto3" json:"LimitCountByStock"`
	// 配置的活动购买数量
	ConfigBuyCount int32 `protobuf:"varint,8,opt,name=ConfigBuyCount,proto3" json:"ConfigBuyCount"`
	// 会员额外折扣
	VipDiscount float64 `protobuf:"fixed64,9,opt,name=VipDiscount,proto3" json:"VipDiscount"`
}

// 优惠计算信息dto
type PromotionCalcDto struct {
	// 类型 1满减、2限时折扣
	PromotionType int32 `protobuf:"varint,1,opt,name=promotionType,proto3" json:"promotionType"`
	// 促销活动
	PromotionId int32 `protobuf:"varint,2,opt,name=promotionId,proto3" json:"promotionId"`
	// 名称
	PromotionTitle string `protobuf:"bytes,3,opt,name=promotionTitle,proto3" json:"promotionTitle"`
	// 金额
	PromotionFee int32 `protobuf:"varint,4,opt,name=promotionFee,proto3" json:"promotionFee"`
}

type StoreProductStockReq struct {
	ChannelId   int64   `json:"channel_id"`   //渠道
	FinanceCode string  `json:"finance_code"` //店铺id
	SkuIds      []int64 `json:"sku_ids"`      //skuid
}

type StoreProductStockRes struct {
	viewmodel.BaseHttpResponse
	Data []StockStru `json:"data"`
}

type StockStru struct {
	FinanceCode string           `json:"finance_code"` //门店id
	StockInfo   map[string]int32 `json:"stock_info"`   // map[skuid]库存值
}

type StoreProductUpdownstatusReq struct {
	ChannelId   int    `json:"channel_id"`   //渠道
	ChainId     int64  `json:"chain_id"`     // 连锁id(前端无需传)
	FinanceCode string `json:"finance_code"` //店铺id（前端无需传）
	SkuIds      []int  `json:"sku_ids"`      //skuid
}

type StoreProductUpdownstatusRes struct {
	viewmodel.BaseHttpResponse
	Data []UpDownStru `json:"data"`
}

type UpDownStru struct {
	FinanceCode string         `json:"finance_code"` //门店id
	StateInfo   map[string]int `json:"state_info"`   // map[productid]上下架   0下架，1上架
}

// 通过sku查询商品是否参与满减活动
type PromotionQueryBySkuIdsRequest struct {
	// 门店财务编码
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	// 渠道Id
	ChannelId int32 `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	// 商品Sku列表
	SkuIds []int64 `protobuf:"bytes,2,rep,name=skuIds,proto3" json:"skuIds"`
	// 是否返回满减明细列表
	IsReachReduceInfo bool `protobuf:"varint,4,opt,name=isReachReduceInfo,proto3" json:"isReachReduceInfo"`
}

type UpdateLocationRequest struct {
	StoreId      string `json:"store_id"`      // 店铺ID
	SkuId        int    `json:"sku_id"`        // 商品SKU ID
	LocationCode string `json:"location_code"` // 库位编码
	ChainId      int64  `json:"chain_id"`      // 店铺所属连锁id(前端无需传)
}

type UpdateLocationSkuRequest struct {
	StoreId      string `json:"store_id"`      // 店铺ID
	SkuId        string `json:"sku_id"`        // 库位要绑定的新的商品skuid或商品条码
	LocationCode string `json:"location_code"` // 库位编码
	ChainId      int64  `json:"chain_id"`      // 店铺所属连锁id(前端无需传)
}

// 服务列表请求
type ServiceListReq struct {
	StoreId     string `json:"store_id"`                 // 店铺ID
	ProductName string `json:"product_name"`             // 服务名称
	CategoryId  int    `json:"category_id"`              // 商品分类
	PageIndex   int    `json:"page_index"`               // 页码
	PageSize    int    `json:"page_size"`                // 每页数量
	ProductType int    `json:"product_type" default:"1"` // 商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)
}

// 服务列表响应
type ServiceListRes struct {
	viewmodel.BaseHttpResponse
	Data  []ServiceInfo `json:"data"`
	Total int64         `json:"total"`
}

type ServiceInfo struct {
	Id           int     `json:"id"`            // 服务ID
	Name         string  `json:"name"`          // 服务名称
	CategoryId   int     `json:"category_id"`   // 分类ID
	CategoryName string  `json:"category_name"` // 分类名称
	Price        float64 `json:"price"`         // 价格
	Status       int     `json:"status"`        // 状态 1:正常 2:下架
	CreateDate   string  `json:"create_date"`   // 创建时间
	BarCode      string  `json:"bar_code"`      // 条码
	//商品分类
	NamePath string `json:"namePath" xorm:"default 'null' comment('分类组合名称') VARCHAR(500) 'namePath'"`
	//商品图片：逗号分割多张
	ProductPic string `json:"product_pic" xorm:"default '' comment('商品图片') VARCHAR(255) 'product_pic'"`
	// 商品id
	ProductId int32 `json:"product_id"`
	// skuId
	SkuId int32 `json:"sku_id"`
}

// 新增/编辑服务请求
type SaveServiceReq struct {
	Id              int     `json:"id"`                           // 服务ID,新增时为0
	StoreId         string  `json:"store_id"`                     // 店铺ID
	Name            string  `json:"name"`                         // 服务名称
	CategoryId      int     `json:"category_id"`                  // 商品分类ID
	Price           float64 `json:"price"`                        // 零售价
	MarketPrice     float64 `json:"market_price"`                 // 市场价
	Images          string  `json:"images"`                       // 商品图片
	ProductType     int     `json:"product_type" default:"1"`     // 商品类型：1-商品，2-服务 3-活体
	ServiceDuration int     `json:"service_duration" default:"0"` // 服务时长
	BirthDate       string  `json:"birth_date"`                   // 出生日期
	PetVariety      string  `json:"pet_variety"`                  // 宠物品种
	PetVarietyName  string  `json:"pet_variety_name"`             // 宠物品种名称
	BarCode         string  `json:"bar_code"`                     // 条码
	NamePath        string  `json:"namePath"`                     // 分类组合名称
	ChainId         int64   `json:"chain_id" xorm:"default 0 comment('连锁ID') BIGINT 'chain_id'"`
	// 商品ID
	ProductId int `json:"product_id" xorm:"default 0 comment('商品ID') INT 'product_id'"`
}
