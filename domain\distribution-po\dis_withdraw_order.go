package distribution_po

import (
	"eShop/infra/log"
	vo "eShop/view-model/distribution-vo"
	"errors"
	"fmt"
	"time"

	"eShop/infra/utils"
	diswithdraw "eShop/services/distribution-service/enum/dis-withdraw"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

// DisWithdrawOrder 提现订单表
type DisWithdrawOrder struct {
	ID         int       `xorm:"pk autoincr 'id' comment('结算ID')"`
	WithdrawNo string    `xorm:"varchar(50) notnull default('') 'withdraw_no' comment('提现编号')"`
	OrderSn    int64     `xorm:"notnull default(0) 'order_sn' comment('订单编号')"`
	GoodsId    int       `xorm:"notnull default(0) 'goods_id' comment('商品id')"`
	OrgType    int       `xorm:"notnull default(0) 'org_type' comment('码包所属企业：1北京百林康源 2-深圳利都')"`
	SwlmCpsId  int       `xorm:"notnull default(0) 'swlm_cps_id' comment('扣减佣金记录id(eshop.dis_distributor_swlm_cps.id)')"`
	CreateTime time.Time `xorm:"created 'create_time' comment('创建时间')"`
	UpdateTime time.Time `xorm:"updated 'update_time' comment('更新时间')"`
}

type DisWithdrawOrderExt struct {
	DisWithdrawOrder `xorm:"extends"`
	OrgId            int `json:"org_id"`
	ShopId           int `json:"shop_id"`
	DisId            int `json:"dis_id"`
	DisCommisAmount  int `json:"dis_commis_amount"`
}

func (w *DisWithdrawOrder) TableName() string {
	return "eshop.dis_withdraw_order"
}

type GetWithdrawOrderListReq struct {
	OrgId          int     `json:"org_id"`
	PageIndex      int     `json:"page_index"`
	PageSize       int     `json:"page_size"`
	ShopId         int     `json:"shop_id"`          // 店铺id
	OrderSn        []int64 `json:"order_sn"`         // 包含的订单编号
	ExcludeOrderSn []int64 `json:"exclude_order_sn"` // 排除的订单编号
	MaxOrderId     int     `json:"max_order_id"`     // 最大订单id
}

// GetWithdrawOrderList 获取可提现订单列表 cnt 总数量 amount 总金额
// 排除自平账的订单
// 排除提现申请中和申请通过的订单

func (w *DisWithdrawOrder) GetWithdrawOrderList(session *xorm.Session, in GetWithdrawOrderListReq) (out []vo.WithdrawOrderItem, cnt, amount, maxOrderId int, err error) {
	logPrefix := fmt.Sprintf("获取可提现订单列表-orgId:%d,入参:%s", in.OrgId, utils.JsonEncode(in))
	log.Info(logPrefix)

	selectStr := `
		a.order_id,
		a.order_sn,
		DATE_FORMAT(FROM_UNIXTIME(a.add_time), '%Y-%m-%d %H:%i:%s') as add_time,
		a.org_type,
		b.withdraw_no,
		b.swlm_cps_id,
		c.status,
		b.id as dis_withdraw_order_id,
		og.goods_name,
		og.goods_num,
		og.goods_id,
		ROUND(og.goods_pay_price * og.dis_commis_rate, 0) as amount,
		e.id as dis_id
	`
	data := make([]vo.WithdrawOrderItem, 0)
	session.Table("upetmart.upet_orders").Alias("a").
		Select(selectStr).
		Join("left", "eshop.dis_withdraw_order b", "a.order_sn = b.order_sn").
		Join("left", "eshop.dis_withdraw c", "b.withdraw_no = c.withdraw_no").
		Join("left", "eshop.dis_distributor_swlm_cps d", "a.order_sn = d.order_sn").
		Join("left", "upetmart.upet_order_goods og", "a.order_id = og.order_id").
		Join("left", "eshop.dis_distributor e", "og.dis_member_id = e.member_id").
		// Where("d.is_self_balance != 1 or d.is_self_balance is null").
		// Where("c.status is null or (c.status =3 and b.swlm_cps_id = 0) or (c.status =3 and b.swlm_cps_id>0 and d.is_self_balance != 1)").
		And("a.store_id = ?", in.OrgId).
		And("e.org_id = ?", in.OrgId).
		And("og.shop_id = ?", in.ShopId).
		And("a.order_father > 0").
		And("a.order_sn not in (SELECT dd.`order_sn` FROM eshop.`dis_distributor_swlm_cps` dd WHERE  a.`order_sn` = dd.`order_sn` AND dd.`is_self_balance` = 1)").
		And("a.order_sn not in (select aa.order_sn from eshop.dis_withdraw_order aa join eshop.dis_withdraw bb on aa.withdraw_no = bb.withdraw_no where a.order_sn=aa.order_sn and bb.status!=3  and aa.swlm_cps_id=0  and bb.shop_id = ?)", in.ShopId).
		And("og.goods_pay_price > 0").
		And("og.dis_commis_rate>0").
		OrderBy("a.order_id desc")
	if len(in.ExcludeOrderSn) > 0 {
		session.NotIn("a.order_sn", in.ExcludeOrderSn)
	}
	if in.MaxOrderId > 0 {
		session.And("a.order_id <= ?", in.MaxOrderId)
	}
	if len(in.OrderSn) > 0 {
		session.In("a.order_sn", in.OrderSn)
	}
	if in.PageIndex > 0 {
		session.Limit(in.PageSize, (in.PageIndex-1)*in.PageSize)
	}
	count64, err := session.GroupBy("a.order_sn").
		FindAndCount(&data)
	if err != nil {
		log.Errorf("%s-获取可提现订单列表失败: %s", logPrefix, err.Error())
		return
	}
	cnt = cast.ToInt(count64)
	if len(data) > 0 {
		maxOrderId = data[0].OrderId
	}
	for _, v := range data {
		amount += v.Amount
	}

	return data, cnt, amount, maxOrderId, err
}

// 获取提现成功和提现中的订单
func (w *DisWithdrawOrder) GetSuccessWithdrawOrder(session *xorm.Session, shopId int, orderNoSli []int64) (out []string, err error) {
	out = make([]string, 0)
	if shopId == 0 {
		err = errors.New("店铺id不能为0")
		return
	}
	if len(orderNoSli) == 0 {
		err = errors.New("订单编号不能为空")
		return
	}
	err = session.Table("eshop.dis_withdraw_order").Alias("a").Select("a.order_sn").
		Join("left", "eshop.dis_withdraw b ", "a.withdraw_no = b.withdraw_no").
		Join("left", "upetmart.upet_orders c", "a.order_sn = c.order_sn").
		Join("left", "upetmart.upet_order_goods d", "c.order_id = d.order_id").
		In("b.status", []int{diswithdraw.StatusChecked, diswithdraw.StatusUncheck}).
		Where("d.shop_id=?", shopId).
		In("a.order_sn", orderNoSli).Find(&out)
	return
}

// 根据提现编号获取提现订单列表 连表upetmart.upet_orders、upetmart.upet_order_goods 获取主体id、店铺id、以及分销员id
func (w *DisWithdrawOrder) GetWithdrawOrderListByWithdrawNo(session *xorm.Session, withdrawNo string) (out []DisWithdrawOrderExt, err error) {

	selectStr := `
		a.id,
		a.withdraw_no,
		a.order_sn,
		a.goods_id,
		a.org_type,
		a.swlm_cps_id,
		b.store_id as org_id,
		c.shop_id,
		d.id as dis_id,
		e.dis_commis_amount
	`
	err = session.Table("eshop.dis_withdraw_order").Alias("a").Select(selectStr).
		Join("left", "upetmart.upet_orders b", "a.order_sn = b.order_sn").
		Join("left", "upetmart.upet_order_goods c", "b.order_id = c.order_id").
		Join("left", "eshop.dis_distributor d", "c.dis_member_id = d.member_id and d.org_id = b.store_id").
		Join("left", "eshop.dis_distributor_swlm_cps e", "a.order_sn = e.order_sn").
		Where("withdraw_no=?", withdrawNo).Find(&out)
	return
}

// GetLatestWithdrawOrderByOrderSns 批量获取订单对应的最后一条提现记录
func (w *DisWithdrawOrder) GetLatestWithdrawOrderByOrderSns(session *xorm.Session, orderSns []int64) (out []DisWithdrawOrder, err error) {
	if len(orderSns) == 0 {
		return nil, errors.New("订单号不能为空")
	}

	err = session.Table("upetmart.upet_orders").Alias("a").
		Select("b.*").
		Join("left", "eshop.dis_withdraw_order b", "a.order_sn = b.order_sn").
		Where("a.order_sn in (?)", orderSns).
		And("b.id = (select max(id) from eshop.dis_withdraw_order where order_sn = a.order_sn)").
		Find(&out)
	return
}
