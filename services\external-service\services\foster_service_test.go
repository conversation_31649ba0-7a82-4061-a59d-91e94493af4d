package services_test

import (
	"context"
	"eShop/infra/log"
	"eShop/services/external-service/services"
	vo "eShop/view-model/external-vo/offline"
	"testing"
)

func TestForsterService_ForsterSettle(t *testing.T) {
	tests := []struct {
		name string // description of this test case
		// Named input parameters for target function.
		dto     *vo.FosterPayDTO
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestForsterService_ForsterSettle",
			dto: &vo.FosterPayDTO{
				StoreId:      "577549921650660693",
				CustomerId:   551611098026463381,
				CustomerName: "章思",
				FosterId:     615428251846738945,
				OrderId:      6153898,
				OrderNo:      "9964128298937258",
				PayType:      "MARK_ALIPAY",
				Amount:       212,
				SellerId:     577549921650660700,
				SellerName:   "老板",
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// TODO: construct the receiver type.
			var s services.ForsterService
			gotErr := s.<PERSON>(context.Background(), tt.dto)
			if gotErr != nil {
				if !tt.wantErr {
					t.Errorf("ForsterSettle() failed: %v", gotErr)
				}
				return
			}
			if tt.wantErr {
				t.Fatal("ForsterSettle() succeeded unexpectedly")
			}
		})
	}
}
