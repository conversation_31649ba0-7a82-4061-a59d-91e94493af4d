package product_po

import (
	"eShop/infra/log"
	"errors"
	"fmt"

	"xorm.io/xorm"
)

// 分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类
const (
	CategoryTypeOnline  = 1
	CategoryTypeOffline = 2
	CategoryTypeService = 3
	CategoryTypeLive    = 4
)

type ProCategory struct {
	// 分类id
	Id int `json:"id" xorm:"pk autoincr not null comment('分类id') INT 'id'"`
	// 连锁ID
	ChainId int64 `json:"chain_id" xorm:"not null default 0 comment('连锁ID') BIGINT 'chain_id'"`
	// 分类名称
	Name string `json:"name" xorm:"default 'null' comment('分类名称') VARCHAR(100) 'name'"`
	// 父类id
	ParentId int `json:"parent_id" xorm:"default 'null' comment('父类id') INT 'parent_id'"`
	// 排序
	Sort int `json:"sort" xorm:"default 'null' comment('排序') INT 'sort'"`
	// 图片(三级分类存)
	Img string `json:"img" xorm:"default '' comment('图片(三级分类存)') VARCHAR(100) 'img'"`
	// 添加时间
	CreateDate string `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('添加时间') DATETIME 'create_date' created"`
	// 修改时间
	UpdateDate string `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('修改时间') DATETIME 'update_date' updated"`
	// 创建人ID
	CreatedBy int64 `json:"created_by" xorm:"not null default 0 comment('创建人ID') BIGINT 'created_by'"`
	// 创建人名称
	CreatedName string `json:"created_name" xorm:"not null default '' comment('创建人名称') VARCHAR(100) 'created_name'"`
	// 更新人ID
	UpdatedBy int64 `json:"updated_by" xorm:"not null default 0 comment('更新人ID') BIGINT 'updated_by'"`
	// 更新人名称
	UpdatedName string `json:"updated_name" xorm:"not null default '' comment('更新人名称') VARCHAR(100) 'updated_name'"`
	//分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类
	Type int `json:"type" xorm:"default 1 comment('分类类型：1-门店线上，2-门店线下，3-服务分类，4-活体分类') INT 'type'"`
}

// 分类查询条件
type CategoryMapInfoReq struct {
	// 分类名称列表	
	CategoryNames []string
	// 连锁ID
	ChainId       int64
	// 分类名称
	CategoryName  string
	// 分类类型
	Type          int
	// 分类ID
	Id            *int
	// 不等于的分类ID
	NotEqualId    *int
	// 分类ID列表
	Ids           []int
	// 父分类ID
	ParentId      *int
	// 输出类型 0-切片列表 1-按名称映射 2-按ID映射
	OutType       int
	// 列表按什么排序
	OrderBy       string
	// 排序
	Sort          *int
}

func (p *ProCategory) GetCategoryMapInfo(db *xorm.Engine, where CategoryMapInfoReq) ([]ProCategory, map[string]ProCategory, map[int]ProCategory, error) {
	session := db.NewSession()
	defer session.Close()

	if len(where.CategoryNames) > 0 {
		session = session.In("name", where.CategoryNames)
	}
	if where.ChainId > 0 {
		session = session.Where("chain_id=?", where.ChainId)
	}
	if where.CategoryName != "" {
		session = session.Where("name=?", where.CategoryName)
	}
	if where.Type > 0 {
		session = session.Where("type=?", where.Type)
	}
	if where.Id != nil {
		session = session.Where("id=?", *where.Id)
	}
	if where.NotEqualId != nil {
		session = session.Where("id<>?", *where.NotEqualId)
	}
	if len(where.Ids) > 0 {
		session = session.In("id", where.Ids)
	}
	if where.ParentId != nil {
		session = session.Where("parent_id=?", *where.ParentId)
	}
	if where.Sort != nil {
		session = session.Where("sort=?", *where.Sort)
	}
	orderBy := "sort"
	if where.OrderBy != "" {
		orderBy = where.OrderBy
		session = session.OrderBy(orderBy)
	}

	categories := make([]ProCategory, 0)
	nameMap := make(map[string]ProCategory)
	idMap := make(map[int]ProCategory)

	if err := session.Table("eshop.pro_category").Find(&categories); err != nil {
		return nil, nil, nil, err
	}

	for _, category := range categories {
		switch where.OutType {
		case 1:
			nameMap[category.Name] = category
		case 2:
			idMap[category.Id] = category
		}
	}

	return categories, nameMap, idMap, nil
}

// GetChildrenByParentId 根据父节点获取子节点列表
func (p *ProCategory) GetChildrenByParentId(engine *xorm.Engine, parentId int) (out []int, err error) {
	logPrefix := fmt.Sprintf("根据父分类查询分类列表，参数是：%d ", parentId)

	session := engine.NewSession()
	defer session.Close()

	//取出所有的分类
	if err = session.Table("eshop.pro_category").Select("id").Where("parent_id = ?", parentId).Find(&out); err != nil {
		log.Error(logPrefix, "查询分类列表失败，err=", err.Error())
		err = errors.New("查询分类列表失败")
		return
	}

	return
}

type CateName struct {
	Id         int    `json:"id"`
	ParentName string `json:"parent_name"`
	ChildName  string `json:"child_name"`
}

// GetCategoryNameByChildId 根据子分类id查询出一级分类、二级分类的名称
func (p *ProCategory) GetCategoryNameByChildId(engine *xorm.Engine, childIds []int) (cateNames map[int]*CateName, err error) {
	logPrefix := fmt.Sprintf("根据子分类id查询分类名称，参数是：%v ", childIds)

	session := engine.NewSession()
	defer session.Close()

	var cateNameList []*CateName
	err = session.Table("eshop.pro_category").Alias("pc1").
		Select("pc1.id,pc2.name as parent_name,pc1.name as child_name").
		Join("left", "eshop.pro_category pc2", "pc1.parent_id = pc2.id").
		In("pc1.id", childIds).Find(&cateNameList)
	if err != nil {
		log.Error(logPrefix, "查询分类名称失败，err=", err.Error())
		err = errors.New("查询分类名称失败")
		return
	}

	cateNames = make(map[int]*CateName, 0)
	for _, cateName := range cateNameList {
		cateNames[cateName.Id] = cateName
	}

	return
}
