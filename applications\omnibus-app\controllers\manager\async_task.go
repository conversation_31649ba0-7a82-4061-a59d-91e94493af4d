package manager

import (
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	vo "eShop/view-model/omnibus-vo"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/spf13/cast"
)

// GetAsyncTaskList
// @Summary 异步任务-获取异步任务列表
// @Description
// @Tags 宠物连锁SAAS-管理后台
// @Accept plain
// @Produce plain
// @Param page_index query int false "当前页，默认第1页"
// @Param page_size query int false "每页显示数据条数，默认显示10条"
// @Param sort query string false "排序类型:createTimeDesc：按创建时间顺序倒序；"
// @Param task_status query int false "任务状态:1:未开始;2:进行中;3:已完成；"
// @Param content_str query string true "任务内容:问各个异步任务的对应人"
// @Param promoter query int false "0:自己;1:全部;2:其他;"
// @Param chain_id query int false "连锁ID，9全部"
// @Success 200 {object} vo.TaskAsyncListRes
// @Failure 400 {object} vo.TaskAsyncListRes
// @Router /omnibus-app/manager/async/list [get]
func GetAsyncTaskList(writer http.ResponseWriter, request *http.Request) {
	resp := vo.TaskAsyncListRes{}
	resp.Code = 400

	req, err := utils.Bind[vo.GetTaskListRequest](request)
	if err != nil {
		log.Error("获取任务出错，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(request.Header.Get("org_id"))
		}
		jwtInfo, err := jwtauth.GetOfflineJwtInfo(request)
		if err != nil {
			log.Error("获取登录信息失败: err", err.Error())
			resp.Message = fmt.Sprintf("获取登录信息失败：%s", err.Error())
		}
		req.CreateId = jwtInfo.UserId
		s := common.AsyncCommService{}
		ret, count, err := s.GetAsyncTaskList(req)
		if err != nil {
			log.Error("获取任务出错：err=" + err.Error())
			resp.Message = fmt.Sprintf("获取任务出错：%s", err.Error())
		} else {
			resp.Code = 200
			resp.Total = count
			resp.Data = ret
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// AsyncAdd
// @Summary 异步任务-异步任务添加
// @Description
// @Tags 宠物连锁SAAS-管理后台
// @Accept plain
// @Produce plain
// @Accept plain
// @Produce plain
// @Param task_content formData int true "任务内容:"
// @Param operation_file_url formData string true "操作文件路径"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/async/add [POST]
func AsyncAdd(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[vo.TaskListAsync](request)
	if err != nil {
		log.Error("创建任务，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("创建异步任务失败，参数解析失败：%s", err.Error())
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(request.Header.Get("org_id"))
		}
		s := common.AsyncCommService{}
		err := s.CreatSyncTask(request, req)
		if err != nil {
			log.Error("创建任务：err=" + err.Error())
			resp.Message = fmt.Sprintf("创建任务：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// TaskImport
// @Summary 导入导出异步任务添加
// @Description
// @Tags 宠物连锁SAAS-管理后台
// @Accept plain
// @Produce plain
// @Accept plain
// @Produce plain
// @Param task_content formData int true "任务内容: 17-saas门店商品导出， 18 -saas连锁商品导出， 19-saas连锁商品导入 20-批量上架导入 21-批量下架导入 22-批量删除导入 23-批量铺品 24-批量调价导入 26-导入服务 27-导入活体 28-库存库位导入"
// @Param operation_file_url formData string true "操作文件路径"
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /omnibus-app/manager/async/export_import/add [POST]
func TaskImport(writer http.ResponseWriter, request *http.Request) {
	resp := viewmodel.BaseHttpResponse{}
	resp.Code = 400

	req, err := utils.Bind[distribution_vo.TaskList](request)
	if err != nil {
		log.Error("创建任务，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("分页查询组织操作，参数解析失败：%s", err.Error())
	} else {
		if req.OrgId == 0 {
			req.OrgId = cast.ToInt(request.Header.Get("org_id"))
		}
		s := common.TaskListService{}
		req.OperationType = 1
		err := s.CreatTask(request, req)
		if err != nil {
			log.Error("创建任务：err=" + err.Error())
			resp.Message = fmt.Sprintf("创建任务：%s", err.Error())
		} else {
			resp.Code = 200
		}
	}

	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
