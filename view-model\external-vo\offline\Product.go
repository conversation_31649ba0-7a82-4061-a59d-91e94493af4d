package offline

type ProductSaveReq struct {
	Id           int64  `json:"id"`
	BrandId      int64  `json:"brandId"`
	BrandName    string `json:"brandName"`
	CategoryId   int64  `json:"categoryId"`
	Img          string `json:"img"`
	IsStandard   bool   `json:"isStandard"`
	ProductName  string `json:"productName"`
	ProductType  string `json:"productType"`
	SkuSaveVO    SkuReq `json:"skuSaveVO"`
	StoreUnit    string `json:"storeUnit"`
	StoreUnitKey string `json:"storeUnitKey"`
	SupplierId   int64  `json:"supplierId"`
	SupplierName string `json:"supplierName"`
}

// 线下门店端 新增商品请求结构
type SkuReq struct {
	Id              int64   `json:"id"`
	BarCode         string  `json:"barCode"`
	BasicPrice      float64 `json:"basicPrice"`
	BirthDate       string  `json:"birthDate"`
	CategoryId      int64   `json:"categoryId"`
	MarketPrice     float64 `json:"marketPrice"`
	SellPrice       float64 `json:"sellPrice"`
	ServeTimeLength int     `json:"serveTimeLength"`
	SkuInventoryNum int     `json:"skuInventoryNum"`
	ProductSpecs    string  `json:"productSpecs"`
}

type AddProductResp struct {
	Code      int            `json:"code"`
	Data      AddProductData `json:"data"`
	ErrorMsg  string         `json:"errorMsg"`
	Extra     interface{}    `json:"extra"`
	IsSuccess bool           `json:"isSuccess"`
	Msg       string         `json:"msg"`
	Path      string         `json:"path"`
	Timestamp string         `json:"timestamp"`
}

type AddProductData struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
type EditProductResp struct {
	Code      int         `json:"code"`
	Data      bool        `json:"data"`
	ErrorMsg  string      `json:"errorMsg"`
	Extra     interface{} `json:"extra"`
	IsSuccess bool        `json:"isSuccess"`
	Msg       string      `json:"msg"`
	Path      string      `json:"path"`
	Timestamp string      `json:"timestamp"`
}
type ProductUpdateStatusByIdResp struct {
	Code      int         `json:"code"`
	Data      bool        `json:"data"`
	ErrorMsg  string      `json:"errorMsg"`
	Extra     interface{} `json:"extra"`
	IsSuccess bool        `json:"isSuccess"`
	Msg       string      `json:"msg"`
	Path      string      `json:"path"`
	Timestamp string      `json:"timestamp"`
}

// 线下门店端商品结构体
type Product struct {
	BarCode          string  `json:"barCode"`
	BrandId          int64   `json:"brandId"`
	BrandName        string  `json:"brandName"`
	CategoryId       int64   `json:"categoryId"`
	CategoryName     string  `json:"categoryName"`
	ChainId          int64   `json:"chainId"`
	CreatedBy        int64   `json:"createdBy"`
	CreatedTime      string  `json:"createdTime"`
	Id               int64   `json:"id"`
	Img              string  `json:"img"`
	MaxSkuId         int64   `json:"maxSkuId"`
	MaxSkuSellPrice  float64 `json:"maxSkuSellPrice"`
	MinSkuId         int64   `json:"minSkuId"`
	MinSkuSellPrice  float64 `json:"minSkuSellPrice"`
	NamePath         string  `json:"namePath"`
	PetKind          string  `json:"petKind"`
	PetKindId        string  `json:"petKindId"`
	PetType          string  `json:"petType"`
	PetTypeId        string  `json:"petTypeId"`
	ProductName      string  `json:"productName"`
	ProductSpecsType string  `json:"productSpecsType"`
	ProductType      string  `json:"productType"`
	SkuId            int64   `json:"skuId"`
	SkuResultVO      SKU     `json:"skuResultVO"`
	SpuInventoryNum  int64   `json:"spuInventoryNum"`
	Status           string  `json:"status"`
	StatusName       string  `json:"statusName"`
	StoreUnit        string  `json:"storeUnit"`
	StoreUnitKey     string  `json:"storeUnitKey"`
	SupplierId       int64   `json:"supplierId"`
	SupplierName     string  `json:"supplierName"`
	TenantId         int64   `json:"tenantId"`
	UpdatedBy        int64   `json:"updatedBy"`
	UpdatedTime      string  `json:"updatedTime"`
}
type SKU struct {
	BarCode          string  `json:"barCode"`
	BasicPrice       float64 `json:"basicPrice"`
	BirthDate        string  `json:"birthDate"`
	CanSellNum       int64   `json:"canSellNum"`
	CategoryId       int64   `json:"categoryId"`
	CreatedBy        int64   `json:"createdBy"`
	CreatedTime      string  `json:"createdTime"`
	Id               int64   `json:"id"`
	IsDiscount       bool    `json:"isDiscount"`
	MarketPrice      float64 `json:"marketPrice"`
	ProductId        int64   `json:"productId"`
	ProductSpecs     string  `json:"productSpecs"`
	ProductSpecsType string  `json:"productSpecsType"`
	SellPrice        float64 `json:"sellPrice"`
	ServeTimeLength  int64   `json:"serveTimeLength"`
	SkuCode          string  `json:"skuCode"`
	SortValue        int64   `json:"sortValue"`
	Status           string  `json:"status"`
	UpdatedBy        int64   `json:"updatedBy"`
	UpdatedTime      string  `json:"updatedTime"`
	Weight           int64   `json:"weight"`
}

type ProductDetailResp struct {
	Code     int     `json:"code"`
	Data     Product `json:"data"`
	ErrorMsg string  `json:"errorMsg"`
	Extra    struct {
	} `json:"extra"`
	IsSuccess bool   `json:"isSuccess"`
	Msg       string `json:"msg"`
	Path      string `json:"path"`
	Timestamp string `json:"timestamp"`
}

type SkuEditReq struct {
	BarCode    string  `json:"barCode"`
	BasicPrice float64 `json:"basicPrice"`
	CategoryId int64   `json:"categoryId"`
	Id         int64   `json:"id"`
	SellPrice  float64 `json:"sellPrice"`
	//SkuInventoryNum int64   `json:"skuInventoryNum"`
	ProductSpecs string `json:"productSpecs"`
}
type ProductEditReq struct {
	BrandId      int64      `json:"brandId"`
	BrandName    string     `json:"brandName"`
	CategoryId   int64      `json:"categoryId"`
	Id           int64      `json:"id"`
	Img          string     `json:"img"`
	ProductName  string     `json:"productName"`
	SkuUpdateVO  SkuEditReq `json:"skuUpdateVO"`
	StoreUnit    string     `json:"storeUnit"`
	StoreUnitKey string     `json:"storeUnitKey"`
	SupplierId   int64      `json:"supplierId"`
	SupplierName string     `json:"supplierName"`
}

type ProductUpdateStatusByIdReq struct {
	Id     int64  `json:"id"`     //主键ID
	Status string `json:"status"` //状态 ，OK正常， STOP停用，DEL删除

}

type DelByIdListReq struct {
	Ids []int `json:"ids"`
}

type DelByIdListResp struct {
	Code     int    `json:"code"`
	Data     bool   `json:"data"`
	ErrorMsg string `json:"errorMsg"`
	Extra    struct {
	} `json:"extra"`
	IsSuccess bool   `json:"isSuccess"`
	Msg       string `json:"msg"`
	Path      string `json:"path"`
	Timestamp string `json:"timestamp"`
}
