package distribution_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	vo "eShop/view-model/distribution-vo"
	"time"

	"github.com/spf13/cast"

	"xorm.io/xorm"
)

type StatsShopDistributorDaily struct {
	Id                     int       `json:"id" xorm:"pk autoincr not null comment('主键') BIGINT 'id'"`
	StatDate               string    `json:"stat_date" xorm:"not null comment('开始日期') DATE 'stat_date'"`
	EndDate                string    `json:"end_date" xorm:"not null comment('结束日期') DATE 'end_date'"`
	EnterpriseId           int64     `json:"enterprise_id" xorm:"not null comment('企业id') bigint 'enterprise_id'"`
	EnterpriseName         string    `json:"enterprise_name" xorm:"not null comment('企业名称') VARCHAR(100) 'enterprise_name'"`
	ShopId                 int       `json:"shop_id" xorm:"not null comment('分销店铺ID') INT 'shop_id'"`
	DisId                  int       `json:"dis_id" xorm:"not null default 0 comment('分销员ID') INT 'dis_id'"`
	ShopName               string    `json:"shop_name" xorm:"not null comment('分销店铺名称') VARCHAR(100) 'shop_name'"`
	Type                   int       `json:"type" xorm:"not null default 1 comment('类型（1:商品订单，2:商品分销订单，3:保险订单，4:保险分销订单）') TINYINT 'type'"`
	IsDis                  int       `json:"is_dis" xorm:"not null default 0 comment('是否分销') TINYINT(1) 'is_dis'"`
	OrderCustomerCount     int       `json:"order_customer_count" xorm:"not null default 0 comment('累计下单客户数') INT 'order_customer_count'"`
	OrderCount             int       `json:"order_count" xorm:"not null default 0 comment('累计下单单数') INT 'order_count'"`
	OrderAmount            int       `json:"order_amount" xorm:"not null default 0 comment('累计下单金额(分)') INT 'order_amount'"`
	OrderProductCount      int       `json:"order_product_count" xorm:"not null default 0 comment('累计下单商品数') INT 'order_product_count'"`
	OrderCustomerPrice     int       `json:"order_customer_price" xorm:"not null default 0 comment('累计下单客单价(分)') INT 'order_customer_price'"`
	TransCustomerCount     int       `json:"trans_customer_count" xorm:"not null default 0 comment('累计成交客户数') INT 'trans_customer_count'"`
	TransCount             int       `json:"trans_count" xorm:"not null default 0 comment('累计成交单数') INT 'trans_count'"`
	TransAmount            int       `json:"trans_amount" xorm:"not null default 0 comment('累计成交金额(分)') INT 'trans_amount'"`
	TransProductCount      int       `json:"trans_product_count" xorm:"not null default 0 comment('累计成交商品数') INT 'trans_product_count'"`
	TransCustomerPrice     int       `json:"trans_customer_price" xorm:"not null default 0 comment('累计成交客单价(分)') INT 'trans_customer_price'"`
	CancelCount            int       `json:"cancel_count" xorm:"not null default 0 comment('累计取消单数') INT 'cancel_count'"`
	CancelAmount           int       `json:"cancel_amount" xorm:"not null default 0 comment('累计取消金额(分)') INT 'cancel_amount'"`
	CancelProductCount     int       `json:"cancel_product_count" xorm:"not null default 0 comment('累计取消商品数') INT 'cancel_product_count'"`
	RefundCount            int       `json:"refund_count" xorm:"not null default 0 comment('累计退款单数') INT 'refund_count'"`
	RefundAmount           int       `json:"refund_amount" xorm:"not null default 0 comment('累计退款金额(分)') INT 'refund_amount'"`
	RefundProductCount     int       `json:"refund_product_count" xorm:"not null default 0 comment('累计退款商品数') INT 'refund_product_count'"`
	PosterScanCount        int       `json:"poster_scan_count" xorm:"not null default 0 comment('分销海报扫码数') INT 'poster_scan_count'"`
	PosterTransCount       int       `json:"poster_trans_count" xorm:"not null default 0 comment('分销海报成交单数') INT 'poster_trans_count'"`
	PosterTransAmount      int       `json:"poster_trans_amount" xorm:"not null default 0 comment('分销海报成交金额(分)') INT 'poster_trans_amount'"`
	LinkClickCount         int       `json:"link_click_count" xorm:"not null default 0 comment('分销链接点击数') INT 'link_click_count'"`
	LinkTransCount         int       `json:"link_trans_count" xorm:"not null default 0 comment('分销链接成交单数') INT 'link_trans_count'"`
	LinkTransAmount        int       `json:"link_trans_amount" xorm:"not null default 0 comment('分销链接成交金额(分)') INT 'link_trans_amount'"`
	FanRelationTransCount  int       `json:"fan_relation_trans_count" xorm:"not null default 0 comment('粉丝关系成交单数') INT 'fan_relation_trans_count'"`
	FanRelationTransAmount int       `json:"fan_relation_trans_amount" xorm:"not null default 0 comment('粉丝关系成交金额(分)') INT 'fan_relation_trans_amount'"`
	Commission             int       `json:"commission" xorm:"not null default 0 comment('累计分销佣金(分)') INT 'commission'"`
	UnsettledCommission    int       `json:"unsettled_commission" xorm:"not null default 0 comment('未结佣金(分)') INT 'unsettled_commission'"`
	SettledCommission      int       `json:"settled_commission" xorm:"not null default 0 comment('已结佣金(分)') INT 'settled_commission'"`
	CreateTime             time.Time `json:"create_time" xorm:"default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'  created"`
	UpdateTime             time.Time `json:"update_time" xorm:"default 'CURRENT_TIMESTAMP' comment('更新时间') DATETIME 'update_time' updated"`
}

func GetShopDistributorData(db *xorm.Engine, where map[string]interface{}) (data StatsShopDistributorDaily, err error) {
	session := db.NewSession()
	defer session.Close()
	s := `sum(order_customer_count) as order_customer_count,
				sum(order_count) as order_count,
				sum(order_amount) as order_amount,
				sum(order_product_count) as order_product_count,
				sum(order_customer_price) as order_customer_price,
				sum(trans_customer_count) as trans_customer_count,
				sum(trans_count) as  trans_count,
				sum(trans_amount) as trans_amount,
				sum(trans_product_count) as trans_product_count ,
				sum(trans_customer_price) as trans_customer_price,
				sum(cancel_count) as cancel_count,
				sum(cancel_amount) as cancel_amount,
				sum(cancel_product_count) as cancel_product_count ,
				sum(refund_count) as refund_count,
				sum(refund_amount) as refund_amount,
				sum(refund_product_count) as refund_product_count,
				sum(poster_scan_count) as poster_scan_count,
				sum(poster_trans_count) as poster_trans_count,
				sum(poster_trans_amount) as  poster_trans_amount,
				sum(link_click_count) as link_click_count,
				sum(link_trans_count) as link_trans_count,
				sum(link_trans_amount) as link_trans_amount,
				sum(fan_relation_trans_count) as fan_relation_trans_count,
				sum(fan_relation_trans_amount) as fan_relation_trans_amount,
				sum(commission) as  commission,
				sum(unsettled_commission) as unsettled_commission,
				sum(settled_commission) as settled_commission`

	session = session.Table("eshop.stats_shop_distributor_daily").Select(s)
	statDateS := where["statDateStart"]
	statDateE, ok := where["statDateEnd"]
	if ok {
		//把statDateE的类型转化成时间类型
		t, _ := time.ParseInLocation(time.DateTime, statDateE.(string)+" 23:59:59", time.Local)
		//判断是否大于当前时间
		if t.After(time.Now()) {
			session = session.Where("stat_date >= ?", statDateS)
			session = session.Where("end_date<=?", statDateE)
			session = session.Where("stat_date=end_date") //排除周 、月、年， 只是 日 数据求和
		} else {
			session = session.Where("stat_date=?", statDateS)
			session = session.Where("end_date=?", statDateE)
		}
	} else {
		session = session.Where("stat_date=end_date")
	}

	shopId, shopIdOk := where["shopId"]
	if shopIdOk {
		session = session.Where("shop_id=?", shopId)
	}

	types, ok := where["types"]
	if ok {
		session = session.In("type", types)
	}

	disId, disIdOk := where["disId"]
	if disIdOk {
		session = session.Where("dis_id=?", disId)
	}

	_, err = session.Table("eshop.stats_shop_distributor_daily").Get(&data)
	if err != nil {
		return
	}

	//累计客户数统计
	if statDateE == nil {
		var (
			OrderCustomerCount  int
			TransCustomerCount  int
			OrderCustomerCount1 int
			TransCustomerCount1 int
		)

		session2 := db.Table("upetmart.upet_orders").Alias("o").
			Join("left", "upetmart.upet_order_goods b", "o.order_id=b.order_id").
			Select(`COUNT(DISTINCT o.buyer_id) AS order_customer_count,COUNT(DISTINCT CASE WHEN o.payment_time > 0 THEN o.buyer_id END) AS trans_customer_count`).
			Where("o.store_id = 3 and o.order_father =0")

		if statDateS != nil && statDateE != nil {
			startStamp := utils.Date2Timestamp(cast.ToString(statDateS))
			endStamp := utils.AddDate2Timestamp(cast.ToString(statDateE))
			session2 = session2.And("o.add_time between ? and ?", startStamp, endStamp)
		}
		_, ok = where["isDis"]
		if ok {
			session2 = session2.And("o.is_dis=1")
		}
		disMemberId, ok := where["disMemberId"]
		if ok {
			session2 = session2.And("b.dis_member_id=?", disMemberId)
		}

		if shopIdOk {
			session2 = session2.Where("b.shop_id=?", shopId)
		}
		_, err = session2.Get(&OrderCustomerCount, &TransCustomerCount)
		if err != nil {
			log.Errorf("GetShopDistributorDataList 订单客户数 error: %v", err)
			return
		}

		session3 := db.Table("insurance_business.pi_order_info").Alias("oi").
			Select(`COUNT(DISTINCT oi.user_id) AS order_customer_count,
             COUNT(DISTINCT CASE WHEN oi.pay_status =2 and oi.pay_time > 0 THEN oi.user_id END) AS trans_customer_count`)
		if statDateS != nil && statDateE != nil {
			session3 = session3.Where("DATE(create_time)  >= ? and DATE(create_time) <= ?", statDateS, statDateE)
		}
		_, ok = where["isDis"]
		if ok {
			session3 = session3.Where("oi.dis_id > 0")
		}

		if disIdOk {
			session3 = session3.Where("oi.dis_id=?", disId)
		}

		if shopIdOk {
			session3 = session3.Where("oi.shop_id=?", shopId)
		}
		_, err = session3.Get(&OrderCustomerCount1, &TransCustomerCount1)
		if err != nil {
			log.Errorf("GetShopDistributorDataList 保险客户数 error: %v", err)
			return
		}
		tv, ok := where["type"]
		if ok {
			switch tv {
			case 1: //商品订单累计客户数
				data.OrderCustomerCount = OrderCustomerCount
				data.TransCustomerCount = TransCustomerCount
			case 2: //保险订单累计客户数
				data.OrderCustomerCount = OrderCustomerCount1
				data.TransCustomerCount = TransCustomerCount1
			}
		} else {
			data.OrderCustomerCount = OrderCustomerCount + OrderCustomerCount1
			data.TransCustomerCount = TransCustomerCount + TransCustomerCount1
		}
		//计算累计客单价
		if data.OrderCustomerCount > 0 {
			data.OrderCustomerPrice = data.OrderAmount / data.OrderCustomerCount
		}
		if data.TransCustomerCount > 0 {
			data.TransCustomerPrice = data.TransAmount / data.TransCustomerCount
		}
	}

	return
}

func GetShopDistributorDataList(db *xorm.Engine, where map[string]interface{}) (data []vo.DisCenterDistributor, total int, err error) {
	session := db.NewSession()
	defer session.Close()

	data = make([]vo.DisCenterDistributor, 0)
	s := `	b.name as dis_name,
			b.member_id as dis_member_id,
			b.dis_role,
			b.status,
			b.total_customer,
			a.shop_id,
			a.dis_id,
			a.type,
			sum(a.trans_customer_count) as trans_customer_count,
			sum(a.trans_count) as trans_count,
			sum(a.trans_amount) as trans_amount,
			sum(a.commission) as commission
			`
	statDateS := where["startDate"]
	statDateE, ok := where["endDate"]
	if ok {
		//把statDateE的类型转化成时间类型
		t, _ := time.ParseInLocation(time.DateTime, statDateE.(string)+" 23:59:59", time.Local)
		//判断是否大于当前时间
		if t.After(time.Now()) {
			session = session.Where("stat_date >= ?", statDateS)
			session = session.Where("end_date<=?", statDateE)
			session = session.Where("stat_date=end_date") //排除周 、月、年， 只是 日 数据求和
		} else {
			session = session.Where("stat_date=?", statDateS)
			session = session.Where("end_date=?", statDateE)
		}
	} else {
		session = session.Where("stat_date=end_date")
	}

	shopId, ok := where["shopId"]
	if ok {
		session = session.Where("a.shop_id=?", shopId)
	}
	types, ok := where["types"]
	if ok {
		session = session.In("a.type", types)
	}
	disIds, ok := where["disIds"]
	if ok {
		session = session.In("a.dis_id", disIds)
	}

	pageSize, ok1 := where["pageSize"]
	pageIndex, ok2 := where["pageIndex"]
	if ok1 && ok2 {
		ps := pageSize.(int)
		pi := pageIndex.(int)
		session = session.Limit((ps), int(ps*(pi-1)))
	}
	disName, ok := where["disName"]
	if ok {
		session = session.Where("b.name like ?", "%"+disName.(string)+"%")
	}
	orderBy, ok := where["orderBy"]
	if ok {
		session = session.OrderBy(orderBy)
	}
	groupBy, ok := where["groupBy"]
	if ok {
		session = session.GroupBy(groupBy.(string))
	}

	t, err := session.Table("eshop.stats_shop_distributor_daily").Alias("a").Select(s).
		Join("left", "eshop.dis_distributor b", "a.dis_id=b.id").FindAndCount(&data)
	total = int(t)
	if err != nil {
		return
	}
	return
}

func GetShopDistributorList(db *xorm.Engine, where map[string]interface{}) (data []StatsShopDistributorDaily, err error) {
	session := db.NewSession()
	defer session.Close()
	data = make([]StatsShopDistributorDaily, 0)
	s := "stat_date,sum(trans_count) as trans_count,sum(trans_amount) as trans_amount,sum(commission) as commission,sum(trans_customer_count) as trans_customer_count"

	session = session.Table("eshop.stats_shop_distributor_daily").Select(s)

	statDateS, ok := where["statDateStart"]
	if ok {
		session = session.Where("stat_date=?", statDateS)
	}
	statDateE, ok := where["statDateEnd"]
	if ok {
		session = session.Where("end_date=?", statDateE)
	}
	shopId, ok := where["shopId"]
	if ok {
		session = session.Where("shop_id=?", shopId)
	}
	types, ok := where["types"]
	if ok {
		session = session.In("type", types)
	}
	disId, ok := where["disId"]
	if ok {
		session = session.Where("dis_id=?", disId)
	}

	groupBy, ok := where["groupBy"]
	if ok {
		groupByStr := groupBy.(string)
		session = session.GroupBy(groupByStr)
	}
	err = session.Find(&data)
	return
}
