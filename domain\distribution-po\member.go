package distribution_po

type Member struct {
	MemberId              int64   `xorm:"pk autoincr 'member_id'" json:"member_id"`               // 会员ID
	ScrmUserId            string  `xorm:"'scrm_user_id'" json:"scrm_user_id"`                     // SCRM的用户user_id
	MemberMobile          string  `xorm:"member_mobile" json:"member_mobile"`                     // 手机号
	DistriState           int     `xorm:"distri_state" json:"distri_state"`                       // 分销状态 0未申请 1待审核 2已通过 3未通过 4清退 5退出 6撤销
	DistriChainid         int     `xorm:"distri_chainid" json:"distri_chainid"`                   // 分销门店ID:1=总部职能
	MemberName            string  `xorm:"member_name" json:"member_name"`                         // 会员名称
	MemberTruename        string  `xorm:"member_truename" json:"member_truename"`                 // 真实姓名
	MemberAvatar          string  `xorm:"member_avatar" json:"member_avatar"`                     // 会员头像
	MemberWxavatar        string  `xorm:"member_wxavatar" json:"member_wxavatar"`                 // 微信头像
	MemberSex             int     `xorm:"member_sex" json:"member_sex"`                           // 会员性别
	MemberBirthday        string  `xorm:"member_birthday" json:"member_birthday"`                 // 生日
	MemberPasswd          string  `xorm:"member_passwd" json:"member_passwd"`                     // 会员密码
	MemberEmail           string  `xorm:"member_email" json:"member_email"`                       // 邮箱
	MemberQq              string  `xorm:"member_qq" json:"member_qq"`                             // QQ
	MemberLoginNum        int     `xorm:"member_login_num" json:"member_login_num"`               // 登录次数
	MemberTime            int64   `xorm:"member_time" json:"member_time"`                         // 注册时间
	MemberLoginTime       int64   `xorm:"member_login_time" json:"member_login_time"`             // 当前登录时间
	MemberOldLoginTime    int64   `xorm:"member_old_login_time" json:"member_old_login_time"`     // 上次登录时间
	MemberLoginIp         string  `xorm:"member_login_ip" json:"member_login_ip"`                 // 当前登录IP
	MemberOldLoginIp      string  `xorm:"member_old_login_ip" json:"member_old_login_ip"`         // 上次登录IP
	MemberMobileBind      int     `xorm:"member_mobile_bind" json:"member_mobile_bind"`           // 手机是否验证 0未验证 1已验证
	MemberEmailBind       int     `xorm:"member_email_bind" json:"member_email_bind"`             // 邮箱是否验证 0未验证 1已验证
	MemberState           int     `xorm:"member_state" json:"member_state"`                       // 会员状态 0正常 1待审核 2锁定
	MemberPoints          int     `xorm:"member_points" json:"member_points"`                     // 会员积分
	DistriBrandid         int     `xorm:"distri_brandid" json:"distri_brandid"`                   // 品牌ID
	DistriTime            int     `xorm:"distri_time" json:"distri_time"`                         // 申请时间
	DistriHandleTime      int     `xorm:"distri_handle_time" json:"distri_handle_time"`           // 处理时间
	DistriShow            int     `xorm:"distri_show" json:"distri_show"`                         // 分销中心是否显示 0不显示 1显示
	QuitTime              int     `xorm:"quit_time" json:"quit_time"`                             // 退出时间
	DistriApplyTimes      int     `xorm:"distri_apply_times" json:"distri_apply_times"`           // 申请次数
	DistriQuitTimes       int     `xorm:"distri_quit_times" json:"distri_quit_times"`             // 退出次数
	MemberSnsvisitnum     int     `xorm:"member_snsvisitnum" json:"member_snsvisitnum"`           // 消息订阅次数
	WeixinMpOpenid        string  `xorm:"weixin_mp_openid" json:"weixin_mp_openid"`               // 微信公众号OpenID
	IsCash                int     `xorm:"is_cash" json:"is_cash"`                                 // 是否允许提现，0否，1是
	IdCardName            string  `xorm:"id_card_name" json:"id_card_name"`                       // 实名认证姓名
	IdCardCode            string  `xorm:"id_card_code" json:"id_card_code"`                       // 身份证号
	IdCardBind            int     `xorm:"id_card_bind" json:"id_card_bind"`                       // 是否实名认证0否,1是
	IdCardState           int     `xorm:"id_card_state" json:"id_card_state"`                     // 审核状态0未申请，1待审核，2审核成功，3审核失败
	IdCardExplain         string  `xorm:"id_card_explain" json:"id_card_explain"`                 // 审核说明
	IdCardImg             string  `xorm:"id_card_img" json:"id_card_img"`                         // 身份证正反面图片
	WeixinMiniOpenid      string  `xorm:"weixin_mini_openid" json:"weixin_mini_openid"`           // 小程序openid(阿闻宠物-北京那边用)
	WeixinMiniAddtime     int     `xorm:"weixin_mini_addtime" json:"weixin_mini_addtime"`         // 小程序绑定时间(阿闻宠物-北京那边用)
	WeixinMiniOpenidshop  string  `xorm:"weixin_mini_openidshop" json:"weixin_mini_openidshop"`   // 小程序openid(阿闻智慧门店-自用)
	WeixinMiniAddtimeshop int     `xorm:"weixin_mini_addtimeshop" json:"weixin_mini_addtimeshop"` // 小程序绑定时间(阿闻智慧门店-自用)
	WeixinMiniOpenidasq   string  `xorm:"weixin_mini_openidasq" json:"weixin_mini_openidasq"`     // 小程序openid(阿闻爱省钱-自用)
	WeixinMiniAddtimeasq  int     `xorm:"weixin_mini_addtimeasq" json:"weixin_mini_addtimeasq"`   // 小程序绑定时间(阿闻爱省钱-自用)
	WeixinMiniOpenidmall  string  `xorm:"weixin_mini_openidmall" json:"weixin_mini_openidmall"`   // 小程序openid(阿闻商城-自用)
	WeixinMiniAddtimemall int     `xorm:"weixin_mini_addtimemall" json:"weixin_mini_addtimemall"` // 小程序绑定时间(阿闻商城-自用)
	EarnestMoney          float64 `xorm:"earnest_money" json:"earnest_money"`                     // 保证金金额
	GevalCommentStatus    int     `xorm:"geval_comment_status" json:"geval_comment_status"`       // 0为电商 1为采集 2为宠医云 3阿闻智慧医院 4阿闻小程序 5阿闻爱省钱 6阿闻商城 7数据中心 8佳雯会员
	DisTradMoney          float64 `xorm:"dis_trad_money" json:"dis_trad_money"`                   // 累计收益
	BillBankBranch        string  `xorm:"bill_bank_branch" json:"bill_bank_branch"`               // 开户银行支行名称
	MemberIsvip           int     `xorm:"member_isvip" json:"member_isvip"`                       // 0.默认1.198会员
	MemberIsbzk           int     `xorm:"member_isbzk" json:"member_isbzk"`                       // 0.默认1.保障卡
	MemberVipstime        int     `xorm:"member_vipstime" json:"member_vipstime"`                 // 会员开始时间
	MemberVipetime        int     `xorm:"member_vipetime" json:"member_vipetime"`                 // 会员过期时间
	MemberBzkstime        int     `xorm:"member_bzkstime" json:"member_bzkstime"`                 // 保障卡开始时间
	MemberBzketime        int     `xorm:"member_bzketime" json:"member_bzketime"`                 // 保障卡结束时间
	UserLevelId           int     `xorm:"user_level_id" json:"user_level_id"`                     // 会员等级，来源datacenter.user_level表
	UserLevelStime        int     `xorm:"user_level_stime" json:"user_level_stime"`               // 会员等级开始时间
	UserLevelEtime        int     `xorm:"user_level_etime" json:"user_level_etime"`               // 会员等级过期时间
	MemberIdentity        string  `xorm:"member_identity" json:"member_identity"`                 // 分销员身份证
	MemberMobileBefore    string  `xorm:"member_mobile_before" json:"member_mobile_before"`       // 修改前手机号
	MemberVipstarttime    int     `xorm:"member_vipstarttime" json:"member_vipstarttime"`         // 电商198会员开始时间
	MemberVipendtime      int     `xorm:"member_vipendtime" json:"member_vipendtime"`             // 电商198会员结束时间
	NewcomerTag           int     `xorm:"newcomer_tag" json:"newcomer_tag"`                       // 新用户标记，null未更新、1=新人、2，有可能成为新人、3=老用户
	VipCardState          int     `xorm:"vip_card_state" json:"vip_card_state"`                   // 付费会员:0-否，1-是
	WeixinMiniOpenid2     string  `xorm:"weixin_mini_openid2" json:"weixin_mini_openid2"`         // 极宠家微信小程序openid
	MemberWw              string  `xorm:"member_ww" json:"member_ww"`                             // 阿里旺旺
	MemberQqopenid        string  `xorm:"member_qqopenid" json:"member_qqopenid"`                 // qq互联id
	MemberQqinfo          string  `xorm:"member_qqinfo" json:"member_qqinfo"`                     // qq账号相关信息
	MemberSinaopenid      string  `xorm:"member_sinaopenid" json:"member_sinaopenid"`             // 新浪微博登录id
	MemberSinainfo        string  `xorm:"member_sinainfo" json:"member_sinainfo"`                 // 新浪账号相关信息序列化值
	WeixinUnionid         string  `xorm:"weixin_unionid" json:"weixin_unionid"`                   // 微信用户统一标识
	WeixinInfo            string  `xorm:"weixin_info" json:"weixin_info"`                         // 微信用户相关信息
	AvailablePredeposit   float64 `xorm:"available_predeposit" json:"available_predeposit"`       // 预存款可用金额
	FreezePredeposit      float64 `xorm:"freeze_predeposit" json:"freeze_predeposit"`             // 预存款冻结金额
	AvailableRcBalance    float64 `xorm:"available_rc_balance" json:"available_rc_balance"`       // 可用充值卡余额
	FreezeRcBalance       float64 `xorm:"freeze_rc_balance" json:"freeze_rc_balance"`             // 冻结充值卡余额
}
