package distribution_vo

// GetWithdrawOrderListReq 获取提现订单列表请求
type GetWithdrawOrderListReq struct {
	PageIndex  int    `json:"page_index" form:"page_index"` // 页码
	PageSize   int    `json:"page_size" form:"page_size"`   // 每页数量
	OrgId      int    `json:"org_id"`                       // 主体ID
	MemberId   int    `json:"member_id"`                    // 分销员ID
	ScrmUserId string `json:"scrm_user_id"`                 // 分销员SCRM用户ID
}

// WithdrawOrderItem 提现订单项
type WithdrawOrderItem struct {
	// 订单id
	OrderId int `json:"order_id"` // 订单id
	// 订单编号
	OrderSn string `json:"order_sn"` // 订单编号
	// 提现编号
	WithdrawNo string `json:"withdraw_no"` // 提现编号
	// 状态
	Status int `json:"status"` // 状态
	// 扣减佣金记录id
	SwlmCpsId int `json:"swlm_cps_id"` // 扣减佣金记录id
	// 提现订单id
	DisWithdrawOrderId int `json:"dis_withdraw_order_id"` // 提现订单id
	// 商品名称
	GoodsName string `json:"goods_name"` // 商品名称
	// 商品数量
	GoodsNum int `json:"goods_num"` // 商品数量
	// 下单时间
	AddTime string `json:"add_time"` // 下单时间
	// 订单金额(单位分)
	Amount int `json:"amount"` // 订单金额(单位分)
	// 码包所属企业
	OrgType int `json:"org_type"` // 码包所属企业
	// 商品id
	GoodsId int `json:"goods_id"` // 商品id
	// 分销员id
	DisId int `json:"dis_id"` // 分销员id
}

// GetWithdrawOrderListRes 获取提现订单列表响应
type GetWithdrawOrderListRes struct {
	// 响应码
	Code       int                 `json:"code"`         // 响应码
	Message    string              `json:"message"`      // 响应信息
	Data       []WithdrawOrderItem `json:"data"`         // 订单列表
	Total      int                 `json:"total"`        // 总数量
	Amount     int                 `json:"amount"`       // 总金额
	MaxOrderId int                 `json:"max_order_id"` // 最大订单id

}

// GetRefundOrderListReq 获取退款单列表请求
type GetRefundOrderListReq struct {
	OrgId      int    `json:"org_id"`       // 主体ID
	MemberId   int    `json:"member_id"`    // 分销员ID(前端不用传)
	ScrmUserId string `json:"scrm_user_id"` // 分销员SCRM用户ID(前端不用传)
}

// RefundOrderItem 退款单项
type RefundOrderItem struct {
	// 订单编号
	OrderSn string `json:"order_sn"`
	// 扣减佣金金额(分)
	Amount int64 `json:"amount"`
	// 状态
	Status int `json:"status"`
	// 商品名称
	GoodsName string `json:"goods_name"`
	// 下单时间
	AddTime string `json:"add_time"`
	// 扣减佣金记录id
	SwlmCpsId int `json:"swlm_cps_id"`
}

// GetRefundOrderListRes 获取退款单列表响应
type GetRefundOrderListRes struct {
	// 响应码
	Code int `json:"code"`
	// 响应信息
	Message string `json:"message"`
	// 总金额
	Amount int `json:"amount"`
	// 退款单列表
	Data []DistributorSwlmCpsListRes `json:"data"`
}
