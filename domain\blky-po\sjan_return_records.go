package blky_po

import (
	"time"

	"xorm.io/xorm"
)

// SjanReturnRecord 北京世纪安诺退货记录表领域模型
type SjanReturnRecord struct {
	Id         int       `xorm:"pk autoincr not null 'id'" json:"id"`                  // 自增主键
	Barcode    string    `xorm:"not null VARCHAR(100) 'barcode'" json:"barcode"`       // 物流码
	CuNo       string    `xorm:"not null VARCHAR(50) 'cu_no'" json:"cu_no"`            // 退货代理编号
	PNo        string    `xorm:"not null VARCHAR(50) 'p_no'" json:"p_no"`              // 退货产品编号
	InDate     time.Time `xorm:"not null datetime 'in_date'" json:"in_date"`           // 退货时间
	Number     int       `xorm:"not null 'number'" json:"number"`                      // 包装数量
	CreateTime time.Time `xorm:"datetime created 'create_time'" json:"create_time"`    // 创建时间
}

// TableName 表名
func (s *SjanReturnRecord) TableName() string {
	return "blky.sjan_return_records"
}

// GetByBarcode 根据物流码获取退货记录列表
func (s *SjanReturnRecord) GetByBarcode(session *xorm.Session, barcode string) ([]SjanReturnRecord, error) {
	var records []SjanReturnRecord
	err := session.Where("barcode = ?", barcode).Find(&records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// GetByCuNo 根据代理编号获取退货记录列表
func (s *SjanReturnRecord) GetByCuNo(session *xorm.Session, cuNo string) ([]SjanReturnRecord, error) {
	var records []SjanReturnRecord
	err := session.Where("cu_no = ?", cuNo).Find(&records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// GetByPNo 根据产品编号获取退货记录列表
func (s *SjanReturnRecord) GetByPNo(session *xorm.Session, pNo string) ([]SjanReturnRecord, error) {
	var records []SjanReturnRecord
	err := session.Where("p_no = ?", pNo).Find(&records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// GetByTimeRange 根据时间范围查询退货记录
func (s *SjanReturnRecord) GetByTimeRange(session *xorm.Session, startTime, endTime time.Time) ([]SjanReturnRecord, error) {
	var records []SjanReturnRecord
	err := session.Where("in_date BETWEEN ? AND ?", startTime, endTime).Find(&records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// AddReturnRecord 添加退货记录
func (s *SjanReturnRecord) AddReturnRecord(session *xorm.Session) (int64, error) {
	return session.Insert(s)
} 