package controllers

import (
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/order-service/services"
	vo "eShop/view-model/order-vo"
	"net/http"

	"github.com/go-chi/chi/v5"
)

func NewPayController(service services.PayService) *PayController {
	return &PayController{
		service: service,
	}
}

// PayController 支付控制器
type PayController struct {
	service services.PayService
}

func (pc *PayController) RegisterRoutes(r chi.Router) {
	r.Route("/order-app/pay", func(r chi.Router) {
		r.Post("/getPayInfo", pc.GetPayInfo)
	})
}

// GetPayInfo 获取支付参数
// @Summary 获取支付参数
// @Description 获取支付参数
// @Tags 支付管理
// @Accept json
// @Produce json
// @Param command body vo.PayRequest true "请求参数"
// @Success 200 {object} vo.WXPayResponse "成功"
// @Failure 400 {object} vo.WXPayResponse "请求错误"
// @Router /order-app/pay/getPayInfo [post]
func (pc *PayController) GetPayInfo(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PayRequest](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	// 调用service获取支付数据
	resp, err := pc.service.GetPayInfo(cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, resp)
}
