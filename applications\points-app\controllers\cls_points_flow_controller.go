package controllers

import (
	po "eShop/domain/points-po"
	"eShop/infra/response"
	"eShop/infra/utils"
	service "eShop/services/points-service"
	vo "eShop/view-model/points-vo"
	"net/http"
	"strings"
	"time"

	"github.com/go-chi/chi"
)

// ClsPointsFlowController 提供了积分流水相关的API接口
type ClsPointsFlowController struct {
	SuperController[int, po.ClsPointsFlow, vo.ClsPointsFlowSaveVO, vo.ClsPointsFlowUpdateVO, vo.ClsPointsFlowQueryVO, vo.ClsPointsFlowResultVO]
	ControllerHooks[int, po.ClsPointsFlow, vo.ClsPointsFlowSaveVO, vo.ClsPointsFlowUpdateVO, vo.ClsPointsFlowQueryVO, vo.ClsPointsFlowResultVO]
	service service.ClsPointsFlowService
}

// NewClsPointsFlowController 创建一个新的 ClsPointsFlowController 实例
func NewClsPointsFlowController() ClsPointsFlowController {
	return ClsPointsFlowController{
		NewSuperController(
			service.NewClsPointsFlowService(),
			&ClsPointsFlowController{},
		),
		NewControllerHooks[int, po.ClsPointsFlow, vo.ClsPointsFlowSaveVO, vo.ClsPointsFlowUpdateVO, vo.ClsPointsFlowQueryVO, vo.ClsPointsFlowResultVO](),
		service.NewClsPointsFlowService(),
	}
}

// Routes 定义并注册与积分流水相关的路由
func (c ClsPointsFlowController) Routes(r chi.Router) {
	c.SuperController.Routes(r)
	r.Post("/sales-in", c.SalesIn)
	r.Get("/get-stat", c.GetStat)
	r.Post("/expire", c.Expire)
}

// SalesIn 处理销售积分入账
// @Summary 处理销售积分入账
// @Description 处理销售积分入账
// @Tags 积分流水
// @Accept json
// @Produce json
// @Param data body vo.SalesInVO true "销售积分入账请求"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Router /points-app/cls-points-flow/sales-in [post]
func (c ClsPointsFlowController) SalesIn(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.SalesInVO](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	// 解析日期范围
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		response.BadRequest(w, "开始日期格式错误")
		return
	}
	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		response.BadRequest(w, "结束日期格式错误")
		return
	}
	if endDate.Before(startDate) {
		response.BadRequest(w, "结束日期不能早于开始日期")
		return
	}

	// 遍历日期范围内的每一天
	var failedDates []string

	for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
		curDate := d.Format("2006-01-02")
		err := c.service.SalesIn(nil, curDate)
		if err != nil {
			failedDates = append(failedDates, curDate+": "+err.Error())
		}
	}

	if len(failedDates) > 0 {
		response.BadRequest(w, "部分日期处理失败：\n"+strings.Join(failedDates, "\n"))
		return
	}

	response.Success(w)
}

// GetStat 获取积分统计信息
// @Summary 获取积分统计信息
// @Description 获取积分统计信息
// @Tags 积分流水
// @Accept json
// @Produce json
// @Param data body vo.ClsPointsFlowQueryVO true "积分统计信息请求"
// @Success 200 {object} response.Response[vo.ClsPointsFlowStatVO] "成功"
// @Failure 400 {object} response.BaseResp "请求参数错误"
// @Router /points-app/cls-points-flow/get-stat [get]
func (c ClsPointsFlowController) GetStat(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.ClsPointsFlowQueryVO](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	result, err := c.service.GetStat(nil, req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, result)
}

func (c ClsPointsFlowController) Expire(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.ClsPointsFlowQueryVO](r)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	err = c.service.PointsExpire(nil, req.ExpireTimeStart)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}
