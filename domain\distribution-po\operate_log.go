package distribution_po

import "time"

type OperateLog struct {
	Id          uint      `json:"id" xorm:"pk autoincr not null INT 'id'"`
	ModuleType  int       `json:"module_type" xorm:"not null comment('业务模块l标识') INT 'module_type'"`
	Type        int       `json:"type" xorm:"not null comment('操作类型') INT 'type'"`
	FromId      string    `json:"from_id" xorm:"not null default 0 comment('来自表id') INT 'from_id'"`
	Description string    `json:"description" xorm:"default '' comment('描述') VARCHAR(255) 'description'"`
	IpLocation  string    `json:"ip_location" xorm:"default '' comment('ip所在地') VARCHAR(20) 'ip_location'"`
	Ip          string    `json:"ip" xorm:"default '' comment('ip地址') VARCHAR(20) 'ip'"`
	UserNo      string    `json:"user_no" xorm:"default '' comment('用户编号') VARCHAR(64) 'user_no'"`
	UserName    string    `json:"user_name" xorm:"default '' comment('用户名称') VARCHAR(30) 'user_name'"`
	CreateTime  time.Time `json:"create_time" xorm:"default 'null' comment('日志时间') DATETIME 'create_time'"`
}
