package export

import (
	"context"
	po "eShop/domain/inventory-po/location"
	"eShop/domain/inventory-po/warehouse"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/inventory-service/location"
	vo "eShop/view-model/inventory-vo/location"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type InventoryLocationTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.QueryParams
	ContextData  string
	writer       *excelize.StreamWriter
	common.BaseService
}

// DataExport 导出库存位置数据
func (e InventoryLocationTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	e.ExportParams = new(vo.QueryParams)
	err = json.Unmarshal([]byte(taskParams), e.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	failNum = 0
	e.ExportParams.Page = 1
	//每页最大数量不能超过此值, 也不能不传, 不然会报`mysql 1390 prepared statement contains too many placeholders`, 具体含义请google
	e.ExportParams.PageSize = 1000

	// 使用流式写入，节省内存
	e.writer, err = e.F.NewStreamWriter(e.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	// 设置表头
	e.SetSheetName()

	// 使用 LocationService 查询库存位置数据
	locationSrv := location.NewLocationService()

	// 写入数据
	total := 0
	for {
		ret, _, err := locationSrv.Query(context.Background(), *e.ExportParams)
		if err != nil {
			return 0, 0, errors.New("获取导出数据失败, " + err.Error())
		}
		e.ExportParams.Page += 1
		for i := 0; i < len(ret); i++ {
			total++
			axis := fmt.Sprintf("A%d", total+1)

			tmp := []interface{}{
				ret[i].Warehouse.WarehouseName, // 仓库名称
				ret[i].WarehouseId,             // 仓库ID
				ret[i].Code,                    // 库位
				ret[i].Products.ProductName,    // 商品名称 (需要从商品服务获取)
				ret[i].Products.ProductID,      // 商品ID
				ret[i].Products.SKUID,          // 商品skuID
				ret[i].Products.BarCode,        // 商品条码 (需要从商品服务获取)
				ret[i].Products.ProductSpec,    // 规格 (需要从商品服务获取)
			}
			err = e.writer.SetRow(axis, tmp)
			if err != nil {
				return 0, 0, errors.New("写入数据失败, " + err.Error())
			}
		}

		if len(ret) < int(e.ExportParams.PageSize) {
			break
		}
	}

	successNum = total
	err = e.writer.Flush()
	if err != nil {
		return 0, 0, errors.New("保存文件失败, " + err.Error())
	}

	return
}

func (e InventoryLocationTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"仓库名称",
		"仓库ID",
		"库位",
		"商品名称",
		"商品ID",
		"商品skuID",
		"商品条码",
		"规格",
	}
	_ = e.writer.SetRow("A1", nameList)
}

func (e InventoryLocationTask) GenerateDownUrl() (url string, err error) {
	fileName := fmt.Sprintf("库存库位导出(%s%d).xlsx", time.Now().Format("20060102150405"), time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(e.F, fileName)
}

func (e InventoryLocationTask) OperationFunc(row []string, orgId int) string {
	e.Begin()
	defer e.Close()

	session := e.Session

	// 错误信息
	var msg string

	// 检查行数据长度
	if len(row) != 3 && len(row) != 5 {
		msg = "模板格式错误，请上传正确模板"
		return msg
	}

	// 获取字段值
	warehouseId := cast.ToInt(row[1])
	locationCode := row[2]

	productId := 0
	skuId := 0
	if len(row) == 5 {
		productId = cast.ToInt(row[3])
		skuId = cast.ToInt(row[4])
	}

	// 根据仓库id和库位编码查询库位信息
	loc, err := new(po.Location).ListLocations(session, po.GetLocationListReq{
		WarehouseId:  warehouseId,
		LocationCode: locationCode,
	})

	// 获取到chainId和storeId
	contextData := strings.Split(e.ContextData, ",")
	if len(contextData) != 2 {
		msg = "上下文数据错误，未能获取到连锁和店铺信息"
		return msg
	}
	// string转int64
	chainId, _ := strconv.ParseInt(contextData[0], 10, 64)
	storeId := contextData[1]

	// 根据仓库id和skuId查询库位信息
	if skuId > 0 {
		skuLocation, _, err := new(po.Location).GetLocation(session, po.GetLocationReq{
			WarehouseId: warehouseId,
			SkuId:       skuId,
		})
		if err != nil {
			msg = fmt.Sprintf("根据仓库id和skuId查询库位失败: %s", err.Error())
			return msg
		}
		if skuLocation.Id > 0 && skuLocation.Code != locationCode {
			msg = fmt.Sprintf("商品：skuId:%d 已绑定库位：%s", skuId, skuLocation.Code)
			return msg
		}

		// 校验skuId所在的仓库和传入是否一致
		warehouse, err := new(warehouse.Warehouse).GetByStoreIdAndSkuId(context.Background(), session, storeId, skuId)
		if err != nil || warehouse.Id == 0 {
			msg = fmt.Sprintf("根据店铺id和skuId查询仓库失败: %s", err.Error())
			return msg
		}
		if warehouse.Id != warehouseId {
			msg = fmt.Sprintf("商品所在仓库：%d 与传入仓库：%d 不一致", warehouse.Id, warehouseId)
			return msg
		}
	}

	// 如果库位不存在，则创建新的库位
	if len(loc) == 0 {
		l := po.Location{
			ChainId:     chainId,
			StoreId:     storeId,
			WarehouseId: warehouseId,
			Code:        locationCode,
			ProductId:   productId,
			SkuId:       skuId,
			UpdatedTime: time.Now(),
			CreatedTime: time.Now(),
		}
		err = l.Create(context.Background(), session)
		if err != nil {
			msg = fmt.Sprintf("创建库位失败: %s", err.Error())
			return msg
		}
	} else {
		// 如果库位存在，则更新库位信息
		loc[0].ProductId = productId
		loc[0].SkuId = skuId
		loc[0].UpdatedTime = time.Now()
		err = loc[0].Update(context.Background(), session)
		if err != nil {
			msg = fmt.Sprintf("更新库位失败: %s", err.Error())
			return msg
		}
	}

	return ""
}
