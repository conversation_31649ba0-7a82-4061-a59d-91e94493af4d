package marketing_po

import (
	"eShop/infra/utils"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

// 优惠券状态常量
const (
	StatusPending = 1 // 未开始
	StatusRunning = 2 // 进行中
	StatusEnded   = 3 // 已结束

	UseStartTypeToday     = 1 // 当日起
	UseStartTypeTomorrow  = 2 // 次日起
	UseStartTypeSpecified = 3 // 指定时间

	//优惠券使用状态0-全部 1-未使用,2-已使用,3-已失效
	StatusAll     = 0
	StatusUnused  = 1
	StatusUsed    = 2
	StatusExpired = 3

	// 适用商品
	ApplyProductAll = 1 //全部
	ApplyProductYes = 2 // 指定商品可用
	ApplyProductNo  = 3 // 指定商品不可用
)

// marketing_coupon 优惠券信息
type MarketingCoupon struct {
	// 优惠券ID
	Id int `xorm:"pk autoincr 'id'" json:"id"`
	// 连锁ID
	ChainId int64 `xorm:"'chain_id'" json:"chain_id"`
	// 店铺ID
	StoreId string `xorm:"'store_id'" json:"store_id"`
	// 优惠券类型 1-满减 2-折扣 3-兑换
	Type int `xorm:"'type'" json:"type"`
	// 优惠券名称
	Name string `xorm:"'name'" json:"name"`
	// 优惠券内容
	Content string `xorm:"'content'" json:"content"`
	// 优惠券总数
	TotalCount int `xorm:"'total_count'" json:"total_count"`
	// 剩余数量
	RemainCount int `xorm:"'remain_count'" json:"remain_count"`
	// 领取数量
	ReceivedCount int `xorm:"'received_count'" json:"received_count"`
	// 已使用数量
	UsedCount int `xorm:"'used_count'" json:"used_count"`
	// 适用商品：1-全部 2-指定可用 3-指定不可用
	ApplyProduct int `xorm:"'apply_product'" json:"apply_product"`
	// 使用门槛
	Threshold float64 `xorm:"'threshold'" json:"threshold"`
	// 折扣
	Discount float64 `xorm:"'discount'" json:"discount"`
	// 最高优惠
	BestOffer float64 `xorm:"'best_offer'" json:"best_offer"`
	// 用券开始类型
	UseStartType int `xorm:"'use_start_type'" json:"use_start_type"`
	// 领券多少天内可以用
	WithinDay int `xorm:"'within_day'" json:"within_day"`
	// 券开始日期
	StartTime time.Time `xorm:"'start_time'" json:"start_time"`
	// 券结束日期
	EndTime time.Time `xorm:"'end_time'" json:"end_time"`
	// 每人限领次数
	PersonLimit int `xorm:"'person_limit'" json:"person_limit"`
	// 使用说明
	Remark string `xorm:"'remark'" json:"remark"`
	// 数据状态
	Status int `xorm:"'status'" json:"status"`
	// 删除标识
	IsDeleted int `xorm:"'is_deleted'" json:"is_deleted"`
	// 创建时间
	CreatedTime time.Time `xorm:"created 'created_time'" json:"created_time"`
	// 更新时间
	UpdatedTime time.Time `xorm:"updated 'updated_time'" json:"updated_time"`
}

func (MarketingCoupon) TableName() string {
	return "eshop.marketing_coupon"
}

// GetList 获取优惠券列表
func (m *MarketingCoupon) GetList(session *xorm.Session, param *CouponListQuery) ([]MarketingCoupon, int64, error) {
	query := session.Table(m.TableName())

	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Type != "" {
		query = query.Where("type = ?", param.Type)
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}

	var coupons []MarketingCoupon
	total, err := query.Where("is_deleted = 0 AND store_id = ?", param.StoreId).
		Limit(param.PageSize, (param.PageIndex-1)*param.PageSize).
		Desc("created_time").
		FindAndCount(&coupons)

	if err != nil {
		return nil, 0, fmt.Errorf("查询优惠券列表失败: %v", err)
	}

	// 计算每个优惠券的实时状态
	for i := range coupons {
		coupons[i].CalculateStatus()
	}

	return coupons, total, nil
}

// GetByID 根据ID获取优惠券
func (m *MarketingCoupon) GetByID(session *xorm.Session, id int64, storeId string) (*MarketingCoupon, error) {
	var coupon MarketingCoupon
	exists, err := session.Where("id = ? AND store_id = ? AND is_deleted = 0", id, storeId).Get(&coupon)
	if err != nil {
		return nil, fmt.Errorf("查询优惠券失败: %v", err)
	}
	if !exists {
		return nil, errors.New("优惠券不存在")
	}

	//coupon.CalculateStatus()
	return &coupon, nil
}

// Save 保存优惠券
func (m *MarketingCoupon) Save(session *xorm.Session) error {
	// 基础验证
	if err := m.ValidateBasic(); err != nil {
		return err
	}

	// 设置优惠券内容
	m.SetContent()

	// 计算状态
	m.CalculateStatus()

	if m.Id > 0 {
		_, err := session.AllCols().ID(m.Id).Update(m)
		return err
	}
	_, err := session.Insert(m)
	return err
}

// Delete 删除优惠券
func (m *MarketingCoupon) Delete(session *xorm.Session) error {
	if m.Status == StatusRunning {
		return errors.New("进行中的优惠券不能删除")
	}

	m.IsDeleted = 1
	m.UpdatedTime = time.Now()

	_, err := session.ID(m.Id).Cols("is_deleted", "updated_time").Update(m)
	if err != nil {
		return fmt.Errorf("删除优惠券失败: %v", err)
	}
	return nil
}

// End 结束优惠券
func (m *MarketingCoupon) End(session *xorm.Session) error {
	if m.Status == StatusEnded {
		return errors.New("优惠券已结束")
	}

	m.Status = StatusEnded
	m.UpdatedTime = time.Now()

	_, err := session.ID(m.Id).Cols("status", "updated_time").Update(m)
	if err != nil {
		return fmt.Errorf("结束优惠券失败: %v", err)
	}
	return nil
}

// Issue 发放优惠券
func (m *MarketingCoupon) Issue(session *xorm.Session, customerId int64) error {
	m.CalculateStatus()
	// 1. 检查优惠券状态
	if m.Status != StatusRunning {
		return errors.New("优惠券活动未开始或已结束")
	}

	// 2. 检查领取限制
	if err := m.checkPersonLimit(session, customerId); err != nil {
		return err
	}

	// 3. 扣减库存
	if err := m.decrementRemainCount(session); err != nil {
		return err
	}

	return nil
}

// ValidateBasic 基础验证
func (m *MarketingCoupon) ValidateBasic() error {
	if m.Name == "" {
		return errors.New("优惠券名称不能为空")
	}
	if m.TotalCount <= 0 {
		return errors.New("优惠券数量必须大于0")
	}
	now := time.Now()
	// 根据UseStartType验证
	switch m.UseStartType {
	case UseStartTypeToday, UseStartTypeTomorrow: // 当日起/次日起
		if m.WithinDay <= 0 {
			return errors.New("当日起/次日起模式下必须设置有效期天数")
		}
		// 当日起/次日起模式下，不存储时间
		m.StartTime = time.Time{}
		m.EndTime = time.Time{}

	case UseStartTypeSpecified: // 指定时间
		if m.StartTime.IsZero() || m.EndTime.IsZero() {
			return errors.New("指定时间模式下必须设置开始和结束时间")
		}
		if m.Id == 0 && m.StartTime.Before(now) {
			return errors.New("开始时间不能小于当前时间")
		}
		if m.StartTime.After(m.EndTime) {
			return errors.New("开始时间必须小于结束时间")
		}
		endTime, err := time.Parse("2006-01-02 15:04:05", m.EndTime.Format("2006-01-02 15:04:05"))
		if err != nil {
			return errors.New("结束时间格式错误")
		}
		if endTime.Before(m.StartTime) || endTime.Equal(m.StartTime) {
			return errors.New("结束时间必须大于开始时间")
		}
	default:
		return errors.New("无效的开始时间类型")
	}

	return nil
}

// SetContent 设置优惠券内容
func (m *MarketingCoupon) SetContent() {
	switch m.Type {
	case 1: // 满减
		m.Content = fmt.Sprintf("满%.2f减%.2f", m.Threshold, m.Discount)
	case 2: // 折扣
		m.Content = fmt.Sprintf("满%.2f打%.1f折", m.Threshold, m.Discount)
	case 3: // 兑换
		m.Content = fmt.Sprintf("满%.2f可兑换", m.Threshold)
	}
}

// CalculateStatus 计算优惠券状态
func (m *MarketingCoupon) CalculateStatus() {
	if m.IsDeleted == 1 || m.Status == StatusEnded {
		m.Status = StatusEnded
		return
	}
	// 当日起/次日起的优惠券直接返回进行中状态
	if m.UseStartType == UseStartTypeToday || m.UseStartType == UseStartTypeTomorrow {
		m.Status = StatusRunning
		return
	}
	now := time.Now()
	if now.Before(m.StartTime) {
		m.Status = StatusPending
	} else if now.After(m.EndTime) {
		m.Status = StatusEnded
	} else {
		m.Status = StatusRunning
	}
}

// CalculateTime 计算优惠券有效期
func (m *MarketingCoupon) CalculateTime() (enableTime, expireTime time.Time) {
	now := time.Now()

	switch m.UseStartType {
	case UseStartTypeToday: // 当日起
		enableTime = now
		if m.WithinDay > 0 {
			expireTime = now.AddDate(0, 0, m.WithinDay)
		} else {
			expireTime = m.EndTime
		}
	case UseStartTypeTomorrow: // 次日起
		enableTime = now.AddDate(0, 0, 1)
		if m.WithinDay > 0 {
			expireTime = enableTime.AddDate(0, 0, m.WithinDay)
		} else {
			expireTime = m.EndTime
		}
	case UseStartTypeSpecified: // 指定时间
		enableTime = m.StartTime
		expireTime = m.EndTime
	}

	return enableTime, expireTime
}

// checkPersonLimit 检查领取限制
func (m *MarketingCoupon) checkPersonLimit(session *xorm.Session, customerId int64) error {
	if m.PersonLimit > 0 {
		count, err := session.Table("marketing_coupon_receiver").
			Where("coupon_id = ? AND customer_id = ? AND is_deleted = 0",
				m.Id, customerId).Count()
		if err != nil {
			return fmt.Errorf("查询领取记录失败: %v", err)
		}
		if count >= int64(m.PersonLimit) {
			return fmt.Errorf("超过领取限制")
		}
	}
	return nil
}

// decrementRemainCount 扣减库存
func (m *MarketingCoupon) decrementRemainCount(session *xorm.Session) error {
	result, err := session.Exec(
		"UPDATE marketing_coupon SET remain_count = remain_count - 1, received_count = received_count + 1 "+
			"WHERE id = ? AND remain_count > 0", m.Id)
	if err != nil {
		return fmt.Errorf("扣减库存失败: %v", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	if affected == 0 {
		return errors.New("优惠券已发完")
	}

	return nil
}

// CouponListQuery 列表查询参数
type CouponListQuery struct {
	ChainId   int64
	StoreId   string
	Name      string
	Type      string
	Status    string
	PageIndex int
	PageSize  int
}

// GetDetail 获取优惠券详情
func (m *MarketingCoupon) GetDetail(session *xorm.Session, id int64) (*CouponDetail, error) {
	// 1. 获取优惠券基本信息
	exists, err := session.Where("id = ? AND is_deleted = 0", id).Get(m)
	if err != nil {
		return nil, fmt.Errorf("查询优惠券失败: %v", err)
	}
	if !exists {
		return nil, errors.New("优惠券不存在")
	}

	// 2. 计算实时状态
	m.CalculateStatus()

	// 3. 获取关联商品
	var products []MarketingProduct
	if m.ApplyProduct == 2 || m.ApplyProduct == 3 {
		err = session.Table("marketing_product").
			Where("ref_id = ? AND type = 1", m.Id).
			Find(&products)
		if err != nil {
			return nil, fmt.Errorf("查询关联商品失败: %v", err)
		}
	}

	return &CouponDetail{
		Coupon:   m,
		Products: products,
	}, nil
}

// GetReceiverList 获取领取记录列表
func (m *MarketingCoupon) GetReceiverList(session *xorm.Session, param *ReceiverListQuery) ([]MarketingCouponReceiver, int64, error) {
	query := session.Table("marketing_coupon_receiver")

	// 构建查询条件
	query = query.Where("coupon_id = ? AND is_deleted = 0", m.Id)
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}
	if param.CustomerName != "" {
		query = query.Where("customer_name LIKE ?", "%"+param.CustomerName+"%")
	}
	if param.CustomerMobile != "" {
		query = query.Where("encry_customer_mobile = ?", utils.MobileEncrypt(param.CustomerMobile))
	}

	var receivers []MarketingCouponReceiver
	total, err := query.Limit(param.PageSize, (param.PageIndex-1)*param.PageSize).
		Desc("created_time").
		FindAndCount(&receivers)
	if err != nil {
		return nil, 0, fmt.Errorf("查询领取记录失败: %v", err)
	}

	return receivers, total, nil
}

// CouponDetail 优惠券详情
type CouponDetail struct {
	Coupon   *MarketingCoupon   `json:"coupon"`
	Products []MarketingProduct `json:"products"`
}

// ReceiverListQuery 领取记录查询参数
type ReceiverListQuery struct {
	// 状态：0-全部 1-未使用 2-已使用 3-已失效
	Status         string
	CustomerName   string
	CustomerMobile string
	PageIndex      int
	PageSize       int
}

// GetUserCouponList 获取用户优惠券列表
func (m *MarketingCoupon) GetUserCouponList(session *xorm.Session, param *CouponUserListQuery) ([]UserCouponInfo, int64, error) {
	// 第一步：查询店铺有效的优惠券活动
	activeCoupons := make([]MarketingCoupon, 0)
	query := session.Table("eshop.marketing_coupon")
	err := query.Where("store_id = ? AND is_deleted = 0", param.StoreId).
		Where("status = 2").       // 进行中的活动
		Where("remain_count > 0"). // 还有剩余数量
		Where("(" +
			"(use_start_type = 1 AND DATE(NOW()) >= DATE(created_time)) OR " + // 当日起
			"(use_start_type = 2 AND DATE(NOW()) > DATE(created_time)) OR " + // 次日起
			"(use_start_type = 3 AND NOW() BETWEEN start_time AND end_time)" + // 指定时间
			")").
		OrderBy("created_time DESC").
		Find(&activeCoupons)

	if err != nil {
		return nil, 0, fmt.Errorf("查询有效优惠券失败: %v", err)
	}

	if len(activeCoupons) == 0 {
		return []UserCouponInfo{}, 0, nil
	}

	// 第二步：查询用户领取状态
	couponIds := make([]int, 0)
	for _, c := range activeCoupons {
		couponIds = append(couponIds, c.Id)
	}

	// 查询用户已领取的优惠券
	receivedMap := make(map[int]string) // couponId -> code
	var receivers []struct {
		CouponId   int       `xorm:"coupon_id"`
		Code       string    `xorm:"code"`
		Status     int       `xorm:"status"`      // 使用状态
		EnableTime time.Time `xorm:"enable_time"` // 生效时间
		ExpireTime time.Time `xorm:"expire_time"` // 失效时间
	}

	currentTime := time.Now()
	err = session.Table("eshop.marketing_coupon_receiver").
		Where("customer_id = ? AND is_deleted = 0", param.CustomerId).
		Where("status = 1").                                                      // 未使用状态
		Where("enable_time <= ? AND expire_time >= ?", currentTime, currentTime). // 在有效期内
		In("coupon_id", couponIds).
		Find(&receivers)

	if err != nil {
		return nil, 0, fmt.Errorf("查询领取状态失败: %v", err)
	}

	for _, r := range receivers {
		receivedMap[r.CouponId] = r.Code
	}

	// 转换为返回结构
	result := make([]UserCouponInfo, 0, len(activeCoupons))
	for _, c := range activeCoupons {
		code, received := receivedMap[c.Id]
		item := UserCouponInfo{
			Id:           c.Id,
			Name:         c.Name,
			Type:         c.Type,
			Content:      c.Content,
			TotalCount:   c.TotalCount,
			RemainCount:  c.RemainCount,
			ApplyProduct: c.ApplyProduct,
			Threshold:    c.Threshold,
			Discount:     c.Discount,
			BestOffer:    c.BestOffer,
			UseStartType: c.UseStartType,
			StartTime:    c.StartTime,
			EndTime:      c.EndTime,
			Status:       c.Status,
			IsReceived:   map[bool]int{false: 0, true: 1}[received],
			Code:         code,
		}
		result = append(result, item)
	}

	total := int64(len(result))

	// 处理分页
	start := (param.PageIndex - 1) * param.PageSize
	end := start + param.PageSize
	if start < len(result) {
		if end > len(result) {
			end = len(result)
		}
		result = result[start:end]
	} else {
		result = []UserCouponInfo{}
	}

	return result, total, nil
}

// CouponUserListQuery 用户优惠券列表查询参数
type CouponUserListQuery struct {
	// 店铺ID
	StoreId string
	// 用户ID
	CustomerId string
	// 状态：0-全部 1-未开始 2-进行中 3-已结束
	Status int
	// 商品ID列表
	SkuIds []string
	// 页码
	PageIndex int
	// 每页数量
	PageSize int
}

// UserCouponInfo 用户优惠券信息
type UserCouponInfo struct {
	// 优惠券ID
	Id int `json:"id"`
	// 优惠券名称
	Name string `json:"name"`
	// 优惠券类型：1-满减 2-折扣 3-兑换
	Type int `json:"type"`
	// 优惠券内容
	Content string `json:"content"`
	// 总数量
	TotalCount int `json:"total_count"`
	// 剩余数量
	RemainCount int `json:"remain_count"`
	// 适用商品：1-全部 2-指定可用 3-指定不可用
	ApplyProduct int `json:"apply_product"`
	// 使用门槛
	Threshold float64 `json:"threshold"`
	// 优惠金额/折扣
	Discount float64 `json:"discount"`
	// 最高优惠
	BestOffer float64 `json:"best_offer"`
	// 用券开始类型：1-当日起 2-次日起 3-指定时间
	UseStartType int `json:"use_start_type"`
	// 领券多少天内可用
	WithinDay int `json:"within_day"`
	// 开始时间
	StartTime time.Time `json:"start_time"`
	// 结束时间
	EndTime time.Time `json:"end_time"`
	// 状态：1-未开始 2-进行中 3-已结束
	Status int `json:"status"`
	// 是否已领取：0-未领取 1-已领取
	IsReceived int `json:"is_received"`
	// 券码(已领取时返回)
	Code string `json:"code"`
}
