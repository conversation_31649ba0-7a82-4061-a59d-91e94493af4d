package distribution_vo

import "time"

// UserPageReq 用户分页查询请求
type UserPageReq struct {
	// 页码
	PageIndex int `json:"page_index"`
	// 每页条数
	PageSize int `json:"page_size"`
	// 手机号
	Mobile string `json:"mobile"`
	// 用户名称
	MemberName string `json:"member_name"`
	// 用户ID
	MemberId int `json:"member_id"`
	// 注册开始时间
	RegisterStartTime string `json:"register_start_time"`
	// 注册结束时间
	RegisterEndTime string `json:"register_end_time"`
	// 组织ID(前端不传)
	OrgId int `json:"org_id"`
}

// UserOrderStats 用户订单统计
type OrderStats struct {
	Count    int       `xorm:"count"`
	LastTime time.Time `xorm:"max_time"`
}

// UserPageResp 用户列表响应
type UserPageResp struct {
	Code    int                `json:"code"`
	Message string             `json:"message"`
	Total   int64              `json:"total"`
	Data    []VoUserMemberView `json:"data"`
}
type VoUserMemberView struct {
	// 用户ID
	MemberId int `json:"member_id"`
	// 用户ID
	ScrmUserId string `json:"scrm_user_id"`
	// 组织ID
	OrgId int `json:"org_id"`
	// 微信用户统一标识
	WeixinUnionid string `json:"weixin_unionid"`
	// 微信小程序openid
	WeixinMiniOpenid string `json:"weixin_mini_openid"`
	// 用户名称
	MemberName string `json:"member_name"`
	// 会员等级
	UserLevelId string `json:"user_level_id"`
	// 用户性别 0未知 1男 2女
	MemberSex int `json:"member_sex"`
	// 用户手机号
	MemberMobile string `json:"member_mobile"`
	// 用户注册时间
	CreateTime string `json:"create_time"`
	// 用户最后登录时间
	MemberLoginTime string `json:"member_login_time"`
	// 用户最后购买时间
	LastPurchaseTime string `json:"last_purchase_time" xorm:"-"`
	// 用户订单数量
	OrderCount int `json:"order_count" xorm:"-"`
}

// UserDetailReq 用户详情请求
type UserDetailReq struct {
	MemberId int64 `json:"member_id" required:"true" validate:"required"` // 用户ID
	OrgId    int   `json:"org_id"`                                        // 机构ID
}

// UserDetailResp 用户详情响应
type UserDetailResp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    *UserDetail `json:"data"`
}

// UserDetail 用户详细信息
type UserDetail struct {
	// 用户ID
	ScrmUserId string `json:"scrm_user_id"`
	// 用户ID
	UserId int64 `json:"user_id"`
	// 用户昵称
	UserName string `json:"user_name"`
	// 手机号
	Mobile string `json:"mobile"`
	// 性别 1男 2女
	Sex int `json:"sex"`
	// 生日
	Birthday string `json:"birthday"`
	// 所在地
	Address string `json:"address"`
	// 注册时间
	RegisterTime string `json:"register_time"`
	// 最后登录时间
	LastLoginTime string `json:"last_login_time"`
	// 订单总数
	OrderCount int `json:"order_count"`
	// 最后下单时间
	LastOrderTime string `json:"last_order_time"`
	// 累计消费金额
	TotalAmount float64 `json:"total_amount"`
	// 扫码验真伪次数
	VerifyCount int `json:"verify_count"`
	// 最后扫码时间
	LastVerifyTime string `json:"last_verify_time"`
	// 扫码验真伪渠道统计
	Count int `json:"count" xorm:"-"`
	// 扫码验真伪渠道-微信扫一扫次数
	WechatCount int `json:"wechat_count" xorm:"-"`
	// 扫码验真伪渠道-首页扫一扫次数
	HomeCount int `json:"home_count" xorm:"-"`
	// 扫码验真伪渠道-个人中心-查验真伪
	PersonalCount int `json:"personal_count" xorm:"-"`
	// 初次养宠时间
	FirstRaisesPet string `json:"first_raises_pet"`
	// 头像 user_avatar
	UserAvatar string `json:"user_avatar"`
}

// UserPhoneReq 获取用户手机号请求
type UserPhoneReq struct {
	// 会员ID
	MemberId string `json:"member_id" form:"member_id" validate:"required"`
}

// UserPhoneData 用户手机号数据
type UserPhoneData struct {
	Mobile string `json:"mobile"` // 带星号的手机号
}

// UserPhoneResp 获取用户手机号响应
type UserPhoneResp struct {
	Code    int           `json:"code"`
	Message string        `json:"message"`
	Data    UserPhoneData `json:"data"`
}
