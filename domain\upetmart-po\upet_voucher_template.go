package upetmart_po

type UpetVoucherTemplate struct {
	VoucherTId          int64   `xorm:"pk autoincr not null comment('代金券模版编号') int 'voucher_t_id'"`
	VoucherTTitle       string  `xorm:"not null comment('代金券模版名称') varchar(50) 'voucher_t_title'"`
	VoucherTDesc        string  `xorm:"not null comment('代金券模版描述') varchar(255) 'voucher_t_desc'"`
	VoucherTStartDate   int64   `xorm:"not null comment('代金券模版有效期开始时间') int 'voucher_t_start_date'"`
	VoucherTEndDate     int64   `xorm:"not null comment('代金券模版有效期结束时间') int 'voucher_t_end_date'"`
	VoucherTPrice       int64   `xorm:"not null comment('代金券模版面额') int 'voucher_t_price'"`
	VoucherTLimit       float64 `xorm:"not null comment('代金券使用时的订单限额') DECIMAL(10,2)""`
	VoucherTStoreId     int64   `xorm:"not null comment('代金券模版的店铺id') int 'voucher_t_store_id'"`
	VoucherTStorename   string  `xorm:"default 'NULL' comment('店铺名称') varchar(100) 'voucher_t_storename'"`
	VoucherTScId        int32   `xorm:"not null default 0 comment('所属店铺分类ID') int 'voucher_t_sc_id'"`
	VoucherTCreatorId   int32   `xorm:"not null comment('代金券模版的创建者id') int 'voucher_t_creator_id'"`
	VoucherTState       int8    `xorm:"not null comment('代金券模版状态(1-有效,2-失效)') tinyint 'voucher_t_state'"`
	VoucherTTotal       int32   `xorm:"not null comment('模版可发放的代金券总数') int 'voucher_t_total'"`
	VoucherTGiveout     int32   `xorm:"not null comment('模版已发放的代金券数量') int 'voucher_t_giveout'"`
	VoucherTUsed        int32   `xorm:"not null comment('模版已经使用过的代金券') int 'voucher_t_used'"`
	VoucherTAddDate     int32   `xorm:"not null comment('模版的创建时间') int 'voucher_t_add_date'"`
	VoucherTQuotaid     int32   `xorm:"not null comment('套餐编号') int 'voucher_t_quotaid'"`
	VoucherTPoints      int32   `xorm:"not null default 0 comment('兑换所需积分') int 'voucher_t_points'"`
	VoucherTEachlimit   int32   `xorm:"not null default 1 comment('每人限领张数') int 'voucher_t_eachlimit'"`
	VoucherRepeatType   int32   `xorm:"not null default 0 comment('是否可重复认领0=否，1=是') int 'voucher_repeat_type'"`
	VoucherTStyleimg    string  `xorm:"default 'NULL' comment('样式模版图片') varchar(200) 'voucher_t_styleimg'"`
	VoucherTCustomimg   string  `xorm:"default 'NULL' comment('自定义代金券模板图片') varchar(200) 'voucher_t_customimg'"`
	VoucherTRecommend   int8    `xorm:"not null default 0 comment('是否推荐 0不推荐 1推荐') tinyint(1) 'voucher_t_recommend'"`
	VoucherTGettype     int8    `xorm:"not null default 1 comment('领取方式 1积分兑换 2卡密兑换 3免费领取 4自动派发 5支付有礼') tinyint(1) 'voucher_t_gettype'"`
	VoucherTIsbuild     int8    `xorm:"not null default 0 comment('领取方式为卡密兑换是否已经生成下属代金券 0未生成 1已生成') tinyint(1) 'voucher_t_isbuild'"`
	VoucherTMgradelimit int8    `xorm:"not null default 0 comment('领取代金券限制的会员等级') tinyint 'voucher_t_mgradelimit'"`
	VoucherTType        int64   `xorm:"default 0 comment('使用范围1,无限制2，限单品(SKU)3,限全品(SPU) 4,限品类5限品牌6活动页面商品7，新用户专享8，老用户专享') tinyint 'voucher_t_type'"`
	VoucherTGcId        string  `xorm:"default '0' comment('分类id') varchar(100) 'voucher_t_gc_id'"`
	VoucherSpecialId    int64   `xorm:"default 0 comment('活动板块id') int 'voucher_special_id'"`
	VoucherBrandId      int64   `xorm:"default 0 comment('品牌id') int 'voucher_brand_id'"`
	VoucherVip          string  `xorm:"default '' comment('是否会员卡使用') varchar(20) 'voucher_vip'"`
	VoucherDays         int64   `xorm:"default 0 comment('多少天内可用') int 'voucher_days'"`
	VoucherWxCard       int8    `xorm:"default 2 comment('是否同步到微信卡包，2不同步，1同步') tinyint 'voucher_wx_card'"`
	VoucherShowState    int8    `xorm:"default 1 comment('是否在固定领劵页展示，2不展示，1展示') tinyint 'voucher_show_state'"`
	WxCardId            string  `xorm:"default 'NULL' comment('微信卡券id') varchar(255) 'wx_card_id'"`
	WxStockId           string  `xorm:"default 'NULL' comment('微信商家券批次id') varchar(255) 'wx_stock_id'"`
	VoucherImage        string  `xorm:"default 'NULL' comment('支付有礼时，券详情图片') varchar(255) 'voucher_image'"`
	MerchantLogoUrl     string  `xorm:"default 'NULL' comment('商户logo') varchar(255) 'merchant_logo_url'"`
	CouponImageUrl      string  `xorm:"default 'NULL' comment('券详情') varchar(255) 'coupon_image_url'"`
	VoucherStartDay     int64   `xorm:"default 0 comment('开始日') int 'voucher_start_day'"`
}

func (u *UpetVoucherTemplate) TableName() string {
	return "upetmart.upet_voucher_template"
}
