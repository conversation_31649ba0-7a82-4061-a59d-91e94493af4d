package utils

import (
	"eShop/infra/pkg/util/cache"
	"fmt"
	"time"
)

// GenInventorySN 生成出入库单号
// prefix: CK-出库, RK-入库, PD-盘点
func GenInventorySN(prefix string) string {
	redis := cache.GetRedisConn()
	// 使用日期作为key的一部分，每天从1开始计数
	keyCode := fmt.Sprintf("inventory:sn:%s:%s", prefix, time.Now().Format("20060102"))

	// 使用 INCR 命令原子性地获取自增数字
	num := redis.Incr(keyCode).Val()

	if num == 1 {
		// 如果是第一次设置，设置24小时过期时间
		redis.Expire(keyCode, 24*time.Hour)
	}

	// 生成格式: 前缀 + 年月日时分秒 + 6位自增序号
	// 例如: CK2024122515541721916933
	return fmt.Sprintf("%s%s%06d",
		prefix,
		time.Now().Format("200601021504"), // YYYYMMDDHHmm
		num%1000000,                       // 取余确保6位数
	)
}
