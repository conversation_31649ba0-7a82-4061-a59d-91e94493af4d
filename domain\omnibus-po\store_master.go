package omnibus_po

import (
	"errors"

	"xorm.io/xorm"
)

type StoreMaster struct {
	AppChannel        int    `json:"app_channel" xorm:"pk autoincr not null comment('店铺主体（类型）id 0.未选择 1.阿闻自有 2.TP代运营') INT 'app_channel'"`
	Name              string `json:"name" xorm:"not null comment('店铺类型名称') VARCHAR(56) 'name'"`
	ElmAppId          string `json:"elm_app_id" xorm:"default 'null' comment('饿了么appid') VARCHAR(56) 'elm_app_id'"`
	ElmAppSecret      string `json:"elm_app_secret" xorm:"default 'null' comment('饿了么appsecret') VARCHAR(56) 'elm_app_secret'"`
	MtAppId           string `json:"mt_app_id" xorm:"default 'null' comment('美团appid') VARCHAR(56) 'mt_app_id'"`
	MtAppSecret       string `json:"mt_app_secret" xorm:"default 'null' comment('美团appsecret') VARCHAR(56) 'mt_app_secret'"`
	JddjAppId         string `json:"jddj_app_id" xorm:"default 'null' comment('京东到家 应用的 token') VARCHAR(56) 'jddj_app_id'"`
	JddjAppSecret     string `json:"jddj_app_secret" xorm:"default 'null' comment('京东到家 应用的app_key') VARCHAR(56) 'jddj_app_secret'"`
	JddjAppMerchantId string `json:"jddj_app_merchant_id" xorm:"default 'null' comment('京东到家 应用的商家ID，用于京东到家回调token的时候区分应用') VARCHAR(56) 'jddj_app_merchant_id'"`
	UpdateUserNo      string `json:"update_user_no" xorm:"not null comment('更新店铺主体信息的用户') VARCHAR(56) 'update_user_no'"`
	CreateUserNo      string `json:"create_user_no" xorm:"not null comment('创建店铺主体的用户') VARCHAR(56) 'create_user_no'"`
	IsDeleted         int    `json:"is_deleted" xorm:"default 0 comment('是否删除 0.未删除 1.已删除') TINYINT(1) 'is_deleted'"`
	UpdateTime        string `json:"update_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('最后更新时间') DATETIME 'update_time'"`
	CreateTime        string `json:"create_time" xorm:"not null default 'CURRENT_TIMESTAMP' comment('创建时间') DATETIME 'create_time'"`
}

func NewStoreMaster() *StoreMaster {
	return new(StoreMaster)
}

// 根据AppId和渠道获取 店铺主体id(appchannel)
func (s *StoreMaster) GetStoreMasterId(session *xorm.Session, appId string, channelId int) (int, error) {

	session = session.Table("store_master").Where("is_deleted = ?", 0)

	switch channelId {
	case 3: // 饿了么
		session = session.And("elm_app_id=?", appId)
	case 2: // 美团
		session = session.And("mt_app_id=?", appId)
	case 4: // 京东到家
		session = session.And("jddj_app_id=?", appId)
	default:
		return 0, errors.New("无效的渠道ID")
	}

	storeMaster := new(StoreMaster)
	has, err := session.Get(storeMaster)
	if err != nil {
		return 0, err
	}
	if !has {
		return 0, errors.New("店铺主体不存在")
	}

	return storeMaster.AppChannel, nil
}
