package product_po

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"fmt"
	"time"

	"xorm.io/xorm"
)

type ProProductStoreSpu struct {
	Id                int       `json:"id" xorm:"pk autoincr not null INT 'id'"`
	StoreId           string    `json:"store_id" xorm:"not null default 0 comment('门店的id') VARCHAR(50) 'store_id'"`
	ProductId         int       `json:"product_id" xorm:"not null default 0 comment('商品ID') INT 'product_id'"`
	ChannelId         int       `json:"channel_id" xorm:"not null default 0 comment('渠道id(1-小程序，2-美团，3-饿了么，4-京东到家，100-线下门店)') INT 'channel_id'"`
	ChannelCategoryId int       `json:"channel_category_id" xorm:"default '' comment('分类id') VARCHAR(50) 'channel_category_id'"`
	SyncError         string    `json:"sync_error" xorm:"default 'null' comment('铺品或者上架报错信息') VARCHAR(500) 'sync_error'"`
	ProductThirdId    string    `json:"product_third_id" xorm:"default '' comment('第三方回写的商品ID') VARCHAR(50) 'product_third_id'"`
	ProductType       int       `json:"product_type" xorm:"default 1 comment('商品类型（1-实物商品,2-虚拟商品,3-组合商品,4-服务,5-活体)') INT 'product_type'"`
	CreateDate        time.Time `json:"create_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品添加日期') DATETIME 'create_date' created"`
	UpdateDate        time.Time `json:"update_date" xorm:"default 'CURRENT_TIMESTAMP' comment('商品最后更新日期') DATETIME 'update_date' updated"`
	//商品分类
	NamePath string `json:"namePath" xorm:"default 'null' comment('分类组合名称') VARCHAR(500) 'namePath'"`
}

// DelSpuByStoreAndProduct 删除指定店铺和商品的SPU数据
// 删除前会校验是否还存在对应的SKU信息
func (p *ProProductStoreSpu) DelSpuByStoreAndProduct(session *xorm.Session) error {
	logPrefix := "删除店铺商品SPU信息===="
	log.Info(logPrefix, "入参：", utils.JsonEncode(p))
	if p.StoreId == "" || p.ProductId == 0 || p.ChannelId == 0 {
		log.Error(logPrefix, "删除店铺商品SPU信息失败,参数错误,storeId=", p.StoreId, "|productId=", p.ProductId, "|channelId=", p.ChannelId)
		return fmt.Errorf("删除店铺商品SPU信息失败,参数错误")
	}
	// 检查是否还存在SKU信息
	exists, err := session.Table("eshop.pro_product_store_info").
		Where("store_id = ? AND product_id = ? AND channel_id = ?", p.StoreId, p.ProductId, p.ChannelId).Exist()
	if err != nil {
		log.Error(logPrefix, "查询SKU信息失败,err=", err.Error())
		return fmt.Errorf("查询SKU信息失败")
	}

	if exists {
		log.Error(logPrefix, "该商品在门店中还存在SKU信息,请先删除SKU信息:", p.StoreId, "|", p.ProductId)
		return nil
	}

	// 删除SPU信息
	if _, err := session.Where("store_id = ? AND product_id = ? AND channel_id = ?", p.StoreId, p.ProductId, p.ChannelId).Delete(p); err != nil {
		log.Error(logPrefix, "删除SPU信息失败,err=", err.Error())
		return fmt.Errorf("删除SPU信息失败")
	}

	return nil
}

// 添加查询构建器
type SpuQuery struct {
	ProductId           int // 0 表示未设置
	ProductIds          []int
	ChannelId           int // 0 表示未设置
	ChannelIds          []int
	StoreId             string // 空字符串表示未设置
	StoreIds            []string
	ExcludeProductTypes []int
	ProductTypes        []int
	ChannelCategoryId   string // 根据分类查询
}

func (p *ProProductStoreSpu) QueryMap(session *xorm.Session, query SpuQuery) (map[string]ProProductStoreSpu, []ProProductStoreSpu, error) {

	// 使用 > 0 判断是否设置了查询条件
	if query.ProductId > 0 {
		session = session.Where("product_id = ?", query.ProductId)
	}
	if len(query.ProductIds) > 0 {
		session = session.In("product_id", query.ProductIds)
	}
	if query.ChannelId > 0 {
		session = session.Where("channel_id = ?", query.ChannelId)
	}
	if len(query.ChannelIds) > 0 {
		session = session.In("channel_id", query.ChannelIds)
	}
	if query.StoreId != "" {
		session = session.Where("store_id = ?", query.StoreId)
	}
	if len(query.StoreIds) > 0 {
		session = session.In("store_id", query.StoreIds)
	}
	if len(query.ExcludeProductTypes) > 0 {
		session = session.NotIn("product_type", query.ExcludeProductTypes)
	}
	if len(query.ProductTypes) > 0 {
		session = session.In("product_type", query.ProductTypes)
	}
	if query.ChannelCategoryId != "" {
		session = session.Where("channel_category_id = ?", query.ChannelCategoryId)
	}

	data := make([]ProProductStoreSpu, 0)
	out := make(map[string]ProProductStoreSpu)

	if err := session.Table("eshop.pro_product_store_spu").Find(&data); err != nil {
		return nil, nil, err
	}

	for _, v := range data {
		k := fmt.Sprintf("%d_%s_%d", v.ChannelId, v.StoreId, v.ProductId)
		out[k] = v
	}
	return out, data, nil
}

func (m ProProductStoreSpu) UpdateProductStoreSpu(db *xorm.Engine, where map[string]interface{}, cols string) (err error) {
	session := db.NewSession()
	defer session.Close()

	id, ok := where["id"]
	if ok {
		session = session.Where("id=?", id)
	}

	productId, ok := where["productId"]
	if ok {
		session = session.Where("product_id=?", productId)
	}

	productIds, ok := where["productIds"]
	if ok {
		session = session.In("product_id", productIds)
	}

	channelId, ok := where["channelId"]
	if ok {
		session = session.Where("channel_id=?", channelId)
	}
	channelIds, ok := where["channelIds"]
	if ok {
		session = session.In("channel_id", channelIds)
	}

	storeId, ok := where["storeId"]
	if ok {
		session = session.Where("store_id=?", storeId)
	}

	storeIds, ok := where["storeIds"]
	if ok {
		session = session.In("store_id", storeIds)
	}
	if cols != "" {
		session = session.Cols(cols)
	}
	if _, err = session.Table("eshop.pro_product_store_spu").Update(&m); err != nil {
		log.Error("更新门店商品SPU表失败，err=", err.Error())
		return
	}

	return
}

type SpuInfo struct {
	ProProductStoreSpu `xorm:"extends"`
	Name               string `xorm:"name"`
	ChannelIdStr       string `xorm:"channel_id_str"`
}

// 根据storeId和productId查询SPU信息
// SELECT spu.*,p.name from eshop.pro_product_store_spu spu left join eshop.pro_product p on spu.product_id = p.id where spu.product_id in (111047,111051) and spu.channel_id =1 and spu.store_id='576534157590153975';
func (m ProProductStoreSpu) QuerySpuByStoreIdAndProductId(session *xorm.Session, storeId string, productId, channelId []int) (spu []SpuInfo, err error) {
	session = session.Table("eshop.pro_product_store_spu").Alias("spu").
		Select("spu.*,p.name,group_concat(spu.channel_id) as channel_id_str").
		Join("LEFT", "eshop.pro_product p", "spu.product_id = p.id").
		GroupBy("spu.product_id")
	session = session.In("spu.product_id", productId)
	if len(channelId) > 0 {
		session = session.In("spu.channel_id", channelId)
	}
	session = session.Where("spu.store_id = ?", storeId)

	if err = session.Find(&spu); err != nil {
		return
	}

	return
}
