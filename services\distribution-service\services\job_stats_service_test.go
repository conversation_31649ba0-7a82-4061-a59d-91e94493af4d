package services

import (
	"eShop/services/common"
	"eShop/view-model/distribution-vo"
	"testing"
)

func TestJobStatsService_StatsOrderDailyData(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req []distribution_vo.StatsShopDistributorDailyReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
		{
			name: "tesst",
			args: args{
				req: []distribution_vo.StatsShopDistributorDailyReq{
					{StartDate: "2024-05-01", EndDate: "2024-06-02"},
				}},
			fields: fields{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &JobStatsService{
				BaseService: tt.fields.BaseService,
			}
			s.StatsOrderDailyData(tt.args.req...)
		})
	}
}

func TestJobStatsService_StatsOrderDaliyData(t *testing.T) {
	s := JobStatsService{}
	//s.StatsOrderDaliyData()
	s.StatsOrderWeeklyData()
	//s.StatsOrderMonthlyData()
	//s.StatsOrderYearlyData()
}

func TestJobStatsService_StatsOrderDaliyDataRun(t *testing.T) {
	s := JobStatsService{}
	s.StatsOrderDaliyDataRun("2024-04-01", "2024-07-15")
	//s.StatsOrderWeeklyDataRun()
}
