package warehouse

import (
	"context"
	po "eShop/domain/inventory-po/warehouse"
	"eShop/services/common"
	vo "eShop/view-model/inventory-vo/warehouse"
)

type WarehouseService struct {
	common.BaseService
}

func NewWarehouseService() *WarehouseService {
	return &WarehouseService{}
}

// List 返回指定连锁下的所有仓库
func (s WarehouseService) GetByStoreId(ctx context.Context, storeId string) ([]vo.WarehouseVO, error) {
	s.<PERSON>()
	defer s.Close()

	warePo := po.Warehouse{}
	warehouses, err := warePo.GetByStoreId(ctx, s.Session, storeId)
	if err != nil {
		return nil, err
	}

	// 转换门店类型category（3-门店仓，4-加盟仓）
	for i, o := range warehouses {
		if o.Category == 3 {
			warehouses[i].CategoryName = "门店仓"
			warehouses[i].AuthorizedChainId = 0
			warehouses[i].AuthorizedChainName = ""
		}
		if o.Category == 4 {
			warehouses[i].CategoryName = "加盟仓"
		}
	}

	return warehouses, nil
}
