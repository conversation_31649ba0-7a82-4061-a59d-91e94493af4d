package services

import (
	"eShop/infra/log"
	"eShop/services/common"
	order_vo "eShop/view-model/order-vo"
	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestOrderService_GetOrderList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req order_vo.OrderListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []order_vo.OrderDetailVo
		want1   int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				req: order_vo.OrderListReq{
					OrderSn: "",
					// OrderType:   1,
					// OrderStatus: 1,
					OrgId:     2,
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewOrderService()

			got, got1, err := s.GetOrderList(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("OrderService.GetOrderList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderService.GetOrderList() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("OrderService.GetOrderList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
