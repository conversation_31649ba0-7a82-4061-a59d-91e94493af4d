package controllers

import (
	jwt "eShop/infra/jwtauth"
	"eShop/infra/response"
	"eShop/infra/utils"
	inventory "eShop/services/inventory-service/inventory"
	vo "eShop/view-model/inventory-vo/inventory"
	"net/http"

	"github.com/go-chi/chi/v5"
)

// InventoryController 库存控制器
type InventoryController struct {
	service inventory.InventoryService
}

// NewInventoryController 创建库存控制器
func NewInventoryController(service inventory.InventoryService) *InventoryController {
	return &InventoryController{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (c InventoryController) RegisterRoutes(r chi.Router) {
	r.Route("/inventory-app/inventory", func(r chi.Router) {
		r.Post("/page", c.Page)
		r.Post("/create", c.Create)
		r.Post("/warning", c.WarningPage)
	})

	r.Route("/inventory-app/inventory-flow", func(r chi.Router) {
		r.Post("/page", c.FlowPage)
	})
}

// Page 库存分页
// @Summary 库存分页
// @Description 获取库存分页数据
// @Tags 库存
// @Accept json
// @Produce json
// @Param page body vo.InventoryPageRequest true "分页请求"
// @Success 200 {object} response.Response[[]vo.InventoryResponse] "成功获取库存分页数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/inventory/page [post]
func (c InventoryController) Page(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.InventoryPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.service.Page(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// @Summary 创建库存
// @Description 创建库存
// @Tags 库存
// @Accept json
// @Produce json
// @Param inventory body vo.InventoryInitCommand true "创建库存请求"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/inventory/create [post]
func (c InventoryController) Create(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.InventoryInitCommand](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	cmd.ChainId = jwt.CtxGet[int64](r.Context(), "ChainId")
	cmd.StoreId = jwt.CtxGet[string](r.Context(), "TenantId")
	err = c.service.BatchCreate(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// @Summary 库存流水分页
// @Description 库存流水分页
// @Tags 库存流水
// @Accept json
// @Produce json
// @Param page body vo.InventoryFlowPageRequest true "分页请求"
// @Success 200 {object} response.Response[[]vo.InventoryFlowResponse] "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/inventory-flow/page [post]
func (c InventoryController) FlowPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.InventoryFlowPageRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.service.FlowPage(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// @Summary 库存缺货预警
// @Description 获取库存缺货预警分页数据 v1.6.0
// @Tags 库存
// @Accept json
// @Produce json
// @Param req body vo.InventoryWarningRequest true "缺货预警请求"
// @Success 200 {object} response.Response[[]vo.InventoryWarningResponse] "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /inventory-app/inventory/warning [post]
func (c InventoryController) WarningPage(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.InventoryWarningRequest](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}
	page, total, err := c.service.WarningPage(r.Context(), cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}
	response.SuccessWithPage(w, page, int(total))
}
