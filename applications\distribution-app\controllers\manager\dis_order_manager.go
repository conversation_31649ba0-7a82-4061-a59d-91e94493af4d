package manager

import (
	tasklist "eShop/infra/enum"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	viewmodel "eShop/view-model"
	distribution_vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"

	"net/http"

	"github.com/spf13/cast"
)

/**
管理后台 - 分销订单管理
*/

// @Summary 获取分销订单列表接口 @fuma-v1.3
// @Tags 后台接口-分销订单
// @Accept  plain
// @Produce  json
// @Param GetDisOrderListReq query distribution_vo.GetDisOrderListReq true " "
// @Success 200 {object} distribution_vo.GetDisOrderListRes
// @Failure 400 {object} distribution_vo.GetDisOrderListRes
// @Router /manager/dis/order/list [GET]
func GetDisOrderList(writer http.ResponseWriter, request *http.Request) {
	var err error
	out := distribution_vo.GetDisOrderListRes{}
	out.Code = 400
	orgId := cast.ToInt(request.Header.Get("org_id"))
	param, err := utils.Bind[distribution_vo.GetDisOrderListReq](request)
	param.OrgId = orgId
	if err != nil {
		log.Errorf("获取分销订单列表-解析参数失败-错误为%s", err.Error())
		out.Message = "解析参数失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	server := services.DisOrderService{}
	if out.Data, out.Total, err = server.GetDisOrderList(param); err != nil {
		log.Errorf("获取分销订单列表失败-错误为%s", err.Error())
		out.Message = "获取分销订单列表失败"
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	out.Code = 200
	out2, _ := json.Marshal(out)
	writer.Write(out2)

}

// @Summary 导出分销订单列表
// @Tags 后台接口-分销订单
// @Accept  json
// @Produce  json
// @Param GetDisOrderListReq body distribution_vo.GetDisOrderListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/order/export [POST]
func DisOrderExport(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400

	param, err := utils.Bind[distribution_vo.GetDisOrderListReq](request)
	param.OrgId = cast.ToInt(request.Header.Get("org_id"))
	if err != nil {
		log.Errorf("导出分销订单列表-参数解析失败-err=%s", err.Error())
		out.Message = fmt.Sprintf("导出分销订单列表，参数解析失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	s := common.TaskListService{}
	var task distribution_vo.TaskList
	par, _ := json.Marshal(param)
	task.OperationFileUrl = string(par)
	task.OrgId = cast.ToInt(request.Header.Get("org_id"))
	task.TaskContent = tasklist.TaskContentDisOrderExport
	err = s.CreatTask(request, task)
	if err != nil {
		log.Errorf("导出分销订单失败：err=%s", err.Error())
		out.Message = fmt.Sprintf("导出分销订单：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200

	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}

// DisOrderDetailExport
// @Summary 分销订单列表导出订单数据（含商品明细）
// @Tags 后台接口-分销订单
// @Accept  json
// @Produce  json
// @Param GetDisOrderListReq body distribution_vo.GetDisOrderListReq true " "
// @Success 200 {object} viewmodel.BaseHttpResponse
// @Failure 400 {object} viewmodel.BaseHttpResponse
// @Router /manager/dis/order/export-detail [POST]
func DisOrderDetailExport(writer http.ResponseWriter, request *http.Request) {
	out := viewmodel.BaseHttpResponse{}
	out.Code = 400

	param, err := utils.Bind[distribution_vo.GetDisOrderListReq](request)
	param.OrgId = cast.ToInt(request.Header.Get("org_id"))
	if err != nil {
		log.Errorf("导出分销订单列表-参数解析失败-err=%s", err.Error())
		out.Message = fmt.Sprintf("导出分销订单列表，参数解析失败：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}
	s := common.TaskListService{}
	var task distribution_vo.TaskList
	par, _ := json.Marshal(param)
	task.OperationFileUrl = string(par)
	task.OrgId = cast.ToInt(request.Header.Get("org_id"))
	task.TaskContent = tasklist.TaskContentDisOrderDetailExport
	err = s.CreatTask(request, task)
	if err != nil {
		log.Errorf("导出分销订单失败：err=%s", err.Error())
		out.Message = fmt.Sprintf("导出分销订单：%s", err.Error())
		out2, _ := json.Marshal(out)
		writer.Write(out2)
		return
	}

	out.Code = 200

	bytes, _ := json.Marshal(out)
	writer.Write(bytes)
}
