package points_po

import "time"

// ClsPointsFlow 对应 cls_points_flow 表的持久化对象
type ClsPointsFlow struct {
	SimpleEntity[int] `xorm:"extends"` // 主键ID, CreatedAt
	DisId             int              `xorm:"'dis_id' notnull index(idx_dis_id)" json:"dis_id"`
	EnterpriseId      int              `xorm:"'enterprise_id' notnull index(idx_enterprise_id)" json:"enterprise_id"`
	Type              int              `xorm:"'type' notnull index(idx_type)" json:"type"`
	BizType           int              `xorm:"'biz_type' notnull" json:"biz_type"`
	Status            int              `xorm:"'status' notnull default 1" json:"status"`
	Points            int              `xorm:"'points' notnull default 0" json:"points"`
	RemainingPoints   int              `xorm:"'remaining_points' notnull default 0" json:"remaining_points"`
	ExpireTime        time.Time        `xorm:"'expire_time' DATETIME null" json:"expire_time,omitempty"`
	BillId            int              `xorm:"'bill_id' notnull default 0" json:"bill_id"`
	BillOrderNo       string           `xorm:"'bill_order_no' varchar(32) notnull default ''" json:"bill_order_no"`
	BillGoodsCode     string           `xorm:"'bill_goods_code' varchar(32) notnull default ''" json:"bill_goods_code"`
	PointsOrderId     int              `xorm:"'points_order_id' notnull default 0" json:"points_order_id"`
	PointsBlky        int              `xorm:"'points_blky' notnull default 0" json:"points_blky"`
	PointsSzld        int              `xorm:"'points_szld' notnull default 0" json:"points_szld"`
	Region            string           `xorm:"'region' varchar(50) null" json:"region,omitempty"`
	Remark            string           `xorm:"'remark' varchar(255) null" json:"remark,omitempty"`
	Operator          string           `xorm:"'operator' varchar(50) null" json:"operator,omitempty"`
	StaffNo           string           `xorm:"'staff_no' varchar(50) null" json:"staff_no,omitempty"`
	OccurTime         time.Time        `xorm:"'occur_time' DATETIME notnull" json:"occur_time"`
	CreatedAt         time.Time        `xorm:"created 'created_at'" json:"created_at"`
}

// TableName 指定数据库中的表名
func (e ClsPointsFlow) TableName() string {
	return "cls_points_flow"
}

// AsPointer 返回实体对象的指针，用于 SuperService 泛型约束
func (e ClsPointsFlow) AsPointer() any {
	return &e
}
