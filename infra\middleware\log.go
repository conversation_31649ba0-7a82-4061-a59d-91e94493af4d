package middleware

import (
	"eShop/infra/utils"
	"fmt"
	"net/http"
	"time"
)

func WithLogger(handle http.Handler) http.Handler {
	return http.HandlerFunc(func(writer http.ResponseWriter, request *http.Request) {
		startTime := time.Now()
		defer func() {
			fmt.Println(request.URL.Path, "end于", time.Since(startTime).Seconds())
		}()

		fmt.Println(request.URL.Path, "begin于", startTime.Format(utils.DateTimeLayout))
		handle.ServeHTTP(writer, request)
	})
}
