package utils

var (
	//门店设置为营业状态
	MtShopSetOpenUrl = "poi/open"

	//获取门店ID
	MtGetShopId = "poi/getids"

	//门店设置为休息状态
	MtShopSetCloseUrl = "poi/close"

	//获取门店品类列表
	MtStoreCategory = "poiTag/list"

	//批量获取门店详细信息
	MtStoreMget = "poi/mget"
	//更新门店信息
	MtStoreSave = "poi/save"

	// 门店设置营业状态
	MtStoreOpen = "poi/open"

	// 门店设置休息状态
	MtStoreClose = "poi/close"

	//获取门店配送范围（自配送）
	MtDeliveryList = "shipping/list"
	//获取门店配送范围（自配送）  普通
	MtDeliveryUpdate = "shipping/save"
	//获取门店配送范围（自配送）  特殊时段
	MtDeliverySpecUpdate = "shipping/spec/save"
	//删除门店配送范围（自配送）
	MtDeliveryDelete = "shipping/delete"
)

var (
	//获取美团订单详细信息
	MtOrderDetail = "order/getOrderDetail"

	// order/preparationMealComplete 商家确认已完成拣货
	MtOrderPreparationMealComplete = "order/preparationMealComplete"

	//自配送商家同步发货状态和配送信息
	MtOrderLogisticsSyncApi = "ecommerce/order/logistics/sync"

	//自配订单配送中（不支持同步骑手信息）
	MtOrderDeliveringApi = "order/delivering"

	//自配订单已送达
	MtOrderArrivedApi = "order/arrived"

	//查询订单状态
	MtOrderViewStatusApi = "order/viewstatus"

	//商家确认美团订单
	MtOrderConfirmApi = "order/confirm"

	//商家商家取消订单
	MtOrderCancelApi = "order/cancel"

	//拉取用户真实手机号
	MtPullPhoneNumber = "order/batchPullPhoneNumber"
	//拉取骑手真实手机号
	MtRiderInfoPhoneNumber = "order/getRiderInfoPhoneNumber"

	//订单确认退款请求
	MtOrderRefundAgree = "order/refund/agree"
	//驳回订单退款申请
	MtOrderRefundReject = "order/refund/reject"

	/// order/getPartRefundFoods 查询可被部分退款的商品详情
	MtOrderGetPartRefundFoods = "order/getPartRefundFoods"
	/// order/applyPartRefund 发起部分退款
	MtOrderApplyPartRefund = "order/applyPartRefund"
	/// ecommerce/order/getOrderRefundDetail 获取订单退款记录
	MtOrderRefundDetail = "ecommerce/order/getOrderRefundDetail"

	// 查询可申请货损赔付的订单
	MtOrderGetSupportedCompensationApi = "order/getSupportedCompensation"
	//查询货损赔付结果
	MtOrderGetCompensationResultApi = "order/getCompensationResult"
	//批量查询客服赔付商家责任订单信息
	MtOrderBatchCompensationApi = "order/batchCompensationOrder"

	//批量拉取异常订单
	MtBatchFetchAbnormalOrder = "order/batchFetchAbnormalOrder"
	//申请货损赔付
	MtApplyCompensation = "order/applyCompensation"
	//下发美团配送订单
	MtLogisticsPush = "order/logistics/push"
	//取消美团配送订单
	MtLogisticsCancel = "order/logistics/cancel"
	//批量查询众包配送费
	MtZhongbaoShippingFee = "order/zhongbao/shippingFee"
	//众包发配送
	MtZhongbaoDispatch = "order/zhongbao/dispatch"
	//获取取消跑腿配送原因列表
	MtGetCancelDeliveryReason = "order/getCancelDeliveryReason"
	//取消跑腿配送
	MtCancelLogisticsByWmOrderId = "order/cancelLogisticsByWmOrderId"
	//售后单（退款/退货退款）审核接口
	MtReviewAfterSales = "ecommerce/order/reviewAfterSales"
)

var (

	///lei start
	// 商品批量更新售卖（上下架）状态（20次/秒，可上传商品数据限定不超过200组）
	MtRetailSellStatus = "retail/sellStatus"
	// 商品查询门店商品分类列表（5次/秒）
	MtRetailCatList = "retailCat/list"
	/// retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑] 当前接口最高调用20次/秒 （20次/秒。可传商品数据限定不能超过200组）
	MtRetailBatchinitdata = "retail/batchinitdata"

	///    retail/initdata 创建/更新商品[支持商品多规格,不含删除逻辑]   50次/S
	MtRetailInitdata = "retail/initdata"

	// retail/multipois/batchinitdata 批量创建/更新商品信息至多店 （1次/5分钟，200个商品，最多可同步至3000个门店）
	MtMultipoisBatchinitdata = "retail/multipois/batchinitdata"

	/// retailCat/update 创建/更新商品分类 当前接口最高调用5次/秒
	MtRetailCatUpdate = "retailCat/update"

	/// retail/getSpTagIds 获取美团后台商品类目（末级类目id） 20次/秒
	MtRetailGetSpTagIds = "retail/getSpTagIds"

	///category/attr/value/list 查询特殊属性的属性值列表
	MtCategoryAttrValueListApi = "gw/category/attr/value/list"
	// category/attr/list 根据末级类目id获取类目属性列表
	MtGwCategoryAttrListApi = "gw/category/attr/list"
	// retail/sku/delete 删除SKU信息 50次/秒
	MtRetailSkuDelete = "retail/sku/delete"
	// /retailCat/batchdelete/catandretail 按app维度，当前接口最高调用5次/秒。
	MtRetailSkuBatchDelete = "retailCat/batchdelete/catandretail"

	/// retail/sku/stock 批量更新SKU库存  20次/秒,可传商品数据（app_food_code维度）限定不超过200组
	MtRetailSkuStock = "retail/sku/stock"
	/// task/status 查询多店同步任务的进程
	MtTaskStatus = "task/status"
	/// lei end
	//retailCat/delete 删除商品分类
	MtRetailCatDeleteApi = "retailCat/delete"
	//批量更新SKU价格
	MtUpdateSkuPrice = "retail/sku/price"
	// retail/get 查询商品详情 50次/秒
	MtRetailGet = "retail/get"

	// retail/get 查询商品详情 50次/秒
	MtRetailBatchGet = "retail/batchget"
	// 查询门店商品列表 最高调用20次/秒
	MtRetailList = "retail/list"
)
