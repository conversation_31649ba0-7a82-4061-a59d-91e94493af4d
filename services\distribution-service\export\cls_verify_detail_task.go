package export

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type ClsVerifyDetailTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.ClsVerifyPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

func (t *ClsVerifyDetailTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	// 解析导出参数
	t.ExportParams = new(vo.ClsVerifyPageReq)
	err = json.Unmarshal([]byte(taskParams), t.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	// 初始化流式写入器
	t.writer, err = t.F.NewStreamWriter(t.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	// 设置表头
	t.SetSheetName()

	// 设置分页参数
	t.ExportParams.PageIndex = 1
	t.ExportParams.PageSize = 10000 // 每次查询的最大数量

	// 创建服务实例
	clsService := services.ClsVerifyService{}

	k := 0
	for {
		// 获取数据
		records, _, err := clsService.GetClsVerifyPage(*t.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}

		t.ExportParams.PageIndex += 1

		// 写入数据
		for i := 0; i < len(records); i++ {
			k++
			axis := fmt.Sprintf("A%d", k+1)
			//VerifyType 1: 手动输入 2: 扫码查询
			verifyType := "手动输入"
			if records[i].VerifyType == 2 {
				verifyType = "扫码查询"
			}
			err = t.writer.SetRow(axis, []interface{}{
				records[i].CompanyName,    // 组织名称
				records[i].Code,           // 防伪码
				records[i].VerifyTime,     // 查询时间
				records[i].UserOpenid,     // 用户Openid
				records[i].UserId,         // 用户会员id
				records[i].UserPhone,      // 用户手机号
				verifyType,                // 查询方式
				records[i].VerifyDesc,     // 查询次数描述
				records[i].VerifyLocation, // 查询所在地
				records[i].VerifyEntry,    // 查询入口
			})

			if err != nil {
				log.Error("写入Excel行数据失败:", err)
				failNum++
			}
		}

		// 如果返回的数据少于页大小，说明已经是最后一页
		if len(records) < int(t.ExportParams.PageSize) {
			break
		}
	}

	successNum = k
	err = t.writer.Flush()
	return
}

func (t *ClsVerifyDetailTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"码包所属公司", "防伪码", "查询时间",
		"用户Openid", "用户会员id", "用户手机号",
		"查询方式", "查询次数", "查询所在地", "查询入口",
	}
	_ = t.writer.SetRow("A1", nameList)
	t.SheetName = "防伪码验证记录"
}

func (t *ClsVerifyDetailTask) GenerateDownUrl() (string, error) {
	fileName := fmt.Sprintf("防伪码验证记录(%s%d).xlsx",
		time.Now().Format("20060102150405"),
		time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(t.F, fileName)
}

func (t *ClsVerifyDetailTask) OperationFunc(row []string, orgId int) string {
	return ""
}
