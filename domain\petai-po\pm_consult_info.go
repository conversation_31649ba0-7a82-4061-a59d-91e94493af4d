package petai_po

import (
	"errors"
	"time"

	"xorm.io/xorm"
)

func (m *PmConsultInfo) TableName() string {
	return "pet_medical.pm_consult_info"
}

type PmConsultInfo struct {
	Id                          int64
	OrderSn                     int64
	DoctorCode                  string
	ConsultStatus               int32
	SummaryStatus               int32
	ConsultType                 int32 // 咨询类型1-在线咨询，2-快速问诊，3-在线问诊
	ConsultModel                int32
	ConsultDuration             int32
	OldConsultDuration          int32
	Amount                      int32
	NeedPrescription            int32
	MakePrescriptionStatus      int32
	MakePrescriptionExpiredTime time.Time // 开方失效时间
	DoctorJoin                  time.Time
	DoctorFirstReplyTime        time.Time
	PlanEndTime                 time.Time
	FinishTime                  time.Time
	MemberId                    string
	MemberName                  string
	MemberAvatar                string
	MemberMobile                string
	PetId                       string // 宠物id
	PetAvatar                   string
	PetName                     string
	PetKindof                   string
	PetVariety                  string
	PetBirthday                 string
	PetAge                      string
	PetWeight                   string
	PetNeutering                int32
	PetSex                      int32
	ImmuneStatus                int32
	BathFreq                    int32
	FeedWay                     int32
	SymptomType                 string
	Symptom                     string
	SymptomRecent               int32
	SymptomImage                string
	IsEvaluate                  int32
	EasemobGroupId              string
	KeepWaitingAt               string
	IsTest                      int32
	CreatedAt                   time.Time
	UpdatedAt                   time.Time
	ServicePackSignId           int64 `json:"service_pack_sign_id"`    //服务包的签约ID
	ConsultConversationId       int64 `json:"consult_conversation_id"` //会话id
}

func (m *PmConsultInfo) GetConsultInfoByrderSn(session *xorm.Session, pmOrderSn int64) (out *PmConsultInfo, err error) {
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if pmOrderSn <= 0 {
		err = errors.New("订单号不能为空")
		return
	}

	session.Table("pet_medical.pm_consult_info").Alias("a").
		Where("a.order_sn=?", pmOrderSn)
	out = new(PmConsultInfo)
	exists, err := session.Get(out)
	if err != nil {
		return
	}
	if !exists {
		err = errors.New("问诊单不存在")
		return
	}
	return
}
