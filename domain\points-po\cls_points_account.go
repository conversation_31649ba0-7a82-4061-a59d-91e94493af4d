package points_po

// For CreatedAt, UpdatedAt if SuperEntity doesn't handle their type explicitly

// ClsPointsAccount 对应 cls_points_account 表的持久化对象
type ClsPointsAccount struct {
	SuperEntity[int] `xorm:"extends"` // 主键ID, CreatedAt, UpdatedAt
	DisId            int              `xorm:"'dis_id' notnull unique(idx_dis_id)" json:"dis_id"`
	EnterpriseId     int              `xorm:"'enterprise_id' notnull" json:"enterprise_id"`
	AvailablePoints  int              `xorm:"'available_points' notnull default 0" json:"available_points"`
	TotalPoints      int              `xorm:"'total_points' notnull default 0" json:"total_points"`
	ConsumePoints    int              `xorm:"'consume_points' notnull default 0" json:"consume_points"`
	ExpiredPoints    int              `xorm:"'expired_points' notnull default 0" json:"expired_points"`
	ExpiringPoints   int              `xorm:"'expiring_points' notnull default 0" json:"expiring_points"`
}

// TableName 指定数据库中的表名
func (e ClsPointsAccount) TableName() string {
	return "cls_points_account"
}

// AsPointer 返回实体对象的指针，用于 SuperService 泛型约束
func (e ClsPointsAccount) AsPointer() any {
	return &e
}
