package order_vo

type ScanHeadBase struct {
	// 商户号
	MercId string `protobuf:"bytes,1,opt,name=merc_id,json=mercId,proto3" json:"merc_id"`
	// 机构号
	OrgId string `protobuf:"bytes,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	// 传机具编号（tsn）
	TrmSn string `protobuf:"bytes,3,opt,name=trm_sn,json=trmSn,proto3" json:"trm_sn"`
	// 终端号，传标准终端绑定接口返回的dyTermNo
	TrmId string `protobuf:"bytes,4,opt,name=trm_id,json=trmId,proto3" json:"trm_id"`
}

type PayForB2CRequest struct {
	// 订单号
	MerOrderNo string `protobuf:"bytes,1,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	// 付款码
	BarCode string `protobuf:"bytes,2,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	// 支付方式 1：微信 2：支付宝 3: 银联
	PayType int32 `protobuf:"varint,3,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	// 实际支付金额
	PayAmount int32 `protobuf:"varint,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	// 不参与优惠金额
	UndiscountableAmount string `protobuf:"bytes,5,opt,name=undiscountable_amount,json=undiscountableAmount,proto3" json:"undiscountable_amount"`
	// 订单名称
	OrderName string `protobuf:"bytes,6,opt,name=order_name,json=orderName,proto3" json:"order_name"`
	// 订单描述
	OrderDesc string `protobuf:"bytes,7,opt,name=order_desc,json=orderDesc,proto3" json:"order_desc"`
	// 订单有效期单位 00-分 01-小时 02-日 03-月
	ValidUnit string `protobuf:"bytes,8,opt,name=validUnit,proto3" json:"validUnit"`
	// 订单有效期数量
	ValidNum string `protobuf:"bytes,9,opt,name=validNum,proto3" json:"validNum"`
	// 扫码请求头信息
	HeadBase *ScanHeadBase `protobuf:"bytes,13,opt,name=head_base,json=headBase,proto3" json:"head_base"`
	// 外部订单号
	OutOrderNo string `protobuf:"bytes,14,opt,name=out_order_no,json=outOrderNo,proto3" json:"out_order_no"`
	// 订单总金额
	TotalAmount int32 `protobuf:"varint,15,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	// 优惠金额
	Discount int32 `protobuf:"varint,16,opt,name=discount,proto3" json:"discount"`
	// 后台回调地址
	NotifyUrl string `protobuf:"bytes,17,opt,name=NotifyUrl,proto3" json:"NotifyUrl"`
	// 终端类型，默认15
	TermType string `protobuf:"bytes,18,opt,name=termType,proto3" json:"termType"`
	// 客户端 IP
	ClientIp string `protobuf:"bytes,19,opt,name=client_ip,json=clientIp,proto3" json:"client_ip"`
	// 应用 ID
	AppId int32 `protobuf:"varint,20,opt,name=app_id,json=appId,proto3" json:"app_id"`
	// 扩展字段
	ExtendInfo string `protobuf:"bytes,21,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info"`
	// 位置
	Location string `protobuf:"bytes,22,opt,name=location,proto3" json:"location"`
}

type PayForB2CResponse struct {
	//返回码
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//商户订单号
	MerOrderNo string `protobuf:"bytes,3,opt,name=mer_order_no,json=merOrderNo,proto3" json:"mer_order_no"`
	//订单总金额 单位：分
	PayAmount string `protobuf:"bytes,4,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//支付结果 S：成功 R：正在执行 F：失败
	PayResult string `protobuf:"bytes,5,opt,name=pay_result,json=payResult,proto3" json:"pay_result"`
	//电银流水号
	PayNo string `protobuf:"bytes,6,opt,name=pay_no,json=payNo,proto3" json:"pay_no"`
	//支付时间
	PayTime string `protobuf:"bytes,7,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//支付中心流水号
	TradeNo string `protobuf:"bytes,8,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
}

type Pay struct {
	//回调地址
	NotifyURL string `json:"notify_url" validate:"required"`
	//订单名称
	OrderName string `json:"order_name" validate:"required"`
	ProductId string `json:"product_id"`
	//商品描述
	ProductDesc string `json:"product_desc"`
	//订单号
	OrderNo string `json:"order_no" validate:"required"`
	//实付金额,单位：分
	PayAmount int64 `json:"pay_amount" validate:"required"`
	//总金额,单位：分
	PayTotal int64 `json:"pay_total" validate:"required"`
	//签名，互联网医疗和阿闻只有部分字段参与签名，如下：app_id=2&notify_url=https://123&order_name=test&order_no=123&pay_amount=1&pay_total=1&timestamp=1234567891234&secret=5fBgKs5UYD2t11PUzLxQqrRIBDwAwggEKAoIBAQDL2qWFfEVHQ8BAf8EBAMCBs
	Sign string `json:"sign" validate:"required"`
	//1：阿闻，2：子龙，3：R1，4：互联网,，5：SAAS
	AppId int32 `json:"app_id" validate:"required"`
	//1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准） 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付
	TransType int32 `json:"trans_type" validate:"required"`
	//1、竖屏B扫C时：传标准终端绑定接口返回的dyTermNo
	//2、B扫C（标准时）：传电银内部终端号
	TrmId string `json:"trm_id"`
	//微信 JSAPI 支付时必传
	OpenId string `json:"open_id"`
	//子商户公众账号 ID
	SubAppId string `json:"sub_app_id"`
	//1、竖屏B扫C时：传机具编号（tsn）
	//2、B扫C（标准时）：智能 POS 终端的机具编号
	TrmSn string `json:"trm_sn"`
	////交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType string `json:"order_pay_type"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extend_info"`
	//优惠金额,单位：分
	Discount int64 `json:"discount"`
	//付款码,用户用银联支付宝、微信生成的付款码
	BarCode string `json:"bar_code"`
	//商户号 (trans_type 为12时必传)
	MerchantId string `json:"merchant_id"`
	ClientIP   string `json:"client_ip"`
	//订单有限时间（分）
	ValidTime int32 `json:"valid_time"`
	//时间戳，用于签名（毫秒）
	Timestamp int64 `json:"timestamp"  validate:"required"`
	//前台回调地址  网银支付参数
	FrontUrl string `json:"front_url"`
	//应用渠道标识  App-Android  App-iOS H5 web   网银支付参数
	ChannelType string `json:"channel_type"`
	//产品类型  ENTERPRISE_BANK(企业网银)  QUICK_PAY(快捷) PERSONAL_BANK(个人网银)  COMPLEX_BANK(综合收银)  网银支付参数
	ProdType string `json:"prod_type"`
	//接口类型  0：跳转网页支付 1：直接接口支付  网银支付参数
	InterfaceType string `json:"interface_type"`
	//卡类型  0：借记卡  1：贷记卡  接口类型为直接接口支付时必传   网银支付参数
	CardType string `json:"card_type"`
	//银行编码 接口类型为直接接口支付时必传   网银支付参数
	BankCode string `json:"bank_code"`
}

type StandardPayResponse struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
	//商户订单号
	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	//支付金额 单位：分
	PayAmount int64 `protobuf:"varint,2,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	//第三方支付流水号
	ThirdPayNo string `protobuf:"bytes,3,opt,name=third_pay_no,json=thirdPayNo,proto3" json:"third_pay_no"`
	//结果 0： 未支付 1：成功 2：正在执行 3：失败
	Result string `protobuf:"bytes,4,opt,name=result,proto3" json:"result"`
	//支付时间
	PayTime string `protobuf:"bytes,5,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//支付中心订单号
	TradeNo string `protobuf:"bytes,6,opt,name=trade_no,json=tradeNo,proto3" json:"trade_no"`
	//支付方式 1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
	TransType int32 `protobuf:"varint,8,opt,name=trans_type,json=transType,proto3" json:"trans_type"`
}

type PayRequest struct {
	//订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 99助力订单
	OrderType int32 `json:"order_type"`
	//1：阿闻，2：子龙，3：R1，4：互联网 5：宠物SAAS
	AppId int32 `json:"app_id"`
	//订单号
	OrderSn string `json:"order_sn"`
	//微信 JSAPI 支付时必传
	OpenId string `json:"open_id"`
	//小程序的APPID
	SubAppId string `json:"sub_app_id"`
}

type WXPayResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    Data   `json:"data"`
}

type Data struct {
	OrderNo    string  `json:"order_no"`
	PayAmount  float64 `json:"pay_amount"`
	ThirdPayNo string  `json:"third_pay_no"`
	Result     string  `json:"result"`
	PayTime    string  `json:"pay_time"`
	TradeNo    string  `json:"trade_no"`
	Details    Details `json:"details"`
	TransType  int     `json:"trans_type"`
}

type Details struct {
	WxJsapi     WxJsapi `json:"wx_jsapi"`
	OrderID     string  `json:"order_id"`
	WxNative    *string `json:"wx_native"` // Assuming this can be null
	WxJsApp     *string `json:"wx_js_app"` // Assuming this can be null
	Bank        string  `json:"bank"`
	AliTradeNo  string  `json:"aliTradeNo"`
	BdOrderInfo *string `json:"bd_order_info"` // Assuming this can be null
}

type WxJsapi struct {
	AppID     string `json:"appId"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	PaySign   string `json:"paySign"`
	SignType  string `json:"signType"`
	TimeStamp string `json:"timeStamp"`
}
