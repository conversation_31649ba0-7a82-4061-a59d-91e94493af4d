package omnibus_vo

import (
	viewmodel "eShop/view-model"
	"time"
)

// SmsConfigRequest 短信配置请求
type SmsConfigRequest struct {
	// 店铺ID
	StoreId string `json:"store_id"`
	// 配置项数据列表
	Data []SmsConfigItem `json:"data"`
}

// ConfigValue 配置值
type ConfigValue struct {
	// 是否启用：0-禁用，1-启用
	IsEnabled int `json:"is_enabled"`
	// 过期天数
	ExpireDays int `json:"expire_days,omitempty"`
}

// SmsConfigResponse 短信配置响应
type SmsConfigResponse struct {
	viewmodel.BaseHttpResponse
	// 配置项数据列表
	Data []SmsConfigItem `json:"data"`
}

// SmsConfigItem 短信配置项
type SmsConfigItem struct {
	// 配置ID
	Id int `json:"id"`
	// 配置类型：store_card_expire-储值卡到期提醒，time_card_expire-次卡到期提醒，coupon_expire-优惠券到期提醒，
	// order_create-订单创建提醒，order_pay-订单支付提醒，order_refund-订单退款提醒，
	// pet_hosting-宠物寄养提醒，coupon_full_reduction-满减赠送通知，coupon_discount-折扣赠送通知
	ConfigType string `json:"config_type"`
	// 配置名称
	ConfigName string `json:"config_name"`
	// 模板编码
	TemplateCode string `json:"template_code"`
	// 是否启用：0-禁用，1-启用
	IsEnabled bool `json:"is_enabled"`
	// 是否设置日期：0-不设置，1-设置
	IsSetDate bool `json:"is_set_date" validate:"required"`
	// 过期天数，当IsEnabled为true且IsSetDate为true时必须设置为1-7天
	ExpireDays int `json:"expire_days" validate:"omitempty,min=1,max=7"`
}

// SmsRefundRequest 退单短信请求
type SmsRefundRequest struct {
	// 退款单号
	RefundSn string `json:"refund_sn" validate:"required"` // 退款单号
	// 订单号
	OrderSn string `json:"order_sn" validate:"required"` // 订单号
	// 手机号
	Mobile string `json:"mobile" validate:"required"` // 手机号
	// 退款金额(分)
	Amount int `json:"amount" validate:"required"` // 退款金额(分)
	// 客户名称
	CustomerName string `json:"customer_name"` // 客户名称
	// 店铺ID
	StoreId string `json:"store_id"` // 店铺ID
	// 连锁ID
	ChainId int `json:"chain_id"` // 连锁ID
}

// SmsRefundListRequest 退单短信列表请求
type SmsRefundListRequest struct {
	viewmodel.BasePageHttpRequest
	// 连锁ID
	ChainId int `json:"chain_id" form:"chain_id"`
	// 店铺ID
	StoreId string `json:"store_id" form:"store_id"`
	// 店铺名称
	StoreName string `json:"store_name" form:"store_name"`
	// 退款单号
	RefundSn string `json:"refund_sn" form:"refund_sn"`
	// 订单号
	OrderSn string `json:"order_sn" form:"order_sn"`
	// 退款状态：0-待审核，1-通过，2-拒绝
	RefundStatus int `json:"refund_status" form:"refund_status"`
	// 申请时间开始
	StartApplyTime string `json:"start_apply_time" form:"start_apply_time"`
	// 申请时间结束
	EndApplyTime string `json:"end_apply_time" form:"end_apply_time"`
	// 退款时间开始
	StartRefundTime string `json:"start_refund_time" form:"start_refund_time"`
	// 退款时间结束
	EndRefundTime string `json:"end_refund_time" form:"end_refund_time"`
}

// SmsRefundData 退款记录数据
type SmsRefundData struct {
	// 退款订单ID
	Id int64 `json:"id"`
	// 连锁ID
	ChainId int64 `json:"chain_id"`
	// 连锁名称
	ChainName string `json:"chain_name"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 店铺名称
	StoreName string `json:"store_name"`
	// 退款单号
	RefundSn string `json:"refund_sn"`
	// 订单号
	OrderSn int64 `json:"order_sn"`
	// 退款数量
	RefundCount int `json:"refund_count"`
	// 退款金额
	RefundAmount int `json:"refund_amount"`
	// 退款单价(元/条)
	RefundUnitPrice string `json:"refund_unit_price"`
	// 退款比例
	RefundRatio string `json:"refund_ratio"`
	// 申请时间
	ApplyTime string `json:"apply_time"`
	// 退款时间
	RefundTime string `json:"refund_time"`
	// 操作人
	Operator string `json:"operator"`
	// 审核人姓名
	AuditorName string `json:"auditor_name"`
	// 审核原因
	AuditReason string `json:"audit_reason"`
	// 审核时间
	AuditTime string `json:"audit_time"`
	// 退款状态：0-待审核，1-通过，2-拒绝
	RefundStatus int `json:"refund_status"`
	// 退款状态描述
	RefundStatusDesc string `json:"refund_status_desc"`
	// 备注信息
	Remark string `json:"remark"`
	// 退款凭证图片
	Images string `json:"images"` // 退款凭证图片
	// 创建时间
	CreatedTime string `json:"created_time"`
	// 订单信息
	OrderInfo *SmsOrderBriefInfo `json:"order_info,omitempty"`
}

// SmsOrderBriefInfo 短信订单简要信息
type SmsOrderBriefInfo struct {
	// 订单ID
	OrderId int64 `json:"order_id"`
	// 充值条数
	AmountNum int `json:"amount_num"`
	// 支付金额(分)
	PayAmount int `json:"pay_amount"`
	// 已使用条数
	UsedCount int `json:"used_count"`
	// 已退款条数
	RefundCount int `json:"refund_count"`
	// 剩余可用条数
	RemainCount int `json:"remain_count"`
	// 单价(元/条)
	UnitPrice string `json:"unit_price"`
	// 订单状态(1支付成功、2支付失败、3支付中)
	OrderStatus int `json:"order_status"`
	// 订单创建时间
	CreateTime string `json:"create_time"`
}

// SmsRefundListResponse 退单短信列表响应
//
//	type SmsRefundListResponse struct {
//		Data  []SmsRefundData `json:"data"`
//		Total int64           `json:"total"`
//	}
type SmsRefundListResponse struct {
	viewmodel.BasePageHttpResponse
	// 退单短信列表
	Data []SmsRefundData `json:"data"` //退单短信列表
}

// SmsRefundDetailResponse 退款记录详情响应
type SmsRefundDetailResponse struct {
	viewmodel.BaseHttpResponse
	// 退款详情数据
	Data SmsRefundData `json:"data"`
}

// SmsRefundAuditRequest 退款审核请求
type SmsRefundAuditRequest struct {
	// 退款记录ID
	Id int64 `json:"id" validate:"required"` // 退款记录ID
	// 审核状态：1-通过，2-拒绝
	RefundStatus int `json:"refund_status" validate:"required"` // 审核状态：1-通过，2-拒绝
	// 操作人
	Operator string `json:"operator"` // 操作人
	// 备注信息
	Remark string `json:"remark"` // 备注信息
	// 审核原因
	AuditReason string `json:"audit_reason"` // 审核原因
}

// SmsRefundAuditResponse 退款审核响应
type SmsRefundAuditResponse struct {
	viewmodel.BaseHttpResponse
}

// SmsOrderQueryRequest 短信订单查询请求
type SmsOrderQueryRequest struct {
	viewmodel.BasePageHttpRequest
	// 连锁ID
	ChainId string `json:"chain_id"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 订单状态(1支付成功、2支付失败、3支付中)
	OrderStatus int `json:"order_status"`
	// 下单时间开始
	StartTime string `json:"start_time"`
	// 下单时间结束
	EndTime string `json:"end_time"`
	// 支付时间开始
	PayStartTime string `json:"pay_start_time"`
	// 支付时间结束
	PayEndTime string `json:"pay_end_time"`
	// 来源0 SAAS  1 miss
	Source int `json:"source"`
}

// SmsOrderResponse 短信订单响应
type SmsOrderResponse struct {
	viewmodel.BasePageHttpResponse
	Data struct {
		// 订单列表
		OrderList []SmsOrderDetailData `json:"order_list"`
		// 订单统计
		Statistics SmsOrderStats `json:"statistics"`
	} `json:"data"`
}

// SmsOrderItem 短信订单项
type SmsOrderItem struct {
	// 订单ID
	Id int64 `json:"id"`
	// 连锁ID
	ChainId int64 `json:"chain_id"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 充值条数
	AmountNum int `json:"amount_num"`
	// 支付金额
	PayAmount int `json:"pay_amount"`
	// 下单时间
	OrderTime time.Time `json:"order_time"`
	// 支付时间
	PayTime time.Time `json:"pay_time"`
	// 操作人
	Operator string `json:"operator"`
	// 订单状态(1支付成功、2支付失败、3支付中)
	OrderStatus int `json:"order_status"`
	// 使用次数
	UsedCount int `json:"used_count"`
	// 退款金额
	RefundAmount int `json:"refund_amount"`
	// 退款次数
	RefundCount int `json:"refund_count" xorm:"INT 'refund_count'"`
}

// SmsOrderStats 短信订单统计
type SmsOrderStats struct {
	// 充值成功条数
	SuccessCount int `json:"success_count"`
	// 充值成功金额
	SuccessAmount int `json:"success_amount"`
	// 退款条数
	RefundACount int `json:"refund_count"`
	// 退款金额
	RefundAmount int `json:"refund_amount"`
	// 使用条数
	UsedCount int `json:"used_count"`
	// 剩余条数
	SurplusCount int `json:"surplus_count"`
}

// SmsSendRecordQueryRequest 短信发送记录查询请求
type SmsSendRecordQueryRequest struct {
	viewmodel.BasePageHttpRequest
	// 连锁ID
	ChainId string `json:"chain_id"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 手机号
	Mobile string `json:"mobile"`
	// 短信类型(支付通知、营销短信等)
	SmsType int `json:"sms_type"`
	// 推送时间开始
	StartTime string `json:"start_time"`
	// 推送时间结束
	EndTime string `json:"end_time"`
	// 来源0 SAAS  1 miss
	Source int `json:"source"`
}

// SmsSendRecordResponse 短信发送记录响应
type SmsSendRecordResponse struct {
	viewmodel.BasePageHttpResponse
	Data struct {
		// 发送记录列表
		Records []SmsSendRecordItem `json:"records"`
		// 订单统计
		Statistics SmsOrderStats `json:"statistics"`
	} `json:"data"`
}

// SmsSendRecordItem 短信发送记录项
type SmsSendRecordItem struct {
	// 记录ID
	Id int64 `json:"id"`
	// 手机号
	Mobile string `json:"mobile"`
	// 短信类型
	SmsType int `json:"sms_type"`
	// 短信内容
	Content string `json:"content"`
	// 发送状态：0-失败，1-成功
	SendStatus int `json:"send_status"`
	// 发送时间
	SendTime time.Time `json:"send_time"`
	// 重试次数
	RetryCount int `json:"retry_count"`
	// 加密手机号
	EnMobile string `json:"en_mobile"`
	// 连锁名称
	ChainName string `json:"chain_name"`
	// 店铺名称
	StoreName string `json:"store_name"`
}

// SmsOrderCreateRequest 创建短信订单请求
type SmsOrderCreateRequest struct {
	// 连锁ID
	ChainId string `json:"chain_id" `
	// 店铺ID
	StoreId string `json:"store_id" `
	// 充值条数
	AmountNum int `json:"amount_num" validate:"required,min=1"`
	// 支付金额 单位分
	PayAmount int `json:"pay_amount" validate:"required"`
	// 操作人姓名
	Operator string `json:"operator" `
	// 操作人ID
	OperatorId int64 `json:"operator_id" `
	// 支付截图
	PayImage string `json:"pay_image" `
	// 备注
	Remark string `json:"remark" `
}

// SmsSendRecordCreateRequest 创建短信发送记录请求
type SmsSendRecordCreateRequest struct {
	// 连锁ID
	ChainId int64 `json:"chain_id" `
	// 店铺ID
	StoreId string `json:"store_id" `
	// 手机号
	Mobile string `json:"mobile" validate:"required"`
	// 短信类型
	SmsType int `json:"sms_type" `
	// 短信内容
	Content string `json:"content" `
	// 重试次数
	RetryCount int `json:"retry_count"`
}

// SmsOrderDetailRequest 短信订单详情请求
type SmsOrderDetailRequest struct {
	// 订单ID
	OrderId int64 `json:"order_id" validate:"required"`
}

// SmsOrderDetailResponse 短信订单详情响应
type SmsOrderDetailResponse struct {
	// 订单详情数据
	Data SmsOrderDetailData `json:"data"`
}

// SmsOrderDetailData 短信订单详情数据
type SmsOrderDetailData struct {
	// 订单编号
	Id int64 `json:"id"`
	// 连锁Id
	ChainId int64 `json:"chain_id"`
	// 连锁名称
	ChainName string `json:"chain_name"`
	// 店铺ID
	StoreId string `json:"store_id"`
	// 店铺名称
	StoreName string `json:"store_name"`
	// 充值条数
	AmountNum int `json:"amount_num"`
	// 支付金额
	PayAmount int `json:"pay_amount"`
	// 下单时间
	OrderTime time.Time `json:"order_time"`
	// 支付时间
	PayTime time.Time `json:"pay_time"`
	// 支付截图
	PayImage string `json:"pay_image"`
	// 操作人
	Operator string `json:"operator"`
	// 订单状态(1支付成功、2支付失败、3支付中)
	OrderStatus int `json:"order_status"`
	// 使用次数
	UsedCount int `json:"used_count"`
	// 退款金额
	RefundAmount int `json:"refund_amount"`
	// 退款次数
	RefundCount int `json:"refund_count"`
	// 备注
	Remark string `json:"remark"`
	// 创建时间
	CreateTime time.Time `json:"create_time"`
	// 单价(分/条)
	UnitPrice int `json:"unit_price" xorm:"INT 'unit_price'"`
}

// 获取短信订单详情
type SmsOrderGetRequest struct {
	// 订单编号
	Id string `json:"id"`
}

type SmsSendMessageRequest struct {
	// 店铺ID
	StoreId string `json:"store_id" xorm:"not null VARCHAR(64) 'store_id'"`
	// 接收手机号
	Mobile string `json:"mobile" validate:"required"`
	// 短信模板编码
	TemplateCode string `json:"template_code,omitempty" validate:"required"`
	// 短信模板参数(JSON格式)
	TemplateParam string `json:"template_param,omitempty" validate:"required"`
}

// SmsReportRequest 短信回执请求
type SmsReportRequest struct {
	// 手机号码
	PhoneNumber string `json:"phone_number"` // 手机号码
	// 发送时间
	SendTime string `json:"send_time"` // 发送时间
	// 状态报告时间
	ReportTime string `json:"report_time"` // 状态报告时间
	// 是否接收成功
	Success bool `json:"success"` // 是否接收成功
	// 状态报告编码
	ErrCode string `json:"err_code"` // 状态报告编码
	// 状态报告说明
	ErrMsg string `json:"err_msg"` // 状态报告说明
	// 短信长度
	SmsSize string `json:"sms_size"` // 短信长度
	// 发送回执ID
	BizId string `json:"biz_id"` // 发送回执ID
	// 用户序列号
	OutId string `json:"out_id"` // 用户序列号
}

// SendVerifyCodeRequest 发送验证码请求
type SendVerifyCodeRequest struct {
	// 手机号
	Mobile string `json:"mobile" validate:"required"` // 手机号
	// 验证码类型:1-登录验证,2-支付验证
	Type int `json:"type" validate:"required"` // 验证码类型:1-登录验证,2-支付验证
	// 店铺ID(支付验证必填)
	StoreId string `json:"store_id"` // 店铺ID(支付验证必填)
}

// VerifyCodeRequest 验证码验证请求
type VerifyCodeRequest struct {
	// 手机号
	Mobile string `json:"mobile" validate:"required"` // 手机号
	// 验证码
	Code string `json:"code" validate:"required"` // 验证码
	// 验证码类型:1-登录验证,2-支付验证
	Type int `json:"type" validate:"required"` // 验证码类型:1-登录验证,2-支付验证
}

// SmsRefundApplyRequest 短信退款申请请求
type SmsRefundApplyRequest struct {
	ChainId      int64  `json:"-"`                                 // 连锁ID(从JWT获取)
	StoreId      string `json:"-"`                                 // 店铺ID(从JWT获取)
	OrderId      int64  `json:"order_id" validate:"required"`      // 订单ID
	RefundCount  int    `json:"refund_count" validate:"required"`  // 退款条数
	RefundAmount int    `json:"refund_amount" validate:"required"` // 退款金额(分)
	Remark       string `json:"remark"`                            // 退款说明
	Images       string `json:"images"`                            // 退款凭证图片(多张用逗号分隔)
	Operator     string `json:"operator"`                          // 退款凭证图片(多张用逗号分隔)
}

type TaskDoRequest struct {
	//1.优惠券过期通知
	//2.储值卡过期通知
	//3.次卡过期通知
	Type int `json:"type" `
}
