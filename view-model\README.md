## 子模块使用说明
- 第一次clone带子模块的项目
```shell script
git clone --recurse-submodules https://gitlab.rvet.cn/s2b2c/view-model.git
git clone --recurse-submodules ******************:s2b2c/view-model.git
```

- 如果第一次没有clone子模块，下载完成后，但是子模块并没有下载下来，使用下面的命令
```shell script
git submodule update --init --recursive
```

- 过了一段时间，子模块更新了，需要重新更新子模块，一个命令解决
```shell script
git submodule foreach git pull origin master
```

- 子模块更新
```shell script
git submodule update --remote
```

- 更新所有子模块
```shell script
git submodule foreach git submodule update
```

- 如果已经克隆了项目并忘记了 ，则可以通过运行 来组合 和 步骤。为了还可以初始化、提取和签出任何嵌套子模块，可以使用万无一失的子模块。
```shell script
--recurse-submodules git submodule init git submodule updategit submodule update --initgit submodule update --init --recursive
```

- 为没有子模块的项目，添加子模块
```shell script
git submodule add https://gitlab.rvet.cn/s2b2c/view-model.git
```

- 如果项目报错，提示 'proto' already exists in the index，说明proto属于该项目的子目录，需要移除
```shell script
git rm -r --cached proto
```
移除后，再手动将proto改个别名，然后再重新添加子模块的引用

## 常用命令
```shell script
git submodule init
git submodule update
git submodule update --remote
git submodule update --init --recursive
```

## 各项目的Token
- 电商的Token
```
{
  "ChainId": "576534157590153849",
  "SourceChainId": "576534157590153849",
  "TenantId": "576534157590153850",
  "TenantIds": "",
  "exp": 1747997398,
  "member_id": 10000218,
  "mobile": "18142651001",
  "name": "18142651001",
  "openid": "",
  "org_id": 0,
  "role_type": 1,
  "scrmid": "572116010632043589",
  "unionid": ""
}
```

- 阿闻的Token
```


```

- Saas的Token
```
{
  "TenantId": "576534157590153850",
  "Uuid": "712935fbb6c042efb89815e692308dda",
  "UserId": "572116010632043589",
  "SourceChainId": "576534157590153849",
  "CustomerId": "0",
  "TenantIds": "",
  "EmployeeId": "576534157590153857",
  "ChainId": "576534157590153849",
  "iat": 1747876527,
  "nbf": 1747876527,
  "exp": 1750468527
}
```
