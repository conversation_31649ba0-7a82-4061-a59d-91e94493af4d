package services

import (
	po "eShop/domain/upetmart-po"
	"eShop/infra/log"
	"eShop/services/common"
	vo "eShop/view-model/distribution-vo"

	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
)

func TestVoucherService_GetVoucherList(t *testing.T) {
	type fields struct {
		BaseService common.BaseService
	}
	type args struct {
		req vo.VoucherPageReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []po.VoucherExt
		want1   int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "测试优惠券列表查询",
			fields: fields{
				BaseService: common.BaseService{},
			},
			args: args{
				req: vo.VoucherPageReq{
					PageIndex: 1,
					PageSize:  10,
					OrgId:     2,
					// VoucherTitle: "",
					// VoucherCode:  "",
					// VoucherState: 1,
				},
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewVoucherService()
			got, got1, err := s.GetVoucherList(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("VoucherService.GetVoucherList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VoucherService.GetVoucherList() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("VoucherService.GetVoucherList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
