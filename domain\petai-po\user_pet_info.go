package petai_po

import (
	omnibus_po "eShop/domain/omnibus-po"
	"eShop/infra/log"
	"eShop/infra/utils"
	"errors"
	"fmt"
	"time"

	"xorm.io/xorm"
)

type UserPetInfo struct {
	Id         int    `json:"id" xorm:"not null pk autoincr INT(11)"`
	UserInfoId string `json:"user_info_id" xorm:"not null VARCHAR(255)"`
	PetInfoId  string `json:"pet_info_id" xorm:"not null VARCHAR(255)"`

	PetId            string    `json:"pet_id" xorm:"not null VARCHAR(255)"`            // 宠物ID作为唯一标示，若授权了同scrm_organization_db.t_scrm_pet_info.pet_id，若未授权则为空
	PetName          string    `json:"pet_name" xorm:"not null VARCHAR(255)"`          // 宠物昵称
	PetSex           int       `json:"pet_sex" xorm:"not null TINYINT(4)"`             // 宠物性别 0未知 1公 2母
	PetKindof        int       `json:"pet_kindof" xorm:"not null TINYINT(4)"`          // 宠物品种 -1未知
	PetVariety       int       `json:"pet_variety" xorm:"not null TINYINT(4)"`         // 宠物品种 -1 未知
	PetNeutering     int       `json:"pet_neutering" xorm:"not null TINYINT(4)"`       // 绝育 -1未知,0未绝育,1已绝育
	PetVaccinated    int       `json:"pet_vaccinated" xorm:"not null TINYINT(4)"`      // 疫苗 -1未知,0未接种,1 已接种
	PetDeworming     int       `json:"pet_deworming" xorm:"not null TINYINT(4)"`       // 驱虫  -1未知,0未驱虫,1 已驱虫
	PetWeight        int       `json:"pet_weight" xorm:"not null INT(11)"`             // 宠物体重,单位g
	PetLong          int       `json:"pet_long" xorm:"not null INT(11)"`               // 宠物体长,单位mm
	PetHeight        int       `json:"pet_height" xorm:"not null INT(11)"`             // 宠物体高,单位mm
	PetSource        int       `json:"pet_source" xorm:"not null TINYINT(4)"`          // 宠物来源 0：子龙 1：小暖 2：瑞鹏 3:小程序
	PetStatus        int       `json:"pet_status" xorm:"not null TINYINT(4)"`          // 宠物状态 0正常,1死亡,2走失,4送人,8隐藏
	PetAvatar        string    `json:"pet_avatar" xorm:"not null VARCHAR(256)"`        // 宠物头像
	PetBirthday      time.Time `json:"pet_birthday" xorm:"not null DATETIME"`          // 宠物生日
	PetHomeday       time.Time `json:"pet_homeday" xorm:"not null DATETIME"`           // 宠物到家日期
	PetRemark        string    `json:"pet_remark" xorm:"not null VARCHAR(128)"`        // 用户备注
	CreateTime       time.Time `json:"create_time" xorm:"not null DATETIME created"`   // 创建时间
	UpdateTime       time.Time `json:"update_time" xorm:"not null DATETIME updated"`   // 更新时间
	BigdataPetId     string    `json:"bigdata_pet_id" xorm:"not null VARCHAR(32)"`     // 大数据唯一Id
	FaceId           string    `json:"face_id" xorm:"not null VARCHAR(60)"`            // 宠物faceId
	PetCode          string    `json:"pet_code" xorm:"not null VARCHAR(15)"`           // 鼻纹识别宠物code
	InsuranceFaceId  string    `json:"insurance_face_id" xorm:"not null VARCHAR(60)"`  // 保险-宠物faceId
	DogLicenceCode   string    `json:"dog_licence_code" xorm:"not null VARCHAR(20)"`   // 养犬许可证号码
	DogVaccinateCode string    `json:"dog_vaccinate_code" xorm:"not null VARCHAR(20)"` // 犬类免疫证号码
	EnterSource      string    `json:"enter_source" xorm:"not null VARCHAR(32)"`       // 宠物鼻纹录入医院
	ChipCode         string    `json:"chip_code" xorm:"not null VARCHAR(50)"`          // 芯片号
	PetFlower        string    `json:"pet_flower" xorm:"not null VARCHAR(50)"`         // 宠物花色
	FlowerCode       string    `json:"flower_code" xorm:"not null VARCHAR(50)"`        // 花色编码

}

func (s *UserPetInfo) TableName() string {
	return "eshop.user_pet_info"
}
func (dto *UserPetInfo) ToPetInfoData(t *omnibus_po.TScrmPetInfo) {
	if t.PetBirthday != "" {
		dto.PetBirthday, _ = time.ParseInLocation("2006-01-02 15:04:05 ", t.PetBirthday, time.Local)
	}
	if t.PetHomeday != "" {
		dto.PetHomeday, _ = time.ParseInLocation("2006-01-02 15:04:05 ", t.PetHomeday, time.Local)
	}

	if t.CreateTime != "" {
		dto.CreateTime, _ = time.ParseInLocation("2006-01-02 15:04:05 ", t.CreateTime, time.Local)
	}
	if t.UpdateTime != "" {
		dto.UpdateTime, _ = time.ParseInLocation("2006-01-02 15:04:05 ", t.UpdateTime, time.Local)
	}

	dto.PetId = t.PetId
	dto.PetName = t.PetName
	dto.PetSex = t.PetSex
	dto.PetKindof = t.PetKindof
	dto.PetVariety = t.PetVariety
	dto.PetNeutering = t.PetNeutering
	dto.PetVaccinated = t.PetVaccinated
	dto.PetDeworming = t.PetDeworming
	dto.PetWeight = t.PetWeight

	dto.PetLong = t.PetLong
	dto.PetHeight = t.PetHeight
	dto.PetSource = t.PetSource
	dto.PetStatus = t.PetStatus
	dto.PetAvatar = t.PetAvatar
	dto.PetRemark = t.PetRemark
	dto.BigdataPetId = t.BigdataPetId
	dto.FaceId = t.FaceId
	dto.PetCode = t.PetCode
	dto.InsuranceFaceId = t.InsuranceFaceId
	dto.DogLicenceCode = t.DogLicenceCode
	dto.DogVaccinateCode = t.DogVaccinateCode
	dto.EnterSource = t.EnterSource
	dto.ChipCode = t.ChipCode
	dto.PetFlower = t.PetFlower
	dto.FlowerCode = t.FlowerCode

}

// 获取指定用户的所有宠物
func (s *UserPetInfo) GetUserPetInfoByUserId(session *xorm.Session, userInfoId string) (out []*UserPetInfo, err error) {
	logPrefix := fmt.Sprintf("====获取指定用户的所有宠物,userInfoId为%s", userInfoId)
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}
	if len(userInfoId) == 0 {
		err = errors.New("用户id不能为空")
		return
	}
	out = make([]*UserPetInfo, 0)
	if err = session.Where("user_info_id = ?", userInfoId).Find(&out); err != nil {
		log.Error(logPrefix + "查询用户宠物信息失败,err:" + err.Error())
		return
	}
	return
}

// 授权scrm用户的宠物数据 到 小闻养宠助手
func (s *UserPetInfo) AuthData(session *xorm.Session, userInfoId string) (err error) {
	logPrefix := fmt.Sprintf("====用户同意授权scrm用户宠物数据 到 小闻养宠助手,userInfoId为%s", userInfoId)
	log.Info(logPrefix)
	if session == nil {
		err = errors.New("session is nil")
		return
	}

	eshopUserInfo, err := new(EshopUserInfo).GetUserInfoByUserId(session, userInfoId)
	if err != nil {
		log.Error(logPrefix + "查询用户信息失败,err:" + err.Error())
		return
	}
	if eshopUserInfo == nil || len(eshopUserInfo.UserInfoId) == 0 {
		err = errors.New("用户不存在")
		return
	}
	if eshopUserInfo.UserId == "" {
		log.Error(logPrefix + "scrm用户id为空")
		err = errors.New("授权失败")
		return
	}
	if eshopUserInfo.IsAuthorized != 1 {
		err = errors.New("用户未授权")
	}

	// 获取eshop用户宠物列表
	petList, err := s.GetUserPetInfoByUserId(session, userInfoId)
	if err != nil {
		log.Error(logPrefix + "查询用户宠物信息失败,err:" + err.Error())
		err = errors.New("授权失败")
		return
	}

	// 获取scrm宠物列表
	scrmPetList, err := new(omnibus_po.TScrmPetInfo).GetPetListByUserId(session, eshopUserInfo.UserId)
	if err != nil {
		log.Error(logPrefix + "查询scrm用户宠物信息失败,err:" + err.Error())
		err = errors.New("授权失败")
		return
	}

	// 获取新增宠物 和 更新宠物列表
	addPetList, _ := s.getAddAndUpdatePetList(userInfoId, petList, scrmPetList)
	if len(addPetList) > 0 {
		if _, err = session.Table("eshop.user_pet_info").Insert(addPetList); err != nil {
			log.Error(logPrefix + "新增用户宠物信息失败,err:" + err.Error())
			err = errors.New("授权失败")
			return
		}
	}
	// if len(updatePetList) > 0 {
	// 	for _, pet := range updatePetList {
	// 		if _, err = session.Table("eshop.user_pet_info").Where("id=?", pet.Id).Update(pet); err != nil {
	// 			log.Error(logPrefix + "更新用户宠物信息失败,err:" + err.Error())
	// 			err = errors.New("授权失败")
	// 			return
	// 		}
	// 	}
	// }
	return

}

func (s *UserPetInfo) getAddAndUpdatePetList(userInfoId string, petList []*UserPetInfo, scrmPetList []*omnibus_po.TScrmPetInfo) (addPetList []*UserPetInfo, updatePetList []*UserPetInfo) {
	addPetList = make([]*UserPetInfo, 0)
	updatePetList = make([]*UserPetInfo, 0)
	existMap := make(map[string]bool)
	for _, scrmPet := range scrmPetList {
		for _, pet := range petList {
			if scrmPet.PetId == pet.PetId {
				// 更新宠物信息
				existMap[scrmPet.PetId] = true
				pet.ToPetInfoData(scrmPet)
				updatePetList = append(updatePetList, pet)
			}
		}

	}

	for _, scrmPet := range scrmPetList {
		if !existMap[scrmPet.PetId] {
			// 新增宠物信息
			pet := &UserPetInfo{UserInfoId: userInfoId}
			pet.PetInfoId = utils.GenerateUUID()

			pet.ToPetInfoData(scrmPet)
			addPetList = append(addPetList, pet)
		}
	}
	return

}
