package export

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
)

type UserTask struct {
	F            *excelize.File
	SheetName    string
	ExportParams *vo.UserPageReq
	writer       *excelize.StreamWriter
	common.BaseService
}

func (t *UserTask) DataExport(taskParams string) (successNum int, failNum int, err error) {
	// 解析导出参数
	t.ExportParams = new(vo.UserPageReq)
	err = json.Unmarshal([]byte(taskParams), t.ExportParams)
	if err != nil {
		err = errors.New("json解析错误, " + err.Error() + ", json：" + taskParams)
		return
	}

	// 初始化流式写入器
	t.writer, err = t.F.NewStreamWriter(t.SheetName)
	if err != nil {
		err = errors.New("生成文件失败, " + err.Error())
		return
	}

	// 设置表头
	t.SetSheetName()

	// 设置分页参数
	t.ExportParams.PageIndex = 1
	t.ExportParams.PageSize = 10000 // 每次查询的最大数量

	// 创建服务实例
	userService := services.NewUpetUserService()

	k := 0
	for {
		// 获取数据
		records, _, err := userService.GetUserList(*t.ExportParams)
		if err != nil {
			err = errors.New("获取导出数据失败, " + err.Error())
			return 0, 0, err
		}

		t.ExportParams.PageIndex += 1

		// 写入数据
		for i := 0; i < len(records); i++ {
			k++
			// 用户性别 0未知 1男 2女
			var memberSex string
			if records[i].MemberSex == 0 {
				memberSex = "未知"
			} else if records[i].MemberSex == 1 {
				memberSex = "男"
			} else {
				memberSex = "女"
			}
			axis := fmt.Sprintf("A%d", k+1)
			err = t.writer.SetRow(axis, []interface{}{
				records[i].MemberId,         // 会员ID
				records[i].MemberName,       // 会员名称
				memberSex,                   // 会员性别
				records[i].MemberMobile,     // 会员手机号
				records[i].UserLevelId,      // 会员等级
				records[i].CreateTime,       // 会员注册时间
				records[i].MemberLoginTime,  // 会员最后登录时间
				records[i].OrderCount,       // 订单数量
				records[i].LastPurchaseTime, // 最后购买时间

			})

			if err != nil {
				log.Error("写入Excel行数据失败:", err)
				failNum++
			}
		}

		// 如果返回的数据少于页大小，说明已经是最后一页
		if len(records) < int(t.ExportParams.PageSize) {
			break
		}
	}

	successNum = k
	err = t.writer.Flush()
	return
}

func (t *UserTask) SetSheetName(args ...interface{}) {
	nameList := []interface{}{
		"会员ID", "会员名称", "会员性别", "会员手机号", "会员等级",
		"会员注册时间", "会员最后登录时间", "订单数量", "最后购买时间",
	}
	_ = t.writer.SetRow("A1", nameList)
	t.SheetName = "客户列表"
}

func (t *UserTask) GenerateDownUrl() (string, error) {
	fileName := fmt.Sprintf("客户列表(%s%d).xlsx",
		time.Now().Format("20060102150405"),
		time.Now().Nanosecond()/1000)
	return utils.UploadExcelToQiNiu(t.F, fileName)
}

func (t *UserTask) OperationFunc(row []string, orgId int) string {
	return ""
}
