package utils

import (
	"eShop/infra/log"
	"reflect"
	"testing"
)

func TestGetOfflineToken(t *testing.T) {
	log.Init()
	GetOfflineToken()
}

func TestHttpApi(t *testing.T) {
	type args struct {
		method  string
		url     string
		Headers string
		body    interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				method: "Get",
				url:    GetTokenUrl,
			},
		},
	}
	log.Init()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := HttpApi(tt.args.method, tt.args.url, tt.args.Headers, tt.args.body)
			if (err != nil) != tt.wantErr {
				t.Errorf("HttpApi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("HttpApi() got = %v, want %v", got, tt.want)
			}
		})
	}
}
