package utils

import (
	"fmt"
	"testing"
)

func TestAliExpress(t *testing.T) {
	// 创建实例
	ali := NewAliExpress()

	// 测试用例
	tests := []struct {
		name        string
		companyCode string
		expressNo   string
		phoneEnd    string
	}{
		{
			name:        "韵达快递",
			companyCode: "YD",
			expressNo:   "464510115927522",
			phoneEnd:    "3765",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行查询
			result, err := ali.QueryExpress(tt.companyCode, tt.expressNo, tt.phoneEnd)
			if err != nil {
				t.<PERSON><PERSON>rf("QueryExpress failed: %v", err)
				return
			}

			// 打印结果
			fmt.Printf("\n=== %s查询结果 ===\n", tt.name)
			if result != nil {
				fmt.Printf("快递公司: %s (%s)\n", result.Result.ExpName, result.Result.ExpPhone)
				fmt.Printf("快递单号: %s\n", result.Result.Number)
				fmt.Printf("官网: %s\n", result.Result.ExpSite)
				if result.Result.Courier != "" {
					fmt.Printf("快递员: %s (%s)\n", result.Result.Courier, result.Result.CourierPhone)
				}
				fmt.Printf("状态: %s (耗时: %s)\n", getDeliveryStatus(result.Result.DeliveryStatus), result.Result.TakeTime)
				fmt.Printf("最后更新: %s\n", result.Result.UpdateTime)

				fmt.Println("\n物流轨迹:")
				for i, track := range result.Result.List {
					fmt.Printf("%d. [%s] %s\n", i+1, track.Time, track.Status)
				}
			} else {
				fmt.Println("未查询到物流信息")
			}
			fmt.Println("========================")
		})
	}
}

// getDeliveryStatus 获取物流状态描述
func getDeliveryStatus(status string) string {
	statusMap := map[string]string{
		"0": "快递收件",
		"1": "在途中",
		"2": "正在派件",
		"3": "已签收",
		"4": "派送失败",
		"5": "疑难件",
		"6": "退件签收",
	}
	if desc, ok := statusMap[status]; ok {
		return desc
	}
	return "未知状态"
}
