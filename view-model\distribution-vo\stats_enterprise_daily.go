package distribution_vo

type StatsEnterpriseDailyReq struct {
	// 开始日期
	StartDate string `json:"start_date"`
	// 结束日期
	EndDate string `json:"end_date"`
}

type Commission struct {
	// 分销佣金
	Comm DisCommission `json:"comm"`
	//佣金提现
	Withdraw WithdrawData `json:"withdraw"`
}
type WithdrawData struct {
	// 累计提现成功(分)
	WithdrawSuccessTotal int `json:"withdraw_success_total"`
	// 新增提现成功(分)
	WithdrawSuccess int `json:"withdraw_success"`
	// 新增提现成功 较上一周期百分比
	WithdrawSuccessPercent string `json:"withdraw_success_percent"`
	// 累计提现申请(分)
	WithdrawApplyTotal int `json:"withdraw_apply_total"`
	// 新增提现申请(分)
	WithdrawApply int `json:"withdraw_apply"`
	// 新增提现申请 较上一周期百分比
	WithdrawApplyPercent string `json:"withdraw_apply_percent"`
}

// stats_enterprise_daily表里数据（除掉成交数据， 因为成交数据： 商品成交数据和保险成交数据是分开的。 ）
type StatsEntView struct {
	// 累计业务员
	TotalSalesman int `json:"total_salesman"`
	// 累计业务员 启用
	TotalSalesmanOpen int `json:"total_salesman_open"`
	// 累计业务员 停用
	TotalSalesmanStop int `json:"total_salesman_stop"`

	// 新增业务员
	TotalSalesmanNew int `json:"total_salesman_new"`
	// 新增业务员 启用
	TotalSalesmanNewOpen int `json:"total_salesman_new_open"`
	// 新增业务员 停用
	TotalSalesmanNewStop int `json:"total_salesman_new_stop"`
	// 新增业务员  较上一周期百分比
	TotalSalesmanNewPercent string `json:"total_salesman_new_percent"`

	// 累计分销企业
	TotalEnterprise int `json:"total_enterprise"`
	// 累计分销店铺
	TotalShop int `json:"total_shop"`

	// 累计分销员
	TotalDistributor int `json:"total_distributor"`
	// 累计分销员启用
	TotalDistributorOpen int `json:"total_distributor_open"`
	// 累计分销员停用
	TotalDistributorStop int `json:"total_distributor_stop"`

	// 新增分销企业
	TotalEnterpriseNew int `json:"total_enterprise_new"`
	// 新增分销店铺
	TotalShopNew int `json:"total_shop_new"`

	// 新增分销员
	TotalDistributorNew int `json:"total_distributor_new"`
	// 新增分销员 启用
	TotalDistributorNewOpen int `json:"total_distributor_new_open"`
	// 新增分销员 停用
	TotalDistributorNewStop int `json:"total_distributor_new_stop"`

	// 新增分销企业 较上一周期百分比
	TotalEnterpriseNewPercent string `json:"total_enterprise_new_percent"`
	// 新增分销店铺 较上一周期百分比
	TotalShopNewPercent string `json:"total_shop_new_percent"`

	// 新增分销员 较上一周期百分比
	TotalDistributorNewPercent string `json:"total_distributor_new_percent"`
}

// stats_enterprise_daily表里商品订单成交数据
type StatsEntTransView struct {
	// 商品订单累计成交企业
	TotalTransEnterprise int `json:"total_trans_enterprise"`
	// 商品订单累计成交分销员
	TotalTransDistributor int `json:"total_trans_distributor"`
	// 商品订单新增成交企业
	TotalTransEnterpriseNew int `json:"total_trans_enterprise_new"`
	// 新增成交分销员
	TotalTransDistributorNew int `json:"total_trans_distributor_new"`
	// 商品订单新增成交企业 较上一周期百分比
	TotalTransEnterpriseNewPercent string `json:"total_trans_enterprise_new_percent"`
	// 商品订单新增成交分销员 较上一周期百分比
	TotalTransDistributorNewPercent string `json:"total_trans_distributor_new_percent"`
}

// stats_enterprise_daily表里保险订单成交数据
type StatsEntInsTransView struct {
	// 保险订单累计成交企业
	InsTotalTransEnterprise int `json:"ins_total_trans_enterprise"`
	// 保险订单累计成交分销员
	InsTotalTransDistributor int `json:"ins_total_trans_distributor"`
	// 保险订单新增成交企业
	InsTotalTransEnterpriseNew int `json:"ins_total_trans_enterprise_new"`
	// 新增成交分销员
	InsTotalTransDistributorNew int `json:"ins_total_trans_distributor_new"`
	// 保险订单新增成交企业 较上一周期百分比
	InsTotalTransEnterpriseNewPercent string `json:"ins_total_trans_enterprise_new_percent"`
	// 保险订单新增成交分销员 较上一周期百分比
	InsTotalTransDistributorNewPercent string `json:"ins_total_trans_distributor_new_percent"`
}
