package api

import (
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/distribution-service/services"
	vo "eShop/view-model/distribution-vo"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"net/http"
)

// StatsSalespersonSummary
// @Summary 数据概览-累计数据展示
// @Description 数据概览-累计数据展示
// @Tags 小程序接口-业务员中心
// @Accept plain
// @Produce plain
// @Param salesman_id query int true "业务员id"
// @Success 200 {object} vo.StatsSalespersonSummaryResp
// @Failure 400 {object} vo.StatsSalespersonSummaryResp
// @Router /api/stats/salesperson/summary [get]
func StatsSalespersonSummary(writer http.ResponseWriter, request *http.Request) {
	resp := vo.StatsSalespersonSummaryResp{}
	resp.Code = 400

	service := services.StatsSalespersonService{}
	req, err := utils.Bind[vo.GetStatsSalespersonReq](request)
	if err != nil {
		log.Error("获取分销员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取分销员，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	resp.Data, err = service.StatsSalespersonSummary(req)
	if err != nil {
		log.Error("获取分销员：err=" + err.Error())
		resp.Message = fmt.Sprintf("获取分销员：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisGraph
// @Summary 数据概览-分销数据趋势（曲线图）
// @Description 数据概览-累计数据展示（曲线图）
// @Tags 小程序接口-业务员中心
// @Accept plain
// @Produce plain
// @Param salesman_id query int true "业务员id"
// @Param start_date query string false "时间范围-开始时间"
// @Param end_date query string false "时间范围-结束时间"
// @Success 200 {object} vo.StatsDisGraphResp
// @Failure 400 {object} vo.StatsDisGraphResp
// @Router /api/stats/salesperson/graph [get]
func StatsDisGraph(writer http.ResponseWriter, request *http.Request) {
	resp := vo.StatsDisGraphResp{}
	resp.Code = 400

	service := services.StatsSalespersonService{}
	req, err := utils.Bind[vo.GetStatsSalespersonReq](request)
	if err != nil {
		log.Error("获取分销员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取分销员，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	resp.Data, err = service.StatsDisGraph(req)
	if err != nil {
		log.Error("获取分销员：err=" + err.Error())
		resp.Message = fmt.Sprintf("获取分销员：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisTrend
// @Summary 数据概览-分销数据趋势（数据展示）
// @Description 数据概览-累计数据展示（数据展示）
// @Tags 小程序接口-业务员中心
// @Accept plain
// @Produce plain
// @Param salesman_id query int true "业务员id"
// @Param start_date query string false "时间范围-开始时间"
// @Param end_date query string false "时间范围-结束时间"
// @Success 200 {object} vo.StatsDisTrendResp
// @Failure 400 {object} vo.StatsDisTrendResp
// @Router /api/stats/salesperson/trend [get]
func StatsDisTrend(writer http.ResponseWriter, request *http.Request) {
	resp := vo.StatsDisTrendResp{}
	resp.Code = 400

	service := services.StatsSalespersonService{}
	req, err := utils.Bind[vo.GetStatsSalespersonReq](request)
	if err != nil {
		log.Error("获取分销员，参数解析失败：err=", err.Error())
		resp.Message = fmt.Sprintf("获取分销员，参数解析失败：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}

	req.OrgId = cast.ToInt(request.Header.Get("org_id"))
	resp.Data, err = service.StatsDisTrend(req)
	if err != nil {
		log.Error("获取分销员：err=" + err.Error())
		resp.Message = fmt.Sprintf("获取分销员：" + err.Error())
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}

// StatsDisGoods
// @Summary 商品分析
// @Description 商品分析
// @Tags 小程序接口-业务员中心
// @Accept plain
// @Produce plain
// @Param StatsDisGoodsReq query vo.StatsDisGoodsReq true " "
// @Success 200 {object} vo.StatsDisGoodsResp
// @Failure 400 {object} vo.StatsDisGoodsResp
// @Router /api/stats/salesperson/goods [get]
func StatsDisGoods(writer http.ResponseWriter, request *http.Request) {
	resp := vo.StatsDisGoodsResp{}
	resp.Code = 400

	service := services.StatsSalespersonService{}
	req, err := utils.Bind[vo.StatsDisGoodsReq](request)
	if err != nil {
		log.Error("获取业务员中心-数据中心-商品分析，参数解析失败：err=" + err.Error())
		resp.Message = "参数解析失败：" + err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	req.OrgId = cast.ToInt64(request.Header.Get("org_id"))
	if resp.Data, resp.Total, err = service.StatsDisGoods(req); err != nil {
		log.Error("获取业务员中心-数据中心-商品分析失败：err=" + err.Error())
		resp.Message = err.Error()
		bytes, _ := json.Marshal(resp)
		writer.Write(bytes)
		return
	}
	resp.Code = 200
	bytes, _ := json.Marshal(resp)
	writer.Write(bytes)
}
