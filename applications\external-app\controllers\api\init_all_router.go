package api

import (
	"github.com/go-chi/chi/v5"
)

func InitAllApi(r chi.Router) {
	InitStoreRouter(r)

	InitSjanRouter(r)
}

func InitStoreRouter(r chi.Router) {
	//
	r.Route("/external-app/api", func(r chi.Router) {
		r.Post("/add_store", AddStore)
		r.Post("/edit_store", EditStore)
		r.Post("/store/sync", SyncStoreRegister)
		r.Post("/employee/sync", SyncEmployee)
		r.Post("/employee/delete", DeleteEmployee)
		r.Post("/wx_code", GetShopWxAppCode)
	})
}

// 世纪安诺第三方接口调用
func InitSjanRouter(r chi.Router) {
	r.Route("/external-app/api/sjan", func(r chi.Router) {
		r.Post("/product/add", AddProducts)        // 添加产品信息接口
		r.Post("/agent/add", AddAgents)            // 添加代理商信息接口
		r.Post("/packaging/add", AddPackagingTask) // 添加包装任务及明细接口
		r.Post("/outbound/add", AddOutboundRecord) // 添加出库记录及明细接口
		r.Post("/return/add", AddReturnRecords)    // 添加退货记录接口
	})
}
